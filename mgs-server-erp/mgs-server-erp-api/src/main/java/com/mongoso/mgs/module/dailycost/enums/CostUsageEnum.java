package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON>ang<PERSON>
 * @date： 2025/2/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CostUsageEnum {
    //成本用途
    PRODUCT_COST("0","生产成本"),
    PERIOD_COST("1","期间成本");

    public final String code;// 编码
    public final String value;// 描述

    public static String getValueByCode(String code) {
        String value = "";
        for (CostUsageEnum costUsageEnum : CostUsageEnum.values()) {
            if (costUsageEnum.code.equals(code)) {
                value = costUsageEnum.value;
                break;
            }
        }
        return value;
    }
}
