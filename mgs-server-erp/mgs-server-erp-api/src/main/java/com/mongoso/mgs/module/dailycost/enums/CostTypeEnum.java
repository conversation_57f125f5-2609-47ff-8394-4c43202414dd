package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Zhou<PERSON>ang<PERSON>
 * @date： 2025/2/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CostTypeEnum {
    //成本相关性
    DIRECT_COST("0","直接成本"),
    INDIRECT_COST("1","间接成本");

    public final String code;// 编码
    public final String value;// 描述

    public static String getValueByCode(String code) {
        String value = "";
        for (CostUsageEnum costUsageEnum : CostUsageEnum.values()) {
            if (costUsageEnum.code.equals(code)) {
                value = costUsageEnum.value;
                break;
            }
        }
        return value;
    }
}
