package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON><PERSON>
 * @date： 2025/2/22
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CostAggreOriginalTypeEnum {
    REIMBURSE_PAYMENT(0,"费用报销付款单"),
    PURCHASE(1,"采购订单"),
    UTILITY_COST(2,"日水电气费用"),
    COMMISSION_DISTRIBUTE(3,"提成发放"),
    OUT_BILL(4,"直接出账"),
    SALARY(5, "工资单");

    public final Integer code;// 编码
    public final String value;// 描述

}
