package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： ZhouYang<PERSON>
 * @date： 2025/2/25
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CostDataSourceEnum {

    INDIRECT_EXPENSE_AGGRE(0,"间接支出归集单"),
    SALARY_AGGRE(1,"工资归集单"),
    MANUALLY_ADD(2,"手动新增")
    ;


    public final Integer code;// 编码
    public final String value;// 描述

    public static String getValueByCode(Integer code) {
        String value = "";
        for (CostDataSourceEnum item : CostDataSourceEnum.values()) {
            if (item.code.equals(code)) {
                value = item.value;
                break;
            }
        }
        return value;
    }

}
