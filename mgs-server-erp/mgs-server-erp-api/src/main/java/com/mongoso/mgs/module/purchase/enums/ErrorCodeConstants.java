package com.mongoso.mgs.module.purchase.enums;


import com.mongoso.mgs.framework.common.exception.ErrorCode;

/**
 * Infra 错误码枚举类
 *
 * infra 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 参数配置 1001000000 ==========
    ErrorCode ORDER_DELETE_NOT_APPROVED = new ErrorCode("50001", "单据状态不是未审核,不可删除！");
    ErrorCode ORDER_EDIT_NOT_APPROVED = new ErrorCode("50001", "单据状态不是未审核,不可编辑！");
    ErrorCode ORDER_NOT_OPERATION_NOT_APPROVED = new ErrorCode("50001", "单据状态不是未审核,不可审核！");
    ErrorCode SALE_NOT_EXIST = new ErrorCode("50001", "关联的销售订单已删除，审核失败！");
    ErrorCode SALE_NOT_APPROVED = new ErrorCode("50001", "关联的销售订单不是已审核状态，审核失败！");
    ErrorCode RELATED_ORDER_NOT_EXIST = new ErrorCode("50001", "关联的上游单据已删除，审核失败！");
    ErrorCode PURCHASE_NOT_EXIST = new ErrorCode("50001", "关联的采购订单已删除，审核失败！");
    ErrorCode INVENTORY_NOT_EXIST = new ErrorCode("50001", "关联的盘点订单已删除，审核失败！");
    ErrorCode ADJUST_DETAIL_ADD_GT_STOCK = new ErrorCode("50001", "调整单明细增加数大于库存数量，不允许进行反审核操作！");
    ErrorCode ADJUST_DETAIL_ADD_LT_STOCK = new ErrorCode("50001", "调整单明细增加数小于库存数量，不允许进行审核操作！");
    ErrorCode ADJUST_DETAIL_REDUCE_LT_STOCK = new ErrorCode("50001", "调整单明细减少数小于库存数量，不允许进行审核操作！");
    ErrorCode ADJUST_DETAIL_REDUCE_GT_STOCK = new ErrorCode("50001", "调整单明细减少数大于库存数量，不允许进行反审核操作！");
    ErrorCode ADJUST_NO_NEED_ADJUST = new ErrorCode("50001", "调整单不为待调整状态，不允许进行反审核操作！");
    ErrorCode ADJUST_NO_FINISHED = new ErrorCode("50001", "调整单不为待调整状态，不允许进行反审核操作！");
    ErrorCode RELATED_ORDER_NOT_APPROVED = new ErrorCode("50001", "关联的上游订单不是已审核状态，审核失败！");
    ErrorCode PROD_NOT_FINISHED = new ErrorCode("50001", "关联的生产订单状态不为待生产/生产中，审核失败！");
    ErrorCode DEMAND_DETAIL_NUM_GREATER_SALE = new ErrorCode("50001", "采购需求单已物料规划需求数量大于销售订单中的可规划需求数量，审核失败！");
    ErrorCode DEMAND_DETAIL_NUM_GREATER_ANALYSIS = new ErrorCode("50001", "采购需求单已物料规划需求数量大于物料分析单中的可规划需求数量，审核失败！");
    ErrorCode DETAIL_NUM_GREATER_RELATED_ORDER = new ErrorCode("50001", "需求采购单的采购数量大于需求订单中的可采购数量，审核失败！");

    ErrorCode RELATED_NUM_GREATER_RELATED_ORDER = new ErrorCode("50001", "采购订单的采购数量大于关联单据中的可采购数量，审核失败！");
    ErrorCode PURCHASE_DETAIL_NUM_GREATER_RECEIPT = new ErrorCode("50001", "采购收货通知单的操作数量大于采购订单中的可操作数量，审核失败！");
    ErrorCode PURCHASE_DETAIL_NUM_GREATER_RETURN = new ErrorCode("50001", "采购退货单的退货数量大于采购订单中的可退货数量，审核失败！");

    ErrorCode PURCHASE_DETAIL_NUM_GREATER_EXCHANGE = new ErrorCode("50001", "采购换货单的换货数量大于采购订单中的可换货数量，审核失败！");
    ErrorCode PURCHASE_DETAIL_NUM_GREATER_DEDUCTION = new ErrorCode("50001", "采购扣费单的扣费数量大于采购订单中的可扣费数量，审核失败！");
    ErrorCode PURCHASE_DETAIL_NUM_GREATER_DEMAND = new ErrorCode("50001", "采购单的每个工序采购数量不可大于工序委外需求清单中的可采购数量，审核失败！");
    ErrorCode DOWNSTREAM_HAVE_APPROVED = new ErrorCode("50001", "已有已审核的下游单据，不可反审核！");

    ErrorCode RELATED_PURCHASE_FORCE_CLOSE = new ErrorCode("50002", "关联的采购订单已强制关闭，不允许进行审核或者反审核操作！");

    ErrorCode RELATED_PURCHASE_FORM_STATUS = new ErrorCode("50002", "关联的采购订单单据状态是【已完成】或者【已关闭】，不允许进行审核或者反审核操作！");
    ErrorCode PURCHASE_PROCESS_OUT_FORM_STATUS = new ErrorCode("50002", "关联的工序委外采购订单单据状态是【已完成】或者【已关闭】，不允许进行审核或者反审核操作！");

    ErrorCode PURCHASE_PROCESS_OUT_NOT_EXIST = new ErrorCode("50001", "关联的工序委外采购订单已删除，审核失败！");
}
