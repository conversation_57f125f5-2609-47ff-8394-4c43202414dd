package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON><PERSON>
 * @date： 2025/2/20
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CostSubjectInitDataEnum {
    SUBJECT1(0, "直接成本", 0),
    SUBJECT2(0, "生产物料", 0),
    SUBJECT3(0, "品质人工", 0),
    SUBJECT4(0, "不良损失", 0),
    SUBJECT5(0, "委外加工费", 0),
    SUBJECT6(0, "生产模具折旧", 1),
    SUBJECT7(0, "生产设备折旧", 1),
    SUBJECT8(0, "生产配件折旧", 1),
    SUBJECT9(0, "生产汽车折旧", 1),
    SUBJECT10(0, "生产汽车保险分摊", 1),
    SUBJECT11(0, "生产场地装修分摊", 1),
    SUBJECT12(0, "生产环保投入分摊", 1),
    SUBJECT13(0, "生产质量体系分摊", 1),
    SUBJECT14(0, "生产场地固定租金", 2),
    SUBJECT15(0, "生产管理固定工资", 2),
    SUBJECT16(0, "生产社保费公司固定成本", 2),
    SUBJECT17(0, "机械贷款利息", 3),
    SUBJECT18(0, "生产特采质量扣费", 3),
    SUBJECT19(0, "生产车间水费", 4),
    SUBJECT20(0, "生产车间电费", 4),
    SUBJECT21(0, "生产车间汽费", 4),
    SUBJECT22(0, "生产设备维保费", 4),
    SUBJECT23(0, "生产模具维保费", 4),
    SUBJECT24(0, "生产辅助物料", 4),
    SUBJECT25(0, "生产夹治具费用", 4),
    SUBJECT26(0, "生产杂费", 4),
    SUBJECT27(1, "后勤汽车折旧", 1),
    SUBJECT28(1, "后勤汽车保险分摊", 1),
    SUBJECT29(1, "后勤场地装修分摊", 1),
    SUBJECT30(1, "信息化分摊", 1),
    SUBJECT31(1, "后勤场地固定租金", 2),
    SUBJECT32(1, "后勤管理固定工资", 2),
    SUBJECT33(1, "后勤社保费公司固定成本", 2),
    SUBJECT34(1, "后勤水电费", 3),
    SUBJECT35(1, "后勤住宿费用", 3),
    SUBJECT36(1, "资金贷款利息", 3),
    SUBJECT37(1, "税费", 3),
    SUBJECT38(1, "后勤办公费", 4),
    SUBJECT39(1, "业务提成费", 4),
    SUBJECT40(1, "打包材料费", 4),
    SUBJECT41(1, "运输物流费", 4),
    SUBJECT42(1, "技改费", 4),
    SUBJECT43(1, "业务费", 4),
    SUBJECT44(1, "财务费", 4),
    SUBJECT45(1, "后勤杂费", 4);

    public final Integer usage;// 成本用途
    public final String subject;// 成本科目
    public final Integer type;//成本相关性
}
