package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON><PERSON>
 * @date： 2025/2/26
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CostInvestTypeEnum {

    FIXED_ASSET(0,"固定资产"),
    INTANGIBLE_ASSET(1,"无形资产"),
    OTHER_INVEST(2,"其他投入")
    ;


    public final Integer code;// 编码
    public final String value;// 描述

}
