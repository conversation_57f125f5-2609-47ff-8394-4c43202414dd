package com.mongoso.mgs.module.produce.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: zhiling
 * @date: 2025/3/28 17:51
 * @description: 分析参数枚举
 */
@AllArgsConstructor
@Getter
public enum AnalyzeParameEnum {

    WAREHOUSE_IN_STOCK(0, "仓库在库"),
    PURCHASE_IN_TRANSIT(1, "采购在途"),
    PRODUCTION_IN_PROGRESS(2, "生产在制"),
    CONSIDERED_RESERVATION(3, "考虑预订"),
    NEGATIVE_INVENTORY(4, "考虑负库存"),
    ALTERNATIVE_MATERIAL(5, "考虑替代方案"),

    ;

    public final Integer key;

    public final String desc;


}

