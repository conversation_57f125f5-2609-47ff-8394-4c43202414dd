package com.mongoso.mgs.module.sale.enums;

import com.mongoso.mgs.framework.common.exception.ErrorCode;

/**
 * @author: zhiling
 * @date: 2024/11/9 18:02
 * @description: 销售模块枚举类
 */
public interface ErrorCodeConstants {

    ErrorCode CUSTOMER_PRICE_PLAN_NOT_EXISTS = new ErrorCode("1001001001", "客户价格方案不存在");

    ErrorCode SALE_NOT_EXISTS_NOT_APPROVE = new ErrorCode("1001001001", "关联的销售订单不存在，不能审批");
    ErrorCode RELATED_SALE_NOT_APPROVE = new ErrorCode("50001", "关联的销售单不是已审核状态，审核失败！");
    ErrorCode RELATED_SALE_FORCE_CLOSE = new ErrorCode("50002", "关联的销售订单单据状态是【已完成】或者【已关闭】,不允许进行审核或反审核操作！");
    ErrorCode PROD_QTY_EXCEEDS_ALLOWED = new ErrorCode("50003", "生产数量不可大于物料分析应自制数量，审核失败！");
    ErrorCode SALESHIPNOTICE_APPROVED_AND_REJECTED = new ErrorCode("50004", "已同意、已拒绝的销售出库通知单不可反审核操作！");
}
