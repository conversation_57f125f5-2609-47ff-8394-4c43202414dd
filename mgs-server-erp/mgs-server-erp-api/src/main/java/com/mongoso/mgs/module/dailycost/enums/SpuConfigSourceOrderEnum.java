package com.mongoso.mgs.module.dailycost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON><PERSON>
 * @date： 2025/2/20
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum SpuConfigSourceOrderEnum {

    //成本用途
    PURCHASE(0,"采购订单"),
    OUTSOURCING_PURCHASE(1,"委外采购订单"),
    OUT_PROCESS_PURCHASE(2,"工序委外采购订单"),
    PURCHASE_RETURN(3,"采购退货单"),
    PURCHASE_DEDUCTION(4,"采购扣费单"),
    REPORTED_PROCESS(5,"工序报工"),
    OUTBOUND(6,"出库单"),
    SALE_RETURN(7,"销售退货单"),
    SALE_DEDUCTION(8,"销售扣费单");

    public final Integer code;// 编码
    public final String value;// 描述

    public static String getValueByCode(Integer code) {
        String value = "";
        for (SpuConfigSourceOrderEnum item : SpuConfigSourceOrderEnum.values()) {
            if (item.code.equals(code)) {
                value = item.value;
                break;
            }
        }
        return value;
    }

}
