<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mongoso.cloud</groupId>
        <artifactId>mgs-server-erp</artifactId>
        <version>2.0.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>mgs-server-erp-biz</artifactId>

    <description>
        软件平台-子系统端，demo子系统biz，所有业务代码在这里实现
    </description>

    <dependencies>
        <!-- erp api -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-server-erp-api</artifactId>
        </dependency>

        <!-- mes api -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-server-mes-api</artifactId>
        </dependency>

        <!-- 平台模块 api -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-server-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-platform-common</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.mongoso.cloud</groupId>-->
<!--            <artifactId>mongoso-lowcode</artifactId>-->
<!--            <version>2.0.0</version>-->
<!--        </dependency>-->

        <!-- 微服务starter -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-cloud-starter</artifactId>
        </dependency>

        <!-- 审批流程 -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-flow</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-message</artifactId>
        </dependency>

        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>net.objecthunter</groupId>
            <artifactId>exp4j</artifactId>
            <version>0.4.8</version>
        </dependency>

    </dependencies>

    <build>
        <!--包名，注意修改自己的项目名-->
        <finalName>platform-erp</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.2</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
<!--                <configuration>-->
<!--                    <mainClass>com.mongoso.mgs.ErpApplication</mainClass> &lt;!&ndash; 替换为你的主类全名 &ndash;&gt;-->
<!--                    <layout>ZIP</layout>-->
<!--                    &lt;!&ndash;               先注释打包，解压出lib，再排除掉lib&ndash;&gt;-->
<!--                    <includes>-->
<!--                        <include>-->
<!--                            <groupId>nothing</groupId>-->
<!--                            <artifactId>nothing</artifactId>-->
<!--                        </include>-->
<!--                    </includes>-->
<!--                </configuration>-->
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 配置这个，表示该项目不上传到maven私服-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>