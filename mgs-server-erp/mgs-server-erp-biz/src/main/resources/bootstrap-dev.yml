# 本地环境
base:
  ip: **************
--- #################### 注册中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: ${base.ip}:8848 # nacos本地访问地址
      discovery:
        username:
        password:
        namespace: jgzy-dev # 命名空间
        enabled: true
        group: DEFAULT_GROUP #默认 可以不用写
        register-enabled: true #是否把自己注册到注册中心的地址

--- #################### 配置中心相关配置 ####################

#spring:
#  cloud:
#    nacos:
#      config:
#        server-addr: ${base.ip}:8848 # nacos本地访问地址
#        username:
#        password:
#        namespace: jgzy-dev # 命名空间
#        enabled: true #是否使用nacos的配置中心配置覆盖本地配置
#        file-extension: yml  #该配置的缺省值为properties，即默认是读取properties格式的配置文件
#        extension-configs:
#          - data-id: erp-application.yml
#            group: DEFAULT_GROUP #默认 可以不用写
#            refresh: true #默认不刷新