server:
  port: 8020
  max-http-request-header-size: 20480  # 请求头设置为 20 KBequestHeaderSize
spring:
  profiles:
    active: local
#    active: test
  application:
    name: erp   # 服务名
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
  servlet:
    multipart:
      max-request-size: 15MB
      max-file-size: 10MB
  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

### mybaties 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: ASSIGN_ID # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      # field-strategy: not_empty
      table-underline: true
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: ${mgs.info.base-package}.module.*.dal.db

mgs:
  info:
    version: 1.0.0
    base-package: com.mongoso.mgs
    name: ${spring.application.name}
    project-name: erp # 项目名
  security:
    token-header: token # token的请求头
    token-time: 2 # token有效期 单位小时 ，-1：永久有效
    mock-enable: true # 模拟用户开关
    mock-secret: test # 模拟用户的请求头，样例:test1
    request-header-encode: true # 网关请求头编解码开关，有性能损耗，如果请求体不带中文可以关闭，默认关闭
    permit-all-urls:  # 无校验接口路劲
      - /admin-api/system/login
      - /admin-ui/**
      - /export/download/*
  data-permission:
    enable: true # 数据权限开关 true：开启，false：关闭
    log: true # 日志开关 true：开启，false：关闭

  # 多租户相关配置项
  tenant:
    enable: true
    # 排除租户无关的接口
    ignore-urls:
      - /ping

    # 排除和租户无关的表
    ignore-tables:
      - s_file_log
      - c_company
      - c_company_register
      - s_supplier