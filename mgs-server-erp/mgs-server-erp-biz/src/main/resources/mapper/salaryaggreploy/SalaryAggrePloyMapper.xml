<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.salary.dal.mysql.salaryaggreploy.SalaryAggrePloyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectByApplyPersonIds" resultType="com.mongoso.mgs.module.salary.dal.db.salaryaggreploy.SalaryAggrePloyDO">
        SELECT *
        FROM "erp"."u_salary_aggre_ploy"
        WHERE  EXISTS (
            SELECT 1
            FROM unnest(apply_person_ids) AS id
            WHERE id = ANY(ARRAY
            <foreach index="index"  collection="req.applyPersonIds" item="pId" open="[" separator="," close="]">
                #{pId}
            </foreach>
                )
            <if test="req.payrollAggreStrategyId!=null">
                and payroll_aggre_strategy_id != #{req.payrollAggreStrategyId}
            </if>
            and data_status = 1
        )
    </select>
</mapper>
