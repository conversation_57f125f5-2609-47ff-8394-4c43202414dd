<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.base.dal.mysql.spu.SpuMapper">

    <update id="updateSpuPublishStatus">
        UPDATE u_spu SET publish_status = #{reqVO.publishStatus}, updated_dt = CURRENT_TIMESTAMP, updated_by = #{reqVO.updatedBy}
        WHERE deleted = 0 AND spu_id IN
        <foreach collection="reqVO.spuIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getSpuPublishStatus"
            resultType="com.mongoso.mgs.module.base.controller.admin.spu.vo.SpuRespVO">
        SELECT
            spu_id,
            CASE
                WHEN COUNT(*) = SUM(publish_status) THEN '1'
                WHEN SUM(publish_status) > 0 THEN '2'
                ELSE '0'
                END AS publishStatus
        FROM
            u_material
        WHERE
            deleted = 0
            AND spu_id IN
            <foreach collection="spuList" item="item" index="index" open="(" separator="," close=")">
                #{item.spuId}
            </foreach>
        GROUP BY
            spu_id
    </select>
</mapper>
