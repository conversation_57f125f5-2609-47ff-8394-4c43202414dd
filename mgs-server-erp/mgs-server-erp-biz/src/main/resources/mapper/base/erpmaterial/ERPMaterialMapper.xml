<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.base.dal.mysql.erpmaterial.ERPMaterialMapper">
<!--    <update id="materialStatusChange">-->
<!--        <foreach collection="list" item="item" separator=";">-->
<!--            UPDATE u_material SET-->
<!--            <if test="item.publishStatus != null">-->
<!--                updated_dt = #{item.updatedDt}, updated_by = #{item.updatedBy},publish_status = #{item.publishStatus}-->
<!--            </if>-->
<!--            WHERE material_id = #{item.materialId}-->
<!--        </foreach>-->
<!--    </update>-->
<!--    <update id="spuPublishStatusChange">-->
<!--        UPDATE u_material SET-->
<!--        <if test="reqVO.publishStatus != null">-->
<!--            updated_dt = #{reqVO.updatedDt}, updated_by = #{reqVO.updatedBy}, publish_status = #{reqVO.publishStatus}-->
<!--        </if>-->
<!--        WHERE data_status = #{reqVO.preStatus}-->
<!--        AND spu_id IN-->
<!--        (-->
<!--        SELECT b.spu_id FROM u_spu AS b WHERE b.data_status = #{reqVO.preStatus} AND b.spu_id IN-->
<!--        <foreach index="index"  collection="reqVO.spuIdList" item="supId" open="(" separator="," close=")">-->
<!--            #{supId}-->
<!--        </foreach>-->
<!--        )-->
<!--    </update>-->
    <update id="materialPublishChangeBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE u_material SET
            <if test="item.publishStatus != null">
                updated_dt = #{item.updatedDt}, updated_by = #{item.updatedBy},publish_status = #{item.publishStatus}
            </if>
            WHERE material_id = #{item.materialId} AND deleted = 0
        </foreach>
    </update>

    <update id="materialPublishChange">
        UPDATE u_material SET
        <if test="item.publishStatus != null">
            updated_dt = #{item.updatedDt}, updated_by = #{item.updatedBy},publish_status = #{item.publishStatus}
        </if>
        WHERE material_id = #{item.materialId} AND deleted = 0
    </update>

    <update id="spuPublishStatusChange">
        UPDATE u_material SET
        <if test="reqVO.publishStatus != null">
          publish_status = #{reqVO.publishStatus}
        </if>
        WHERE data_status = #{reqVO.preStatus} AND deleted = 0
          AND spu_id IN
              (
              SELECT b.spu_id FROM u_spu AS b WHERE b.deleted = 0 AND b.data_status = #{reqVO.preStatus} AND b.spu_id IN
                <foreach index="index"  collection="reqVO.spuIdList" item="supId" open="(" separator="," close=")">
                    #{supId}
                </foreach>
            )
    </update>

    <update id="updateBySpuId">
        UPDATE u_material SET data_status = #{item.dataStatus}, publish_status = #{item.publishStatus}
        , reviewer = #{item.approvedBy}, review_dt = #{item.approvedDt},approved_by = #{item.approvedBy}, approved_dt = #{item.approvedDt}
        WHERE spu_id = #{item.spuId} AND deleted = 0
    </update>
    <!--    <update id="unbindBatchMaterials" >-->
<!--        <foreach collection="idList" item="id" separator=";">-->
<!--            UPDATE u_material SET spu_id = NULL-->
<!--            WHERE spu_id = #{id} AND spu_id_old = #{id}-->
<!--        </foreach>-->
<!--    </update>-->

    <select id="getSpuListPublishStatus"
            resultType="com.mongoso.mgs.module.base.dal.db.spu.SpuDO">
        SELECT
        spu_id,
        CASE
        WHEN COUNT(*) = SUM(publish_status) THEN '1'
        WHEN SUM(publish_status) > 0 THEN '2'
        ELSE '0'
        END AS publishStatus
        FROM
        u_material
        WHERE deleted = 0
        AND spu_id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.spuId}
        </foreach>
        GROUP BY
        spu_id
    </select>

    <select id="getSpuPublishStatus"
            resultType="com.mongoso.mgs.module.base.dal.db.spu.SpuDO">
        SELECT
        spu_id,
            CASE
                WHEN COUNT(*) = SUM(publish_status) THEN '1'
                WHEN SUM(publish_status) > 0 THEN '2'
                ELSE '0'
            END AS publishStatus
        FROM
        u_material
        WHERE
        spu_id = #{item.spuId} AND deleted = 0
        GROUP BY
        spu_id
    </select>
    <!--    <update id="unbindMaterials">-->
<!--        -->
<!--    </update>-->

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryMaterialQuotedPage"
            resultType="com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ErpMaterialQuotedRespVO">
        SELECT
            a.*, b.demand_qty, b.loss_rate, b.estimated_qty
        FROM u_material a
        LEFT JOIN u_material_bom b ON a.material_id = b.material_id AND b.fk_material_bom_id = #{reqVO.materialBomId}
        <where>
            AND a.deleted = 0
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND a.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND a.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND a.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND a.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
                AND a.material_id NOT IN
                <foreach item="item" index="index" collection="reqVO.exclMaterialIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY b.material_bom_id ASC, a.material_id ASC
    </select>

    <update id="updateMaterialCostValue" parameterType="com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO">
--遍历更新，传递list进来
        <foreach collection="list" item="item" separator=";">
            UPDATE u_material
            SET
                cost_value = #{item.costValue}
            WHERE material_id = #{item.materialId}
        </foreach>
    </update>

    <update id="updateMaterialCostValueOne" parameterType="com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO">
        UPDATE
            u_material
        SET cost_value = #{costValue},
            calc_formula = #{calcFormula},
            cost_type = #{costType}
        WHERE material_id = #{materialId}
    </update>
</mapper>
