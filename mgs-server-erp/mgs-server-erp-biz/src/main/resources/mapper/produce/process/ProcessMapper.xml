<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.process.ProcessMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListByWorkCenterId" resultType="com.mongoso.mgs.module.produce.dal.db.process.ProcessDO">
        SELECT * FROM u_process process
        LEFT JOIN u_work_center_rel workCenterRel ON process.process_id = workCenterRel.related_order_id
        WHERE data_status = 1 and workCenterRel.work_center_id = #{workCenterId}
    </select>


</mapper>
