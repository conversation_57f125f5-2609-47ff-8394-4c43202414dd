<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.reportedwork.ReportedWorkMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- ResultMap for ReportedWorkRespVO with jsonb field handling -->
    <resultMap id="ReportedWorkRespVOMap" type="com.mongoso.mgs.module.produce.controller.admin.reportedwork.vo.ReportedWorkRespVO">
        <id property="reportedWorkId" column="reported_work_id"/>
        <result property="reportedWorkCode" column="reported_work_code"/>
        <result property="reportedWorkType" column="reported_work_type"/>
        <result property="prodOrderId" column="prod_order_id"/>
        <result property="prodOrderCode" column="prod_order_code"/>
        <result property="prodWorkId" column="prod_work_id"/>
        <result property="prodWorkCode" column="prod_work_code"/>
        <result property="workCenterId" column="work_center_id"/>
        <result property="workCenterCode" column="work_center_code"/>
        <result property="workCenterName" column="work_center_name"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="processId" column="process_id"/>
        <result property="processCode" column="process_code"/>
        <result property="processName" column="process_name"/>
        <result property="finalProcess" column="final_process"/>
        <result property="reportedId" column="reported_id"/>
        <result property="priceRuleId" column="price_rule_id"/>
        <result property="priceRuleName" column="price_rule_name"/>
        <result property="reportedDt" column="reported_dt"/>
        <result property="dispatchWorkId" column="dispatch_work_id"/>
        <result property="dispatchQty" column="dispatch_qty"/>
        <result property="okQty" column="ok_qty"/>
        <result property="ngQty" column="ng_qty"/>
        <!-- jsonb字段特殊处理 -->
        <result property="ngDetailList" column="ng_detail_list" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>
        <result property="flowCardCode" column="flow_card_code"/>
        <result property="flowCardType" column="flow_card_type"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="moldId" column="mold_id"/>
        <result property="directorId" column="director_id"/>
        <result property="directorOrgId" column="director_org_id"/>
        <result property="reportBatchCode" column="report_batch_code"/>
        <result property="dataStatus" column="data_status"/>
        <result property="approvedBy" column="approved_by"/>
        <result property="approvedDt" column="approved_dt"/>
        <result property="warehouseOrgId" column="warehouse_org_id"/>
        <result property="workTimeQty" column="work_time_qty"/>
        <result property="okAmt" column="ok_amt"/>
        <result property="ngAmt" column="ng_amt"/>
        <result property="unitAmt" column="unit_amt"/>
        <result property="totalAmt" column="total_amt"/>
        <result property="pieceworkMethodDictId" column="piecework_method_dict_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <!-- 扩展字段 -->
        <result property="reportedWorkTypeDictName" column="reported_work_type_dict_name"/>
        <result property="dataStatusDictName" column="data_status_dict_name"/>
        <result property="pendingQty" column="pending_qty"/>
        <result property="approveTaskId" column="approve_task_id"/>
    </resultMap>

    <select id="selectSumOkQty" resultType="java.math.BigDecimal">
        select sum(ok_qty) from u_reported_work where dispatch_work_id = #{dispatchWorkId} and data_status = 1
    </select>

    <select id="queryReportedWorkPage" resultMap="ReportedWorkRespVOMap">
        SELECT a.reported_work_id,
               a.reported_work_code,
               a.reported_work_type,
               a.prod_order_id,
               a.prod_order_code,
               a.prod_work_id,
               a.prod_work_code,
               a.work_center_id,
               a.work_center_code,
               a.work_center_name,
               a.material_id,
               a.material_code,
               a.material_name,
               a.process_id,
               a.process_code,
               a.process_name,
               a.final_process,
               a.reported_id,
               a.price_rule_id,
               a.price_rule_name,
               a.reported_dt,
               a.dispatch_work_id,
               a.dispatch_qty,
               a.ok_qty,
               a.ng_qty,
               a.ng_detail_list,
               a.flow_card_code,
               a.flow_card_type,
               a.device_id,
               a.device_code,
               a.device_name,
               a.mold_id,
               a.director_id,
               a.director_org_id,
               a.report_batch_code,
               a.data_status,
               a.approved_by,
               a.approved_dt,
               a.warehouse_org_id,
               a.work_time_qty,
               a.ok_amt,
               a.ng_amt,
               a.unit_amt,
               a.total_amt,
               a.piecework_method_dict_id,
               a.created_by,
               a.created_dt,
               a.updated_by,
               a.updated_dt
        FROM u_reported_work a
        LEFT JOIN u_prod_work b ON a.prod_work_id = b.prod_work_id
        <where>
            <if test="reqVO.reportedWorkCode != null and reqVO.reportedWorkCode != ''">
                AND a.reported_work_code LIKE CONCAT('%', #{reqVO.reportedWorkCode}, '%')
            </if>
            <if test="reqVO.prodOrderCode != null and reqVO.prodOrderCode != ''">
                AND a.prod_order_code LIKE CONCAT('%', #{reqVO.prodOrderCode}, '%')
            </if>
            <if test="reqVO.prodWorkCode != null and reqVO.prodWorkCode != ''">
                AND a.prod_work_code LIKE CONCAT('%', #{reqVO.prodWorkCode}, '%')
            </if>
            <if test="reqVO.processCode != null and reqVO.processCode != ''">
                AND a.process_code LIKE CONCAT('%', #{reqVO.processCode}, '%')
            </if>
            <if test="reqVO.processName != null and reqVO.processName != ''">
                AND a.process_name LIKE CONCAT('%', #{reqVO.processName}, '%')
            </if>
            <if test="reqVO.reportedId != null and reqVO.reportedId != ''">
                AND a.reported_id = #{reqVO.reportedId}
            </if>
            <if test="reqVO.startReportedDt != null">
                AND a.reported_dt >= #{reqVO.startReportedDt}
            </if>
            <if test="reqVO.endReportedDt != null">
                AND a.reported_dt &lt;= #{reqVO.endReportedDt}
            </if>
            <if test="reqVO.reportedWorkType != null">
                AND a.reported_work_type = #{reqVO.reportedWorkType}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND (a.reported_work_type = 3 OR b.director_id = #{reqVO.directorId})
            </if>
            <if test="reqVO.directorOrgIdList != null and reqVO.directorOrgIdList.size > 0 ">
                AND (a.reported_work_type = 3 OR b.director_org_id IN
                    <foreach item="item" index="index" collection="reqVO.directorOrgIdList" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="reqVO.reportedWorkTypeList != null and reqVO.reportedWorkTypeList.size > 0 ">
                AND a.reported_work_type IN
                <foreach item="item" index="index" collection="reqVO.reportedWorkTypeList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.created_dt desc
    </select>

    <select id="selectReportedQty" resultType="java.math.BigDecimal">
        select COALESCE(SUM(t1.ok_qty), 0)
        from u_reported_work t1
        inner join u_dispatch_work t2 on t1.dispatch_work_id = t2.dispatch_work_id
        where t1.data_status = 1 and t1.prod_work_id = #{prodWorkId} and t2.dispatch_strategy_config = #{dispatchStrategyConfig} and t1.process_id = #{processId}
    </select>

</mapper>
