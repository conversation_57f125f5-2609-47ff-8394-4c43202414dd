<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.dispatchstrategy.DispatchStrategyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="checkMaterial" resultType="java.lang.Integer">
        select count(*)
        from u_dispatch_strategy strategy
             left join u_material_rel material on strategy.dispatch_strategy_id = material.related_order_id
        <where>
            material.source = 2 and strategy.data_status = 1
            <if test="dispatchStrategyId != null">
                and strategy.dispatch_strategy_id != #{dispatchStrategyId}
            </if>
            <if test="materialIdList != null and materialIdList.size > 0">
                and material.material_id IN
                <foreach collection="materialIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialId != null">
                and material.material_id = #{materialId}
            </if>
            <if test="materialApplyRange != null">
                and strategy.material_apply_range = #{materialApplyRange}
            </if>
        </where>

    </select>

    <select id="queryMaterial"
            resultType="com.mongoso.mgs.module.produce.dal.db.dispatchstrategy.DispatchStrategyDO">
        select strategy.*
        from u_dispatch_strategy strategy
        left join u_material_rel material on strategy.dispatch_strategy_id = material.related_order_id
        <where>
            material.source = 2 and strategy.data_status = 1
            <if test="materialIdList != null and materialIdList.size > 0">
                and material.material_id IN
                <foreach collection="materialIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dispatchStrategyId != null">
                and strategy.dispatch_strategy_id != #{dispatchStrategyId}
            </if>
            <if test="materialId != null">
                and material.material_id = #{materialId}
            </if>
        </where>
    </select>


</mapper>
