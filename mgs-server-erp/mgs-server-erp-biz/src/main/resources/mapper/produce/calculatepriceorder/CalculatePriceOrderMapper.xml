<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.calculatepriceorder.CalculatePriceOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectPage2"
            resultType="com.mongoso.mgs.module.produce.controller.admin.calculatepriceorder.vo.CalculatePriceOrderRespVO">

        SELECT
            t1.calculate_price_order_id,
            t1.calculate_price_order_code,
            t1.material_id,
            t1.material_code,
            t1.process_id,
            t1.process_code,
            t1.calculate_time_amt,
            t1.ok_calculate_unit_amt,
            t1.ng_calculate_unit_amt,
            t1.standard_working_hours,
            t1.remark,
            t1.data_status,
            t1.director_id,
            t1.director_org_id,
            t1.form_dt,
            t1.approved_by,
            t1.approved_dt,
            t1.created_by,
            t1.created_dt,
            t1.updated_by,
            t1.updated_dt,
            t2.material_name,
            t3.process_name
        FROM erp.u_calculate_price_order t1
        LEFT JOIN erp.u_material t2 ON t1.material_id = t2.material_id
        LEFT JOIN erp.u_process t3 ON t1.process_id = t3.process_id
        WHERE t1.biz_type = #{reqVO.bizType}
        <if test="reqVO.calculatePriceOrderCode != null and reqVO.calculatePriceOrderCode != ''">
            AND t1.calculate_price_order_code LIKE concat('%', #{reqVO.calculatePriceOrderCode}, '%')
        </if>
        <if test="reqVO.dataStatus != null">
            AND t1.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND t1.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND t1.director_org_id LIKE concat('%', #{reqVO.directorOrgId}, '%')
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
            AND t1.form_dt >= #{reqVO.startFormDt}
            AND t1.form_dt &lt;= #{reqVO.endFormDt}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t1.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t2.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.processCode != null and reqVO.processCode != ''">
            AND t1.process_code LIKE concat('%', #{reqVO.processCode}, '%')
        </if>
        <if test="reqVO.processName != null and reqVO.processName != ''">
            AND t3.process_name LIKE concat('%', #{reqVO.processName}, '%')
        </if>

        <if test="reqVO.calculatePriceOrderIdList != null and reqVO.calculatePriceOrderIdList.size > 0 ">
            AND t1.calculate_price_order_id IN
            <foreach item="item" index="index" collection="reqVO.calculatePriceOrderIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ORDER BY t1.calculate_price_order_id DESC

    </select>


    <select id="selectListBy"
            resultType="com.mongoso.mgs.module.produce.service.calculatepriceorder.bo.CalculatePriceOrderRuleBO2">
        SELECT
            t1.calculate_price_order_id AS priceRuleId,
            t1.calculate_price_order_code,
            t1.calculate_time_amt AS unitAmt,
            t1.ok_calculate_unit_amt AS okAmt,
            t1.ng_calculate_unit_amt AS ngAmt
        FROM erp.u_calculate_price_order t1
        WHERE  t1.data_status = 1
          AND t1.biz_type = #{bizType}
          AND t1.piecework_method_dict_id = #{pieceworkMethodDictId}
          AND t1.process_id = #{processId}
          AND t1.material_id = #{materialId}

    </select>


</mapper>
