<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.materialbom.MaterialBomMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="maxVersion" resultType="com.mongoso.mgs.module.produce.dal.db.materialbom.MaterialBomDO">
        SELECT material_bom_id ,max(version) as version,cost_value,sub_total_cost,cost_total_value FROM u_material_bom
        where material_id = #{materialId} and is_parent=1
        group by material_bom_id
        order by max(version) desc
    </select>

    <select id="batchMaxVersion" resultType="com.mongoso.mgs.module.produce.dal.db.materialbom.MaterialBomDO">
        SELECT
            t1.material_id,
            t1.material_bom_id,
            t1.cost_value,
            t1.sub_total_cost,
            t1.cost_total_value,
            t1.demand_qty,
            t2.max_version as version
        FROM (
        SELECT
        material_id,
        MAX(version) as max_version
        FROM u_material_bom
        WHERE data_status = 1
        AND material_id IN
        <foreach item="item" index="index" collection="materialIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY material_id
        ) t2
        LEFT JOIN u_material_bom t1 ON t1.material_id = t2.material_id
        WHERE t1.is_parent = 1
    </select>


    <select id="getBomByMaterialId"
            resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.BomMaterialBO">
        SELECT
            t1.material_id, t1.material_code, t1.material_name,
            t1.material_category_dict_id, t1.material_source_dict_id, t1.main_unit_dict_id,
            t1.spec_model, t1.spec_attribute_str,
            t2.material_bom_id, t2.warehouse_org_id, t2.picking_method_dict_id, t2.version,
            t2.demand_qty, t2.loss_rate, t2.estimated_qty
        FROM u_material t1
        INNER JOIN u_material_bom t2 ON t1.material_id = t2.material_id AND t2.data_status=1
        WHERE t1.deleted = 0 AND t1.data_status=1 AND t2.is_parent=1 AND t1.material_id IN
        <foreach item="item" index="index" collection="materialIdList" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectChildListByMaterialId"
            resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.BomMaterialBO">
        SELECT
            t1.material_id, t1.material_code, t1.material_name,
            t1.material_category_dict_id, t1.material_source_dict_id, t1.main_unit_dict_id,
            t1.spec_model, t1.spec_attribute_str,
            t2.material_bom_id, t2.warehouse_org_id, t2.picking_method_dict_id, t2.version,
            t2.demand_qty, t2.loss_rate, t2.estimated_qty
        FROM u_material t1
        INNER JOIN u_material_bom t2 ON t1.material_id = t2.material_id
        WHERE t1.deleted = 0 AND t2.fk_material_bom_id = (SELECT material_bom_id
                                       FROM u_material_bom
                                       WHERE is_parent=1 AND data_status=1 AND material_id=#{materialId}
        )
    </select>

    <select id="materialBomSelectPage"
            resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.BomMaterialBO">
        SELECT
            t1.material_id, t1.material_code, t1.material_name,
            t1.material_category_dict_id, t1.material_source_dict_id, t1.main_unit_dict_id,
            t1.spec_model, t1.spec_attribute_str,
            t2.material_bom_id, t2.version,
            t2.warehouse_org_id, t2.picking_method_dict_id,
            t2.demand_qty, t2.loss_rate, t2.estimated_qty, t2.remark
        FROM u_material t1
        INNER JOIN u_material_bom t2 ON t1.material_id = t2.material_id AND t1.data_status=1
        WHERE t1.deleted = 0 AND t2.is_parent=1 AND t2.data_status=1
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t1.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t1.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t1.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.materialSourceDictId != null">
            AND t1.material_source_dict_id = #{reqVO.materialSourceDictId}
        </if>
        <if test="reqVO.pickingMethodDictId != null and reqVO.pickingMethodDictId != ''">
            AND t2.picking_method_dict_id = #{reqVO.pickingMethodDictId}
        </if>
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND t1.material_id NOT IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t2.created_dt DESC
    </select>


    <select id="getMaterialByMaterialId"
            resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.MaterialBO">
        SELECT
            t1.material_id, t1.material_code, t1.material_name,
            t1.material_category_dict_id, t1.material_source_dict_id, t1.main_unit_dict_id,
            t1.spec_model, t1.spec_attribute_str, t1.purchase_standard_price, t1.processing_fee
        FROM u_material t1
        WHERE t1.deleted = 0 AND t1.material_id IN
        <foreach item="item" index="index" collection="materialIdList" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <select id="getDataStatusMaterialByMaterialId"
            resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.MaterialBO">
        SELECT
        t1.material_id, t1.material_code, t1.material_name,
        t1.material_category_dict_id, t1.material_source_dict_id, t1.main_unit_dict_id,
        t1.spec_model, t1.spec_attribute_str
        FROM u_material t1
        WHERE t1.deleted = 0 AND t1.data_status=1 AND t1.material_id IN
        <foreach item="item" index="index" collection="materialIdList" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>


    <select id="getMaterialIdByName" resultType="long">
        SELECT material_id
        FROM u_material
        WHERE deleted = 0 AND data_status=1 AND material_name LIKE concat('%', #{materialName}, '%')
    </select>

    <select id="selectAllChildrenRelations" resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.BomRelationBO">
        SELECT
            material_bom_id,
            material_id,
            COALESCE(parent_id,0) AS parent_id
        FROM u_material_bom
        WHERE is_parent = 0
--         ORDER BY parent_id, row_no
    </select>

    <select id="selectByBomIds" resultType="com.mongoso.mgs.module.produce.dal.db.materialbom.MaterialBomDO">
        SELECT material_bom_id,
        material_id,
        material_code,
        demand_qty,
        loss_rate,
        estimated_qty,
        picking_method_dict_id,
        warehouse_org_id,
        COALESCE(parent_id,0) AS parent_id
        FROM u_material_bom
        WHERE material_id IN
        <foreach item="item" index="index" collection="bomIds" open="(" separator="," close=")">
            #{item}
        </foreach>
--         ORDER BY parent_id, row_no
    </select>

    <select id="selectIPage" resultType="com.mongoso.mgs.module.produce.dal.db.materialbom.MaterialBomDO">
        select bom.*
            from u_material_bom bom
            inner join u_material m on bom.material_id = m.material_id
        <where>
            bom.is_parent = 1 and bom.data_status=1 and m.data_status=1
            <if test="reqVO.parentIdList != null and reqVO.parentIdList.size > 0">
                AND bom.parent_id IN
                <foreach item="item" index="index" collection="reqVO.parentIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.directorOrgIdList != null and reqVO.directorOrgIdList.size > 0">
                AND bom.director_org_id IN
                <foreach item="item" index="index" collection="reqVO.directorOrgIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.directorId != null">
                AND bom.director_id = #{reqVO.directorId}
            </if>
        </where>
        order by bom.created_dt desc
    </select>

    <update id="updateMaterialCostValue" parameterType="com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO">
        --遍历更新，传递list进来
        <foreach collection="list" item="item" separator=";">
            UPDATE u_material_bom
            SET
            cost_value = cost_value+#{item.costValue},
            sub_total_cost = sub_total_cost+#{item.subTotalCost},
            cost_total_value = cost_total_value+#{item.costTotalValue}
            WHERE material_bom_id = #{item.materialBomId}
        </foreach>
    </update>
</mapper>
