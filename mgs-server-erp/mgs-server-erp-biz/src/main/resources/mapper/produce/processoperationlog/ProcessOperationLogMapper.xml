<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.processoperationlog.ProcessOperationLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryProcessOperationLogPage" resultType="com.mongoso.mgs.module.produce.controller.admin.processoperationlog.vo.ProcessOperationLogRespVO">
        SELECT a.*
        FROM u_process_operation_log a
        LEFT JOIN u_prod_work b ON a.prod_work_id = b.prod_work_id
        <where>
            <if test="reqVO.processOperationCode != null and reqVO.processOperationCode != ''">
                AND a.process_operation_code LIKE CONCAT('%', #{reqVO.processOperationCode}, '%')
            </if>
            <if test="reqVO.reportedWorkCode != null and reqVO.reportedWorkCode != ''">
                AND a.reported_work_code LIKE CONCAT('%', #{reqVO.reportedWorkCode}, '%')
            </if>
            <if test="reqVO.flowCardCode != null and reqVO.flowCardCode != ''">
                AND a.flow_card_code LIKE CONCAT('%', #{reqVO.flowCardCode}, '%')
            </if>
            <if test="reqVO.prodOrderCode != null and reqVO.prodOrderCode != ''">
                AND a.prod_order_code LIKE CONCAT('%', #{reqVO.prodOrderCode}, '%')
            </if>
            <if test="reqVO.prodWorkCode != null and reqVO.prodWorkCode != ''">
                AND a.prod_work_code LIKE CONCAT('%', #{reqVO.prodWorkCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND a.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND a.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.processId != null">
                AND a.process_id = #{reqVO.processId}
            </if>
            <if test="reqVO.processCode != null and reqVO.processCode != ''">
                AND a.process_code LIKE CONCAT('%', #{reqVO.processCode}, '%')
            </if>
            <if test="reqVO.processName != null and reqVO.processName != ''">
                AND a.process_name LIKE CONCAT('%', #{reqVO.processName}, '%')
            </if>
            <if test="reqVO.reportedId != null  and reqVO.reportedId != ''">
                AND a.reported_id = #{reqVO.reportedId}
            </if>
            <if test="reqVO.startReportedDt != null">
                AND a.reported_dt >= #{reqVO.startReportedDt}
            </if>
            <if test="reqVO.endReportedDt != null">
                AND a.reported_dt &lt;= #{reqVO.endReportedDt}
            </if>
            <if test="reqVO.reportedWorkType != null">
                AND a.reported_work_type = #{reqVO.reportedWorkType}
            </if>
            <if test="reqVO.logSource != null">
                AND a.log_source = #{reqVO.logSource}
            </if>
            <if test="reqVO.logSourceList != null and reqVO.logSourceList.size > 0 ">
                AND a.log_source IN
                <foreach item="item" index="index" collection="reqVO.logSourceList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.sourceOrderId != null">
                AND a.source_order_id = #{reqVO.sourceOrderId}
            </if>
            <if test="reqVO.sourceOrderCode != null and reqVO.sourceOrderCode != ''">
                AND a.source_order_code LIKE CONCAT('%', #{reqVO.sourceOrderCode}, '%')
            </if>
            <if test="reqVO.userId != null">
                AND a.user_id = #{reqVO.userId}
            </if>
            <if test="reqVO.startCreatedDt != null">
                AND a.created_dt >= #{reqVO.startCreatedDt}
            </if>
            <if test="reqVO.endCreatedDt != null">
                AND a.created_dt &lt;= #{reqVO.endCreatedDt}
            </if>
            <if test="reqVO.startUpdatedDt != null">
                AND a.updated_dt >= #{reqVO.startUpdatedDt}
            </if>
            <if test="reqVO.endUpdatedDt != null">
                AND a.updated_dt &lt;= #{reqVO.endUpdatedDt}
            </if>
            <if test="reqVO.directorId != null">
                AND b.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.workCenterId != null">
                AND a.work_center_id = #{reqVO.workCenterId}
            </if>
            <if test="reqVO.workCenterCode != null and reqVO.workCenterCode != ''">
                AND a.work_center_code LIKE CONCAT('%', #{reqVO.workCenterCode}, '%')
            </if>
            <if test="reqVO.workCenterName != null and reqVO.workCenterName != ''">
                AND a.work_center_name LIKE CONCAT('%', #{reqVO.workCenterName}, '%')
            </if>
            <if test="reqVO.directorOrgIdList != null and reqVO.directorOrgIdList.size > 0 ">
                AND b.director_org_id IN
                <foreach item="item" index="index" collection="reqVO.directorOrgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.created_dt desc
    </select>

</mapper>
