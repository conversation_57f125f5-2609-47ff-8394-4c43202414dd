<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage2"
            resultType="com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item.WorkPickingItemRespVO">

        SELECT
            t1.work_picking_id, t1.work_picking_code, t1.work_picking_type_dict_id,t1.remark AS mainRemark,
            t1.related_order_id, t1.related_order_code,t1.form_dt,t1.director_id,
            t1.material_id AS mainMaterialId, t1.material_code AS mainMaterialCode,t1.qty,t1.version, t1.material_category_dict_id as mainMaterialCategoryDictId,
            t2.row_no, t2.material_id, t2.material_code, t2.material_category_dict_id,
            t2.picking_qty, t2.warehouse_org_id, t2.remark, t2.estimated_qty
        FROM u_work_picking t1
             inner join u_work_picking_detail t2 ON t1.work_picking_id = t2.work_picking_id
        WHERE 1=1
        <if test="reqVO.workPickingCode != null and reqVO.workPickingCode != ''">
            AND t1.work_picking_code LIKE concat('%', #{reqVO.workPickingCode}, '%')
        </if>
        <if test="reqVO.workPickingTypeDictId != null and reqVO.workPickingTypeDictId != ''">
            AND t1.work_picking_type_dict_id = #{reqVO.workPickingTypeDictId}
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND t1.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.mainMaterialCode != null and reqVO.mainMaterialCode != ''">
            AND t1.material_code LIKE concat('%', #{reqVO.mainMaterialCode}, '%')
        </if>
        <if test="reqVO.mainMaterialCategoryDictId != null and reqVO.mainMaterialCategoryDictId != ''">
            AND t1.material_category_dict_id = #{reqVO.mainMaterialCategoryDictId}
        </if>
        <if test="reqVO.mainMaterialIdList != null and reqVO.mainMaterialIdList.size > 0">
            AND t1.material_id IN
            <foreach item="item" index="index" collection="reqVO.mainMaterialIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t2.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
            AND t2.warehouse_org_id = #{reqVO.warehouseOrgId}
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t2.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0">
            AND t2.material_id IN
            <foreach item="item" index="index" collection="reqVO.materialIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.created_dt desc,t2.row_no asc
    </select>

    <select id="selectPage3"
            resultType="com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingRespVO">
        SELECT
        t1.work_picking_id, t1.work_picking_code, t1.work_picking_type_dict_id,t1.remark,
        t1.related_order_id, t1.related_order_code, t1.form_dt, t1.director_id, t1.director_org_id,
        t1.material_id, t1.material_code, t1.qty, t1.version, t1.data_status,t1.is_full_outbounded,
        t1.created_by, t1.created_dt, t1.updated_by, t1.updated_dt, t1.approved_by, t1.approved_dt,
        t2.material_name, t2.material_category_dict_id,
        t2.material_source_dict_id, t2.main_unit_dict_id, t2.spec_model, t2.spec_attribute_str
        FROM u_work_picking t1
        LEFT JOIN u_material t2 ON t1.material_id = t2.material_id AND t2.deleted = 0
        WHERE t1.work_picking_biz_type = #{reqVO.workPickingBizType}
        <if test="reqVO.workPickingCode != null and reqVO.workPickingCode != ''">
            AND t1.work_picking_code LIKE concat('%', #{reqVO.workPickingCode}, '%')
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND t1.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t1.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.dataStatus != null">
            AND t1.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.isFullOutbounded != null">
            AND t1.is_full_outbounded = #{reqVO.isFullOutbounded}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
            AND t1.form_dt >= #{reqVO.startFormDt}
            AND t1.form_dt &lt;= #{reqVO.endFormDt}
        </if>
        <if test="reqVO.directorId != null">
            AND t1.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND t1.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t2.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND t2.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t2.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        ORDER BY t1.created_dt desc
    </select>


</mapper>
