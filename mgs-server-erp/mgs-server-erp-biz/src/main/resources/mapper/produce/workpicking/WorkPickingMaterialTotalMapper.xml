<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMaterialTotalMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="outboundedQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT t.material_id as fieldId, t.material_code as code, (t.outbounded_qty-t.returned_qty) as sumQty
        FROM u_work_picking_material_total t
        WHERE t.related_order_id = #{relatedOrderId}
    </select>

    <select id="inboundedQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT t.material_id as fieldId, t.material_code as code, (t.outbounded_qty-t.inbounded_qty) as sumQty
        FROM u_work_picking_material_total t
        WHERE t.related_order_id = #{relatedOrderId}
    </select>

    <select id="outboundedQtyByRelatedOrderIdList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT  prodWord.prod_order_id as relatedOrderId,  t.outbounded_qty as outboundedQty
        FROM u_work_picking_material_total t
        INNER JOIN u_prod_work prodWord  ON prodWord.prod_work_id = t.related_order_id
        WHERE    prodWord.prod_order_id in
        <foreach collection="relatedOrderIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="inboundedQtyByRelatedOrderIdList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT prodWord.prod_order_id as relatedOrderId,  t.inbounded_qty  as inboundedQty
        FROM u_work_picking_material_total t
                 INNER JOIN u_prod_work prodWord ON prodWord.prod_work_id = t.related_order_id
        WHERE      prodWord.prod_order_id in
        <foreach collection="relatedOrderIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="queryWorkPickingQty" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT prodWord.prod_work_id as relatedOrderId,
               t.received_qty        as receivedQty,
               t.returned_qty        as returnedQty,
               t.inbounded_qty       as inboundedQty,
               t.outbounded_qty      as outboundedQty
        FROM u_work_picking_material_total t
                 INNER JOIN u_prod_work prodWord ON prodWord.prod_work_id = t.related_order_id
        WHERE    prodWord.prod_work_id in
        <foreach collection="relatedOrderIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="returnedQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT t.material_id as fieldId, t.material_code as code, (t.outbounded_qty-t.returned_qty) as sumQty
        FROM u_work_picking_material_total t
        WHERE t.related_order_id = #{relatedOrderId}
    </select>

</mapper>
