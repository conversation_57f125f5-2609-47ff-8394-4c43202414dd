<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="outboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.work_picking_detail_id as fieldId, detail.material_code as code, outboundable_qty as sumQty
        FROM u_work_picking_detail detail
        WHERE detail.work_picking_id = #{workPickingId}
    </select>

    <select id="outboundDetailQuotedList" resultType="com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item.WorkPickingItemRespVO">
        SELECT stock.warehouse_org_id, detail.*, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty,
            CASE
            WHEN stock.warehouse_org_id = detail.warehouse_org_id THEN 1
            ELSE 0
            END AS isDefaultQuoted
        FROM u_work_picking_detail detail
        LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id
        WHERE detail.work_picking_id = #{workPickingId}
        AND detail.is_material_full_outbounded = 0
    </select>

</mapper>
