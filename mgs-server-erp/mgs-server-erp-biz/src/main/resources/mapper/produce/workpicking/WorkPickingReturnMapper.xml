<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd.WorkPickingReturnMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectPage2"
            resultType="com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.item.WorkPickingReturnItemRespVO">

        SELECT
            t1.work_picking_return_id, t1.work_picking_return_code, t1.work_picking_return_type_dict_id,
            t1.related_order_id, t1.related_order_code,t1.form_dt,
            t2.material_id, t2.material_code,t2.row_no,t2.qty,
            t2.warehouse_org_id,t2.remark,t2.material_category_dict_id
        FROM u_work_picking_return  t1
        inner join u_work_picking_return_detail t2 ON t1.work_picking_return_id = t2.work_picking_return_id
        WHERE 1=1
        <if test="reqVO.workPickingReturnCode != null and reqVO.workPickingReturnCode != ''">
            AND t1.work_picking_return_code LIKE concat('%', #{reqVO.workPickingReturnCode}, '%')
        </if>
        <if test="reqVO.workPickingReturnTypeDictId != null and reqVO.workPickingReturnTypeDictId != ''">
            AND t1.work_picking_return_type_dict_id = #{reqVO.workPickingReturnTypeDictId}
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND t1.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t2.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
            AND t2.warehouse_org_id = #{reqVO.warehouseOrgId}
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t2.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0">
            AND t2.material_id IN
            <foreach item="item" index="index" collection="reqVO.materialIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.created_dt desc,t2.row_no asc
    </select>

</mapper>
