<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.materialalternative.MaterialAlternativeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectPage3"
            resultType="com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.materialalternative.MaterialAlternativeRespVO">

        SELECT
        t1.alternative_material_id,
        t1.alternative_material_code,
        t1.material_id,
        t1.material_code,
        t1.remark,
        t1.data_status,
        t1.approved_by,
        t1.approved_dt,
        t1.form_dt,
        t1.director_id,
        t1.director_org_id,
        t1.created_by,
        t1.created_dt,
        t1.updated_by,
        t1.updated_dt,
        t2.material_name,
        t2.material_category_dict_id,
        t2.main_unit_dict_id,
        t2.spec_model
        FROM erp.u_material_alternative t1
        LEFT JOIN erp.u_material t2 ON t1.material_id = t2.material_id
        WHERE t2.data_status=1
        <if test="reqVO.alternativeMaterialCode != null and reqVO.alternativeMaterialCode != ''">
            AND t1.alternative_material_code LIKE concat('%', #{reqVO.alternativeMaterialCode}, '%')
        </if>
        <if test="reqVO.dataStatus != null">
            AND t1.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.alternativeStrategy != null">
            AND t1.alternative_strategy = #{reqVO.alternativeStrategy}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
            AND t1.form_dt >= #{reqVO.startFormDt}
            AND t1.form_dt &lt;= #{reqVO.endFormDt}
        </if>
        <if test="reqVO.directorId != null">
            AND t1.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND t1.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t2.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t2.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t2.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND t2.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
        </if>
        ORDER BY t1.alternative_material_id DESC
    </select>


    <select id="selectListByMaterialIds"
            resultType="com.mongoso.mgs.module.produce.service.materialalternative.bo.AlternativeMaterialBO">
        SELECT
            t1.alternative_material_id,
            t1.alternative_strategy,
            t1.material_id AS mainMaterialId,
            t2.material_id,
            t3.material_name
        FROM erp.u_material_alternative t1
            LEFT JOIN erp.u_material_alternative_detail t2 ON t1.alternative_material_id = t2.alternative_material_id
            LEFT JOIN erp.u_material t3 ON t3.material_id = t2.material_id AND  t3.deleted = 0
        WHERE t1.data_status=1
            AND t1.material_id IN
        <foreach item="item" index="index" collection="materialIdList" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectListByMaterialId"
            resultType="com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.related.MaterialAlternativeVO">

        SELECT
        t2.material_id,
        t2.material_code,
        t2.main_qty,
        t2.alternative_qty
        FROM erp.u_material_alternative t1
        LEFT JOIN erp.u_material_alternative_detail t2 ON t1.alternative_material_id = t2.alternative_material_id
        WHERE t1.data_status=1
        AND t1.material_id = #{materialId}
    </select>

    <select id="selectListByMaterialIdList"
            resultType="com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.related.MaterialAlternativeVO">

        SELECT
            t1.material_id AS mainMaterialId,
            t2.material_id,
            t2.main_qty,
            t2.alternative_qty
        FROM erp.u_material_alternative t1
                 LEFT JOIN erp.u_material_alternative_detail t2 ON t1.alternative_material_id = t2.alternative_material_id
        WHERE t1.data_status=1
        AND t1.material_id IN
        <foreach item="item" index="index" collection="materialIdList" open="(" separator=","
                 close=")">
            #{item}
        </foreach>

    </select>


</mapper>
