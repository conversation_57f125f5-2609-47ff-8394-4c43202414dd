<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.materialanalysis.MaterialAnalysisMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList4Purchase"
            resultType="com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisRespVO"
            parameterType="com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisQueryReqVO">
        SELECT DISTINCT analysis.*
        FROM u_material_analysis analysis
        LEFT JOIN u_material_analysis_total total ON total.material_analysis_id = analysis.material_analysis_id
        WHERE total.material_source_dict_id = #{reqVO.materialSourceDictId}
        AND analysis.data_status = #{reqVO.dataStatus}
        AND analysis.use_enable = 1
        <if test="reqVO.isFullPurchased != null">
          AND analysis.is_full_purchased = #{reqVO.isFullPurchased}
        </if>
        <if test="reqVO.isMaterialFullPurchased != null">
          AND total.is_material_full_purchased = #{reqVO.isMaterialFullPurchased}
        </if>
        <if test="reqVO.isFullPlaned != null">
          AND analysis.is_full_planed = #{reqVO.isFullPlaned}
        </if>
        <if test="reqVO.isMaterialFullPlaned != null">
            AND total.is_material_full_planed = #{reqVO.isMaterialFullPlaned}
        </if>
        ORDER BY analysis.material_analysis_id DESC
    </select>
<!--    <select id="queryPage"-->
<!--            resultType="com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisRespVO">-->
<!--        SELECT DISTINCT analysis.*-->
<!--        FROM u_material_analysis analysis-->
<!--        LEFT JOIN u_material_analysis_total total ON total.material_analysis_id = analysis.material_analysis_id-->
<!--        WHERE total.material_source_dict_id = #{reqVO.materialSourceDictId}-->
<!--        AND analysis.data_status = #{reqVO.dataStatus}-->
<!--        <if test="reqVO.isFullPurchased != null">-->
<!--            AND analysis.is_full_purchased = #{reqVO.isFullPurchased}-->
<!--        </if>-->
<!--        <if test="reqVO.isMaterialFullPurchased != null">-->
<!--            AND total.is_material_full_purchased = #{reqVO.isMaterialFullPurchased}-->
<!--        </if>-->
<!--        <if test="reqVO.isFullPlaned != null">-->
<!--            AND analysis.is_full_planed = #{reqVO.isFullPlaned}-->
<!--        </if>-->
<!--        <if test="reqVO.isMaterialFullPlaned != null">-->
<!--            AND total.is_material_full_planed = #{reqVO.isMaterialFullPlaned}-->
<!--        </if>-->
<!--        <if test="reqVO.materialAnalysisCode != null">-->
<!--            AND analysis.material_analysis_code = #{reqVO.materialAnalysisCode}-->
<!--        </if>-->
<!--        <if test="reqVO.materialAnalysisTypeDictId != null and reqVO.materialAnalysisTypeDictId != ''">-->
<!--            AND analysis.material_analysis_type_dict_id = #{reqVO.materialAnalysisTypeDictId}-->
<!--        </if>-->
<!--        <if test="reqVO.relatedOrderCode != null">-->
<!--            AND analysis.related_order_code = #{reqVO.relatedOrderCode}-->
<!--        </if>-->
<!--        <if test="reqVO.analysisScope != null">-->
<!--            AND analysis.analysis_scope = #{reqVO.analysisScope}-->
<!--        </if>-->
<!--    </select>-->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisRespVO">
        SELECT DISTINCT analysis.*
        FROM u_material_analysis analysis
        LEFT JOIN u_material_analysis_total total ON total.material_analysis_id = analysis.material_analysis_id
        WHERE total.material_source_dict_id = #{reqVO.materialSourceDictId}
        AND analysis.data_status = #{reqVO.dataStatus}
        AND analysis.use_enable = 1
        <if test="reqVO.isFullPurchased != null">
            AND analysis.is_full_purchased = #{reqVO.isFullPurchased}
        </if>
        <if test="reqVO.isMaterialFullPurchased != null">
            AND total.is_material_full_purchased = #{reqVO.isMaterialFullPurchased}
        </if>
        <if test="reqVO.isFullPlaned != null">
            AND analysis.is_full_planed = #{reqVO.isFullPlaned}
        </if>
        <if test="reqVO.isMaterialFullPlaned != null">
            AND total.is_material_full_planed = #{reqVO.isMaterialFullPlaned}
        </if>
        <if test="reqVO.materialAnalysisCode != null and reqVO.materialAnalysisCode != ''">
            AND analysis.material_analysis_code LIKE CONCAT('%', #{reqVO.materialAnalysisCode}, '%')
        </if>
        <if test="reqVO.materialAnalysisTypeDictId != null and reqVO.materialAnalysisTypeDictId != ''">
            AND analysis.material_analysis_type_dict_id = #{reqVO.materialAnalysisTypeDictId}
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND analysis.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.analysisScope != null and reqVO.analysisScope != ''">
            AND analysis.analysis_scope LIKE CONCAT('%', #{reqVO.analysisScope}, '%')
        </if>
        ORDER BY analysis.material_analysis_id DESC
    </select>
</mapper>
