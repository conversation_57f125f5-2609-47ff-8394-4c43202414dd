<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.prodworkmaterialbom.ProdWorkMaterialBomMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="outboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT bom.material_bom_id as fieldId, bom.material_code as code, outboundable_qty as sumQty
        FROM u_prod_work_material_bom bom
        WHERE bom.parent_id = #{prodWorkId}
    </select>

    <select id="outboundDetailQuotedList" resultType="com.mongoso.mgs.module.produce.controller.admin.prodworkmaterialbom.vo.ProdWorkMaterialBomQuotedRespVO">
        SELECT stock.warehouse_org_id, bom.*, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty,
             m.material_name, m.material_category_dict_id, m.spec_model, m.spec_attribute_str, bom.act_used_qty backflushQty
        FROM u_prod_work_material_bom bom
        LEFT JOIN u_material m ON bom.material_id = m.material_id
        LEFT JOIN u_material_stock stock ON bom.material_id = stock.material_id
        WHERE bom.parent_id = #{prodWorkId}
        AND bom.picking_method_dict_id = 1
        AND bom.is_material_full_outbounded = 0
    </select>

</mapper>
