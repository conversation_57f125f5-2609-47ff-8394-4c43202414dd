<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="findProdWorkQtyByProdId"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkRespBO">

        select prodOrderId,sum(estimatedQty) as estimatedQty,sum(inboundedQty) as inboundedQty,sum(estimateInboundQty) as estimateInboundQty
         from (
            select works.prod_order_id as prodOrderId,works.prod_work_id,(case when works.is_inbound = 1 then works.work_plan_total_qty else 0 end) as estimateInboundQty,
                   works.work_plan_total_qty*sum(COALESCE(worksBom.estimated_qty,0)) as estimatedQty,works.inbounded_qty as inboundedQty
                from u_prod_work works
                     left join u_prod_work_material_bom worksBom on works.prod_work_id=worksBom.parent_id
            <where>
                works.data_status = 1
                <if test="erpProdIds != null and erpProdIds.size > 0 ">
                    and works.prod_order_id IN
                    <foreach item="item" index="index" collection="erpProdIds"  open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            group by works.prod_order_id,works.prod_work_id,works.is_inbound,works.inbounded_qty
        )tab
        group by tab.prodOrderId

    </select>

    <select id="findProdWorkPickQtyByProdId"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkRespBO">
        select works.prod_order_id as prodOrderId,sum(COALESCE(pick.total_qty,0)) as pickQty
        from u_prod_work works
        left join u_work_picking pick on works.prod_work_id=pick.related_order_id and pick.data_status = 1
        <where>
            works.data_status = 1
            <if test="erpProdIds != null and erpProdIds.size > 0 ">
                and works.prod_order_id IN
                <foreach item="item" index="index" collection="erpProdIds"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by works.prod_order_id

    </select>

    <select id="findProdWorkReturnQtyByProdId"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkRespBO">
        select works.prod_order_id as prodOrderId,sum(COALESCE(pick_return.total_qty,0)) as pickReturnQty
        from u_prod_work works
        left join u_work_picking_return pick_return on works.prod_work_id=pick_return.related_order_id and pick_return.data_status = 1
        <where>
            works.data_status = 1
            <if test="erpProdIds != null and erpProdIds.size > 0 ">
                and works.prod_order_id IN
                <foreach item="item" index="index" collection="erpProdIds"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by works.prod_order_id
    </select>

    <select id="findProdWorkPickQtyByWorkId"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkRespBO">
        select works.prod_work_id as prodWorkId,sum(COALESCE(pick.total_qty,0)) as pickQty
        from u_prod_work works
        left join u_work_picking pick on works.prod_work_id=pick.related_order_id and pick.data_status = 1
        <where>
            works.data_status = 1
            <if test="prodWorkIds != null and prodWorkIds.size > 0 ">
                and works.prod_work_id IN
                <foreach item="item" index="index" collection="prodWorkIds"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by works.prod_work_id
    </select>

    <select id="findProdWorkReturnQtyByWorkId"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkRespBO">
        select works.prod_work_id as prodWorkId,sum(COALESCE(pick_return.total_qty,0)) as pickReturnQty
        from u_prod_work works
        left join u_work_picking_return pick_return on works.prod_work_id=pick_return.related_order_id and pick_return.data_status = 1
        <where>
            works.data_status = 1
            <if test="prodWorkIds != null and prodWorkIds.size > 0 ">
                and works.prod_work_id IN
                <foreach item="item" index="index" collection="prodWorkIds"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by works.prod_work_id
    </select>

    <select id="queryProdWorkTrackReport"
            resultType="com.mongoso.mgs.module.produce.controller.admin.report.vo.ProdWorkReportRespVO">

        select tab1.*,sum(COALESCE(dispatchs.dispatch_qty,0)) as dispatch_qty
            from (
                select  works.prod_work_id,works.prod_order_code,works.prod_work_code,works.work_plan_total_qty,works.work_act_total_qty,works.form_status,works.is_inbound,works.inbounded_qty as inboundedQty,
                        works.material_id,works.material_code,works.plan_start_date,works.plan_end_date,works.act_end_date as actEndDate,works.created_dt,
                        sum(worksBom.estimated_qty) as estimatedQty,count(worksBom.material_bom_id) as worksBomNum,
                        works.director_id,works.director_org_id
                from u_prod_work works
                left join u_prod_work_material_bom worksBom on works.prod_work_id=worksBom.parent_id
                <where>
                    works.data_status = 1
                    <if test="reqVO.prodOrderCode != null and reqVO.prodOrderCode != ''">
                        and works.prod_order_code LIKE concat('%', #{reqVO.prodOrderCode}, '%')
                    </if>
                    <if test="reqVO.prodWorkCode != null and reqVO.prodWorkCode != ''">
                        AND works.prod_work_code LIKE concat('%', #{reqVO.prodWorkCode}, '%')
                    </if>
                    <if test="reqVO.formStatus != null">
                        AND works.form_status = #{reqVO.formStatus}
                    </if>
                    <if test="reqVO.directorId != null">
                        AND works.director_id = #{reqVO.directorId}
                    </if>
                    <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                        AND works.director_org_id = #{reqVO.directorOrgId}
                    </if>
                    <if test="reqVO.startPlanStartDate != null and reqVO.endPlanStartDate != null ">
                        AND (
                        (works.plan_start_date BETWEEN #{reqVO.startPlanStartDate} AND #{reqVO.endPlanStartDate}) OR
                        (works.plan_end_date BETWEEN #{reqVO.startPlanStartDate} AND #{reqVO.endPlanStartDate})
                        )
                    </if>
                    <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
                        AND works.material_id IN
                        <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
                group by  works.prod_work_id,works.prod_order_code,works.prod_work_code,works.work_plan_total_qty,works.work_act_total_qty,works.form_status,works.is_inbound,works.inbounded_qty,
                          works.material_id,works.material_code,works.plan_start_date,works.plan_end_date,works.act_end_date,works.created_dt,
                          works.director_id,works.director_org_id
        )tab1
        left join u_dispatch_work dispatchs on dispatchs.prod_work_id=tab1.prod_work_id
        group by tab1.prod_work_id,tab1.prod_order_code,tab1.prod_work_code,tab1.work_plan_total_qty,tab1.work_act_total_qty,tab1.form_status,tab1.is_inbound,tab1.inboundedQty,
                 tab1.material_id,tab1.material_code,tab1.plan_start_date,tab1.plan_end_date,tab1.actEndDate,tab1.created_dt,
                 tab1.director_id,tab1.director_org_id,tab1.estimatedQty,tab1.worksBomNum
        order by tab1.created_dt desc
    </select>

    <select id="selectSumQty"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkQtyBO">
        select prod_work_id,prod_work_code,material_id,work_plan_total_qty
          from u_prod_work
        where data_status = 1
        <if test="reqVO.formStatusList != null and reqVO.formStatusList.size > 0 ">
            and form_status IN
            <foreach item="item" index="index" collection="reqVO.formStatusList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
            and material_id IN
            <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.demandDate != null">
            AND plan_end_date &lt;= #{reqVO.demandDate}
        </if>
    </select>

    <select id="selectDispatchWorkCount" resultType="java.lang.Integer">
        select sum(qty) as qty
        from (
            select count(1) as qty from u_dispatch_work where prod_work_id = #{prodWorkId}
            union all
            select count(1) as qty from u_process_out_demand where prod_work_id =#{prodWorkId}
        )tabA
    </select>


</mapper>
