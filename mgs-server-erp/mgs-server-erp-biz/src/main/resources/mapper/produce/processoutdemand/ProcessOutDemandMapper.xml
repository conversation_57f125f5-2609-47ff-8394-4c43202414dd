<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectProcessOutDemandDetailQtyByPW"
            resultType="com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO">
        SELECT demand.prod_order_id,demand.prod_order_code,demand.prod_work_id,demand.prod_work_code,demand.material_id,demand.material_code,
               demand.work_plan_total_qty,sum(COALESCE(demand.out_demand_qty, 0)) as outDemandQty,sum(COALESCE(demand.purchaseable_qty, 0)) as purchaseableQty,
               demand.material_name,demand.process_out_demand_id,demand.process_out_demand_code,demand.process_method
        FROM u_process_out_demand demand
        <where>
            dispatch_method = 0 and COALESCE(demand.purchaseable_qty, 0)>0
            <if test="prodWorkIdList != null and prodWorkIdList.size > 0 ">
                AND demand.prod_work_id IN
                <foreach item="item" index="index" collection="prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="processId != null ">
                AND demand.process_id = #{processId}
            </if>
        </where>
        group by demand.prod_order_id,demand.prod_order_code,demand.prod_work_id,demand.prod_work_code,demand.material_id,demand.material_code,
                 demand.work_plan_total_qty,demand.material_name,demand.process_out_demand_id,demand.process_out_demand_code,demand.process_method
        HAVING(sum(COALESCE(demand.purchaseable_qty, 0)))>0
    </select>

    <select id="selectProcessOutDemandDetailQty"
            resultType="com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO">
        SELECT demand.process_id,demand.final_process,demand.piecework_method_dict_id,demand.material_id,demand.material_code,demand.material_name,sum(COALESCE(demand.out_demand_qty, 0)) as outDemandQty,sum(COALESCE(demand.purchased_qty, 0)) as purchasedQty
        FROM u_process_out_demand demand
        <where>
            dispatch_method = 1
            <if test="prodWorkIdList != null and prodWorkIdList.size > 0 ">
                AND demand.prod_work_id IN
                <foreach item="item" index="index" collection="prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="processId != null ">
                AND demand.process_id = #{processId}
            </if>
            <if test="pieceworkMethodDictId != null and pieceworkMethodDictId != '' ">
                AND demand.piecework_method_dict_id = #{pieceworkMethodDictId}
            </if>
        </where>
        group by demand.process_id,demand.final_process,demand.piecework_method_dict_id,demand.material_id,demand.material_code,demand.material_name
    </select>

    <select id="findAbleProcessOutDemandList"
            resultType="com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO">
        select demand.process_out_demand_id,demand.work_plan_total_qty,demand.out_demand_qty+COALESCE(dispatch.dispatch_qty, 0) as outDemandQty
        from u_process_out_demand as demand
             left join u_dispatch_work as dispatch on dispatch.prod_work_id = demand.prod_work_id and dispatch.process_id = demand.process_id
        <where>
            demand.form_status in (0,1) and demand.work_plan_total_qty-demand.out_demand_qty-COALESCE(dispatch.dispatch_qty, 0) > 0
            <if test="reqVO.prodWorkIdList != null and reqVO.prodWorkIdList.size > 0 ">
                AND demand.prod_work_id IN
                <foreach item="item" index="index" collection="reqVO.prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.materialId != null ">
                AND demand.material_id = #{reqVO.materialId}
            </if>
            <if test="reqVO.dispatchMethod != null ">
                AND demand.dispatch_method = #{reqVO.dispatchMethod}
            </if>
            <if test="reqVO.processId != null ">
                AND demand.process_id = #{reqVO.processId}
            </if>
            <if test="reqVO.pieceworkMethodDictId != null and reqVO.pieceworkMethodDictId != '' ">
                AND demand.piecework_method_dict_id = #{reqVO.pieceworkMethodDictId}
            </if>
        </where>
    </select>

    <select id="findReportAbleProcessOutDemandList"
            resultType="com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandRespVO">
        SELECT demand.*,COALESCE(demand.receipted_qty, 0) - COALESCE(demand.ok_qty, 0) as pendingQty,prodWork.is_auto_inbound as isAutoInbound
        FROM u_process_out_demand demand
             left join u_prod_work prodWork ON demand.prod_work_id = prodWork.prod_work_id
        <where>
            COALESCE(demand.receipted_qty, 0)-COALESCE(demand.ok_qty, 0)>0 AND prodWork.form_status IN (0,1)
            <if test="reqVO.processCode != null and reqVO.processCode != ''">
                AND demand.process_code LIKE CONCAT('%', #{reqVO.processCode}, '%')
            </if>
            <if test="reqVO.processId != null ">
                AND demand.process_id = #{reqVO.processId}
            </if>
            <if test="reqVO.processName != null and reqVO.processName != ''">
                AND demand.process_name LIKE CONCAT('%', #{reqVO.processName}, '%')
            </if>
            <if test="reqVO.processOutDemandId != null ">
                AND demand.process_out_demand_id = #{reqVO.processOutDemandId}
            </if>
            <if test="reqVO.processOutDemandCode != null and reqVO.processOutDemandCode != ''">
                AND demand.process_out_demand_code LIKE CONCAT('%', #{reqVO.processOutDemandCode}, '%')
            </if>
            <if test="reqVO.prodOrderCode != null and reqVO.prodOrderCode != ''">
                AND demand.prod_order_code LIKE CONCAT('%', #{reqVO.prodOrderCode}, '%')
            </if>
            <if test="reqVO.prodWorkCode != null and reqVO.prodWorkCode != ''">
                AND demand.prod_work_code LIKE CONCAT('%', #{reqVO.prodWorkCode}, '%')
            </if>
        </where>
    </select>

    <select id="selectOutDemandQty" resultType="java.math.BigDecimal">
        select COALESCE(SUM(out_demand_qty), 0)
        from u_process_out_demand
        where prod_work_id = #{prodWorkId} and dispatch_strategy_config = #{dispatchStrategyConfig} and process_id = #{processId}
    </select>

    <select id="sumDispatchQty"
            resultType="com.mongoso.mgs.module.produce.service.dispatchwork.bo.DispatchQtyRespBO">
        select prod_work_id,work_plan_total_qty,work_plan_total_qty-COALESCE(sum(out_demand_qty),0) as dispatchAbleQty
        from u_process_out_demand
        <where>
            <if test="reqVO.prodWorkIdList != null and reqVO.prodWorkIdList.size > 0 ">
                AND prod_work_id IN
                <foreach item="item" index="index" collection="reqVO.prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.prodWorkId != null ">
                and prod_work_id = #{reqVO.prodWorkId}
            </if>
            <if test="reqVO.processId != null ">
                AND process_id = #{reqVO.processId}
            </if>
            <if test="reqVO.dispatchMethod != null ">
                AND dispatch_method = #{reqVO.dispatchMethod}
            </if>
            <if test="reqVO.pieceworkMethodDictId != null and reqVO.pieceworkMethodDictId !='' ">
                AND piecework_method_dict_id = #{reqVO.pieceworkMethodDictId}
            </if>
            <if test="reqVO.materialId != null ">
                AND material_id = #{reqVO.materialId}
            </if>
        </where>
        group by prod_work_id,work_plan_total_qty
    </select>

    <update id="updateReceiptQtyById">
        update u_process_out_demand set receipted_qty = receipted_qty + #{receiptQty}
        where process_out_demand_id = #{processOutDemandId}
    </update>


</mapper>
