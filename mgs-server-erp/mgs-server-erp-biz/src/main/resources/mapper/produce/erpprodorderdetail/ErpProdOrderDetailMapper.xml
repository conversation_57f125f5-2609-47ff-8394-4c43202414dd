<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.erpprodorderdetail.ErpProdOrderDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateInboundQty">
        UPDATE u_erp_prod_order_detail
        SET outsource_inbound_qty = outsource_inbound_qty + #{reqVO.outsourceInboundQty}
        WHERE material_id = #{reqVO.materialId} AND prod_order_id = #{reqVO.prodOrderId}
    </update>

    <select id="queryList" resultType="com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO"
            parameterType="com.mongoso.mgs.module.produce.controller.admin.erpprodorderdetail.vo.ErpProdOrderDetailQueryReqVO">
        SELECT detail.*
        FROM u_erp_prod_order_detail detail
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.materialSourceDictId != null">
                AND material.material_source_dict_id = #{reqVO.materialSourceDictId}
            </if>
            <if test="reqVO.prodOrderId != null">
                AND detail.prod_order_id = #{reqVO.prodOrderId}
            </if>
            <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size >0">
                AND detail.material_id NOT IN
                <foreach collection="reqVO.exclMaterialIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.materialSourceDictIdList != null and reqVO.materialSourceDictIdList.size >0">
                AND detail.material_source_dict_id IN
                <foreach collection="reqVO.materialSourceDictIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMaterialAnalysis" resultType="com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO">
        SELECT detail.material_id,sum(detail.prod_plan_qty) as prodPlanQty
        FROM u_erp_prod_order_detail detail
        LEFT JOIN u_erp_prod_order prod_order ON prod_order.prod_order_id = detail.prod_order_id
        <where>
            prod_order.data_status = 1
            <if test="relatedOrderId != null">
                AND prod_order.related_order_id = #{relatedOrderId}
            </if>
            <if test="prodOrderId != null">
                or prod_order.prod_order_id = #{prodOrderId}
            </if>
         </where>
        group by detail.material_id
    </select>

    <select id="selectMaterialIdList" resultType="java.lang.Long">
        SELECT detail.material_id
        FROM u_erp_prod_order_detail detail
        LEFT JOIN u_erp_prod_order prod_order ON prod_order.prod_order_id = detail.prod_order_id
        <where>
            prod_order.data_status = 1
            <if test="relatedOrderId != null">
                AND prod_order.related_order_id = #{relatedOrderId}
            </if>
            <if test="prodOrderId != null">
                AND prod_order.prod_order_id = #{prodOrderId}
            </if>
        </where>
    </select>


</mapper>
