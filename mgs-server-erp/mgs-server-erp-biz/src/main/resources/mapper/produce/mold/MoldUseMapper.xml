<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.mold.MoldUseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="moldUseDetailListByMoldUseId"
            resultType="com.mongoso.mgs.module.produce.service.mold.bo.MoldUseDetailRespBO">
        SELECT
            t1.id, t1.mold_use_id, t1.returned_status, t1.mold_code,
            t2.mold_id, t2.mold_name,
            t2.stock_status, t2.mold_status, t2.data_status
        FROM u_mold_use_detail t1
            LEFT JOIN u_mold t2 ON t1.mold_id = t2.mold_id AND t2.deleted = 0
        WHERE t1.mold_use_id = #{moldUseId}
    </select>

    <select id="moldUseDetailListByMoldUseId2"
            resultType="com.mongoso.mgs.module.produce.controller.admin.mold.vo.molduse.MoldUseDetailRespVO">
        SELECT
            t1.id, t1.row_no, t1.mold_use_id, t1.used_by, t1.mold_code,
            t1.returned_by, t1.returned_date, t1.returned_status,
            t2.mold_id, t2.mold_name, t2.mold_type_dict_id, t2.mold_model,
            t2.stock_status, t2.mold_status, t2.data_status
        FROM u_mold_use_detail t1
        LEFT JOIN u_mold t2 ON t1.mold_id = t2.mold_id AND t2.deleted = 0
        WHERE t1.mold_use_id = #{moldUseId}
        order by t1.row_no asc
    </select>

</mapper>
