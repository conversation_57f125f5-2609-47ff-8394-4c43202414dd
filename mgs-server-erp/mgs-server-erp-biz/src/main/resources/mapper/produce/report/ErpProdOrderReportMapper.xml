<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.report.ErpProdOrderReportMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="findErpProdOrderDetail"
            resultType="com.mongoso.mgs.module.produce.controller.admin.report.vo.ErpProdOrderReportRespVO">

        select prod.prod_order_id, prod.prod_order_code, prod.prod_order_type_dict_id, prod.related_order_id, prod.related_order_code, prod.form_status,
               prod.delivery_date, prod.plan_start_date, prod.plan_end_date, prod.director_id, prod.director_org_id,prod.act_end_date as actEndDate,
               prod.prod_plan_total_qty, prod.prod_act_total_qty,
               sum(detail.work_plan_qty) as workPlanTotalQty,sum(detail.purchased_qty) as outsourceQty,sum(detail.ok_qty) as ok_qty,sum(detail.ng_qty) as ng_qty
        from u_erp_prod_order prod
             left join u_erp_prod_order_detail detail on prod.prod_order_id=detail.prod_order_id
             left join "platform".t_dict dict on dict.dict_obj_code ='ERP416' and dict.dict_code = prod.prod_order_type_dict_id
         <where>
             prod.data_status = 1
             <if test="reqVO.prodOrderCode != null and reqVO.prodOrderCode != ''">
                 and prod.prod_order_code LIKE concat('%', #{reqVO.prodOrderCode}, '%')
             </if>
             <if test="reqVO.prodOrderTypeDictId != null and reqVO.prodOrderTypeDictId != ''">
                 AND prod.prod_order_type_dict_id = #{reqVO.prodOrderTypeDictId}
             </if>
             <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                 AND prod.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
             </if>
             <if test="reqVO.formStatus != null">
                 AND prod.form_status = #{reqVO.formStatus}
             </if>
             <if test="reqVO.directorId != null">
                 AND prod.director_id = #{reqVO.directorId}
             </if>
             <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                 AND prod.director_org_id = #{reqVO.directorOrgId}
             </if>
             <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
                 AND prod.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate}
             </if>
             <if test="reqVO.startPlanStartDate != null and reqVO.endPlanStartDate != null ">
                 AND (
                     (prod.plan_start_date BETWEEN #{reqVO.startPlanStartDate} AND #{reqVO.endPlanStartDate}) OR
                     (prod.plan_end_date BETWEEN #{reqVO.startPlanStartDate} AND #{reqVO.endPlanStartDate})
                 )
             </if>
         </where>
        group by prod.prod_order_id, prod.prod_order_code, prod.prod_order_type_dict_id, prod.related_order_id, prod.related_order_code, prod.form_status,
                 prod.delivery_date, prod.plan_start_date, prod.plan_end_date, prod.director_id, prod.director_org_id,
                 prod.prod_plan_total_qty, prod.prod_act_total_qty,prod.created_dt,prod.act_end_date
        order by prod.created_dt desc

    </select>

    <select id="findProdOutTrackReport"
            resultType="com.mongoso.mgs.module.produce.controller.admin.report.vo.ProdOutReportRespVO">

        select tab1.*,sum(pick_return.total_qty) as pickReturnQty
        from (
            SELECT purchase.purchase_order_id,purchase.purchase_order_code,purchase.related_order_id,purchase.related_order_code,purchase.related_supplier_id,
                    purchase.delivery_date,purchase.incl_tax_total_amt,purchase.director_id,purchase.director_org_id,purchase.is_take_material,purchase.purchase_qty,
                    sum(pick.total_qty) as pickQty,purchase.created_dt
            FROM u_purchase_order purchase
            left join u_work_picking pick on pick.related_order_id=purchase.purchase_order_id and pick.data_status=1
            left join u_supplier supplier on supplier.supplier_id = purchase.related_supplier_id and supplier.data_status=1
            <where>
                purchase.data_status = 1 and purchase.purchase_order_biz_type = 3
                <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
                    and purchase.purchase_order_code LIKE concat('%', #{reqVO.purchaseOrderCode}, '%')
                </if>
                <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                    AND purchase.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
                </if>
                <if test="reqVO.relatedSupplierId != null">
                    AND purchase.related_supplier_id = #{reqVO.relatedSupplierId}
                </if>
                <if test="reqVO.directorId != null">
                    AND purchase.director_id = #{reqVO.directorId}
                </if>
                <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                    AND purchase.director_org_id = #{reqVO.directorOrgId}
                </if>
                <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
                    AND purchase.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate}
                </if>
            </where>
            group by purchase.purchase_order_id,purchase.purchase_order_code,purchase.related_order_id,purchase.related_order_code,purchase.related_supplier_id,purchase.delivery_date,purchase.incl_tax_total_amt,
            purchase.director_id,purchase.director_org_id,purchase.is_take_material,purchase.purchase_qty,purchase.created_dt
        ) tab1
        left join u_work_picking_return pick_return on pick_return.related_order_id=tab1.purchase_order_id and pick_return.data_status=1
            group by tab1.purchase_order_id,tab1.purchase_order_code,tab1.related_order_id,tab1.related_order_code,tab1.related_supplier_id,tab1.created_dt,
                     tab1.delivery_date,tab1.incl_tax_total_amt,tab1.director_id,tab1.director_org_id,tab1.is_take_material,tab1.purchase_qty,tab1.pickQty
            order by tab1.created_dt desc
    </select>

</mapper>
