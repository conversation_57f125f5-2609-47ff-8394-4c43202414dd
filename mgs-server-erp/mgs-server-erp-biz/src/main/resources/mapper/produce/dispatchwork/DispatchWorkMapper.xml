<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.dispatchwork.DispatchWorkMapper">


    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectDispatchMaterialList" resultType="com.mongoso.mgs.module.produce.dal.db.dispatchwork.DispatchWorkDO">
        SELECT prod_work.prod_order_id,prod_work.prod_order_code,prod_work.prod_work_id,prod_work.prod_work_code,prod_work.material_id,prod_work.material_code,prod_work.plan_start_date,prod_work.plan_end_date,
               prod_work.work_plan_total_qty,prod_work.work_plan_total_qty-COALESCE(dispatch.dispatch_qty, 0) as dispatchAbleQty,dispatch.process_id,
               material.material_name
        FROM u_prod_work prod_work
        left join u_dispatch_work dispatch on prod_work.prod_work_id = dispatch.prod_work_id and prod_work.material_id = dispatch.material_id
        left join u_material material on prod_work.material_id = material.material_id and material.deleted = 0
        <where>
            prod_work.data_status = 1 and dispatch_method = 0 and prod_work.work_plan_total_qty-COALESCE(DISPATCH.dispatch_qty, 0) >0
            <if test="prodWorkIdList != null and prodWorkIdList.size > 0 ">
                AND prod_work.prod_work_id IN
                <foreach item="item" index="index" collection="prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="formStatusList != null and formStatusList.size > 0 ">
                AND prod_work.form_status IN
                <foreach item="item" index="index" collection="formStatusList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDispatchWorkCenterList" resultType="com.mongoso.mgs.module.produce.controller.admin.dispatchwork.vo.DispatchWorkRespVO">
        SELECT center.work_center_id,center.work_center_code,center.work_center_name,
               sum(COALESCE(dispatch.dispatch_qty,0)-COALESCE(dispatch.reported_qty, 0)) as workCenterPendingQty
        FROM u_work_center center
        left join u_dispatch_work dispatch on center.work_center_id = dispatch.work_center_id
        <where>
            center.data_status = 1 and dispatch_method = 0
            <if test="reqVO.workCenterCode != null and reqVO.workCenterCode != ''">
                AND center.work_center_code LIKE concat('%', #{reqVO.workCenterCode}, '%')
            </if>
            <if test="reqVO.workCenterName != null and reqVO.workCenterName != ''">
                AND center.work_center_name LIKE concat('%', #{reqVO.workCenterName}, '%')
            </if>
            <if test="reqVO.workCenterIdList != null and reqVO.workCenterIdList.size > 0 ">
                AND center.work_center_id IN
                <foreach item="item" index="index" collection="reqVO.workCenterIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by center.work_center_id,center.work_center_code,center.work_center_name,center.created_dt
        order by center.created_dt desc
    </select>

    <select id="selectWorkCenterDispatchQty" resultType="java.math.BigDecimal">
        SELECT SUM(material_dispatch_qty) as workCenterDispatchQty
        FROM u_dispatch_work
        WHERE material_id = #{reqVO.materialId} and work_center_id = #{reqVO.workCenterId} and process_id = #{reqVO.processId}
        <if test="reqVO.prodWorkId != null ">
            AND prod_work_id = #{reqVO.prodWorkId}
        </if>
        <if test="reqVO.dispatchMethod != null ">
            AND dispatch_method = #{reqVO.dispatchMethod}
        </if>

    </select>


    <select id="findAbleDispatchWorkList"
            resultType="com.mongoso.mgs.module.produce.dal.db.dispatchwork.DispatchWorkDO">
        select dispatch.dispatch_work_id,dispatch.work_plan_total_qty,dispatch.dispatch_qty+COALESCE(demand.out_demand_qty, 0) as dispatchQty
        from u_dispatch_work as dispatch
             left join u_process_out_demand as demand on dispatch.prod_work_id = demand.prod_work_id and dispatch.process_id = demand.process_id
        <where>
            dispatch.form_status in (0,1) and dispatch.work_plan_total_qty-dispatch.dispatch_qty-COALESCE(demand.out_demand_qty, 0) > 0
            <if test="reqVO.prodWorkIdList != null and reqVO.prodWorkIdList.size > 0 ">
                AND dispatch.prod_work_id IN
                <foreach item="item" index="index" collection="reqVO.prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.materialId != null ">
                AND dispatch.material_id = #{reqVO.materialId}
            </if>
            <if test="reqVO.dispatchMethod != null ">
                AND dispatch.dispatch_method = #{reqVO.dispatchMethod}
            </if>
            <if test="reqVO.processId != null ">
                AND dispatch.process_id = #{reqVO.processId}
            </if>
            <if test="reqVO.workCenterId != null ">
                AND dispatch.work_center_id = #{reqVO.workCenterId}
            </if>
            <if test="reqVO.pieceworkMethodDictId != null and reqVO.pieceworkMethodDictId !=''">
                AND dispatch.piecework_method_dict_id = #{reqVO.pieceworkMethodDictId}
            </if>
        </where>
    </select>

    <select id="findDispatchProcessList"
            resultType="com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessRespVO">
        SELECT
            p.process_id,
            p.process_code,
            p.process_name
        FROM u_prod_work prod
        INNER JOIN u_flow_process_detail p ON prod.prod_work_id = p.flow_process_id AND p.flow_process_type = 1
        LEFT JOIN (
            SELECT
            prod_work_id,
            process_id,
            SUM(dispatch_qty) AS dispatch_qty,
            SUM(pending_qty) AS pending_qty
            FROM u_dispatch_work
            GROUP BY
            prod_work_id, process_id
        ) d ON prod.prod_work_id = d.prod_work_id AND p.process_id = d.process_id
        LEFT JOIN (
            SELECT
            prod_work_id,
            process_id,
            SUM(out_demand_qty) AS out_demand_qty,
            SUM(purchaseable_qty) AS purchaseable_qty
            FROM u_process_out_demand
            GROUP BY
            prod_work_id, process_id
        ) od ON prod.prod_work_id = od.prod_work_id AND p.process_id = od.process_id
        <where>
            prod.data_status = 1 and prod.form_status IN (0, 1)
            <if test="reqVO.dispatchMethod != null ">
                AND prod.process_config = #{reqVO.dispatchMethod}
            </if>
            <if test="reqVO.prodWorkIdList != null and reqVO.prodWorkIdList.size > 0 ">
                AND prod.prod_work_id IN
                <foreach item="item" index="index" collection="reqVO.prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.isFullDispatch != null and reqVO.isFullDispatch == 0">
                and prod.work_plan_total_qty - COALESCE(d.dispatch_qty, 0) - COALESCE(od.out_demand_qty, 0) > 0
            </if>
            <if test="reqVO.isFullDispatch != null and reqVO.isFullDispatch == 1">
                and COALESCE(d.pending_qty, 0) + COALESCE(od.purchaseable_qty, 0) > 0
            </if>
            <if test="reqVO.processCode != null and reqVO.processCode != ''">
                AND p.process_code LIKE concat('%', #{reqVO.processCode}, '%')
            </if>
            <if test="reqVO.processName != null and reqVO.processName != ''">
                AND p.process_name LIKE concat('%', #{reqVO.processName}, '%')
            </if>
            <if test="reqVO.processId != null ">
                AND p.process_id = #{reqVO.processId}
            </if>
        </where>
        GROUP BY p.process_id, p.process_code, p.process_name
        ORDER BY p.process_id DESC;
    </select>

    <select id="selectDispatchQty" resultType="java.math.BigDecimal">
        select COALESCE(SUM(dispatch_qty), 0)
        from u_dispatch_work
        where prod_work_id = #{prodWorkId} and dispatch_strategy_config = #{dispatchStrategyConfig} and process_id = #{processId}
    </select>

    <select id="selectListByReporte"
            resultType="com.mongoso.mgs.module.produce.dal.db.dispatchwork.DispatchWorkDO">
        select t1.*
        from u_dispatch_work t1
             inner join u_reported_work t2 on t1.dispatch_work_id = t2.dispatch_work_id and t2.data_status = 1
        where t1.prod_work_id = #{prodWorkId}
    </select>

    <select id="sumDispatchQty"
            resultType="com.mongoso.mgs.module.produce.service.dispatchwork.bo.DispatchQtyRespBO">
        select prod_work_id,work_plan_total_qty,work_plan_total_qty-COALESCE(sum(dispatch_qty),0) as dispatchAbleQty
        from u_dispatch_work
        <where>
            <if test="reqVO.prodWorkIdList != null and reqVO.prodWorkIdList.size > 0 ">
                AND prod_work_id IN
                <foreach item="item" index="index" collection="reqVO.prodWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.prodWorkId != null ">
                and prod_work_id = #{reqVO.prodWorkId}
            </if>
            <if test="reqVO.processId != null ">
                AND process_id = #{reqVO.processId}
            </if>
            <if test="reqVO.dispatchMethod != null ">
                AND dispatch_method = #{reqVO.dispatchMethod}
            </if>
            <if test="reqVO.pieceworkMethodDictId != null and reqVO.pieceworkMethodDictId !='' ">
                AND piecework_method_dict_id = #{reqVO.pieceworkMethodDictId}
            </if>
            <if test="reqVO.materialId != null ">
                AND material_id = #{reqVO.materialId}
            </if>
        </where>
        group by prod_work_id,work_plan_total_qty
    </select>

    <update id="updateDispatchWorkStatus">
        UPDATE u_dispatch_work SET form_status = 3,updated_dt = CURRENT_TIMESTAMP
        <where>
            <if test="dispatchWorkIdList != null and dispatchWorkIdList.size > 0 ">
                AND dispatch_work_id IN
                <foreach item="item" index="index" collection="dispatchWorkIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

</mapper>
