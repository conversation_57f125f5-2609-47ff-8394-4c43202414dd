<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.workbench.ProdWorkbenchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="reportWorkMap" type="com.mongoso.mgs.module.produce.controller.admin.workbench.bo.ReportNgRankBO">
        <result property="ngDetailList" column="ng_detail_list" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>
    </resultMap>

    <!-- 在制订单数 -->
    <select id="getInProdOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_erp_prod_order a
        WHERE a.data_status = 1
        AND a.form_status IN (0, 1)
    </select>

    <!-- 在制工单数 -->
    <select id="getInProdWorkCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_prod_work a
        WHERE a.data_status = 1
        AND a.form_status IN (0, 1)
    </select>

    <select id="getProdOrderAbleCount" resultType="java.lang.Integer">
        SELECT SUM(prod_plan_total_qty)
        FROM u_erp_prod_order a
        WHERE a.data_status = 1 AND a.form_status IN (0, 1)
    </select>

    <select id="getProdWorkAbleCount" resultType="java.lang.Integer">
        SELECT SUM(work_plan_total_qty)
        FROM u_prod_work a
        WHERE a.data_status = 1 AND a.form_status IN (0, 1)
    </select>

    <!-- 实际生产工单数 -->
    <select id="getProdWorkActCount" resultType="java.math.BigDecimal">
        SELECT SUM(ok_qty+ng_qty) as workActTotalQty
        FROM u_reported_work a
        WHERE DATE(reported_dt) = CURRENT_DATE and prod_work_id is not null
    </select>

    <!-- 延期订单数 -->
    <select id="getDelayProdOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_erp_prod_order a
        WHERE a.data_status = 1
        AND a.form_status IN (0, 1)
        AND a.plan_end_date &lt; CURRENT_DATE
    </select>

    <!-- 延期工单数 -->
    <select id="getDelayProdWorkCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_prod_work a
        WHERE a.data_status = 1
        AND a.form_status IN (0, 1)
        AND a.plan_end_date &lt; CURRENT_DATE
    </select>

    <!-- 本月完成订单数量 -->
    <select id="getCurMonthCompleteProdOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_erp_prod_order a
        WHERE a.data_status = 1
        AND a.form_status IN (2, 3)
        AND DATE_TRUNC('month', act_end_date) = DATE_TRUNC('month', CURRENT_DATE)
    </select>

    <!-- 本月完成工单数 -->
    <select id="getCurMonthCompleteProdWorkCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_prod_work a
        WHERE a.data_status = 1
        AND a.form_status IN (2, 3)
        AND DATE_TRUNC('month', act_end_date) = DATE_TRUNC('month', CURRENT_DATE)
    </select>

    <!-- 当天报工良总数 -->
    <select id="getTodayReportedOkQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(ok_qty), 0)
        FROM u_reported_work
        WHERE DATE(reported_dt) = CURRENT_DATE
        <if test="reportedWorkTypeList != null and reportedWorkTypeList.size > 0">
            AND reported_work_type IN
            <foreach item="item" index="index" collection="reportedWorkTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 当天报工不良总数 -->
    <select id="getTodayReportedNgQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(ng_qty), 0)
        FROM u_reported_work
        WHERE DATE(reported_dt) = CURRENT_DATE
        <if test="reportedWorkTypeList != null and reportedWorkTypeList.size > 0">
            AND reported_work_type IN
            <foreach item="item" index="index" collection="reportedWorkTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryreportedWorkLineList"
            resultType="com.mongoso.mgs.module.produce.controller.admin.workbench.bo.ReportWorkLineBO">
        SELECT TO_CHAR(reported_dt, 'YYYY-MM-DD') as reportedDt,SUM(ok_qty) AS okQty,SUM(ng_qty) AS ngQty
        FROM u_reported_work
        WHERE reported_work_type in (0,1,2) and reported_dt > CURRENT_DATE - INTERVAL '1 months'
        GROUP BY TO_CHAR(reported_dt, 'YYYY-MM-DD')
    </select>

    <select id="queryNgQtyRank" resultMap="reportWorkMap">
        SELECT ng_detail_list
        FROM u_reported_work
        WHERE reported_work_type in (0,1,2) and jsonb_array_length("ng_detail_list") > 0
              and reported_dt > CURRENT_DATE - INTERVAL '1 months'
    </select>


    <select id="getInProdOrderCountByDate" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_erp_prod_order a
        WHERE a.data_status = 1 AND a.form_status IN (0, 1) and form_dt > CURRENT_DATE - INTERVAL '1 year'
    </select>

    <select id="getInProdWorkCountByDate" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_prod_work a
        WHERE a.data_status = 1 AND a.form_status IN (0, 1) and form_dt > CURRENT_DATE - INTERVAL '1 year'
    </select>

    <select id="getProdOrderAbleCountByDate" resultType="java.lang.Integer">
        SELECT SUM(prod_plan_total_qty)
        FROM u_erp_prod_order a
        WHERE a.data_status = 1 AND a.form_status IN (0, 1) and form_dt > CURRENT_DATE - INTERVAL '1 year'
    </select>

    <select id="getProdWorkAbleCountByDate" resultType="java.lang.Integer">
        SELECT SUM(work_plan_total_qty)
        FROM u_prod_work a
        WHERE a.data_status = 1 AND a.form_status IN (0, 1) and form_dt > CURRENT_DATE - INTERVAL '1 year'
    </select>


</mapper>
