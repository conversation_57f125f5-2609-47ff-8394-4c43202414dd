<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.materialsupplier.MaterialSupplierMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListBySupplierIdAndDataStatus"
            resultType="com.mongoso.mgs.module.produce.service.materialbom.bo.MaterialBO">
        SELECT
            t2.material_id,
            t2.material_code,
            t2.material_name,
            t2.material_category_dict_id,
            t2.material_source_dict_id,
            t2.main_unit_dict_id,
            t2.spec_model,
            t2.spec_attribute_str
        FROM erp.u_material_supplier t1
        LEFT JOIN u_material t2 ON t2.material_id = t1.material_id
        WHERE t2.data_status = 1 AND t1.supplier_id = #{supplierId}

    </select>



</mapper>
