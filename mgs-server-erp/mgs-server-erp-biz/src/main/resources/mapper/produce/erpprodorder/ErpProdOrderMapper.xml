<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="findErpProdOrderQty"
            resultType="com.mongoso.mgs.module.produce.controller.admin.erpprodorder.bo.ErpProdOrderRespBO">
        SELECT prod_order.source_form_id as sourceFormId,prod_order.prod_order_id,prod_order.form_status,
               sum(prod_order.prod_plan_total_qty) as prodPlanTotalQty,sum(prod_order.prod_act_total_qty) as prodActTotalQty
        from u_erp_prod_order as prod_order
        <where>
            prod_order.data_status = 1
            and prod_order.prod_order_change_id is null
            <if test="saleOrderIds != null and saleOrderIds.size > 0 ">
                and prod_order.related_order_id IN
                <foreach item="item" index="index" collection="saleOrderIds"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by prod_order.source_form_id,prod_order.prod_order_id,prod_order.form_status

    </select>


    <select id="selectListByRelatedOrderIdAndMaterialIdList"
            resultType="com.mongoso.mgs.module.sale.service.erpsaleorderdetail.bo.ErpProdOrderDetailBO">
        SELECT
            t2.material_id AS materialId,
            sum(t2.prod_plan_qty) AS prodPlanQty
        FROM erp.u_erp_prod_order t1
        LEFT JOIN erp.u_erp_prod_order_detail t2 ON t1.prod_order_id=t2.prod_order_id
        WHERE t1.data_status=1 AND t1.related_order_id=#{relatedOrderId}
        AND t2.material_id IN
        <foreach item="item" index="index" collection="materialIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY t2.material_id
    </select>

    <select id="queryProdDetailPage"
            resultType="com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo.ErpProdOrderResp">
        SELECT prod.*, detail.prod_order_detail_id, detail.material_id,
               detail.material_code,
               detail.main_unit_dict_id,
               detail.material_source_dict_id,
               detail.row_no,
               detail.priority,
               detail.delivery_date   AS detail_delivery_date,
               detail.plan_start_date AS detail_plan_start_date,
               detail.plan_end_date   AS detail_plan_end_date,
               detail.prod_plan_qty,
               detail.work_plan_qty,
               detail.outsource_qty,
               detail.produced_qty,
               detail.ok_qty,
               detail.ng_qty,
               detail.outsource_inbound_qty,
               detail.purchased_qty,
               detail.remark          AS detail_remark
        FROM erp.u_erp_prod_order prod
        INNER JOIN erp.u_erp_prod_order_detail detail ON prod.prod_order_id = detail.prod_order_id
        <where>
            prod.data_status = 1
            <if test="reqVO.prodOrderIdList != null and reqVO.prodOrderIdList.size > 0 ">
                AND prod.prod_order_id IN
                <foreach item="item" index="index" collection="reqVO.prodOrderIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.relatedOrderId != null">
                AND prod.related_order_id = #{reqVO.relatedOrderId}
            </if>
            <if test="reqVO.formStatus != null">
                AND prod.form_status = #{reqVO.formStatus}
            </if>
            <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
                AND detail.material_id IN
                <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by prod.created_dt desc, detail.row_no asc
    </select>
</mapper>
