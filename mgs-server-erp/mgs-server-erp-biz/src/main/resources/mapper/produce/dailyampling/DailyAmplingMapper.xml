<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.produce.dal.mysql.dailyampling.DailyAmplingMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage2"
            resultType="com.mongoso.mgs.module.produce.controller.admin.dailyampling.vo.DailyAmplingRespVO">

        SELECT
        t1.daily_ampling_id,
        t1.daily_ampling_code,
        t1.supplier_id,
        t1.material_id,
        t1.material_code,
        t1.check_user_ids,
        t1.check_qty,
        t1.ok_qty,
        t1.remark,
        t1.check_dt,
        t1.created_by,
        t1.created_dt,
        t1.updated_by,
        t1.updated_dt,
        t2.material_name,
        t3.supplier_name
        FROM erp.u_daily_ampling t1
        LEFT JOIN erp.u_material t2 ON t1.material_id = t2.material_id
        LEFT JOIN erp.u_supplier t3 ON t1.supplier_id = t3.supplier_id
        WHERE 1=1
        <if test="reqVO.dailyAmplingCode != null and reqVO.dailyAmplingCode != ''">
            AND t1.daily_ampling_code LIKE concat('%', #{reqVO.dailyAmplingCode}, '%')
        </if>
        <if test="reqVO.checkUserId != null and reqVO.checkUserId != ''">
            AND t1.check_user_ids LIKE concat('%', #{reqVO.checkUserId}, '%')
        </if>
        <if test="reqVO.startCheckDt != null and reqVO.endCheckDt != null ">
            AND t1.check_dt >= #{reqVO.startCheckDt}
            AND t1.check_dt &lt;= #{reqVO.endCheckDt}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t2.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t2.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.supplierId != null and reqVO.supplierId != ''">
            AND t3.supplier_id = #{reqVO.supplierId}
        </if>
        ORDER BY t1.daily_ampling_id DESC

    </select>


    <select id="selectPage3"
            resultType="com.mongoso.mgs.module.produce.controller.admin.dailyampling.vo.DailyAmplingRespVO">

        SELECT
        t1.daily_ampling_id,
        t1.daily_ampling_code,
        t1.supplier_id,
        t1.material_id,
        t1.material_code,
        t1.check_user_ids,
        t1.check_qty,
        t1.ok_qty,
        t1.remark,
        t1.check_dt,
        t1.created_by,
        t1.created_dt,
        t1.updated_by,
        t1.updated_dt,
        t2.material_name,
        t3.supplier_name
        FROM erp.u_daily_ampling t1
        LEFT JOIN erp.u_material t2 ON t1.material_id = t2.material_id
        LEFT JOIN erp.u_supplier t3 ON t1.supplier_id = t3.supplier_id
        WHERE t1.created_id =  #{userId}
          AND t1.created_dt >= #{weekStart} AND t1.created_dt &lt;= #{weekEnd}
        ORDER BY t1.daily_ampling_id DESC
    </select>


</mapper>
