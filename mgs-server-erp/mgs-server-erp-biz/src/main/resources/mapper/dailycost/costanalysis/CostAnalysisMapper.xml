<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costanalysis.CostAnalysisMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectCompanyCostProfitReport" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costanalysis.vo.CompanyCostProfitRespVO">
        SELECT
            undertakeOrgId,
            <if test="reqVO.reportType == 0">
                TO_CHAR(form_dt, 'YYYY-MM-DD') formDt,
                '' formDtRange,
            </if>
            <if test="reqVO.reportType == 1">
                TO_CHAR(form_dt, 'YYYY-WW') formDt,
                CONCAT((DATE(form_dt) - EXTRACT(DOW FROM DATE(form_dt))::int + 1), '~',-- 周的第一天（星期一）
                (DATE(form_dt) + (7 - EXTRACT(DOW FROM DATE(form_dt))::int) )) formDtRange, -- 周的最后一天（星期日）
            </if>
            <if test="reqVO.reportType == 2">
                TO_CHAR(form_dt, 'YYYY-MM') formDt,
                '' formDtRange,
            </if>
            SUM(income) AS income,
            SUM(shipmentQty) AS shipmentQty,
            SUM(fixedInvestCost) AS fixedInvestCost,
            SUM(fixedExpendCost) AS fixedExpendCost,
            SUM(deferFeeCost) AS deferFeeCost,
            SUM(dailyExpendCost) AS dailyExpendCost,
            SUM(prodMaterialCost) AS prodMaterialCost,
            SUM(prodPurchaseCost) AS prodPurchaseCost,
            SUM(prodLaborCost) AS prodLaborCost,
            SUM(fixedInvestCost + fixedExpendCost + deferFeeCost + dailyExpendCost + prodMaterialCost + prodPurchaseCost + prodLaborCost) AS commission,
            SUM(income) -
            SUM(fixedInvestCost + fixedExpendCost + deferFeeCost + dailyExpendCost + prodMaterialCost + prodPurchaseCost + prodLaborCost) AS profit
        FROM (
            SELECT
                b.form_dt,
                b.undertake_org_id AS undertakeOrgId,
                COALESCE((CASE WHEN order_type = 0 THEN total_amt ELSE 0 END) -
                (CASE WHEN order_type IN (1, 2) THEN total_amt ELSE 0 END), 0) AS income,
                COALESCE((CASE WHEN order_type = 0 THEN qty ELSE 0 END) -
                (CASE WHEN order_type = 1 THEN qty ELSE 0 END),0) AS shipmentQty,
                0 AS fixedInvestCost,
                0 AS fixedExpendCost,
                0 AS deferFeeCost,
                0 AS dailyExpendCost,
                0 AS prodMaterialCost,
                0 AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_sale_income_detail b
            WHERE b.form_dt >= #{reqVO.startDate}
            AND b.form_dt &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and b.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                a.provision_date,
                a.undertake_org_id AS undertakeOrgId,
                0 AS income,
                0 AS shipmentQty,
                (CASE WHEN a.aggre_type = 0 THEN a.undertake_amt ELSE 0 END) AS fixedInvestCost,
                0 AS fixedExpendCost,
                0 AS deferFeeCost,
                0 AS dailyExpendCost,
                0 AS prodMaterialCost,
                0 AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_aggre_detail a
            WHERE a.provision_date >= #{reqVO.startDate}
            AND a.provision_date &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and a.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                a.provision_date,
                a.undertake_org_id AS undertakeOrgId,
                0 AS income,
                0 AS shipmentQty,
                0 AS fixedInvestCost,
                (CASE WHEN a.aggre_type = 2 THEN a.undertake_amt ELSE 0 END) AS fixedExpendCost,
                0 AS deferFeeCost,
                0 AS dailyExpendCost,
                0 AS prodMaterialCost,
                0 AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_aggre_detail a
            WHERE a.provision_date >= #{reqVO.startDate}
            AND a.provision_date &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and a.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                a.provision_date,
                a.undertake_org_id AS undertakeOrgId,
                0 AS income,
                0 AS shipmentQty,
                0 AS fixedInvestCost,
                0 AS fixedExpendCost,
                (CASE WHEN a.aggre_type = 3 THEN a.undertake_amt ELSE 0 END) AS deferFeeCost,
                0 AS dailyExpendCost,
                0 AS prodMaterialCost,
                0 AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_aggre_detail a
            WHERE a.provision_date >= #{reqVO.startDate}
            AND a.provision_date &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and a.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                a.provision_date,
                a.undertake_org_id AS undertakeOrgId,
                0 AS income,
                0 AS shipmentQty,
                0 AS fixedInvestCost,
                0 AS fixedExpendCost,
                0 AS deferFeeCost,
                (CASE WHEN a.aggre_type = 4 THEN a.undertake_amt ELSE 0 END) AS dailyExpendCost,
                0 AS prodMaterialCost,
                0 AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_aggre_detail a
            WHERE a.provision_date >= #{reqVO.startDate}
            AND a.provision_date &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and a.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                b.form_dt,
                b.undertake_org_id,
                0 AS income,
                0 AS shipmentQty,
                0 AS fixedInvestCost,
                0 AS fixedExpendCost,
                0 AS deferFeeCost,
                0 AS dailyExpendCost,
                a.total_amt AS prodMaterialCost,
                0 AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_prod_material a
            LEFT JOIN u_cost_prod_material_detail b ON b.cost_prod_material_id = a.cost_prod_material_id
            WHERE b.form_dt >= #{reqVO.startDate}
            AND b.form_dt &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and b.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                a.form_dt,
                a.undertake_org_id,
                0 AS income,
                0 AS shipmentQty,
                0 AS fixedInvestCost,
                0 AS fixedExpendCost,
                0 AS deferFeeCost,
                0 AS dailyExpendCost,
                0 AS prodMaterialCost,
                (CASE WHEN b.order_type IN (0, 1, 2) THEN b.excl_tax_amt ELSE 0 END) -
                (CASE WHEN b.order_type IN (3, 4) THEN b.excl_tax_amt ELSE 0 END) AS prodPurchaseCost,
                0 AS prodLaborCost
            FROM u_cost_prod_purchase_detail a
            LEFT JOIN u_cost_prod_purchase b ON b.cost_prod_purchase_id = a.cost_prod_purchase_id
            WHERE a.form_dt >= #{reqVO.startDate}
            AND a.form_dt &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and a.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT
                b.form_dt,
                b.undertake_org_id,
                0 AS income,
                0 AS shipmentQty,
                0 AS fixedInvestCost,
                0 AS fixedExpendCost,
                0 AS deferFeeCost,
                0 AS dailyExpendCost,
                0 AS prodMaterialCost,
                0 AS prodPurchaseCost,
                a.total_amt AS prodLaborCost
            FROM u_cost_prod_labor a
            LEFT JOIN u_cost_prod_labor_detail b ON b.cost_prod_labor_id = a.cost_prod_labor_id
            WHERE b.form_dt >= #{reqVO.startDate}
            AND b.form_dt &lt;= #{reqVO.endDate}
            <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
                and b.undertake_org_id IN
                <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ) t
        GROUP BY formDt, formDtRange, undertakeOrgId
        ORDER BY formDt desc, undertakeOrgId
    </select>


    <select id="selectSelfMadeProductReport" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costanalysis.vo.CompanyCostProfitRespVO">
        SELECT
        undertakeOrgId,
        <if test="reqVO.reportType == 0">
            TO_CHAR(form_dt, 'YYYY-MM-DD') formDt,
            '' formDtRange,
        </if>
        <if test="reqVO.reportType == 1">
            TO_CHAR(form_dt, 'YYYY-WW') formDt,
            CONCAT((DATE(form_dt) - EXTRACT(DOW FROM DATE(form_dt))::int + 1), '~',-- 周的第一天（星期一）
            (DATE(form_dt) + (7 - EXTRACT(DOW FROM DATE(form_dt))::int) )) formDtRange, -- 周的最后一天（星期日）
        </if>
        <if test="reqVO.reportType == 2">
            TO_CHAR(form_dt, 'YYYY-MM') formDt,
            '' formDtRange,
        </if>
        undertakeMaterialCode,
        undertakeMaterialId,
        SUM(income) AS income,
        SUM(shipmentQty) AS shipmentQty,
        SUM(prodPurchaseCost) AS prodPurchaseCost,
        SUM(prodMaterialCost) AS prodMaterialCost,
        SUM(prodDailyIndirectCost) AS prodDailyIndirectCost,
        SUM(prodLaborCost) AS prodLaborCost,
        SUM(prodDailyIndirectCost + prodMaterialCost + prodPurchaseCost + prodLaborCost) AS commission,
        SUM(income) - SUM(prodDailyIndirectCost + prodMaterialCost + prodPurchaseCost + prodLaborCost) AS profit
        FROM (
        SELECT
        b.undertake_org_id AS undertakeOrgId,
        b.form_dt,
        a.undertake_material_code AS undertakeMaterialCode,
        a.undertake_material_id AS undertakeMaterialId,
        COALESCE(SUM(CASE WHEN b.order_type IN (0) THEN b.total_amt ELSE 0 END) -
        SUM(CASE WHEN b.order_type IN (1, 2) THEN b.total_amt ELSE 0 END), 0) AS income,
        COALESCE(SUM(CASE WHEN b.order_type = 0 THEN b.qty ELSE 0 END) -
        SUM(CASE WHEN b.order_type = 1 THEN b.qty ELSE 0 END), 0) AS shipmentQty,
        0 AS prodDailyIndirectCost,
        0 AS prodLaborCost,
        0 AS prodMaterialCost,
        0 AS prodPurchaseCost
        FROM u_cost_sale_income a
        LEFT JOIN u_cost_sale_income_detail b ON b.sale_income_id = a.sale_income_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.data_status = 1
        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)
        AND b.detail_type = 0
        AND b.form_dt >= #{reqVO.startDate}
        AND b.form_dt &lt;= #{reqVO.endDate}
        GROUP BY b.form_dt, b.undertake_org_id, a.undertake_material_code, a.undertake_material_id  -- 添加 GROUP BY
        UNION ALL
        SELECT
        a.undertake_org_id AS undertakeOrgId,
        a.form_dt,
        a.undertake_material_code AS undertakeMaterialCode,
        a.undertake_material_id AS undertakeMaterialId,
        0 AS income,
        0 AS shipmentQty,
        CASE WHEN b.prod_labor_amt_all IS NULL OR b.prod_labor_amt_all = 0 THEN 0
        ELSE ROUND((a.prod_labor_amt / b.prod_labor_amt_all) * COALESCE(c.undertake_amt, 0),2)
        END AS prodDailyIndirectCost,
        0 AS prodLaborCost,
        0 AS prodMaterialCost,
        0 AS prodPurchaseCost
        FROM (
        SELECT
        DATE(a.reported_dt) AS form_dt,
        a.undertake_org_id,
        a.undertake_material_code,
        a.undertake_material_id,
        SUM(a.total_amt) AS prod_labor_amt
        FROM
        u_cost_prod_labor a
        LEFT JOIN
        u_material c ON a.material_id = c.material_id AND c.data_status = 1
        WHERE
        c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)
        GROUP BY
        DATE(a.reported_dt),
        a.undertake_org_id,
        a.undertake_material_code,
        a.undertake_material_id
        ) AS a
        JOIN (
        SELECT
        DATE(a.reported_dt) AS form_dt,
        a.undertake_org_id,
        SUM(a.total_amt) AS prod_labor_amt_all
        FROM
        u_cost_prod_labor a
        LEFT JOIN
        u_cost_process_config d ON d.process_id = a.process_id
        WHERE d.process_id IS NOT NULL
        GROUP BY
        DATE(a.reported_dt),
        a.undertake_org_id
        ) AS b ON a.form_dt = b.form_dt AND a.undertake_org_id = b.undertake_org_id
        LEFT JOIN (
        SELECT
        DATE(a.provision_date) AS form_dt,
        a.undertake_org_id,
        SUM(a.undertake_amt) AS undertake_amt
        FROM
        u_cost_aggre_detail a
        WHERE
        a.undertake_amt IS NOT NULL
        GROUP BY
        DATE(a.provision_date),
        a.undertake_org_id
        ) AS c ON a.form_dt = c.form_dt AND a.undertake_org_id = c.undertake_org_id
        WHERE a.form_dt >= #{reqVO.startDate}
        AND a.form_dt &lt;= #{reqVO.endDate}
        UNION ALL
        SELECT
        b.undertake_org_id AS undertakeOrgId,
        b.form_dt,
        a.undertake_material_code AS undertakeMaterialCode,
        a.undertake_material_id AS undertakeMaterialId,
        0 AS income,
        0 AS shipmentQty,
        0 AS prodDailyIndirectCost,
        0 AS prodLaborCost,
        COALESCE(SUM(a.total_amt), 0) AS prodMaterialCost,
        0 AS prodPurchaseCost
        FROM u_cost_prod_material a
        RIGHT JOIN u_cost_prod_material_detail b ON a.cost_prod_material_id = b.cost_prod_material_id
        LEFT JOIN u_material c ON a.material_id = c.material_id AND c.data_status = 1
        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)
        AND b.form_dt >= #{reqVO.startDate}
        AND b.form_dt &lt;= #{reqVO.endDate}
        GROUP BY b.form_dt, b.undertake_org_id, a.undertake_material_code, a.undertake_material_id  -- 添加 GROUP BY
        UNION ALL
        SELECT
        b.undertake_org_id AS undertakeOrgId,
        b.form_dt,
        a.undertake_material_code AS undertakeMaterialCode,
        a.undertake_material_id AS undertakeMaterialId,
        0 AS income,
        0 AS shipmentQty,
        0 AS prodDailyIndirectCost,
        0 AS prodLaborCost,
        0 AS prodMaterialCost,
        COALESCE(SUM(CASE WHEN a.order_type IN (0, 1, 2) THEN a.excl_tax_amt ELSE 0 END), 0) -
        COALESCE(SUM(CASE WHEN a.order_type IN (3, 4) THEN a.excl_tax_amt ELSE 0 END), 0) AS prodPurchaseCost
        FROM u_cost_prod_purchase a
        LEFT JOIN u_cost_prod_purchase_detail b ON a.cost_prod_purchase_id = b.cost_prod_purchase_id
        LEFT JOIN u_material c ON a.material_id = c.material_id AND c.data_status = 1
        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)
        AND b.detail_type = 0
        AND b.form_dt >= #{reqVO.startDate}
        AND b.form_dt &lt;= #{reqVO.endDate}
        GROUP BY b.form_dt, b.undertake_org_id, a.undertake_material_code, a.undertake_material_id  -- 添加 GROUP BY
        UNION ALL
        SELECT
        b.undertake_org_id AS undertakeOrgId,
        b.form_dt,
        a.undertake_material_code AS undertakeMaterialCode,
        a.undertake_material_id AS undertakeMaterialId,
        0 AS income,
        0 AS shipmentQty,
        0 AS prodDailyIndirectCost,
        SUM(a.total_amt) AS prodLaborCost,
        0 AS prodMaterialCost,
        0 AS prodPurchaseCost
        FROM u_cost_prod_labor a
        RIGHT JOIN u_cost_prod_labor_detail b ON a.cost_prod_labor_id = b.cost_prod_labor_id
        LEFT JOIN u_material c ON a.material_id = c.material_id AND c.data_status = 1
        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)
        AND b.form_dt >= #{reqVO.startDate}
        AND b.form_dt &lt;= #{reqVO.endDate}
        GROUP BY b.form_dt, b.undertake_org_id, a.undertake_material_code, a.undertake_material_id  -- 添加 GROUP BY
        ) t
        GROUP BY undertakeOrgId, undertakeMaterialCode, undertakeMaterialId, formDt,formDtRange  -- 外层 GROUP BY
        ORDER BY formDt DESC, undertakeOrgId;
    </select>

<!--    <select id="selectSelfMadeProductReport" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costanalysis.vo.CompanyCostProfitRespVO">-->
<!--        SELECT-->
<!--        undertakeOrgId,-->
<!--        <if test="reqVO.reportType == 0">-->
<!--            TO_CHAR(form_dt, 'YYYY-MM-DD') formDt,-->
<!--            '' formDtRange,-->
<!--        </if>-->
<!--        <if test="reqVO.reportType == 1">-->
<!--            TO_CHAR(form_dt, 'YYYY-WW') formDt,-->
<!--            CONCAT((DATE(form_dt) - EXTRACT(DOW FROM DATE(form_dt))::int + 1), '~',&#45;&#45; 周的第一天（星期一）-->
<!--            (DATE(form_dt) + (7 - EXTRACT(DOW FROM DATE(form_dt))::int) )) formDtRange, &#45;&#45; 周的最后一天（星期日）-->
<!--        </if>-->
<!--        <if test="reqVO.reportType == 2">-->
<!--            TO_CHAR(form_dt, 'YYYY-MM') formDt,-->
<!--            '' formDtRange,-->
<!--        </if>-->
<!--        spuCode,-->
<!--        spuId,-->
<!--        SUM(income) AS income,-->
<!--        SUM(shipmentQty) AS shipmentQty,-->
<!--        SUM(prodPurchaseCost) AS prodPurchaseCost,-->
<!--        SUM(prodMaterialCost) AS prodMaterialCost,-->
<!--        SUM(prodDailyIndirectCost) AS prodDailyIndirectCost,-->
<!--        SUM(prodLaborCost) AS prodLaborCost,-->
<!--        SUM(prodDailyIndirectCost + prodMaterialCost + prodPurchaseCost + prodLaborCost) AS commission,-->
<!--        SUM(income) - SUM(prodDailyIndirectCost + prodMaterialCost + prodPurchaseCost + prodLaborCost) AS profit-->
<!--        FROM (-->
<!--        SELECT-->
<!--        b.undertake_org_id AS undertakeOrgId,-->
<!--        b.form_dt,-->
<!--        c.spu_code AS spuCode,-->
<!--        c.spu_id AS spuId,-->
<!--        COALESCE(SUM(CASE WHEN b.order_type IN (0) THEN b.total_amt ELSE 0 END) - -->
<!--        SUM(CASE WHEN b.order_type IN (1, 2) THEN b.total_amt ELSE 0 END), 0) AS income,-->
<!--        COALESCE(SUM(CASE WHEN b.order_type = 0 THEN b.qty ELSE 0 END) - -->
<!--        SUM(CASE WHEN b.order_type = 1 THEN b.qty ELSE 0 END), 0) AS shipmentQty,-->
<!--        0 AS prodDailyIndirectCost,-->
<!--        0 AS prodLaborCost,-->
<!--        0 AS prodMaterialCost,-->
<!--        0 AS prodPurchaseCost-->
<!--        FROM u_cost_sale_income a-->
<!--        LEFT JOIN u_cost_sale_income_detail b ON b.sale_income_id = a.sale_income_id-->
<!--        LEFT JOIN u_cost_spu_config c ON a.related_up_form_id = c.related_up_order_id AND b.material_id = c.material_id AND c.data_status = 1-->
<!--        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)-->
<!--        AND b.detail_type = 0-->
<!--        AND b.form_dt >= #{reqVO.startDate}-->
<!--        AND b.form_dt &lt;= #{reqVO.endDate}-->
<!--        GROUP BY b.form_dt, b.undertake_org_id, c.spu_code, c.spu_id  &#45;&#45; 添加 GROUP BY-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--        a.undertake_org_id AS undertakeOrgId,-->
<!--        a.form_dt,-->
<!--        a.spu_code AS spuCode,-->
<!--        a.spu_id AS spuId,-->
<!--        0 AS income,-->
<!--        0 AS shipmentQty,-->
<!--        CASE WHEN b.prod_labor_amt_all IS NULL OR b.prod_labor_amt_all = 0 THEN 0-->
<!--        ELSE ROUND((a.prod_labor_amt / b.prod_labor_amt_all) * COALESCE(c.undertake_amt, 0),2)-->
<!--        END AS prodDailyIndirectCost,-->
<!--        0 AS prodLaborCost,-->
<!--        0 AS prodMaterialCost,-->
<!--        0 AS prodPurchaseCost-->
<!--        FROM (-->
<!--            SELECT-->
<!--            DATE(a.reported_dt) AS form_dt,-->
<!--            a.undertake_org_id,-->
<!--            c.spu_code,-->
<!--            c.spu_id,-->
<!--            SUM(a.total_amt) AS prod_labor_amt-->
<!--            FROM-->
<!--            u_cost_prod_labor a-->
<!--            LEFT JOIN-->
<!--            u_cost_spu_config c ON a.reported_work_id = c.related_up_order_id AND a.material_id = c.material_id AND c.data_status = 1-->
<!--            WHERE-->
<!--            c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)-->
<!--            GROUP BY-->
<!--            DATE(a.reported_dt),-->
<!--            a.undertake_org_id,-->
<!--            c.spu_code,-->
<!--            c.spu_id-->
<!--        ) AS a-->
<!--        JOIN (-->
<!--            SELECT-->
<!--            DATE(a.reported_dt) AS form_dt,-->
<!--            a.undertake_org_id,-->
<!--            SUM(a.total_amt) AS prod_labor_amt_all-->
<!--            FROM-->
<!--            u_cost_prod_labor a-->
<!--            LEFT JOIN-->
<!--            u_cost_process_config d ON d.process_id = a.process_id-->
<!--            WHERE d.process_id IS NOT NULL-->
<!--            GROUP BY-->
<!--            DATE(a.reported_dt),-->
<!--            a.undertake_org_id-->
<!--        ) AS b ON a.form_dt = b.form_dt AND a.undertake_org_id = b.undertake_org_id-->
<!--        LEFT JOIN (-->
<!--            SELECT-->
<!--            DATE(a.provision_date) AS form_dt,-->
<!--            a.undertake_org_id,-->
<!--            SUM(a.undertake_amt) AS undertake_amt-->
<!--            FROM-->
<!--            u_cost_aggre_detail a-->
<!--            WHERE-->
<!--            a.undertake_amt IS NOT NULL-->
<!--            GROUP BY-->
<!--            DATE(a.provision_date),-->
<!--            a.undertake_org_id-->
<!--        ) AS c ON a.form_dt = c.form_dt AND a.undertake_org_id = c.undertake_org_id-->
<!--        WHERE a.form_dt >= #{reqVO.startDate}-->
<!--        AND a.form_dt &lt;= #{reqVO.endDate}-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--        b.undertake_org_id AS undertakeOrgId,-->
<!--        b.form_dt,-->
<!--        c.spu_code AS spuCode,-->
<!--        c.spu_id AS spuId,-->
<!--        0 AS income,-->
<!--        0 AS shipmentQty,-->
<!--        0 AS prodDailyIndirectCost,-->
<!--        0 AS prodLaborCost,-->
<!--        COALESCE(SUM(a.total_amt), 0) AS prodMaterialCost,-->
<!--        0 AS prodPurchaseCost-->
<!--        FROM u_cost_prod_material a-->
<!--        RIGHT JOIN u_cost_prod_material_detail b ON a.cost_prod_material_id = b.cost_prod_material_id-->
<!--        LEFT JOIN u_cost_spu_config c ON a.reported_work_id = c.related_up_order_id AND a.material_id = c.material_id AND c.data_status = 1-->
<!--        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)-->
<!--        AND b.form_dt >= #{reqVO.startDate}-->
<!--        AND b.form_dt &lt;= #{reqVO.endDate}-->
<!--        GROUP BY b.form_dt, b.undertake_org_id, c.spu_code, c.spu_id  &#45;&#45; 添加 GROUP BY-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--        b.undertake_org_id AS undertakeOrgId,-->
<!--        b.form_dt,-->
<!--        c.spu_code AS spuCode,-->
<!--        c.spu_id AS spuId,-->
<!--        0 AS income,-->
<!--        0 AS shipmentQty,-->
<!--        0 AS prodDailyIndirectCost,-->
<!--        0 AS prodLaborCost,-->
<!--        0 AS prodMaterialCost,-->
<!--        COALESCE(SUM(CASE WHEN a.order_type IN (0, 1, 2) THEN a.excl_tax_amt ELSE 0 END), 0) - -->
<!--        COALESCE(SUM(CASE WHEN a.order_type IN (3, 4) THEN a.excl_tax_amt ELSE 0 END), 0) AS prodPurchaseCost-->
<!--        FROM u_cost_prod_purchase a-->
<!--        LEFT JOIN u_cost_prod_purchase_detail b ON a.cost_prod_purchase_id = b.cost_prod_purchase_id-->
<!--        LEFT JOIN u_cost_spu_config c ON a.related_order_id = c.related_up_order_id AND a.material_id = c.material_id AND c.data_status = 1-->
<!--        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)-->
<!--        AND b.detail_type = 0-->
<!--        AND b.form_dt >= #{reqVO.startDate}-->
<!--        AND b.form_dt &lt;= #{reqVO.endDate}-->
<!--        GROUP BY b.form_dt, b.undertake_org_id, c.spu_code, c.spu_id  &#45;&#45; 添加 GROUP BY-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--        b.undertake_org_id AS undertakeOrgId,-->
<!--        b.form_dt,-->
<!--        c.spu_code AS spuCode,-->
<!--        c.spu_id AS spuId,-->
<!--        0 AS income,-->
<!--        0 AS shipmentQty,-->
<!--        0 AS prodDailyIndirectCost,-->
<!--        SUM(a.total_amt) AS prodLaborCost,-->
<!--        0 AS prodMaterialCost,-->
<!--        0 AS prodPurchaseCost-->
<!--        FROM u_cost_prod_labor a-->
<!--        RIGHT JOIN u_cost_prod_labor_detail b ON a.cost_prod_labor_id = b.cost_prod_labor_id-->
<!--        LEFT JOIN u_cost_spu_config c ON a.reported_work_id = c.related_up_order_id AND a.material_id = c.material_id AND c.data_status = 1-->
<!--        WHERE c.material_id IS NOT NULL AND c.material_source_dict_id IN (0, 1)-->
<!--        AND b.form_dt >= #{reqVO.startDate}-->
<!--        AND b.form_dt &lt;= #{reqVO.endDate}-->
<!--        GROUP BY b.form_dt, b.undertake_org_id, c.spu_code, c.spu_id  &#45;&#45; 添加 GROUP BY-->
<!--        ) t-->
<!--        GROUP BY undertakeOrgId, spuCode, spuId, formDt,formDtRange  &#45;&#45; 外层 GROUP BY-->
<!--        ORDER BY formDt DESC, undertakeOrgId;-->
<!--    </select>-->

    <select id="selectProcessReportedList" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costanalysis.vo.CompanyCostReportedRespVO">
        SELECT
            b.undertake_org_id,
            <if test="reqVO.reportType == 0">
                TO_CHAR(b.form_dt, 'YYYY-MM-DD') form_dt,
            </if>
            <if test="reqVO.reportType == 1">
                TO_CHAR(b.form_dt, 'YYYY-WW') form_dt,
            </if>
            <if test="reqVO.reportType == 2">
                TO_CHAR(b.form_dt, 'YYYY-MM') form_dt,
            </if>
            b.process_id,
            SUM(a.reported_qty) AS reported_qty
        FROM u_cost_prod_labor a
        LEFT JOIN u_cost_prod_labor_detail b ON b.cost_prod_labor_id = a.cost_prod_labor_id
        LEFT JOIN u_cost_process_config c ON c.process_id = a.process_id
        WHERE b.process_id IS NOT NULL AND c.process_id IS NOT NULL
        GROUP BY form_dt, b.undertake_org_id, b.process_id
        ORDER BY b.process_id
     </select>


    <select id="selectSelfMadeProductList" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costanalysis.vo.CompanyCostReportedRespVO">
        SELECT
            b.undertake_org_id,
            <if test="reqVO.reportType == 0">
                TO_CHAR(b.form_dt, 'YYYY-MM-DD') form_dt,
            </if>
            <if test="reqVO.reportType == 1">
                TO_CHAR(b.form_dt, 'YYYY-WW') form_dt,
            </if>
            <if test="reqVO.reportType == 2">
                TO_CHAR(b.form_dt, 'YYYY-MM') form_dt,
            </if>
            b.process_id,
            a.undertake_material_code,
            a.undertake_material_id,
            SUM(a.reported_qty) AS reported_qty
        FROM u_cost_prod_labor a
        LEFT JOIN u_cost_prod_labor_detail b ON b.cost_prod_labor_id = a.cost_prod_labor_id
        LEFT JOIN u_cost_process_config c ON c.process_id = a.process_id
        LEFT JOIN u_material d ON a.material_id = d.material_id AND d.data_status = 1
        WHERE b.process_id IS NOT NULL AND c.process_id IS NOT NULL AND d.material_source_dict_id IN (0, 1)
        GROUP BY b.form_dt, b.undertake_org_id, b.process_id, a.undertake_material_code,a.undertake_material_id
        ORDER BY b.process_id
    </select>
</mapper>
