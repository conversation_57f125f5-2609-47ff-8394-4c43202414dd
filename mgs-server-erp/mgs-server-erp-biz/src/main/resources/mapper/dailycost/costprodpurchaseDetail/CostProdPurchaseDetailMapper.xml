<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costprodpurchasedetail.CostProdPurchaseDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <delete id="deleteByCostProdPurchaseId">
        DELETE FROM u_cost_prod_purchase_detail
        WHERE cost_prod_purchase_id = #{costProdPurchaseId}
    </delete>

    <update id="costProdPurchaseIdEdit">
        UPDATE u_cost_prod_purchase_detail SET total_amt = #{reqVO.totalAmt}
        WHERE cost_prod_purchase_id = #{reqVO.costProdPurchaseId}
    </update>

    <sql id="dateTypeChoose">
        <choose>
            <when test="reqVO.dateType == 0">
                TO_CHAR(DATE(a.form_dt), 'YYYY-MM-DD') AS period
            </when>
            <when test="reqVO.dateType == 1">
                TO_CHAR(DATE_TRUNC('week', a.form_dt), 'IYYY') || '年' || TO_CHAR(DATE_TRUNC('week', a.form_dt), 'IW') || '周' AS period,
                DATE_TRUNC('week', a.form_dt) AS week_start,  -- 周开始日期
                DATE_TRUNC('week', a.form_dt) + INTERVAL '6 days' AS week_end  -- 周结束日期
            </when>
            <when test="reqVO.dateType == 2">
                TO_CHAR(DATE_TRUNC('month', a.form_dt), 'YYYY-MM') AS period
            </when>
        </choose>
    </sql>

    <sql id="dateTypeGroup">
        <choose>
            <when test="reqVO.dateType == 0">
                DATE(a.form_dt)
            </when>
            <when test="reqVO.dateType == 1">
                DATE_TRUNC('week', a.form_dt)
            </when>
            <when test="reqVO.dateType == 2">
                DATE_TRUNC('month', a.form_dt)
            </when>
        </choose>
    </sql>

<!--    数据比较难找，先这么关联表处理 Q1 成本   Q2 收入-->
    <select id="getSummary" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchasedetail.vo.CostReportRespVO">
        SELECT
        COALESCE(q1.period, q2.period) AS date_str,
        <if test="reqVO.dateType == 1">
            TO_CHAR(COALESCE(q1.week_start,q2.week_start), 'YYYY-MM-DD') AS week_start_date,
            TO_CHAR(COALESCE(q1.week_end,q2.week_end), 'YYYY-MM-DD') AS week_end_date,
            CONCAT(TO_CHAR(COALESCE(q1.week_start,q2.week_start), 'YYYY-MM-DD'), ' ~ ', TO_CHAR(COALESCE(q1.week_end,q2.week_end), 'YYYY-MM-DD')) AS dateRange,
        </if>
        COALESCE(q1.undertake_org_id, q2.undertake_org_id) AS undertake_org_id,
        COALESCE(q1.undertake_material_id, q2.undertake_material_id) AS undertakeMaterialId,
        COALESCE(q1.undertake_material_code, q2.undertake_material_code) AS undertakeMaterialCode,
        COALESCE(q1.total_qty, 0) AS purchase_qty,
        COALESCE(q1.total_amount, 0) AS purchase_amt,
        COALESCE(q2.total_qty2, 0) AS income_qty,
        COALESCE(q2.total_amount2, 0) AS income_amt,
        COALESCE(q2.total_amount2, 0) - COALESCE(q1.total_amount, 0) AS diff_amt
        FROM
        (
        SELECT
            <include refid="dateTypeChoose" />,
            a.undertake_org_id,
            b.undertake_material_id,
            b.undertake_material_code,
            SUM(CASE WHEN b.order_type IN (0, 1, 2) THEN b.qty ELSE 0 END) -
            SUM(CASE WHEN b.order_type = 3 THEN b.qty ELSE 0 END) AS total_qty,
            SUM(CASE WHEN b.order_type IN (0, 1, 2) THEN b.excl_tax_amt ELSE 0 END) -
            SUM(CASE WHEN b.order_type IN (3, 4) THEN b.excl_tax_amt ELSE 0 END) AS total_amount
        FROM
        u_cost_prod_purchase_detail a
        LEFT JOIN
        u_cost_prod_purchase b ON a.cost_prod_purchase_id = b.cost_prod_purchase_id
        right join u_material c on b.material_id=c.material_id and c.data_status = 1
        WHERE
        a.detail_type = 1 and c.material_source_dict_id = 2
        <if test="reqVO.startDate != null">
            AND a.form_dt >= #{reqVO.startDate}
        </if>
        <if test="reqVO.endDate != null">
            AND a.form_dt &lt;= #{reqVO.endDate}
        </if>
        <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
            and a.undertake_org_id IN
            <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND c.material_name like concat('%', #{reqVO.materialName}, '%')
        </if>
        GROUP BY
        a.undertake_org_id,b.undertake_material_id, b.undertake_material_code,
        <include refid="dateTypeGroup" />
        ) q1
        FULL OUTER JOIN
        (
        SELECT
            <include refid="dateTypeChoose" />,
            a.undertake_org_id,
            b.undertake_material_id,
            b.undertake_material_code,
            SUM(CASE WHEN b.order_type = 0 THEN a.qty ELSE 0 END) -
            SUM(CASE WHEN b.order_type = 1 THEN a.qty ELSE 0 END) AS total_qty2,
            SUM(CASE WHEN b.order_type = 0 THEN a.total_amt ELSE 0 END) -
            SUM(CASE WHEN b.order_type IN (1, 2) THEN a.total_amt ELSE 0 END) AS total_amount2
        FROM
        u_cost_sale_income_detail a
        LEFT JOIN u_cost_sale_income b ON A.sale_income_id = b.sale_income_id
        right join u_material c on a.material_id=c.material_id and c.data_status = 1
        WHERE
        a.detail_type = 1 and c.material_source_dict_id = 2
        <if test="reqVO.orgIdList != null and reqVO.orgIdList.size > 0 ">
            and a.undertake_org_id IN
            <foreach item="item" index="index" collection="reqVO.orgIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.startDate != null">
            AND a.form_dt >= #{reqVO.startDate}
        </if>
        <if test="reqVO.endDate != null">
            AND a.form_dt &lt;= #{reqVO.endDate}
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND c.material_name like concat('%', #{reqVO.materialName}, '%')
        </if>
        GROUP BY
        a.undertake_org_id,b.undertake_material_id, b.undertake_material_code,
        <include refid="dateTypeGroup" />
        ) q2 ON
        q1.period = q2.period
        AND q1.undertake_material_code = q2.undertake_material_code
        order by date_str desc
    </select>

<!--    <select id="getSummaryList" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchasedetail.vo.CostReportRespVO">-->
<!--        WITH date_series AS (-->
<!--            SELECT period-->
<!--            FROM (-->
<!--            &#45;&#45; 生成日期序列（天、周、月）-->
<!--            <choose>-->
<!--                <when test="reqVO.dateType == 0">-->
<!--                    SELECT-->
<!--                    TO_CHAR(generate_series(#{reqVO.startDate}::timestamp, #{reqVO.endDate}::timestamp, '1 day'::interval), 'YYYY-MM-DD') AS period-->
<!--                </when>-->
<!--                <when test="reqVO.dateType == 1">-->
<!--                    TO_CHAR(generate_series(DATE_TRUNC('week', #{reqVO.startDate}::timestamp), DATE_TRUNC('week', #{reqVO.endDate}::timestamp) + INTERVAL '6 days', '1 week'::interval), 'IYYY') || '年' || TO_CHAR(DATE_TRUNC('week', generate_series(DATE_TRUNC('week', #{reqVO.startDate}::timestamp), DATE_TRUNC('week', #{reqVO.endDate}::timestamp) + INTERVAL '6 days', '1 week'::interval)), 'IW') || '周' AS period-->
<!--                </when>-->
<!--                <when test="reqVO.dateType == 2">-->
<!--                    TO_CHAR(SELECT generate_series(DATE_TRUNC('month', #{reqVO.startDate}::timestamp), DATE_TRUNC('month', #{reqVO.endDate}::timestamp) + INTERVAL '1 month' - INTERVAL '1 day', '1 month'::interval), 'YYYY-MM') AS period-->
<!--                </when>-->
<!--            </choose>-->
<!--            ) AS generated_periods-->
<!--        ),-->

<!--        purchase_summary AS (-->
<!--            SELECT-->
<!--            ds.period,-->
<!--            <if test="reqVO.dateType == 1">-->
<!--                DATE_TRUNC('week', a.form_dt) AS week_start,  &#45;&#45; 周开始日期-->
<!--                DATE_TRUNC('week', a.form_dt) + INTERVAL '6 days' AS week_end  &#45;&#45; 周结束日期-->
<!--            </if>-->
<!--            a.undertake_org_id,-->
<!--            c.spu_code,-->
<!--            d.material_name,-->
<!--            COALESCE(SUM(b.qty), 0) AS total_qty,-->
<!--            COALESCE(SUM(b.excl_tax_amt), 0) AS total_amount-->
<!--            FROM u_cost_prod_purchase_detail a-->
<!--            LEFT JOIN u_cost_prod_purchase b ON a.cost_prod_purchase_id = b.cost_prod_purchase_id-->
<!--            RIGHT JOIN u_cost_spu_config c ON b.material_id = c.material_id AND b.related_order_id = c.related_up_order_id AND c.spu_code IS NOT NULL-->
<!--            LEFT JOIN u_spu d ON c.spu_id = d.spu_id-->
<!--            RIGHT JOIN date_series ds ON ds.period =-->
<!--            <choose>-->
<!--                <when test="reqVO.dateType == 0">-->
<!--                    TO_CHAR(DATE(a.form_dt), 'YYYY-MM-DD')-->
<!--                </when>-->
<!--                <when test="reqVO.dateType == 1">-->
<!--                    TO_CHAR(DATE_TRUNC('week', a.form_dt), 'IYYY') || '年' || TO_CHAR(DATE_TRUNC('week', a.form_dt), 'IW') || '周'-->
<!--                </when>-->
<!--                <when test="reqVO.dateType == 2">-->
<!--                    TO_CHAR(DATE_TRUNC('month', a.form_dt), 'YYYY-MM')-->
<!--                </when>-->
<!--            </choose>-->
<!--            WHERE a.detail_type = 1-->
<!--            <if test="reqVO.startDate != null"> AND a.form_dt >= #{reqVO.startDate}::timestamp </if>-->
<!--            <if test="reqVO.endDate != null"> AND a.form_dt &lt;= #{reqVO.endDate}::timestamp </if>-->
<!--            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''"> AND a.undertake_org_id = #{reqVO.companyOrgId} </if>-->
<!--            <if test="reqVO.materialName != null and reqVO.materialName != ''"> AND d.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%') </if>-->
<!--            GROUP BY ds.period, a.undertake_org_id, c.spu_code, d.material_name-->
<!--        ),-->

<!--        income_summary AS (-->
<!--            SELECT-->
<!--            ds.period,-->
<!--            <if test="reqVO.dateType == 1">-->
<!--                DATE_TRUNC('week', a.form_dt) AS week_start,  &#45;&#45; 周开始日期-->
<!--                DATE_TRUNC('week', a.form_dt) + INTERVAL '6 days' AS week_end  &#45;&#45; 周结束日期-->
<!--            </if>-->
<!--            a.undertake_org_id,-->
<!--            c.spu_code,-->
<!--            d.material_name,-->
<!--            COALESCE(SUM(a.qty), 0) AS total_qty2,-->
<!--            COALESCE(SUM(a.total_amt), 0) AS total_amount2-->
<!--            FROM u_cost_sale_income_detail a-->
<!--            LEFT JOIN u_cost_sale_income b ON a.sale_income_id = b.sale_income_id-->
<!--            RIGHT JOIN u_cost_spu_config c ON a.material_id = c.material_id AND b.related_up_form_id = c.related_up_order_id AND c.spu_code IS NOT NULL-->
<!--            LEFT JOIN u_spu d ON c.spu_id = d.spu_id-->
<!--            RIGHT JOIN date_series ds ON ds.period =-->
<!--            <choose>-->
<!--                <when test="reqVO.dateType == 0">-->
<!--                    TO_CHAR(DATE(a.form_dt), 'YYYY-MM-DD')-->
<!--                </when>-->
<!--                <when test="reqVO.dateType == 1">-->
<!--                    TO_CHAR(DATE_TRUNC('week', a.form_dt), 'IYYY') || '年' || TO_CHAR(DATE_TRUNC('week', a.form_dt), 'IW') || '周'-->
<!--                </when>-->
<!--                <when test="reqVO.dateType == 2">-->
<!--                    TO_CHAR(DATE_TRUNC('month', a.form_dt), 'YYYY-MM')-->
<!--                </when>-->
<!--            </choose>-->
<!--            WHERE a.detail_type = 1-->
<!--            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''"> AND a.undertake_org_id = #{reqVO.companyOrgId} </if>-->
<!--            <if test="reqVO.startDate != null"> AND a.form_dt >= #{reqVO.startDate}::timestamp </if>-->
<!--            <if test="reqVO.endDate != null"> AND a.form_dt &lt;= #{reqVO.endDate}::timestamp </if>-->
<!--            <if test="reqVO.materialName != null and reqVO.materialName != ''"> AND d.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%') </if>-->
<!--            GROUP BY ds.period, a.undertake_org_id, c.spu_code, d.material_name-->
<!--        ),-->

<!--        SELECT-->
<!--        ds.period AS date_str,-->
<!--        <if test="reqVO.dateType == 1">-->
<!--            CONCAT(TO_CHAR(COALESCE(q1.week_start, q2.week_start), 'YYYY-MM-DD'), ' ~ ', TO_CHAR(COALESCE(q1.week_end, q2.week_end), 'YYYY-MM-DD')) AS dateRange,-->
<!--        </if>-->
<!--        COALESCE(q1.undertake_org_id, q2.undertake_org_id) AS undertake_org_id,-->
<!--        COALESCE(q1.spu_code, q2.spu_code) AS material_code,-->
<!--        COALESCE(q1.material_name, q2.material_name) AS material_name,-->
<!--        COALESCE(q1.total_qty, 0) AS purchase_qty,-->
<!--        COALESCE(q1.total_amount, 0) AS purchase_amt,-->
<!--        COALESCE(q2.total_qty2, 0) AS income_qty,-->
<!--        COALESCE(q2.total_amount2, 0) AS income_amt,-->
<!--        COALESCE(q2.total_amount2, 0) - COALESCE(q1.total_amount, 0) AS diff_amt-->
<!--        FROM date_series ds-->
<!--        LEFT JOIN purchase_summary q1 ON ds.period = q1.period-->
<!--        LEFT JOIN income_summary q2 ON ds.period = q2.period AND q1.spu_code = q2.spu_code-->
<!--        ORDER BY date_str DESC;-->
<!--    </select>-->

    <select id="getPrice"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">
        WITH LatestRecords AS (
            SELECT DISTINCT detail.cost_prod_purchase_detail_id AS id
            , CAST(prod.form_dt AS DATE) AS statisDate
            , prod.form_dt AS dateTime
            , prod.excl_tax_unit_price AS price
            , ROW_NUMBER() OVER (PARTITION BY CAST(detail.form_dt AS DATE) ORDER BY detail.form_dt DESC) AS rn
            , detail.tenant_id
            FROM erp.u_cost_prod_purchase_detail detail
                     LEFT JOIN erp.u_cost_prod_purchase prod ON detail.cost_prod_purchase_id = prod.cost_prod_purchase_id
            WHERE detail.detail_type = 0
              AND detail.undertake_org_id = #{reqVO.undertakeOrgId}
              AND prod.undertake_material_id = #{reqVO.undertakeMaterialId}
              AND prod.form_dt BETWEEN #{reqVO.startDt} AND #{reqVO.endDt}
        )
        SELECT
            statisDate,
            price
        FROM LatestRecords
        WHERE
            rn = 1
        ORDER BY
            statisDate;
    </select>

<!--    <select id="getPrice"-->
<!--            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">-->
<!--        SELECT DISTINCT detail.cost_prod_purchase_detail_id AS id-->
<!--                      , CAST(detail.form_dt AS DATE) AS statisDate-->
<!--                      , detail.form_dt AS dateTime-->
<!--                      , prod.excl_tax_unit_price AS price-->
<!--        FROM erp.u_cost_prod_purchase_detail detail-->
<!--        LEFT JOIN erp.u_cost_prod_purchase prod ON detail.cost_prod_purchase_id = prod.cost_prod_purchase_id-->
<!--        LEFT JOIN erp.u_cost_spu_config spu ON spu.related_up_order_id = prod.related_order_id-->
<!--        WHERE detail.detail_type = 0-->
<!--        AND detail.undertake_org_id = #{reqVO.undertakeOrgId}-->
<!--        AND spu.spu_id = #{reqVO.spuId}-->
<!--        AND detail.form_dt BETWEEN #{reqVO.startDt} AND #{reqVO.endDt}-->
<!--    </select>-->
<!--    AND spu.spu_id = #{reqVO.spuId}-->
<!--    AND detail.form_dt >= #{reqVO.nowDt}-->
<!--    AND detail.form_dt &lt; #{reqVO.nextDt}-->
<!--    AND detail.form_dt = (-->
<!--    SELECT MAX(form_dt)-->
<!--    FROM erp.u_cost_prod_purchase_detail-->
<!--    WHERE undertake_org_id = #{reqVO.undertakeOrgId}-->
<!--    AND form_dt >= #{reqVO.nowDt}-->
<!--    AND form_dt &lt; #{reqVO.nextDt}-->
<!--    );-->
</mapper>
