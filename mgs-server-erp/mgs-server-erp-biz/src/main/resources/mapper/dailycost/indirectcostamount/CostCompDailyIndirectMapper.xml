<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.indirectcostamount.CostCompDailyIndirectMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.comp.CostCompDailyIndirectRespVO">
        SELECT detail.undertake_org_id, detail.provision_date AS costDate, COALESCE(SUM(detail.undertake_amt), 0) AS costAmt
        FROM erp.u_cost_aggre_detail detail
        <where>
            <if test="reqVO.bizType != null and reqVO.bizType == 1">
                AND detail.aggre_type IN (0, 2)
            </if>
            <if test="reqVO.undertakeOrgId != null and reqVO.undertakeOrgId != ''">
                AND detail.undertake_org_id = #{reqVO.undertakeOrgId}
            </if>
            <if test="reqVO.startCostDate != null and reqVO.endCostDate != null">
                AND (detail.provision_date BETWEEN #{reqVO.startCostDate} AND #{reqVO.endCostDate})
            </if>
        </where>
        GROUP BY detail.undertake_org_id, detail.provision_date
        ORDER BY detail.provision_date DESC;
    </select>
    <select id="queryCompIndirectCost" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(detail.undertake_amt), 0)
        FROM erp.u_cost_aggre_detail detail
        WHERE detail.undertake_org_id = #{reqVO.undertakeOrgId}
        AND detail.provision_date = #{reqVO.costDate}
        <if test="bizType != null and bizType == 1">
            AND detail.aggre_type IN (0, 2)
        </if>
    </select>
    <select id="getCompIndirectCostByDateArea" resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">
        SELECT COALESCE(SUM(detail.undertake_amt), 0) AS indirectCost, detail.provision_date AS statisDate
        FROM erp.u_cost_aggre_detail detail
        WHERE detail.undertake_org_id = #{reqVO.undertakeOrgId}
        AND detail.provision_date BETWEEN #{reqVO.startStatisDate} AND #{reqVO.endStatisDate}
        GROUP BY detail.provision_date
    </select>

</mapper>
