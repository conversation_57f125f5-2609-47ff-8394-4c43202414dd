<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.indirectcostamount.CostSpuDailyIndirectMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getProdLaborTotalAmt" resultType="java.math.BigDecimal"
            parameterType="com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.spu.CostSpuDailyIndirectRespVO">
        SELECT COALESCE(SUM(prod.total_amt), 0)
        FROM erp.u_cost_prod_labor prod
        LEFT JOIN erp.u_cost_process_config config ON prod.process_id = config.process_id
        WHERE prod.undertake_org_id = #{reqVO.undertakeOrgId}
        <if test="undertakeMaterialId != null">
            AND prod.undertake_material_id = #{undertakeMaterialId}
        </if>
        <if test="undertakeMaterialId == null">
            AND prod.undertake_material_id IS NOT NULL
        </if>
        AND DATE(prod.reported_dt) = #{reqVO.costDate} ;
    </select>
    <select id="queryPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.spu.CostSpuDailyIndirectRespVO">
        SELECT prod.undertake_org_id, prod.undertake_material_id, prod.undertake_material_code,material.material_name AS undertakeMaterialName, CAST(prod.reported_dt AS DATE) AS costDate
        FROM erp.u_cost_prod_labor prod
        LEFT JOIN erp.u_cost_process_config config ON prod.process_id = config.process_id
        LEFT JOIN u_material material ON prod.undertake_material_id = material.material_id AND material.deleted = 0
        <where>
            AND prod.undertake_org_id IS NOT NULL
            AND prod.undertake_material_id IS NOT NULL
            AND config.process_id IS NOT NULL
            <if test="reqVO.undertakeOrgId != null and reqVO.undertakeOrgId !=''">
                AND prod.undertake_org_id = #{reqVO.undertakeOrgId}
            </if>
            <if test="reqVO.undertakeMaterialName != null and reqVO.undertakeMaterialName !=''">
                AND material.material_name LIKE CONCAT('%', #{reqVO.undertakeMaterialName}, '%')
            </if>
            <if test="reqVO.startCostDate != null and reqVO.endCostDate != null">
                AND (CAST(prod.reported_dt AS DATE) BETWEEN #{reqVO.startCostDate} AND #{reqVO.endCostDate})
            </if>
        </where>
        GROUP BY prod.undertake_org_id, prod.undertake_material_id, prod.undertake_material_code,material.material_name, CAST(prod.reported_dt AS DATE)
        ORDER BY CAST(prod.reported_dt AS DATE) DESC
    </select>
    <select id="getSpuIndirectCost" resultType="java.math.BigDecimal">
        WITH
        sum_calculation AS (
        SELECT
        COALESCE(SUM(CASE WHEN prod.undertake_material_id = #{reqVO.undertakeMaterialId} THEN prod.total_amt END), 0) AS sum1,
        COALESCE(SUM(CASE WHEN prod.undertake_material_id IS NOT NULL THEN prod.total_amt END), 0) AS sum2,
        prod.tenant_id
        FROM erp.u_cost_prod_labor prod
        LEFT JOIN erp.u_cost_process_config config ON prod.process_id = config.process_id
        WHERE prod.undertake_org_id = #{reqVO.undertakeOrgId}
        AND CAST(prod.reported_dt AS DATE) = #{reqVO.costDate} AND config.process_id IS NOT NULL
        GROUP BY prod.tenant_id
        ),
        total_amt AS (
        SELECT COALESCE(SUM(undertake_amt), 0) AS total_amt,
               tenant_id
        FROM erp.u_cost_aggre_detail
        WHERE undertake_org_id = #{reqVO.undertakeOrgId}
        <if test="bizType != null and bizType == 1">
            AND aggre_type IN (0, 2)
        </if>
        AND provision_date = #{reqVO.costDate}
        GROUP BY tenant_id
        )
        SELECT
        amt.total_amt * CASE
        WHEN sums.sum1 = 0 OR sums.sum2 = 0 THEN 0
        ELSE ROUND(sums.sum1::NUMERIC / sums.sum2::NUMERIC, 6)
        END AS price
        FROM sum_calculation sums, total_amt amt;
    </select>
    <!--    <select id="queryPage"-->
<!--            resultType="com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.spu.CostSpuDailyIndirectRespVO">-->
<!--        SELECT daily.*, spu.material_name-->
<!--        FROM erp.u_cost_spu_daily_indirect daily-->
<!--        LEFT JOIN u_spu spu ON spu.spu_id = daily.spu_id-->
<!--        <where>-->
<!--            <if test="reqVO.undertakeOrgId != null and reqVO.undertakeOrgId !=''">-->
<!--                AND daily.undertake_org_id = #{reqVO.undertakeOrgId}-->
<!--            </if>-->
<!--            <if test="reqVO.materialName != null and reqVO.materialName !=''">-->
<!--                AND spu.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')-->
<!--            </if>-->
<!--            <if test="reqVO.startCostDate != null and reqVO.endCostDate != null">-->
<!--                AND (daily.cost_date BETWEEN #{reqVO.startCostDate} AND #{reqVO.endCostDate})-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY daily.cost_date DESC, daily.created_dt DESC-->
<!--    </select>-->
</mapper>
