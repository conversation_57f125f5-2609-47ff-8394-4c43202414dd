<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costprodlabordetail.CostProdLaborDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <delete id="deleteByCostProdLaborId">
        DELETE FROM u_cost_prod_labor_detail
        WHERE cost_prod_labor_id = #{costProdLaborId}
    </delete>
    <select id="getPrice"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">
        WITH OriginalInfo AS (
            SELECT DISTINCT
                detail.cost_prod_labor_detail_id AS ids,
                detail.process_id AS processId,
                CAST(prod.reported_dt AS DATE) AS statisDate,
                prod.reported_dt AS dateTime,
                prod.reported_qty AS reportedQty,
                prod.excl_tax_unit_price AS price,
                detail.tenant_id
            FROM erp.u_cost_prod_labor_detail detail
                     LEFT JOIN erp.u_cost_prod_labor prod
                               ON detail.cost_prod_labor_id = prod.cost_prod_labor_id
            WHERE detail.undertake_org_id = #{reqVO.undertakeOrgId}
              AND prod.undertake_material_id = #{reqVO.undertakeMaterialId}
              AND prod.reported_dt BETWEEN #{reqVO.startDt} AND #{reqVO.endDt}
        ),
             LatestRecords AS (
                 SELECT
                     statisDate,
                     processId,
                     price,
                     dateTime,
                     reportedQty,
                     ROW_NUMBER() OVER ( PARTITION BY statisDate, processId ORDER BY dateTime DESC) AS rn,
                     tenant_id
                 FROM OriginalInfo
             )
        SELECT
            statisDate,
            SUM(price) AS price
        FROM LatestRecords
        WHERE rn = 1
        GROUP BY statisDate
    </select>
    <select id="getReportedQty"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">
        WITH OriginalInfo AS (
            SELECT DISTINCT
                prod.process_id AS processId,
                CAST(prod.reported_dt AS DATE) AS statisDate,
                prod.reported_dt AS dateTime,
                prod.reported_qty AS reportedQty,
                prod.excl_tax_unit_price AS price,
                prod.tenant_id
            FROM erp.u_cost_prod_labor prod
            WHERE prod.undertake_org_id = #{reqVO.undertakeOrgId}
              AND prod.undertake_material_id = #{reqVO.undertakeMaterialId}
              AND prod.reported_dt BETWEEN #{reqVO.startDt} AND #{reqVO.endDt}
        )
        SELECT
        statisDate,
        SUM(reportedQty) AS reportedQty
        FROM OriginalInfo
        GROUP BY statisDate
    </select>


</mapper>
