<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costprodpurchase.CostProdPurchaseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectPage" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseRespVO">
        SELECT cost.*, material.material_id, material.material_code, material.material_name,
        material.material_source_dict_id, material.spec_attribute_str
        FROM u_cost_prod_purchase cost
        LEFT JOIN u_material material ON cost.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND cost.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (cost.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.amortiseStatus != null">
                AND cost.amortise_status = #{reqVO.amortiseStatus}
            </if>
            <if test="reqVO.orderType != null">
                AND cost.order_type = #{reqVO.orderType}
            </if>
            <if test="reqVO.directorId != null">
                AND cost.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND cost.director_org_id = #{reqVO.directorOrgId}
            </if>
                AND cost.aggre_status = 0
        </where>
        ORDER BY
        cost.created_dt DESC,               -- 按创建日期降序
        cost.related_order_id ASC,          -- 然后按订单ID升序
        cost.related_row_no ASC;            -- 最后按行号升序
    </select>

    <delete id="deleteByRelatedOrderId">
        DELETE FROM u_cost_prod_purchase
        WHERE related_order_id = #{relatedOrderId}
    </delete>

    <delete id="deleteByRelatedOrderDetailId">
        DELETE FROM u_cost_prod_purchase
        WHERE related_order_detail_id = #{relatedOrderDetailId}
    </delete>

    <select id="selectDetail" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseRespVO">
        SELECT cost.*, material.material_id, material.material_code, material.material_name,
        material.material_source_dict_id, material.spec_attribute_str
        FROM u_cost_prod_purchase cost
        LEFT JOIN u_material material ON cost.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="costProdPurchaseId != null">
                AND cost.cost_prod_purchase_id = #{costProdPurchaseId}
            </if>
            <if test="relatedOrderDetailId != null">
                AND cost.related_order_detail_id = #{relatedOrderDetailId}
            </if>
        </where>

    </select>


    <select id="selectReqList" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseRespVO">
        SELECT cost.*, material.material_id, material.material_code, material.material_name,
        material.material_source_dict_id, material.spec_attribute_str
        FROM u_cost_prod_purchase cost
        LEFT JOIN u_material material ON cost.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.costProdPurchaseIdList != null and reqVO.costProdPurchaseIdList.size > 0 ">
                AND cost.cost_prod_purchase_id IN
                <foreach item="item" index="index" collection="reqVO.costProdPurchaseIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.relatedOrderId != null">
                AND cost.related_order_id = #{reqVO.relatedOrderId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND cost.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.aggreStatus != null">
                AND cost.aggre_status = #{reqVO.aggreStatus}
            </if>
        </where>
        ORDER BY cost.cost_prod_purchase_id DESC, cost.related_row_no ASC
    </select>


    <select id="queryOrderStatData" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseOrderRespVO">
        SELECT
            COUNT(CASE WHEN order_type = 0 THEN 1 END) AS purchase_order,
            COUNT(CASE WHEN order_type = 1 THEN 1 END) AS out_purchase_order,
            COUNT(CASE WHEN order_type = 2 THEN 1 END) AS process_out_purchase_order,
            COUNT(CASE WHEN order_type = 3 THEN 1 END) AS purchase_return_order,
            COUNT(CASE WHEN order_type = 4 THEN 1 END) AS purchase_deduction_order
        FROM u_cost_prod_purchase
        WHERE amortise_status = 0 AND aggre_status = 0;
    </select>

    <update id="updateByRelatedOrderDetailId">
        UPDATE u_cost_prod_purchase SET related_row_no = #{reqVO.relatedRowNo}, related_supplier_id = #{reqVO.relatedSupplierId}, form_dt = #{reqVO.formDt}
        , qty = #{reqVO.qty}, excl_tax_unit_price = #{reqVO.exclTaxUnitPrice}, excl_tax_amt = #{reqVO.exclTaxAmt}
        WHERE related_order_detail_id = #{reqVO.relatedOrderDetailId}
    </update>

    <update id="updateByRelatedOrderId">
        UPDATE u_cost_prod_purchase SET aggre_status = #{aggreStatus}
        WHERE related_order_id = #{relatedOrderId}
    </update>

    <update id="updateByundertakeMaterial">
        UPDATE u_cost_prod_purchase SET undertake_material_id = #{reqVO.undertakeMaterialId}, undertake_material_code = #{reqVO.undertakeMaterialCode}, material_bom_id_path = #{reqVO.materialBomIdPath}
        WHERE cost_prod_purchase_id = #{reqVO.costProdPurchaseId}
    </update>


</mapper>
