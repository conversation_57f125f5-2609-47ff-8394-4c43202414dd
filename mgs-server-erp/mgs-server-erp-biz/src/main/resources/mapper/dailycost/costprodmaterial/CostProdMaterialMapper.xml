<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costprodmaterial.CostProdMaterialMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectDetail" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costprodmaterial.vo.CostProdMaterialRespVO">
        SELECT * FROM u_cost_prod_material
        <where>
            <if test="reportedWorkId != null">
                AND reported_work_id = #{reportedWorkId}
            </if>
        </where>
    </select>

    <update id="updateByundertakeMaterial">
        UPDATE u_cost_prod_material SET undertake_material_id = #{reqVO.undertakeMaterialId}, undertake_material_code = #{reqVO.undertakeMaterialCode}, material_bom_id_path = #{reqVO.materialBomIdPath}
        WHERE cost_prod_material_id = #{reqVO.costProdMaterialId}
    </update>

</mapper>
