<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costaggreorder.CostAggreOriginalOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryAggreReimbursePaymentPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderRespVO">
        SELECT original.*, reimburse.reimburse_name, payment.out_bill_amt, reimburse.reimburser_id
        FROM erp.u_cost_aggre_original_order original
        LEFT JOIN erp.u_fee_reimburse_payment payment ON original.original_order_id = payment.fee_reimburse_payment_id
        LEFT JOIN erp.u_fee_reimburse reimburse ON payment.fee_reimburse_id = reimburse.fee_reimburse_id
        <where>
            <if test="reqVO.originalType != null">
                AND original.original_type = #{reqVO.originalType}
            </if>
            <if test="reqVO.originalOrderCode != null and reqVO.originalOrderCode != ''">
                AND original.original_order_code LIKE CONCAT('%', #{reqVO.originalOrderCode}, '%')
            </if>
            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
                AND original.company_org_id = #{reqVO.companyOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (original.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.aggreStatus != null">
                AND original.aggre_status = #{reqVO.aggreStatus}
            </if>
        </where>
        ORDER BY original.aggre_original_order_id DESC
    </select>
    <select id="queryAggrePurchasePage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderRespVO">
        SELECT original.*, purchase.related_supplier_id, purchase.excl_tax_total_amt, purchase.purchase_order_biz_type
        FROM erp.u_cost_aggre_original_order original
        LEFT JOIN erp.u_purchase_order purchase ON original.original_order_id = purchase.purchase_order_id
        <where>
            AND purchase.purchase_order_id NOT IN (
            SELECT DISTINCT prod.related_order_id
            FROM erp.u_cost_prod_purchase prod
            WHERE prod.amortise_status IN (1,2) AND prod.related_order_id = original.original_order_id
            )
            <if test="reqVO.originalType != null">
                AND original.original_type = #{reqVO.originalType}
            </if>
            <if test="reqVO.originalOrderCode != null and reqVO.originalOrderCode != ''">
                AND original.original_order_code LIKE CONCAT('%', #{reqVO.originalOrderCode}, '%')
            </if>
            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
                AND original.company_org_id = #{reqVO.companyOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (original.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.aggreStatus != null">
                AND original.aggre_status = #{reqVO.aggreStatus}
            </if>
        </where>
        ORDER BY original.aggre_original_order_id DESC
    </select>
    <select id="queryCommissionGrantPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderRespVO">
        SELECT original.*, commissiongrant.commission_grant_code, commissiongrant.source_order_code, commissiongrant.source_order_id, commissiongrant.source_order_row_no,
        commissiongrant.form_type, commissiongrant.qty, commissiongrant.excl_tax_amt,commissiongrant.commission_ratio, commissiongrant.commission_amt, commissiontask.sale_order_code,
        commissiontask.sale_order_id, commissiontask.related_row_no, commissiontask.customer_id, commissiontask.material_id, commissiontask.material_code, commissiontask.form_dt
        FROM erp.u_cost_aggre_original_order original
        LEFT JOIN erp.u_commission_grant commissiongrant ON original.original_order_id = commissiongrant.commission_grant_id
        LEFT JOIN erp.u_commission_task commissiontask ON commissiontask.commission_task_id = commissiongrant.commission_task_id
        <where>
            <if test="reqVO.originalType != null">
                AND original.original_type = #{reqVO.originalType}
            </if>
            <if test="reqVO.originalOrderCode != null and reqVO.originalOrderCode != ''">
                AND commissiontask.sale_order_code LIKE CONCAT('%', #{reqVO.originalOrderCode}, '%')
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (original.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.aggreStatus != null">
                AND original.aggre_status = #{reqVO.aggreStatus}
            </if>
        </where>
        ORDER BY original.aggre_original_order_id DESC
    </select>
    <select id="queryOutBillPurchasePage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderRespVO">
        SELECT original.*, bill.out_bill_name, bill.out_bill_amount AS outBillAmt
        FROM erp.u_cost_aggre_original_order original
        LEFT JOIN erp.u_out_bill bill ON original.original_order_id = bill.out_bill_id
        <where>
            <if test="reqVO.originalType != null">
                AND original.original_type = #{reqVO.originalType}
            </if>
            <if test="reqVO.originalOrderCode != null and reqVO.originalOrderCode != ''">
                AND original.original_order_code LIKE CONCAT('%', #{reqVO.originalOrderCode}, '%')
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (original.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.aggreStatus != null">
                AND original.aggre_status = #{reqVO.aggreStatus}
            </if>
        </where>
        ORDER BY original.aggre_original_order_id DESC
    </select>
    <select id="queryAggreOriginalStatData"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalStatRespVO">
        SELECT COUNT(CASE WHEN original_type = 0 THEN 1 END) AS reimbursePaymentCount,
               COUNT(CASE
                         WHEN original_type = 1 AND
                              (SELECT count(*)
                               FROM erp.u_cost_prod_purchase prod
                               WHERE prod.related_order_id = original.original_order_id
                                 AND prod.amortise_status IN (1,2)
                                 AND original.original_type = 1) = 0
                             THEN 1 END)                     AS purchaseCount,
               COUNT(CASE WHEN original_type = 2 THEN 1 END) AS utilityCostCount,
               COUNT(CASE WHEN original_type = 3 THEN 1 END) AS commissionDetailCount,
               COUNT(CASE WHEN original_type = 4 THEN 1 END) AS outBillCount
        FROM erp.u_cost_aggre_original_order original
        WHERE original.aggre_status = 0;
    </select>
    <select id="queryutilityCostPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderRespVO">
        SELECT original.*, utility.utility_config_id, utility.utility_archives_id
        , utility.conver_mult, utility.today_read_qty, utility.read_difference, utility.today_usage
        , utility.cost_price, utility.utility_amt
        FROM erp.u_cost_aggre_original_order original
                 LEFT JOIN erp.u_utility_cost utility ON original.original_order_id = utility.id
        <where>
            <if test="reqVO.originalType != null">
                AND original.original_type = #{reqVO.originalType}
            </if>
            <if test="reqVO.originalOrderCode != null and reqVO.originalOrderCode != ''">
                AND original.original_order_code LIKE CONCAT('%', #{reqVO.originalOrderCode}, '%')
            </if>
            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
                AND utility.company_id = CAST(#{reqVO.companyOrgId} AS int8 )
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (original.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.aggreStatus != null">
                AND original.aggre_status = #{reqVO.aggreStatus}
            </if>
        </where>
    </select>
</mapper>
