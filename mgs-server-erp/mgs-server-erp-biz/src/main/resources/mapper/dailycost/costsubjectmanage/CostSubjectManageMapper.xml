<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costsubjectmanage.CostSubjectManageMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="querySubjectByKeyword"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costsubjectmanage.vo.CostSubjectManageRespVO"
            parameterType="com.mongoso.mgs.module.dailycost.controller.admin.costsubjectmanage.vo.CostSubjectManageQueryReqVO">
        SELECT *
        FROM erp.u_cost_subject_manage
        <where>
            AND data_status = 1
            <if test="reqVO.keywordList != null and reqVO.keywordList.size != 0">
                AND (
                <foreach collection="reqVO.keywordList" item="item" separator=" OR ">
                    #{item} = ANY(string_to_array(cost_name_keyword, ','))
                </foreach>
                )
            </if>
        </where>
        ORDER BY cost_name_priority DESC, cost_subject_id DESC
            LIMIT 1;
    </select>

    <select id="selectCostSubjectManageList" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costsubjectmanage.vo.CostSubjectManageRespVO">
        SELECT *
        FROM erp.u_cost_subject_manage
        <where>
            <if test="reqVO.costSubjectCode != null and reqVO.costSubjectCode != ''">
                AND cost_subject_code LIKE CONCAT('%', #{reqVO.costSubjectCode}, '%')
            </if>
            <if test="reqVO.costSubjectName != null and reqVO.costSubjectName != ''">
                AND cost_subject_name LIKE CONCAT('%', #{reqVO.costSubjectName}, '%')
            </if>
            <if test="reqVO.costUsage != null">
                AND cost_usage = #{reqVO.costUsage}
            </if>
            <if test="reqVO.costType != null">
                AND cost_type = #{reqVO.costType}
            </if>
            <if test="reqVO.costUsage != null">
                AND cost_usage = #{reqVO.costUsage}
            </if>
            <if test="reqVO.applyCompanyList != null and reqVO.applyCompanyList.size() > 0">
                AND (
                ARRAY[<foreach collection="reqVO.applyCompanyList" item="item" separator=",">#{item}</foreach>]::text[] &lt;@ string_to_array(apply_company, ',')
                )
            </if>
            <if test="reqVO.approvedBy != null and reqVO.approvedBy != ''">
                AND approved_by LIKE CONCAT('%', #{reqVO.approvedBy}, '%')
            </if>
            <if test="reqVO.directorId != null">
                AND director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND data_status = #{reqVO.dataStatus}
            </if>
        </where>
        ORDER BY cost_subject_id DESC
    </select>
</mapper>
