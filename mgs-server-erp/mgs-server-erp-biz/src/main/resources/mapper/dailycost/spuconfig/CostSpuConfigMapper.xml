<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.spuconfig.CostSpuConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigRespVO">
        SELECT config.* FROM u_cost_spu_config config
        LEFT JOIN u_material material ON config.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.orderType != null">
                AND config.order_type = #{reqVO.orderType}
            </if>
            <if test="reqVO.relatedUpOrderCode != null and reqVO.relatedUpOrderCode != ''">
                AND config.related_up_order_code LIKE CONCAT('%', #{reqVO.relatedUpOrderCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND config.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND config.data_status = #{reqVO.dataStatus}
            </if>
        </where>
        ORDER BY config.spu_config_id DESC
    </select>
</mapper>
