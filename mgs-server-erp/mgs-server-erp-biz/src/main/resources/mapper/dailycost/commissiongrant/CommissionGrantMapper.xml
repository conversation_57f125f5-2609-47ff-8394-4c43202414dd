<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.commissiongrant.CommissionGrantMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <delete id="deleteByCostProdLaborId">
        DELETE FROM u_cost_prod_labor_detail
        WHERE cost_prod_labor_id = #{costProdLaborId}
    </delete>

    <select id="selectPage" resultType="com.mongoso.mgs.module.dailycost.controller.admin.commissiongrant.vo.CommissionGrantRespVO">
        SELECT a.*, b.sale_order_code, b.related_row_no, b.customer_id, b.material_id, b.material_code
        FROM u_commission_grant a
        LEFT JOIN u_commission_task b ON a.commission_task_id = b.commission_task_id
        <where>
            <if test="reqVO.commissionGrantCode != null and reqVO.commissionGrantCode != ''">
                AND a.commission_grant_code LIKE CONCAT('%', #{reqVO.commissionGrantCode}, '%')
            </if>
            <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
                AND b.sale_order_code LIKE CONCAT('%', #{reqVO.saleOrderCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND b.form_dt >= #{reqVO.startFormDt}
                AND b.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
                AND b.material_id IN
                <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.customerIdList != null and reqVO.customerIdList.size > 0 ">
                AND b.customer_id IN
                <foreach item="item" index="index" collection="reqVO.customerIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.created_dt desc
    </select>

    <select id="selectByIdDetail" resultType="com.mongoso.mgs.module.dailycost.controller.admin.commissiongrant.vo.CommissionGrantRespVO">
        SELECT a.*, b.sale_order_code, b.related_row_no, b.customer_id, b.material_id, b.material_code, b.qty, b.excl_tax_amt,
        b.commission_ratio, b.commission_amt, b.form_dt
        FROM u_commission_grant a
        LEFT JOIN u_commission_task b ON a.commission_task_id = b.commission_task_id
        WHERE a.commission_grant_id = #{commissionGrantId}
    </select>

</mapper>
