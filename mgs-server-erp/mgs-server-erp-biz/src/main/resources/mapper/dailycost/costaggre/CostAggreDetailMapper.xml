<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costaggre.CostAggreDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailRespVO">
        SELECT detail.*, aggre.form_dt
        FROM erp.u_cost_aggre_detail detail
        LEFT JOIN erp.u_cost_aggre aggre ON detail.aggre_id = aggre.aggre_id
        LEFT JOIN u_cost_subject_manage subject ON aggre.cost_subject_id = subject.cost_subject_id
        <where>
            <if test="reqVO.aggreDetailCode != null and reqVO.aggreDetailCode != ''">
                AND detail.aggre_detail_code LIKE CONCAT('%', #{reqVO.aggreDetailCode}, '%')
            </if>
            <if test="reqVO.aggreCode != null and reqVO.aggreCode != ''">
                AND detail.aggre_code LIKE CONCAT('%', #{reqVO.aggreCode}, '%')
            </if>
            <if test="reqVO.aggreName != null and reqVO.aggreName != ''">
                AND aggre.aggre_name LIKE CONCAT('%', #{reqVO.aggreName}, '%')
            </if>
            <if test="reqVO.aggreType != null">
                AND detail.aggre_type = #{reqVO.aggreType}
            </if>
            <if test="reqVO.aggreId != null">
                AND detail.aggre_id = #{reqVO.aggreId}
            </if>
            <if test="reqVO.costSubjectName != null and reqVO.costSubjectName != ''">
                AND subject.cost_subject_name LIKE CONCAT('%', #{reqVO.costSubjectName}, '%')
            </if>
            <if test="reqVO.costUsage != null">
                AND aggre.cost_usage = #{reqVO.costUsage}
            </if>
            <if test="reqVO.provisionStartDate != null and reqVO.provisionEndDate != null">
                AND (detail.provision_date BETWEEN #{reqVO.provisionStartDate} AND #{reqVO.provisionEndDate})
            </if>
            <if test="reqVO.provisionDate != null">
                AND detail.provision_date = #{reqVO.provisionDate}
            </if>
            <if test="reqVO.undertakeOrgId != null and reqVO.undertakeOrgId != ''">
                AND detail.undertake_org_id = #{reqVO.undertakeOrgId}
            </if>
        </where>
        ORDER BY detail.aggre_detail_id DESC
    </select>
    <select id="queryCompDailyIndirectCost" resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CompDailyIndirectCostStatRespVO"
            parameterType="java.util.List">
        SELECT detail.undertake_org_id AS undertakeOrgId, SUM(detail.daily_provision_amt) AS indirectCostAmt
        FROM erp.u_cost_aggre_detail detail
        WHERE detail.undertake_org_id IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        AND provision_date = CURRENT_DATE
        GROUP BY detail.undertake_org_id
    </select>
</mapper>
