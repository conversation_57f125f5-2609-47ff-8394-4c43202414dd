<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costaggre.CostAggreMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

<!--    <resultMap id="resultMap" type="com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.CostAggreRespVO">-->
<!--        <result column="undertake_obj_list" property="undertakeObjList"-->
<!--                typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>-->
<!--        <result column="provision_period" property="provisionPeriod"-->
<!--                typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>-->
<!--    </resultMap>-->

    <select id="getSalaryAggreStatus" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT
        CASE
        -- 存在数据且全部已审核为 1
        WHEN COUNT(*) > 0 AND SUM(CASE WHEN data_status != 1 THEN 1 ELSE 0 END) = 0 THEN 1
        -- 存在数据且全部未审核为 2
        WHEN COUNT(*) > 0 AND SUM(CASE WHEN data_status != 0 THEN 1 ELSE 0 END) = 0 THEN 2
        -- 其他情况（包括无数据、混合状态或存在其他值）
        ELSE 0
        END AS result
        FROM u_cost_aggre
        WHERE data_source = 1
        AND aggre_original_order_id = #{aggreOriginalOrderId}
        AND aggre_type IN (1,3);
    </select>

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.CostAggreRespVO">
        SELECT aggre.*, subject.cost_subject_name, original.original_type
        FROM u_cost_aggre aggre
        LEFT JOIN u_cost_subject_manage subject ON aggre.cost_subject_id = subject.cost_subject_id
        LEFT JOIN u_cost_aggre_original_order original ON aggre.aggre_original_order_id = original.aggre_original_order_id
        <where>
            <if test="reqVO.relatedAggreId != null">
                AND aggre.related_aggre_id = #{reqVO.relatedAggreId}
            </if>
            <if test="reqVO.aggreType != null">
                AND aggre.aggre_type = #{reqVO.aggreType}
            </if>
            <if test="reqVO.aggreCode != null and reqVO.aggreCode != ''">
                AND aggre.aggre_code LIKE CONCAT('%', #{reqVO.aggreCode}, '%')
            </if>
            <if test="reqVO.aggreName != null and reqVO.aggreName != ''">
                AND aggre.aggre_name LIKE CONCAT('%', #{reqVO.aggreName}, '%')
            </if>
            <if test="reqVO.costSubjectName != null and reqVO.costSubjectName != ''">
                AND subject.cost_subject_name LIKE CONCAT('%', #{reqVO.costSubjectName}, '%')
            </if>
            <if test="reqVO.costUsage != null">
                AND aggre.cost_usage = #{reqVO.costUsage}
            </if>
            <if test="reqVO.dataStatus != null">
                AND aggre.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND aggre.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND aggre.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND aggre.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY aggre.aggre_id DESC
    </select>
</mapper>
