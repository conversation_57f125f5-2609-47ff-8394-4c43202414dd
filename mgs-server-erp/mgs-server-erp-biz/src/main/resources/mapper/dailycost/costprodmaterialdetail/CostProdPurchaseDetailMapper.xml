<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.dailycost.dal.mysql.costprodmaterialdetail.CostProdMaterialDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <delete id="deleteByCostProdMaterialId">
        DELETE FROM u_cost_prod_material_detail
        WHERE cost_prod_material_id = #{costProdMaterialId}
    </delete>

    <select id="getPrice"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">
        WITH OriginalInfo AS (
            SELECT DISTINCT
                detail.cost_prod_material_detail_id AS id,
                prod.reported_dt AS dateTime,
                CAST(prod.reported_dt AS DATE) AS statisDate,
                prod.price AS price,
                prod.tenant_id
            FROM erp.u_cost_prod_material_detail detail
            LEFT JOIN erp.u_cost_prod_material prod
            ON detail.cost_prod_material_id = prod.cost_prod_material_id
            WHERE detail.undertake_org_id = #{reqVO.undertakeOrgId}
              AND prod.undertake_material_id = #{reqVO.undertakeMaterialId}
              AND prod.reported_dt BETWEEN #{reqVO.startDt} AND #{reqVO.endDt}
        ),
        LatestRecords AS (
        SELECT
             statisDate,
             price,
             dateTime,
             ROW_NUMBER() OVER (PARTITION BY statisDate ORDER BY dateTime DESC) AS rn,
             tenant_id
         FROM OriginalInfo
        )
        SELECT
            lr.statisDate,
            COALESCE(lr.price, 0) AS price  -- 将NULL替换为0
        FROM LatestRecords lr
        WHERE lr.rn = 1
        ORDER BY lr.statisDate;
    </select>
<!--    <select id="getPrice"-->
<!--            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">-->
<!--        SELECT DISTINCT detail.cost_prod_material_detail_id AS id-->
<!--                      , detail.form_dt AS dateTime-->
<!--                      , CAST(detail.form_dt AS DATE) AS statisDate-->
<!--                      , prod.price AS price-->
<!--                      , prod.reported_qty AS reportedQty-->
<!--        FROM erp.u_cost_prod_material_detail detail-->
<!--        LEFT JOIN erp.u_cost_prod_material prod ON detail.cost_prod_material_id = prod.cost_prod_material_id-->
<!--        LEFT JOIN erp.u_cost_spu_config spu ON spu.related_up_order_id = prod.reported_work_id-->
<!--        WHERE detail.undertake_org_id = #{reqVO.undertakeOrgId}-->
<!--          AND spu.spu_id = #{reqVO.spuId}-->
<!--          AND detail.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt}-->
<!--    </select>-->
    <select id="getReportedQty"
            resultType="com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO">
        WITH OriginalInfo AS (
            SELECT
                prod.reported_dt AS dateTime,
                CAST(prod.reported_dt AS DATE) AS statisDate,
                prod.reported_qty AS reportedQty,
                prod.tenant_id
            FROM erp.u_cost_prod_material prod
            WHERE prod.undertake_org_id = #{reqVO.undertakeOrgId}
              AND prod.undertake_material_id = #{reqVO.undertakeMaterialId}
              AND prod.reported_dt BETWEEN #{reqVO.startDt} AND #{reqVO.endDt}
        )
        SELECT
            statisDate,
            SUM(reportedQty) AS reportedQty,
            tenant_id
        FROM OriginalInfo
        GROUP BY statisDate, tenant_id
    </select>

    <update id="costProdPurchaseIdEdit">
        UPDATE u_cost_prod_purchase_detail SET total_amt = #{reqVO.totalAmt}
        WHERE cost_prod_purchase_id = #{reqVO.costProdPurchaseId}
    </update>

</mapper>
