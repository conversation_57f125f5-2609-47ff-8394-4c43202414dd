<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryErpSaleOrderDetailPage"
            resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderDetailResp">

        SELECT detail.sale_order_id, detail.row_no,detail.related_row_no,detail.material_id,detail.main_unit_dict_id, detail.qty,
               detail.excl_tax_price, detail.excl_tax_amt, detail.invoice_type_id as detail_invoice_type_id,
               detail.invoice_type_name as detail_invoice_type_name, detail.tax_rate as detailTaxRate, detail.incl_tax_price,
               detail.incl_tax_amt, detail.out_qty AS outQty,detail.notified_qty as noticeQty,detail.returned_qty as returnQty,
               detail.feed_qty as deductionQty,detail.exchange_qty as exchangeQty,detail.remark,
               detail.excl_tax_local_currency_amt, detail.incl_tax_local_currency_amt,
               sale.*
        FROM u_erp_sale_order sale
        INNER JOIN u_erp_sale_order_detail detail on sale.sale_order_id=detail.sale_order_id
        <where> sale.sale_chang_id is null
            <if test="reqVO.saleOrderId != null">
                AND sale.sale_order_id = #{reqVO.saleOrderId}
            </if>
            <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
                AND sale.sale_order_code LIKE concat('%', #{reqVO.saleOrderCode}, '%')
            </if>
            <if test="reqVO.salesOrderTypeDictId != null and reqVO.salesOrderTypeDictId != ''">
                AND sale.sales_order_type_dict_id = #{reqVO.salesOrderTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND sale.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.customerId != null">
                AND sale.customer_id = #{reqVO.customerId}
            </if>
            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
                AND sale.company_org_id = #{reqVO.companyOrgId}
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                AND sale.currency_dict_id = #{reqVO.currencyDictId}
            </if>
            <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
                AND sale.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
            </if>
            <if test="reqVO.paymentTermsDictId != null and reqVO.paymentTermsDictId != ''">
                AND sale.payment_terms_dict_id = #{reqVO.paymentTermsDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND sale.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.outboundStatus != null">
                AND sale.outbound_status = #{reqVO.outboundStatus}
            </if>
            <if test="reqVO.isForceClose != null">
                AND sale.is_force_close = #{reqVO.isForceClose}
            </if>
            <if test="reqVO.directorId != null">
                AND sale.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND sale.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.saleOrderDetailId != null">
                AND detail.sale_order_detail_id = #{reqVO.saleOrderDetailId}
            </if>
            <if test="reqVO.materialId != null">
                AND detail.material_id = #{reqVO.materialId}
            </if>
            <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
                AND detail.material_id IN
                <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND sale.form_dt >= #{reqVO.startFormDt}
                AND sale.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
                AND sale.delivery_date >= #{reqVO.startDeliveryDate}
                AND sale.delivery_date &lt;= #{reqVO.endDeliveryDate}
            </if>
            <if test="reqVO.formStatus != null">
                AND sale.form_status = #{reqVO.formStatus}
            </if>
        </where>
        order by sale.created_dt desc,detail.row_no asc
    </select>

    <select id="selectSaleForDemandPage"
            resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderRespVO">
        SELECT DISTINCT sale.* FROM u_erp_sale_order sale
        LEFT JOIN u_erp_sale_order_detail detail ON detail.sale_order_id = sale.sale_order_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        LEFT JOIN platform.t_dict dict ON dict.dict_obj_code ='SERP101' AND dict.dict_code = TO_CHAR(material.material_source_dict_id, 'FM99999')
        WHERE (dict.dict_name = '外购' OR material.material_source_dict_id = 2)
        AND sale.data_status = 1
        AND sale.sale_chang_id IS NULL
        <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
            AND sale.sale_order_code LIKE concat('%', #{reqVO.saleOrderCode}, '%')
        </if>
        <if test="reqVO.salesOrderTypeDictId != null and reqVO.salesOrderTypeDictId != ''">
            AND sale.sales_order_type_dict_id = #{reqVO.salesOrderTypeDictId}
        </if>
        <if test="reqVO.customerId != null">
            AND sale.customer_id = #{reqVO.customerId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
            AND sale.delivery_date >= #{reqVO.startDeliveryDate}
            AND sale.delivery_date &lt;= #{reqVO.endDeliveryDate}
        </if>
        <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
            AND sale.currency_dict_id = #{reqVO.currencyDictId}
        </if>
        <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
            AND sale.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
        </if>
        <if test="reqVO.paymentTermsDictId != null and reqVO.paymentTermsDictId != ''">
            AND sale.payment_terms_dict_id = #{reqVO.paymentTermsDictId}
        </if>
        <if test="reqVO.isCanIssueSalePur != null">
            AND sale.is_can_issue_sale_pur = #{reqVO.isCanIssueSalePur}
        </if>
        <if test="reqVO.formStatusList != null and reqVO.formStatusList.size > 0">
            AND sale.form_status IN
            <foreach collection="reqVO.formStatusList" index="index" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY sale.sale_order_id DESC
    </select>
    <select id="selectSaleForDemandList"
            resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderDetailResp">
        SELECT DISTINCT sale.* FROM u_erp_sale_order sale
        LEFT JOIN u_erp_sale_order_detail detail ON detail.sale_order_id = sale.sale_order_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        LEFT JOIN platform.t_dict dict ON dict.dict_obj_code ='SERP101' AND dict.dict_code = TO_CHAR(material.material_source_dict_id, 'FM99999')
        WHERE (dict.dict_name = '外购' OR material.material_source_dict_id = 2)
        AND sale.data_status = 1
        AND sale.sale_chang_id IS NULL
        <if test="reqVO.saleOrderIdList != null and reqVO.saleOrderIdList.size > 0">
            AND sale.sale_order_id IN
            <foreach collection="reqVO.saleOrderIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
            AND sale.sale_order_code LIKE concat('%', #{reqVO.saleOrderCode}, '%')
        </if>
        <if test="reqVO.isCanIssueSalePur != null ">
            AND sale.is_can_issue_sale_pur = #{reqVO.isCanIssueSalePur}
        </if>
        <if test="reqVO.salesOrderTypeDictId != null and reqVO.salesOrderTypeDictId != ''">
            AND sale.sales_order_type_dict_id = #{reqVO.salesOrderTypeDictId}
        </if>
        <if test="reqVO.customerId != null">
            AND sale.customer_id = #{reqVO.customerId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
            AND sale.delivery_date >= #{reqVO.startDeliveryDate}
            AND sale.delivery_date &lt;= #{reqVO.endDeliveryDate}
        </if>
        <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
            AND sale.currency_dict_id = #{reqVO.currencyDictId}
        </if>
        <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
            AND sale.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
        </if>
        <if test="reqVO.paymentTermsDictId != null and reqVO.paymentTermsDictId != ''">
            AND sale.payment_terms_dict_id = #{reqVO.paymentTermsDictId}
        </if>
        <if test="reqVO.formStatusList != null and reqVO.formStatusList.size > 0">
            AND sale.form_status IN
            <foreach collection="reqVO.formStatusList" index="index" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY sale.sale_order_id DESC
    </select>



    <select id="selectBy" resultType="com.mongoso.mgs.module.ai.controller.admin.finance.vo.AccountBalanceVOAI4">
        SELECT
            t1.sale_order_id,t1.sale_order_code,t1.total_amt,t1.form_dt,t1.total_amt,
            t2.material_id,t2.incl_tax_amt
        FROM u_erp_sale_order t1
        LEFT JOIN u_erp_sale_order_detail t2 ON t1.sale_order_id = t2.sale_order_id
        WHERE t1.data_status = 1 AND t1.is_force_close = 0 AND t1.outbound_status in (0,1)
    </select>

</mapper>
