<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.salededuction.SaleDeductionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="querySaleDeductionDetailPage"
            resultType="com.mongoso.mgs.module.sale.controller.admin.salededuction.vo.SaleDeductionDetailResp">
        SELECT detail.remark, detail.deduction_order_id, detail.row_no, detail.related_row_no,detail.material_id,detail.main_unit_dict_id,
               detail.deduction_qty, detail.excl_tax_price, detail.excl_tax_amt,detail.invoice_type_id as detail_invoice_type_id,
               detail.invoice_type_name as detail_invoice_type_name, detail.tax_rate as detailTaxRate, detail.incl_tax_price, detail.incl_tax_amt,
               detail.excl_tax_local_currency_amt, detail.incl_tax_local_currency_amt,
               sale.*
        FROM u_sale_deduction sale
        INNER JOIN u_sale_deduction_detail detail on sale.deduction_order_id=detail.deduction_order_id
        <where>
            <if test="reqVO.deductionOrderCode != null and reqVO.deductionOrderCode != ''">
                AND sale.deduction_order_code LIKE concat('%', #{reqVO.deductionOrderCode}, '%')
            </if>
            <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
                AND sale.sale_order_code LIKE concat('%', #{reqVO.saleOrderCode}, '%')
            </if>
            <if test="reqVO.salesOrderTypeDictId != null and reqVO.salesOrderTypeDictId != ''">
                AND sale.sales_order_type_dict_id = #{reqVO.salesOrderTypeDictId}
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                AND sale.currency_dict_id = #{reqVO.currencyDictId}
            </if>
            <if test="reqVO.customerId != null">
                AND sale.customer_id = #{reqVO.customerId}
            </if>
            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
                AND sale.company_org_id = #{reqVO.companyOrgId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND sale.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND sale.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND sale.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.materialId != null">
                AND detail.material_id = #{reqVO.materialId}
            </if>
            <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
                AND sale.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
            </if>
            <if test="reqVO.refunConditionDictId != null and reqVO.refunConditionDictId != ''">
                AND sale.refun_condition_dict_id = #{reqVO.refunConditionDictId}
            </if>
            <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
                AND detail.material_id IN
                <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
                AND sale.delivery_date >= #{reqVO.startDeliveryDate}
                AND sale.delivery_date &lt;= #{reqVO.endDeliveryDate}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND sale.form_dt >= #{reqVO.startFormDt}
                AND sale.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        order by sale.created_dt desc,detail.row_no asc
    </select>
</mapper>
