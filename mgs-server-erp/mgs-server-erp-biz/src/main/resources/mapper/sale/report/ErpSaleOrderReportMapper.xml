<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.report.ErpSaleOrderReportMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="findErpSaleOrderDetail"
            resultType="com.mongoso.mgs.module.sale.controller.admin.report.vo.ErpSaleOrderReportRespVO">
        select sale.sale_order_id,
               sale.sale_order_code,
               sale.sales_order_type_dict_id,
               sale.customer_id,
               sale.form_dt,
               sale.delivery_date,
               sale.total_amt                as totalAmt,
               sum(detail.qty)               as saleTotalQty,
               sale.outbound_status,
               sum(detail.notified_qty)      as noticeQty,
               sum(detail.out_qty)           as outboundedQty,
               sale.sales_order_type_dict_id as salesOrderTypeDictId,
               customer.customer_name        AS customerName
        from "erp".u_erp_sale_order sale
                 right join "erp".u_erp_sale_order_detail detail on sale.sale_order_id = detail.sale_order_id
                 left join "erp".u_customer customer on customer.customer_id = sale.customer_id
        <where>
            sale.sale_chang_id is null
              and sale.data_status = 1
            <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
                AND sale.sale_order_code LIKE concat('%', #{reqVO.saleOrderCode}, '%')
            </if>
            <if test="reqVO.salesOrderTypeDictId != null and reqVO.salesOrderTypeDictId != ''">
                AND sale.sales_order_type_dict_id = #{reqVO.salesOrderTypeDictId}
            </if>
            <if test="reqVO.customerId != null">
                AND sale.customer_id = #{reqVO.customerId}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
                AND sale.delivery_date >= #{reqVO.startDeliveryDate}
                AND sale.delivery_date &lt;= #{reqVO.endDeliveryDate}
            </if>
        </where>
        group by sale.sale_order_id, sale.sale_order_code, sale.sales_order_type_dict_id, sale.customer_id,
                 sale.form_dt,
                 sale.delivery_date, sale.total_amt, sale.sales_order_type_dict_id, sale.outbound_status, sale.created_dt,
                 customer.customer_name
        order by sale.created_dt desc
    </select>


    <select id="queryPurchaseOrderIdList" resultType="java.lang.String">
        SELECT purchase.purchase_order_id
        FROM "u_erp_sale_order" saleOrder
                 inner join u_purchase_demand demand on saleOrder.sale_order_id = demand.related_order_id
                 INNER JOIN u_purchase_order purchase ON demand.purchase_demand_id = purchase.related_order_id
        <where>
            and purchase.data_status=1
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>

    <select id="queryInboundIdByProdOutPurchaseList" resultType="java.lang.String">
        SELECT inbound.inbound_id
        FROM "u_erp_sale_order" saleOrder
                 INNER JOIN u_purchase_order purchase ON saleOrder.sale_order_id = purchase.related_order_id
                 INNER JOIN u_inbound inbound ON inbound.purchase_order_id = purchase.purchase_order_id
        <where>
            and saleOrder.is_can_issue_pro = 0
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>
    <select id="queryInboundIdByDemandList" resultType="java.lang.String">
        SELECT inbound.inbound_id
        FROM "u_erp_sale_order" saleOrder
        INNER JOIN u_purchase_demand demand on saleOrder.sale_order_id = demand.related_order_id
        INNER JOIN u_purchase_order purchase ON demand.purchase_demand_id = purchase.related_order_id
        INNER JOIN u_inbound inbound ON inbound.purchase_order_id = purchase.purchase_order_id
        <where>
            and inbound.data_status=1
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>
    <select id="queryInboundIdByPurchaseList" resultType="java.lang.String">
        SELECT inbound.inbound_id
        FROM "u_erp_sale_order" saleOrder
        INNER JOIN u_purchase_order purchase ON saleOrder.sale_order_id = purchase.related_order_id
        INNER JOIN u_inbound inbound ON inbound.purchase_order_id = purchase.purchase_order_id
        <where>
            and inbound.data_status=1
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>

    <select id="queryInboundIdByProdList" resultType="java.lang.String">
        SELECT inbound.inbound_id
        FROM "u_erp_sale_order" saleOrder
                 INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
                 INNER JOIN u_prod_work word ON prod.prod_order_id = word.prod_order_id
                 INNER JOIN u_inbound inbound ON inbound.related_order_id = word.prod_work_id
        <where>
            and inbound.data_status = 1
                  and saleOrder.is_can_issue_pro = 0
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>

    <!-- 查询销售订单关联的所有生产入库单（包括直接关联和物料分析关联） -->
    <select id="queryAllInboundIdByProdList" resultType="java.lang.String">
        SELECT DISTINCT inbound.inbound_id
        FROM (
            -- 直接关联的生产入库单
            SELECT inbound.inbound_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
                     INNER JOIN u_prod_work word ON prod.prod_order_id = word.prod_order_id
                     INNER JOIN u_inbound inbound ON inbound.related_order_id = word.prod_work_id
            WHERE inbound.data_status = 1
              AND saleOrder.is_can_issue_pro = 0
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>

            UNION

            -- 通过物料分析关联的生产入库单
            SELECT inbound.inbound_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_material_analysis ma ON saleOrder.sale_order_id = ma.related_order_id
                     INNER JOIN u_erp_prod_order prod ON ma.material_analysis_id = prod.related_order_id
                     INNER JOIN u_prod_work word ON prod.prod_order_id = word.prod_order_id
                     INNER JOIN u_inbound inbound ON inbound.related_order_id = word.prod_work_id
            WHERE inbound.data_status = 1
              AND ma.data_status = 1
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>
        ) inbound
        ORDER BY inbound.inbound_id
    </select>

    <select id="queryProdWorkIdList"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkRespVO">
        SELECT prodWork.prod_work_id,
               prodWork.out_process_config_dict_id,
               prodWork.in_process_config_dict_id
        FROM "u_erp_sale_order" saleOrder
                 INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
                 INNER JOIN u_prod_work prodWork ON prodWork.prod_order_id = prod.prod_order_id
        <where>
            and prodWork.data_status=1
            and saleOrder.is_can_issue_pro = 0
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>

    <!-- 查询销售订单关联的所有生产工单（包括直接关联和物料分析关联） -->
    <select id="queryAllProdWorkIdList"
            resultType="com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkRespVO">
        SELECT DISTINCT prodWork.prod_work_id,
               prodWork.out_process_config_dict_id,
               prodWork.in_process_config_dict_id
        FROM (
            -- 直接关联的生产工单
            SELECT prodWork.prod_work_id,
                   prodWork.out_process_config_dict_id,
                   prodWork.in_process_config_dict_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
                     INNER JOIN u_prod_work prodWork ON prodWork.prod_order_id = prod.prod_order_id
            WHERE prodWork.data_status = 1
              AND saleOrder.is_can_issue_pro = 0
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>

            UNION

            -- 通过物料分析关联的生产工单
            SELECT prodWork.prod_work_id,
                   prodWork.out_process_config_dict_id,
                   prodWork.in_process_config_dict_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_material_analysis ma ON saleOrder.sale_order_id = ma.related_order_id
                     INNER JOIN u_erp_prod_order prod ON ma.material_analysis_id = prod.related_order_id
                     INNER JOIN u_prod_work prodWork ON prodWork.prod_order_id = prod.prod_order_id
            WHERE prodWork.data_status = 1
              AND ma.data_status = 1
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>
        ) prodWork
        ORDER BY prodWork.prod_work_id
    </select>

    <select id="queryOutboundIdList" resultType="java.lang.String">
        SELECT outbound.outbound_id
        FROM erp.u_work_picking pick
        INNER JOIN erp.u_outbound outbound ON outbound.related_order_id = pick.work_picking_id
        WHERE outbound.data_status=1 and pick.related_order_id IN
        <foreach item="item" collection="prodWorkIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryInboundIdList" resultType="java.lang.String">
        SELECT inbound.inbound_id
        FROM erp.u_work_picking_return pick
        INNER JOIN erp.u_inbound inbound ON inbound.related_order_id = pick.work_picking_return_id
        WHERE inbound.data_status=1 and  pick.related_order_id IN
        <foreach item="item" collection="prodWorkIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryPurchaseIdByOutsourceList" resultType="java.lang.String">
        SELECT purchase.purchase_order_id
        FROM "u_erp_sale_order" saleOrder
        INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
        INNER JOIN u_purchase_order purchase ON prod.prod_order_id = purchase.related_order_id
        <where>
            and purchase.data_status=1
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>

    <!-- 查询销售订单关联的所有委外采购订单（包括直接关联和物料分析关联） -->
    <select id="queryAllPurchaseIdByOutsourceList" resultType="java.lang.String">
        SELECT DISTINCT purchase.purchase_order_id
        FROM (
            -- 直接关联的委外采购订单
            SELECT purchase.purchase_order_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
                     INNER JOIN u_purchase_order purchase ON prod.prod_order_id = purchase.related_order_id
            WHERE purchase.data_status = 1
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>

            UNION

            -- 通过物料分析关联的委外采购订单
            SELECT purchase.purchase_order_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_material_analysis ma ON saleOrder.sale_order_id = ma.related_order_id
                     INNER JOIN u_erp_prod_order prod ON ma.material_analysis_id = prod.related_order_id
                     INNER JOIN u_purchase_order purchase ON prod.prod_order_id = purchase.related_order_id
            WHERE purchase.data_status = 1
              AND ma.data_status = 1
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>
        ) purchase
        ORDER BY purchase.purchase_order_id
    </select>

    <select id="queryInboundIdByOutsourceList" resultType="java.lang.String">
        SELECT inbound.inbound_id
        FROM "u_erp_sale_order" saleOrder
        INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
        INNER JOIN u_purchase_order purchase ON prod.prod_order_id = purchase.related_order_id
        INNER JOIN u_inbound inbound ON inbound.purchase_order_id = purchase.purchase_order_id
        <where>
            and inbound.data_status=1
            and saleOrder.is_can_issue_pro = 0
            <if test="saleOrderId != null">
                AND saleOrder.sale_order_id = #{saleOrderId}
            </if>
        </where>
    </select>

    <!-- 查询销售订单关联的所有委外入库单（包括直接关联和物料分析关联） -->
    <select id="queryAllInboundIdByOutsourceList" resultType="java.lang.String">
        SELECT DISTINCT inbound.inbound_id
        FROM (
            -- 直接关联的委外入库单
            SELECT inbound.inbound_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_erp_prod_order prod ON saleOrder.sale_order_id = prod.related_order_id
                     INNER JOIN u_purchase_order purchase ON prod.prod_order_id = purchase.related_order_id
                     INNER JOIN u_inbound inbound ON inbound.purchase_order_id = purchase.purchase_order_id
            WHERE inbound.data_status = 1
              AND saleOrder.is_can_issue_pro = 0
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>

            UNION

            -- 通过物料分析关联的委外入库单
            SELECT inbound.inbound_id
            FROM "u_erp_sale_order" saleOrder
                     INNER JOIN u_material_analysis ma ON saleOrder.sale_order_id = ma.related_order_id
                     INNER JOIN u_erp_prod_order prod ON ma.material_analysis_id = prod.related_order_id
                     INNER JOIN u_purchase_order purchase ON prod.prod_order_id = purchase.related_order_id
                     INNER JOIN u_inbound inbound ON inbound.purchase_order_id = purchase.purchase_order_id
            WHERE inbound.data_status = 1
              AND ma.data_status = 1
              <if test="saleOrderId != null">
                  AND saleOrder.sale_order_id = #{saleOrderId}
              </if>
        ) inbound
        ORDER BY inbound.inbound_id
    </select>
</mapper>
