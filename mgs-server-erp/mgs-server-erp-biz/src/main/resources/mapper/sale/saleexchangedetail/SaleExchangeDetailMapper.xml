<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.saleexchangedetail.SaleExchangeDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectSaleExchangeDetailPage" resultType="com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO">
        SELECT a.*, b.sale_exchange_code, b.sale_order_id, b.sale_order_code, b.sales_order_type_dict_id, b.related_customer_id, b.delivery_date, b.exchange_date,
               b.exchange_code_type, b.director_id, b.director_org_id, b.approved_by, b.approved_dt, b.data_status, b.form_dt, m.material_name, m.material_category_dict_id,
               m.spec_model, m.spec_attribute_str, m.main_unit_dict_id
        FROM u_sale_exchange b
        JOIN u_sale_exchange_detail a ON a.sale_exchange_id = b.sale_exchange_id
        LEFT JOIN u_material m on m.material_id = a.material_id AND m.deleted = 0
        <where>
            <if test="reqVO.saleExchangeCode != null and reqVO.saleExchangeCode != ''">
                AND b.sale_exchange_code LIKE CONCAT('%', #{reqVO.saleExchangeCode}, '%')
            </if>
            <if test="reqVO.saleOrderCode != null and reqVO.saleOrderCode != ''">
                AND b.sale_order_code LIKE CONCAT('%', #{reqVO.saleOrderCode}, '%')
            </if>
            <if test="reqVO.salesOrderTypeDictId != null and reqVO.salesOrderTypeDictId != ''">
                AND b.sales_order_type_dict_id = #{reqVO.salesOrderTypeDictId}
            </if>
            <if test="reqVO.relatedCustomerId != null">
                AND b.related_customer_id = #{reqVO.relatedCustomerId}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
                AND b.delivery_date >= #{reqVO.startDeliveryDate}
                AND b.delivery_date &lt;= #{reqVO.endDeliveryDate}
            </if>
            <if test="reqVO.startExchangeDate != null and reqVO.endExchangeDate != null ">
                AND b.exchange_date >= #{reqVO.startExchangeDate}
                AND b.exchange_date &lt;= #{reqVO.endExchangeDate}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND a.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND m.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND m.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND m.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND b.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND b.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND b.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND b.form_dt >= #{reqVO.startFormDt}
                AND b.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        order by b.created_dt desc,a.row_no ASC
    </select>

    <select id="selectSaleExchangeDetailList" resultType="com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO">
        SELECT a.*, b.sale_exchange_code, b.sale_order_id, b.sale_order_code, b.sales_order_type_dict_id, b.related_customer_id, b.delivery_date, b.exchange_date,
        b.exchange_code_type, b.director_id, b.director_org_id, b.approved_by, b.approved_dt, b.data_status, b.form_dt, m.material_name, m.material_category_dict_id,
        m.spec_model, m.spec_attribute_str, m.main_unit_dict_id
        FROM u_sale_exchange_detail a
        LEFT JOIN u_sale_exchange b ON a.sale_exchange_id = b.sale_exchange_id
        LEFT JOIN u_material m on m.material_id = a.material_id AND m.deleted = 0
        <where>
            <if test="reqVO.saleExchangeId != null">
                AND a.sale_exchange_id = #{reqVO.saleExchangeId}
            </if>
            <if test="reqVO.isMaterialFullInbounded != null">
                AND a.is_material_full_inbounded = #{reqVO.isMaterialFullInbounded}
            </if>
            <if test="reqVO.isExchangeMaterialFullOutbounded != null">
                AND a.is_exchange_material_full_outbounded = #{reqVO.isExchangeMaterialFullOutbounded}
            </if>
            <if test="reqVO.dataStatus != null">
                AND b.data_status = #{reqVO.dataStatus}
            </if>
        </where>
        order by b.created_dt desc,a.row_no ASC
    </select>


    <select id="sumQty" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        select detail.material_id as fieldId,m.material_code as code,sum(detail.exchange_qty) as sumQty
        from u_sale_exchange sale
        left join u_sale_exchange_detail detail on sale.sale_exchange_id = detail.sale_exchange_id
        left join u_material m on m.material_id = detail.material_id AND m.deleted = 0
        where sale.data_status =1
        <if test="reqVO.saleOrderId!=null">
            and sale.sale_order_id = #{reqVO.saleOrderId}
        </if>
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0 ">
            AND detail.material_id not IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.saleOrderIdList != null and reqVO.saleOrderIdList.size > 0 ">
            AND sale.sale_order_id IN
            <foreach item="item" index="index" collection="reqVO.saleOrderIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.saleExchangeId!=null">
            or sale.sale_exchange_id = #{reqVO.saleExchangeId}
        </if>
        group by detail.material_id,m.material_code

    </select>

    <select id="inboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.sale_exchange_detail_id as fieldId, m.material_code as code, detail.material_id as materialId, detail.inboundable_qty as sumQty
        FROM u_sale_exchange_detail detail
                 LEFT JOIN u_material m on m.material_id = detail.material_id AND m.deleted = 0
        WHERE detail.sale_exchange_id = #{saleExchangeId}
    </select>

    <select id="outboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.sale_exchange_detail_id as fieldId, m.material_code as code, detail.material_id as materialId, detail.outboundable_qty as sumQty
        FROM u_sale_exchange_detail detail
                 LEFT JOIN u_material m on m.material_id = detail.material_id AND m.deleted = 0
        WHERE detail.sale_exchange_id = #{saleExchangeId}
    </select>

    <select id="outboundDetailQuotedList" resultType="com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO">
        SELECT detail.*, stock.warehouse_org_id, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty,
               b.sale_exchange_code, b.sale_order_id, b.sale_order_code, b.sales_order_type_dict_id, b.related_customer_id, b.delivery_date, b.exchange_date,
               b.exchange_code_type, b.director_id, b.director_org_id, b.approved_by, b.approved_dt, b.data_status, b.form_dt, m.material_name, m.material_category_dict_id,
               m.spec_model, m.spec_attribute_str, m.main_unit_dict_id
        FROM u_sale_exchange_detail detail
                 LEFT JOIN u_sale_exchange b ON detail.sale_exchange_id = b.sale_exchange_id
                 LEFT JOIN u_material m on m.material_id = detail.material_id AND m.deleted = 0
                 LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id
        WHERE detail.sale_exchange_id = #{saleExchangeId}
          AND detail.is_exchange_material_full_outbounded = 0
    </select>

    <!-- 批量更新销售换货明细的可换货数量 -->
    <update id="batchUpdateExchangeableQtyByMaterialId">
        <foreach collection="updateParams" item="updateBO" separator=";">
            UPDATE u_sale_exchange_detail
            SET can_exchange_qty = can_exchange_qty - #{updateBO.exchangedQty}
            WHERE material_id = #{updateBO.materialId}
        </foreach>
    </update>

</mapper>

