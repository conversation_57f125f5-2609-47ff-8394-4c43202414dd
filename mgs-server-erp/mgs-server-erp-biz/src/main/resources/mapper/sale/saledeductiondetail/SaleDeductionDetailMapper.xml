<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.saledeductiondetail.SaleDeductionDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="sumQty" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        select detail.material_id as fieldId,m.material_code as code,sum(detail.deduction_qty) as sumQty
        from u_sale_deduction sale
             left join u_sale_deduction_detail detail on sale.deduction_order_id = detail.deduction_order_id
             left join u_material m on m.material_id = detail.material_id AND m.deleted = 0
        where sale.data_status =1
        <if test="reqVO.saleOrderId!=null">
            and sale.sale_order_id = #{reqVO.saleOrderId}
        </if>
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0 ">
            AND detail.material_id not IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.saleOrderIdList != null and reqVO.saleOrderIdList.size > 0 ">
            AND sale.sale_order_id IN
            <foreach item="item" index="index" collection="reqVO.saleOrderIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.deductionOrderId!=null">
           or sale.deduction_order_id = #{reqVO.deductionOrderId}
        </if>
        group by detail.material_id,m.material_code

    </select>

</mapper>
