<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper">


    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="sumQty" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        select detail.material_id as fieldId,sum(detail.qty) as sumQty
        from u_erp_sale_order sale
             left join u_erp_sale_order_detail detail on sale.sale_order_id = detail.sale_order_id
        where sale.data_status =1
        <if test="reqVO.saleOrderId!=null">
            and sale.sale_order_id = #{reqVO.saleOrderId}
        </if>
        <if test="reqVO.relatedOrderId!=null">
            and sale.related_order_id = #{reqVO.relatedOrderId}
        </if>
        group by detail.material_id
    </select>

    <select id="selectSaleDetailForDemand"
            resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderDetailResp"
            parameterType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailQueryReqVO">
        SELECT detail.*, detail.row_no AS relatedRowNo, material.material_code, material.material_name, material.spec_model, material.spec_attribute_str
             , dict2.dict_name AS mainUnitDictName, dict3.dict_name AS materialCategoryDictName
        FROM u_erp_sale_order_detail detail
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        LEFT JOIN platform.t_dict dict ON dict.dict_obj_code ='SERP101' and dict.dict_code = TO_CHAR(material.material_source_dict_id, 'FM99999')
        LEFT JOIN platform.t_dict dict2 ON dict2.dict_obj_code ='ERP108' and dict2.dict_code = detail.main_unit_dict_id
        LEFT JOIN platform.t_dict dict3 ON dict3.dict_obj_code ='ERP107' and dict3.dict_code = material.material_category_dict_id
        WHERE (dict.dict_name = '外购' OR material.material_source_dict_id = 2)
          AND detail.plan_able_demand_qty > 0
          AND detail.sale_order_id = #{reqVO.saleOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
            </foreach>
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="outboundDetailQuotedList" resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO">
        SELECT detail.*, stock.warehouse_org_id, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty
        FROM u_erp_sale_order_detail detail
        LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id
        WHERE detail.sale_order_id = #{saleOrderId}
        ORDER BY detail.row_no ASC
    </select>

    <select id="outboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.sale_order_detail_id as fieldId, material.material_code as code, (detail.qty - detail.out_qty) as sumQty
        FROM u_erp_sale_order_detail detail
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE detail.sale_order_id = #{saleOrderId}
    </select>

    <select id="completeableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.sale_order_detail_id as fieldId, detail.material_id, m.material_code as code,
            (detail.out_qty - returned_qty - feed_qty- exchange_qty + exchange_inbound_qty) as sumQty
        FROM u_erp_sale_order_detail detail
        LEFT JOIN u_material m on m.material_id = detail.material_id AND m.deleted = 0
        WHERE detail.sale_order_id = #{saleOrderId}
    </select>


    <select id="getSaleOrderStat" resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderStatRespVO">
        SELECT SUM(qty) totalSaleQty, SUM(out_qty) totalOutboundedQty
        FROM u_erp_sale_order_detail detail
        WHERE detail.sale_order_id = #{saleOrderId}
    </select>

    <select id="selectMaterialIdList" resultType="java.lang.Long">
        select detail.material_id
        from u_erp_sale_order sale
        left join u_erp_sale_order_detail detail on sale.sale_order_id = detail.sale_order_id
        where sale.data_status =1
        <if test="saleOrderId!=null">
            and sale.sale_order_id = #{saleOrderId}
        </if>
    </select>

    <select id="selectDetailWithMaterial" resultType="com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO">
        SELECT
            detail.*,
            material.material_code,
            material.material_name,
            material.spec_model
        FROM
            u_erp_sale_order_detail detail
        LEFT JOIN
            u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE
            detail.sale_order_id = #{saleOrderId}
        ORDER BY
            detail.row_no ASC
    </select>

</mapper>
