<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.shipnoticedetail.SaleShipNoticeDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="sumQty" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        select detail.material_id as fieldId,m.material_code as code,sum(detail.notice_qty) as sumQty
        from u_sale_ship_notice sale
             left join u_sale_ship_notice_detail detail on sale.ship_notice_id = detail.ship_notice_id
             left join u_material m on m.material_id = detail.material_id AND m.deleted = 0
        where sale.data_status = 1
        <if test="reqVO.saleOrderId!=null">
            and sale.sale_order_id = #{reqVO.saleOrderId}
        </if>
        <if test="reqVO.shipNoticeId!=null">
            or sale.ship_notice_id = #{reqVO.shipNoticeId}
        </if>
        <if test="reqVO.confirmStatus!=null">
            and sale.confirm_status = #{reqVO.confirmStatus}
        </if>
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0 ">
            AND detail.material_id not IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.saleOrderIdList != null and reqVO.saleOrderIdList.size > 0 ">
            AND sale.sale_order_id IN
            <foreach item="item" index="index" collection="reqVO.saleOrderIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by detail.material_id,m.material_code
    </select>

    <select id="outboundDetailQuotedList" resultType="com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo.ShipNoticeDetailRespVO">
        SELECT detail.*, stock.warehouse_org_id, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty
        FROM u_sale_ship_notice_detail detail
        LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id
        WHERE detail.ship_notice_id = #{shipNoticeId}
        AND detail.is_material_full_outbounded = 0
    </select>


    <select id="outboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.ship_notice_detail_id as fieldId, material.material_code as code, outboundable_qty as sumQty
        FROM u_sale_ship_notice_detail detail
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE detail.ship_notice_id = #{shipNoticeId}
    </select>
    <select id="queryList"
            resultType="com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo.ShipNoticeDetailRespVO">
        SELECT detail.*, SUM(outdetail.delivered_qty) deliveredQty
        FROM erp.u_sale_ship_notice_detail detail
        LEFT JOIN erp.u_outbound outbound ON detail.ship_notice_detail_id = outbound.related_order_id
        LEFT JOIN erp.u_outbound_detail outdetail ON outbound.outbound_id = outdetail.outbound_id
        WHERE detail.ship_notice_id = #{shipNoticeId}
        GROUP BY detail.material_id
    </select>

</mapper>
