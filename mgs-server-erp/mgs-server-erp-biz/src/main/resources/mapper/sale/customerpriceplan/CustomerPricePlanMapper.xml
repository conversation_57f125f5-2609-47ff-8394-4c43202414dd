<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.sale.dal.mysql.customerpriceplan.CustomerPricePlanMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

<!--    <resultMap id="customerPricePlanResultMap" type="com.mongoso.mgs.module.sale.dal.db.customerpriceplan.CustomerPricePlanDO">-->
<!--        <id property="pricePlanId" column="price_plan_id"/>-->
<!--        <result property="directorId" column="director_id"/>-->
<!--        <result property="directorOrgId" column="director_org_id"/>-->
<!--        <result property="baseInfo" column="base_info" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>-->
<!--        <result property="customerInfo" column="customer_info" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>-->
<!--        <result property="materialInfo" column="material_info" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>-->
<!--    </resultMap>-->


    <select id="selectListByCustomerId" resultType="com.mongoso.mgs.module.sale.dal.db.customerpriceplan.CustomerPricePlanDO">
        SELECT plan.*
        FROM u_customer_price_plan plan
        left join erp.u_cutomer_rel rel on plan.price_plan_id = rel.related_order_id
        WHERE plan.is_effective = '1' and plan.data_status = 1 and rel.customer_id = #{customerId}

    </select>

    <select id="selectListByMaterialId" resultType="com.mongoso.mgs.module.sale.dal.db.customerpriceplan.CustomerPricePlanDO">
        SELECT plan.*
        FROM u_customer_price_plan plan
        left join erp.u_material_rel price on plan.price_plan_id = price.related_order_id
        <where>
             plan.is_effective = '1' and plan.data_status = 1 and price.material_id = #{materialId}
            <if test="pricePlanIdList != null and pricePlanIdList.size > 0 ">
                AND plan.price_plan_id IN
                <foreach item="item" index="index" collection="pricePlanIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectNotApproveList"
            resultType="com.mongoso.mgs.module.sale.dal.db.customerpriceplan.CustomerPricePlanDO">
        SELECT price_plan_id,director_id, director_org_id, data_status,created_by, created_dt, updated_by, updated_dt, tenant_id
        FROM u_customer_price_plan
        where data_status != 0
        <if test="reqVO.pricePlanIdList != null and reqVO.pricePlanIdList.size > 0 ">
            AND price_plan_id IN
            <foreach item="item" index="index" collection="reqVO.pricePlanIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
