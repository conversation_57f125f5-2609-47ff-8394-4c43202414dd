<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailRespVO"
            parameterType="com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailQueryReqVO">
        SELECT purchasedet.*, demanddet.purchaseable_qty, demanddet.process_name
        FROM u_purchase_process_out_detail purchasedet
        LEFT JOIN u_process_out_demand demanddet ON purchasedet.process_out_demand_id = demanddet.process_out_demand_id
        WHERE purchasedet.purchase_process_out_id = #{reqVO.purchaseProcessOutId}
        <if test="reqVO.isMaterialFullReceipted != null ">
            AND purchasedet.is_material_full_receipted = #{reqVO.isMaterialFullReceipted}
        </if>
        <if test="reqVO.isMaterialFullOpered != null ">
            AND purchasedet.is_material_full_opered = #{reqVO.isMaterialFullOpered}
        </if>
        <if test="reqVO.exclDetailIdList != null and reqVO.exclDetailIdList.size > 0">
            AND purchasedet.process_out_detail_id NOT IN
            <foreach item="item" index="index" collection="reqVO.exclDetailIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by purchasedet.row_no ASC
    </select>
    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailRespVO">
        SELECT detail.*, purchase.*, material.material_name, demanddet.process_name
        FROM u_purchase_process_out_detail detail
        LEFT JOIN u_purchase_process_out purchase ON purchase.purchase_process_out_id = detail.purchase_process_out_id
        LEFT JOIN u_process_out_demand demanddet ON detail.process_out_demand_id = demanddet.process_out_demand_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.purchaseProcessOutCode != null and reqVO.purchaseProcessOutCode != ''">
                AND detail.purchase_process_out_code LIKE CONCAT('%', #{reqVO.purchaseProcessOutCode}, '%')
            </if>
            <if test="reqVO.relatedSupplierId != null">
                AND purchase.related_supplier_id = #{reqVO.relatedSupplierId}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
                AND (purchase.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                AND purchase.currency_dict_id = #{reqVO.currencyDictId}
            </if>
            <if test="reqVO.paymentTermsDictId != null and reqVO.paymentTermsDictId != ''">
                AND purchase.payment_terms_dict_id = #{reqVO.paymentTermsDictId}
            </if>
            <if test="reqVO.processOutDemandCode != null and reqVO.processOutDemandCode != ''">
                AND detail.process_out_demand_code LIKE CONCAT('%', #{reqVO.processOutDemandCode}, '%')
            </if>
            <if test="reqVO.prodOrderCode != null and reqVO.prodOrderCode != ''">
                AND detail.prod_order_code LIKE CONCAT('%', #{reqVO.prodOrderCode}, '%')
            </if>
            <if test="reqVO.prodWorkCode != null and reqVO.prodWorkCode != ''">
                AND detail.prod_work_code LIKE CONCAT('%', #{reqVO.prodWorkCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND detail.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.processName != null and reqVO.processName != ''">
                AND demanddet.process_name LIKE CONCAT('%', #{reqVO.processName}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND purchase.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND purchase.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND purchase.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (purchase.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
                AND purchase.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
            </if>
        </where>
        ORDER BY purchase.purchase_process_out_id DESC, detail.row_no ASC
    </select>
    <select id="getPurchaseIsApproveAble" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN demand.purchaseable_qty &lt; detail.purchase_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_process_out_demand demand
        LEFT JOIN u_purchase_process_out_detail detail ON detail.process_out_demand_id = demand.process_out_demand_id
        WHERE detail.purchase_process_out_id = #{item.purchaseProcessOutId}
    </select>
</mapper>
