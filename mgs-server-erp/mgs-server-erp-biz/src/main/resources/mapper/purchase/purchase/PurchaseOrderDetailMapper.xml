<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.purchase.detail.PurchaseOrderDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <update id="updateDemandDetailPurchaseQty">
        UPDATE u_purchase_demand_detail
        <if test="operate != null">
            SET purchase_able_qty = (u_purchase_demand_detail.purchase_able_qty - b.purchase_qty), purchased_qty = (u_purchase_demand_detail.purchased_qty + b.purchase_qty)
        </if>
        <if test="operate == null">
            SET purchase_able_qty = (u_purchase_demand_detail.purchase_able_qty + b.purchase_qty), purchased_qty = (u_purchase_demand_detail.purchased_qty - b.purchase_qty)
        </if>
        FROM u_purchase_order_detail b
        WHERE u_purchase_demand_detail.material_id = b.material_id
        AND b.purchase_order_id = #{item.purchaseOrderId}
        AND u_purchase_demand_detail.purchase_demand_id = #{item.relatedOrderId};
    </update>

    <update id="updateDemandDetailPurchaseQty4Change">
        UPDATE u_purchase_demand_detail
        SET purchase_able_qty = (u_purchase_demand_detail.purchase_able_qty + purchase.purchase_qty - change.purchase_qty),
            purchased_qty = (u_purchase_demand_detail.purchased_qty - purchase.purchase_qty + change.purchase_qty)
        FROM u_purchase_order_detail purchase
        LEFT JOIN u_purchase_change_detail change ON change.purchase_order_id = purchase.purchase_order_id
        WHERE u_purchase_demand_detail.material_id = purchase.material_id
        AND purchase.purchase_order_id = #{item.purchaseOrderId}
        AND u_purchase_demand_detail.purchase_demand_id = #{item.relatedOrderId}
        AND change.purchase_change_id = #{item.purchaseChangeId};
    </update>

    <select id="selectDemandDetailForPurchase"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailRespVO">
        SELECT detail.*, material.purchase_standard_price
        FROM u_purchase_demand_detail detail
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE detail.purchase_able_qty > 0
          AND detail.purchase_demand_id = #{reqVO.purchaseDemandId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY detail.row_no ASC
    </select>
    <select id="materialHistoryPricePage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*, supplier.supplier_name AS relatedSupplierName, purchase.purchase_type_dict_id, purchase.related_order_code,
               supplier.supplier_name AS relatedSupplierName, purchase.delivery_date, purchase.currency_dict_id,
               purchase.settlement_method_dict_id, purchase.payment_terms_dict_id, purchase.incl_tax_total_amt,
               purchase.director_org_id, purchase.form_dt, purchase.director_id, purchase.director_org_id, purchase.company_org_id
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON detail.purchase_order_id = purchase.purchase_order_id
        LEFT JOIN u_supplier supplier ON purchase.related_supplier_id = supplier.supplier_id AND supplier.deleted = 0
        WHERE purchase.related_supplier_id = #{reqVO.relatedSupplierId}
        AND detail.material_id = #{reqVO.materialId} AND purchase.data_status = 1
    </select>
    <select id="getPurchaseIsApproveAble" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.purchase_able_qty &lt; b.purchase_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_purchase_demand_detail a
                 LEFT JOIN u_purchase_order_detail b ON a.material_id = b.material_id
        WHERE b.purchase_order_id = #{item.purchaseOrderId}
          AND a.purchase_demand_id = #{item.relatedOrderId}
    </select>
    <select id="getPurchaseChangeIsApproveAbleByDemand" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.purchase_able_qty >= (COALESCE(b.purchase_qty, 0) - COALESCE(c.purchase_qty, 0)) THEN 0 ELSE 1 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_purchase_demand_detail a
        LEFT JOIN u_purchase_change_detail b ON a.material_id = b.material_id
        LEFT JOIN u_purchase_order_detail c ON c.purchase_order_id = b.purchase_order_id AND c.material_id = b.material_id
        WHERE b.purchase_change_id = #{item.purchaseChangeId}
          AND a.purchase_demand_id = #{item.relatedOrderId}
    </select>

    <select id="getPurchaseChangeIsApproveAbleByProd" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.purchasechange.PurchaseChangeDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.purchaseable_qty >= (COALESCE(b.purchase_qty, 0) - COALESCE(c.purchase_qty, 0)) THEN 0 ELSE 1 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_erp_prod_order_detail a
                 LEFT JOIN u_purchase_change_detail b ON a.material_id = b.material_id
                 LEFT JOIN u_purchase_order_detail c ON c.purchase_order_id = b.purchase_order_id AND c.material_id = b.material_id
        WHERE b.purchase_change_id = #{item.purchaseChangeId}
          AND a.prod_order_id = #{item.relatedOrderId}
    </select>

    <select id="getPurchaseChangeIsApproveAbleByAnalysis" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.purchasechange.PurchaseChangeDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.purchaseable_qty >= (COALESCE(b.purchase_qty, 0) - COALESCE(c.purchase_qty, 0)) THEN 0 ELSE 1 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_material_analysis_total a
                 LEFT JOIN u_purchase_change_detail b ON a.material_id = b.material_id
                 LEFT JOIN u_purchase_order_detail c ON c.purchase_order_id = b.purchase_order_id AND c.material_id = b.material_id
        WHERE b.purchase_change_id = #{item.purchaseChangeId}
          AND a.material_analysis_id = #{item.relatedOrderId}
    </select>

    <select id="getPurchaseIsApproveAbleByProd" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.purchaseable_qty &lt; b.purchase_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_erp_prod_order_detail a
        LEFT JOIN u_purchase_order_detail b ON a.material_id = b.material_id
        WHERE b.purchase_order_id = #{item.purchaseOrderId}
          AND a.prod_order_id = #{item.relatedOrderId}
    </select>

    <select id="getPurchaseIsApproveAbleByAnalysis" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN total.purchaseable_qty &lt; purchase.purchase_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_material_analysis_total total
        LEFT JOIN u_purchase_order_detail purchase ON total.material_id = purchase.material_id
        WHERE purchase.purchase_order_id = #{item.purchaseOrderId}
          AND total.material_analysis_id = #{item.relatedOrderId}
    </select>

    <select id="queryMaterialDetailPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT purchase.delivery_date purchaseDeliveryDate, purchase.inbound_status, purchase.local_currency_dict_id,
               detail.*, purchase.*, detail.delivery_date detailDeliveryDate, purchase.incl_tax_local_currency_total_amt
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON detail.purchase_order_id = purchase.purchase_order_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE detail.purchase_order_detail_id IS NOT NULL
        <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
            AND purchase.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
        </if>
        <if test="reqVO.purchaseOrderId != null and reqVO.purchaseOrderId != ''">
            AND purchase.purchase_order_id LIKE CONCAT('%', #{reqVO.purchaseOrderId}, '%')
        </if>
        <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
            AND purchase.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND purchase.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.relatedOrderId != null">
            AND purchase.related_order_id = #{reqVO.relatedOrderId}
        </if>
        <if test="reqVO.relatedSupplierId != null">
            AND purchase.related_supplier_id = #{reqVO.relatedSupplierId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
            AND (detail.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
        </if>
        <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
            AND purchase.currency_dict_id = #{reqVO.currencyDictId}
        </if>
        <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
            AND purchase.company_org_id = #{reqVO.companyOrgId}
        </if>
        <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
            AND purchase.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
        </if>
        <if test="reqVO.paymentTermsDictId != null and reqVO.paymentTermsDictId != ''">
            AND purchase.payment_terms_dict_id = #{reqVO.paymentTermsDictId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.dataStatus != null">
            AND purchase.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND purchase.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND purchase.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.inboundStatus != null">
            AND detail.inbound_status = #{reqVO.inboundStatus}
        </if>
        <if test="reqVO.isTakeMaterial != null">
            AND purchase.is_take_material = #{reqVO.isTakeMaterial}
        </if>
        <if test="reqVO.isTakeMold != null">
            AND purchase.is_take_mold = #{reqVO.isTakeMold}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
            AND (purchase.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
        </if>
        <if test="reqVO.purchaseOrderIdList != null and reqVO.purchaseOrderIdList.size > 0">
            AND detail.purchase_order_id IN
            <foreach collection="reqVO.purchaseOrderIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.purchaseOrderBizTypeList != null and reqVO.purchaseOrderBizTypeList.size > 0">
            AND purchase.purchase_order_biz_type IN
            <foreach collection="reqVO.purchaseOrderBizTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.formStatus != null">
            AND purchase.form_status = #{reqVO.formStatus}
        </if>
        ORDER BY purchase.purchase_order_id DESC, detail.row_no ASC
    </select>

    <select id="queryPurchaseDetailList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.isMaterialFullOpered != null">
            AND detail.is_material_full_opered = #{reqVO.isMaterialFullOpered}
        </if>
        <if test="reqVO.isMaterialFullCompleted != null">
            AND detail.is_material_full_completed = #{reqVO.isMaterialFullCompleted}
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="queryPurchaseDetailList4Demand"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*, demanddetail.purchase_able_qty
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        LEFT JOIN u_purchase_demand demand ON demand.purchase_demand_id = purchase.related_order_id
        LEFT JOIN u_purchase_demand_detail demanddetail
        ON demand.purchase_demand_id = demanddetail.purchase_demand_id
        AND demanddetail.material_id = detail.material_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.isMaterialFullOpered != null">
            AND detail.is_material_full_opered = #{reqVO.isMaterialFullOpered}
        </if>
        <if test="reqVO.isMaterialFullCompleted != null">
            AND detail.is_material_full_completed = #{reqVO.isMaterialFullCompleted}
        </if>
        ORDER BY detail.row_no ASC
    </select>
    <select id="queryPurchaseDetailList4Prod"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*, proddetail.purchaseable_qty
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        LEFT JOIN u_erp_prod_order prod ON prod.prod_order_id = purchase.related_order_id
        LEFT JOIN u_erp_prod_order_detail proddetail
        ON prod.prod_order_id = proddetail.prod_order_id
        AND proddetail.material_id = detail.material_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.isMaterialFullOpered != null">
            AND detail.is_material_full_opered = #{reqVO.isMaterialFullOpered}
        </if>
        <if test="reqVO.isMaterialFullCompleted != null">
            AND detail.is_material_full_completed = #{reqVO.isMaterialFullCompleted}
        </if>
        ORDER BY detail.row_no ASC
    </select>
    <select id="queryPurchaseDetailList4Material"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*, total.purchaseable_qty
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        LEFT JOIN u_material_analysis analysis ON analysis.material_analysis_id = purchase.related_order_id
        LEFT JOIN u_material_analysis_total total
        ON analysis.material_analysis_id = total.material_analysis_id
        AND total.material_id = detail.material_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.isMaterialFullOpered != null">
            AND detail.is_material_full_opered = #{reqVO.isMaterialFullOpered}
        </if>
        <if test="reqVO.isMaterialFullCompleted != null">
            AND detail.is_material_full_completed = #{reqVO.isMaterialFullCompleted}
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="queryDetailList4ChangeEdit"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*,
        COALESCE(detail.opered_qty, 0) AS nonChangeValue
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="queryDetailList4ChangeEditByDemand"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*,
        (COALESCE(demanddetail.purchase_able_qty, 0) + COALESCE(detail.purchase_qty, 0)) AS purchaseAbleQty,
        COALESCE(detail.opered_qty, 0) AS nonChangeValue
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        LEFT JOIN u_purchase_demand demand ON demand.purchase_demand_id = purchase.related_order_id
        LEFT JOIN u_purchase_demand_detail demanddetail
        ON demand.purchase_demand_id = demanddetail.purchase_demand_id
        AND demanddetail.material_id = detail.material_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="queryDetailList4ChangeEditByProd"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*,
        (COALESCE(proddetail.purchaseable_qty, 0) + COALESCE(detail.purchase_qty, 0)) AS purchaseAbleQty,
        COALESCE(detail.opered_qty, 0) AS nonChangeValue
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        LEFT JOIN u_erp_prod_order prod ON prod.prod_order_id = purchase.related_order_id
        LEFT JOIN u_erp_prod_order_detail proddetail
        ON prod.prod_order_id = proddetail.prod_order_id
        AND proddetail.material_id = detail.material_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="queryDetailList4ChangeEditByMaterial"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT detail.*,
        (COALESCE(total.purchaseable_qty, 0) + COALESCE(detail.purchase_qty, 0)) AS purchaseAbleQty,
        COALESCE(detail.opered_qty, 0) AS nonChangeValue
        FROM u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
        LEFT JOIN u_material_analysis analysis ON analysis.material_analysis_id = purchase.related_order_id
        LEFT JOIN u_material_analysis_total total
        ON analysis.material_analysis_id = total.material_analysis_id
        AND total.material_id = detail.material_id
        WHERE detail.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY detail.row_no ASC
    </select>

    <select id="queryProdPurchaseDetailList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO"
            parameterType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailQueryReqVO">
        SELECT detail.*, proddetail.purchaseable_qty
        FROM u_purchase_order_detail detail
                 LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = detail.purchase_order_id
                 LEFT JOIN u_erp_prod_order prod ON prod.prod_order_id = purchase.related_order_id
                 LEFT JOIN u_erp_prod_order_detail proddetail ON prod.prod_order_id = proddetail.prod_order_id AND proddetail.material_id = detail.material_id
        WHERE purchase.purchase_order_id = #{reqVO.purchaseOrderId}
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND detail.material_id NOT IN
            <foreach collection="reqVO.exclMaterialIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryDetailListForProd"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO"
            parameterType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailQueryReqVO">
        SELECT detail.*, purchase.related_supplier_id, purchase.delivery_date, purchase.director_id, purchase.form_dt
        FROM  u_purchase_order_detail detail
        LEFT JOIN u_purchase_order purchase ON detail.purchase_order_id = purchase.purchase_order_id
        LEFT JOIN u_erp_prod_order prod ON purchase.related_order_id = prod.prod_order_id
        WHERE purchase.related_order_id = #{reqVO.relatedOrderId}
        AND purchase.data_status = #{reqVO.dataStatus}
        ORDER BY purchase.purchase_order_id DESC, detail.row_no ASC
    </select>

    <select id="operableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.purchase_order_detail_id as fieldId, detail.material_code as code, detail.operable_qty as sumQty
        FROM u_purchase_order_detail detail
        WHERE detail.purchase_order_id = #{purchaseOrderId}
    </select>

    <select id="completeableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.purchase_order_detail_id as fieldId, detail.material_id, detail.material_code as code,
            detail.completeable_qty as sumQty
        FROM u_purchase_order_detail detail
        WHERE detail.purchase_order_id = #{purchaseOrderId}
    </select>

    <select id="getPurchaseOrderStat" resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderStatRespVO">
        SELECT SUM(purchase_qty) totalPurchaseQty, SUM(inbounded_qty) totalInboundedQty
        FROM u_purchase_order_detail detail
        WHERE detail.purchase_order_id = #{purchaseOrderId}
    </select>

    <select id="queryMaterialPurchaseQty" resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO">
        SELECT b.material_id, SUM(b.purchase_qty) purchase_qty
        FROM u_purchase_order a
        JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
        WHERE a.data_status = 1
        AND a.related_order_id = #{relatedOrderId}
        <if test="materialIdList != null and materialIdList.size > 0">
            AND b.material_id IN
            <foreach collection="materialIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY b.material_id
    </select>

</mapper>
