<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListByMaterialId" resultType="java.lang.Integer">
        select count(1)
        from u_purchase_order orders
             left join u_purchase_order_detail detail on orders.purchase_order_id=detail.purchase_order_id
        where orders.data_status=1 and orders.related_order_id = #{relatedOrderId} and detail.material_id = #{materialId}
    </select>

    <select id="findPurchaseQty"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseRespBO">

        SELECT prod_order.related_order_id as sourceFormId,prod_order.related_order_id as sourceFormId,purchase.related_order_id as relatedOrderId,sum(purchase_detail.purchase_qty) as purchasedQty,sum(purchase_detail.inbounded_qty) as inbounded_qty,
               sum(purchase.is_take_material) as isTakeMaterial
        FROM u_purchase_order as purchase
             left join u_purchase_order_detail purchase_detail on purchase.purchase_order_id=purchase_detail.purchase_order_id
             left join u_erp_prod_order prod_order on purchase.related_order_id=prod_order.prod_order_id
        where purchase.data_status=1
        <if test="purchaseOrderBizType != null">
            and purchase.purchase_order_biz_type = #{purchaseOrderBizType}
        </if>
        <if test="purchaseOrderBizTypeList != null and purchaseOrderBizTypeList.size > 0 ">
            and purchase.purchase_order_biz_type IN
            <foreach item="item" index="index" collection="purchaseOrderBizTypeList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids != null and ids.size > 0 ">
            and purchase.related_order_id IN
            <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY purchase.related_order_id,prod_order.related_order_id
    </select>

    <select id="selectSumQty"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseQtyBO">
        select orders.purchase_order_biz_type as bizType,orders.purchase_order_id,orders.purchase_order_code,detail.material_id,detail.purchase_qty - detail.inbounded_qty as inboundableQty
        from u_purchase_order orders
        left join u_purchase_order_detail detail on orders.purchase_order_id=detail.purchase_order_id
        where orders.data_status = 1
        <if test="reqVO.inboundStatusList != null and reqVO.inboundStatusList.size > 0 ">
            and orders.inbound_status IN
            <foreach item="item" index="index" collection="reqVO.inboundStatusList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.formStatusList != null and reqVO.formStatusList.size > 0 ">
            and orders.form_status IN
            <foreach item="item" index="index" collection="reqVO.formStatusList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
            and detail.material_id IN
            <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.demandDate != null ">
            AND detail.delivery_date &lt;= #{reqVO.demandDate}
        </if>
    </select>
</mapper>
