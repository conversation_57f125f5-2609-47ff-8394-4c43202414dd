<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.purchasereturn.PurchaseReturnDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO"
            parameterType="com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailQueryReqVO">
        SELECT returndet.*, purchasedet.completeable_qty
        FROM u_purchase_return_detail returndet
        LEFT JOIN u_purchase_order_detail purchasedet ON purchasedet.material_id = returndet.material_id
        LEFT JOIN u_purchase_return return ON return.purchase_order_id = purchasedet.purchase_order_id AND return.purchase_return_id = returndet.purchase_return_id
        WHERE return.purchase_return_id = #{reqVO.purchaseReturnId}
    </select>
    <select id="getReturnIsApproveAble" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.purchasereturn.PurchaseReturnDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.completeable_qty &lt; b.total_return_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_purchase_order_detail a
        LEFT JOIN (
            SELECT
                material_id,
                SUM(return_qty) AS total_return_qty
            FROM u_purchase_return_detail
            WHERE purchase_return_id = #{item.purchaseReturnId}
            GROUP BY material_id
        ) b ON a.material_id = b.material_id
        WHERE a.purchase_order_id = #{item.purchaseOrderId}
          AND b.material_id IS NOT NULL
    </select>
    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO">
        SELECT detail.*, return.*
        FROM u_purchase_return_detail detail
        LEFT JOIN u_purchase_return return ON detail.purchase_return_id = return.purchase_return_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.purchaseReturnCode != null and reqVO.purchaseReturnCode != ''">
                AND return.purchase_return_code LIKE CONCAT('%', #{reqVO.purchaseReturnCode}, '%')
            </if>
            <if test="reqVO.returnTypeDictId != null and reqVO.returnTypeDictId != ''">
                AND return.return_type_dict_id = #{reqVO.returnTypeDictId}
            </if>
            <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
                AND return.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
            </if>
            <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
                AND return.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
            </if>
            <if test="reqVO.relatedSupplierId != null">
                AND return.related_supplier_id = #{reqVO.relatedSupplierId}
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                AND return.currency_dict_id = #{reqVO.currencyDictId}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
                AND (return.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
            </if>
            <if test="reqVO.startReturnDate != null and reqVO.endReturnDate != null">
                AND (return.return_date BETWEEN #{reqVO.startReturnDate} AND #{reqVO.endReturnDate})
            </if>
            <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
                AND return.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
            </if>
            <if test="reqVO.refundConditionDictId != null and reqVO.refundConditionDictId != ''">
                AND return.refund_condition_dict_id = #{reqVO.refundConditionDictId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND return.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND return.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND return.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (return.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
        </where>
        ORDER BY return.purchase_return_id DESC, detail.row_no ASC
    </select>

    <select id="outboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.purchase_return_detail_id as fieldId, detail.material_code as code, outboundable_qty as sumQty
        FROM u_purchase_return_detail detail
        WHERE detail.purchase_return_id = #{purchaseReturnId}
    </select>

    <select id="outboundDetailQuotedList" resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO">
        SELECT detail.*, stock.warehouse_org_id, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty
        FROM u_purchase_return_detail detail
        LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id
        WHERE detail.purchase_return_id = #{purchaseReturnId}
        AND detail.is_material_full_outbounded = 0
    </select>


</mapper>
