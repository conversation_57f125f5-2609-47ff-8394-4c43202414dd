<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.purchasechange.detail.PurchaseChangeDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO">
        SELECT detail.*, COALESCE(purdetail.opered_qty, 0) AS nonChangeValue,
               COALESCE(purdetail.reconciled_qty, 0) AS reconciled_qty, COALESCE(purdetail.planed_qty, 0) AS planed_qty, COALESCE(purdetail.received_qty, 0) AS received_qty
        FROM u_purchase_change_detail detail
        LEFT JOIN u_purchase_change change ON change.purchase_change_id = detail.purchase_change_id
        LEFT JOIN u_purchase_order_detail purdetail ON purdetail.purchase_order_id = change.purchase_order_id
                   AND purdetail.material_id = detail.material_id
        WHERE detail.purchase_change_id = #{reqVO.purchaseChangeId}
        ORDER BY detail.row_no
    </select>

    <select id="queryList4Demand"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO">
        SELECT detail.*,
                (COALESCE(demanddetail.purchase_able_qty, 0) + COALESCE(purdetail.purchase_qty, 0)) AS purchaseAbleQty,
                COALESCE(purdetail.opered_qty, 0) AS nonChangeValue,
                COALESCE(purdetail.reconciled_qty, 0) AS reconciled_qty, COALESCE(purdetail.planed_qty, 0) AS planed_qty, COALESCE(purdetail.received_qty, 0) AS received_qty
        FROM u_purchase_change_detail detail
        LEFT JOIN u_purchase_change change ON change.purchase_change_id = detail.purchase_change_id
        LEFT JOIN u_purchase_demand demand ON demand.purchase_demand_id = change.related_order_id
        LEFT JOIN u_purchase_demand_detail demanddetail ON demand.purchase_demand_id = demanddetail.purchase_demand_id
        AND demanddetail.material_id = detail.material_id
        LEFT JOIN u_purchase_order_detail purdetail ON purdetail.purchase_order_id = change.purchase_order_id
        AND purdetail.material_id = detail.material_id
        WHERE detail.purchase_change_id = #{reqVO.purchaseChangeId}
        ORDER BY detail.row_no
    </select>
    <select id="queryList4Prod"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO">
        SELECT detail.*,
               (COALESCE(proddetail.purchaseable_qty, 0) + COALESCE(purdetail.purchase_qty, 0)) AS purchaseAbleQty,
               COALESCE(purdetail.opered_qty, 0) AS nonChangeValue,
               COALESCE(purdetail.reconciled_qty, 0) AS reconciled_qty, COALESCE(purdetail.planed_qty, 0) AS planed_qty, COALESCE(purdetail.received_qty, 0) AS received_qty
        FROM u_purchase_change_detail detail
        LEFT JOIN u_purchase_change change ON change.purchase_change_id = detail.purchase_change_id
        LEFT JOIN u_erp_prod_order prod ON prod.prod_order_id = change.related_order_id
        LEFT JOIN u_erp_prod_order_detail proddetail ON prod.prod_order_id = proddetail.prod_order_id
                      AND proddetail.material_id = detail.material_id
        LEFT JOIN u_purchase_order_detail purdetail ON purdetail.purchase_order_id = change.purchase_order_id
        AND purdetail.material_id = detail.material_id
        WHERE detail.purchase_change_id = #{reqVO.purchaseChangeId}
        ORDER BY detail.row_no
    </select>
    <select id="queryList4Material"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO">
        SELECT detail.*,
               (COALESCE(total.purchaseable_qty, 0) + COALESCE(purdetail.purchase_qty, 0)) AS purchaseAbleQty,
               COALESCE(purdetail.opered_qty, 0) AS nonChangeValue,
               COALESCE(purdetail.reconciled_qty, 0) AS reconciled_qty, COALESCE(purdetail.planed_qty, 0) AS planed_qty, COALESCE(purdetail.received_qty, 0) AS received_qty
        FROM u_purchase_change_detail detail
        LEFT JOIN u_purchase_change change ON change.purchase_change_id = detail.purchase_change_id
        LEFT JOIN u_material_analysis analysis ON analysis.material_analysis_id = change.related_order_id
        LEFT JOIN u_material_analysis_total total ON analysis.material_analysis_id = total.material_analysis_id
                      AND total.material_id = detail.material_id
        LEFT JOIN u_purchase_order_detail purdetail ON purdetail.purchase_order_id = change.purchase_order_id
        AND purdetail.material_id = detail.material_id
        WHERE detail.purchase_change_id = #{reqVO.purchaseChangeId}
        ORDER BY detail.row_no
    </select>

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO">
        SELECT detail.*, change.*, change.delivery_date purchaseDeliveryDate, detail.delivery_date detailDeliveryDate
        FROM u_purchase_change_detail detail
        LEFT JOIN u_purchase_change change ON detail.purchase_change_id = change.purchase_change_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE change.begin_purchase_change_id IS NOT NULL 
        <if test="reqVO.purchaseChangeCode != null and reqVO.purchaseChangeCode != ''">
            AND change.purchase_change_code LIKE CONCAT('%', #{reqVO.purchaseChangeCode}, '%')
        </if>
        <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
            AND change.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
        </if>
        <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
            AND change.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND change.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.relatedSupplierId != null">
            AND change.related_supplier_id = #{reqVO.relatedSupplierId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
            AND (change.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
        </if>
        <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
            AND change.currency_dict_id = #{reqVO.currencyDictId}
        </if>
        <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
            AND change.company_org_id = #{reqVO.companyOrgId}
        </if>
        <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
            AND change.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
        </if>
        <if test="reqVO.paymentTermsDictId != null and reqVO.paymentTermsDictId != ''">
            AND change.payment_terms_dict_id = #{reqVO.paymentTermsDictId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.dataStatus != null">
            AND change.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND change.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND change.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
            AND (change.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
        </if>
        <if test="reqVO.purchaseOrderBizTypeList != null and reqVO.purchaseOrderBizTypeList.size > 0">
            AND change.purchase_order_biz_type IN
            <foreach collection="reqVO.purchaseOrderBizTypeList" index="index" item= "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY change.purchase_change_id DESC, detail.row_no ASC
    </select>
</mapper>
