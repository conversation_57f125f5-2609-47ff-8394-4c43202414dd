<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.deduction.PurchaseDeductionDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailRespVO">
        SELECT deductiondet.*, purchasedet.completeable_qty
        FROM u_purchase_deduction_detail deductiondet
                 LEFT JOIN u_purchase_order_detail purchasedet ON purchasedet.material_id = deductiondet.material_id
                 LEFT JOIN u_purchase_deduction deduction ON deduction.purchase_order_id = purchasedet.purchase_order_id
                                            AND deduction.purchase_deduction_id = deductiondet.purchase_deduction_id
        WHERE deduction.purchase_deduction_id = #{reqVO.purchaseDeductionId}
    </select>
    <select id="getDeductionIsApproveAble" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.completeable_qty &lt; b.total_deduction_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_purchase_order_detail a
        LEFT JOIN (
            SELECT
                material_id,
                SUM(deduction_qty) AS total_deduction_qty
            FROM u_purchase_deduction_detail
            WHERE purchase_deduction_id = #{item.purchaseDeductionId}
            GROUP BY material_id
        ) b ON a.material_id = b.material_id
        WHERE a.purchase_order_id = #{item.purchaseOrderId}
          AND b.material_id IS NOT NULL
    </select>
    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailRespVO">
        SELECT detail.*, deduct.*
        FROM u_purchase_deduction_detail detail
        LEFT JOIN u_purchase_deduction deduct ON detail.purchase_deduction_id = deduct.purchase_deduction_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE detail.purchase_deduction_detail_id IS NOT NULL
        <if test="reqVO.purchaseDeductionCode != null and reqVO.purchaseDeductionCode != ''">
            AND deduct.purchase_deduction_code LIKE CONCAT('%', #{reqVO.purchaseDeductionCode}, '%')
        </if>
        <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
            AND deduct.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
        </if>
        <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
            AND deduct.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
        </if>
        <if test="reqVO.relatedSupplierId != null">
            AND deduct.related_supplier_id = #{reqVO.relatedSupplierId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
            AND (deduct.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
        </if>
        <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
            AND deduct.currency_dict_id = #{reqVO.currencyDictId}
        </if>
        <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
            AND deduct.company_org_id = #{reqVO.companyOrgId}
        </if>
        <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
            AND deduct.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
        </if>
        <if test="reqVO.refundConditionDictId != null and reqVO.refundConditionDictId != ''">
            AND deduct.refund_condition_dict_id = #{reqVO.refundConditionDictId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.dataStatus != null">
            AND deduct.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND deduct.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND deduct.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
            AND (deduct.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
        </if>
        ORDER BY deduct.purchase_deduction_id DESC, detail.row_no ASC
    </select>
</mapper>
