<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.demand.PurchaseDemandMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="demandForPurchasePage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseDemandRespVO">
        SELECT demand.* FROM u_purchase_demand demand
        LEFT JOIN u_purchase_demand_detail detail ON detail.purchase_demand_id = demand.purchase_demand_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE demand.data_status = 1
        AND detail.purchase_able_qty > 0
        <if test="reqVO.purchaseDemandCode != null and reqVO.purchaseDemandCode != ''">
            AND demand.purchase_demand_code LIKE CONCAT('%', #{reqVO.purchaseDemandCode}, '%')
        </if>
        <if test="reqVO.demandTypeDictId != null and reqVO.demandTypeDictId != ''">
            AND demand.demand_type_dict_id = #{reqVO.demandTypeDictId}
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND demand.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        GROUP BY demand.purchase_demand_id
        ORDER BY demand.purchase_demand_id DESC
    </select>
    <select id="demandForPurchaseList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseDemandRespVO">
        SELECT demand.* FROM u_purchase_demand demand
        LEFT JOIN u_purchase_demand_detail detail ON detail.purchase_demand_id = demand.purchase_demand_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE demand.data_status = 1
        AND detail.purchase_able_qty > 0
        <if test="reqVO.purchaseDemandCode != null and reqVO.purchaseDemandCode != ''">
            AND demand.purchase_demand_code LIKE CONCAT('%', #{reqVO.purchaseDemandCode}, '%')
        </if>
        GROUP BY demand.purchase_demand_id
        ORDER BY demand.purchase_demand_id DESC
    </select>

    <select id="findPurchaseDemandQty"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.demand.bo.PurchaseDemandRespBO">

        SELECT demand.related_order_id as saleOrderId,demand.purchase_demand_id as purchaseDemandId,
               sum(demand_detail.plan_demand_qty) as plan_demand_qty,sum(demand_detail.purchased_qty) as purchased_qty
        FROM u_purchase_demand as demand
             left join u_purchase_demand_detail demand_detail on demand.purchase_demand_id=demand_detail.purchase_demand_id
        where demand.data_status=1 and demand.purchase_demand_biz_type = #{purchaseDemandBizType}
        <if test="saleOrderIds != null and saleOrderIds.size > 0 ">
            and demand.related_order_id IN
            <foreach item="item" index="index" collection="saleOrderIds"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY demand.related_order_id,demand.purchase_demand_id

    </select>


    <select id="selectListBy" resultType="com.mongoso.mgs.module.ai.controller.admin.finance.vo.PurchaseDemandBOAI">
        SELECT
            t1.purchase_demand_id,t1.purchase_demand_code,
            t2.purchase_able_qty as qty,
            t3.material_code, t3.material_name
        FROM u_purchase_demand t1
        LEFT JOIN u_purchase_demand_detail t2 ON t2.purchase_demand_id = t1.purchase_demand_id
        LEFT JOIN u_material t3  ON t3.material_id = t2.material_id
        WHERE t1.data_status = 1 AND t2.purchase_able_qty > 0
    </select>


    <select id="selectListBy2"
            resultType="com.mongoso.mgs.module.ai.controller.admin.finance.vo.AccountBalanceVOAI5">
        SELECT
            t1.purchase_demand_id, t2.purchase_demand_code,t1.form_dt,
            t2.purchased_qty as qty , t2.material_id
        FROM u_purchase_demand t1
        LEFT JOIN u_purchase_demand_detail t2 ON t2.purchase_demand_id = t1.purchase_demand_id
        WHERE t1.data_status = 1
          AND t1.form_dt >= #{startDt}
          AND t1.form_dt &lt;= #{endDt}

    </select>
</mapper>
