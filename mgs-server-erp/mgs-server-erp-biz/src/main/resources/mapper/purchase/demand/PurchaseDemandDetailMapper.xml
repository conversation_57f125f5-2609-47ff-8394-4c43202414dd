<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.demand.detail.PurchaseDemandDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailRespVO">
        SELECT detail.*, demand.*, demand.approved_by
        FROM u_purchase_demand demand
        JOIN u_purchase_demand_detail detail ON demand.purchase_demand_id = detail.purchase_demand_id
        LEFT JOIN u_material material ON material.material_id = detail.material_id AND material.deleted = 0
        WHERE detail.purchase_demand_detail_id IS NOT NULL
        <if test="reqVO.purchaseDemandCode != null and reqVO.purchaseDemandCode != ''">
            AND demand.purchase_demand_code LIKE CONCAT('%', #{reqVO.purchaseDemandCode}, '%')
        </if>
        <if test="reqVO.purchaseDemandName != null and reqVO.purchaseDemandName != ''">
            AND demand.purchase_demand_name LIKE CONCAT('%', #{reqVO.purchaseDemandName}, '%')
        </if>
        <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
            AND demand.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
        </if>
        <if test="reqVO.relatedOrderId != null ">
            AND demand.related_order_id = #{reqVO.relatedOrderId}
        </if>
        <if test="reqVO.demandTypeDictId != null and reqVO.demandTypeDictId != ''">
            AND demand.demand_type_dict_id = #{reqVO.demandTypeDictId}
        </if>
        <if test="reqVO.dataStatus != null">
            AND demand.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND demand.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND demand.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        ORDER BY demand.purchase_demand_id DESC, detail.row_no ASC
    </select>

    <select id="getDemandIsApproveAble" resultType="java.lang.Integer">
        SELECT
        CASE
            WHEN SUM(CASE WHEN a.plan_able_demand_qty &lt; b.plan_demand_qty THEN 1 ELSE 0 END) = 0 THEN 1
            ELSE 0
        END AS result
        FROM u_erp_sale_order_detail a
        LEFT JOIN u_purchase_demand_detail b ON a.material_id = b.material_id
        WHERE b.purchase_demand_id = #{item.purchaseDemandId}
        AND a.sale_order_id = #{item.relatedOrderId}
    </select>

    <select id="getDemandIsApproveAbleByAnalysis" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN total.planable_demand_qty &lt; demand.plan_demand_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_material_analysis_total total
                 LEFT JOIN u_purchase_demand_detail demand ON total.material_id = demand.material_id
        WHERE demand.purchase_demand_id = #{item.purchaseDemandId}
          AND total.material_analysis_id = #{item.relatedOrderId}
    </select>


</mapper>
