<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.report.PurchaseReportMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="queryMaterialPurchaseReport"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.report.vo.MaterialPurchaseReportRespVO">
        SELECT *
        FROM (
            SELECT b.material_id, b.material_code, m.material_name, dict.dict_name material_category_dict_name,
                m.spec_model, m.spec_attribute_str
            FROM u_purchase_order a
            JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
            LEFT JOIN u_material m ON b.material_id = m.material_id
            LEFT JOIN platform.t_dict dict ON dict.dict_obj_code = 'ERP107' and  m.material_category_dict_id = dict.dict_code
            WHERE a.data_status = 1
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND m.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND m.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND m.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND DATE(a.form_dt) >= #{reqVO.startFormDt}
                AND DATE(a.form_dt) &lt;= #{reqVO.endFormDt}
            </if>
            UNION
            SELECT b.material_id, b.material_code, m.material_name, dict.dict_name material_category_dict_name,
                m.spec_model, m.spec_attribute_str
            FROM u_purchase_return a
            JOIN u_purchase_return_detail b ON a.purchase_return_id = b.purchase_return_id
            LEFT JOIN u_material m ON b.material_id = m.material_id
            LEFT JOIN platform.t_dict dict ON dict.dict_obj_code = 'ERP107' AND m.material_category_dict_id = dict.dict_code
            WHERE a.data_status = 1
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND m.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND m.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND m.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND DATE(a.form_dt) >= #{reqVO.startFormDt}
                AND DATE(a.form_dt) &lt;= #{reqVO.endFormDt}
            </if>
            UNION
            SELECT b.material_id, b.material_code, m.material_name, dict.dict_name material_category_dict_name,
                m.spec_model, m.spec_attribute_str
            FROM u_purchase_deduction a
            JOIN u_purchase_deduction_detail b ON a.purchase_deduction_id = b.purchase_deduction_id
            LEFT JOIN u_material m ON b.material_id = m.material_id
            LEFT JOIN platform.t_dict dict ON dict.dict_obj_code = 'ERP107' AND m.material_category_dict_id = dict.dict_code
            WHERE a.data_status = 1
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND m.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND m.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND m.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND DATE(a.form_dt) >= #{reqVO.startFormDt}
                AND DATE(a.form_dt) &lt;= #{reqVO.endFormDt}
            </if>
        ) t
        ORDER BY material_id
    </select>


    <select id="queryMaterialPurchaseReportDetailList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.report.bo.MaterialPurchaseReportBO">
        SELECT materialId, COALESCE(SUM(purchaseQty), 0) purchaseQty, COALESCE(SUM(purchaseInclTaxAmt), 0) purchaseInclTaxAmt,
            COALESCE(SUM(returnQty), 0) returnQty, COALESCE(SUM(returnInclTaxAmt), 0) returnInclTaxAmt,
            COALESCE(SUM(deductionQty), 0) deductionQty, COALESCE(SUM(deductionInclTaxAmt), 0) deductionInclTaxAmt
            <if test="reportType == 0">
                , TO_CHAR(formdt, 'YYYY-MM-DD') reportDate
            </if>
            <if test="reportType == 1">
                , TO_CHAR(formdt, 'YYYY-MM') reportDate
            </if>
            <if test="reportType == 2">
                , TO_CHAR(formdt, 'YYYY') reportDate
            </if>
        FROM (
            SELECT b.material_id materialId, b.purchase_qty purchaseQty, b.incl_tax_amt purchaseInclTaxAmt,
                0 returnQty, 0 returnInclTaxAmt, 0 deductionQty, 0 deductionInclTaxAmt, a.form_dt formdt
            FROM u_purchase_order_detail b
            JOIN u_purchase_order a ON a.purchase_order_id = b.purchase_order_id
            WHERE a.data_status = 1
            <if test="materialIdList != null and materialIdList.size > 0 ">AND b.material_id IN
                <foreach item ="item" index="index" collection="materialIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="startFormDt != null and endFormDt != null ">
                    AND DATE(a.form_dt) >= #{startFormDt}
                    AND DATE(a.form_dt) &lt;= #{endFormDt}
                </if>
                UNION ALL
                SELECT b.material_id materialId, 0 purchaseQty, 0 purchaseInclTaxAmt, b.return_qty returnQty,
                    b.incl_tax_amt returnInclTaxAmt, 0 deductionQty, 0 deductionInclTaxAmt, a.form_dt formdt
                FROM u_purchase_return a
                JOIN u_purchase_return_detail b ON a.purchase_return_id = b.purchase_return_id
                WHERE a.data_status = 1
                <if test="materialIdList != null and materialIdList.size > 0 ">
                    AND b.material_id IN
                    <foreach item="item" index="index" collection="materialIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="startFormDt != null and endFormDt != null ">
                    AND DATE(a.form_dt) >= #{startFormDt}
                    AND DATE(a.form_dt) &lt;= #{endFormDt}
                </if>
                UNION ALL
                SELECT b.material_id materialId, 0 purchase_qty, 0 purchaseInclTaxAmt, 0 returnQty, 0 returnInclTaxAmt,
                    b.deduction_qty deductionQty, b.incl_tax_amt deductionInclTaxAmt, a.form_dt formdt
                FROM u_purchase_deduction_detail b
                JOIN u_purchase_deduction a ON a.purchase_deduction_id = b.purchase_deduction_id
                WHERE a.data_status = 1
                <if test="materialIdList != null and materialIdList.size > 0 ">
                    AND b.material_id IN
                    <foreach item="item" index="index" collection="materialIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="startFormDt != null and endFormDt != null ">
                    AND DATE(a.form_dt) >= #{startFormDt}
                    AND DATE(a.form_dt) &lt;= #{endFormDt}
                </if>
        ) t
        GROUP BY materialId
        <if test="reportType == 0">
            , TO_CHAR(formdt, 'YYYY-MM-DD')
        </if>
        <if test="reportType == 1">
            , TO_CHAR(formdt, 'YYYY-MM')
        </if>
        <if test="reportType == 2">
            , TO_CHAR(formdt, 'YYYY')
        </if>
    </select>

</mapper>
