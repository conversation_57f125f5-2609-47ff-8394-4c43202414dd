<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.workbench.PurchaseWorkbenchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 待完成需求单数量 -->
    <select id="getPendingDemandCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT a.purchase_demand_id)
        FROM u_purchase_demand a
        JOIN u_purchase_demand_detail b ON a.purchase_demand_id = b.purchase_demand_id
        WHERE a.data_status = 1
        AND b.purchase_able_qty > 0
    </select>

    <!-- 待完成订单数量 -->
    <select id="getPendingOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_purchase_order a
        WHERE a.data_status = 1
        AND a.is_force_close = 0
        AND a.inbound_status IN (0, 1)
    </select>

    <!-- 待入库物料数量 -->
    <select id="getPendingInboundQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(b.purchase_qty - b.inbounded_qty), 0)
        FROM u_purchase_order a
        JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
        WHERE a.data_status = 1
        AND a.is_force_close = 0
        AND a.inbound_status IN (0, 1)
    </select>

    <!-- 工序委外待采购数量 -->
    <select id="getPODPendingPurchaseQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(purchaseable_qty), 0)
        FROM u_process_out_demand
    </select>

    <!-- 待退货物料数量 -->
    <select id="getReturnPendingInboundedQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(outboundable_qty), 0)
        FROM u_purchase_return a
        JOIN u_purchase_return_detail b ON a.purchase_return_id = b.purchase_return_id
        WHERE a.data_status = 1
    </select>

    <!-- 供应商数量 -->
    <select id="getSupplierNumber" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_supplier a
        WHERE a.data_status = 1
    </select>

    <!-- 近3月活跃数量 -->
    <select id="getActiveSupplierNumber" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT related_supplier_id)
        FROM (
            SELECT related_supplier_id
            FROM u_purchase_order a
            WHERE a.data_status = 1
            AND a.form_dt > CURRENT_DATE - INTERVAL '90 days'
            UNION
            SELECT related_supplier_id
            FROM u_purchase_process_out a
            WHERE a.data_status = 1
            AND a.form_dt > CURRENT_DATE - INTERVAL '90 days'
        ) t
    </select>

    <select id="queryPurchaseAmtLineList"
            resultType="com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseLineBO">
        SELECT
            TO_CHAR( formdt, 'YYYY-MM-DD' ) DATE,
	SUM ( purchaseInclTaxAmt ) purchaseInclTaxAmt,
	SUM ( returnInclTaxAmt ) returnInclTaxAmt,
	SUM ( deductionInclTaxAmt ) deductionInclTaxAmt
        FROM
            (
            SELECT
            b.incl_tax_local_currency_amt purchaseInclTaxAmt,
            0 returnInclTaxAmt,
            0 deductionInclTaxAmt,
            A.form_dt formdt
            FROM
            u_purchase_order
            A JOIN u_purchase_order_detail b ON A.purchase_order_id = b.purchase_order_id
            WHERE
            A.data_status = 1
            AND A.form_dt > CURRENT_DATE - INTERVAL '30 days' UNION ALL
            SELECT
            b.incl_tax_local_currency_amt purchaseInclTaxAmt,
            0 returnInclTaxAmt,
            0 deductionInclTaxAmt,
            A.form_dt formdt
            FROM
            u_purchase_process_out
            A JOIN u_purchase_process_out_detail b ON A.purchase_process_out_id = b.purchase_process_out_id
            WHERE
            A.data_status = 1
            AND A.form_dt > CURRENT_DATE - INTERVAL '30 days' UNION ALL
            SELECT
            0 purchaseInclTaxAmt,
            b.incl_tax_local_currency_amt returnInclTaxAmt,
            0 deductionInclTaxAmt,
            A.form_dt formdt
            FROM
            u_purchase_return
            A JOIN u_purchase_return_detail b ON A.purchase_return_id = b.purchase_return_id
            WHERE
            A.data_status = 1
            AND A.form_dt > CURRENT_DATE - INTERVAL '30 days' UNION ALL
            SELECT
            0 purchaseInclTaxAmt,
            b.incl_tax_local_currency_amt returnInclTaxAmt,
            0 deductionInclTaxAmt,
            A.form_dt formdt
            FROM
            u_pur_process_out_return
            A JOIN u_pur_process_out_return_detail b ON A.process_out_return_id = b.process_out_return_id
            WHERE
            A.data_status = 1
            AND A.form_dt > CURRENT_DATE - INTERVAL '30 days' UNION ALL
            SELECT
            0 purchaseInclTaxAmt,
            0 returnInclTaxAmt,
            b.incl_tax_local_currency_amt deductionInclTaxAmt,
            A.form_dt formdt
            FROM
            u_purchase_deduction
            A JOIN u_purchase_deduction_detail b ON A.purchase_deduction_id = b.purchase_deduction_id
            WHERE
            A.data_status = 1
            AND A.form_dt > CURRENT_DATE - INTERVAL '30 days' UNION ALL
            SELECT
            0 purchaseInclTaxAmt,
            0 returnInclTaxAmt,
            b.incl_tax_local_currency_amt deductionInclTaxAmt,
            A.form_dt formdt
            FROM
            u_pur_process_out_deduction
            A JOIN u_pur_process_out_deduction_detail b ON A.process_out_deduction_id = b.process_out_deduction_id
            WHERE
            A.data_status = 1
            AND A.form_dt > CURRENT_DATE - INTERVAL '30 days'
            ) T
        GROUP BY
            TO_CHAR( formdt, 'YYYY-MM-DD' )

    </select>

    <select id="queryPurchaseAmtRank"
            resultType="com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseRankBO">
        SELECT m.material_code, m.material_name, m.spec_attribute_str, SUM(b.incl_tax_local_currency_amt) value
        FROM u_purchase_order a
        JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
        LEFT JOIN u_material m ON b.material_id = m.material_id
        WHERE a.data_status = 1
        AND a.form_dt > CURRENT_DATE - INTERVAL '30 days'
        GROUP BY m.material_code, m.material_name, m.spec_attribute_str
        ORDER BY value DESC
        LIMIT 10
    </select>

    <select id="queryPurchaseQtyRank"
            resultType="com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseRankBO">
        SELECT m.material_code, m.material_name, m.spec_attribute_str, SUM(b.purchase_qty) value
        FROM u_purchase_order a
        JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
        LEFT JOIN u_material m ON b.material_id = m.material_id
        WHERE a.data_status = 1
        AND a.form_dt > CURRENT_DATE - INTERVAL '30 days'
        GROUP BY m.material_code, m.material_name, m.spec_attribute_str
        ORDER BY value DESC
        LIMIT 10
    </select>

</mapper>
