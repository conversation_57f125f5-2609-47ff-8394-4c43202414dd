<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutdeduction.PurProcessOutDeductionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryProcessOutDeductionDetailPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeduction.vo.PurProcessOutDeductionDetailResp">
        SELECT detail.row_no,detail.related_row_no,detail.material_id,detail.material_code,detail.process_id,detail.process_code,detail.process_name,
               detail.excl_tax_unit_price, detail.excl_tax_amt, detail.invoice_type_id, detail.tax_rate,
               detail.incl_tax_unit_price, detail.incl_tax_amt, detail.deduction_qty, detail.main_unit_dict_id, detail.remark,
               detail.excl_tax_local_currency_amt, detail.excl_tax_local_currency_amt,
               deduction.*
        FROM u_pur_process_out_deduction deduction
            INNER JOIN u_pur_process_out_deduction_detail detail on deduction.process_out_deduction_id=detail.process_out_deduction_id
        <where>
            <if test="reqVO.processOutDeductionId != null">
                AND deduction.process_out_deduction_id = #{reqVO.processOutDeductionId}
            </if>
            <if test="reqVO.processOutDeductionCode != null and reqVO.processOutDeductionCode != ''">
                AND deduction.process_out_deduction_code LIKE concat('%', #{reqVO.processOutDeductionCode}, '%')
            </if>
            <if test="reqVO.purchaseProcessOutCode != null and reqVO.purchaseProcessOutCode != ''">
                AND deduction.purchase_process_out_code LIKE concat('%', #{reqVO.purchaseProcessOutCode}, '%')
            </if>
            <if test="reqVO.relatedSupplierId != null">
                AND deduction.related_supplier_id = #{reqVO.relatedSupplierId}
            </if>
            <if test="reqVO.companyOrgId != null and reqVO.companyOrgId != ''">
                AND deduction.company_org_id = #{reqVO.companyOrgId}
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                AND deduction.currency_dict_id = #{reqVO.currencyDictId}
            </if>
            <if test="reqVO.settlementMethodDictId != null and reqVO.settlementMethodDictId != ''">
                AND deduction.settlement_method_dict_id = #{reqVO.settlementMethodDictId}
            </if>
            <if test="reqVO.refundConditionDictId != null and reqVO.refundConditionDictId != ''">
                AND deduction.refund_condition_dict_id = #{reqVO.refundConditionDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND deduction.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND deduction.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND deduction.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.materialId != null">
                AND detail.material_id = #{reqVO.materialId}
            </if>
            <if test="reqVO.materialIdList != null and reqVO.materialIdList.size > 0 ">
                AND detail.material_id IN
                <foreach item="item" index="index" collection="reqVO.materialIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND deduction.form_dt >= #{reqVO.startFormDt}
                AND deduction.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null ">
                AND deduction.delivery_date >= #{reqVO.startDeliveryDate}
                AND deduction.delivery_date &lt;= #{reqVO.endDeliveryDate}
            </if>
            <if test="reqVO.startReturnDate != null and reqVO.endReturnDate != null ">
                AND deduction.return_date >= #{reqVO.startReturnDate}
                AND deduction.return_date &lt;= #{reqVO.endReturnDate}
            </if>
        </where>
        order by deduction.created_dt desc,detail.row_no asc
    </select>
</mapper>
