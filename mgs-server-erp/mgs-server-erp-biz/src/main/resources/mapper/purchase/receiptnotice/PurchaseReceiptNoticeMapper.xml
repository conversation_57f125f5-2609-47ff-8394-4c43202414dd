<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.PurchaseReceiptNoticeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeRespVO">
        SELECT notice.*, purchase.delivery_date
        FROM u_purchase_receipt_notice notice
        LEFT JOIN u_purchase_order purchase ON notice.purchase_order_id = purchase.purchase_order_id
        WHERE notice.receipt_notice_id IS NOT NULL
        <if test="reqVO.receiptNoticeCode != null and reqVO.receiptNoticeCode != ''">
            AND notice.receipt_notice_code LIKE CONCAT('%', #{reqVO.receiptNoticeCode}, '%')
        </if>
        <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
            AND notice.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
        </if>
        <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
            AND notice.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
        </if>
        <if test="reqVO.relatedSupplierId != null">
            AND notice.related_supplier_id = #{reqVO.relatedSupplierId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
            AND (purchase.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
        </if>
        <if test="reqVO.isFullReceipted != null">
            AND notice.is_full_receipted = #{reqVO.isFullReceipted}
        </if>
        <if test="reqVO.dataStatus != null">
            AND notice.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND notice.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND notice.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
            AND (notice.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
        </if>
        ORDER BY notice.receipt_notice_id DESC
    </select>
</mapper>
