<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.detail.PurchaseReceiptNoticeDetailMapper">


    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <update id="updatePurchaseDetailPurchaseQty">
        UPDATE u_purchase_order_detail
        <if test="operate != null">
            SET operable_qty = (u_purchase_order_detail.operable_qty - b.notice_qty), opered_qty = (u_purchase_order_detail.opered_qty + b.notice_qty)
        </if>
        <if test="operate == null">
            SET operable_qty = (u_purchase_order_detail.operable_qty + b.notice_qty), opered_qty = (u_purchase_order_detail.opered_qty - b.notice_qty)
        </if>
        FROM u_purchase_receipt_notice_detail b
        WHERE u_purchase_order_detail.material_id = b.material_id
        AND b.receipt_notice_id = #{item.receiptNoticeId}
        AND u_purchase_order_detail.purchase_order_id = #{item.relatedOrderId};
    </update>

    <select id="selectNoticeQtyListByPurchaseId"
            resultType="com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDetailDO">
        SELECT detail.material_id, SUM(detail.notice_qty) AS noticeQty
        FROM u_purchase_receipt_notice_detail detail
        LEFT JOIN u_purchase_receipt_notice notice ON detail.receipt_notice_id = notice.receipt_notice_id
        WHERE notice.purchase_order_id = #{purchaseOrderId}
        GROUP BY detail.material_id
    </select>

    <select id="getReceiptNoticeIsApproveAble" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDO">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.operable_qty &lt; b.notice_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_purchase_order_detail a
                 LEFT JOIN u_purchase_receipt_notice_detail b ON a.material_id = b.material_id
        WHERE b.receipt_notice_id = #{item.receiptNoticeId}
          AND a.purchase_order_id = #{item.purchaseOrderId}
    </select>

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO">
        SELECT detail.*, notice.*, purchase.delivery_date
        FROM u_purchase_receipt_notice_detail detail
        LEFT JOIN u_purchase_receipt_notice notice ON detail.receipt_notice_id = notice.receipt_notice_id
        LEFT JOIN u_purchase_order purchase ON purchase.purchase_order_id = notice.purchase_order_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        WHERE detail.receipt_notice_detail_id IS NOT NULL
        <if test="reqVO.receiptNoticeCode != null and reqVO.receiptNoticeCode != ''">
            AND notice.receipt_notice_code LIKE CONCAT('%', #{reqVO.receiptNoticeCode}, '%')
        </if>
        <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
            AND notice.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
        </if>
        <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
            AND notice.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
        </if>
        <if test="reqVO.relatedSupplierId != null">
            AND notice.related_supplier_id = #{reqVO.relatedSupplierId}
        </if>
        <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
            AND (purchase.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.isMaterialFullReceipted != null">
            AND notice.is_material_full_receipted = #{reqVO.isMaterialFullReceipted}
        </if>
        <if test="reqVO.dataStatus != null">
            AND notice.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND notice.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND notice.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
            AND (notice.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
        </if>
        ORDER BY notice.receipt_notice_id DESC, detail.row_no ASC
    </select>

    <select id="queryList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO"
            parameterType="com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailQueryReqVO">
        SELECT noticedet.*, purchasedet.operable_qty
        FROM u_purchase_receipt_notice_detail noticedet
        LEFT JOIN u_purchase_order_detail purchasedet ON purchasedet.material_id = noticedet.material_id
        LEFT JOIN u_purchase_receipt_notice notice ON notice.purchase_order_id = purchasedet.purchase_order_id AND notice.receipt_notice_id = noticedet.receipt_notice_id
        WHERE notice.receipt_notice_id = #{reqVO.receiptNoticeId}
        <if test="reqVO.isMaterialFullReceipted != null">
            AND noticedet.is_material_full_receipted = #{reqVO.isMaterialFullReceipted}
        </if>
    </select>

    <select id="receiptableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT noticedet.receipt_notice_detail_id as fieldId, noticedet.material_code as code,
            noticedet.receiptable_qty as sumQty
        FROM u_purchase_receipt_notice_detail noticedet
        WHERE noticedet.receipt_notice_id = #{receiptNoticeId}
    </select>

</mapper>
