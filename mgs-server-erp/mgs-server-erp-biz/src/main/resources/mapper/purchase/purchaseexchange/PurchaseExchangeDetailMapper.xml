<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.purchase.dal.mysql.purchaseexchange.PurchaseExchangeDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getExchangeIsApproveAble" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN SUM(CASE WHEN a.completeable_qty &lt; b.total_exchange_qty THEN 1 ELSE 0 END) = 0 THEN 1
                ELSE 0
                END AS result
        FROM u_purchase_order_detail a
        LEFT JOIN (
            SELECT
                material_id,
                SUM(exchange_qty) AS total_exchange_qty
            FROM u_purchase_exchange_detail
            WHERE purchase_exchange_id = #{item.purchaseExchangeId}
            GROUP BY material_id
        ) b ON a.material_id = b.material_id
        WHERE a.purchase_order_id = #{item.purchaseOrderId}
          AND b.material_id IS NOT NULL
    </select>
    <select id="queryList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailRespVO">
        SELECT exchangedet.*, purchasedet.completeable_qty
        FROM erp.u_purchase_exchange_detail exchangedet
        LEFT JOIN erp.u_purchase_order_detail purchasedet ON purchasedet.purchase_order_detail_id = exchangedet.purchase_order_detail_id
        WHERE exchangedet.purchase_exchange_id = #{reqVO.purchaseExchangeId}
        <if test="reqVO.isMaterialFullInbounded != null">
            AND exchangedet.is_material_full_inbounded = #{reqVO.isMaterialFullInbounded}
        </if>
    </select>
    <select id="inboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.purchase_exchange_detail_id as fieldId, m.material_code as code, detail.material_id as materialId, detail.inboundable_qty as sumQty
        FROM erp.u_purchase_exchange_detail detail
                 LEFT JOIN erp.u_material m on m.material_id = detail.material_id AND m.deleted = 0
        WHERE detail.purchase_exchange_id = #{exchangeId}
    </select>
    <select id="outboundableQtyList"
            resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT detail.purchase_exchange_detail_id as fieldId, m.material_code as code, detail.material_id as materialId, detail.outboundable_qty as sumQty
        FROM erp.u_purchase_exchange_detail detail
                 LEFT JOIN erp.u_material m on m.material_id = detail.material_id AND m.deleted = 0
        WHERE detail.purchase_exchange_id = #{exchangeId}
    </select>
    <select id="queryPage"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailRespVO">
        SELECT detail.*, exchange.*
        FROM u_purchase_exchange exchange
        JOIN u_purchase_exchange_detail detail ON detail.purchase_exchange_id = exchange.purchase_exchange_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.purchaseExchangeCode != null and reqVO.purchaseExchangeCode != ''">
                AND exchange.purchase_exchange_code LIKE CONCAT('%', #{reqVO.purchaseExchangeCode}, '%')
            </if>
            <if test="reqVO.purchaseOrderCode != null and reqVO.purchaseOrderCode != ''">
                AND exchange.purchase_order_code LIKE CONCAT('%', #{reqVO.purchaseOrderCode}, '%')
            </if>
            <if test="reqVO.purchaseTypeDictId != null and reqVO.purchaseTypeDictId != ''">
                AND exchange.purchase_type_dict_id = #{reqVO.purchaseTypeDictId}
            </if>
            <if test="reqVO.relatedSupplierId != null">
                AND exchange.related_supplier_id = #{reqVO.relatedSupplierId}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
                AND (exchange.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
            </if>
            <if test="reqVO.startExchangeDate != null and reqVO.endExchangeDate != null">
                AND (exchange.exchange_date BETWEEN #{reqVO.startExchangeDate} AND #{reqVO.endExchangeDate})
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND exchange.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND exchange.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND exchange.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (exchange.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
        </where>
        ORDER BY exchange.purchase_exchange_id DESC, detail.row_no ASC
    </select>
    <select id="outboundDetailQuotedList"
            resultType="com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailRespVO">
        SELECT detail.*, stock.warehouse_org_id, stock.material_stock_id, stock.stock_qty, stock.locked_qty, stock.available_qty stockableQty,
               b.purchase_exchange_code, b.purchase_order_id, b.purchase_order_code, b.purchase_type_dict_id, b.related_supplier_id, b.delivery_date, b.exchange_date,
               b.director_id, b.director_org_id, b.approved_by, b.approved_dt, b.data_status, b.form_dt, m.material_name, m.material_category_dict_id,
               m.spec_model, m.spec_attribute_str, m.main_unit_dict_id
        FROM u_purchase_exchange_detail detail
                 LEFT JOIN u_purchase_exchange b ON detail.purchase_exchange_id = b.purchase_exchange_id
                 LEFT JOIN u_material m on m.material_id = detail.material_id AND m.deleted = 0
                 LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id
        WHERE detail.purchase_exchange_id = #{purchaseExchangeId}
          AND detail.is_material_full_outbounded = 0
    </select>
</mapper>
