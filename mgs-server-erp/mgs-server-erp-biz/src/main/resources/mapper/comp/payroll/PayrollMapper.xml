<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.comp.payroll.dal.mysql.payroll.PayrollMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updatePayrollStat">
         UPDATE u_payroll a
         SET (number_of_employee, total_payable_amt, total_paid_amt, total_tax_amt) =
         (
            SELECT
                    COUNT(1),
                    COALESCE(SUM(payable_amt), 0),
                    COALESCE(SUM(paid_amt), 0),
                    COALESCE(SUM(tax), 0)
            FROM u_payroll_member b
            WHERE a.payroll_id = b.payroll_id
         )
         WHERE a.payroll_id = #{payrollId}
    </update>
</mapper>
