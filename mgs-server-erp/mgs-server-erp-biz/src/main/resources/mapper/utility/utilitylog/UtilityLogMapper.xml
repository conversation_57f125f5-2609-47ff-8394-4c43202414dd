<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.utility.dal.mysql.utilitylog.UtilityLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectUtilityLogDetail" resultType="com.mongoso.mgs.module.utility.controller.admin.utilitylog.vo.UtilityLogRespVO">
        select ul.*,uc.utility_name utilityConfigName from u_utility_log ul
            left join u_utility_config uc
                on ul.utility_config_id = uc.utility_config_id
        where ul.utility_log_id = #{utilityLogId}
    </select>

    <select id="selectUtilityLogPage" resultType="com.mongoso.mgs.module.utility.controller.admin.utilitylog.vo.UtilityLogRespVO">
        select ul.*,uc.utility_name utilityConfigName from u_utility_log ul
            left join u_utility_config uc
            on ul.utility_config_id = uc.utility_config_id
        where 1=1
        <if test="reqVo.companyId != null">
            and ul.company_id = #{reqVo.companyId}
        </if>
        <if test="reqVo.utilityConfigId != null">
            and ul.utility_config_id = #{reqVo.utilityConfigId}
        </if>
        <if test="reqVo.utilityArchivesId != null">
            and ul.utility_archives_id = #{reqVo.utilityArchivesId}
        </if>
        <if test="reqVo.dataStatus != null">
            and ul.data_status = #{reqVo.dataStatus}
        </if>
        <if test="reqVo.startReadTime != null">
            and ul.read_time between #{reqVo.startReadTime} and #{reqVo.endReadTime}
        </if>
        <if test="reqVo.directorOrgId != null and reqVo.directorOrgId != ''">
            and ul.director_org_id = #{reqVo.directorOrgId}
        </if>
        <if test="reqVo.directorId != null">
            and ul.director_id = #{reqVo.directorId}
        </if>
        <if test="reqVo.utilityLogCode != null and reqVo.utilityLogCode != ''">
            and ul.utility_log_code like concat('%', #{reqVo.utilityLogCode}, '%')
        </if>
        order by ul.utility_log_id desc
--         order by ul.utility_archives_id asc, ul.created_dt desc
    </select>

</mapper>
