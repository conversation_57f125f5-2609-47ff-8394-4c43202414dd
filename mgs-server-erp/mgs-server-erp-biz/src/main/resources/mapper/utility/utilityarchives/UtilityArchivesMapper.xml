<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.utility.dal.mysql.utilityarchives.UtilityArchivesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectUtilityArchivesPage" resultType="com.mongoso.mgs.module.utility.controller.admin.utilityarchives.vo.UtilityArchivesRespVO">
        select
            ua.*,
            uc.utility_name utilityConfigName
        from u_utility_archives ua
            left join u_utility_config uc
            on ua.utility_config_id = uc.utility_config_id
        where 1=1
        <if test="reqVo.companyId != null">
            and ua.company_id = #{reqVo.companyId}
        </if>
        <if test="reqVo.companyIdList != null and reqVo.companyIdList.size > 0 ">
            AND ua.company_id IN
            <foreach item="item" index="index" collection="reqVo.companyIdList"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVo.utilityConfigId != null">
            and uc.utility_config_id = #{reqVo.utilityConfigId}
        </if>
        <if test="reqVo.directorOrgId != null and reqVo.directorOrgId != ''">
            and ua.director_org_id = #{reqVo.directorOrgId}
        </if>
        <if test="reqVo.directorId != null">
            and ua.director_id = #{reqVo.directorId}
        </if>
        <if test="reqVo.costEnable != null">
            and ua.cost_enable = #{reqVo.costEnable}
        </if>
        <if test="reqVo.dataStatus != null">
            and ua.data_status = #{reqVo.dataStatus}
        </if>
        <if test="reqVo.startFormDt != null">
            and ua.form_dt between #{reqVo.startFormDt} and #{reqVo.endFormDt}
        </if>
        <if test="reqVo.utilityArchivesCode != null and reqVo.utilityArchivesCode != ''">
            and ua.utility_archives_code like concat('%', #{reqVo.utilityArchivesCode}, '%')
        </if>
        <if test="reqVo.archivesName != null and reqVo.archivesName != ''">
            and ua.archives_name like concat('%', #{reqVo.archivesName}, '%')
        </if>
        order by ua.utility_archives_id desc
    </select>

    <select id="selectListByCompanyId" resultType="com.mongoso.mgs.module.utility.dal.db.utilityarchives.UtilityArchivesDO">
        SELECT
        *
        FROM
        erp.u_utility_archives
        WHERE 1=1
        <if test="utilityConfigId != null">
            and utility_config_id = #{utilityConfigId}
        </if>
        <if test="companyIdList != null and companyIdList.size > 0 ">
          and company_id in
          <foreach item="item" index="index" collection="companyIdList"  open="(" separator="," close=")">
              #{item}
          </foreach>
        </if>
    </select>
</mapper>
