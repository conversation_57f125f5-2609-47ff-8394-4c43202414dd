<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.utility.dal.mysql.utilitycost.UtilityCostMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectMonthPage" resultType="com.mongoso.mgs.module.utility.controller.admin.utilitycost.vo.UtilityCostRespVO">
        SELECT
        utility_archives_id,
        utility_config_id,
        company_id,
        TO_CHAR(utility_cost_date, 'YYYY-MM') AS month_date,
        SUM(today_usage) AS today_usage,
        SUM(utility_amt) AS utility_amt
        FROM
        erp.u_utility_cost
         WHERE  1=1
        <if test="reqVO.companyId != null">
            and company_id = #{reqVO.companyId}
        </if>
        <if test="reqVO.utilityConfigId != null">
            and utility_config_id = #{reqVO.utilityConfigId}
        </if>
        <if test="reqVO.utilityArchivesId != null">
            and utility_archives_id = #{reqVO.utilityArchivesId}
        </if>
        <if test="reqVO.monthDate != null and reqVO.monthDate != ''">
            and TO_CHAR(utility_cost_date, 'YYYY-MM') = #{reqVO.monthDate}
        </if>
        GROUP BY
        utility_archives_id,
        utility_config_id,
        company_id,
        TO_CHAR(utility_cost_date, 'YYYY-MM')
        order by TO_CHAR(utility_cost_date, 'YYYY-MM') desc
    </select>
</mapper>
