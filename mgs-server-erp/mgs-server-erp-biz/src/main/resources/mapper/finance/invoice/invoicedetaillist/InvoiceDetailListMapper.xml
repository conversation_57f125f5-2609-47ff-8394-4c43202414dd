<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicedetaillist.InvoiceDetailListMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="invoiceDetailStatics" resultType="com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.resp.InvoiceDetailMsgResp">
        SELECT
            A.invoiceDetailListId,
            u.invoice_detail_id,
            u.material_code,
            u.material_id,
            u.main_unit_dict_id,
            u.row_no,
            A.qty,
            u.incl_tax_unit_price,
            u.excl_tax_unit_price,
            u.invoice_type_dict_id,
            u.tax_rate,
            A.taxAmt,
            A.invoiceAmtInclTax,
            A.invoiceAmtExclTax
        FROM
            (
                SELECT
                    uid.material_code,
                    MAX ( uid.invoice_detail_list_id ) invoiceDetailListId,
                    COALESCE ( SUM ( uid.qty ), 0 ) qty,
                    COALESCE ( SUM ( uid.tax_amt ), 0 ) taxAmt,
                    COALESCE ( SUM ( uid.invoice_amt_incl_tax ), 0 ) invoiceAmtInclTax,
                    COALESCE ( SUM ( uid.invoice_amt_excl_tax ), 0 ) invoiceAmtExclTax
                FROM
                    u_invoice_detail_list uid
                WHERE
                    uid.invoice_id = #{invoiceId}
                GROUP BY
                    uid.material_code
            )
                A LEFT JOIN u_invoice_detail_list u ON A.invoiceDetailListId = u.invoice_detail_list_id
                ORDER BY u.created_dt asc
    </select>
</mapper>
