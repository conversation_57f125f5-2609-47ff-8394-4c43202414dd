<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapplydetail.InvoiceApplyDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="invoiceApplyQty" resultType="java.math.BigDecimal">
        select sum(remaining_apply_amt) from u_invoice_apply_detail where invoice_apply_id = #{invoiceApplyId}
    </select>

    <delete id="deleteByinvoicePlanIds">
        delete from u_invoice_apply_detail
        where invoice_apply_id in (
            select invoice_apply_id from u_invoice_apply
            <where>
                invoice_plan_id IN
                <foreach item="item" index="index" collection="invoicePlanIds"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </where>
        )
    </delete>
</mapper>
