<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceissue.InvoiceIssueMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="redInvoiceAmt" resultType="java.math.BigDecimal">
        select sum(invoice_total_tax) from u_invoice_issue where red_invoice_id = #{invoiceId} and data_status = 1
    </select>

    <update id="updateRemainIssueAmtAndQty">

        UPDATE u_invoice_detail ud
        SET
            remaining_invoice_qty = #{qty},
            remaining_invoice_amt = #{amt}
            FROM u_invoice_issue up
        WHERE
            ud.invoice_id = up.invoice_id AND
            up.data_status = 0
            <if test="applyDetailId!=null">
                AND ud.invoice_apply_detail_id = #{applyDetailId};
            </if>
            <if test="panlDetailId!=null">
                AND ud.invoice_plan_detail_id = #{panlDetailId};
            </if>
    </update>

    <delete id="deleteByinvoicePlanIds">
        DELETE FROM u_invoice_issue
        <where>
            invoice_plan_id IN
            <foreach item="item" index="index" collection="invoicePlanIds"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>
</mapper>
