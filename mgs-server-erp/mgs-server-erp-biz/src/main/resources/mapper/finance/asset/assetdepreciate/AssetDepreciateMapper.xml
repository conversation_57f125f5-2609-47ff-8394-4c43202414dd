<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.asset.assetdepreciate.AssetDepreciateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage" resultType="com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateRespVO">
        SELECT a.*, b.asset_name, b.asset_code, b.asset_type_dict_id, b.accrual_period, b.accrual_unit_type, b.asset_purchase_price, b.net_residual_value_rate, b.net_residual_value, b.pre_accrued_period
        , b.pre_accrued_amt,b.depreciations_config_id,b.remaining_period,b.asset_accrued_amt
        FROM u_asset_depreciate a
        LEFT JOIN u_asset_register b ON a.asset_id = b.asset_register_id
        <where>
            <if test="reqVO.assetCode != null and reqVO.assetCode != ''">
                AND b.asset_code LIKE CONCAT('%', #{reqVO.assetCode}, '%')
            </if>
            <if test="reqVO.assetName != null and reqVO.assetName != ''">
                AND b.asset_name LIKE CONCAT('%', #{reqVO.assetName}, '%')
            </if>
            <if test="reqVO.accrualStartMonth != null and reqVO.accrualStartMonth != '' and reqVO.accrualEndMonth != null ">
                AND a.accrual_month >= #{reqVO.accrualStartMonth}
                AND a.accrual_month &lt;= #{reqVO.accrualEndMonth}
            </if>
            <if test="reqVO.assetId != null">
                AND a.asset_id = #{reqVO.assetId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND a.form_dt >= #{reqVO.startFormDt}
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id = #{reqVO.directorOrgId}
            </if>
        </where>
        ORDER BY a.asset_id DESC, a.current_period DESC
    </select>

    <select id="selectBatchIdList" resultType="com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciate.AssetDepreciateDO">
        SELECT a.*
        FROM u_asset_depreciate a
        <where>
            <if test="idList != null and idList.size > 0 ">
                AND a.asset_depreciate_id IN
                <foreach item="item" index="index" collection="idList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="buttonType == 1">
                ORDER BY a.asset_id DESC, a.current_period ASC
            </if>
            <if test="buttonType == 0">
                ORDER BY a.asset_id DESC, a.current_period DESC
            </if>
        </where>

    </select>

    <select id="selectByIdDetail" resultType="com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateRespVO">
        SELECT a.*, b.asset_name, b.asset_type_dict_id, b.accrual_period, b.accrual_unit_type, b.asset_purchase_price, b.net_residual_value_rate, b.net_residual_value, b.pre_accrued_period
        , b.pre_accrued_amt,b.depreciations_config_id,b.remaining_period, b.is_pre_accrued
        FROM u_asset_depreciate a
        LEFT JOIN u_asset_register b ON a.asset_id = b.asset_register_id
        WHERE a.asset_depreciate_id = #{assetDepreciateId}
    </select>

    <delete id="assetDepreciateDel">
        DELETE FROM u_asset_depreciate
        WHERE asset_id = #{assetId}
    </delete>

    <select id="selectIdAndCurrentPeriod" resultType="com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateRespVO">
        SELECT *
        FROM u_asset_depreciate
        WHERE asset_id = #{assetId} and current_period = #{currentPeriod}
    </select>

</mapper>
