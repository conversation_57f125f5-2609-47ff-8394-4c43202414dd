<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.shouldpaymentconf.ShouldPaymentConfMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getAnalysisList" resultType="java.util.Map">
        SELECT
        customer_id,
        currency_dict_id,
        <foreach collection="req.confList" item="conf" separator=",">
            <![CDATA[ SUM(CASE WHEN act_rec_date between '${conf.startDay}' AND '${conf.endDay}' THEN (total_amt - real_amt) ELSE 0 END) as "${conf.title}" ]]>
        </foreach>
        FROM
        erp.u_should_payment
        where data_status = 1
          and form_type = #{req.formType}
        <if test="req.currencyDictId != null and req.currencyDictId != ''">
            AND currency_dict_id = #{req.currencyDictId}
        </if>
        <if test="req.customerIdList != null and req.customerIdList.size > 0">
            AND customer_id IN
            <foreach item="item" index="index" collection="req.customerIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        customer_id,
        currency_dict_id
    </select>

    <select id="salePaymentAnalysis" resultType="com.mongoso.mgs.module.finance.controller.admin.shouldpaymentconf.vo.SalePaymentAnalysisRespVO">
        SELECT
            TO_CHAR(form_dt, 'YYYY-MM') reportMonth,
            customerId,
            currencyDictId,
            SUM(totalReconciliationAmt) AS totalReconciliationAmt,
            SUM(totalAmt) AS totalAmt,
            SUM(realAmt) AS realAmt,
            SUM(totalAmt) - SUM(realAmt) AS unpaidAmt,
            SUM(readyInvoiceAmt) AS readyInvoiceAmt,
            SUM(canInvoiceAmt) AS canInvoiceAmt
        FROM (
            SELECT
            form_dt,
            customer_id AS customerId,
            currency_dict_id AS currencyDictId,
            total_reconciliation_amt AS totalReconciliationAmt,
            0 AS totalAmt,
            0 AS realAmt,
            0 AS unpaidAmt,
            0 AS readyInvoiceAmt,
            0 AS canInvoiceAmt
            FROM u_account_order
            WHERE form_dt >= #{reqVO.startMonth}
            AND form_dt &lt;= #{reqVO.endMonth}
            AND form_type = #{reqVO.formType}
            AND data_status = 1
            <if test="reqVO.customerIdList != null and reqVO.customerIdList.size > 0 ">
                and customer_id IN
                <foreach item="item" index="index" collection="reqVO.customerIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                and currency_dict_id = #{reqVO.currencyDictId}
            </if>
        UNION ALL
            SELECT
            form_dt,
            customer_id AS customerId,
            currency_dict_id AS currencyDictId,
            0 AS totalReconciliationAmt,
            total_amt AS totalAmt,
            real_amt AS realAmt,
            total_amt - real_amt AS unpaidAmt,
            0 AS readyInvoiceAmt,
            0 AS canInvoiceAmt
            FROM u_should_payment
            WHERE form_dt >= #{reqVO.startMonth}
            AND form_dt &lt;= #{reqVO.endMonth}
            AND form_type = #{reqVO.formType}
            AND data_status = 1
            <if test="reqVO.customerIdList != null and reqVO.customerIdList.size > 0 ">
                and customer_id IN
                <foreach item="item" index="index" collection="reqVO.customerIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                and currency_dict_id = #{reqVO.currencyDictId}
            </if>
        UNION ALL
            SELECT
            form_dt,
            customer_id AS customerId,
            currency_dict_id AS currencyDictId,
            0 AS totalReconciliationAmt,
            0 AS totalAmt,
            0 AS realAmt,
            0 AS unpaidAmt,
            ready_invoice_amt AS readyInvoiceAmt,
            can_invoice_amt AS canInvoiceAmt
            FROM u_invoice_plan
            WHERE form_dt >= #{reqVO.startMonth}
            AND form_dt &lt;= #{reqVO.endMonth}
            AND data_status = 1
            <if test="reqVO.formType == 1">
                AND billing_direction = 0
            </if>
            <if test="reqVO.formType == 2">
                AND billing_direction = 1
            </if>
            <if test="reqVO.customerIdList != null and reqVO.customerIdList.size > 0 ">
                and customer_id IN
                <foreach item="item" index="index" collection="reqVO.customerIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.currencyDictId != null and reqVO.currencyDictId != ''">
                and currency_dict_id = #{reqVO.currencyDictId}
            </if>

        ) t
        GROUP BY reportMonth, customerId, currencyDictId
        ORDER BY reportMonth desc
    </select>

</mapper>
