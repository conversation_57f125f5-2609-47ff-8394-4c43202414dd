<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.workbench.FinanceWorkbenchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 销售待对账金额 -->
    <select id="getAccountBalance" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(b.balance), 0)
        FROM u_bank_config a
        JOIN u_account_details b ON a.bank_config_id = b.bank_config_id
        WHERE a.data_status = 1 and a.deleted = 0
        AND b.bank_account_id = (
            SELECT bank_account_id FROM u_account_details c WHERE b.bank_config_id = c.bank_config_id
            ORDER BY c.bank_account_date desc, c.bank_account_id desc
            LIMIT 1
        )
    </select>

    <!-- 销售待对账金额 -->
    <select id="getSalePendingReconcileAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(reconcilable_amt), 0)
        FROM u_settle_pool
        WHERE account_status IN (0, 1)
        AND form_type = 1
    </select>

    <!-- 销售待收款金额 -->
    <select id="getSalePendingPaymentAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_amt - real_amt), 0)
        FROM u_should_payment
        WHERE data_status = 1
        AND form_type = 1
    </select>

    <!-- 采购待对账金额 -->
    <select id="getPurchasePendingReconcileAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(reconcilable_amt), 0)
        FROM u_settle_pool
        WHERE account_status IN (0, 1)
        AND form_type = 2
    </select>

    <!-- 采购待付款金额 -->
    <select id="getPurchasePendingPaymentAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_amt - real_amt), 0)
        FROM u_should_payment
        WHERE data_status = 1
        AND form_type = 2
    </select>

    <!-- 待开发票申请数量 -->
    <select id="getPendingOpenInvApplyCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_invoice_plan
        WHERE data_status = 1
        AND billing_direction = 0
        AND invoice_apply_status IN (0, 2, 3, 5)
    </select>

    <!-- 待收发票申请数量 -->
    <select id="getPendingReceiveInvApplyCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_invoice_plan
        WHERE data_status = 1
        AND billing_direction = 1
        AND invoice_apply_status IN (3, 5)
    </select>

    <!-- 报销待付款金额 -->
    <select id="getReimbursePendingPaymentAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_amt), 0)
        FROM u_fee_reimburse
        WHERE data_status = 1
        AND out_bill_status = 0
    </select>

    <!-- 借款待付款金额 -->
    <select id="getLoanPendingPaymentAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(loan_amt), 0)
        FROM u_fee_loan
        WHERE data_status = 1
        AND out_bill_status = 0
    </select>

    <!-- 银行账户支出收入趋势-->
    <select id="queryBankAccountDetailLineList"
            resultType="com.mongoso.mgs.module.finance.controller.admin.workbench.bo.FinanceLineBO">
        SELECT TO_CHAR(bankAccountDate, 'YYYY-MM-DD') date, SUM(income) income,
            SUM(expenditure) expend
        FROM (
            SELECT a.income, 0 expenditure, a.bank_account_date bankAccountDate
            FROM u_account_details a
            WHERE a.bank_account_date > CURRENT_DATE - INTERVAL '30 days'
            AND a.income > 0
            UNION ALL
            SELECT 0 income, a.expenditure, a.bank_account_date bankAccountDate
            FROM u_account_details a
            WHERE a.bank_account_date > CURRENT_DATE - INTERVAL '30 days'
            AND a.expenditure > 0
        ) t
        GROUP BY TO_CHAR(bankAccountDate, 'YYYY-MM-DD')
    </select>

    <!-- 银行账户明细统计-->
    <select id="queryBankAccountDetailList"
            resultType="com.mongoso.mgs.module.finance.controller.admin.workbench.vo.FinanceAccountDetailsRespVO">
        SELECT b.account_name, b.bank_account, b.currency_dict_id, a.bank_account_date,
            a.account_detail_type, a.income, a.expenditure, a.balance
        FROM u_account_details a
        JOIN u_bank_config b ON a.bank_config_id = b.bank_config_id
        ORDER BY a.bank_account_date DESC
        LIMIT 30
    </select>

    <!-- 固定资产净值 -->
    <select id="getAssetNetValue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(asset_net_value), 0)
        FROM u_asset_register
        WHERE data_status = 1
    </select>

    <!-- 预收款 -->
    <select id="getAdvanceReceivePaymentAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(remaining_amt), 0)
        FROM u_advance_payment
        WHERE data_status = 1
        AND form_type = 1
    </select>

    <!-- 预付款 -->
    <select id="getAdvanceExpensePaymentAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(remaining_amt), 0)
        FROM u_advance_payment
        WHERE data_status = 1
        AND form_type = 2
    </select>

    <!-- 未入账承兑汇票 -->
    <select id="getAcceptBillPendingInBillAmt" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(bill_amt - discount_amt - endorse_amt), 0)
        FROM u_accept_bill
        WHERE data_status = 1
        AND transaction_status = 0
        AND bill_direction = 1
    </select>

</mapper>
