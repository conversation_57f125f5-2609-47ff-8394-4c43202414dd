<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.cashbank.acceptbillreceipt.AcceptBillReceiptMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage" resultType="com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbillreceipt.vo.AcceptBillReceiptRespVO">
        SELECT a.*, b.ticket_bill_package_no, b.sub_ticket_range_Lower, b.sub_ticket_range_upper, b.bill_amt, b.draw_date
        FROM u_accept_bill_receipt a
        LEFT JOIN u_accept_bill b ON a.accept_bill_id = b.accept_bill_id
        <where>
            <if test="reqVO.receiptCode != null and reqVO.receiptCode != ''">
                AND a.receipt_code LIKE CONCAT('%', #{reqVO.receiptCode}, '%')
            </if>
            <if test="reqVO.ticketBillPackageNo != null and reqVO.ticketBillPackageNo != ''">
                AND b.ticket_bill_package_no LIKE CONCAT('%', #{reqVO.ticketBillPackageNo}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND a.form_dt >= #{reqVO.startFormDt}
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id = #{reqVO.directorOrgId}
            </if>
        </where>
        order by a.created_dt desc
    </select>

    <select id="selectByIdDetail" resultType="com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbillreceipt.vo.AcceptBillReceiptRespVO">
        SELECT a.*, b.ticket_bill_package_no, b.sub_ticket_range_Lower, b.sub_ticket_range_upper, b.bill_amt, b.available_amt
        FROM u_accept_bill_receipt a
        LEFT JOIN u_accept_bill b ON a.accept_bill_id = b.accept_bill_id
        WHERE a.receipt_id = #{receiptId}
    </select>
</mapper>
