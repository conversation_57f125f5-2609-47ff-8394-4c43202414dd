<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.cashbank.exchangerate.ExchangeRateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage" resultType="com.mongoso.mgs.module.finance.controller.admin.cashbank.exchangerate.vo.ExchangeRateRespVO">
        SELECT a.*, b.fieldvalue originalCurrencyDictName, c.fieldvalue localCurrencyDictName
        FROM t_exchange_rate a
        JOIN t_all_dict b ON a.original_currency_dict_id = b.id
        JOIN t_all_dict c ON a.local_currency_dict_id = c.id
        <where>
            <if test="reqVO.originalCurrencyDictId != null and reqVO.originalCurrencyDictId != ''">
                AND a.original_currency_dict_id = #{reqVO.originalCurrencyDictId}
            </if>
            <if test="reqVO.localCurrencyDictId != null and reqVO.localCurrencyDictId != ''">
                AND a.local_currency_dict_id = #{reqVO.localCurrencyDictId}
            </if>
            <if test="reqVO.originalCurrencyDictName != null and reqVO.originalCurrencyDictName != ''">
                AND b.fieldvalue LIKE CONCAT('%', #{reqVO.originalCurrencyDictName}, '%')
            </if>
            <if test="reqVO.localCurrencyDictName != null and reqVO.localCurrencyDictName != ''">
                AND c.fieldvalue LIKE CONCAT('%', #{reqVO.localCurrencyDictName}, '%')
            </if>
        </where>
    </select>

</mapper>
