<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.cashbank.acceptbill.AcceptBillMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateValueNull">
        UPDATE u_accept_bill
        <if test="billDirection == 1">
            SET payee_id = NULL, payee_bank = NULL, payee_account = NULL
        </if>
        <if test="billDirection == 0">
            SET drawer_id = NULL, drawer_bank = NULL, drawer_account = NULL, transfer_status = NULL, discount_status = NULL
        </if>
        WHERE accept_bill_id = #{acceptBillId}
    </update>
</mapper>
