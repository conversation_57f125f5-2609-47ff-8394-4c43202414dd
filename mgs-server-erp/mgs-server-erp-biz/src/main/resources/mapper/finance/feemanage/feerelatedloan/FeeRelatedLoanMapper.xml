<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.feemanage.feerelatedloan.FeeRelatedLoanMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectList" resultType="com.mongoso.mgs.module.finance.controller.admin.feemanage.feerelatedloan.vo.FeeRelatedLoanRespVO">
        SELECT a.*,
        <if test="reqVO.feeReimbursePaymentId != null">
            b.loan_name, b.loan_amt, b.un_return_amt, b.loaner_id, b.form_dt
        </if>
        <if test="reqVO.feeLoanId != null">
            c.out_bill_amt, c.deduction_total_amt,c.form_dt, d.currency_dict_id, d.reimburser_id, d.total_amt
        </if>
        FROM u_fee_related_loan a
            JOIN u_fee_loan b ON a.fee_loan_id = b.fee_loan_id
            JOIN u_fee_reimburse_payment c ON a.fee_reimburse_payment_id = c.fee_reimburse_payment_id
            JOIN u_fee_reimburse d ON c.fee_reimburse_id = d.fee_reimburse_id
        <where>
            <if test="reqVO.feeReimbursePaymentId != null">
                AND a.fee_reimburse_payment_id = #{reqVO.feeReimbursePaymentId}
            </if>
            <if test="reqVO.feeLoanId != null">
                AND a.fee_loan_id = #{reqVO.feeLoanId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
        </where>
        ORDER BY a.created_dt DESC
    </select>

    <delete id="deletebByReimbursePaymentId">
        DELETE FROM u_fee_related_loan
        WHERE fee_reimburse_payment_id = #{feeReimbursePaymentId}
    </delete>
</mapper>
