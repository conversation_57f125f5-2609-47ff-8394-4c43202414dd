<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.feemanage.feereimbursepayment.FeeReimbursePaymentMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage" resultType="com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimbursepayment.vo.FeeReimbursePaymentRespVO">
        SELECT a.*, b.currency_dict_id, b.total_amt, b.reimburser_id
        FROM u_fee_reimburse_payment a
        LEFT JOIN u_fee_reimburse b ON a.fee_reimburse_id = b.fee_reimburse_id
        <where>
            <if test="reqVO.reimbursePaymentCode != null and reqVO.reimbursePaymentCode != ''">
                AND a.reimburse_payment_code LIKE CONCAT('%', #{reqVO.reimbursePaymentCode}, '%')
            </if>
            <if test="reqVO.reimburseCode != null and reqVO.reimburseCode != ''">
                AND a.reimburse_code LIKE CONCAT('%', #{reqVO.reimburseCode}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null ">
                AND a.form_dt >= #{reqVO.startFormDt}
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id = #{reqVO.directorOrgId}
            </if>
        </where>
        order by a.created_dt desc
    </select>

    <select id="selectByIdDetail" resultType="com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimbursepayment.vo.FeeReimbursePaymentRespVO">
        SELECT a.*, b.currency_dict_id, b.total_amt, b.reimburser_id
        FROM u_fee_reimburse_payment a
        LEFT JOIN u_fee_reimburse b ON a.fee_reimburse_id = b.fee_reimburse_id
        WHERE a.fee_reimburse_payment_id = #{feeReimbursePaymentId}
    </select>
</mapper>
