<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.finance.dal.mysql.accountdetails.AccountDetailsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="accountDetailUpdate" parameterType="java.lang.Object">
        update
            u_account_details
        set balance = balance + #{amt}
        where bank_config_id = #{bankConfigId}
    </update>

    <update id="accountDetailDelToUpdate" parameterType="java.lang.Object">
        update
            u_account_details
        set balance = balance + #{amt}
        where bank_config_id = #{bankConfigId}
          and bank_account_date > #{bankAccountDate}
          and account_detail_type != 0
    </update>

    <select id="refreshBalance" resultType="java.math.BigDecimal">
        select balance from u_account_details where bank_account_date &lt;= #{bankAccountDate} and account_detail_type != 0 and bank_config_id = #{bankConfigId} order by bank_account_date desc, bank_account_id desc limit 1
    </select>

    <update id="updateBalanceToLastTime">
        update u_account_details set balance = balance + #{amt} where bank_account_date > #{bankAccountDate} and bank_config_id = #{bankConfigId} and account_detail_type != 0
    </update>

    <select id="selectStatistics"
            resultType="com.mongoso.mgs.module.finance.controller.admin.accountdetails.resp.BankAccountSummaryQueryResp">
        select uc.bank_config_id,
        COALESCE(a.currentExpenditure,0) currentExpenditure,
        COALESCE(a.currentIncome,0) currentIncome
        from u_bank_config uc
            left join
        (
        SELECT
        ud.bank_config_id bankConfigId,
        COALESCE(SUM ( ud.income ),0) currentIncome,
        COALESCE(SUM ( ud.expenditure ),0) currentExpenditure,
        ud.tenant_id
        FROM
        u_account_details ud
        WHERE ud.bank_account_date >= #{req.startDate}
        and ud.bank_account_date &lt;= #{req.endDate}
        GROUP BY
        ud.bank_config_id,ud.tenant_id
        order by ud.bank_config_id desc) a on uc.bank_config_id = a.bankConfigId
        where uc.data_status = 1 and uc.deleted = 0
        order by uc.created_dt desc
    </select>

    <select id="findBeginningBalance" resultType="com.mongoso.mgs.module.finance.controller.admin.accountdetails.vo.AccountBalanceVO">
        select COALESCE(b.balance,a.balance) beginningBalance,
               COALESCE(c.balance, 0) manualBeginningBalance,
               uc.account_name,
               uc.bank_account,
               uc.currency_dict_id
            from
                u_bank_config uc left join
        (select
            COALESCE(balance,0) balance,
            bank_config_id
        from u_account_details
        where bank_config_id = #{bankConfigId}
        and bank_account_date &lt; #{beginTime}
        order by bank_account_date desc, bank_account_id desc
        limit 1) a on a.bank_config_id = uc.bank_config_id
            left join
        (select
            balance,
            bank_config_id
        from u_account_details
        where bank_config_id = #{bankConfigId}
        and bank_account_date >= #{beginTime} and bank_account_date &lt;= #{endTime}
        and account_detail_type = 0) b on uc.bank_config_id = b.bank_config_id
        left join u_account_details c on uc.bank_config_id = c.bank_config_id and c.account_detail_type = 0
        where uc.bank_config_id = #{bankConfigId}
    </select>

    <select id="selectBatchBankIdList" resultType="com.mongoso.mgs.module.finance.dal.db.accountdetails.AccountDetailsDO">
        SELECT * FROM u_account_details AS a
        WHERE bank_account_date = (
        SELECT MAX(b.bank_account_date) FROM u_account_details AS b WHERE b.bank_config_id = a.bank_config_id
        AND b.bank_config_id IN
        <if test="bankIdList != null and bankIdList.size > 0 ">
            <foreach item="item" index="index" collection="bankIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <select id="accountBankList" resultType="com.mongoso.mgs.module.finance.controller.admin.accountdetails.resp.BankAccountListResp"  >
        SELECT
            bc.account_name,
            bc.bank_account,
            bc.currency_dict_id,
            bc.bank_config_id,
            COALESCE ( uad.balance, 0 ) balance
        FROM
            u_bank_config bc
                LEFT JOIN (
                SELECT
                    ud.bank_config_id,
                    ud.tenant_id,
                    MAX ( ud.bank_account_id ) dataId
                FROM
                    u_account_details ud
                GROUP BY
                    ud.bank_config_id,
                    ud.tenant_id
            ) de ON bc.bank_config_id = de.bank_config_id
                LEFT JOIN u_account_details uad ON uad.bank_account_id = de.dataId
        WHERE
            bc.data_status = 1
        ORDER BY
            bc.created_dt DESC
    </select>


    <select id="selectListBy3"
            resultType="com.mongoso.mgs.module.ai.controller.admin.finance.vo.AccountBalanceVOAI8">
        select sum(income) As income,sum(expenditure) As expenditure,sum(balance) As balance
        from u_account_details
        WHERE bank_account_date >= #{startDt} AND bank_account_date &lt;= #{endDt}
    </select>


</mapper>
