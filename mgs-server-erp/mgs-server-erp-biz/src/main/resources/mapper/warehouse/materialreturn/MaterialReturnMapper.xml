<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialreturn.MaterialReturnMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryMaterialReturnPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.MaterialReturnRespVO">
        SELECT a.*, b.loan_name, b.loan_type_dict_id, b.loan_type, b.loan_obj_type, b.loan_obj_id,
            b.loan_org_id, b.contact_name, b.contact_phone
        FROM u_material_return a
        LEFT JOIN u_material_loan b ON a.loan_id = b.loan_id
        <where>
            <if test="reqVO.returnCode != null and reqVO.returnCode != ''">
                AND a.return_code LIKE concat('%', #{reqVO.returnCode}, '%')
            </if>
            <if test="reqVO.returnName != null and reqVO.returnName != ''">
                AND a.return_name LIKE concat('%', #{reqVO.returnName}, '%')
            </if>
            <if test="reqVO.returnTypeDictId != null and reqVO.returnTypeDictId != ''">
                AND a.return_type_dict_id = #{reqVO.returnTypeDictId}
            </if>
            <if test="reqVO.loanCode != null and reqVO.loanCode != ''">
                AND b.loan_code LIKE concat('%', #{reqVO.loanCode}, '%')
            </if>
            <if test="reqVO.loanName != null and reqVO.loanName != ''">
                AND b.loan_name LIKE concat('%', #{reqVO.loanName}, '%')
            </if>
            <if test="reqVO.loanTypeDictId != null and reqVO.loanTypeDictId != ''">
                AND b.loan_type_dict_id = #{reqVO.loanTypeDictId}
            </if>
            <if test="reqVO.loanType != null">
                AND b.loan_type = #{reqVO.loanType}
            </if>
            <if test="reqVO.loanObjId != null">
                AND b.loan_obj_id = #{reqVO.loanObjId}
            </if>
            <if test="reqVO.loanOrgId != null and reqVO.loanOrgId != ''">
                AND b.loan_org_id = #{reqVO.loanOrgId}
            </if>
            <if test="reqVO.contactName != null and reqVO.contactName != ''">
                AND b.contact_name LIKE concat('%', #{reqVO.contactName}, '%')
            </if>
            <if test="reqVO.contactPhone != null and reqVO.contactPhone != ''">
                AND b.contact_phone LIKE concat('%', #{reqVO.contactPhone}, '%')
            </if>
            <if test="reqVO.startReturnDt != null">
                AND a.return_dt >= #{reqVO.startReturnDt}
            </if>
            <if test="reqVO.endReturnDt != null">
                AND a.return_dt &lt;= #{reqVO.endReturnDt}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc
    </select>

    <select id="queryMaterialReturnDetail"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.MaterialReturnRespVO">
       SELECT a.*, b.loan_name, b.loan_type_dict_id, b.loan_type, b.loan_obj_type, b.loan_obj_id,
            b.loan_org_id, b.contact_name, b.contact_phone
        FROM u_material_return a
        LEFT JOIN u_material_loan b ON a.loan_id = b.loan_id
        WHERE a.return_id = #{returnId}
    </select>

</mapper>
