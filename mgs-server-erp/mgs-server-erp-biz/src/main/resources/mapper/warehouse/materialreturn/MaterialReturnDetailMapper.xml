<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialreturn.MaterialReturnDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryMaterialReturnDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.detail.MaterialReturnDetailRespVO">
        SELECT b.remark, a.*, l.loan_name, l.loan_type_dict_id, l.loan_type, l.loan_obj_type, l.loan_obj_id,
            l.loan_org_id, l.contact_name, l.contact_phone, b.row_no, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.return_qty, b.remark, b.material_code, c.material_name,
            c.material_category_dict_id, c.spec_model, c.spec_attribute_str, ld.row_no relatedRowNo
        FROM u_material_return a
        LEFT JOIN u_material_loan l ON a.loan_id = l.loan_id
        LEFT JOIN u_material_return_detail b ON a.return_id = b.return_id
        LEFT JOIN u_material_loan_detail ld ON b.loan_detail_id = ld.loan_detail_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.returnCode != null and reqVO.returnCode != ''">
                AND a.return_code LIKE concat('%', #{reqVO.returnCode}, '%')
            </if>
            <if test="reqVO.returnName != null and reqVO.returnName != ''">
                AND a.return_name LIKE concat('%', #{reqVO.returnName}, '%')
            </if>
            <if test="reqVO.returnTypeDictId != null and reqVO.returnTypeDictId != ''">
                AND a.return_type_dict_id = #{reqVO.returnTypeDictId}
            </if>
            <if test="reqVO.startReturnDt != null">
                AND a.return_dt >= #{reqVO.startReturnDt}
            </if>
            <if test="reqVO.endReturnDt != null">
                AND a.return_dt &lt;= #{reqVO.endReturnDt}
            </if>
            <if test="reqVO.loanCode != null and reqVO.loanCode != ''">
                AND l.loan_code LIKE concat('%', #{reqVO.loanCode}, '%')
            </if>
            <if test="reqVO.loanName != null and reqVO.loanName != ''">
                AND l.loan_name LIKE concat('%', #{reqVO.loanName}, '%')
            </if>
            <if test="reqVO.loanTypeDictId != null and reqVO.loanTypeDictId != ''">
                AND l.loan_type_dict_id = #{reqVO.loanTypeDictId}
            </if>
            <if test="reqVO.loanType != null">
                AND l.loan_type = #{reqVO.loanType}
            </if>
            <if test="reqVO.loanObjId != null">
                AND l.loan_obj_id = #{reqVO.loanObjId}
            </if>
            <if test="reqVO.loanOrgId != null and reqVO.loanOrgId != ''">
                AND l.loan_org_id = #{reqVO.loanOrgId}
            </if>
            <if test="reqVO.contactName != null and reqVO.contactName != ''">
                AND l.contact_name LIKE concat('%', #{reqVO.contactName}, '%')
            </if>
            <if test="reqVO.contactPhone != null and reqVO.contactPhone != ''">
                AND l.contact_phone LIKE concat('%', #{reqVO.contactPhone}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND a.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="queryMaterialReturnDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.detail.MaterialReturnDetailRespVO">
        SELECT b.return_detail_id, b.loan_detail_id, b.row_no, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.return_qty, b.remark, b.material_code, c.material_name,
            c.material_category_dict_id, c.spec_model, c.spec_attribute_str,
            ld.row_no relatedRowNo
        FROM u_material_return_detail b
        LEFT JOIN u_material_loan_detail ld ON b.loan_detail_id = ld.loan_detail_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.returnId != null">
                AND b.return_id = #{reqVO.returnId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="returnQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.loan_detail_id as fieldId, b.material_code as code,
            ld.material_stock_id, b.warehouse_org_id, b.return_qty as sumQty
        FROM u_material_return_detail b
        LEFT JOIN u_material_loan_detail ld ON b.loan_detail_id = ld.loan_detail_id
        WHERE b.return_id = #{returnId}
    </select>

    <select id="returnMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.return_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_material_return_detail b
        WHERE b.return_id IN
        <foreach item="item" index="index" collection="returnIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.return_id, b.material_id, b.warehouse_org_id
    </select>

</mapper>
