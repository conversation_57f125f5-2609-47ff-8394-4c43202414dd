<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory.ErpInventoryDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailRespVO">
        SELECT detail.*, (COALESCE(stock.stock_qty, 0) - COALESCE(stock.locked_qty, 0)) adjustableQty, inventory.warehouse_org_id
        FROM erp.u_inventory inventory
        LEFT JOIN erp.u_inventory_detail detail ON detail.inventory_id = inventory.inventory_id
        LEFT JOIN erp.u_material_stock stock ON detail.material_id = stock.material_id AND detail.warehouse_org_id = stock.warehouse_org_id
        LEFT JOIN erp.u_material material ON detail.material_id = material.material_id
        <where>
            AND detail.inventory_qty != detail.act_inventory_qty
            AND detail.inventory_id = #{reqVO.inventoryId}
            <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
                AND detail.material_id NOT IN
                <foreach collection="reqVO.exclMaterialIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND inventory.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND detail.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND material.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND material.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
        </where>
        ORDER BY detail.inventory_detail_id DESC
    </select>
</mapper>
