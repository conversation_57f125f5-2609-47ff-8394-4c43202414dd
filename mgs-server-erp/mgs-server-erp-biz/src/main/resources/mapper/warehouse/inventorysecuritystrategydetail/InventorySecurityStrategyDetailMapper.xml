<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.inventorysecuritystrategydetail.InventorySecurityStrategyDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage3"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategydetail.vo.InventorySecurityStrategyDetailRespVO">
        SELECT
            t1.inventory_security_strategy_id,
            t1.inventory_security_strategy_code,
            t1.inventory_security_strategy_name,
            t1.director_id,
            t1.director_org_id,
            t1.warehouse_org_id,
            t1.form_dt,
            t1.created_by,
            t1.created_dt,
            t1.updated_by,
            t1.updated_dt,
            t1.approved_by,
            t1.approved_dt,
            t1.data_status,
            t2.material_id,
            t2.material_code,
            t2.min_value,
            t2.max_value,
            t2.row_no,
            t2.remark,
            t3.material_name,
            t3.material_category_dict_id,
            t3.material_source_dict_id,
            t3.main_unit_dict_id,
            t3.spec_model,
            t3.spec_attribute_str
        FROM u_inventory_security_strategy t1
        JOIN u_inventory_security_strategy_detail t2 ON t1.inventory_security_strategy_id = t2.inventory_security_strategy_id
        LEFT JOIN u_material t3 ON t2.material_id = t3.material_id
        WHERE 1 = 1
        <if test="reqVO.inventorySecurityStrategyCode != null and reqVO.inventorySecurityStrategyCode != ''">
            AND t1.inventory_security_strategy_code LIKE CONCAT('%', #{reqVO.inventorySecurityStrategyCode}, '%')
        </if>
        <if test="reqVO.inventorySecurityStrategyName != null and reqVO.inventorySecurityStrategyName != ''">
            AND t1.inventory_security_strategy_name LIKE CONCAT('%', #{reqVO.inventorySecurityStrategyName}, '%')
        </if>
        <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
            AND t1.warehouse_org_id = #{reqVO.warehouseOrgId}
        </if>
        <if test="reqVO.dataStatus != null">
            AND t1.data_status = #{reqVO.dataStatus}
        </if>
        <if test="reqVO.directorId != null">
            AND t1.director_id = #{reqVO.directorId}
        </if>
        <if test="reqVO.startFormDt != null">
            AND t1.form_dt >= #{reqVO.startFormDt}
        </if>
        <if test="reqVO.endFormDt != null">
            AND t1.form_dt &lt;= #{reqVO.endFormDt}
        </if>
        <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
            AND t1.director_org_id = #{reqVO.directorOrgId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t3.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t3.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t3.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND t3.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        ORDER BY t2.inventory_security_strategy_id DESC
    </select>



</mapper>
