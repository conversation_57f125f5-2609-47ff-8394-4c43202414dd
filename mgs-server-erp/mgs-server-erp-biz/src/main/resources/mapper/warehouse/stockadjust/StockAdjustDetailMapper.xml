<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.stockadjust.StockAdjustDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockadjust.vo.detail.StockAdjustDetailRespVO">
        SELECT detail.*, adjust.*
        FROM u_stock_adjust adjust
        JOIN u_stock_adjust_detail detail ON detail.stock_adjust_id = adjust.stock_adjust_id
        LEFT JOIN u_material material ON detail.material_id = material.material_id AND material.deleted = 0
        <where>
            <if test="reqVO.stockAdjustCode != null and reqVO.stockAdjustCode != ''">
                AND adjust.stock_adjust_code LIKE CONCAT('%', #{reqVO.stockAdjustCode}, '%')
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND adjust.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.stockAdjustTypeDictId != null and reqVO.stockAdjustTypeDictId != ''">
                AND adjust.stock_adjust_type_dict_id = #{reqVO.stockAdjustTypeDictId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND material.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND material.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND material.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND material.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.dataStatus != null">
                AND adjust.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND adjust.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND adjust.director_org_id = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (adjust.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
        </where>
        ORDER BY adjust.stock_adjust_id DESC, detail.row_no ASC
    </select>

    <select id="queryList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockadjust.vo.detail.StockAdjustDetailRespVO">
        SELECT detail.*, (COALESCE(stock.stock_qty, 0) - COALESCE(stock.locked_qty, 0)) adjustableQty
        FROM u_stock_adjust_detail detail
        LEFT JOIN erp.u_material_stock stock ON detail.material_id = stock.material_id AND detail.warehouse_org_id = stock.warehouse_org_id
        <where>
            AND detail.stock_adjust_id = #{reqVO.stockAdjustId}
        </where>
        ORDER BY detail.row_no ASC
    </select>

    <select id="stockAdjustMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.stock_adjust_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_stock_adjust_detail b
        WHERE b.stock_adjust_id IN
        <foreach item="item" index="index" collection="stockAdjustIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.stock_adjust_id, b.material_id, b.warehouse_org_id
    </select>

</mapper>
