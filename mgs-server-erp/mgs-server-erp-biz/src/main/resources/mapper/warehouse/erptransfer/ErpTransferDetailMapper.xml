<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erptransfer.ErpTransferDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryErpTransferDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.detail.ErpTransferDetailRespVO">
        SELECT b.remark, a.*, b.transfer_detail_id, b.row_no, b.material_id, b.material_code, b.main_unit_dict_id,
            b.transfer_qty, b.transferred_out_qty transferredOutQty, b.transferred_in_qty transferredInQty,
            c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_transfer a
        JOIN u_transfer_detail b ON a.transfer_id = b.transfer_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.transferCode != null and reqVO.transferCode != ''">
                AND a.transfer_code LIKE concat('%', #{reqVO.transferCode}, '%')
            </if>

            <if test="reqVO.transferTypeDictId != null and reqVO.transferTypeDictId != ''">
                AND a.transfer_type_dict_id = #{reqVO.transferTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.transferOutOrgId != null and reqVO.transferOutOrgId != ''">
                AND a.transfer_out_org_id = #{reqVO.transferOutOrgId}
            </if>
            <if test="reqVO.transferInOrgId != null and reqVO.transferInOrgId != ''">
                AND a.transfer_in_org_id = #{reqVO.transferInOrgId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>


    <select id="queryErpTransferDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.detail.ErpTransferDetailRespVO">
        SELECT b.transfer_detail_id, b.row_no, b.material_id, b.material_code, b.main_unit_dict_id, b.transfer_qty,
            b.material_stock_id, b.transferred_out_qty transferredOutQty, b.transferred_in_qty transferredInQty,
            b.remark, c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_transfer_detail b
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.transferId != null">
                AND b.transfer_id = #{reqVO.transferId}
            </if>
            <if test="reqVO.transferCode != null and reqVO.transferCode != ''">
                AND b.transfer_code LIKE concat('%', #{reqVO.transferCode}, '%')
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="transferOutQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.transfer_detail_id as fieldId, b.material_code as code, b.transfer_qty as sumQty,
           a.transfer_out_org_id warehouseOrgId,  b.material_Id
        FROM u_transfer a
        JOIN u_transfer_detail b ON a.transfer_id = b.transfer_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        WHERE b.transfer_id = #{transferId}
    </select>

    <select id="transferInQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.transfer_detail_id as fieldId, b.material_code as code, b.transfer_qty as sumQty,
           a.transfer_in_org_id warehouseOrgId,  b.material_Id
        FROM u_transfer a
        JOIN u_transfer_detail b ON a.transfer_id = b.transfer_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        WHERE b.transfer_id = #{transferId}
    </select>

    <select id="transferMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.transfer_id as orderId, b.material_id
        FROM u_transfer_detail b
        WHERE b.transfer_id IN
        <foreach item="item" index="index" collection="transferIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.transfer_id, b.material_id
    </select>

</mapper>
