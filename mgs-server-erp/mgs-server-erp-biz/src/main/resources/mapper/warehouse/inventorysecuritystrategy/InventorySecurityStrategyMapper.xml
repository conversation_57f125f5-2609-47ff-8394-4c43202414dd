<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.inventorysecuritystrategy.InventorySecurityStrategyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListBy"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategy.vo.InventorySecurityStrategyReportRespVO">
        SELECT
            t1.inventory_security_strategy_id,
            t1.inventory_security_strategy_code,
            t1.inventory_security_strategy_name,
            t1.warehouse_org_id,
            t2.material_id,
            t2.material_code,
            t2.min_value,
            t2.max_value,
            t2.remark,
            t3.material_name,
            t3.material_category_dict_id,
            t3.material_source_dict_id,
            t3.main_unit_dict_id,
            t3.spec_model,
            t3.spec_attribute_str,
            COALESCE(t4.stock_qty, 0) AS stock_qty,
            CASE
                WHEN COALESCE(t4.stock_qty, 0) &lt; t2.min_value THEN 1
                WHEN COALESCE(t4.stock_qty, 0) > t2.max_value THEN 2
            END AS warningType
        FROM erp.u_inventory_security_strategy t1
        LEFT JOIN erp.u_inventory_security_strategy_detail t2 ON t1.inventory_security_strategy_id = t2.inventory_security_strategy_id
        LEFT JOIN u_material t3 ON t2.material_id = t3.material_id
        LEFT JOIN erp.u_material_stock t4 ON t3.material_id = t4.material_id AND t1.warehouse_org_id = t4.warehouse_org_id
        WHERE t1.data_status = 1
        <if test="reqVO.warningType == 0">
            AND (COALESCE(t4.stock_qty, 0) &lt; t2.min_value OR COALESCE(t4.stock_qty, 0) > t2.max_value)
        </if>
        <if test="reqVO.warningType == 1">
            AND (COALESCE(t4.stock_qty, 0) &lt; t2.min_value)
        </if>
        <if test="reqVO.warningType == 2">
            AND (COALESCE(t4.stock_qty, 0) > t2.max_value)
        </if>
        <if test="reqVO.inventorySecurityStrategyCode != null and reqVO.inventorySecurityStrategyCode != ''">
            AND t1.inventory_security_strategy_code LIKE CONCAT('%', #{reqVO.inventorySecurityStrategyCode}, '%')
        </if>
        <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
            AND t1.warehouse_org_id = #{reqVO.warehouseOrgId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND t3.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND t3.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND t3.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND t3.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
        </if>
        ORDER BY t1.inventory_security_strategy_id DESC
    </select>


    <select id="doforewarnJob"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategy.vo.InventorySecurityStrategyReportRespVO">
        SELECT
        t1.inventory_security_strategy_id,
        t1.inventory_security_strategy_code,
        t1.inventory_security_strategy_name,
        t1.warehouse_org_id,
        t2.material_id,
        t2.material_code,
        t2.min_value,
        t2.max_value,
        t3.stock_qty,
        t4.material_name,
        t4.material_category_dict_id,
        t4.material_source_dict_id,
        t4.main_unit_dict_id,
        t4.spec_model,
        t4.spec_attribute_str,
            CASE
                WHEN t3.stock_qty &lt; t2.min_value THEN 1
                WHEN t3.stock_qty > t2.max_value THEN 2
            END AS warningType
        FROM erp.u_inventory_security_strategy t1
        LEFT JOIN erp.u_inventory_security_strategy_detail t2 ON t1.inventory_security_strategy_id = t2.inventory_security_strategy_id
        LEFT JOIN erp.u_material_stock t3 ON t2.material_id = t3.material_id AND t1.warehouse_org_id = t3.warehouse_org_id
        LEFT JOIN u_material t4 ON t2.material_id = t4.material_id
        WHERE t1.data_status = 1 AND (t3.stock_qty &lt; t2.min_value OR t3.stock_qty > t2.max_value)
    </select>


</mapper>
