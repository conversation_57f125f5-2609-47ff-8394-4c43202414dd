<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryReceiptDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailRespVO">
        SELECT b.remark, b.check_result, a.*, b.receipt_detail_id, b.row_no, b.related_row_no, b.material_id, b.material_code,
            b.warehouse_org_id, b.main_unit_dict_id, b.receipt_qty, b.receipted_qty, b.inbounded_qty, b.check_qty,
            b.allow_defects_qty, b.ok_qty, b.ng_qty, c.material_name, c.material_category_dict_id, c.spec_model,
            c.spec_attribute_str,b.process_id,b.process_code,b.process_name
        FROM u_receipt a
        JOIN u_receipt_detail b ON a.receipt_id = b.receipt_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.receiptCode != null and reqVO.receiptCode != ''">
                AND a.receipt_code LIKE concat('%', #{reqVO.receiptCode}, '%')
            </if>
            <if test="reqVO.receiptTypeDictId != null and reqVO.receiptTypeDictId != ''">
                AND a.receipt_type_dict_id = #{reqVO.receiptTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND b.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.bizType != null">
                AND a.biz_type = #{reqVO.bizType}
            </if>
            <if test="reqVO.exclBizType != null">
                AND a.biz_type != #{reqVO.exclBizType}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>


    <select id="queryReceiptDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailRespVO">
        SELECT b.remark, a.*, b.receipt_detail_id, b.related_order_detail_id, b.row_no, b.related_row_no,
            b.material_id, b.material_code, b.warehouse_org_id, b.main_unit_dict_id, b.receipt_qty,
            b.receipted_qty, b.inbounded_qty, b.inboundable_qty, b.check_qty, b.allow_defects_qty,
            b.ok_qty, b.ng_qty, b.check_result, c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str,b.process_id,b.process_code,b.process_name
        FROM u_receipt a
        JOIN u_receipt_detail b ON a.receipt_id = b.receipt_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.receiptId != null">
                AND a.receipt_id = #{reqVO.receiptId}
            </if>
            <if test="reqVO.receiptCode != null and reqVO.receiptCode != ''">
                AND a.receipt_code LIKE concat('%', #{reqVO.receiptCode}, '%')
            </if>
            <if test="reqVO.isMaterialFullInbounded != null">
                AND b.is_material_full_inbounded = #{reqVO.isMaterialFullInbounded}
            </if>
            <if test="reqVO.isMaterialFullCheck != null">
                AND b.is_material_full_check = #{reqVO.isMaterialFullCheck}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="receiptQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.related_order_detail_id as fieldId, b.material_code as code, SUM(b.receipt_qty) as sumQty
        FROM u_receipt_detail b
        WHERE b.receipt_id = #{receiptId}
        GROUP BY b.related_order_detail_id, b.material_code
    </select>

    <select id="inboundableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.receipt_detail_id as fieldId, b.material_code as code, (b.receipt_qty - b.inbounded_qty) as sumQty
        FROM u_receipt_detail b
        WHERE b.receipt_id = #{receiptId}
    </select>

    <select id="selectReceiptQty"
            resultType="com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO">
        select sum(demand.ok_qty) as okQty, sum(demand.receipted_qty) as receiptedQty
        from u_receipt_detail detail
            left JOIN u_purchase_process_out_detail process on process.process_out_detail_id = detail.related_order_detail_id
            left JOIN u_process_out_demand demand on demand.process_out_demand_id = process.process_out_demand_id
        where detail.receipt_id = #{receiptId}
    </select>

    <select id="selectListBySourceOrderCode"
            resultType="com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDetailDO">
        select b.* from u_receipt a
        JOIN u_receipt_detail b ON a.receipt_id = b.receipt_id
        where a.data_status = 1 and  a.receipt_code = #{sourceOrderCode}
    </select>

</mapper>
