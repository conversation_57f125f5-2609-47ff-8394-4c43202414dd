<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.stockbook.StockBookMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <update id="additionBookSeq" parameterType="java.lang.Integer">
        UPDATE erp.u_stock_book
        SET seq = seq + 1
        WHERE seq >= #{bookSeq} AND task_status = 0;
    </update>
    <update id="intervalUpdateBookSeq">
        UPDATE erp.u_stock_book
        SET book_seq = book_seq + #{operation}
        WHERE
        task_status = 0
        <if test="upSeq != null">
            AND book_seq >= #{upSeq}
        </if>
        <if test="downSeq != null">
            AND book_seq &lt;= #{downSeq}
        </if>
    </update>
    <update id="updateBookStatus" parameterType="java.lang.Long">
        UPDATE erp.u_stock_book
        SET task_status = 1
        WHERE stock_book_id = #{bookId}
          AND NOT EXISTS (
            SELECT 1
            FROM erp.u_stock_book_detail ibd
            WHERE ibd.stock_book_id = #{bookId}
              AND ibd.bookable_qty > 0
        );
    </update>
    <select id="queryRelatedOrderPage"
            resultType="com.mongoso.mgs.module.base.controller.admin.orderrelation.vo.OrderRelationRespVO">
        SELECT
        ${orderIdField} AS orderId,
        ${orderCodeField} AS orderCode,
        director_id,director_org_id,form_dt
        FROM ${tableName}
        <where>
            AND data_status = 1
            <if test="orderCode != null and orderCode != ''">
                AND ${orderCodeField} LIKE CONCAT('%', #{orderCode}, '%')
            </if>
            <if test="bizTypeList != null and bizTypeList.size > 0">
                AND ${bizTypeField} IN
                <foreach collection="bizTypeList" item="bizType" open="(" separator="," close=")">
                    #{bizType}
                </foreach>
            </if>
            <if test="orderType != null">
                AND (form_status = 0 OR form_status = 1)
                <if test="orderType == 2">
                    AND is_take_material = 1
                </if>
                <if test="orderType == 3">
                    AND sale_chang_id IS NULL
                </if>
                <if test="orderType == 4">
                    AND prod_order_change_id IS NULL
                </if>
            </if>
        </where>
        ORDER BY ${orderIdField} DESC
    </select>
    <select id="queryRelatedOrderDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO">
        SELECT
            ${materialIdField} AS materialId,
            ${qtyField} AS orderDetailQty
        FROM ${tableName}
        WHERE ${orderIdField} = #{orderId}
        <if test="exclMaterialIdList != null and exclMaterialIdList.size >0 ">
            AND ${materialIdField} NOT IN
            <foreach collection="exclMaterialIdList" item="exclMaterialId" open="(" separator="," close=")">
                #{exclMaterialId}
            </foreach>
        </if>
        ORDER BY ${materialIdField} DESC
    </select>
    <select id="selectMaxBookSeq" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(CAST(book_seq AS INT)), 0) FROM erp.u_stock_book
        WHERE task_status = 0
    </select>

</mapper>
