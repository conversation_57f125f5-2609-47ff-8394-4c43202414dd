<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.stockbook.StockBookDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <update id="updateBookAbleQty">
        UPDATE erp.u_stock_book_detail
        SET bookable_qty = bookable_qty - #{qty}, used_qty = used_qty + #{qty}
        WHERE stock_book_detail_id = #{detailId}
    </update>

    <select id="checkBookedQty" resultType="java.lang.Integer"
            parameterType="com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.StockBookAditReqVO">
        SELECT CASE WHEN EXISTS (
            SELECT 1
            FROM erp.u_stock_book_detail
            WHERE stock_book_id = #{reqVO.stockBookId} AND book_qty &lt; used_qty
        ) THEN 1 ELSE 0 END AS result;
    </select>

    <select id="queryBookIdListByDetailList"
            resultType="com.mongoso.mgs.module.warehouse.dal.db.stockbook.StockBookDetailDO">
        SELECT detail.*
        FROM erp.u_stock_book_detail detail
        LEFT JOIN erp.u_stock_book book ON detail.stock_book_id = book.stock_book_id
        <where>
            <if test="reqVOList != null and reqVOList.size > 0">
                <foreach item="item" index="index" collection="reqVOList">
                    OR (detail.material_id = #{item.materialId} AND detail.warehouse_org_id = #{item.warehouseOrgId})
                </foreach>
            </if>
            AND book.task_status = 0
        </where>
        ORDER BY book.stock_book_id DESC
    </select>
    <select id="queryList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO">
        SELECT detail.*, COALESCE(stock.booked_qty, 0) AS booked_qty, stock.stock_qty
        FROM erp.u_stock_book_detail detail
        LEFT JOIN u_material_stock stock ON detail.material_id = stock.material_id AND detail.warehouse_org_id = stock.warehouse_org_id
        <where>
            <if test="reqVO.stockBookId != null">
                AND detail.stock_book_id = #{reqVO.stockBookId}
            </if>
        </where>
        ORDER BY detail.row_no ASC
    </select>
    <select id="queryBookIdList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO">
        SELECT detail.*, book.order_id
        FROM erp.u_stock_book_detail detail
                 LEFT JOIN erp.u_stock_book book ON detail.stock_book_id = book.stock_book_id
        WHERE book.task_status = 0 AND detail.material_id = #{reqVO.materialId} AND detail.warehouse_org_id = #{reqVO.warehouseOrgId}
        ORDER BY book.book_seq ASC
    </select>


    <!--    <select id="queryBookIdList"-->
<!--            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO"-->
<!--            parameterType="java.util.List">-->
<!--        SELECT CONCAT(detail.material_id, detail.warehouse_org_id) AS materialWarehouseKey,-->
<!--        GROUP_CONCAT(CONCAT(book.stock_book_id, '-', detail.bookable_qty) ORDER BY book.stock_book_id DESC SEPARATOR ',') AS stockBookIds-->
<!--        FROM erp.u_stock_book_detail detail-->
<!--        LEFT JOIN erp.u_stock_book book ON detail.stock_book_id = book.stock_book_id-->
<!--        WHERE book.task_status = 0 AND (material_id, warehouse_org_id) IN-->
<!--        <foreach collection="list" item="item" open="(" separator="," close=")">-->
<!--            (#{item.materialId}, #{warehouseOrgId})-->
<!--        </foreach>-->
<!--        GROUP BY detail.material_id, detail.warehouse_org_id-->
<!--    </select>-->

<!--    SELECT CONCAT(detail.material_id, detail.warehouse_org_id) AS materialWarehouseKey,-->
<!--    GROUP_CONCAT(CONCAT(book.stock_book_id, '-', detail.bookable_qty) ORDER BY book.stock_book_id DESC SEPARATOR ',') AS stockBookIds-->
<!--    FROM erp.u_stock_book_detail detail-->
<!--    LEFT JOIN erp.u_stock_book book ON detail.stock_book_id = book.stock_book_id-->
<!--    WHERE book.task_status = 0 AND (material_id, warehouse_org_id) IN-->
<!--    <foreach collection="list" item="item" open="(" separator="," close=")">-->
<!--        (#{item.materialId}, #{warehouseOrgId})-->
<!--    </foreach>-->
<!--    GROUP BY detail.material_id, detail.warehouse_org_id-->
</mapper>
