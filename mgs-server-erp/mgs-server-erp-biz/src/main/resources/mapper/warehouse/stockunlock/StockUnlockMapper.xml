<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.stockunlock.StockUnlockMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryStockUnlockPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockunlock.vo.StockUnlockRespVO">
        SELECT a.*, b.lock_type_dict_id
        FROM u_stock_unlock a
        LEFT JOIN u_stock_lock b ON a.lock_id = b.lock_id
        <where>
            <if test="reqVO.unlockCode != null and reqVO.unlockCode != ''">
                AND a.unlock_code LIKE concat('%', #{reqVO.unlockCode}, '%')
            </if>
            <if test="reqVO.unlockTypeDictId != null and reqVO.unlockTypeDictId != ''">
                AND a.unlock_type_dict_id = #{reqVO.unlockTypeDictId}
            </if>
            <if test="reqVO.unlockReason != null and reqVO.unlockReason != ''">
                AND a.unlock_reason LIKE concat('%', #{reqVO.unlockReason}, '%')
            </if>
            <if test="reqVO.lockCode != null and reqVO.lockCode != ''">
                AND a.lock_code LIKE concat('%', #{reqVO.lockCode}, '%')
            </if>
            <if test="reqVO.lockTypeDictId != null and reqVO.lockTypeDictId != ''">
                AND b.lock_type_dict_id = #{reqVO.lockTypeDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc
    </select>

    <select id="queryStockUnlockDetail"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockunlock.vo.StockUnlockRespVO">
        SELECT a.*, b.lock_type_dict_id
        FROM u_stock_unlock a
        LEFT JOIN u_stock_lock b ON a.lock_id = b.lock_id
        WHERE a.unlock_id = #{unlockId}
    </select>

</mapper>
