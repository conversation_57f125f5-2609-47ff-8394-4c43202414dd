<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.stockunlock.StockUnlockDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryStockUnlockDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockunlock.vo.detail.StockUnlockDetailRespVO">
        SELECT b.remark, a.*, l.lock_type_dict_id, b.row_no, b.material_id, b.warehouse_org_id, b.main_unit_dict_id,
            b.unlock_qty, b.remark, b.material_code, c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str, ld.row_no relatedRowNo
        FROM u_stock_unlock a
        LEFT JOIN u_stock_lock l ON a.lock_id = l.lock_id
        LEFT JOIN u_stock_unlock_detail b ON a.unlock_id = b.unlock_id
        LEFT JOIN u_stock_lock_detail ld ON b.lock_detail_id = ld.lock_detail_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.unlockCode != null and reqVO.unlockCode != ''">
                AND a.unlock_code LIKE concat('%', #{reqVO.unlockCode}, '%')
            </if>
            <if test="reqVO.unlockTypeDictId != null and reqVO.unlockTypeDictId != ''">
                AND a.unlock_type_dict_id = #{reqVO.unlockTypeDictId}
            </if>
            <if test="reqVO.unlockReason != null and reqVO.unlockReason != ''">
                AND a.unlock_reason LIKE concat('%', #{reqVO.unlockReason}, '%')
            </if>
            <if test="reqVO.lockCode != null and reqVO.lockCode != ''">
                AND a.lock_code LIKE concat('%', #{reqVO.lockCode}, '%')
            </if>
            <if test="reqVO.lockTypeDictId != null and reqVO.lockTypeDictId != ''">
                AND l.lock_type_dict_id = #{reqVO.lockTypeDictId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND a.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="queryStockUnlockDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stockunlock.vo.detail.StockUnlockDetailRespVO">
        SELECT b.unlock_detail_id, b.lock_detail_id, b.row_no, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.unlock_qty, b.remark, b.material_code, c.material_name,
            c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str,
            ld.row_no relatedRowNo
        FROM u_stock_unlock_detail b
        LEFT JOIN u_stock_lock_detail ld ON b.lock_detail_id = ld.lock_detail_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.unlockId != null">
                AND b.unlock_id = #{reqVO.unlockId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="unlockQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.lock_detail_id as fieldId, b.material_code as code,
            ld.material_stock_id, b.warehouse_org_id, b.unlock_qty as sumQty
        FROM u_stock_unlock_detail b
        LEFT JOIN u_stock_lock_detail ld ON b.lock_detail_id = ld.lock_detail_id
        WHERE b.unlock_id = #{unlockId}
    </select>

    <select id="unlockMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.unlock_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_stock_unlock_detail b
        WHERE b.unlock_id IN
        <foreach item="item" index="index" collection="unlcokIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.unlock_id, b.material_id, b.warehouse_org_id
    </select>


</mapper>
