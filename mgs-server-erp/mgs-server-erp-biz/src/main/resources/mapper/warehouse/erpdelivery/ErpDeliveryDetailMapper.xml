<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erpdelivery.ErpDeliveryDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryErpDeliveryDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpdelivery.vo.detail.ErpDeliveryDetailRespVO">
        SELECT b.remark, a.*, b.delivery_detail_id, b.row_no, b.related_row_no, b.material_id, b.material_code,
            b.main_unit_dict_id, b.delivery_qty, b.detail_type_dict_id, c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str
        FROM u_delivery a
        JOIN u_delivery_detail b ON a.delivery_id = b.delivery_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.deliveryCode != null and reqVO.deliveryCode != ''">
                AND a.delivery_code LIKE concat('%', #{reqVO.deliveryCode}, '%')
            </if>
            <if test="reqVO.deliveryName != null and reqVO.deliveryName != ''">
                AND a.delivery_name LIKE concat('%', #{reqVO.deliveryName}, '%')
            </if>
            <if test="reqVO.deliveryTypeDictId != null and reqVO.deliveryTypeDictId != ''">
                AND a.delivery_type_dict_id = #{reqVO.deliveryTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.startDeliveryDate != null">
                AND a.delivery_date >= #{reqVO.startDeliveryDate}
            </if>
            <if test="reqVO.endDeliveryDate != null">
                AND a.delivery_date &lt;= #{reqVO.endDeliveryDate}
            </if>
            <if test="reqVO.receiptObjId != null">
                AND a.receipt_obj_id = #{reqVO.receiptObjId}
            </if>
            <if test="reqVO.deliveryMethodDictId != null and reqVO.deliveryMethodDictId != ''">
                AND a.delivery_method_dict_id = #{reqVO.deliveryMethodDictId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.detailTypeDictId != null and reqVO.detailTypeDictId != ''">
                AND b.detail_type_dict_id = #{reqVO.detailTypeDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.saleOrderId != null">
                AND a.sale_order_id = #{reqVO.saleOrderId}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.detail_type_dict_id asc, b.row_no asc
    </select>


    <select id="queryErpDeliveryDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpdelivery.vo.detail.ErpDeliveryDetailRespVO">
        SELECT b.delivery_detail_id, b.row_no, b.related_order_detail_id, b.related_row_no, b.material_id, b.material_code,
            b.main_unit_dict_id, b.delivery_qty, b.detail_type_dict_id, b.remark, c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str
        FROM u_delivery_detail b
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.deliveryId != null">
                AND b.delivery_id = #{reqVO.deliveryId}
            </if>
            <if test="reqVO.deliveryCode != null and reqVO.deliveryCode != ''">
                AND b.delivery_code LIKE concat('%', #{reqVO.deliveryCode}, '%')
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="deliveryQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.related_order_detail_id as fieldId, b.material_code as code, b.delivery_qty as sumQty
        FROM u_delivery_detail b
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        WHERE b.delivery_id = #{deliveryId} AND b.detail_type_dict_id = '0'
    </select>

    <select id="selectListBySourceOrderCode"
            resultType="com.mongoso.mgs.module.warehouse.dal.db.erpdelivery.ErpDeliveryDetailDO">
        SELECT b.*
        FROM u_delivery a JOIN u_delivery_detail b ON a.delivery_id = b.delivery_id
        where a.data_status = 1 and  a.delivery_code = #{sourceOrderCode}
    </select>


</mapper>
