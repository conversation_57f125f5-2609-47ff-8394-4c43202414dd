<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryStockChangeStatPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockChangeStatRespVO">
        SELECT t.*, (r.end_stock - t.changeStock) startStock, r.end_stock
        FROM (
            SELECT o.org_name warehouseOrgName, b.material_code, b.material_name,
                c.dict_name material_category_dict_name, b.spec_model, b.spec_attribute_str,
                SUM(CASE WHEN source_form_type = 0 THEN change_stock ELSE 0 END) AS miInboundQty,
                SUM(CASE WHEN source_form_type = 1 THEN change_stock ELSE 0 END) AS aoInboundQty,
                SUM(CASE WHEN source_form_type = 2 THEN change_stock ELSE 0 END) AS doInboundQty,
                SUM(CASE WHEN source_form_type = 3 THEN change_stock ELSE 0 END) AS rfInboundQty,
                SUM(CASE WHEN source_form_type = 4 THEN change_stock ELSE 0 END) AS toInboundQty,
                SUM(CASE WHEN source_form_type = 10 THEN change_stock ELSE 0 END) AS moOutboundQty,
                SUM(CASE WHEN source_form_type = 11 THEN change_stock ELSE 0 END) AS aoOutboundQty,
                SUM(CASE WHEN source_form_type = 12 THEN change_stock ELSE 0 END) AS doOutboundQty,
                SUM(CASE WHEN source_form_type = 13 THEN change_stock ELSE 0 END) AS lfOutboundQty,
                SUM(CASE WHEN source_form_type = 14 THEN change_stock ELSE 0 END) AS toOutboundQty,
                SUM(change_stock) changeStock,
                MAX(stock_record_id) maxStockRecordId
            FROM u_material_stock_record a
            LEFT JOIN u_material b ON a.material_id = b.material_id
            LEFT JOIN platform.t_dict c ON c.dict_obj_code = 'ERP107' and b.material_category_dict_id = c.dict_code
            LEFT JOIN platform.t_emp_auth_org o ON a.warehouse_org_id = o.org_id
        <where>
                <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                    AND a.warehouse_org_id = #{reqVO.warehouseOrgId}
                </if>
                <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                    AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
                </if>
                <if test="reqVO.materialName != null and reqVO.materialName != ''">
                    AND b.material_name LIKE concat('%', #{reqVO.materialName}, '%')
                </if>
                <if test="reqVO.specModel != null and reqVO.specModel != ''">
                    AND b.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
                </if>
                <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                    AND b.material_category_dict_id = #{reqVO.materialCategoryDictId}
                </if>
                <if test="reqVO.startDate != null and reqVO.endDate != null ">
                    AND DATE(a.created_dt) >= #{reqVO.startDate}
                    AND DATE(a.created_dt) &lt;= #{reqVO.endDate}
                </if>
            </where>
            GROUP BY a.warehouse_org_id, a.material_id, b.material_id, c.dict_id, o.org_id
        ) t
        JOIN u_material_stock_record r ON t.maxStockRecordId = r.stock_record_id
        <where>
            (
                miInboundQty != 0 OR aoInboundQty != 0
                OR doInboundQty != 0 OR rfInboundQty != 0
                OR toInboundQty != 0 OR moOutboundQty != 0
                OR aoOutboundQty != 0 OR doOutboundQty != 0
                OR lfOutboundQty != 0 OR toOutboundQty != 0
            )
            <if test="reqVO.endStockType != null and reqVO.endStockType == 0">
                AND r.end_stock = 0
            </if>
            <if test="reqVO.endStockType != null and reqVO.endStockType == 1">
                AND r.end_stock != 0
            </if>
        </where>
    </select>

    <select id="queryStartStockList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockChangeStatRespVO">
        SELECT stock_record_id minStockRecordId, start_stock
        FROM u_material_stock_record
        <where>
            <if test="minStockRecordIdList != null and minStockRecordIdList.size > 0 ">
                AND stock_record_id IN
                <foreach item="item" index="index" collection="minStockRecordIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
