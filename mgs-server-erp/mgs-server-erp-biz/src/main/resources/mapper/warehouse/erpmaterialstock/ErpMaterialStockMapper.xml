<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryWarehouseStockPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockRespVO">
        SELECT a.material_stock_id, a.warehouse_org_id, b.material_id, b.material_code, b.material_name,
            b.material_category_dict_id, b.spec_model, b.spec_attribute_str, b.main_unit_dict_id,b.cost_value,
            a.stock_qty, a.locked_qty, a.available_qty, COALESCE(a.booked_qty, 0) booked_qty
        FROM u_material_stock a
        JOIN u_material b ON a.material_id = b.material_id AND b.deleted = 0
        <where>
            <if test="reqVO.stockType == 0">
                AND ( a.stock_qty > 0 OR (a.stock_qty = 0 AND a.booked_qty != 0))
            </if>
            <if test="reqVO.stockType == 1">
                AND a.available_qty > 0
            </if>
            <if test="reqVO.lockType == 1">
                AND a.stock_qty - a.locked_qty > 0
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND a.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.warehouseOrgIdList != null and reqVO.warehouseOrgIdList.size > 0">
                AND a.warehouse_org_id IN
                <foreach item="item" index="index" collection="reqVO.warehouseOrgIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND b.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND b.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND b.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.exclMaterialStockIdList != null and reqVO.exclMaterialStockIdList.size > 0">
                AND a.material_stock_id NOT IN
                <foreach item="item" index="index" collection="reqVO.exclMaterialStockIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.material_id ASC
    </select>

    <select id="queryWarehouseStockQuotedPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockQuotedRespVO">
        SELECT a.material_stock_id, a.warehouse_org_id, b.material_id, b.material_code, b.material_name,
            b.material_category_dict_id, b.spec_model, b.spec_attribute_str, b.main_unit_dict_id,
            a.stock_qty, a.available_qty, bom.demand_qty, bom.loss_rate, bom.estimated_qty
        FROM u_material_stock a
        JOIN u_material b ON a.material_id = b.material_id AND b.deleted = 0
        LEFT JOIN u_material_bom bom ON a.material_id = bom.material_id AND bom.fk_material_bom_id = #{reqVO.materialBomId}
        WHERE available_qty > 0
        <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
            AND a.warehouse_org_id = #{reqVO.warehouseOrgId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND b.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND b.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND b.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.exclMaterialStockIdList != null and reqVO.exclMaterialStockIdList.size > 0">
            AND a.material_stock_id NOT IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialStockIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.exclMaterialIdList != null and reqVO.exclMaterialIdList.size > 0">
            AND a.material_id NOT IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY bom.material_bom_id ASC, a.material_id ASC
    </select>

    <select id="queryWarehouseStockList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockRespVO">
        SELECT a.material_stock_id, a.warehouse_org_id, b.material_id, b.material_code, b.material_name,
            b.material_category_dict_id, b.spec_model, b.spec_attribute_str, b.main_unit_dict_id,
            a.stock_qty, a.locked_qty, a.available_qty, a.booked_qty
        FROM u_material_stock a
        JOIN u_material b ON a.material_id = b.material_id AND b.deleted = 0
        <where>
            <if test="reqVO.stockType == 0">
                AND ( a.stock_qty > 0 OR (a.stock_qty = 0 AND a.booked_qty != 0))
            </if>
            <if test="reqVO.stockType == 1">
                AND a.available_qty > 0
            </if>
            <if test="reqVO.stockType == 2">
                AND a.stock_qty > 0
            </if>
            <if test="reqVO.materialId != null">
                AND b.material_id = #{reqVO.materialId}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND a.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.warehouseOrgIdList != null and reqVO.warehouseOrgIdList.size > 0">
                AND a.warehouse_org_id in
                <foreach collection="reqVO.warehouseOrgIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.exclMaterialStockIdList != null and reqVO.exclMaterialStockIdList.size > 0">
                AND a.material_stock_id NOT IN
                <foreach item="item" index="index" collection="reqVO.exclMaterialStockIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.material_id ASC
    </select>


    <select id="queryMaterialStockPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockRespVO">
        SELECT b.material_id, b.material_code, b.material_name, b.material_category_dict_id, b.spec_model,b.cost_value,
            b.spec_attribute_str, b.main_unit_dict_id, SUM(a.stock_qty) stock_qty, SUM(a.locked_qty) locked_qty,
            SUM(a.available_qty) available_qty, SUM(COALESCE(a.booked_qty, 0)) booked_qty
        FROM u_material_stock a
        JOIN u_material b ON a.material_id = b.material_id AND b.deleted = 0
        WHERE a.stock_qty > 0
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND b.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND b.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND b.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        <if test="reqVO.exclMaterialStockIdList != null and reqVO.exclMaterialStockIdList.size > 0">
            AND a.material_stock_id NOT IN
            <foreach item="item" index="index" collection="reqVO.exclMaterialStockIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY b.material_id
        ORDER BY b.material_id ASC
    </select>

    <select id="queryMaterialStockQtyForSpu" resultType="java.math.BigDecimal">
        SELECT SUM(stock_qty) FROM u_material_stock
        WHERE material_id IN
              (SELECT material_id FROM u_material WHERE spu_id = #{spuId} AND deleted = 0)
    </select>

    <select id="queryMaterialStockQty" resultType="java.math.BigDecimal" parameterType="java.lang.Long">
        SELECT SUM(stock_qty) FROM u_material_stock
        WHERE material_id = #{materialId}
    </select>

    <select id="queryStockAvailableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT a.material_stock_id as fieldId, a.available_qty as sumQty, a.locked_qty, a.stock_qty
        FROM u_material_stock a
        <where>
            <if test="materialStockIdList != null and materialStockIdList.size > 0">
                AND a.material_stock_id IN
                <foreach item="item" index="index" collection="materialStockIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryStockListByMaterialAndOrgId" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT a.material_stock_id as fieldId, a.material_id, a.warehouse_org_id, a.available_qty as sumQty, a.locked_qty, a.stock_qty, a.booked_qty
        FROM u_material_stock a
        <where>
            <if test="reqVOList != null and reqVOList.size > 0">
                <foreach item="item" index="index" collection="reqVOList">
                    OR (a.material_id = #{item.materialId} AND warehouse_org_id = #{item.warehouseOrgId})
                </foreach>
            </if>
        </where>
    </select>



    <select id="erpMaterialStatAI"
            resultType="com.mongoso.mgs.module.ai.controller.admin.finance.vo.AccountBalanceVOAI1">

        select T1.material_id as materialId, T1.material_code as materialCode,T1.material_name as materialName,T1.stock_qty as stockQty,T2.form_dt as formDt,
               COALESCE(DATE_PART('day', CURRENT_DATE - T2.form_dt), 0) AS distanceDays
        from
            (
                select A.material_id,B.material_code,B.material_name,sum(stock_qty) as stock_qty
                from u_material_stock A
                left join u_material B ON A.material_id = b.material_id
                GROUP BY A.material_id,B.material_code,B.material_name
            ) as T1 left JOIN

            (
                select max(A.form_dt) as form_dt,B.material_code
                from u_outbound A
                left join u_outbound_detail B on a.outbound_code = B.outbound_code
                GROUP BY B.material_code
            ) as T2 on t1.material_code = t2.material_code
        WHERE t2.form_dt is not null
        order by T2.form_dt
    </select>
    <select id="queryWarehouseOrgStockList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockRespVO">
        SELECT org.org_id warehouseOrgId, org.org_name warehouseOrgName, COALESCE(stock.stock_qty, 0) stockQty, COALESCE(stock.booked_qty, 0) bookedQty, stock.material_id
        FROM platform.t_emp_auth_org org
        LEFT JOIN erp.u_material_stock stock ON org.org_id = stock.warehouse_org_id AND stock.material_id = #{reqVO.materialId}
        <where>
            AND org.deleted = 0
            <if test="reqVO.orgType != null">
                AND org.org_type = #{reqVO.orgType}
            </if>
            <if test="reqVO.isEnable != null">
                AND org.is_enable = #{reqVO.isEnable}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND stock.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.warehouseOrgName != null and reqVO.warehouseOrgName != ''">
                AND org.org_name LIKE concat('%', #{reqVO.warehouseOrgName}, '%')
            </if>
        </where>
        ORDER BY org.org_id DESC
    </select>

    <update id="updateBookedQty">
        UPDATE erp.u_material_stock SET booked_qty = COALESCE(booked_qty, 0) + #{bookedQty}, available_qty = available_qty - #{bookedQty}
        WHERE material_id = #{reqVO.materialId} AND warehouse_org_id = #{reqVO.warehouseOrgId}
    </update>

    <!-- 物料库存报表分页查询 -->
    <select id="selectMaterialStockReportPage" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockReportRespVO">
        SELECT
        m.material_id,
        m.material_code,
        m.material_name,
        m.material_category_dict_id,
        m.spec_model,
        m.spec_attribute_str,
        m.cost_value,
        COALESCE(SUM(s.stock_qty), 0) as stock_qty,
        COALESCE(SUM(s.available_qty), 0) as available_qty,
        NULL as warehouse_org_id
        FROM u_material m
        LEFT JOIN u_material_stock s ON m.material_id = s.material_id
        <where>
            m.deleted = 0
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND m.material_code LIKE CONCAT('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND m.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND m.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND m.spec_model LIKE CONCAT('%', #{reqVO.specModel}, '%')
            </if>
        </where>
        GROUP BY m.material_id, m.material_code, m.material_name, m.material_category_dict_id, m.spec_model, m.spec_attribute_str, m.cost_value
        HAVING SUM(s.stock_qty) > 0
        ORDER BY m.material_code ASC
    </select>

</mapper>
