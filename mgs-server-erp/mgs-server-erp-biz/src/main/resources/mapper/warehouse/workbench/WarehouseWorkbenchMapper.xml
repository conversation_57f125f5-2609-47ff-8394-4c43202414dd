<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.workbench.WarehouseWorkbenchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 待入库物料数量 -->
    <select id="getPendingInboundQty" resultType="java.math.BigDecimal">
        SELECT SUM(inboundQty)
        FROM (
            <!-- 采购订单 -->
            SELECT COALESCE(SUM(b.purchase_qty-inbounded_qty), 0) inboundQty
            FROM u_purchase_order a
            JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
            WHERE a.data_status = 1
            AND a.is_force_close = 0
            AND a.inbound_status IN (0, 1)
            UNION ALL
            <!-- 生产工单 -->
            SELECT COALESCE(SUM(inboundable_qty), 0) inboundQty
            FROM u_prod_work
            WHERE data_status = 1
            AND is_inbound = 1
            AND form_status IN (2, 3, 4) <!-- 已完工/强制完工/已关闭 -->
            AND is_full_inbounded = 0
            UNION ALL
            <!-- 销售退货 -->
            SELECT COALESCE(SUM(b.return_qty - b.inbounded_qty), 0) inboundQty
            FROM u_sale_return a
            JOIN u_sale_return_detail b ON a.sale_return_id = b.sale_return_id
            WHERE a.data_status = 1
            AND a.is_full_opered = 0
            <!-- 工单退料单/委外订单退料单 -->
            UNION ALL
            SELECT COALESCE(SUM(inboundable_qty), 0) inboundQty
            FROM u_work_picking_return a
            JOIN u_work_picking_return_detail b ON a.work_picking_return_id = b.work_picking_return_id
            WHERE a.data_status = 1
            AND b.is_material_full_inbounded = 0
         ) t
    </select>

    <!-- 待出库物料数量 -->
    <select id="getPendingOutboundQty" resultType="java.math.BigDecimal">
        SELECT SUM(outboundQty)
        FROM (
            <!-- 销售订单 -->
            SELECT COALESCE(SUM(qty-out_qty), 0) outboundQty
            FROM u_erp_sale_order a
            JOIN u_erp_sale_order_detail b ON a.sale_order_id = b.sale_order_id
            WHERE a.data_status = 1
            AND a.is_force_close = 0
            AND a.outbound_status IN (0, 1)
            AND a.sale_chang_id IS NULL
            UNION ALL
            <!-- 采购退货 -->
            SELECT COALESCE(SUM(outboundable_qty), 0) outboundQty
            FROM u_purchase_return a
            JOIN u_purchase_return_detail b ON a.purchase_return_id = b.purchase_return_id
            WHERE a.data_status = 1
            <!-- 工单退料单/委外订单退料单 -->
            UNION ALL
            SELECT COALESCE(SUM(outboundable_qty), 0) outboundQty
            FROM u_work_picking a
            JOIN u_work_picking_detail b ON a.work_picking_id = b.work_picking_id
            WHERE a.data_status = 1
            AND b.is_material_full_outbounded = 0
        ) t
    </select>

    <!-- 待归还物料数量 -->
    <select id="getPendingLoanReturnQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(pending_return_qty), 0)
        FROM u_material_loan a
        JOIN u_material_loan_detail b ON a.loan_id = b.loan_id
        WHERE a.data_status = 1
        AND b.is_material_returned = 0
    </select>

    <!-- 待完成盘点单 -->
    <select id="getPendingInventoryCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_inventory a
        WHERE a.data_status = 1
        AND inventory_status IN (0, 1)
    </select>

    <!-- 今日入库物料数量 -->
    <select id="getTodayInboundQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(a.change_stock), 0)
        FROM u_material_stock_record a
        WHERE DATE(a.created_dt) = CURRENT_DATE
        AND a.source_form_type IN (0, 1, 2, 3)
    </select>

    <!-- 今日出库物料数量 -->
    <select id="getTodayOutboundQty" resultType="java.math.BigDecimal">
        SELECT ABS(COALESCE(SUM(a.change_stock), 0))
        FROM u_material_stock_record a
        WHERE DATE(a.created_dt) = CURRENT_DATE
        AND a.source_form_type IN (10, 11, 12, 13)
    </select>

    <!-- 今日调拨物料数量 -->
    <select id="getTodayTransferQty" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(a.change_stock), 0)
        FROM u_material_stock_record a
        WHERE DATE(a.created_dt) = CURRENT_DATE
        AND a.source_form_type = 4
    </select>

    <select id="queryMaterialStockLineList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.workbench.bo.WarehouseLineBO">
        SELECT TO_CHAR(createdDt, 'YYYY-MM-DD') date, SUM(inboundQty) inboundQty,
            ABS(SUM(outboundQty)) outboundQty
        FROM (
            SELECT a.change_stock inboundQty, 0 outboundQty, a.created_dt createdDt
            FROM u_material_stock_record a
            WHERE DATE(a.created_dt) > CURRENT_DATE - INTERVAL '30 days'
            AND a.source_form_type IN (0, 1, 2, 3)
            UNION ALL
            SELECT 0 inboundQty, a.change_stock outboundQty, a.created_dt createdDt
            FROM u_material_stock_record a
            WHERE DATE(a.created_dt) > CURRENT_DATE - INTERVAL '30 days'
            AND a.source_form_type IN (10, 11, 12, 13)
        ) t
        GROUP BY TO_CHAR(createdDt, 'YYYY-MM-DD')
    </select>

    <select id="queryMaterialStockRank"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.workbench.bo.WarehouseRankBO">
        SELECT m.material_code, m.material_name, m.spec_attribute_str, SUM(a.stock_qty) value
        FROM u_material_stock a
        LEFT JOIN u_material m ON a.material_id = m.material_id
        WHERE a.stock_qty != 0
        GROUP BY m.material_code, m.material_name, m.spec_attribute_str
        ORDER BY value DESC
        LIMIT 10
    </select>

</mapper>
