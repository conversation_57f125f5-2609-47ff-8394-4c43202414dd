<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryInboundDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailRespVO">
        SELECT b.remark, a.*, b.inbound_detail_id, b.row_no, b.related_row_no, b.material_id, b.material_code,
            b.warehouse_org_id, b.main_unit_dict_id, b.inbound_qty, b.inbounded_qty, c.material_name,
            c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_inbound a
        JOIN u_inbound_detail b ON a.inbound_id = b.inbound_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.inboundCode != null and reqVO.inboundCode != ''">
                AND a.inbound_code LIKE concat('%', #{reqVO.inboundCode}, '%')
            </if>
            <if test="reqVO.inboundTypeDictId != null and reqVO.inboundTypeDictId != ''">
                AND a.inbound_type_dict_id = #{reqVO.inboundTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND b.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.inboundIdList != null and reqVO.inboundIdList.size > 0">
                AND a.inbound_id IN
                <foreach item="item" index="index" collection="reqVO.inboundIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>


    <select id="queryInboundDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailRespVO">
        SELECT b.remark, a.*, b.inbound_detail_id, b.related_order_detail_id, b.row_no, b.related_row_no,
            b.material_id, b.material_code, b.warehouse_org_id, b.main_unit_dict_id, b.inbound_qty,
            b.inbounded_qty, c.material_name, c.material_category_dict_id, c.spec_model,
            c.spec_attribute_str
        FROM u_inbound a
        JOIN u_inbound_detail b ON a.inbound_id = b.inbound_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.inboundId != null">
                AND a.inbound_id = #{reqVO.inboundId}
            </if>
            <if test="reqVO.inboundCode != null and reqVO.inboundCode != ''">
                AND a.inbound_code LIKE concat('%', #{reqVO.inboundCode}, '%')
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="inboundQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.related_order_detail_id as fieldId, b.material_code as code, SUM(b.inbound_qty) as sumQty
        FROM u_inbound_detail b
        WHERE b.inbound_id = #{inboundId}
        GROUP BY b.related_order_detail_id, b.material_code
    </select>

    <select id="materialInboundQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.material_id as fieldId, b.material_code as code, SUM(b.inbound_qty) as sumQty
        FROM u_inbound_detail b
        WHERE b.inbound_id = #{inboundId}
        GROUP BY b.material_id, b.material_code
    </select>

    <select id="inboundQtyDetailList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.related_order_detail_id as fieldId, b.material_id, b.material_code as code,
            b.warehouse_org_id, b.inbound_qty as sumQty
        FROM u_inbound_detail b
        WHERE b.inbound_id = #{inboundId}
    </select>

    <select id="inboundMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.inbound_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_inbound_detail b
        WHERE b.inbound_id IN
        <foreach item="item" index="index" collection="inboundIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.inbound_id, b.material_id, b.warehouse_org_id
    </select>

    <select id="selectListBySourceOrderCode"
            resultType="com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDetailDO">
        SELECT b.* FROM u_inbound a
        JOIN u_inbound_detail b ON a.inbound_id = b.inbound_id
        where a.data_status = 1 and  a.inbound_code = #{sourceOrderCode}
    </select>


</mapper>
