<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.stocklock.StockLockDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryStockLockDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailRespVO">
        SELECT b.remark, a.*, b.lock_detail_id, b.row_no, b.material_stock_id, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.lock_qty, b.unlocked_qty, b.unlockable_qty, b.is_material_full_unlock, b.remark,
            b.material_code, c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_stock_lock a
        JOIN u_stock_lock_detail b ON a.lock_id = b.lock_id
        JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.lockCode != null and reqVO.lockCode != ''">
                AND a.lock_code LIKE concat('%', #{reqVO.lockCode}, '%')
            </if>
            <if test="reqVO.lockTypeDictId != null and reqVO.lockTypeDictId != ''">
                AND a.lock_type_dict_id = #{reqVO.lockTypeDictId}
            </if>
            <if test="reqVO.lockReason != null and reqVO.lockReason != ''">
                AND a.lock_reason LIKE concat('%', #{reqVO.lockReason}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="queryStockLockDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailRespVO">
        SELECT b.lock_detail_id, b.row_no, b.material_stock_id, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.lock_qty, b.unlocked_qty, b.unlockable_qty, b.is_material_full_unlock, b.remark,
            b.material_code, c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_stock_lock_detail b
        JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.lockId != null">
                AND b.lock_id = #{reqVO.lockId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="queryStockLockDetailQuoteList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailRespVO">
        SELECT b.lock_detail_id, b.row_no, b.material_stock_id, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.lock_qty, b.unlocked_qty, b.unlockable_qty, b.material_code,
            c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_stock_lock a
        JOIN u_stock_lock_detail b ON a.lock_id = b.lock_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        WHERE a.data_status = 1
        AND b.unlockable_qty > 0
        <if test="reqVO.lockId != null">
            AND b.lock_id = #{reqVO.lockId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="stockQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.material_stock_id as fieldId, b.material_code as code,
            b.warehouse_org_id, b.lock_qty as sumQty
        FROM u_stock_lock_detail b
        WHERE b.lock_id = #{lockId}
    </select>

    <select id="unlockableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.lock_detail_id as fieldId, b.material_code as code, b.unlockable_qty as sumQty
        FROM u_stock_lock a
        JOIN u_stock_lock_detail b ON a.lock_id = b.lock_id
        WHERE b.lock_id = #{lockId}
        AND a.data_status = 1
    </select>

    <select id="lockMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.lock_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_stock_lock_detail b
        WHERE b.lock_id IN
        <foreach item="item" index="index" collection="lockIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.lock_id, b.material_id, b.warehouse_org_id
    </select>

</mapper>