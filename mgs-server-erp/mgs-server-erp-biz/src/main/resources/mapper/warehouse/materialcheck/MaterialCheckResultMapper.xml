<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialcheck.MaterialCheckResultMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="materialCheckResultMap" type="com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckResultRespVO">
        <id property="checkResultId" column="check_result_id"/>
        <result property="checkResultCode" column="check_result_code"/>
        <result property="checkId" column="check_id"/>
        <result property="checkCode" column="check_code"/>
        <result property="checkTypeDictId" column="check_type_dict_id"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="mainUnitDictId" column="main_unit_dict_id"/>
        <result property="materialCategoryDictId" column="material_category_dict_id"/>
        <result property="specModel" column="spec_model"/>
        <result property="specAttributeStr" column="spec_attribute_str"/>
        <result property="checkQty" column="check_qty"/>
        <result property="allowDefectsQty" column="allow_defects_qty"/>
        <result property="checkResult" column="check_result"/>
        <result property="checkDesc" column="check_desc"/>
        <result property="remark" column="remark"/>
        <result property="okQty" column="ok_qty"/>
        <result property="ngQty" column="ng_qty"/>
        <result property="ngDetailList" column="ng_detail_list" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>
        <result property="directorId" column="director_id"/>
        <result property="directorOrgId" column="director_org_id"/>
        <result property="formDt" column="form_dt"/>
        <result property="dataStatus" column="data_status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="approvedBy" column="approved_by"/>
        <result property="approvedDt" column="approved_dt"/>
    </resultMap>

    <!-- 物料检验统计结果映射 -->
    <resultMap id="materialCheckStatisticsMap" type="com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckStatisticsRespVO">
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="materialCategoryDictName" column="material_category_dict_name"/>
        <result property="specModel" column="spec_model"/>
        <result property="specAttributeStr" column="spec_attribute_str"/>
        <result property="totalCheckQty" column="total_check_qty"/>
        <result property="totalOkQty" column="total_ok_qty"/>
        <result property="totalNgQty" column="total_ng_qty"/>
        <result property="okRate" column="ok_rate"/>
    </resultMap>

    <!-- 不良品原因统计结果映射 -->
    <resultMap id="ngReasonStatisticsMap" type="com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.NgReasonStatistics">
        <result property="ngReasonDictId" column="ng_reason_dict_id"/>
        <result property="ngQty" column="ng_qty"/>
    </resultMap>

    <select id="queryMaterialCheckResultPage" resultMap="materialCheckResultMap">
        SELECT a.*, b.check_type_dict_id, b.material_id, b.material_code, b.main_unit_dict_id,
            b.check_result, b.check_qty, b.allow_defects_qty,c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str
        FROM u_material_check_result a
        LEFT JOIN u_material_check b ON a.check_id = b.check_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.checkResultCode != null and reqVO.checkResultCode != ''">
                AND a.check_result_code LIKE concat('%', #{reqVO.checkResultCode}, '%')
            </if>
            <if test="reqVO.checkCode != null and reqVO.checkCode != ''">
                AND a.check_code LIKE concat('%', #{reqVO.checkCode}, '%')
            </if>
            <if test="reqVO.checkTypeDictId != null and reqVO.checkTypeDictId != ''">
                AND a.check_type_dict_id = #{reqVO.checkTypeDictId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.checkResult != null">
                AND a.check_result = #{reqVO.checkResult}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt DESC
    </select>

    <select id="queryMaterialCheckResultDetail" resultMap="materialCheckResultMap">
        SELECT b.check_desc, a.*, b.check_type_dict_id, b.material_code, b.main_unit_dict_id,
            b.check_result, b.check_qty, b.allow_defects_qty, c.material_name,
            c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_material_check_result a
        LEFT JOIN u_material_check b ON a.check_id = b.check_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        WHERE a.check_result_id = #{checkResultId}
    </select>

    <!-- 物料检验统计查询（分页） -->
    <select id="queryMaterialCheckStatisticsPage" resultMap="materialCheckStatisticsMap">
        SELECT
            c.material_code,
            c.material_name,
            c.material_category_dict_id AS material_category_dict_name,
            c.spec_model,
            c.spec_attribute_str,
            SUM(b.check_qty) AS total_check_qty,
            SUM(COALESCE(a.ok_qty, 0)) AS total_ok_qty,
            SUM(COALESCE(a.ng_qty, 0)) AS total_ng_qty,
            CASE
                WHEN SUM(b.check_qty) > 0 THEN
                    ROUND(SUM(COALESCE(a.ok_qty, 0)) * 100.0 / SUM(b.check_qty), 2)
                ELSE 0
            END AS ok_rate
        FROM u_material_check_result a
        LEFT JOIN u_material_check b ON a.check_id = b.check_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            AND a.data_status = 1
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != '' ">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
        </where>
        GROUP BY c.material_code, c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        ORDER BY c.material_code
    </select>

    <!-- 不良品原因统计查询 - 关联不良品明细表 -->
    <select id="queryNgReasonStatistics" resultMap="ngReasonStatisticsMap">
        SELECT
            d.ng_reason_dict_id,
            SUM(d.ng_qty) AS ng_qty
        FROM u_material_check_result a
        LEFT JOIN u_material_check b ON a.check_id = b.check_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        LEFT JOIN u_material_check_ng_detail d ON a.check_result_id = d.related_order_id
        WHERE a.data_status = 1
            AND c.material_code = #{materialCode}
            AND d.ng_reason_dict_id IS NOT NULL
            AND d.ng_qty > 0
            <if test="startFormDt != null">
                AND a.form_dt >= #{startFormDt}
            </if>
            <if test="endFormDt != null">
                AND a.form_dt &lt;= #{endFormDt}
            </if>
        GROUP BY d.ng_reason_dict_id
        ORDER BY SUM(d.ng_qty) DESC
    </select>

</mapper>
