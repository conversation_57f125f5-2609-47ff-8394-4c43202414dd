<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialcheck.MaterialCheckMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="materialCheckMap" type="com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.MaterialCheckRespVO">
        <id property="checkId" column="check_id"/>
        <result property="checkCode" column="check_code"/>
        <result property="checkTypeDictId" column="check_type_dict_id"/>
        <result property="relatedOrderId" column="related_order_id"/>
        <result property="relatedOrderCode" column="related_order_code"/>
        <result property="relatedRowNo" column="relatedRowNo"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="mainUnitDictId" column="main_unit_dict_id"/>
        <result property="receiptQty" column="receiptQty"/>
        <result property="checkQty" column="check_qty"/>
        <result property="allowDefectsQty" column="allow_defects_qty"/>
        <result property="checkDesc" column="check_desc"/>
        <result property="remark" column="remark"/>
        <result property="checkResult" column="check_result"/>
        <result property="okQty" column="ok_qty"/>
        <result property="ngQty" column="ng_qty"/>
        <result property="ngDetailList" column="ng_detail_list" typeHandler="com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler"/>
        <result property="bizType" column="biz_type"/>
        <result property="directorId" column="director_id"/>
        <result property="directorOrgId" column="director_org_id"/>
        <result property="formDt" column="form_dt"/>
        <result property="dataStatus" column="data_status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="approvedBy" column="approved_by"/>
        <result property="approvedDt" column="approved_dt"/>
        <result property="materialName" column="material_name"/>
        <result property="materialCategoryDictId" column="material_category_dict_id"/>
        <result property="specModel" column="spec_model"/>
        <result property="specAttributeStr" column="spec_attribute_str"/>
    </resultMap>

    <select id="queryMaterialCheckPage" resultMap="materialCheckMap">
        SELECT a.*, b.material_name, b.material_category_dict_id,
            b.spec_model, b.spec_attribute_str, c.row_no as relatedRowNo, c.receipt_qty as receiptQty
        FROM u_material_check a
        LEFT JOIN u_material b ON a.material_id = b.material_id AND b.deleted = 0
        LEFT JOIN u_receipt_detail c ON a.biz_type = 1 AND a.related_order_detail_id = c.receipt_detail_id
        <where>
            <if test="reqVO.checkCode != null and reqVO.checkCode != ''">
                AND a.check_code LIKE concat('%', #{reqVO.checkCode}, '%')
            </if>
            <if test="reqVO.checkTypeDictId != null and reqVO.checkTypeDictId != ''">
                AND a.check_type_dict_id = #{reqVO.checkTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND b.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND b.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND b.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.checkResult != null">
                AND a.check_result = #{reqVO.checkResult}
            </if>
            <if test="reqVO.bizType != null">
                AND a.biz_type = #{reqVO.bizType}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt DESC
    </select>

    <select id="queryMaterialCheckDetail" resultMap="materialCheckMap">
        SELECT a.*, b.material_name, b.material_name, b.material_category_dict_id,
            b.spec_model, b.spec_attribute_str, c.row_no as relatedRowNo, c.receipt_qty as receiptQty
        FROM u_material_check a
        LEFT JOIN u_material b ON a.material_id = b.material_id AND b.deleted = 0
        LEFT JOIN u_receipt_detail c ON a.biz_type = 1 AND a.related_order_detail_id = c.receipt_detail_id
        WHERE a.check_id = #{checkId}
        ORDER BY a.created_dt DESC
    </select>

</mapper>
