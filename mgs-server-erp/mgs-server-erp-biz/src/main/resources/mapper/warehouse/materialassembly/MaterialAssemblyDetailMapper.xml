<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialassembly.MaterialAssemblyDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryMaterialAssemblyDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail.MaterialAssemblyDetailRespVO">
        SELECT b.remark, a.*, b.assembly_detail_id, b.row_no, b.material_stock_id, b.material_id, b.material_code,
            b.warehouse_org_id, b.main_unit_dict_id, b.outbound_qty, b.inbound_qty, c.material_name assemblyMaterialName,
            c.material_category_dict_id assemblyMaterialCategoryDictId, c.spec_model assemblySpecModel, c.spec_attribute_str assemblySpecAttributeStr,
            d.material_name, d.material_category_dict_id, d.spec_model, d.spec_attribute_str
        FROM u_material_assembly a
        JOIN u_material_assembly_detail b ON a.assembly_id = b.assembly_id
        LEFT JOIN u_material c ON a.assembly_material_id  = c.material_id AND c.deleted = 0
        LEFT JOIN u_material d ON b.material_id = d.material_id AND d.deleted = 0
        <where>
            <if test="reqVO.assemblyCode != null and reqVO.assemblyCode != ''">
                AND a.assembly_code LIKE concat('%', #{reqVO.assemblyCode}, '%')
            </if>
            <if test="reqVO.assemblyName != null and reqVO.assemblyName != ''">
                AND a.assembly_name LIKE concat('%', #{reqVO.assemblyName}, '%')
            </if>
            <if test="reqVO.assemblyTypeDictId != null and reqVO.assemblyTypeDictId != ''">
                AND a.assembly_type_dict_id = #{reqVO.assemblyTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.assemblyMaterialCode != null and reqVO.assemblyMaterialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.assemblyMaterialCode}, '%')
            </if>
            <if test="reqVO.assemblyMaterialName != null and reqVO.assemblyMaterialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.assemblyMaterialName}, '%')
            </if>
            <if test="reqVO.assemblySpecModel != null and reqVO.assemblySpecModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.assemblySpecModel}, '%')
            </if>
            <if test="reqVO.assemblyMaterialCategoryDictId != null and reqVO.assemblyMaterialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.assemblyMaterialCategoryDictId}
            </if>
            <if test="reqVO.bizType != null">
                AND a.biz_type = #{reqVO.bizType}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="queryMaterialAssemblyDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail.MaterialAssemblyDetailRespVO">
        SELECT b.assembly_detail_id, b.row_no, b.material_stock_id, b.material_id, b.material_code,
            b.warehouse_org_id, b.main_unit_dict_id, b.outbound_qty, b.inbound_qty, b.remark,
            c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str
        FROM  u_material_assembly_detail b
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.assemblyId != null">
                AND b.assembly_id = #{reqVO.assemblyId}
            </if>
            <if test="reqVO.assemblyCode != null and reqVO.assemblyCode != ''">
                AND a.assembly_code LIKE concat('%', #{reqVO.assemblyCode}, '%')
            </if>
            <if test="reqVO.assemblyName != null and reqVO.assemblyName != ''">
                AND a.assembly_name LIKE concat('%', #{reqVO.assemblyName}, '%')
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="outboundQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.material_stock_id as fieldId, b.material_code as code, b.material_stock_id,
            b.warehouse_org_id, b.outbound_qty as sumQty
        FROM u_material_assembly_detail b
        WHERE b.assembly_id = #{assemblyId}
    </select>

    <select id="inboundQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.assembly_detail_id as fieldId, b.material_code as code, b.material_id,
            b.warehouse_org_id ,b.inbound_qty as sumQty
        FROM u_material_assembly_detail b
        WHERE b.assembly_id = #{assemblyId}
    </select>

    <select id="assemblyMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.assembly_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_material_assembly_detail b
        WHERE b.assembly_id IN
        <foreach item="item" index="index" collection="assemblyIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.assembly_id, b.material_id, b.warehouse_org_id
    </select>

</mapper>
