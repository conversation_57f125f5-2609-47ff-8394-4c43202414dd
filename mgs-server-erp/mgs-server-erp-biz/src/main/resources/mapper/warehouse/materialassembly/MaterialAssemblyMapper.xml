<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialassembly.MaterialAssemblyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryMaterialAssemblyPage" resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.MaterialAssemblyRespVO">
        SELECT a.*, b.material_name as assemblyMaterialName, b.material_category_dict_id assemblyMaterialCategoryDictId,
            b.spec_model assemblySpecModel, b.spec_attribute_str assemblySpecAttributeStr
        FROM u_material_assembly a
        LEFT JOIN u_material b ON a.assembly_material_id = b.material_id
        <where>
            <if test="reqVO.assemblyCode != null and reqVO.assemblyCode != ''">
                AND a.assembly_code LIKE concat('%', #{reqVO.assemblyCode}, '%')
            </if>
            <if test="reqVO.assemblyName != null and reqVO.assemblyName != ''">
                AND a.assembly_name LIKE concat('%', #{reqVO.assemblyName}, '%')
            </if>
            <if test="reqVO.assemblyTypeDictId != null and reqVO.assemblyTypeDictId != ''">
                AND a.assembly_type_dict_id = #{reqVO.assemblyTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.assemblyMaterialCode != null and reqVO.assemblyMaterialCode != ''">
                AND a.assembly_material_code LIKE concat('%', #{reqVO.assemblyMaterialCode}, '%')
            </if>
            <if test="reqVO.assemblyMaterialName != null and reqVO.assemblyMaterialName != ''">
                AND b.material_name LIKE concat('%', #{reqVO.assemblyMaterialName}, '%')
            </if>
            <if test="reqVO.assemblySpecModel != null and reqVO.assemblySpecModel != ''">
                AND b.spec_model LIKE concat('%', #{reqVO.assemblySpecModel}, '%')
            </if>
            <if test="reqVO.assemblyMaterialCategoryDictId != null and reqVO.assemblyMaterialCategoryDictId != ''">
                AND b.material_category_dict_id = #{reqVO.assemblyMaterialCategoryDictId}
            </if>
            <if test="reqVO.bizType != null">
                AND a.biz_type = #{reqVO.bizType}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt DESC
    </select>

    <select id="queryMaterialAssemblyDetail" resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.MaterialAssemblyRespVO">
        SELECT a.*, b.material_name as assemblyMaterialName, b.material_category_dict_id assemblyMaterialCategoryDictId,
            b.spec_model assemblySpecModel, b.spec_attribute_str assemblySpecAttributeStr
        FROM u_material_assembly a
        LEFT JOIN u_material b ON a.assembly_material_id = b.material_id
        WHERE a.assembly_id = #{assemblyId}
    </select>

</mapper>
