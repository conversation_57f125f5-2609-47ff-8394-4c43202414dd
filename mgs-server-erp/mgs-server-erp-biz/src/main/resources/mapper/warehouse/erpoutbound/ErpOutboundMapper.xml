<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="findErpOutboundQty"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.bo.ErpOutboundRespBO">
        SELECT outs.sale_order_id as relatedOrderId,sum(out_detail.delivered_qty) as delivered_qty
        FROM u_outbound as outs
             left join u_outbound_detail out_detail on outs.outbound_id=out_detail.outbound_id
        <where>
            outs.data_status = 1
            <if test="ids != null and ids.size > 0 ">
                and outs.sale_order_id in
                <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY outs.sale_order_id

    </select>

    <select id="queryOutboundQtyBy"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.bo.ErpOutboundRespBO">
        SELECT outs.sale_order_id as relatedOrderId,sum(out_detail.delivered_qty) as delivered_qty
        FROM u_outbound as outs
             left join u_outbound_detail out_detail on outs.outbound_id=out_detail.outbound_id
        <where>
            <if test="reqVO.dataStatus != null">
                and outs.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.relatedOrderIdList != null and reqVO.relatedOrderIdList.size > 0 ">
                and outs.sale_order_id in
                <foreach item="item" index="index" collection="reqVO.relatedOrderIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY outs.sale_order_id

    </select>

    <select id="selectListBy" resultType="java.lang.Double">
        SELECT
            sum(t2.outbounded_qty)
        FROM u_outbound t1
        JOIN u_outbound_detail t2 ON t1.outbound_id = t2.outbound_id
        WHERE t1.data_status = 1
          AND t1.form_dt >= #{startDt} AND t1.form_dt &lt;= #{endDt}
    </select>
    <select id="queryPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.ErpOutboundRespVO">
        SELECT outbound.* FROM erp.u_outbound outbound
        LEFT JOIN erp.u_delivery delivery ON outbound.delivery_id = delivery.delivery_id
        <where>
            <if test="reqVO.outboundCode != null and reqVO.outboundCode != ''">
                AND outbound.outbound_code LIKE CONCAT('%', #{reqVO.outboundCode}, '%')
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND outbound.related_order_code LIKE CONCAT('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.bizType != null">
                AND outbound.biz_type = #{reqVO.bizType}
            </if>
            <if test="reqVO.startDeliveryDate != null and reqVO.endDeliveryDate != null">
                AND (delivery.delivery_date BETWEEN #{reqVO.startDeliveryDate} AND #{reqVO.endDeliveryDate})
            </if>
            <if test="reqVO.startFormDt != null and reqVO.endFormDt != null">
                AND (outbound.form_dt BETWEEN #{reqVO.startFormDt} AND #{reqVO.endFormDt})
            </if>
            <if test="reqVO.receiptObjId != null">
                AND delivery.receipt_obj_id = #{reqVO.receiptObjId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND outbound.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND outbound.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND outbound.director_org_id = #{reqVO.directorOrgId}
            </if>
        </where>
        ORDER BY outbound.outbound_id DESC
    </select>

</mapper>
