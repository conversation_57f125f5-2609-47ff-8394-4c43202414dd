<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryOutboundDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailRespVO">
        SELECT b.remark, a.*, b.outbound_detail_id, b.row_no, b.related_row_no, b.material_id, b.material_code,
            b.warehouse_org_id, b.main_unit_dict_id, b.outbound_qty, b.outbounded_qty, b.delivered_qty,
            b.deliverable_qty, b.detail_type_dict_id, c.material_name,
            c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_outbound a
        JOIN u_outbound_detail b ON a.outbound_id = b.outbound_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        LEFT JOIN u_delivery d ON d.delivery_id = a.delivery_id
        <where>
            <if test="reqVO.outboundCode != null and reqVO.outboundCode != ''">
                AND a.outbound_code LIKE concat('%', #{reqVO.outboundCode}, '%')
            </if>
            <if test="reqVO.outboundTypeDictId != null and reqVO.outboundTypeDictId != ''">
                AND a.outbound_type_dict_id = #{reqVO.outboundTypeDictId}
            </if>
            <if test="reqVO.relatedOrderId != null ">
                AND a.related_order_id = #{reqVO.relatedOrderId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND b.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.warehouseOrgId != null and reqVO.warehouseOrgId != ''">
                AND b.warehouse_org_id = #{reqVO.warehouseOrgId}
            </if>
            <if test="reqVO.detailTypeDictId != null and reqVO.detailTypeDictId != ''">
                AND b.detail_type_dict_id = #{reqVO.detailTypeDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
            <if test="reqVO.saleOrderId != null">
                AND a.sale_order_id = #{reqVO.saleOrderId}
            </if>
            <if test="reqVO.customerId != null">
                AND d.receipt_obj_id = #{reqVO.customerId}
            </if>
            <if test="reqVO.outboundIdList != null and reqVO.outboundIdList.size > 0">
                AND a.outbound_id IN
                <foreach item="item" index="index" collection="reqVO.outboundIdList"  open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="reqVO.outboundTypeDictIdList != null and reqVO.outboundTypeDictIdList.size > 0 ">
                AND a.outbound_type_dict_id IN
                <foreach item="item" index="index" collection="reqVO.outboundTypeDictIdList"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.created_dt desc, b.detail_type_dict_id asc, b.row_no asc
    </select>


    <select id="queryOutboundDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailRespVO">
        SELECT b.remark, a.*, b.outbound_detail_id, b.related_order_detail_id, b.row_no, b.related_row_no, b.material_stock_id,
            b.material_id, b.material_code, b.warehouse_org_id, b.main_unit_dict_id, b.outbound_qty, b.outbounded_qty,
            b.delivered_qty, b.deliverable_qty, b.detail_type_dict_id, c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str
        FROM u_outbound a
        JOIN u_outbound_detail b ON a.outbound_id = b.outbound_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.outboundId != null">
                AND a.outbound_id = #{reqVO.outboundId}
            </if>
            <if test="reqVO.outboundCode != null and reqVO.outboundCode != ''">
                AND a.outbound_code LIKE concat('%', #{reqVO.outboundCode}, '%')
            </if>
            <if test="reqVO.isMaterialFullDelivered != null">
                AND b.is_material_full_delivered = #{reqVO.isMaterialFullDelivered}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="outboundQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.related_order_detail_id as fieldId, b.material_code as code, SUM(b.outbound_qty) as sumQty
        FROM u_outbound_detail b
        WHERE b.outbound_id = #{outboundId}
        GROUP BY b.related_order_detail_id, b.material_code AND b.detail_type_dict_id = '0'
    </select>

    <select id="deliverableQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.outbound_detail_id as fieldId, b.material_code as code, b.deliverable_qty as sumQty
        FROM u_outbound_detail b
        WHERE b.outbound_id = #{outboundId} AND b.detail_type_dict_id = '0'
    </select>

    <select id="deliveredQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT outdetail.material_id, SUM(outdetail.delivered_qty) sumQty
        FROM erp.u_outbound_detail outdetail
        LEFT JOIN erp.u_outbound outbound ON outbound.outbound_id = outdetail.outbound_id
        WHERE outbound.related_order_id = #{relatedOrderId}
        GROUP BY outdetail.material_id
    </select>

    <select id="materialOutboundQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.material_id as fieldId, b.material_code as code, SUM(b.outbound_qty) as sumQty
        FROM u_outbound_detail b
        WHERE b.outbound_id = #{outboundId} AND b.detail_type_dict_id = '0'
        GROUP BY b.material_id, b.material_code
    </select>
    <select id="queryOutboundQtyByPurchaseId"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailRespVO">
        SELECT detail.material_id, SUM(detail.outbound_qty) FROM erp.u_outbound_detail detail
        LEFT JOIN erp.u_outbound outbound ON outbound.outbound_id = detail.outbound_id
        LEFT JOIN erp.u_purchase_exchange exchange ON exchange.purchase_exchange_id = outbound.related_order_id
        LEFT JOIN erp.u_purchase_order purchase ON purchase.purchase_order_id = exchange.purchase_order_id
        WHERE purchase.purchase_order_id = #{purchaseOrderId} AND outbound.data_status = 1
        GROUP BY detail.material_id
    </select>

    <select id="selectListBySourceOrderCode"
            resultType="com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDetailDO">
        SELECT b.*
        FROM u_outbound a JOIN u_outbound_detail b ON a.outbound_id = b.outbound_id
        where a.data_status = 1 and a.outbound_code = #{sourceOrderCode}
    </select>

    <select id="outboundMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.outbound_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_outbound_detail b
        WHERE b.outbound_id IN
        <foreach item="item" index="index" collection="outboundIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.outbound_id, b.material_id, b.warehouse_org_id
    </select>

</mapper>
