<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.warehouse.dal.mysql.materialloan.MaterialLoanDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryMaterialLoanDetailPage"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo.detail.MaterialLoanDetailRespVO">
        SELECT b.remark, a.*, b.loan_detail_id, b.row_no, b.material_stock_id, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.loan_qty, b.plan_return_date, b.returned_qty, b.pending_return_qty,
            b.is_material_returned, b.material_code, c.material_name, c.material_category_dict_id,
            c.spec_model, c.spec_attribute_str
        FROM u_material_loan a
        JOIN u_material_loan_detail b ON a.loan_id = b.loan_id
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.loanCode != null and reqVO.loanCode != ''">
                AND a.loan_code LIKE concat('%', #{reqVO.loanCode}, '%')
            </if>
            <if test="reqVO.loanName != null and reqVO.loanName != ''">
                AND a.loan_name LIKE concat('%', #{reqVO.loanName}, '%')
            </if>
            <if test="reqVO.loanTypeDictId != null and reqVO.loanTypeDictId != ''">
                AND a.loan_type_dict_id = #{reqVO.loanTypeDictId}
            </if>
            <if test="reqVO.relatedOrderCode != null and reqVO.relatedOrderCode != ''">
                AND a.related_order_code LIKE concat('%', #{reqVO.relatedOrderCode}, '%')
            </if>
            <if test="reqVO.loanType != null">
                AND a.loan_type = #{reqVO.loanType}
            </if>
            <if test="reqVO.loanObjId != null">
                AND a.loan_obj_id = #{reqVO.loanObjId}
            </if>
            <if test="reqVO.loanOrgId != null and reqVO.loanOrgId != ''">
                AND a.loan_org_id = #{reqVO.loanOrgId}
            </if>
            <if test="reqVO.contactName != null and reqVO.contactName != ''">
                AND a.contact_name LIKE concat('%', #{reqVO.contactName}, '%')
            </if>
            <if test="reqVO.contactPhone != null and reqVO.contactPhone != ''">
                AND a.contact_phone LIKE concat('%', #{reqVO.contactPhone}, '%')
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
            <if test="reqVO.dataStatus != null">
                AND a.data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.directorId != null">
                AND a.director_id = #{reqVO.directorId}
            </if>
            <if test="reqVO.directorOrgId != null and reqVO.directorOrgId != ''">
                AND a.director_org_id  = #{reqVO.directorOrgId}
            </if>
            <if test="reqVO.startFormDt != null">
                AND a.form_dt >= #{reqVO.startFormDt}
            </if>
            <if test="reqVO.endFormDt != null">
                AND a.form_dt &lt;= #{reqVO.endFormDt}
            </if>
        </where>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="queryMaterialLoanDetailList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo.detail.MaterialLoanDetailRespVO">
        SELECT b.loan_detail_id, b.row_no, b.material_stock_id, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.loan_qty, b.plan_return_date, b.returned_qty, b.pending_return_qty,
            b.is_material_returned, b.remark, b.material_code, c.material_name,
            c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_material_loan_detail b
        LEFT JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        <where>
            <if test="reqVO.loanId != null">
                AND b.loan_id = #{reqVO.loanId}
            </if>
            <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
                AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
            </if>
            <if test="reqVO.materialName != null and reqVO.materialName != ''">
                AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
            </if>
            <if test="reqVO.specModel != null and reqVO.specModel != ''">
                AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
            </if>
            <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
                AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
            </if>
        </where>
        ORDER BY b.row_no asc
    </select>

    <select id="queryMaterialLoanDetailQuoteList"
            resultType="com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo.detail.MaterialLoanDetailRespVO">
        SELECT b.loan_detail_id, b.row_no, b.material_stock_id, b.material_id, b.warehouse_org_id,
            b.main_unit_dict_id, b.loan_qty, b.plan_return_date, b.returned_qty, b.pending_return_qty,
            b.material_code, c.material_name, c.material_category_dict_id, c.spec_model, c.spec_attribute_str
        FROM u_material_loan a
        JOIN u_material_loan_detail b ON a.loan_id = b.loan_id
        JOIN u_material c ON b.material_id = c.material_id AND c.deleted = 0
        WHERE a.data_status = 1
        AND b.pending_return_qty > 0
        <if test="reqVO.loanId != null">
            AND b.loan_id = #{reqVO.loanId}
        </if>
        <if test="reqVO.materialCode != null and reqVO.materialCode != ''">
            AND c.material_code LIKE concat('%', #{reqVO.materialCode}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND c.material_name LIKE concat('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.specModel != null and reqVO.specModel != ''">
            AND c.spec_model LIKE concat('%', #{reqVO.specModel}, '%')
        </if>
        <if test="reqVO.materialCategoryDictId != null and reqVO.materialCategoryDictId != ''">
            AND c.material_category_dict_id = #{reqVO.materialCategoryDictId}
        </if>
        ORDER BY a.created_dt desc, b.row_no asc
    </select>

    <select id="loanQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.material_stock_id as fieldId, b.material_code as code,
            b.warehouse_org_id, b.loan_qty as sumQty
        FROM u_material_loan_detail b
        WHERE b.loan_id = #{loanId}
    </select>

    <select id="pendingReturnQtyList" resultType="com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO">
        SELECT b.loan_detail_id as fieldId, b.material_code as code, b.pending_return_qty as sumQty
        FROM u_material_loan a
        JOIN u_material_loan_detail b ON a.loan_id = b.loan_id
        WHERE b.loan_id = #{loanId}
        AND a.data_status = 1
    </select>

    <select id="loanMaterialWarehouseList" resultType="com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO">
        SELECT b.loan_id as orderId, b.material_id, b.warehouse_org_id
        FROM u_material_loan_detail b
        WHERE b.loan_id IN
        <foreach item="item" index="index" collection="loanIdList"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY b.loan_id, b.material_id, b.warehouse_org_id
    </select>


</mapper>
