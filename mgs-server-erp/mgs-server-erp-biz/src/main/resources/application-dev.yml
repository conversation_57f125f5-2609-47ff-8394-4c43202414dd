# 开发环境
base-ip: ************** # 开发环境
db-port: 54321 # 端口
db-username: postgres # 账号
db-password: postgres1 # 密码

jgzy-db: mgs-jgzy

# 数据权限配置的数据库
data-permission:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************************************************************************
    username: ${db-username}
    password: ${db-password}

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${base-ip}:${db-port}/${jgzy-db}?currentSchema=erp&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true
    username: ${db-username}
    password: ${db-password}

  # redis
  data:
    redis:
      host: ${base-ip}
      port: 6379 # 开发环境
      database: 11
      timeout: 5000ms # 连接超时时间（毫秒）
      jedis:
        pool:
          max-active: 100 # 连接池最大连接数（使用负值表示没有限制）
          max-idle: 30 # 连接池中的最大空闲连接
          min-idle: 30 # 连接池中的最小空闲连接
          max-wait: 5000ms # 连接池最大阻塞等待时间（使用负值表示没有限制）

file:
  local: # 本地配置,最终文件路劲 = /路劲/文件类型/文件名.png = /Users/<USER>/Desktop/beiyong/upload/ireve/image/aaa.png
    domain: http://image.mongoso.com # 图片服务器域名
    path: C:/Users/<USER>/Desktop/beiyong/image # win图片存放目录
    expires-time: 500
  #    path: /Users/<USER>/Desktop/beiyong/image/upload # mac路劲
  ali: # 阿里配置
    accessKey: LTAI5tSG1z4d2vEZLgX4mxho
    secret-key: ****************************** # 密码
    bucketname: jinggongzhiyun-test # 空间名
    end-point: oss-cn-shenzhen.aliyuncs.com # 模块名
    domain: https://jinggongzhiyun-test.oss-cn-shenzhen.aliyuncs.com # 第三方域名,内网，部署用的
    path: /platform # 路劲

  qiniu: # 七牛配置,最终文件路劲 = /路劲/文件类型/文件名.png = /ireve/image/aaa.png
    access-key: 4UgnSE4vpzLfABDxGQoS4V6emcT2T79KJDSEkp9t # 账号
    secret-key: wkBBKRVSSqr1FpzHABtxhqo_tSuD54HC4b7pRmG5 # 密码
    bucketname: mgsfile # 空间名
    expires-time: 3600 #  token有效期，单位秒
    domain: https://files.mgsdev.cn # 第三方域名
    path: /ireve # 路劲
  upyun: # 又拍云配置,最终文件路劲 = /路劲/文件类型/文件名.png = /ireve/image/aaa.png
    access-key: fcimage # 账号
    secret-key: zbOkMYB3I9lcUDy85waA7j2fZ8BTqnR1 # 密码
    bucketname: fcimage # 空间名
    expires-time: 30 #  请求超时，单位秒
    domain: https://fcimage.fangcang.com # 第三方域名
    path: /ireve # 路劲

logging:
  level:
    root: info # 打印所有的info日志
#    com.mongoso.mgs.module.system.dal.mysql: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.employeearchives.dal.mysql: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.company.dal.mysql.company: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.dict.dal.mysql: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.employee.dal.mysql.employeearchives: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.purchase.dal.mysql.order: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.base.dal.mysql: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.produce.dal.mysql: DEBUG # 打印该包下的debug日志
#    com.mongoso.mgs.module.workpicking.dal.mysql: DEBUG # 打印该包下的debug日志

mgs:
  gateway:
    enable: true  # true:网关解决跨域 false:应用服务器解决跨域
  excel:
    download-url: https://${base-ip}/export/download
  log:
    log-type: local # 日志类型，local：本地，sls：阿里
    access-log: false # 访问日志 true：开启，false：关闭
    error-log: false # 访问日志
    operate-log: false  # 操作日志
  # 接入阿里SLS日志配置
  sls:
    endpoint: cn-shenzhen.log.aliyuncs.com
    access-key-id: LTAI5tSG1z4d2vEZLgX4mxho
    access-key-secret: ******************************
    project: seikosmartcloud-test
    logStore: platform-erp

xxl:
  job:
    enable: false
    ### 执行器通讯TOKEN [选填]：非空时启用；
    accessToken: default_token
    admin:
      addresses: http://*************:8082/xxl-job-admin
    executor:
      ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address: ''
      ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: xxl-job-erp
      ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip: ''
      ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: ./log/${spring.application.name}/xxl-log
      ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 3
      ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      port: 18020