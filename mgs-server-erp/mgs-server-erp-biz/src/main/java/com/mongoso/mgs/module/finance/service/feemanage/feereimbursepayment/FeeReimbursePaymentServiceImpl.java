package com.mongoso.mgs.module.finance.service.feemanage.feereimbursepayment;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeloan.vo.FeeLoanRespVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimbursepayment.vo.FeeReimbursePaymentAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimbursepayment.vo.FeeReimbursePaymentPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimbursepayment.vo.FeeReimbursePaymentQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimbursepayment.vo.FeeReimbursePaymentRespVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feerelatedloan.vo.FeeRelatedLoanQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feerelatedloan.vo.FeeRelatedLoanRespVO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feereimburse.FeeReimburseDO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feereimbursepayment.FeeReimbursePaymentDO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feerelatedloan.FeeRelatedLoanDO;
import com.mongoso.mgs.module.finance.dal.mysql.feemanage.feereimbursepayment.FeeReimbursePaymentMapper;
import com.mongoso.mgs.module.finance.dal.mysql.feemanage.feerelatedloan.FeeRelatedLoanMapper;
import com.mongoso.mgs.module.finance.handler.approve.feemanage.FeeReimbursePaymentHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.feemanage.FeeReimbursePaymentFlowCallBackHandler;
import com.mongoso.mgs.module.finance.service.feemanage.feerelatedloan.FeeRelatedLoanService;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.finance.enums.ErrorCodeConstants.*;


/**
 * 费用报销付款 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FeeReimbursePaymentServiceImpl implements FeeReimbursePaymentService {

    @Resource
    private FeeReimbursePaymentMapper feeReimbursePaymentMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    @Lazy
    private FeeReimbursePaymentHandler reimbursePaymentHandler;

    @Resource
    private SeqService seqService;

    @Resource
    private FeeRelatedLoanMapper feeRelatedLoanMapper;

    @Resource
    private FeeRelatedLoanService feeRelatedLoanService;

    @Resource
    private FeeReimbursePaymentFlowCallBackHandler feeReimbursePaymentFlowCallBackHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long feeReimbursePaymentAdd(FeeReimbursePaymentAditReqVO reqVO) {
        // 插入
        FeeReimbursePaymentDO feeReimbursePayment = BeanUtilX.copy(reqVO, FeeReimbursePaymentDO::new);
        //报销付款单号
        String code = seqService.getGenerateCode(reqVO.getReimbursePaymentCode(), MenuEnum.EXPENSE_REIMBURSEMENT_PAYMENT.menuId);
        feeReimbursePayment.setReimbursePaymentCode(code);

        feeReimbursePaymentMapper.insert(feeReimbursePayment);
        //新增报销关联借款单
        reqVO.setFeeReimbursePaymentId(feeReimbursePayment.getFeeReimbursePaymentId());
        reqVO.setReimbursePaymentCode(code);
        this.feeRelatedLoanAdd(reqVO.getLoanList(),reqVO);
        // 返回
        return feeReimbursePayment.getFeeReimbursePaymentId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long feeReimbursePaymentEdit(FeeReimbursePaymentAditReqVO reqVO) {
        // 校验存在
//        this.feeReimbursePaymentValidateExists(reqVO.getFeeReimbursePaymentId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.feeReimbursePaymentValidateExists(reqVO.getFeeReimbursePaymentId()), reqVO);

        // 更新
        FeeReimbursePaymentDO feeReimbursePayment = BeanUtilX.copy(reqVO, FeeReimbursePaymentDO::new);
        feeReimbursePaymentMapper.updateById(feeReimbursePayment);
        //先删除后新增
        feeRelatedLoanMapper.deletebByReimbursePaymentId(reqVO.getFeeReimbursePaymentId());
        //新增报销关联借款单
        this.feeRelatedLoanAdd(reqVO.getLoanList(),reqVO);
        // 返回
        return feeReimbursePayment.getFeeReimbursePaymentId();
    }

    @Override
    public void feeReimbursePaymentDel(Long feeReimbursePaymentId) {
        // 校验存在
        FeeReimbursePaymentDO returnDo = this.feeReimbursePaymentValidateExists(feeReimbursePaymentId);
        if(returnDo.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey().shortValue()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        feeReimbursePaymentMapper.deleteById(feeReimbursePaymentId);
    }

    private FeeReimbursePaymentDO feeReimbursePaymentValidateExists(Long feeReimbursePaymentId) {
        FeeReimbursePaymentDO feeReimbursePayment = feeReimbursePaymentMapper.selectById(feeReimbursePaymentId);
        if (feeReimbursePayment == null) {
            // throw exception(FEE_REIMBURSE_PAYMENT_NOT_EXISTS);
            throw new BizException("5001", "费用报销付款不存在");
        }
        return feeReimbursePayment;
    }

    @Override
    public FeeReimbursePaymentRespVO feeReimbursePaymentDetail(Long feeReimbursePaymentId) {
        FeeReimbursePaymentRespVO respVO = feeReimbursePaymentMapper.selectByIdDetail(feeReimbursePaymentId);
        //查询关联借款单
        FeeRelatedLoanQueryReqVO reqVO = new FeeRelatedLoanQueryReqVO();
        reqVO.setFeeReimbursePaymentId(feeReimbursePaymentId);
        List<FeeRelatedLoanRespVO> loanDOList = feeRelatedLoanService.feeRelatedLoanList(reqVO);
        List<FeeLoanRespVO> loanRespVOList = BeanUtilX.copy(loanDOList, FeeLoanRespVO :: new);

        respVO.setLoanList(loanRespVOList);
        //VO属性填充
        fillVoProperties(respVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(feeReimbursePaymentId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<FeeReimbursePaymentRespVO> feeReimbursePaymentList(FeeReimbursePaymentQueryReqVO reqVO) {
        List<FeeReimbursePaymentRespVO> respVOList = BeanUtilX.copy(feeReimbursePaymentMapper.selectList(reqVO), FeeReimbursePaymentRespVO :: new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<FeeReimbursePaymentRespVO> feeReimbursePaymentPage(FeeReimbursePaymentPageReqVO reqVO) {
        IPage<FeeReimbursePaymentRespVO> paymentIPage = feeReimbursePaymentMapper.selectPage(PageUtilX.buildParam(reqVO),reqVO);
        PageResult<FeeReimbursePaymentRespVO> pageResult = PageUtilX.buildResult(paymentIPage);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    @Override
    public ResultX<BatchResult> feeReimbursePaymentDelBatch(IdReq reqVO) {
        //获取对象属性名
        String id = EntityUtilX.getPropertyName(FeeReimbursePaymentDO:: getFeeReimbursePaymentId);
        String code = EntityUtilX.getPropertyName(FeeReimbursePaymentDO::getReimbursePaymentCode);

        return erpBaseService.batchDelete(reqVO.getIdList(), FeeReimbursePaymentDO.class, null, id, code);
    }


    @Override
    public BatchResult feeReimbursePaymentApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<FeeReimbursePaymentDO> list = feeReimbursePaymentMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (FeeReimbursePaymentDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus().intValue());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = reimbursePaymentHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getReimbursePaymentCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (FeeReimbursePaymentDO item : list) {
                String reason = reasonMap.get(item.getReimbursePaymentCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getFeeReimbursePaymentId());
                    messageInfoBO.setObjCode(item.getReimbursePaymentCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getFeeReimbursePaymentId());
                    messageInfoBO.setObjCode(item.getReimbursePaymentCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object feeReimbursePaymentFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        FeeReimbursePaymentDO item = this.feeReimbursePaymentValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();
        return feeReimbursePaymentFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }


    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(FeeReimbursePaymentRespVO respVO) {
        List<FeeReimbursePaymentRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<FeeReimbursePaymentRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> bankIdList = new ArrayList<>();
        List<Long> reimburserIdList = new ArrayList<>();
        for(FeeReimbursePaymentRespVO respVO : respVOList){
            directorIdList.add(respVO.getDirectorId());
            directorOrgIdList.add(respVO.getDirectorOrgId());
            bankIdList.add(respVO.getOutBillAccountId());
            reimburserIdList.add(respVO.getReimburserId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //出账账户
        Map<Long, String> bankMap = erpBaseService.getBankAccountByIdList(bankIdList);

        //查询报销人
        Map<Long, String> reimburserMap = erpBaseService.getEmpNameByIdList(reimburserIdList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        // 属性填充
        for (FeeReimbursePaymentRespVO item : respVOList) {

            // 币种
            if(StrUtilX.isNotEmpty(item.getCurrencyDictId())){
                String currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + item.getCurrencyDictId();
                item.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = item.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                item.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            //申请人
            if(item.getReimburserId() != null){
                item.setReimburserName(reimburserMap.get(item.getReimburserId()));
            }

            //出账账户
            if(item.getOutBillAccountId() != null){
                item.setOutBillAccountName(bankMap.get(item.getOutBillAccountId()));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if(item.getDirectorId() != null){
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
    }

    private void feeRelatedLoanAdd(List<FeeLoanRespVO> loanList,FeeReimbursePaymentAditReqVO reqVO) {
        if (loanList != null && loanList.size() > 0){
            List<FeeRelatedLoanDO> loanDOList = new ArrayList<>();
            for (FeeLoanRespVO item : loanList) {
                FeeRelatedLoanDO loanDO = new FeeRelatedLoanDO();
                loanDO.setFeeLoanId(item.getFeeLoanId());
                loanDO.setDataStatus((short) 0);
                loanDO.setLoanCode(item.getLoanCode());
                loanDO.setFeeReimbursePaymentId(reqVO.getFeeReimbursePaymentId());
                loanDO.setReimbursePaymentCode(reqVO.getReimbursePaymentCode());
                loanDO.setFeeReimburseId(reqVO.getFeeReimburseId());
                loanDO.setReimburseCode(reqVO.getReimburseCode());
                loanDO.setDeductionAmt(item.getReimburseDeductionAmt());
                loanDO.setRowNo(item.getRowNo());
                loanDOList.add(loanDO);
            }
            //新增报销关联借款单
            feeRelatedLoanMapper.insertBatch(loanDOList);
        }
    }
}

