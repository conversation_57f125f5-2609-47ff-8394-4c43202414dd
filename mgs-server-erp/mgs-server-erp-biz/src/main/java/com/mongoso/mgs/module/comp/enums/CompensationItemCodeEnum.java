package com.mongoso.mgs.module.comp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @date 2024/05/06 14:22
 */
public enum CompensationItemCodeEnum {

    /** 基本工资 **/
    TYPE_001(001, "基本工资"),
    /** 绩效工资 **/
    TYPE_002(002, "绩效工资"),

    /** 业务提成 **/
    TYPE_101(101, "业务提成"),
    /** 计件工资 **/
    TYPE_102(102, "计件工资"),
    /** 一线绩效工资 **/
    TYPE_103(103, "一线绩效工资"),

    /** 高温补贴 **/
    TYPE_201(201, "高温补贴"),
    /** 晚班补贴 **/
    TYPE_202(202, "晚班补贴"),
    /** 全勤补贴 **/
    TYPE_203(203, "全勤补贴"),
    /** 住宿补贴 **/
    TYPE_204(204, "住宿补贴"),
    /** 其它补贴 **/
    TYPE_205(205, "其它补贴"),

    /** 工作日加班费 **/
    TYPE_301(301, "工作日加班费"),
    /** 休息日加班费 **/
    TYPE_302(302, "休息日加班费"),
    /** 节假日加班费 **/
    TYPE_303(303, "节假日加班费"),

    /** 假期扣款 **/
    TYPE_401(401, "假期扣款"),
    /** 迟到扣款 **/
    TYPE_402(402, "迟到扣款"),
    /** 早退扣款 **/
    TYPE_403(403, "早退扣款"),
    /** 旷工扣款 **/
    TYPE_404(404, "旷工扣款"),

    /** 个人社保 **/
    TYPE_801(801, "个人社保"),
    /** 个人公积金 **/
    TYPE_802(802, "个人公积金"),
    /** 个人年金 **/
    TYPE_803(803, "个人年金"),

    /** 企业缴纳社保费 **/
    TYPE_901(901, "企业缴纳社保费"),
    /** 企业缴纳公积金费 **/
    TYPE_902(902, "企业缴纳公积金费"),
    /** 企业年金 **/
    TYPE_903(903, "企业年金"),

    /** 累计税前工资(至上月) **/
    TYPE_1001(1001, "累计税前工资(至上月)"),
    /** 累计免征额减除(至上月) **/
    TYPE_1002(1002, "累计免征额减除(至上月)"),
    /** 累计专项扣除(至上月) **/
    TYPE_1003(1003, "累计专项扣除(至上月)"),
    /** 累计专项附加扣除(至上月) **/
    TYPE_1004(1004, "累计专项附加扣除(至上月)"),
    /** 累计已缴工资税 **/
    TYPE_1005(1005, "累计已缴工资税"),

    /** 累计税前工资 **/
    TYPE_1101(1101, "累计税前工资"),
    /** 累计免征额减除 **/
    TYPE_1102(1102, "累计免征额减除"),
    /** 累计专项扣除 **/
    TYPE_1103(1103, "累计专项扣除"),
    /** 累计专项附加扣除 **/
    TYPE_1104(1104, "累计专项附加扣除"),
    /** 累计应税工资 **/
    TYPE_1105(1105, "累计应税工资"),
    /** 累计应纳工资税 **/
    TYPE_1106(1106, "累计应纳工资税"),

    /** 工资税 **/
    TYPE_1201(1201, "工资税"),
    /** 劳务税 **/
    TYPE_1202(1202, "劳务税"),

    /** 年度绩效/奖金（暂不计税） **/
    TYPE_1301(1301, "年度绩效/奖金（暂不计税）"),
    /** 年度绩效/奖金（计税） **/
    TYPE_1302(1302, "年度绩效/奖金（计税）"),

    /** 累计度绩效/奖金 **/
    TYPE_1501(1501, "累计度绩效/奖金"),
    /** 已缴奖金税 **/
    TYPE_1502(1502, "已缴奖金税"),
    /** 应缴奖金税 **/
    TYPE_1503(1503, "应缴奖金税"),
    /** 奖金税 **/
    TYPE_1504(1504, "奖金税"),

    /** 离职补偿金 **/
    TYPE_1801(1801, "离职补偿金"),

    /** 离职补偿金税 **/
    TYPE_1901(1901, "离职补偿金税"),

    /** 应发金额 **/
    TYPE_2001(2001, "应发金额"),
    /** 五险二金 **/
    TYPE_2002(2002, "五险二金"),
    /** 个人所得税 **/
    TYPE_2003(2003, "个人所得税"),
    /** 税发补发 **/
    TYPE_2004(2004, "税发补发"),
    /** 税后扣款 **/
    TYPE_2005(2005, "税后扣款"),
    /** 实发金额 **/
    TYPE_2006(2006, "实发金额");


    private int key;

    private String value;

    CompensationItemCodeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }
}
