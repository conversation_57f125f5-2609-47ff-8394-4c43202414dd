package com.mongoso.mgs.module.warehouse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收货单业务类型
 */

@AllArgsConstructor
@Getter
public enum ErpReceiptBizTypeEnum {

    RECEIPT(0,"收货单"),
    PURCHASE_RECEIPT(1,"采购收货单"),
    PURCHASE_NOTICE_RECEIPT(2,"采购通知收货单"),
    SALE_RETURN_RECEIPT(3,"销售退货收货单"),
    PURCHASE_PROCESS_OUT_RECEIPT(4,"工序委外收货单"),

    ;

    public final int type;// 类型
    public final String desc;// 描述
}
