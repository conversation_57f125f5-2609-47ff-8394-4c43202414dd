package com.mongoso.mgs.module.finance.service.invoice.invoicedetail;

import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetail.vo.InvoiceDetailAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetail.vo.InvoiceDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetail.vo.InvoiceDetailQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetail.vo.InvoiceDetailRespVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetail.InvoiceDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicedetail.InvoiceDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.invoice.enums.ErrorCodeConstants.*;


/**
 * 实开发票明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvoiceDetailServiceImpl implements InvoiceDetailService {

    @Resource
    private InvoiceDetailMapper detailMapper;

    @Override
    public Long invoiceDetailAdd(InvoiceDetailAditReqVO reqVO) {
        // 插入
        InvoiceDetailDO detail = BeanUtilX.copy(reqVO, InvoiceDetailDO::new);
        detailMapper.insert(detail);
        // 返回
        return detail.getInvoiceDetailId();
    }

    @Override
    public Long invoiceDetailEdit(InvoiceDetailAditReqVO reqVO) {
        // 校验存在
        this.invoiceDetailValidateExists(reqVO.getInvoiceDetailId());
        // 更新
        InvoiceDetailDO detail = BeanUtilX.copy(reqVO, InvoiceDetailDO::new);
        detailMapper.updateById(detail);
        // 返回
        return detail.getInvoiceDetailId();
    }

    @Override
    public void invoiceDetailDel(Long invoiceDetailId) {
        // 校验存在
        this.invoiceDetailValidateExists(invoiceDetailId);
        // 删除
        detailMapper.deleteById(invoiceDetailId);
    }

    private InvoiceDetailDO invoiceDetailValidateExists(Long invoiceDetailId) {
        InvoiceDetailDO detail = detailMapper.selectById(invoiceDetailId);
        if (detail == null) {
            // throw exception(DETAIL_NOT_EXISTS);
            throw new BizException("5001", "实开发票明细不存在");
        }
        return detail;
    }

    @Override
    public InvoiceDetailRespVO invoiceDetailDetail(Long invoiceDetailId) {
        InvoiceDetailDO data = detailMapper.selectById(invoiceDetailId);
        return BeanUtilX.copy(data, InvoiceDetailRespVO::new);
    }

    @Override
    public List<InvoiceDetailRespVO> invoiceDetailList(InvoiceDetailQueryReqVO reqVO) {
        List<InvoiceDetailDO> data = detailMapper.selectList(reqVO);
        return BeanUtilX.copy(data, InvoiceDetailRespVO::new);
    }

    @Override
    public PageResult<InvoiceDetailRespVO> invoiceDetailPage(InvoiceDetailPageReqVO reqVO) {
        PageResult<InvoiceDetailDO> data = detailMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, InvoiceDetailRespVO::new);
    }

}
