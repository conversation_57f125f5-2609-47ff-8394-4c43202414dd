package com.mongoso.mgs.module.finance.controller.admin.cashbank.outbill.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 直接出账 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class OutBillQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long outBillId;

    /** 出账编码 */
    private String outBillCode;

    /** 出账标题 */
    private String outBillName;

    /** 出账账户ID */
    private Long outBillAccountId;

    /** 可用余额 */
    private BigDecimal availableBalance;

    /** 币种 */
    private String currencyDictName;

    /** 出账金额 */
    private BigDecimal outBillAmount;

    /** 关联客户ID */
    private Long relatedCustomerId;

    /** 备注 */
    private String remark;

    /** 出账分类ID */
    private String outBillCategoryId;

    /** 出账日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startOutBillDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endOutBillDate;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 审核人 */
    private String approvedBy;

}
