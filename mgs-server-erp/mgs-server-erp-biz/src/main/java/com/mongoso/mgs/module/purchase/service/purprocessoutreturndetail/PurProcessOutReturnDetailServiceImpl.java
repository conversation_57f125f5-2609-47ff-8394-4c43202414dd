package com.mongoso.mgs.module.purchase.service.purprocessoutreturndetail;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.InvTypeManageQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutreturndetail.vo.*;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutreturndetail.PurProcessOutReturnDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutreturndetail.PurProcessOutReturnDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 工序委外采购退货单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurProcessOutReturnDetailServiceImpl implements PurProcessOutReturnDetailService {

    @Resource
    private PurProcessOutReturnDetailMapper purProcessOutReturnDetailMapper;

    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private InvTypeManageMapper invTypeManageMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Override
    public Long purProcessOutReturnDetailAdd(PurProcessOutReturnDetailAditReqVO reqVO) {
        // 插入
        PurProcessOutReturnDetailDO purProcessOutReturnDetail = BeanUtilX.copy(reqVO, PurProcessOutReturnDetailDO::new);
        purProcessOutReturnDetailMapper.insert(purProcessOutReturnDetail);
        // 返回
        return purProcessOutReturnDetail.getProcessOutReturnDetailId();
    }

    @Override
    public Long purProcessOutReturnDetailEdit(PurProcessOutReturnDetailAditReqVO reqVO) {
        // 校验存在
        this.purProcessOutReturnDetailValidateExists(reqVO.getProcessOutReturnDetailId());
        // 更新
        PurProcessOutReturnDetailDO purProcessOutReturnDetail = BeanUtilX.copy(reqVO, PurProcessOutReturnDetailDO::new);
        purProcessOutReturnDetailMapper.updateById(purProcessOutReturnDetail);
        // 返回
        return purProcessOutReturnDetail.getProcessOutReturnDetailId();
    }

    @Override
    public void purProcessOutReturnDetailDel(Long processOutReturnDetailId) {
        // 校验存在
        this.purProcessOutReturnDetailValidateExists(processOutReturnDetailId);
        // 删除
        purProcessOutReturnDetailMapper.deleteById(processOutReturnDetailId);
    }

    private PurProcessOutReturnDetailDO purProcessOutReturnDetailValidateExists(Long processOutReturnDetailId) {
        PurProcessOutReturnDetailDO purProcessOutReturnDetail = purProcessOutReturnDetailMapper.selectById(processOutReturnDetailId);
        if (purProcessOutReturnDetail == null) {
            // throw exception(PUR_PROCESS_OUT_RETURN_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "工序委外采购退货单明细不存在");
        }
        return purProcessOutReturnDetail;
    }

    @Override
    public PurProcessOutReturnDetailRespVO purProcessOutReturnDetailDetail(Long processOutReturnDetailId) {
        PurProcessOutReturnDetailDO data = purProcessOutReturnDetailMapper.selectById(processOutReturnDetailId);
        return BeanUtilX.copy(data, PurProcessOutReturnDetailRespVO::new);
    }

    @Override
    public List<PurProcessOutReturnDetailRespVO> purProcessOutReturnDetailList(PurProcessOutReturnDetailQueryReqVO reqVO) {
        List<PurProcessOutReturnDetailDO> data = purProcessOutReturnDetailMapper.selectList(reqVO);
        List<PurProcessOutReturnDetailRespVO> respVOS = BeanUtilX.copy(data, PurProcessOutReturnDetailRespVO::new);

        //属性填充
        getDetailRespList(respVOS);

        return respVOS;
    }

    @Override
    public PageResult<PurProcessOutReturnDetailRespVO> purProcessOutReturnDetailPage(PurProcessOutReturnDetailPageReqVO reqVO) {
        PageResult<PurProcessOutReturnDetailDO> data = purProcessOutReturnDetailMapper.selectPage(reqVO);
        PageResult<PurProcessOutReturnDetailRespVO> pageResult = BeanUtilX.copy(data, PurProcessOutReturnDetailRespVO::new);

        //属性填充
        if (CollUtilX.isNotEmpty(pageResult.getList())){
            getDetailRespList(pageResult.getList());
        }
        return pageResult;
    }

    /**
     * 产品明细详情处理
     *
     * @param detailRespVOS
     * @return
     */
    private List<PurProcessOutReturnDetailRespVO> getDetailRespList(List<PurProcessOutReturnDetailRespVO> detailRespVOS) {

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        List<Long> processIdList = new ArrayList<>();
        for (PurProcessOutReturnDetailRespVO detailResp: detailRespVOS){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
            processIdList.add(detailResp.getProcessId());
        }

        //查询物料信息
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        //填充物料基本信息
        for (PurProcessOutReturnDetailRespVO detailRespVO : detailRespVOS){
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailRespVO.getMaterialId());
            if (erpMaterialDO !=null){
                detailRespVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailRespVO.setMaterialName(erpMaterialDO.getMaterialName());
                detailRespVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailRespVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailRespVO.setSpecModel(erpMaterialDO.getSpecModel());
                detailRespVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }

            //基本单位
            if(StrUtilX.isNotEmpty(detailRespVO.getMainUnitDictId())){
                detailRespVO.setMainUnitDictName(dictMap.get(detailRespVO.getMainUnitDictId()));
            }

            //票据类型
            detailRespVO.setInvoiceTypeName(invTypeMap.get(detailRespVO.getInvoiceTypeId()));
        }

        return detailRespVOS;
    }

}
