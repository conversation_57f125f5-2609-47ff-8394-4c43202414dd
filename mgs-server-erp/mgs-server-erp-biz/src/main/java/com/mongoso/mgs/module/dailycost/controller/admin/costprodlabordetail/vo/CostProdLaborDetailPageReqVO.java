package com.mongoso.mgs.module.dailycost.controller.admin.costprodlabordetail.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 生产人工成本明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostProdLaborDetailPageReqVO extends PageParam {

    /** 生产人工成本单号 */
    private String costProdLaborCode;

    /** 生产人工成本ID */
    private Long costProdLaborId;

    private List<Long> costProdLaborIdList;

    /** 物料ID */
    private Long materialId;

    private List<Long> materialIdList;

    /** 物料名称 */
    private String materialName;

    /** 工序ID */
    private Long processId;

    /** 成本用途 */
    private String costUsage;

    /** 成本科目管理ID */
    private Long costSubjectId;

    /** 承担对象ID */
    private String undertakeOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 报工时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startReportedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endReportedDt;

}
