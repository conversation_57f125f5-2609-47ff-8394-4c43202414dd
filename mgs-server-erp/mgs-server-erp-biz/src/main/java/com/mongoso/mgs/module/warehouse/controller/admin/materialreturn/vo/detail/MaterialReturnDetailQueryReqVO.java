package com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.detail;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 归还单明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialReturnDetailQueryReqVO extends CommonParam{

    /** 归还单Id */
    private Long returnId;

    /** 归还单号 */
    private String returnCode;

    /** 归还单主题 */
    private String returnName;

    /** 归还单类型Id */
    private String returnTypeDictId;

    /** 归还单开始时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startReturnDt;

    /** 归还单结束时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endReturnDt;

    /** 单据状态 */
    private Short dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;
    /** 外借单ID */
    private Long loanId;

    /** 外借单号 */
    private String loanCode;

    /** 外借单主题 */
    private String loanName;

    /** 外借单类型ID */
    private String loanTypeDictId;

    /** 外借类型 */
    private Integer loanType;

    /** 外借对象类型 */
    private Integer loanObjType;

    /** 外借对象ID */
    private Long loanObjId;

    /** 外借组织ID */
    private String loanOrgId;

    /** 外借联系人ID */
    private Long contactId;

    /** 外借联系人姓名 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 物料ID */
    private Long materialId;

    /** 仓库ID */
    private String warehouseOrgId;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 物料编码 */
    private String materialCode;

    /** 物料编码 */
    private String materialName;

    /** 规格型号 */
    private String specModel;

    /** 物料类别字典ID */
    private String materialCategoryDictId;

}
