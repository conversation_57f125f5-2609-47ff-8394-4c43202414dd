package com.mongoso.mgs.module.comp.payroll.dal.db.payroll;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工资单 DO
 *
 * <AUTHOR>
 */
@TableName("erp.u_payroll")
@KeySequence("erp.u_payroll_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayrollDO extends OperateDO {

    /** 工资单ID */
    @TableId
    private Long payrollId;

    /** 工资单主题 */
    private String payrollName;

    /** 工资单号 */
    private String payrollCode;

    /** 工资单月份 */
    private String payrollMonth;

    /** 组织ID */
    private String companyOrgId;

    /** 计薪人数 */
    private Integer numberOfEmployee;

    /** 应发总额 */
    private BigDecimal totalPayableAmt;

    /** 实发总额 */
    private BigDecimal totalPaidAmt;

    /** 个税总额 */
    private BigDecimal totalTaxAmt;

    /** 状态 ["未发放", "已发放"] */
    private Integer payrollStatus;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;


}
