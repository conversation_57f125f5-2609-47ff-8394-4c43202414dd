package com.mongoso.mgs.module.warehouse.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDO;
import org.springframework.stereotype.Component;

/**
 * @author: Fashon.Liu
 * @date: 2024/12/3 9:40
 * @description: 库存锁定单回调处理类
 */

@Component
public class ErpReceiptFlowCallBackHandler extends FlowCallbackHandler<ErpReceiptDO> {

    protected ErpReceiptFlowCallBackHandler(FlowApproveHandler<ErpReceiptDO> approveHandler) {
        super(approveHandler);
    }

}

