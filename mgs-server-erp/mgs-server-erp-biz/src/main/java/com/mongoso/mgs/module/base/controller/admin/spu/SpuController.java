package com.mongoso.mgs.module.base.controller.admin.spu;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.controller.admin.spu.vo.*;
import com.mongoso.mgs.module.base.service.spu.SpuService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * SPU物料 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/base")
@Validated
public class SpuController {

    @Resource
    private SpuService spuService;

    @OperateLog("SPU物料添加或编辑")
    @PostMapping("/spuAdit")
    @PreAuthorize("@ss.hasPermission('spu:adit')")
    public ResultX<Long> spuAdit(@Valid @RequestBody SpuAditReqVO reqVO) {
        return success(reqVO.getSpuId() == null
                            ? spuService.spuAdd(reqVO)
                            : spuService.spuEdit(reqVO));
    }

    @OperateLog("SPU物料删除(批量)")
    @PostMapping("/spuDelBatch")
    @PreAuthorize("@ss.hasPermission('spu:del')")
    public ResultX<Boolean> spuDelBatch(@Valid @RequestBody SpuPrimaryReqVO reqVO) {
        spuService.spuDelBatch(reqVO);
        return success(true);
    }

    @OperateLog("SPU物料删除")
    @PostMapping("/spuDel")
    @PreAuthorize("@ss.hasPermission('spu:del')")
    public ResultX<Boolean> spuDel(@Valid @RequestBody SpuPrimaryReqVO reqVO) {
        spuService.spuDel(reqVO.getSpuId());
        return success(true);
    }

    @OperateLog("SPU物料详情")
    @PostMapping("/spuDetail")
    @PreAuthorize("@ss.hasPermission('spu:query')")
    public ResultX<SpuRespVO> spuDetail(@Valid @RequestBody SpuPrimaryReqVO reqVO) {
        return success(spuService.spuDetail(reqVO.getSpuId()));
    }

    @OperateLog("SPU物料详情")
    @PostMapping("/spuTest")
    @PreAuthorize("@ss.hasPermission('spu:query')")
    public ResultX<SpuRespVO> spuTest(@Valid @RequestBody SpuQueryReqVO reqVO) {
        return success(spuService.spuTest(reqVO));
    }

    @OperateLog("SPU物料列表")
    @PostMapping("/spuList")
    @PreAuthorize("@ss.hasPermission('spu:query')")
    @DataPermission
    public ResultX<List<SpuRespVO>> spuList(@Valid @RequestBody SpuQueryReqVO reqVO) {
        return success(spuService.spuList(reqVO));
    }

    @OperateLog("SPU物料列表")
    @PostMapping("/spuQuoteList")
    @PreAuthorize("@ss.hasPermission('spu:query')")
    public ResultX<List<SpuRespVO>> spuQuoteList(@Valid @RequestBody SpuQueryReqVO reqVO) {
        return success(spuService.spuList(reqVO));
    }

    @OperateLog("SPU物料分页")
    @PostMapping("/spuPage")
    @PreAuthorize("@ss.hasPermission('spu:query')")
    @DataPermission
    public ResultX<PageResult<SpuRespVO>> spuPage(@Valid @RequestBody SpuPageReqVO reqVO) {
        return success(spuService.spuPage(reqVO));
    }

    @OperateLog("SPU物料分页")
    @PostMapping("/spuQuotePage")
    @PreAuthorize("@ss.hasPermission('spu:query')")
    public ResultX<PageResult<SpuRespVO>> spuQuotePage(@Valid @RequestBody SpuPageReqVO reqVO) {
        return success(spuService.spuPage(reqVO));
    }

    /**
     * materialIdList、formStatus或publishStatus
     * @param reqVO
     * @return
     */
    @OperateLog("SPU物料全部上下架")
    @PostMapping("/spuPublishStatusChange")
    @PreAuthorize("@ss.hasPermission('material:statuschange')")
    public ResultX<Long> materialStatusChange(@Valid @RequestBody SpuQueryReqVO reqVO) {
        return success(spuService.publishStatusChange(reqVO));
    }

    @OperateLog("SPU审核")
    @PostMapping("/spuApprove")
    @PreAuthorize("@ss.hasPermission('spu:adit')")
    public ResultX<BatchResult> spuApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(spuService.spuApprove(reqVO));
    }

    @OperateLog("物料流程回调接口")
    @PostMapping("/spuFlowCallback")
    public ResultX<Object> spuFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(spuService.spuFlowCallback(reqVO));
    }

}
