package com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 出库单明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ErpOutboundDetailQueryReqVO extends CommonParam{

    /** 出库单ID */
    private Long outboundId;

    /** 出库单号 */
    private String outboundCode;

    /** 出库单类型 */
    private String outboundTypeDictId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 是否完全发货 */
    private Integer isMaterialFullDelivered;

    /** 备注 */
    private String remark;

    /** 业务类型 */
    private Short bizType;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据状态 */
    private Integer dataStatus;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 物料名称 */
    private String materialName;

    /** 物料类别字典ID */
    private String materialCategoryDictId;

    /** 规格型号 */
    private String specModel;

    /** 出库组织ID */
    private String warehouseOrgId;

    /** 明细类型['物料明细','核对物料明细'] */
    private String detailTypeDictId;
}
