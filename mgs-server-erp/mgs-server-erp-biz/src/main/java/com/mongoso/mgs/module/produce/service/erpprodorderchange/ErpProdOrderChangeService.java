package com.mongoso.mgs.module.produce.service.erpprodorderchange;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchange.vo.*;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderchange.ErpProdOrderChangeDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 生产订单变更单 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpProdOrderChangeService {

    /**
     * 创建生产订单变更单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpProdOrderChangeAdd(@Valid ErpProdOrderChangeAditReqVO reqVO);

    /**
     * 更新生产订单变更单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpProdOrderChangeEdit(@Valid ErpProdOrderChangeAditReqVO reqVO);

    /**
     * 删除生产订单变更单
     *
     * @param prodOrderChangeId 编号
     */
    void erpProdOrderChangeDel(Long prodOrderChangeId);

    /**
     * 获得生产订单变更单信息
     *
     * @param prodOrderChangeId 编号
     * @return 生产订单变更单信息
     */
    ErpProdOrderChangeRespVO erpProdOrderChangeDetail(Long prodOrderChangeId);

    /**
     * 获得生产订单变更单列表
     *
     * @param reqVO 查询条件
     * @return 生产订单变更单列表
     */
    List<ErpProdOrderChangeRespVO> erpProdOrderChangeList(@Valid ErpProdOrderChangeQueryReqVO reqVO);

    /**
     * 获得生产订单变更单分页
     *
     * @param reqVO 查询条件
     * @return 生产订单变更单分页
     */
    PageResult<ErpProdOrderChangeRespVO> erpProdOrderChangePage(@Valid ErpProdOrderChangePageReqVO reqVO);

    /**
     * 批量删除
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> erpProdOrderChangeDelBatch(IdReq reqVO);

    BatchResult erpProdOrderChangeApprove(FlowApprove reqVO);

    Object erpProdOrderChangeFlowCallback(FlowCallback reqVO);

}
