package com.mongoso.mgs.module.warehouse.service.erpoutbound;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.common.vo.QuoteDetailReqVO;
import com.mongoso.mgs.common.vo.QuoteReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 出库单 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpOutboundService {

    /**
     * 创建出库单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpOutboundAdd(@Valid ErpOutboundAditReqVO reqVO);

    /**
     * 更新出库单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpOutboundEdit(@Valid ErpOutboundAditReqVO reqVO);

    /**
     * 删除出库单
     *
     * @param outboundId 编号
     */
    void erpOutboundDel(Long outboundId);

    /**
     * 批量删除出库单
     *
     * @param reqVO 删除信息
     */
    ResultX<BatchResult> erpOutboundDelBatch(IdsReqVO reqVO);

    /**
     * 获得出库单信息
     *
     * @param outboundId 编号
     * @return 出库单信息
     */
    ErpOutboundRespVO erpOutboundDetail(Long outboundId);

    /**
     * 获得出库单被引用信息
     *
     * @param outboundId 编号
     * @return 出库单信息
     */
    ErpOutboundRespVO erpOutboundQuotedDetail(Long outboundId);

    /**
     * 获得出库单列表
     *
     * @param reqVO 查询条件
     * @return 出库单列表
     */
    List<ErpOutboundRespVO> erpOutboundList(@Valid ErpOutboundQueryReqVO reqVO);

    /**
     * 获得出库单列表
     *
     * @param saleOrderId 查询条件
     * @return 出库单列表
     */
    List<ErpOutboundRespVO> selectBySaleOrderIdList(Long saleOrderId);

    /**
     * 获得出库单分页
     *
     * @param reqVO 查询条件
     * @return 出库单分页
     */
    PageResult<ErpOutboundRespVO> erpOutboundPage(@Valid ErpOutboundPageReqVO reqVO);

    /**
     * 审核出库单
     *
     * @param reqVO 查询条件
     * @return 审核结果
     */
    BatchResult erpOutboundApprove(FlowApprove reqVO);

    /**
     * 审核出库单回调
     *
     * @param reqVO 审核信息
     * @return 回调结果
     */
    Object erpOutboundFlowCallback(FlowCallback reqVO);

    /**
     * 出库单引用列表
     *
     * @param reqVO 引用条件
     * @return 引用列表
     */
    ResultX erpOutboundQuoteList(@Valid ErpOutboundQuoteReqVO reqVO);

    /**
     * 出库单引用明细
     *
     * @param reqVO 引用条件
     * @return 引用明细
     */
    ResultX erpOutboundQuoteDetail(QuoteReqVO reqVO);

    /**
     * 出库单引用明细列表
     *
     * @param reqVO 引用条件
     * @return 引用明细列表
     */
    ResultX erpOutboundQuoteDetailList(QuoteDetailReqVO reqVO);

}
