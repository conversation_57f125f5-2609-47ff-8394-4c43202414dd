package com.mongoso.mgs.module.produce.service.calculatepriceorder.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 计价单规则 DO
 *
 * <AUTHOR>
 */
@Data
public class CalculatePriceOrderRuleBO2 implements Serializable {

    private Long priceRuleId;
    private String calculatePriceOrderCode;
    private String priceRuleName;
    //记时单价
    private String unitAmt;
    //良品计件单价
    private String okAmt;
    //不良品计件单价
    private String ngAmt;

}
