package com.mongoso.mgs.module.warehouse.controller.admin.workbench.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售客户数据 RespVO
 *
 * <AUTHOR>
 */
@Data
public class WarehouseOrderDataRespVO implements Serializable {

    /** 待入库物料数量 */
    private BigDecimal pendingInboundQty;

    /** 待出库物料数量 */
    private BigDecimal pendingOutboundQty;

    /** 待归还物料数量 */
    private BigDecimal pendingLoanReturnQty;

    /** 待完成盘点单 */
    private Integer pendingInventoryCount;
}
