package com.mongoso.mgs.module.finance.handler.flowCallback.cashbank;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.finance.dal.db.cashbank.inbill.InBillDO;
import org.springframework.stereotype.Component;

/**
 * @author: AI Assistant
 * @date: 2024/12/19
 * @description: 直接入账回调处理类
 */

@Component
public class InBillFlowCallBackHandler extends FlowCallbackHandler<InBillDO> {

    protected InBillFlowCallBackHandler(FlowApproveHandler<InBillDO> approveHandler) {
        super(approveHandler);
    }
}
