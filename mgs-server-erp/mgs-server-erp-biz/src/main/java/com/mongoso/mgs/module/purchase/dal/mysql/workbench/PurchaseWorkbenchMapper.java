package com.mongoso.mgs.module.purchase.dal.mysql.workbench;

import com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseLineBO;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseRankBO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购工作台 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseWorkbenchMapper {

    /***
     * 查询待完成需求单数量
     * @return 待完成需求单数量
     */
    Integer getPendingDemandCount();

    /***
     * 查询待完成订单数量
     * @return 待完成订单数量
     */
    Integer getPendingOrderCount();

    /***
     * 查询待入库物料数量
     * @return 待入库物料数量
     */
    BigDecimal getPendingInboundQty();

    /***
     * 查询工序委外待采购数量
     * @return 工序委外待采购数量
     */
    BigDecimal getPODPendingPurchaseQty();

    /***
     * 查询工序委外待采购数量
     * @return 待退货商品数量
     */
    BigDecimal getReturnPendingInboundedQty();

    /***
     * 查询供应商数量
     * @return 供应商数量
     */
    Integer getSupplierNumber();

    /***
     *  查询活跃供应商数量(近3月)
     * @return 活跃供应商数量
     */
    Integer getActiveSupplierNumber();

    /***
     * 查询采购折线图数据
     * @return 折线图数据
     */
    List<PurchaseLineBO> queryPurchaseAmtLineList();

    /***
     * 查询采购金额排行
     * @return 采购金额排行
     */
    List<PurchaseRankBO> queryPurchaseAmtRank();

    /***
     * 查询采购数量排行
     * @return 采购数量排行
     */
    List<PurchaseRankBO> queryPurchaseQtyRank();

}