package com.mongoso.mgs.module.finance.service.invoice.invoicependingplan;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.mongoso.mgs.common.enums.*;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.controller.admin.erpsupplier.vo.ERPSupplierQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.erpcustomer.ERPCustomerDO;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpcustomer.ERPCustomerMapper;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.base.service.erpsupplier.ERPSupplierService;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingdetail.vo.InvoicePendingDetailBaseVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.BillingDirectionEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.InvoiceApplyEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.InvoiceStatusEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.req.InvoicePendingPlanAditReq;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.vo.InvoicePendingPlanAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.vo.InvoicePendingPlanPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.vo.InvoicePendingPlanQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.vo.InvoicePendingPlanRespVO;
import com.mongoso.mgs.module.finance.controller.admin.strategyconfig.vo.StrategyConfigRespVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicependingdetail.InvoicePendingDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicependingplan.InvoicePendingPlanDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplan.InvoicePlanDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail.InvoicePlanDetailDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicependingdetail.InvoicePendingDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicependingplan.InvoicePendingPlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplan.InvoicePlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplandetail.InvoicePlanDetailMapper;
import com.mongoso.mgs.module.finance.service.strategyconfig.StrategyConfigService;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseOrderBO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.purchase.service.processout.PurchaseProcessOutService;
import com.mongoso.mgs.module.purchase.service.purchase.PurchaseOrderService;
import com.mongoso.mgs.module.purchase.service.purchase.detail.PurchaseOrderDetailService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.bo.ErpSaleOrderBO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.ErpSaleOrderDetailService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.erpdelivery.ErpDeliveryDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpdelivery.ErpDeliveryMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.invoice.enums.ErrorCodeConstants.*;


/**
 * 待开票计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InvoicePendingPlanServiceImpl implements InvoicePendingPlanService {

    @Resource
    private InvoicePendingPlanMapper pendingPlanMapper;

    @Resource
    private InvoicePendingDetailMapper invoicePendingDetailMapper;

    @Resource
    private InvoicePlanMapper invoicePlanMapper;

    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ERPSupplierService erpSupplierService;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private ERPCustomerMapper erpCustomerMapper;

    @Resource
    private ERPSupplierMapper erpSupplierMapper;

    @Resource
    private ErpSaleOrderDetailService erpSaleOrderDetailService;

    @Resource
    private ErpDeliveryMapper erpDeliveryMapper;

    @Resource
    private ErpOutboundMapper erpOutboundMapper;

    @Resource
    private ErpReceiptMapper erpReceiptMapper;

    @Resource
    private ErpInboundMapper erpInboundMapper;
    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    @Lazy
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private PurchaseOrderDetailService purchaseDetailService;

    @Resource
    private PurchaseProcessOutService purchaseProcessOutService;

    @Resource
    private StrategyConfigService strategyConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long invoicePendingPlanAdd(InvoicePendingPlanAditReqVO reqVO) {
        Long pendingPlanId = null;
        log.info("上游传递的数据是：{}", reqVO);

        if (reqVO.getAutomatic() == 0) return null;
        //判断当前单据是否存在
        InvoicePendingPlanDO pendingInvoicingPlanDO = pendingPlanMapper.selectOne(new LambdaQueryWrapper<InvoicePendingPlanDO>()
                .eq(InvoicePendingPlanDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eq(InvoicePendingPlanDO::getBillingDirection, reqVO.getBillingDirection())
        );
        if (Objects.nonNull(pendingInvoicingPlanDO)){
            //修改当前数据
            return updatePendingInvoicingPlan(reqVO, pendingInvoicingPlanDO, pendingInvoicingPlanDO.getInvoicePendingPlanId());
        }
        if (reqVO.getAutomatic() == 1){
            //自动
            pendingPlanId = automaticAdd(reqVO);

            if (reqVO.getSourceFormType() == OrderTypeEnum.SALE_ORDER.getType().shortValue()){
                //修改销售订单状态
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(reqVO.getUpId());
                erpSaleOrderService.editChildrenOrderCount(erpSaleOrder.getSaleOrderId(), DataButtonEnum.APPROVE.getKey());
            }
            if (reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()){
                //修改采购订单状态
                PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(reqVO.getUpId());
                if (purchaseOrder != null){
                    purchaseOrderService.editChildrenOrderCount(purchaseOrder.getPurchaseOrderId(), DataButtonEnum.APPROVE.getKey());
                }else {
                    //修改工序委外采购订单状态
                    purchaseProcessOutService.editChildrenOrderCount(reqVO.getUpId(), DataButtonEnum.APPROVE.getKey());
                }
            }
        }else if (reqVO.getAutomatic() == 2){
            //手动
            pendingPlanId = manualAdd(reqVO);
        }

        // 返回
        return pendingPlanId;
    }

    public Long invoicePendingPlanAdd4Change(@Valid InvoicePendingPlanAditReqVO reqVO) {
        InvoicePendingPlanDO invoicePendingPlanDO = this.checkOrder(reqVO.getUpId());
        List<InvoicePendingPlanAditReq> detailList = reqVO.getDetailList();
        ArrayList<InvoicePendingPlanAditReq> newDetailList = new ArrayList<>();
        ArrayList<Long> delDetailList = new ArrayList<>();
        for (InvoicePendingPlanAditReq detail : detailList) {
            if (detail.getQty().compareTo(BigDecimal.ZERO) > 0){
                newDetailList.add(detail);
            }else {
                InvoicePendingDetailDO pendingDetailDO = invoicePendingDetailMapper.selectOne(new LambdaQueryWrapper<InvoicePendingDetailDO>()
                                .eq(InvoicePendingDetailDO::getInvoicePendingPlanId, invoicePendingPlanDO.getInvoicePendingPlanId())
                                .eq(InvoicePendingDetailDO::getUpId, detail.getUpId()));
                if (Objects.isNull(pendingDetailDO)){
                    delDetailList.add(pendingDetailDO.getInvoicePendingPlanDetailId());
                }
            }
        }
        reqVO.setDetailList(newDetailList);
        invoicePendingDetailMapper.deleteBatchIds(delDetailList);
        return this.invoicePendingPlanAdd(reqVO);
    }

    //修改待开票计划数据
    private Long updatePendingInvoicingPlan(InvoicePendingPlanAditReqVO reqVO, InvoicePendingPlanDO pendingInvoicingPlanDO, Long pendingPlanId) {

        //修改明细
        List<InvoicePendingDetailDO> listPendingDetailUpdate = new ArrayList<>();
        List<InvoicePendingDetailDO> listPendingDetailAdd = new ArrayList<>();
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal totalAmt = BigDecimal.ZERO;
        List<Long> upIds = new ArrayList<>();
        Long i = 1L;

        //更新数据来源为 销售订单的历史开票计划明细
        List<InvoicePlanDetailDO> oldInvoicePlanDetailList = new ArrayList<>();
        Map<Long, InvoicePendingDetailDO> invoicePendingDetailMap = new HashMap<>();

        for (InvoicePendingPlanAditReq planAditReq : reqVO.getDetailList()){

            InvoicePendingDetailDO pendingDetailDO = invoicePendingDetailMapper.selectOne(new LambdaQueryWrapper<InvoicePendingDetailDO>()
                            .eq(InvoicePendingDetailDO::getInvoicePendingPlanId, pendingInvoicingPlanDO.getInvoicePendingPlanId())
                            .eq(InvoicePendingDetailDO::getUpId, planAditReq.getUpId())
            );
            if (Objects.isNull(pendingDetailDO)){

                pendingDetailDO =  BeanUtilX.copy(planAditReq, InvoicePendingDetailDO::new);
                pendingDetailDO.setInvoicePendingPlanId(pendingPlanId);
                long pendingDetailId = IdWorker.getId();
                pendingDetailDO.setInvoicePendingPlanDetailId(pendingDetailId);

                pendingDetailDO.setRemainingPlanQty(planAditReq.getQty());
                pendingDetailDO.setRemainingPlanAmt(planAditReq.getInclTaxAmt());

                pendingDetailDO.setTaxAmt(planAditReq.getInclTaxAmt().subtract(planAditReq.getExclTaxAmt()));

                pendingDetailDO.setCanQty(planAditReq.getQty());
                pendingDetailDO.setCanAmt(planAditReq.getInclTaxAmt());
                pendingDetailDO.setReadyAmt(BigDecimal.ZERO);
                pendingDetailDO.setReadyQty(BigDecimal.ZERO);
                pendingDetailDO.setBillingDirection(pendingInvoicingPlanDO.getBillingDirection());
                pendingDetailDO.setInvoicePendingPlanId(pendingInvoicingPlanDO.getInvoicePendingPlanId());
                pendingDetailDO.setRowNo(i);

                listPendingDetailAdd.add(pendingDetailDO);
            }else {

                BigDecimal remainingPlanQty = planAditReq.getQty().subtract(pendingDetailDO.getReadyQty());
                BigDecimal remainingPlanAmt = planAditReq.getInclTaxAmt().subtract(pendingDetailDO.getReadyAmt());
                BigDecimal readyQty = pendingDetailDO.getReadyQty();
                BigDecimal readyAmt = pendingDetailDO.getReadyAmt();

                Long invoicePendingPlanDetailId = pendingDetailDO.getInvoicePendingPlanDetailId();
                pendingDetailDO = BeanUtilX.copy(planAditReq, InvoicePendingDetailDO::new);
                pendingDetailDO.setInvoicePendingPlanDetailId(invoicePendingPlanDetailId);
                pendingDetailDO.setReadyQty(readyQty);//已申请数量
                pendingDetailDO.setReadyAmt(readyAmt);//已申请金额
                pendingDetailDO.setRemainingPlanQty(remainingPlanQty);//剩余可计划数量
                pendingDetailDO.setRemainingPlanAmt(remainingPlanAmt);//剩余可计划金额

                pendingDetailDO.setTaxAmt(planAditReq.getInclTaxAmt().subtract(planAditReq.getExclTaxAmt()));

                pendingDetailDO.setCanQty(remainingPlanQty);
                pendingDetailDO.setCanAmt(remainingPlanAmt);
                pendingDetailDO.setBillingDirection(pendingInvoicingPlanDO.getBillingDirection());
                pendingDetailDO.setInvoicePendingPlanId(pendingInvoicingPlanDO.getInvoicePendingPlanId());
                pendingDetailDO.setRowNo(i);

                listPendingDetailUpdate.add(pendingDetailDO);

                //查询销售订单||采购订单 历史开票计划明细
                if (reqVO.getSourceFormType()  == OrderTypeEnum.SALE_ORDER.type.shortValue() || reqVO.getSourceFormType()  == OrderTypeEnum.PURCHASE_ORDER.type.shortValue()){
                    List<InvoicePlanDetailDO> invoicePlanDetailList = invoicePlanDetailMapper.selectList(InvoicePlanDetailDO::getInvoicePendingPlanDetailId, pendingDetailDO.getInvoicePendingPlanDetailId());
                    invoicePendingDetailMap.put(pendingDetailDO.getInvoicePendingPlanDetailId(), pendingDetailDO);
                    oldInvoicePlanDetailList.addAll(invoicePlanDetailList);
                }
            }

            totalQty = totalQty.add(planAditReq.getQty());
            totalAmt = totalAmt.add(planAditReq.getInclTaxAmt());

            //标识存在明细
            upIds.add(planAditReq.getUpId());

            i ++;
        }

        //可计划金额
        pendingInvoicingPlanDO.setPendingTotalAmt(totalAmt);
        //剩余可计划金额
        pendingInvoicingPlanDO.setRemainingPlanAmt(totalAmt.subtract(pendingInvoicingPlanDO.getPlanAmt()));

        pendingInvoicingPlanDO.setCanAmt(totalAmt);
        pendingInvoicingPlanDO.setCanQty(totalQty);
        pendingInvoicingPlanDO.setInvoicePlanStatus(InvoiceStatusEnum.NOT_INVOICE.type.shortValue());

        pendingPlanMapper.updateById(pendingInvoicingPlanDO);

        //更新历史应收账款
        List<InvoicePlanDetailDO> updatePlanDetailList = new ArrayList<>();
        for (InvoicePlanDetailDO oldPlanDetailDO : oldInvoicePlanDetailList) {

            InvoicePendingDetailDO invoicePendingDetailDO = invoicePendingDetailMap.get(oldPlanDetailDO.getInvoicePendingPlanDetailId());

            if (invoicePendingDetailDO != null){
                InvoicePlanDetailDO planDetailDO = BeanUtilX.copy(invoicePendingDetailDO, InvoicePlanDetailDO::new);

                planDetailDO.setInvoicePlanDetailId(oldPlanDetailDO.getInvoicePlanDetailId());
                planDetailDO.setSourceOrderCode(oldPlanDetailDO.getSourceOrderCode());
                planDetailDO.setSourceLineNumber(oldPlanDetailDO.getSourceLineNumber());

                planDetailDO.setQty(invoicePendingDetailDO.getQty());
                planDetailDO.setRemainingPlanQty(invoicePendingDetailDO.getRemainingPlanQty());//剩余可计划数量
                planDetailDO.setRemainingPlanAmt(invoicePendingDetailDO.getRemainingPlanAmt());//剩余可计划金额
                planDetailDO.setReadyQty(oldPlanDetailDO.getReadyQty());
                planDetailDO.setReadyAmt(oldPlanDetailDO.getReadyAmt());
                planDetailDO.setCanQty(oldPlanDetailDO.getCanQty());
                planDetailDO.setCanAmt(oldPlanDetailDO.getCanAmt());
                updatePlanDetailList.add(planDetailDO);
            }

        }

        if (CollUtilX.isNotEmpty(updatePlanDetailList)){
            invoicePlanDetailMapper.updateBatch(updatePlanDetailList);
        }

        //处理删除物料明细的数据
        if (CollectionUtils.isNotEmpty(upIds)){
            invoicePendingDetailMapper.delete(new LambdaQueryWrapper<InvoicePendingDetailDO>().eq(InvoicePendingDetailDO::getInvoicePendingPlanId, pendingPlanId).notIn(InvoicePendingDetailDO::getUpId, upIds));
        }
        if (CollectionUtils.isNotEmpty(listPendingDetailAdd)){
            invoicePendingDetailMapper.insertBatch(listPendingDetailAdd);
        }

        if (CollectionUtils.isNotEmpty(listPendingDetailUpdate)){
            invoicePendingDetailMapper.updateBatch(listPendingDetailUpdate);
        }

        return pendingInvoicingPlanDO.getInvoicePendingPlanId();
    }

    private Long manualAdd(InvoicePendingPlanAditReqVO reqVO) {
        //只生成待开票计划，和待开票计划明细
        InvoicePendingPlanDO pendingPlan = BeanUtilX.copy(reqVO, InvoicePendingPlanDO::new);
        pendingPlan.setInvoicePlanStatus(InvoiceStatusEnum.NOT_INVOICE.type.shortValue());
        long pendingPlanId = IdWorker.getId();
        pendingPlan.setInvoicePendingPlanId(pendingPlanId);


        //待开票总数量
        BigDecimal qty = BigDecimal.ZERO;
        //待开票总金额
        BigDecimal amt = BigDecimal.ZERO;
        List<InvoicePendingDetailDO> listPendingDetail = new ArrayList<>();
        long rowNo = 1L;
        for (InvoicePendingPlanAditReq planAditReq : reqVO.getDetailList()){
            //设置开票方向
            planAditReq.setBillingDirection(pendingPlan.getBillingDirection());

            //待开票计划明细
            InvoicePendingDetailDO pendingDetailDO = BeanUtilX.copy(planAditReq, InvoicePendingDetailDO::new);

            pendingDetailDO.setInvoicePendingPlanId(pendingPlanId);
            long pendingDetailId = IdWorker.getId();
            pendingDetailDO.setInvoicePendingPlanDetailId(pendingDetailId);
            pendingDetailDO.setRemainingPlanQty(planAditReq.getCurrentQty());
            pendingDetailDO.setRemainingPlanAmt(planAditReq.getInclCurrentAmt());
            pendingDetailDO.setTaxAmt(pendingDetailDO.getInclTaxAmt().subtract(pendingDetailDO.getExclTaxAmt()));

            pendingDetailDO.setRowNo(rowNo++);

            pendingDetailDO.setCanQty(planAditReq.getCurrentQty());
            pendingDetailDO.setCanAmt(planAditReq.getInclCurrentAmt());
            pendingDetailDO.setReadyQty(BigDecimal.ZERO);
            pendingDetailDO.setReadyAmt(BigDecimal.ZERO);

            listPendingDetail.add(pendingDetailDO);

            qty = qty.add(planAditReq.getCurrentQty());
            amt = amt.add(planAditReq.getInclCurrentAmt());
        }
        invoicePendingDetailMapper.insertBatch(listPendingDetail);

        pendingPlan.setReadyQty(BigDecimal.ZERO);
        pendingPlan.setReadyAmt(BigDecimal.ZERO);
        pendingPlan.setCanQty(qty);
        pendingPlan.setCanAmt(amt);

        //已计划金额 = 0
        pendingPlan.setPlanAmt(BigDecimal.ZERO);
        //剩余待开票总额 = 待开票总额
        pendingPlan.setRemainingPlanAmt(amt);
        pendingPlan.setPendingTotalAmt(amt);

        pendingPlanMapper.insert(pendingPlan);
        return pendingPlanId;
    }

    private Long automaticAdd(InvoicePendingPlanAditReqVO reqVO) {

        //来源单据类型
        Short sourceFormType = reqVO.getSourceFormType();

        //待开票计划
        InvoicePendingPlanDO pendingPlan = BeanUtilX.copy(reqVO, InvoicePendingPlanDO::new);
        pendingPlan.setInvoicePlanStatus(InvoiceStatusEnum.ISSUE_INVOICE.type.shortValue());
        long pendingPlanId = IdWorker.getId();
        pendingPlan.setInvoicePendingPlanId(pendingPlanId);

        List<InvoicePlanDO> invoicePlanDOList = new ArrayList<>();
        List<InvoicePendingDetailDO> listPendingDetail = new ArrayList<>();
        List<InvoicePlanDetailDO> listPlanDetail = new ArrayList<>();
        //票据类型--用于生成多个开票计划判断
        Map<Long, BigDecimal> invoiceTypeDictMap = new HashMap<>();
        Map<Long, Long> invoiceIdByTypeDictMap = new HashMap<>();
        Map<Long, Long> rowNoByTypeDictMap = new HashMap<>();
        //开票总额含税
        Map<Long, BigDecimal> planInvoiceTotalInclTaxMap = new HashMap<>();
        //开票总额不含税
        Map<Long, BigDecimal> planInvoiceTotalExclTaxMap = new HashMap<>();
        //开票总数量
        Map<Long, BigDecimal> planQtyMap = new HashMap<>();
        //Long invoicePlanId = null;//开票计划id
        Long rowNo = 1L;
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal totalAmt = BigDecimal.ZERO;
        String code = null;
        for (InvoicePendingPlanAditReq planAditReq : reqVO.getDetailList()){
            //设置开票方向
            planAditReq.setBillingDirection(pendingPlan.getBillingDirection());

            BigDecimal inclTaxAmt = invoiceTypeDictMap.get(planAditReq.getInvoiceTypeDictId());
            if (inclTaxAmt == null){
                invoiceTypeDictMap.put(planAditReq.getInvoiceTypeDictId(), planAditReq.getInclTaxAmt());


                //开票计划
                InvoicePlanDO invoicePlanDO = BeanUtilX.copy(reqVO, InvoicePlanDO::new);
                Long invoicePlanId = IdWorker.getId();
                invoicePlanDO.setInvoicePlanId(invoicePlanId);
                invoicePlanDO.setInvoicePendingPlanId(pendingPlanId);
                invoicePlanDO.setInvoiceTypeDictId(planAditReq.getInvoiceTypeDictId());
                invoicePlanDO.setInvoiceTypeDictName(planAditReq.getInvoiceTypeDictName());
                //判断策略配置跳过开票申请是否为是
                StrategyConfigRespVO configRespVO = strategyConfigService.strategyConfigDetail(null);
                invoicePlanDO.setSkipInvoiceApply(configRespVO.getSkipInvoiceApply());
                invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.NOT_APPLY.getType().shortValue());

                if ((configRespVO != null && configRespVO.getSkipInvoiceApply() == 1) || pendingPlan.getBillingDirection() == 1) {
                    invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.NOT_INVOICE.type.shortValue());
                }

                //生成开票计划单号
                if (pendingPlan.getBillingDirection() == 0) {
//                    Long seq = seqService.lockInsertLamb(SeqEnum.invoiceplancode.getTableName(), LocalDate.now(), null);
//                    code = CodeGenUtil.generateCode(CodeGenUtil.CodeTypeEnums.INVOICE_PLAN_CODE, seq);
                    code = seqService.getGenerateCode(reqVO.getInvoicePlanNo(), MenuEnum.INVOICE_PLAN.menuId);
                }else {
//                    Long seq = seqService.lockInsertLamb(SeqEnum.invoicecollectplancode.getTableName(), LocalDate.now(), null);
//                    code = CodeGenUtil.generateCode(CodeGenUtil.CodeTypeEnums.INVOICE_COLLECT_PLAN_CODE, seq);
                    code = seqService.getGenerateCode(reqVO.getInvoicePlanNo(), MenuEnum.INVOICE_COLLECT_PLAN.menuId);

                }

                invoicePlanDO.setInvoicePlanNo(code);
                invoicePlanDO.setApprovedDt(LocalDateTime.now());
                invoicePlanDO.setDataStatus(DataStatusEnum.APPROVED.getKey().shortValue());
                invoicePlanDO.setApprovedBy("系统");
                invoicePlanDO.setPlanInvoiceDate(LocalDate.now());
                invoicePlanDOList.add(invoicePlanDO);

                planInvoiceTotalInclTaxMap.put(invoicePlanId, planAditReq.getInclCurrentAmt());
                planInvoiceTotalExclTaxMap.put(invoicePlanId, planAditReq.getExclCurrentAmt());
                planQtyMap.put(invoicePlanId, planAditReq.getCurrentQty());


                invoiceIdByTypeDictMap.put(planAditReq.getInvoiceTypeDictId(), invoicePlanId);
                rowNoByTypeDictMap.put(planAditReq.getInvoiceTypeDictId(), 1L);
            }else {
                invoiceTypeDictMap.put(planAditReq.getInvoiceTypeDictId(), planAditReq.getInclCurrentAmt().add(inclTaxAmt));
                planInvoiceTotalInclTaxMap.put(invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()), planAditReq.getInclCurrentAmt()
                        .add(planInvoiceTotalInclTaxMap.get(
                                invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()))));
                planInvoiceTotalExclTaxMap.put(invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()), planAditReq.getExclCurrentAmt()
                        .add(planInvoiceTotalExclTaxMap.get(
                                invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()))));
                planQtyMap.put(invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()), planAditReq.getCurrentQty()
                        .add(planQtyMap.get(invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()))));
            }

            //待开票计划明细
            InvoicePendingDetailDO pendingDetailDO = BeanUtilX.copy(planAditReq, InvoicePendingDetailDO::new);
            pendingDetailDO.setInvoicePendingPlanId(pendingPlanId);
            long pendingDetailId = IdWorker.getId();
            pendingDetailDO.setInvoicePendingPlanDetailId(pendingDetailId);
            //剩余可计划数量和金额都为0
            pendingDetailDO.setRemainingPlanQty(BigDecimal.ZERO);
            pendingDetailDO.setRemainingPlanAmt(BigDecimal.ZERO);
            pendingDetailDO.setCanQty(BigDecimal.ZERO);
            pendingDetailDO.setCanAmt(BigDecimal.ZERO);
            pendingDetailDO.setReadyQty(planAditReq.getCurrentQty());
            pendingDetailDO.setReadyAmt(planAditReq.getInclCurrentAmt());
            pendingDetailDO.setTaxAmt(planAditReq.getInclCurrentAmt().subtract(planAditReq.getExclCurrentAmt()));
            pendingDetailDO.setRowNo(rowNo++);
            listPendingDetail.add(pendingDetailDO);

            //开票计划明细
            InvoicePlanDetailDO planDetailDO = BeanUtilX.copy(planAditReq, InvoicePlanDetailDO::new);
            Long planRowNo = rowNoByTypeDictMap.get(planAditReq.getInvoiceTypeDictId());
            long planDetailId = IdWorker.getId();
            planDetailDO.setInvoicePendingPlanDetailId(pendingDetailId);
            planDetailDO.setInvoicePlanDetailId(planDetailId);
            planDetailDO.setInvoicePlanId(invoiceIdByTypeDictMap.get(planAditReq.getInvoiceTypeDictId()));
            planDetailDO.setPlanInvoiceQty(planAditReq.getCurrentQty());
            planDetailDO.setPlanInvoiceAmtInclTax(planAditReq.getInclCurrentAmt());
            planDetailDO.setPlanInvoiceAmtExclTax(planAditReq.getExclCurrentAmt());
            planDetailDO.setRemainingPlanAmt(planAditReq.getInclCurrentAmt());
            planDetailDO.setRemainingPlanQty(planAditReq.getCurrentQty());
            planDetailDO.setCanQty(planAditReq.getCurrentQty());
            planDetailDO.setCanAmt(planAditReq.getInclCurrentAmt());
            planDetailDO.setReadyQty(BigDecimal.ZERO);
            planDetailDO.setReadyAmt(BigDecimal.ZERO);
            planDetailDO.setReadyInvoiceQty(BigDecimal.ZERO);
            planDetailDO.setReadyInvoiceAmt(BigDecimal.ZERO);
            planDetailDO.setTaxAmt(planAditReq.getInclCurrentAmt().subtract(planAditReq.getExclCurrentAmt()));
            planDetailDO.setRowNo(planRowNo++);
            planDetailDO.setInvoicePlanNo(code);
            planDetailDO.setCanInvoiceAmt(planDetailDO.getPlanInvoiceAmtInclTax());
            planDetailDO.setCanInvoiceQty(planDetailDO.getPlanInvoiceQty());
            listPlanDetail.add(planDetailDO);
            totalQty = totalQty.add(planAditReq.getCurrentQty());
            //qty = qty.add(pendingDetailDO.getQty());
            totalAmt = totalAmt.add(planAditReq.getInclCurrentAmt());

            rowNoByTypeDictMap.put(planAditReq.getInvoiceTypeDictId(), planRowNo + 1);

        }
        //批量新增
        //计算计划总额
        invoicePlanDOList.forEach(invoicePlanDO -> {
            invoicePlanDO.setPlanInvoiceTotalInclTax(planInvoiceTotalInclTaxMap.get(invoicePlanDO.getInvoicePlanId()));
            invoicePlanDO.setRemainPlanTotalInclTax(invoicePlanDO.getPlanInvoiceTotalInclTax());
            invoicePlanDO.setPlanInvoiceTotalExclTax(planInvoiceTotalExclTaxMap.get(invoicePlanDO.getInvoicePlanId()));
            invoicePlanDO.setTotalTax(invoicePlanDO.getPlanInvoiceTotalInclTax().subtract(invoicePlanDO.getPlanInvoiceTotalExclTax()));
            invoicePlanDO.setReadyAmt(BigDecimal.ZERO);
            invoicePlanDO.setReadyQty(BigDecimal.ZERO);
            invoicePlanDO.setCanAmt(invoicePlanDO.getPlanInvoiceTotalInclTax());
            invoicePlanDO.setCanQty(planQtyMap.get(invoicePlanDO.getInvoicePlanId()));
            invoicePlanDO.setReadyInvoiceAmt(BigDecimal.ZERO);
            invoicePlanDO.setReadyInvoiceQty(BigDecimal.ZERO);
            invoicePlanDO.setCanInvoiceAmt(invoicePlanDO.getPlanInvoiceTotalInclTax());
            invoicePlanDO.setCanInvoiceQty(planQtyMap.get(invoicePlanDO.getInvoicePlanId()));

            //更新本币币种及汇率
            invoicePlanDO.setLocalCurrencyDictId(reqVO.getLocalCurrencyDictId());
            invoicePlanDO.setExchangeRate(reqVO.getExchangeRate());
            if(reqVO.getExchangeRate() != null){
                invoicePlanDO.setLocalCurrencyPlanInvoiceTotalInclTax(invoicePlanDO.getPlanInvoiceTotalInclTax()
                        .multiply(reqVO.getExchangeRate()).setScale(2, RoundingMode.HALF_UP));
                invoicePlanDO.setLocalCurrencyReadyInvoiceAmt(BigDecimal.ZERO);
                invoicePlanDO.setLocalCurrencyCanInvoiceAmt(invoicePlanDO.getLocalCurrencyPlanInvoiceTotalInclTax());
            }
        });
        invoicePlanMapper.insertBatch(invoicePlanDOList);//开票计划
        invoicePendingDetailMapper.insertBatch(listPendingDetail);//待开票计划明细
        invoicePlanDetailMapper.insertBatch(listPlanDetail);//开票计划明细

        pendingPlan.setReadyQty(totalQty);
        pendingPlan.setReadyAmt(totalAmt);
        pendingPlan.setCanQty(BigDecimal.ZERO);
        pendingPlan.setCanAmt(BigDecimal.ZERO);
        //已计划金额=待开票总额
        pendingPlan.setPlanAmt(totalAmt);
        pendingPlan.setPendingTotalAmt(totalAmt);
        //剩余待开票总额= 0
        pendingPlan.setRemainingPlanAmt(BigDecimal.ZERO);
        pendingPlanMapper.insert(pendingPlan);//待开票计划

        //回写计划开票数量
        writeBackSaleOrPurchaseOrder(sourceFormType,listPlanDetail);

        return pendingPlanId;
    }

    /**
     * 回写计划开票数量
     *
     * @param sourceFormType
     * @param listPlanDetail
     */
    private void writeBackSaleOrPurchaseOrder(Short sourceFormType, List<InvoicePlanDetailDO> listPlanDetail) {
        for (InvoicePlanDetailDO planDetail : listPlanDetail){

            //销售订单
            if (sourceFormType == OrderTypeEnum.SALE_ORDER.getType().shortValue()) {
                ErpSaleOrderBO erpSaleOrderBO = new ErpSaleOrderBO();
                erpSaleOrderBO.setMaterialId(planDetail.getMaterialId());
                erpSaleOrderBO.setInvoicedQty(planDetail.getPlanInvoiceQty());
                erpSaleOrderBO.setSaleOrderCode(planDetail.getSourceOrderCode());
                erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
            }

            //销售发货单
            if (sourceFormType == OrderTypeEnum.SALE_INVOICES_ORDER.getType().shortValue()) {
                ErpSaleOrderBO erpSaleOrderBO = new ErpSaleOrderBO();
                erpSaleOrderBO.setMaterialId(planDetail.getMaterialId());
                erpSaleOrderBO.setInvoicedQty(planDetail.getPlanInvoiceQty());
                ErpDeliveryDO erpDeliveryDO = erpDeliveryMapper.selectOne(ErpDeliveryDO::getDeliveryCode, planDetail.getSourceOrderCode());
                if (!Objects.isNull(erpDeliveryDO)){
                    erpSaleOrderBO.setSaleOrderId(erpDeliveryDO.getSaleOrderId());
                    erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
                }
            }

            //销售出库单
            if (sourceFormType == OrderTypeEnum.SALE_OUT_BOUND_ORDER.getType().shortValue()) {
                ErpSaleOrderBO erpSaleOrderBO = new ErpSaleOrderBO();
                erpSaleOrderBO.setMaterialId(planDetail.getMaterialId());
                erpSaleOrderBO.setInvoicedQty(planDetail.getPlanInvoiceQty());
                ErpOutboundDO erpOutboundDO = erpOutboundMapper.selectOne(ErpOutboundDO::getOutboundCode, planDetail.getSourceOrderCode());
                if (!Objects.isNull(erpOutboundDO)){
                    erpSaleOrderBO.setSaleOrderId(erpOutboundDO.getSaleOrderId());
                    erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
                }
            }

            //采购订单
            if (sourceFormType == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()){
                PurchaseOrderBO purchaseOrderBO = new PurchaseOrderBO();
                purchaseOrderBO.setMaterialId(planDetail.getMaterialId());
                purchaseOrderBO.setReceivedQty(planDetail.getPlanInvoiceQty());
                purchaseOrderBO.setPurchaseOrderCode(planDetail.getSourceOrderCode());
                purchaseOrderBO.setRowNo(Integer.valueOf(planDetail.getSourceLineNumber()));
                purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
            }

            //采购收货单
            if (sourceFormType == OrderTypeEnum.PURCHASE_RECEIPT_ORDER.getType().shortValue()){
                PurchaseOrderBO purchaseOrderBO = new PurchaseOrderBO();
                purchaseOrderBO.setMaterialId(planDetail.getMaterialId());
                purchaseOrderBO.setReceivedQty(planDetail.getPlanInvoiceQty());

                ErpReceiptDO erpReceiptDO = erpReceiptMapper.selectOne(ErpReceiptDO::getReceiptCode, planDetail.getSourceOrderCode());
                if (!Objects.isNull(erpReceiptDO)){
                    purchaseOrderBO.setPurchaseOrderId(erpReceiptDO.getPurchaseOrderId());
                    purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
                }
            }

            //采购入库单
            if (sourceFormType == OrderTypeEnum.PURCHASE_IN_BOUND_ORDER.getType().shortValue()){
                PurchaseOrderBO purchaseOrderBO = new PurchaseOrderBO();
                purchaseOrderBO.setMaterialId(planDetail.getMaterialId());
                purchaseOrderBO.setReceivedQty(planDetail.getPlanInvoiceQty());

                ErpInboundDO erpInboundDO = erpInboundMapper.selectOne(ErpInboundDO::getInboundCode, planDetail.getSourceOrderCode());
                if (!Objects.isNull(erpInboundDO)){
                    purchaseOrderBO.setPurchaseOrderId(erpInboundDO.getPurchaseOrderId());
                    purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
                }
            }
        }
    }

    //上游反审核的时候只需要修改待开票计划和待开票计划明细的数据
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long invoicePendingPlanEdit(InvoicePendingPlanAditReqVO reqVO) {

        //判断当前单据是否存在
        InvoicePendingPlanDO pendingPlan = pendingPlanMapper.selectOne(new LambdaQueryWrapper<InvoicePendingPlanDO>()
                .eq(InvoicePendingPlanDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                //.eq(InvoicePendingPlanDO::getSourceLineNo, reqVO.getSourceLineNo())
        );
        if (pendingPlan == null) {
            // throw exception(PENDING_PLAN_NOT_EXISTS);
            throw new BizException("5001", "待开票计划不存在");
        }
//        invoicePendingPlanValidateExists(reqVO.getInvoicePendingPlanId());
//
//        InvoicePendingPlanDO pendingPlan = BeanUtilX.copy(reqVO, InvoicePendingPlanDO::new);

        //修改开票总金额和剩余开票金额
        pendingPlan.setPendingTotalAmt(BigDecimal.ZERO);
        if (pendingPlan.getRemainingPlanAmt().compareTo(BigDecimal.ZERO) != 0) {
            pendingPlan.setRemainingPlanAmt(pendingPlan.getRemainingPlanAmt().negate());
            pendingPlan.setInvoicePlanStatus(InvoiceStatusEnum.PART_INVOICE.type.shortValue());
        }

        // 更新
        pendingPlanMapper.updateById(pendingPlan);

        //查询明细
        List<InvoicePendingDetailDO> list = invoicePendingDetailMapper.selectList(new LambdaQueryWrapper<InvoicePendingDetailDO>().eq(InvoicePendingDetailDO::getInvoicePendingPlanId, reqVO.getInvoicePendingPlanId()));
        list.forEach(detailDO->{
            detailDO.setQty(BigDecimal.ZERO);
            detailDO.setExclTaxAmt(BigDecimal.ZERO);
            detailDO.setInclTaxAmt(BigDecimal.ZERO);
            detailDO.setRemainingPlanAmt(detailDO.getRemainingPlanAmt().negate());
            detailDO.setRemainingPlanQty(detailDO.getRemainingPlanQty().negate());
        });
        invoicePendingDetailMapper.updateBatch(list);
        // 返回
        return pendingPlan.getInvoicePendingPlanId();
    }

    @Override
    public void invoicePendingPlanDel(Long upId) {
        InvoicePendingPlanDO invoicePendingPlanDO = this.checkOrder(upId);
        if (invoicePendingPlanDO == null) return;
        // 删除待开票计划
        pendingPlanMapper.deleteById(invoicePendingPlanDO.getInvoicePendingPlanId());
        //删除明细
        invoicePendingDetailMapper.delete(new LambdaQueryWrapper<InvoicePendingDetailDO>()
                .eq(InvoicePendingDetailDO::getInvoicePendingPlanId, invoicePendingPlanDO.getInvoicePendingPlanId())
        );
    }

    @Nullable
    @Override
    public InvoicePendingPlanDO checkOrder(Long upId) {
        //上游反审核删除待开票计划，
        InvoicePendingPlanDO invoicePendingPlanDO = pendingPlanMapper.selectOne(new LambdaQueryWrapper<InvoicePendingPlanDO>()
                .eq(InvoicePendingPlanDO::getUpId, upId)
                .last(" limit 1")
        );
        if (Objects.isNull(invoicePendingPlanDO)){
            return null;
        }
        //判断下游是否存在已审核的数据
        Long count = invoicePlanMapper.selectCount(new LambdaQueryWrapper<InvoicePlanDO>()
                .eq(InvoicePlanDO::getInvoicePendingPlanId, invoicePendingPlanDO.getInvoicePendingPlanId())
                .eq(InvoicePlanDO::getDataStatus, DataStatusEnum.APPROVED.getKey().shortValue())
        );
        if (count != null && count > 0){
            if (invoicePendingPlanDO.getBillingDirection() == BillingDirectionEnum.ISSUE_INVOICE.type.shortValue()){
                throw new BizException("5001","待开票计划下游存在已审核的数据");
            }else {
                throw new BizException("5001","待收票计划下游存在已审核的数据");
            }
        }
        return invoicePendingPlanDO;
    }

    private InvoicePendingPlanDO invoicePendingPlanValidateExists(Long invoicePendingPlanId) {
        InvoicePendingPlanDO pendingPlan = pendingPlanMapper.selectById(invoicePendingPlanId);
        if (pendingPlan == null) {
            // throw exception(PENDING_PLAN_NOT_EXISTS);
            throw new BizException("5001", "待开票计划不存在");
        }
        return pendingPlan;
    }

    @Override
    public InvoicePendingPlanRespVO invoicePendingPlanDetail(Long invoicePendingPlanId) {
        //InvoicePendingPlanDO data = pendingPlanMapper.selectById(invoicePendingPlanId);
        InvoicePendingPlanDO data = pendingPlanMapper.selectOne(new LambdaQueryWrapper<InvoicePendingPlanDO>()
                .eq(InvoicePendingPlanDO::getInvoicePendingPlanId, invoicePendingPlanId)
                .or()
                .eq(InvoicePendingPlanDO::getUpId, invoicePendingPlanId)
                .last(" limit 1")
        );
        if (Objects.isNull(data)){
            throw new BizException("5001", "待开票计划不存在");
        }
        InvoicePendingPlanRespVO planRespVO = BeanUtilX.copy(data, InvoicePendingPlanRespVO::new);
        fillVoProperties(planRespVO);

        //查询明细
        List<InvoicePendingDetailDO> invoicePendingDetailDOS = invoicePendingDetailMapper.selectList(new LambdaQueryWrapper<InvoicePendingDetailDO>()
                .eq(InvoicePendingDetailDO::getInvoicePendingPlanId, data.getInvoicePendingPlanId()));
        List<InvoicePendingDetailBaseVO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(invoicePendingDetailDOS)){
            invoicePendingDetailDOS.forEach(detail->{
                InvoicePendingDetailBaseVO copy = BeanUtilX.copy(detail, InvoicePendingDetailBaseVO::new);
                list.add(copy);
            });
        }
        detailBatchFillVoProperties(list);
        planRespVO.setDetailList(list);
        return planRespVO;
    }

    @Override
    public List<InvoicePendingPlanRespVO> invoicePendingPlanList(InvoicePendingPlanQueryReqVO reqVO) {
        List<InvoicePendingPlanDO> data = pendingPlanMapper.selectList(reqVO);
        return BeanUtilX.copy(data, InvoicePendingPlanRespVO::new);
    }

    @Override
    public PageResult<InvoicePendingPlanRespVO> invoicePendingPlanPage(InvoicePendingPlanPageReqVO reqVO) {

        List<Long> customerIdList = new ArrayList<>();
        if (reqVO.getBillingDirection() == BillingDirectionEnum.ISSUE_INVOICE.type.shortValue()){
            if (StringUtils.isNotEmpty(reqVO.getCustomerName())){
                ERPCustomerQueryReqVO custmReq = new ERPCustomerQueryReqVO();
                custmReq.setCustomerName(reqVO.getCustomerName());
                List<ERPCustomerDO> customerList = erpCustomerMapper.selectList(custmReq);

                if (CollUtilX.isEmpty(customerList)){
                    return PageResult.empty();
                }

                if (CollUtilX.isNotEmpty(customerList)) {
                    customerIdList = customerList.stream().map(ERPCustomerDO::getCustomerId).collect(Collectors.toList());
                    reqVO.setCustomerIdList(customerIdList);
                }

            }
        }else {
            ERPSupplierQueryReqVO custmReq = new ERPSupplierQueryReqVO();
            custmReq.setSupplierName(reqVO.getCustomerName());
            List<ERPSupplierDO> customerList = erpSupplierMapper.selectList(custmReq);

            if (CollUtilX.isEmpty(customerList)){
                return PageResult.empty();
            }

            if (CollUtilX.isNotEmpty(customerList)) {
                customerIdList = customerList.stream().map(ERPSupplierDO::getSupplierId).collect(Collectors.toList());
                reqVO.setCustomerIdList(customerIdList);
            }
        }
        PageResult<InvoicePendingPlanDO> data = pendingPlanMapper.selectPage(reqVO);
        if (CollectionUtils.isNotEmpty(data.getList())){
            List<InvoicePendingPlanRespVO> list = data.getList().stream().map((item) -> {
                InvoicePendingPlanRespVO resp = BeanUtilX.copy(item, InvoicePendingPlanRespVO::new);

                return resp;
            }).collect(Collectors.toList());
            batchFillVoProperties(list);
            return PageResult.init(data, list);
        }
        return BeanUtilX.copy(data, InvoicePendingPlanRespVO::new);
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(InvoicePendingPlanRespVO respVO) {
        //查询客户名称
        String customerName = null;
        if(respVO.getBillingDirection() == BillingDirectionEnum.ISSUE_INVOICE.type.shortValue()){//开票
            //查询客户名称
            customerName = erpBaseService.getERPCustomerNameById(respVO.getCustomerId());
        }else{//收票
            //查询供应商
            customerName = erpSupplierService.getErpSupplierById(respVO.getCustomerId());
        }
        respVO.setCustomerName(customerName);

        //查询字典库信息
        List<String> dictColdeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), SystemDictEnum.INVOICE_PLAN_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictColdeList);

        //查询负责人
        String directorName = erpBaseService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);

        //查询责任部门
        String directorOrgName = erpBaseService.getOrgNameById(respVO.getDirectorOrgId().toString());
        respVO.setDirectorOrgName(directorOrgName);

        // 币种
        if(StrUtilX.isNotEmpty(respVO.getCurrencyDictId())){
            String currencyDictId =  SystemDictEnum.CURRENCY.getDictCode() + "-" + respVO.getCurrencyDictId();
            respVO.setCurrencyDictName(dictMap.get(currencyDictId));
        }

        // 开票/收票计划状态
        if(respVO.getInvoicePlanStatus() != null){
            String invoicePlanStatus =  SystemDictEnum.INVOICE_PLAN_STATUS.getDictCode() + "-" + respVO.getInvoicePlanStatus();
            respVO.setInvoicePlanStatusDictName(dictMap.get(invoicePlanStatus));
        }
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respList
     */
    private void batchFillVoProperties(List<InvoicePendingPlanRespVO> respList) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }


        //查询字典库信息
        List<String> dictColdeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), SystemDictEnum.INVOICE_PLAN_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictColdeList);

        List<Long> empIdList = new ArrayList<>();

        List<Long> customerIdList = new ArrayList<>();
        for (InvoicePendingPlanRespVO deatilResp: respList){
            empIdList.add(deatilResp.getDirectorId());
            customerIdList.add(deatilResp.getCustomerId());
        }
        Map<Long, String> customerNameMap = new HashMap<>();
        //查询客户名称
        if(respList.get(0).getBillingDirection() == BillingDirectionEnum.ISSUE_INVOICE.type.shortValue()) {//开票
            //查询客户名称
            customerNameMap = erpBaseService.getERPCustomerNameByIdList(customerIdList);
        } else {//收票
            //查询供应商
            customerNameMap = erpSupplierService.erpSupplierNameMap(customerIdList);
        }

        for (InvoicePendingPlanRespVO deatilResp: respList){

            //客户名称
            deatilResp.setCustomerName(customerNameMap.get(deatilResp.getCustomerId()));

            // 币种
            if(StrUtilX.isNotEmpty(deatilResp.getCurrencyDictId())){
                String currencyDictId =  SystemDictEnum.CURRENCY.getDictCode() + "-" + deatilResp.getCurrencyDictId();
                deatilResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 开票/收票计划状态
            if(deatilResp.getInvoicePlanStatus() != null){
                String invoicePlanStatus =  SystemDictEnum.INVOICE_PLAN_STATUS.getDictCode() + "-" + deatilResp.getInvoicePlanStatus();
                deatilResp.setInvoicePlanStatusDictName(dictMap.get(invoicePlanStatus));
            }
        }
    }

    private void detailBatchFillVoProperties(List<InvoicePendingDetailBaseVO> respList) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }


        //List<Long> empIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        for (InvoicePendingDetailBaseVO deatilResp: respList){
            //empIdList.add(deatilResp.getDirectorId());
            materialIdList.add(deatilResp.getMaterialId());
        }
        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        for (InvoicePendingDetailBaseVO respVO: respList){

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(respVO.getMaterialId());
            if (erpMaterialDO!=null){
                respVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                respVO.setMaterialName(erpMaterialDO.getMaterialName());
                respVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                respVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                respVO.setSpecModel(erpMaterialDO.getSpecModel());
                respVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
            respVO.setMainUnitDictName(dictMap.getOrDefault(respVO.getMainUnitDictId(),""));
        }
    }

}
