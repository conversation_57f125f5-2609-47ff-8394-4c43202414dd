package com.mongoso.mgs.module.dailycost.controller.admin.costprodlabordetail.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 生产人工成本明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CostProdLaborDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long costProdLaborDetailId;

    /** 生产人工成本单号 */
    private String costProdLaborCode;

    /** 生产人工成本ID */
    @NotNull(message = "生产人工成本ID不能为空")
    private Long costProdLaborId;

    /** 物料ID */
    private Long materialId;

    /** 工序ID */
    private Long processId;

    /** 成本用途 */
    private String costUsage;

    /** 成本科目管理ID */
    private Long costSubjectId;

    /** 承担对象ID */
    private String undertakeOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

}
