package com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.*;
import com.mongoso.mgs.module.dailycost.service.costprodpurchase.CostProdPurchaseService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 生产采购成本单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostProdPurchaseController {

    @Resource
    private CostProdPurchaseService costProdPurchaseService;

    @OperateLog("生产采购成本单添加或编辑")
    @PostMapping("/costProdPurchaseAdit")
    @PreAuthorize("@ss.hasPermission('costProdPurchase:adit')")
    public ResultX<Long> costProdPurchaseAdit(@Valid @RequestBody CostProdPurchaseAditReqVO reqVO) {
        return success(reqVO.getCostProdPurchaseId() == null
                            ? costProdPurchaseService.costProdPurchaseAdd(reqVO)
                            : costProdPurchaseService.costProdPurchaseEdit(reqVO));
    }

    @OperateLog("生产采购成本单修改承担物料信息")
    @PostMapping("/undertakeMaterialPurchaseAdit")
    @PreAuthorize("@ss.hasPermission('costProdPurchase:adit')")
    public ResultX<Long> undertakeMaterialAdit(@Valid @RequestBody CostProdPurchaseAditReqVO reqVO) {
        return success(costProdPurchaseService.undertakeMaterialAdit(reqVO));
    }


    @OperateLog("生产采购成本单列表")
    @PostMapping("/costProdPurchaseList")
    @PreAuthorize("@ss.hasPermission('costProdPurchase:query')")
    @DataPermission
    public ResultX<List<CostProdPurchaseRespVO>> costProdPurchaseList(@Valid @RequestBody CostProdPurchaseQueryReqVO reqVO) {
        return success(costProdPurchaseService.costProdPurchaseList(reqVO));
    }

    @OperateLog("生产采购成本单分页")
    @PostMapping("/costProdPurchasePage")
    @PreAuthorize("@ss.hasPermission('costProdPurchase:query')")
    @DataPermission
    public ResultX<PageResult<CostProdPurchaseRespVO>> costProdPurchasePage(@Valid @RequestBody CostProdPurchasePageReqVO reqVO) {
        return success(costProdPurchaseService.costProdPurchasePage(reqVO));
    }

    @OperateLog("摊销状态变更")
    @PostMapping("/amortiseStatusChange")
    @PreAuthorize("@ss.hasPermission('costProdPurchase:adit')")
    public ResultX<BatchResult> amortiseStatusChange(@Valid @RequestBody AmortiseStatusReqVO reqVO) {
        return success(costProdPurchaseService.amortiseStatusChange(reqVO));
    }

    @OperateLog("查询订单统计数据")
    @PostMapping("/queryOrderStatData")
    @PreAuthorize("@ss.hasPermission('costProdPurchase:query')")
    public ResultX<CostProdPurchaseOrderRespVO> queryOrderStatData(@Valid @RequestBody CostProdPurchasePageReqVO reqVO) {
        return success(costProdPurchaseService.queryOrderStatData());
    }
}
