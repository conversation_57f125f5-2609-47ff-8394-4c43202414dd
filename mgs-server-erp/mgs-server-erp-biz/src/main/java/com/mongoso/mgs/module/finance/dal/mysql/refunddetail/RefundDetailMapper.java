package com.mongoso.mgs.module.finance.dal.mysql.refunddetail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.refunddetail.vo.RefundDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.refunddetail.vo.RefundDetailQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.refunddetail.RefundDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 退款明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RefundDetailMapper extends BaseMapperX<RefundDetailDO> {

    default PageResult<RefundDetailDO> selectPageOld(RefundDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<RefundDetailDO>lambdaQueryX()
                .eqIfPresent(RefundDetailDO::getRefundId, reqVO.getRefundId())
                .eqIfPresent(RefundDetailDO::getFormType, reqVO.getFormType())
                .eqIfPresent(RefundDetailDO::getSourceFormType, reqVO.getSourceFormType())
                .eqIfPresent(RefundDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(RefundDetailDO::getSourceDocumentQty, reqVO.getSourceDocumentQty())
                .eqIfPresent(RefundDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getSourceOrderId, reqVO.getSourceOrderId())
                .likeIfPresent(RefundDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(RefundDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(RefundDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(RefundDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(RefundDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(RefundDetailDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .likeIfPresent(RefundDetailDO::getMainUnitDictName, reqVO.getMainUnitDictName())
                .eqIfPresent(RefundDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(RefundDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(RefundDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(RefundDetailDO::getRemainingRefundQty, reqVO.getRemainingRefundQty())
                .eqIfPresent(RefundDetailDO::getCurrentRefundQty, reqVO.getCurrentRefundQty())
                .eqIfPresent(RefundDetailDO::getRefundableAmt, reqVO.getRefundableAmt())
                .eqIfPresent(RefundDetailDO::getInclCurrentRefundAmt, reqVO.getInclCurrentRefundAmt())
                .betweenIfPresent(RefundDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(RefundDetailDO::getCreatedDt));
    }



    default PageResult<RefundDetailDO> selectPage(RefundDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<RefundDetailDO>lambdaQueryX()
                .eqIfPresent(RefundDetailDO::getRefundId, reqVO.getRefundId())
                .eqIfPresent(RefundDetailDO::getFormType, reqVO.getFormType())
                .eqIfPresent(RefundDetailDO::getSourceFormType, reqVO.getSourceFormType())
                .eqIfPresent(RefundDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(RefundDetailDO::getSourceDocumentQty, reqVO.getSourceDocumentQty())
                .eqIfPresent(RefundDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getSourceOrderId, reqVO.getSourceOrderId())
                .likeIfPresent(RefundDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(RefundDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(RefundDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(RefundDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(RefundDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(RefundDetailDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .likeIfPresent(RefundDetailDO::getMainUnitDictName, reqVO.getMainUnitDictName())
                .eqIfPresent(RefundDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(RefundDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(RefundDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(RefundDetailDO::getRemainingRefundQty, reqVO.getRemainingRefundQty())
                .eqIfPresent(RefundDetailDO::getCurrentRefundQty, reqVO.getCurrentRefundQty())
                .eqIfPresent(RefundDetailDO::getRefundableAmt, reqVO.getRefundableAmt())
                .eqIfPresent(RefundDetailDO::getInclCurrentRefundAmt, reqVO.getInclCurrentRefundAmt())
                .betweenIfPresent(RefundDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(RefundDetailDO::getCreatedDt));
    }

    default List<RefundDetailDO> selectListOld(RefundDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<RefundDetailDO>lambdaQueryX()
                .eqIfPresent(RefundDetailDO::getRefundId, reqVO.getRefundId())
                .eqIfPresent(RefundDetailDO::getFormType, reqVO.getFormType())
                .eqIfPresent(RefundDetailDO::getSourceFormType, reqVO.getSourceFormType())
                .eqIfPresent(RefundDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(RefundDetailDO::getSourceDocumentQty, reqVO.getSourceDocumentQty())
                .eqIfPresent(RefundDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getSourceOrderId, reqVO.getSourceOrderId())
                .likeIfPresent(RefundDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(RefundDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(RefundDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(RefundDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(RefundDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(RefundDetailDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .likeIfPresent(RefundDetailDO::getMainUnitDictName, reqVO.getMainUnitDictName())
                .eqIfPresent(RefundDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(RefundDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(RefundDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(RefundDetailDO::getRemainingRefundQty, reqVO.getRemainingRefundQty())
                .eqIfPresent(RefundDetailDO::getCurrentRefundQty, reqVO.getCurrentRefundQty())
                .eqIfPresent(RefundDetailDO::getRefundableAmt, reqVO.getRefundableAmt())
                .eqIfPresent(RefundDetailDO::getInclCurrentRefundAmt, reqVO.getInclCurrentRefundAmt())
                .betweenIfPresent(RefundDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(RefundDetailDO::getCreatedDt));
    }

    default List<RefundDetailDO> selectList(RefundDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<RefundDetailDO>lambdaQueryX()
                .eqIfPresent(RefundDetailDO::getRefundId, reqVO.getRefundId())
                .eqIfPresent(RefundDetailDO::getFormType, reqVO.getFormType())
                .eqIfPresent(RefundDetailDO::getSourceFormType, reqVO.getSourceFormType())
                .eqIfPresent(RefundDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(RefundDetailDO::getSourceDocumentQty, reqVO.getSourceDocumentQty())
                .eqIfPresent(RefundDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getSourceOrderId, reqVO.getSourceOrderId())
                .likeIfPresent(RefundDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(RefundDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(RefundDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(RefundDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(RefundDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(RefundDetailDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .likeIfPresent(RefundDetailDO::getMainUnitDictName, reqVO.getMainUnitDictName())
                .eqIfPresent(RefundDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(RefundDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(RefundDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(RefundDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(RefundDetailDO::getRemainingRefundQty, reqVO.getRemainingRefundQty())
                .eqIfPresent(RefundDetailDO::getCurrentRefundQty, reqVO.getCurrentRefundQty())
                .eqIfPresent(RefundDetailDO::getRefundableAmt, reqVO.getRefundableAmt())
                .eqIfPresent(RefundDetailDO::getInclCurrentRefundAmt, reqVO.getInclCurrentRefundAmt())
                .betweenIfPresent(RefundDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(RefundDetailDO::getSourceOrderCode, RefundDetailDO::getSourceLineNumber));
    }

}