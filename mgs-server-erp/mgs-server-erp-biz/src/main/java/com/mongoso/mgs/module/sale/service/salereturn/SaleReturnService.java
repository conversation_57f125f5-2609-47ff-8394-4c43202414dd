package com.mongoso.mgs.module.sale.service.salereturn;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.sale.controller.admin.salereturn.vo.*;
import com.mongoso.mgs.module.sale.dal.db.salereturn.SaleReturnDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 销售退货单 Service 接口
 *
 * <AUTHOR>
 */
public interface SaleReturnService {

    /**
     * 创建销售退货单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long saleReturnAdd(@Valid SaleReturnAditReqVO reqVO);

    /**
     * 更新销售退货单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long saleReturnEdit(@Valid SaleReturnAditReqVO reqVO);

    /**
     * 删除销售退货单
     *
     * @param id 编号
     */
    ResultX<String> saleReturnDel(Long id);

    /**
     * 删除销售退货单
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> saleReturnDelBatch(IdReq reqVO);

    /**
     * 获得销售退货单信息
     *
     * @param saleReturnId 编号
     * @return 销售退货单信息
     */
    SaleReturnRespVO saleReturnDetail(Long saleReturnId);

    /**
     * 获得销售退货单被引用信息
     *
     * @param saleReturnId 编号
     * @return 销售退货单信息
     */
    SaleReturnRespVO saleReturnQuotedDetail(Long saleReturnId);

    /**
     * 获得销售退货单列表
     *
     * @param reqVO 查询条件
     * @return 销售退货单列表
     */
    List<SaleReturnRespVO> saleReturnList(@Valid SaleReturnQueryReqVO reqVO);

    /**
     * 获得销售退货单分页
     *
     * @param reqVO 查询条件
     * @return 销售退货单分页
     */
    PageResult<SaleReturnDetailResp> saleReturnPage(@Valid SaleReturnPageReqVO reqVO);

    /**
     * 主子表销售退货单明细分页
     *
     * @param reqVO
     * @return
     */
    PageResult<SaleReturnDetailResp> querySaleReturnDetailPage(SaleReturnPageReqVO reqVO);

    BatchResult saleReturnApprove(FlowApprove reqVO);

    Object saleReturnFlowCallback(FlowCallback reqVO);
}
