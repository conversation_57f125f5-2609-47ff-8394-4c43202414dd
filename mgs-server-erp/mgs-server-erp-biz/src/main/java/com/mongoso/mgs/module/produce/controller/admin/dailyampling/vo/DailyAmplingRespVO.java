package com.mongoso.mgs.module.produce.controller.admin.dailyampling.vo;

import lombok.*;


    
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import com.alibaba.fastjson.JSONObject;
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 


/**
 * 日常抽检 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DailyAmplingRespVO extends DailyAmplingBaseVO {

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    private String supplierName;

    private String materialName;// 物料名称

    private List<String> checkUserNameList;

    private List<DailyAmplingNgDetailRespVO> ngDetailList;

}
