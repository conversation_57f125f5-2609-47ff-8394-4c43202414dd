package com.mongoso.mgs.module.finance.controller.admin.shouldpayment.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 应收付账款 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ShouldPaymentBaseVO implements Serializable {

    /** 应收款主键ID */
    private Long paymentId;

//    /** 对账单明细主键ID，如果来源对账单 */
//    private Long accountDetailId;

    /** 待计划主键ID */
    private Long planId;
    /** 应收账款状态 */
//    @NotNull(message = "应收账款状态不能为空")
    private Short accountsPayableStatus;

    /** 单据类型 */
    @NotNull(message = "单据类型不能为空")
    private Short formType;

    /** 应收账款单号 */
//    @NotEmpty(message = "应收账款单号不能为空")
    private String accountsPayableNumber;

    /** 来源单据类型 */
    @NotNull(message = "来源单据类型不能为空")
    private Short sourceFormType;

    /** 来源单id */
    @NotNull(message = "来源单id不能为空")
    private Long sourceOrderId;

    /** 来源单号 */
    @NotNull(message = "来源单号不能为空")
    private String sourceOrderCode;

    /** 供应商Id */
    @NotNull(message = "客户/供应商Id不能为空")
    private Long customerId;

    /** 供应商名称 */
    private String customerName;

    /** 币种名称 */
    private String currencyDictName;

    /** 币种id */
    @NotNull(message = "币种id不能为空")
    private String currencyDictId;

    /** 剩余应收金额 */
    @NotNull(message = "剩余应收金额不能为空")
    private BigDecimal amountPayable;

    /** 剩余应收数量 */
    private BigDecimal shouldPayQty;
    /**
     * 已收金额
     */
    private BigDecimal receivedAmt;

    /** 可申请数量 */
    private BigDecimal applyableAmt;

    /** 订单总金额 */
//    @NotNull(message = "订单总金额不能为空")
    private BigDecimal totalAmt;

    /** 单据时间 */
    @NotNull(message = "单据时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;
    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审批状态 */
    private Short dataStatus;

    /** 审批人 */
    private String approvedBy;
    /** 备注 */
    private String remark;
    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    @NotNull(message = "计划收款日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate planRecDate;

    @NotNull(message = "应收日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate actRecDate;

    /**
     * 实收金额 收款/退款回写
     */
    private BigDecimal realAmt;

    /**
     * 未收金额
     */
    private BigDecimal unpaidAmt;

    /** 版本号 */
    private Integer version;

    /** 是否跳过付款申请 */
    private Integer skipPaymentApply;

    /** 源头单据id */
    private Long originOrderId;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币应收金额 */
    private BigDecimal localCurrencyTotalAmt;

    /** 本币实收金额 */
    private BigDecimal localCurrencyRealAmt;

    /** 本币实收金额 */
    private BigDecimal localCurrencyAmountPayable;
}
