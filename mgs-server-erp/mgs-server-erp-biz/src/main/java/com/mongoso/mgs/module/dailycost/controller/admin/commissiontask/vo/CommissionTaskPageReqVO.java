package com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 
 


/**
 * 提成任务 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CommissionTaskPageReqVO extends PageParam {

    /** 提成任务单号 */
    private String commissionTaskCode;

    /** 销售订单id */
    private Long saleOrderId;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 关联客户 */
    private String customerName;
    private Long customerId;
    private List<Long> customerIdList;

    /** 物料ID */
    private Long materialId;
    private List<Long> materialIdList;

    /** 物料编码 */
    private String materialName;
    private String materialCode;

    /** 数量 */
    private BigDecimal qty;

    /** 单价 (不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额 (不含税) */
    private BigDecimal exclTaxAmt;

    /** 提成比例 */
    private BigDecimal commissionRatio;

    /** 提成金额 */
    private BigDecimal commissionAmt;

    /** 员工提成列表 */
    private List<JSONObject> employeeCommissionList;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 单据状态 */
    private Short dataStatus;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

}
