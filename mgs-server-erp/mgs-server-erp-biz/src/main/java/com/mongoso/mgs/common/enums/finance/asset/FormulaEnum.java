package com.mongoso.mgs.common.enums.finance.asset;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 公式
 */
@AllArgsConstructor
@Getter
public enum FormulaEnum implements Serializable {
    ORIGINAL_VALUE("(1)","assetPurchasePrice","资产购价"),
    NET_RESIDUAL_RATE("(2)","netResidualValueRate","净残值率"),
    NET_RESIDUAL_VALUE("(3)","netResidualValue","净残值"),
    WORKLOAD("(4)","workload","工作量"),
    ACCRUAL_PERIOD("(5)","accrualPeriod","计提周期"),
    ACCRUED_PERIOD("(6)","preAccruedPeriod","以前已提周期"),
    AMOUNT_ACCRUED("(7)","preAccruedAmt","以前已提金额");

    public final String key;// 类型
    public final String value;// 字段
    public final String name;// 名称
}
