package com.mongoso.mgs.module.finance.service.invoice.invoiceissue;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssueAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssuePageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssueQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssueRespVO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 实开发票 Service 接口
 *
 * <AUTHOR>
 */
public interface InvoiceIssueService {

    /**
     * 创建实开发票
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long invoiceIssueAdd(@Valid InvoiceIssueAditReqVO reqVO);

    /**
     * 新增红冲数据
     * @param reqVO
     * @return
     */
    Long invoiceIssueRedAdd(@Valid InvoiceIssueAditReqVO reqVO);

    /**
     * 更新实开发票
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long invoiceIssueEdit(@Valid InvoiceIssueAditReqVO reqVO);

    /**
     * 删除实开发票
     *
     * @param invoiceId 编号
     */
    void invoiceIssueDel(Long invoiceId);

    /**
     * 批量删除
     * @param idReq
     * @return
     */
    ResultX<BatchResult> invoiceIssueDelBatch(IdReq idReq);

    /**
     * 获得实开发票信息
     *
     * @param invoiceId 编号
     * @return 实开发票信息
     */
    InvoiceIssueRespVO invoiceIssueDetail(Long invoiceId);

    /**
     * 获得实开发票列表
     *
     * @param reqVO 查询条件
     * @return 实开发票列表
     */
    List<InvoiceIssueRespVO> invoiceIssueList(@Valid InvoiceIssueQueryReqVO reqVO);

    /**
     * 获得实开发票分页
     *
     * @param reqVO 查询条件
     * @return 实开发票分页
     */
    PageResult<InvoiceIssueRespVO> invoiceIssuePage(@Valid InvoiceIssuePageReqVO reqVO);

    /**
     * 实开发票审核
     * @param reqVO
     * @return
     */
    BatchResult invoiceIssueApprove(FlowApprove reqVO);

    /**
     * 实开发票回调接口
     * @param reqVO
     * @return
     */
    Object invoiceIssueFlowCallback(FlowCallback reqVO);
}
