package com.mongoso.mgs.module.finance.controller.admin.pendingpaymentplandetail.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 待收款计划明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class PendingPaymentPlanDetailQueryReqVO extends CommonParam{

    /** 计划详情主键ID */
    private Long planDetailId;

    /** 单据类型 */
    private Short formType;

    /** 计划单id */
    private Long planId;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 来源单号 */
    private String sourceOrderNumber;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 来源单id */
    private Long sourceOrderId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 主单位ID */
    private String mainUnitDictId;

    /** 来源单据数量 */
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    private Long invoiceTypeDictId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 剩余应收数量 */
    private BigDecimal receiveableQty;

    /** 本次应收数量 */
    private BigDecimal currentReceiveQty;

    /** 本次应收金额(不含税) */
    private BigDecimal exclCurrentReceiveAmt;

    /** 本次应收金额(含税) */
    private BigDecimal inclCurrentReceiveAmt;


    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
