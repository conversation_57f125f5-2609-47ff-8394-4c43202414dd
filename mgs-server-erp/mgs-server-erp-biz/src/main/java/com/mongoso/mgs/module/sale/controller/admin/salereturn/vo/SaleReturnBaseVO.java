package com.mongoso.mgs.module.sale.controller.admin.salereturn.vo;

import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.sale.controller.admin.salereturndetail.vo.SaleReturnDetailRespVO;
import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
  import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 销售退货单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SaleReturnBaseVO implements Serializable {

    /** 主键ID */
    private Long saleReturnId;

    /** 销售退货单号 */
    private String salesReturnCode;

    /** 销售退货单类型 */
    private String salesReturnTypeDictId;
    private String salesReturnTypeDictName;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 销售订单id */
    private Long saleOrderId;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 销售订单类型 */
    private String salesOrderTypeDictId;

    /** 销售订单类型名称 */
    private String salesOrderTypeDictName;

    /** 关联单据id */
    private Long relatedOrderId;

    /** 关联单据code */
    private String relatedOrderCode;

    /** 关联客户 */
    private Long customerId;
    private String customerName;

    /** 联系人 */
    private String contactPersonName;

    /** 联系人电话 */
    private String contactPhone;

    /** 收货人 */
    private String recipientName;

    /** 收货人电话 */
    private String recipientPhone;

    /** 收货地址 */
    private String receiptAddress;

    /** 退货日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate returnDate;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate deliveryDate;

    /** 币种id */
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 结算方式 */
    private String settlementMethodDictId;
    private String settlementMethodDictName;

    /** 退款条件 */
    private String refundConditionDictId;
    private String refundConditionDictName;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 税率 */
    private BigDecimal detailTaxRate;

    /** 退款总金额 */
    private BigDecimal totalRefundAmt;

    /** 审批状态 */
    private Integer dataStatus;

    /** 备注 */
    private String remark;

    /** 下发销售退货收获单 */
    private Short isIssueReturnReceipt;

    /** 下发销售退货入库单 */
    private Short isIssueReturnInventory;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
     private String directorOrgId;
    private String directorOrgName;

    /** 公司主体 */
    private String companyOrgId;
    private String companyOrgName;

    /** 是否可下发销售退货收货单 */
    private Short ableReturnReceipt;

    /** 是否可下发销售退货入库单 */
    private Short ableReturnInbound;

    /** 是否全部已操作 */
    private Integer isFullOpered;

    /** 产品明细 */
    private List<SaleReturnDetailRespVO> detailList;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币退款总金额 */
    private BigDecimal localCurrencyTotalRefundAmt;

}
