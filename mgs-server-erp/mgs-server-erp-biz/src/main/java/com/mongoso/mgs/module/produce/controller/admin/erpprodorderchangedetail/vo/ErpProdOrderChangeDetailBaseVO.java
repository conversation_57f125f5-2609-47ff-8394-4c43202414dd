package com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 
/**
 * 生产物料 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ErpProdOrderChangeDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long prodOrderChangeDetailId;

    /** 生产订单变更单id */
    private Long prodOrderChangeId;

    /** 生产订单变更单号 */
    private String prodOrderChangeCode;

    /** 优先级 */
    private Integer priority;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 行号 */
    private Integer rowNo;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate deliveryDate;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planEndDate;

    /** 生产计划数量 */
    private BigDecimal prodPlanQty;

    /** 工单计划数量 */
    private BigDecimal workPlanQty;

    /** 委外采购数量 */
    private BigDecimal outsourceQty;

    /** 生产数量 */
    private BigDecimal producedQty;

    /** 良品数量 */
    private BigDecimal okQty;

    /** 不良品数量 */
    private BigDecimal ngQty;

    /** 委外采购入库数量 */
    private BigDecimal outsourceInboundQty;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 主单位(基本单位) */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 物料来源 */
    private Integer materialSourceDictId;
    private String materialSourceDictName;

    /** 已采购数量 */
    private BigDecimal purchasedQty;

}
