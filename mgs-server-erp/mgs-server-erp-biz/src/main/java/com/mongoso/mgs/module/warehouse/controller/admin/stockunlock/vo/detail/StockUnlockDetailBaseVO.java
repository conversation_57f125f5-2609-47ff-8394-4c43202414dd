package com.mongoso.mgs.module.warehouse.controller.admin.stockunlock.vo.detail;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 
/**
 * 库存解锁单明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class StockUnlockDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long unlockDetailId;

    /** 行号 */
    private Long rowNo;

    /** 库存解锁单ID */
    private Long unlockId;

    /** 库存解锁单号 */
    private String unlockCode;

    /** 库存锁定单明细ID */
    private Long lockDetailId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 仓库组织ID */
    private String warehouseOrgId;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 解锁数量 */
    private BigDecimal unlockQty;

    /** 备注 */
    private String remark;

}
