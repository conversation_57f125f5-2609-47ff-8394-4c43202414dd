package com.mongoso.mgs.module.warehouse.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.warehouse.dal.db.erptransfer.ErpTransferDO;
import org.springframework.stereotype.Component;

/**
 * @author: Fashon.Liu
 * @date: 2024/12/19 9:40
 * @description: 调拨单回调处理类
 */

@Component
public class ErpTransferFlowCallBackHandler extends FlowCallbackHandler<ErpTransferDO> {

    protected ErpTransferFlowCallBackHandler(FlowApproveHandler<ErpTransferDO> approveHandler) {
        super(approveHandler);
    }

}
