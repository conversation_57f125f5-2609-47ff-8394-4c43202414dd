package com.mongoso.mgs.module.sale.service.cutomerrel;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.sale.controller.admin.cutomerrel.vo.*;
import com.mongoso.mgs.module.sale.dal.db.cutomerrel.CutomerRelDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 客户中间 Service 接口
 *
 * <AUTHOR>
 */
public interface CutomerRelService {

    /**
     * 创建客户中间
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long cutomerRelAdd(@Valid CutomerRelAditReqVO reqVO);

    /**
     * 更新客户中间
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long cutomerRelEdit(@Valid CutomerRelAditReqVO reqVO);

    /**
     * 删除客户中间
     *
     * @param customerRelId 编号
     */
    void cutomerRelDelete(Long customerRelId);

    /**
     * 获得客户中间信息
     *
     * @param customerRelId 编号
     * @return 客户中间信息
     */
    CutomerRelRespVO cutomerRelDetail(Long customerRelId);

    /**
     * 获得客户中间列表
     *
     * @param reqVO 查询条件
     * @return 客户中间列表
     */
    List<CutomerRelRespVO> cutomerRelList(@Valid CutomerRelQueryReqVO reqVO);

    /**
     * 获得客户中间分页
     *
     * @param reqVO 查询条件
     * @return 客户中间分页
     */
    PageResult<CutomerRelRespVO> cutomerRelPage(@Valid CutomerRelPageReqVO reqVO);

}
