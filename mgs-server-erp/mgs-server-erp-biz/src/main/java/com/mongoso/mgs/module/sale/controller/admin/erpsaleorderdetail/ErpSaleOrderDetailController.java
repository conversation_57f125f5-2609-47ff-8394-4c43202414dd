package com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail;

import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.*;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail.ErpSaleOrderDetailDO;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.ErpSaleOrderDetailService;

/**
 * 销售订单明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sale")
@Validated
public class ErpSaleOrderDetailController {

    @Resource
    private ErpSaleOrderDetailService erpSaleOrderDetailService;

    @OperateLog("销售订单明细添加或编辑")
    @PostMapping("/erpSaleOrderDetailAdit")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:adit')")
    public ResultX<Long> erpSaleOrderDetailAdit(@Valid @RequestBody ErpSaleOrderDetailAditReqVO reqVO) {
        return success(reqVO.getSaleOrderDetailId() == null
                            ? erpSaleOrderDetailService.erpSaleOrderDetailAdd(reqVO)
                            : erpSaleOrderDetailService.erpSaleOrderDetailEdit(reqVO));
    }

    @OperateLog("销售订单明细删除")
    @PostMapping("/erpSaleOrderDetailDel")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:del')")
    public ResultX<Boolean> erpSaleOrderDetailDel(@Valid @RequestBody ErpSaleOrderDetailPrimaryReqVO reqVO) {
        erpSaleOrderDetailService.erpSaleOrderDetailDel(reqVO.getSaleOrderDetailId());
        return success(true);
    }

    @OperateLog("销售订单明细详情")
    @PostMapping("/erpSaleOrderDetailDetail")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<ErpSaleOrderDetailRespVO> erpSaleOrderDetailDetail(@Valid @RequestBody ErpSaleOrderDetailPrimaryReqVO reqVO) {
        ErpSaleOrderDetailDO oldDO = erpSaleOrderDetailService.erpSaleOrderDetailDetail(reqVO.getSaleOrderDetailId());
        return success(BeanUtilX.copy(oldDO, ErpSaleOrderDetailRespVO::new));
    }

    @OperateLog("销售订单明细列表")
    @PostMapping("/erpSaleOrderDetailList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpSaleOrderDetailList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpSaleOrderDetailList(reqVO);
        return success(BeanUtilX.copyList(list, ErpSaleOrderDetailRespVO::new));
    }

    @OperateLog("销售订单明细列表")
    @PostMapping("/erpSaleOrderDetailQuotedList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpSaleOrderDetailQuotedList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpSaleOrderDetailList(reqVO);
        return success(BeanUtilX.copyList(list, ErpSaleOrderDetailRespVO::new));
    }

    @OperateLog("销售订单明细分页")
    @PostMapping("/erpSaleOrderDetailPage")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    @DataPermission
    public ResultX<PageResult<ErpSaleOrderDetailRespVO>> erpSaleOrderDetailPage(@Valid @RequestBody ErpSaleOrderDetailPageReqVO reqVO) {
        PageResult<ErpSaleOrderDetailDO> pageResult = erpSaleOrderDetailService.erpSaleOrderDetailPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ErpSaleOrderDetailRespVO::new));
    }

    @OperateLog("销售发货通知单引用物料列表")
    @PostMapping("/erpShipNoticelQuoteList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpShipNoticelQuoteList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpShipNoticelQuoteList(reqVO);
        return success(list);
    }

    @OperateLog("销售退货单引用物料列表")
    @PostMapping("/erpSaleReturnQuoteList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpSaleReturnQuoteList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpSaleReturnQuoteList(reqVO);
        return success(list);
    }

    @OperateLog("销售扣费单引用物料列表")
    @PostMapping("/erpSaleDeductionQuoteList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpSaleDeductionQuoteList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpSaleDeductionQuoteList(reqVO);
        return success(list);
    }

    @OperateLog("销售换货单引用物料列表")
    @PostMapping("/erpSaleExchangeQuoteList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpSaleExchangeQuoteList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpSaleExchangeQuoteList(reqVO);
        return success(list);
    }

    @OperateLog("销售采购单引用物料列表")
    @PostMapping("/erpSalePurQuoteList")
    @PreAuthorize("@ss.hasPermission('erpSaleOrderDetail:query')")
    public ResultX<List<ErpSaleOrderDetailRespVO>> erpSalePurQuoteList(@Valid @RequestBody ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailRespVO> list = erpSaleOrderDetailService.erpSalePurQuoteList(reqVO);
        return success(list);
    }

}
