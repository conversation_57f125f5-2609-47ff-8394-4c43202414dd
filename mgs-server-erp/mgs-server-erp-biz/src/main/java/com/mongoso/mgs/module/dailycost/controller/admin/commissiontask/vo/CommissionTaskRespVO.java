package com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 
 


/**
 * 提成任务 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CommissionTaskRespVO extends CommissionTaskBaseVO {

    /** 审核状态 */
    private String dataStatusDictName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 责任人 */
    private String directorName;

    /** 责任部门 */
    private String directorOrgName;

    /** 客户名称 */
    private String customerName;

    /** 物料名称 */
    private String materialName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 主单位(基本单位) */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 审批任务id */
    private Long approveTaskId;
}
