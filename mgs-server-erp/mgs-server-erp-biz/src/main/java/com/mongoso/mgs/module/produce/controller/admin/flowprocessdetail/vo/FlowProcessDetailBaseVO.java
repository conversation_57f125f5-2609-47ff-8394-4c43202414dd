package com.mongoso.mgs.module.produce.controller.admin.flowprocessdetail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
 import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
 import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工艺路线明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class FlowProcessDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long flowProcessDetailId;

    /** 工艺路线id */
    private Long flowProcessId;

    /** 工艺路线编码 */
    private String flowProcessCode;

    /** 工艺路线类型['工艺路线','生产工单工艺路线'] */
    private Integer flowProcessType;

    /** 序号 */
    @NotNull(message = "序号不能为空")
    private Integer rowNo;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 工序id */
    private Long processId;
    private String processCode;
    private String processName;
    private String processDesc;

    /** 是否带模生产 */
    private Integer isMolded;

    //是否最终工序['否','是']
    private Integer finalProcess;

    /** 加工方式 */
    private Long processMethod;
    private String processMethodName;

    /** 计件方式 */
    private String pieceworkMethodDictId;
    private String pieceworkMethodDictName;

//    /** 良品计价规则 */
//    private String goodProductPriceRule;
//
//    /** 良品计价规则信息 */
//    private List<JSONObject> goodProductPriceRuleInfo;
//
//    /** 良品理论单价 */
//    private BigDecimal goodProductPrice;
//
//    /** 质检计价规则 */
//    private String checkPriceRule;
//
//    /** 质检计价规则信息 */
//    private List<JSONObject> checkPriceRuleInfo;
//
//    /** 质检理论单价 */
//    private BigDecimal checkPrice;
//
//    /** 返工计价规则 */
//    private String reworkPriceRule;
//
//    /** 返工计价规则信息 */
//    private List<JSONObject> reworkPriceRuleInfo;
//
//    /** 返工理论单价 */
//    private BigDecimal reworkPrice;

    /** 前工序 */
    private Long preProcessId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

//    /** 良品维度属性信息 */
//    private List<JSONObject> goodProductRuleInfo;
//
//    /** 质检维度属性信息 */
//    private List<JSONObject> checkRuleInfo;
//
//    /** 返工维度属性信息 */
//    private List<JSONObject> reworkRuleInfo;


}
