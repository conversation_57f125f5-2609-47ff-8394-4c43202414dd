package com.mongoso.mgs.module.dailycost.service.spuconfig;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.base.dal.db.spu.SpuDO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigRespVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * SPU配置 Service 接口
 *
 * <AUTHOR>
 */
public interface CostSpuConfigService {

    /**
     * 创建SPU配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costSpuConfigAdd(@Valid CostSpuConfigAditReqVO reqVO);

    /**
     * 更新SPU配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long costSpuConfigEdit(@Valid CostSpuConfigAditReqVO reqVO);

    /**
     * 上游单据审核反审核操作SPU配置
     * @param buttonType
     * @param materialList
     * @return
     */
    Long costSpuConfigOperate(Integer buttonType, List<CostSpuConfigAditReqVO> materialList);
    Long costSpuConfigOperate(List<CostSpuConfigAditReqVO> addList, List<CostSpuConfigAditReqVO> delList);

    /**
     * 删除SPU配置
     *
     * @param spuConfigId 编号
     */
    void costSpuConfigDel(Long spuConfigId);

    /**
     * 获得SPU配置信息
     *
     * @param spuConfigId 编号
     * @return SPU配置信息
     */
    CostSpuConfigRespVO costSpuConfigDetail(Long spuConfigId);

    /**
     * 获得SPU配置列表
     *
     * @param reqVO 查询条件
     * @return SPU配置列表
     */
    List<CostSpuConfigRespVO> costSpuConfigList(@Valid CostSpuConfigQueryReqVO reqVO);

    /**
     * 获得SPU配置分页
     *
     * @param reqVO 查询条件
     * @return SPU配置分页
     */
    PageResult<CostSpuConfigRespVO> costSpuConfigPage(@Valid CostSpuConfigPageReqVO reqVO);

    BatchResult costSpuConfigApprove(FlowApprove reqVO);

    List<SpuDO> materialBomSpuList(MaterialBomQueryReqVO reqVO);

    ResultX<BatchResult> costSpuConfigDelBatch(IdReq reqVO);

    /**
     * 批量查询spu编码
     *
     * @param relatedUpOrderIdList 更新信息
     * @return 编号
     */
    Map<Long, Map<Long, String>> spuCodeMap(List<Long> relatedUpOrderIdList);
}
