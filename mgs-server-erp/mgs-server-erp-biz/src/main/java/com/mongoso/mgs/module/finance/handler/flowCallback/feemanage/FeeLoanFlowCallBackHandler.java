package com.mongoso.mgs.module.finance.handler.flowCallback.feemanage;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feeapply.FeeApplyDO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feeloan.FeeLoanDO;
import org.springframework.stereotype.Component;

@Component
public class FeeLoanFlowCallBackHandler extends FlowCallbackHandler<FeeLoanDO> {
    protected FeeLoanFlowCallBackHandler(FlowApproveHandler<FeeLoanDO> approveHandler) {
        super(approveHandler);
    }
}