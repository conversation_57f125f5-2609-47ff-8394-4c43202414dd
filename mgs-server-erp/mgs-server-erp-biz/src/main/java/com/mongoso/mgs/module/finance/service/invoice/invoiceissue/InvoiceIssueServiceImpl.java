package com.mongoso.mgs.module.finance.service.invoice.invoiceissue;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapply.enums.InvoiceDirctionEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetail.vo.InvoiceDetailBaseVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListRespVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.enums.InvoiceIssueStatusEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.req.IssueDetail;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.resp.InvoiceDetailMsgResp;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssueAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssuePageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssueQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.InvoiceIssueRespVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapply.InvoiceApplyDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapplydetail.InvoiceApplyDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetail.InvoiceDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetaillist.InvoiceDetailListDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceissue.InvoiceIssueDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplan.InvoicePlanDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail.InvoicePlanDetailDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapply.InvoiceApplyMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapplydetail.InvoiceApplyDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicedetail.InvoiceDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicedetaillist.InvoiceDetailListMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceissue.InvoiceIssueMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplan.InvoicePlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplandetail.InvoicePlanDetailMapper;
import com.mongoso.mgs.module.finance.handler.approve.invoice.InvoiceIssueApproveHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.invoice.InvoiceIssueFlowCallBackHandler;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.InvTypeManageQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.invoice.enums.ErrorCodeConstants.*;


/**
 * 实开发票 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InvoiceIssueServiceImpl implements InvoiceIssueService {

    @Resource
    private InvoiceIssueMapper issueMapper;

    @Resource
    private InvoiceApplyMapper invoiceApplyMapper;

    @Resource
    private InvoiceApplyDetailMapper invoiceApplyDetailMapper;

    @Resource
    private InvoiceDetailMapper invoiceDetailMapper;

    @Resource
    private ErpBaseService compositeService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private InvoiceIssueApproveHandler invoiceIssueApproveHandler;

    @Resource
    private InvoiceIssueFlowCallBackHandler invoiceIssueFlowCallBackHandler;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;

    @Resource
    private InvoicePlanMapper invoicePlanMapper;

    @Resource
    private InvoiceDetailListMapper invoiceDetailListMapper;

    @Resource
    private InvTypeManageMapper invTypeManageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long invoiceIssueAdd(InvoiceIssueAditReqVO reqVO) {

        //发票代码验重
        InvoiceIssueDO invoiceIssueDO = issueMapper.selectOne(new LambdaQueryWrapper<InvoiceIssueDO>()
                .eq(InvoiceIssueDO::getInvoiceCode, reqVO.getInvoiceCode())
                .last("limit 1")
        );
        if (Objects.nonNull(invoiceIssueDO)){
            throw new BizException("5001", "发票代码已存在");
        }

        // 插入
        InvoiceIssueDO issue = BeanUtilX.copy(reqVO, InvoiceIssueDO::new);
        long invoiceId = IdWorker.getId();
        issue.setInvoiceId(invoiceId);
        issue.setInvoiceApplyId(reqVO.getInvoiceApplyId());


        if (reqVO.getInvoiceTotalTax().compareTo(BigDecimal.ZERO) == 0){
            throw new BizException("5001", "开票金额总额不能为0");
        }

        //查询开票申请信息
        if (reqVO.getInvoiceApplyId() != null) {
            InvoiceApplyDO invoiceApplyDO = invoiceApplyMapper.selectById(reqVO.getInvoiceApplyId());
//        if (reqVO.getInvoiceTotalTax().compareTo(invoiceApplyDO.getCanAmt()) > 0){
//            throw new BizException("5001", "开票金额总额大于剩余可开票金额");
//        }
            if (issue.getInvoiceTotalTax().compareTo(BigDecimal.ZERO) > 0){
                issue.setInvoiceDirection(InvoiceDirctionEnum.BLUE.type.shortValue());
            }else {
                issue.setInvoiceDirection(InvoiceDirctionEnum.RED.type.shortValue());
            }

            issue.setBillingDirection(invoiceApplyDO.getBillingDirection());
            issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.NOT_INVOICE.type.shortValue());
            issue.setInvoiceDirection(invoiceApplyDO.getInvoiceDirection());
            issue.setCurrencyDictId(invoiceApplyDO.getCurrencyDictId());
            issue.setInvoiceTypeDictId(invoiceApplyDO.getInvoiceTypeDictId());
            issue.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey().shortValue());
            issue.setInvoiceTypeDictName(invoiceApplyDO.getInvoiceTypeDictName());
            issue.setCurrencyDictId(invoiceApplyDO.getCurrencyDictId());
            issue.setSourceFormType(invoiceApplyDO.getSourceFormType());
            issue.setCustomerId(invoiceApplyDO.getCustomerId());
            issue.setInvoiceStrategy(invoiceApplyDO.getInvoiceStrategy());
            issue.setCollectionPlanStrategy(invoiceApplyDO.getCollectionPlanStrategy());
            issue.setRelatedOrderCode(invoiceApplyDO.getInvoiceApplyNo());
        }else {
            InvoicePlanDO invoicePlanDO = invoicePlanMapper.selectById(reqVO.getInvoicePlanId());
            if (issue.getInvoiceTotalTax().compareTo(BigDecimal.ZERO) > 0){
                issue.setInvoiceDirection(InvoiceDirctionEnum.BLUE.type.shortValue());
            }else {
                issue.setInvoiceDirection(InvoiceDirctionEnum.RED.type.shortValue());
            }
            issue.setBillingDirection(invoicePlanDO.getBillingDirection());
            issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.NOT_INVOICE.type.shortValue());
            issue.setCurrencyDictId(invoicePlanDO.getCurrencyDictId());
            issue.setInvoiceTypeDictId(invoicePlanDO.getInvoiceTypeDictId());
            issue.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey().shortValue());
            issue.setInvoiceTypeDictName(invoicePlanDO.getInvoiceTypeDictName());
            issue.setCurrencyDictId(invoicePlanDO.getCurrencyDictId());
            issue.setSourceFormType(invoicePlanDO.getSourceFormType());
            issue.setCustomerId(invoicePlanDO.getCustomerId());
            issue.setInvoiceStrategy(invoicePlanDO.getInvoiceStrategy());
            issue.setCollectionPlanStrategy(invoicePlanDO.getCollectionPlanStrategy());
            issue.setRelatedOrderCode(invoicePlanDO.getInvoicePlanNo());
        }
        issueMapper.insert(issue);

        //新增明细
        List<Long> applyDetailId = new ArrayList<>();
        List<Long> planDetailId = new ArrayList<>();
        reqVO.getDetailList().forEach(issueDetail -> {
            applyDetailId.add(issueDetail.getInvoiceApplyDetailId());
        });
        reqVO.getDetailList().forEach(issueDetail -> {
            planDetailId.add(issueDetail.getInvoicePlanDetailId());
        });
        Map<Long, InvoiceApplyDetailDO> applyDetailMap = new HashMap<>();
        if (reqVO.getInvoiceApplyId() != null) {
            List<InvoiceApplyDetailDO> applyDetailList = invoiceApplyDetailMapper.selectBatchIds(applyDetailId);
            if (CollectionUtils.isEmpty(applyDetailList)) {
                throw new BizException("5001", "申请开票明细id不存在");
            }
            applyDetailMap = applyDetailList.stream().collect(Collectors.toMap(InvoiceApplyDetailDO::getInvoiceApplyDetailId, Function.identity(), (s1, s2) -> s1));
        }
        Map<Long, InvoicePlanDetailDO> planDetailMap = new HashMap<>();
        if (reqVO.getInvoiceApplyId() == null) {
            List<InvoicePlanDetailDO> planDetailList = invoicePlanDetailMapper.selectBatchIds(planDetailId);
            if (CollectionUtils.isEmpty(planDetailList)) {
                throw new BizException("5001", "申请计划明细id不存在");
            }
            planDetailMap = planDetailList.stream().collect(Collectors.toMap(InvoicePlanDetailDO::getInvoicePlanDetailId, Function.identity(), (s1, s2) -> s1));
        }


        List<InvoiceDetailDO> list = new ArrayList<>();
        for (IssueDetail issueDetail : reqVO.getDetailList()){
            if (reqVO.getInvoiceApplyId() != null) {
                InvoiceApplyDetailDO detailDO = applyDetailMap.get(issueDetail.getInvoiceApplyDetailId());
                if (Objects.isNull(detailDO)){
                    throw new BizException("5001", "申请开票明细id异常");
                }
                if (issueDetail.getInvoiceAmtInclTax().compareTo(BigDecimal.ZERO) == 0){
                    throw new BizException("5001", "开票金额不能为0");
                }
                if (issueDetail.getInvoiceAmtInclTax().abs().compareTo(detailDO.getCanAmt().abs()) > 0){
                    throw new BizException("5001", "开票金额大于剩余开票金额");
                }
                InvoiceDetailDO invoiceDetailDO = BeanUtilX.copy(detailDO, InvoiceDetailDO::new);
                long invoiceDetailId = IdWorker.getId();
                invoiceDetailDO.setInvoiceDetailId(invoiceDetailId);
                invoiceDetailDO.setInvoiceId(invoiceId);
                invoiceDetailDO.setInvoiceApplyDetailId(detailDO.getInvoiceApplyDetailId());
                invoiceDetailDO.setQty(issueDetail.getInvoiceQty());
                invoiceDetailDO.setRemainingInvoiceAmt(detailDO.getRemainingApplyAmt());
                invoiceDetailDO.setRemainingInvoiceQty(detailDO.getRemainingApplyQty());
                invoiceDetailDO.setApplyInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());
                invoiceDetailDO.setApplyInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
                invoiceDetailDO.setInvoiceQty(issueDetail.getInvoiceQty());
                invoiceDetailDO.setInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
                invoiceDetailDO.setInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());

                invoiceDetailDO.setTaxAmt(issueDetail.getInvoiceAmtInclTax().subtract(issueDetail.getInvoiceAmtExclTax()));
                invoiceDetailDO.setMaterialCode(detailDO.getMaterialCode());
                invoiceDetailDO.setInvoiceCode(reqVO.getInvoiceCode());
                invoiceDetailDO.setRowNo(issueDetail.getRowNo());
                list.add(invoiceDetailDO);
            }else {
                InvoicePlanDetailDO detailDO = planDetailMap.get(issueDetail.getInvoicePlanDetailId());
                if (Objects.isNull(detailDO)){
                    throw new BizException("5001",reqVO.getBillingDirection() == 1 ? "申请收票明细id异常" : "开票计划明细id异常");
                }
                if (issueDetail.getInvoiceAmtInclTax().compareTo(BigDecimal.ZERO) == 0){
                    throw new BizException("5001",reqVO.getBillingDirection() == 1 ? "收票金额不能为0" : "开票金额不能为0");
                }
                if (issueDetail.getInvoiceAmtInclTax().abs().compareTo(detailDO.getCanInvoiceAmt().abs()) > 0){
                    throw new BizException("5001",reqVO.getBillingDirection() == 1 ? "收票金额大于剩余收票金额" : "开票金额大于剩余开票金额");
                }
                InvoiceDetailDO invoiceDetailDO = BeanUtilX.copy(detailDO, InvoiceDetailDO::new);
                long invoiceDetailId = IdWorker.getId();
                invoiceDetailDO.setInvoiceDetailId(invoiceDetailId);
                invoiceDetailDO.setInvoiceId(invoiceId);
                invoiceDetailDO.setInvoicePlanDetailId(detailDO.getInvoicePlanDetailId());
                invoiceDetailDO.setQty(issueDetail.getInvoiceQty());
                invoiceDetailDO.setRemainingInvoiceAmt(detailDO.getRemainingPlanAmt());
                invoiceDetailDO.setRemainingInvoiceQty(detailDO.getRemainingPlanQty());
                invoiceDetailDO.setApplyInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());
                invoiceDetailDO.setApplyInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
                invoiceDetailDO.setInvoiceQty(issueDetail.getInvoiceQty());
                invoiceDetailDO.setInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
                invoiceDetailDO.setInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());

                invoiceDetailDO.setTaxAmt(issueDetail.getInvoiceAmtInclTax().subtract(issueDetail.getInvoiceAmtExclTax()));
                invoiceDetailDO.setMaterialCode(detailDO.getMaterialCode());
                invoiceDetailDO.setRowNo(issueDetail.getRowNo());
                invoiceDetailDO.setInvoiceCode(reqVO.getInvoiceCode());
                list.add(invoiceDetailDO);
            }

            //invoiceDetailDO.setin
        }
        //新增开票明细
        invoiceDetailMapper.insertBatch(list);

        this.invoiceDetailListAdd(reqVO.getDetailMsgList(),issue.getInvoiceId());
        // 返回
        return issue.getInvoiceId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long invoiceIssueRedAdd(InvoiceIssueAditReqVO reqVO) {

        if (reqVO.getInvoiceTotalTax().compareTo(BigDecimal.ZERO) >= 0){
            throw new BizException("5001", "红字发票价税合计不能大于等于0");
        }
        //发票代码验重
        InvoiceIssueDO invoiceIssueDO = issueMapper.selectOne(new LambdaQueryWrapper<InvoiceIssueDO>()
                .eq(InvoiceIssueDO::getInvoiceCode, reqVO.getInvoiceCode())
                .last("limit 1")
        );
        if (Objects.nonNull(invoiceIssueDO)){
            if (reqVO.getInvoiceId() != null){
                return invoiceIssueEdit(reqVO);
            }
            throw new BizException("5001", "发票代码已存在");
        }

        //查询开票信息
        InvoiceIssueDO invoiceApplyDO = issueMapper.selectById(reqVO.getRedInvoiceId());
        if (invoiceApplyDO.getInvoiceDirection() != InvoiceDirctionEnum.BLUE.type.shortValue()){
            throw new BizException("5001", "当前发票不是蓝字，不能红冲");
        }

//        if (reqVO.getInvoiceTotalTax().compareTo(invoiceApplyDO.getInvoiceTotalTax()) > 0){
//            throw new BizException("5001", "红冲金额不能大于可红冲总金额");
//        }
        // 插入
        InvoiceIssueDO issue = BeanUtilX.copy(reqVO, InvoiceIssueDO::new);
        long invoiceId = IdWorker.getId();
        issue.setInvoiceId(invoiceId);
        issue.setInvoiceApplyId(reqVO.getInvoiceApplyId());

        issue.setBillingDirection(invoiceApplyDO.getBillingDirection());
        issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.NOT_INVOICE.type.shortValue());
        issue.setInvoiceDirection(InvoiceDirctionEnum.RED.type.shortValue());
        issue.setCurrencyDictId(invoiceApplyDO.getCurrencyDictId());
        issue.setInvoiceTypeDictId(invoiceApplyDO.getInvoiceTypeDictId());
        issue.setInvoiceTypeDictName(invoiceApplyDO.getInvoiceTypeDictName());
        issue.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey().shortValue());
        issue.setInvoiceTypeDictId(invoiceApplyDO.getInvoiceTypeDictId());
        issue.setCurrencyDictId(invoiceApplyDO.getCurrencyDictId());
        issue.setSourceFormType(invoiceApplyDO.getSourceFormType());

        //查询上级
        InvoiceIssueDO issueDO = issueMapper.selectById(reqVO.getRedInvoiceId());
        issue.setInvoiceStrategy(issueDO.getInvoiceStrategy());
        issue.setCollectionPlanStrategy(issueDO.getCollectionPlanStrategy());

        issue.setRedInvoiceId(reqVO.getRedInvoiceId());
        issue.setRelatedOrderCode(invoiceApplyDO.getInvoiceCode());
        issueMapper.insert(issue);

        //新增明细
        List<Long> applyDetailId = new ArrayList<>();
        reqVO.getDetailList().forEach(issueDetail -> {
            applyDetailId.add(issueDetail.getInvoiceDetailId());
        });
        List<InvoiceDetailDO> applyDetailList = invoiceDetailMapper.selectBatchIds(applyDetailId);
        if (CollectionUtils.isEmpty(applyDetailList)){
            throw new BizException("5001", "开票明细id不存在");
        }
        Map<Long, InvoiceDetailDO> applyDetailMap = applyDetailList.stream().collect(Collectors.toMap(InvoiceDetailDO::getInvoiceDetailId, Function.identity(), (s1, s2) -> s1));

        List<InvoiceDetailDO> list = new ArrayList<>();
        for (IssueDetail issueDetail : reqVO.getDetailList()){
            InvoiceDetailDO detailDO = applyDetailMap.get(issueDetail.getInvoiceDetailId());
            if (Objects.isNull(detailDO)){
                throw new BizException("5001", "原开票明细id异常");
            }
            if (issueDetail.getInvoiceAmtInclTax().abs().compareTo(detailDO.getInvoiceAmtInclTax().abs()) > 0){
                throw new BizException("5001", "红冲金额不能大于可红冲金额");
            }

            InvoiceDetailDO invoiceDetailDO = BeanUtilX.copy(detailDO, InvoiceDetailDO::new);
            long invoiceDetailId = IdWorker.getId();
            invoiceDetailDO.setInvoiceDetailId(invoiceDetailId);
            invoiceDetailDO.setInvoiceId(invoiceId);
            //红冲的上级id是蓝字详情的id
            invoiceDetailDO.setInvoiceApplyDetailId(detailDO.getInvoiceDetailId());
            invoiceDetailDO.setQty(issueDetail.getInvoiceQty());
            //因为上游单已经审核通过了，所以剩余的等于上游本次开票金额
            invoiceDetailDO.setRemainingInvoiceAmt(detailDO.getInvoiceAmtInclTax());
            invoiceDetailDO.setRemainingInvoiceQty(detailDO.getInvoiceQty());
            invoiceDetailDO.setApplyInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());
            invoiceDetailDO.setApplyInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
            invoiceDetailDO.setInvoiceQty(issueDetail.getInvoiceQty());
            invoiceDetailDO.setInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
            invoiceDetailDO.setInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());

            invoiceDetailDO.setTaxAmt(issueDetail.getInvoiceAmtInclTax().subtract(issueDetail.getInvoiceAmtExclTax()));
            invoiceDetailDO.setRowNo(issueDetail.getRowNo());
            invoiceDetailDO.setInvoiceCode(reqVO.getInvoiceCode());
            list.add(invoiceDetailDO);
            //invoiceDetailDO.setin
        }
        invoiceDetailMapper.insertBatch(list);

        this.invoiceDetailListAdd(reqVO.getDetailMsgList(),issue.getInvoiceId());
        // 返回
        return issue.getInvoiceId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long invoiceIssueEdit(InvoiceIssueAditReqVO reqVO) {
        //发票代码验重
        InvoiceIssueDO invoiceIssueCode = issueMapper.selectOne(new LambdaQueryWrapper<InvoiceIssueDO>()
                .eq(InvoiceIssueDO::getInvoiceCode, reqVO.getInvoiceCode())
                .last("limit 1")
        );
        if (Objects.nonNull(invoiceIssueCode) && !invoiceIssueCode.getInvoiceId().equals(reqVO.getInvoiceId())){
            throw new BizException("5001", "发票代码已存在");
        }
        // 校验存在
        InvoiceIssueDO invoiceIssueDO = this.invoiceIssueValidateExists(reqVO.getInvoiceId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(invoiceIssueDO, reqVO);

        //查询开票申请信息
        //查询开票申请信息

        BigDecimal canAmt = BigDecimal.ZERO;
        if (reqVO.getInvoiceApplyId() != null) {
            InvoiceApplyDO invoiceApplyDO = invoiceApplyMapper.selectById(invoiceIssueDO.getInvoiceApplyId());
            canAmt = invoiceApplyDO.getCanAmt();
        }else {
            InvoicePlanDO invoicePlanDO = invoicePlanMapper.selectById(reqVO.getInvoicePlanId());
            canAmt = invoicePlanDO.getCanInvoiceAmt();
        }


        // 更新
        InvoiceIssueDO issue = BeanUtilX.copy(reqVO, InvoiceIssueDO::new);

        if (reqVO.getInvoiceTotalTax().compareTo(BigDecimal.ZERO) == 0){
            throw new BizException("5001", "开票金额总额不能为0");
        }

        //不是红冲的时候校验，红冲的则在新增那里校验
        if (invoiceIssueDO.getRedInvoiceId() == null) {
            if (reqVO.getInvoiceTotalTax().abs().compareTo(canAmt.abs()) > 0){
                throw new BizException("5001", "开票金额总额大于剩余可开票金额");
            }
        }

        //非红冲发票处理
        if (invoiceIssueDO.getRedInvoiceId() == null) {
            if (issue.getInvoiceTotalTax().compareTo(BigDecimal.ZERO) > 0){
                issue.setInvoiceDirection(InvoiceDirctionEnum.BLUE.type.shortValue());
            }else {
                issue.setInvoiceDirection(InvoiceDirctionEnum.RED.type.shortValue());
            }
        }


        issueMapper.updateById(issue);

        //更新明细
        if (CollectionUtils.isNotEmpty(reqVO.getDetailList())){
            List<Long> invoiceDetailId = new ArrayList<>();
            reqVO.getDetailList().forEach(issueDetail -> {
                invoiceDetailId.add(issueDetail.getInvoiceDetailId());
            });
            List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectBatchIds(invoiceDetailId);
            if (CollectionUtils.isEmpty(invoiceDetailDOS)){
                throw new BizException("5001", "开票明细id不存在");
            }
            Map<Long, InvoiceDetailDO> invoiceDetailMap = invoiceDetailDOS.stream().collect(Collectors.toMap(InvoiceDetailDO::getInvoiceDetailId, Function.identity(), (s1, s2) -> s1));
            List<InvoiceDetailDO> updateList = new ArrayList<>();

            for (IssueDetail issueDetail : reqVO.getDetailList()){
                InvoiceDetailDO detailDO = invoiceDetailMap.get(issueDetail.getInvoiceDetailId());
                if (Objects.isNull(detailDO)) {
                    throw new BizException("5001", "开票明细id异常");
                }
//                if (issueDetail.getInvoiceQty().compareTo(detailDO.getRemainingInvoiceQty()) > 0){
//                    log.info("开票数量大于剩余开票数量");
//                    throw new BizException("5001", "开票数量大于剩余开票数量");
//                }
                if (issueDetail.getInvoiceAmtInclTax().compareTo(BigDecimal.ZERO) == 0){
                    log.info("开票金额不能为0");
                    throw new BizException("5001", "开票金额不能为0");
                }
                if (issueDetail.getInvoiceAmtInclTax().abs().compareTo(detailDO.getRemainingInvoiceAmt().abs()) > 0){
                    log.info("开票金额大于剩余开票金额");
                    throw new BizException("5001", "开票金额大于剩余开票金额");
                }
                detailDO.setInvoiceQty(issueDetail.getInvoiceQty());
                detailDO.setInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());
                detailDO.setInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
                detailDO.setTaxAmt(issueDetail.getTaxAmt());
                updateList.add(detailDO);
            }
            List<InvoiceDetailListDO> detailListDOS =  BeanUtilX.copy(reqVO.getDetailMsgList(), InvoiceDetailListDO::new);
//            for (InvoiceDetailListRespVO invoiceDetailListRespVO : reqVO.getDetailMsgList()){
//                if (invoiceDetailListRespVO.getInvoiceDetailId().equals(detailDO.getInvoiceDetailId())){
//                    invoiceDetailListDO.setInvoiceAmtInclTax(issueDetail.getInvoiceAmtInclTax());
//                    invoiceDetailListDO.setInvoiceAmtExclTax(issueDetail.getInvoiceAmtExclTax());
//                    invoiceDetailListDO.setInvoiceDetailListId(invoiceDetailListRespVO.getInvoiceDetailListId());
//                    detailListDOS.add(invoiceDetailListDO);
//                }
//
//            }
            if (CollectionUtils.isNotEmpty(updateList)){
                invoiceDetailMapper.updateBatch(updateList);
                invoiceDetailListMapper.updateBatch(detailListDOS);
            }

        }
        // 返回
        return issue.getInvoiceId();
    }

    @Override
    public void invoiceIssueDel(Long invoiceId) {
        // 校验存在
        InvoiceIssueDO invoiceIssueDO = this.invoiceIssueValidateExists(invoiceId);
        if (invoiceIssueDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey().shortValue()){
            throw new BizException("5001", "当前状态不是未审核");
        }
        // 删除
        issueMapper.deleteById(invoiceId);
        invoiceDetailMapper.delete(new LambdaQueryWrapper<InvoiceDetailDO>().eq(InvoiceDetailDO::getInvoiceId, invoiceId));
        invoiceDetailListMapper.delete(new LambdaQueryWrapper<InvoiceDetailListDO>().eq(InvoiceDetailListDO::getInvoiceId, invoiceId));
    }

    @Override
    public ResultX<BatchResult> invoiceIssueDelBatch(IdReq idReq) {
        //获取对象属性名
        String id = EntityUtilX.getPropertyName(InvoiceIssueDO:: getInvoiceId);
        String code = EntityUtilX.getPropertyName(InvoiceIssueDO::getInvoiceCode);
        return erpBaseService.batchDelete(idReq.getIdList(), InvoiceIssueDO.class, InvoiceDetailDO.class, id, code);

    }

    private InvoiceIssueDO invoiceIssueValidateExists(Long invoiceId) {
        InvoiceIssueDO issue = issueMapper.selectById(invoiceId);
        if (issue == null) {
            // throw exception(ISSUE_NOT_EXISTS);
            throw new BizException("5001", "实开发票不存在");
        }
        return issue;
    }

    @Override
    public InvoiceIssueRespVO invoiceIssueDetail(Long invoiceId) {
        InvoiceIssueDO data = issueMapper.selectById(invoiceId);
        InvoiceIssueRespVO invoiceIssueRespVO = BeanUtilX.copy(data, InvoiceIssueRespVO::new);
        fillVoProperties(invoiceIssueRespVO);
        //查询开票明细
        List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>().eq(InvoiceDetailDO::getInvoiceId, invoiceId));
        List<InvoiceDetailBaseVO> invoiceDetailList = new ArrayList<>();
        invoiceDetailDOS.forEach(detailDO->{
            InvoiceDetailBaseVO detailBaseVO = BeanUtilX.copy(detailDO, InvoiceDetailBaseVO::new);
            detailBaseVO.setRemainingRedInvoiceAmt(detailBaseVO.getRemainingInvoiceAmt());
            detailBaseVO.setRemainingRedInvoiceQty(detailBaseVO.getRemainingInvoiceQty());
            invoiceDetailList.add(detailBaseVO);
        });
        detailBatchFillVoProperties(invoiceDetailList);
        invoiceIssueRespVO.setDetailList(invoiceDetailList);

        //查询发票明细
        List<InvoiceDetailMsgResp> detailMsgList = invoiceDetailListMapper.invoiceDetailStatics(invoiceId);

        //判断是否是红冲数据，是的话，数据取反
        int red = 0; //0否 1是
        if (data.getRedInvoiceId() != null){
            red = 1;
        }
        detailMsgBatchFillVoProperties(detailMsgList, red);
        invoiceIssueRespVO.setDetailMsgList(detailMsgList);

        //查询关联红冲发票
        List<InvoiceIssueDO> invoiceIssueDOS = issueMapper.selectList(new LambdaQueryWrapper<InvoiceIssueDO>()
                .eq(InvoiceIssueDO::getRedInvoiceId, invoiceId)
                .eq(InvoiceIssueDO::getDataStatus, DataStatusEnum.APPROVED.getKey().shortValue())
        );
        List<InvoiceIssueRespVO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(invoiceIssueDOS)) {
            invoiceIssueDOS.forEach(invoiceIssueDO -> {
                list.add(BeanUtilX.copy(invoiceIssueDO, InvoiceIssueRespVO :: new));
            });
        }
        batchFillVoProperties(list);
        invoiceIssueRespVO.setRedDetailList(list);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(invoiceId.toString())).ifPresent(approveTask -> invoiceIssueRespVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return invoiceIssueRespVO;
    }

    @Override
    public List<InvoiceIssueRespVO> invoiceIssueList(InvoiceIssueQueryReqVO reqVO) {
        List<InvoiceIssueDO> data = issueMapper.selectList(reqVO);
        return BeanUtilX.copy(data, InvoiceIssueRespVO::new);
    }

    @Override
    public PageResult<InvoiceIssueRespVO> invoiceIssuePage(InvoiceIssuePageReqVO reqVO) {
        PageResult<InvoiceIssueDO> data = issueMapper.selectPage(reqVO);
        if (CollectionUtils.isNotEmpty(data.getList())) {
            List<InvoiceIssueRespVO> list = data.getList().stream().map((item) -> {
                InvoiceIssueRespVO resp = BeanUtilX.copy(item, InvoiceIssueRespVO::new);

                return resp;
            }).collect(Collectors.toList());
            batchFillVoProperties(list);
            return PageResult.init(data, list);
        }else {
            return BeanUtilX.copy(data, InvoiceIssueRespVO::new);
        }
    }

    @Override
    public BatchResult invoiceIssueApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<InvoiceIssueDO> list =issueMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (InvoiceIssueDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus().intValue());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理（使用FlowApproveHandler的方法签名）
                FailItem failItem = invoiceIssueApproveHandler.process(item, flowApproveBO);
                if (ObjUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                exception.printStackTrace();
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getInvoiceCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (InvoiceIssueDO item : list) {
                String reason = reasonMap.get(item.getInvoiceCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInvoiceId());
                    messageInfoBO.setObjCode(item.getInvoiceCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInvoiceId());
                    messageInfoBO.setObjCode(item.getInvoiceCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object invoiceIssueFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        InvoiceIssueDO item = this.invoiceIssueValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return invoiceIssueFlowCallBackHandler.handleFlowCallback(item, flowCallbackBO);
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(InvoiceIssueRespVO respVO) {
        //查询客户名称
//        String customerName = compositeService.getERPCustomerNameById(respVO.getCustomerId());
//        respVO.setCustomerName(customerName);

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), SystemDictEnum.ACT_OPEN_INVOICE_ISSUE_STATUS.getDictCode(),
                SystemDictEnum.ACT_RECEIPT_INVOICE_ISSUE_STATUS.getDictCode());
        Map<String, String> dictMap = compositeService.dictMap(dictCodeList);

        //查询负责人
        String directorName = compositeService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);

        //查询责任部门
        String directorOrgName = compositeService.getOrgNameById(respVO.getDirectorOrgId().toString());
        respVO.setDirectorOrgName(directorOrgName);

        // 币种
        if(StrUtilX.isNotEmpty(respVO.getCurrencyDictId())){
            String currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + respVO.getCurrencyDictId();
            respVO.setCurrencyDictName(dictMap.get(currencyDictId));
        }

        // 本币币种
        String localCurrencyDictId = respVO.getLocalCurrencyDictId();
        if(StrUtilX.isNotEmpty(localCurrencyDictId)){
            localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
            respVO.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
        }

        // 发票状态
        if(respVO.getInvoiceIssueStatus() != null){
            // 发票状态
            if(respVO.getInvoiceIssueStatus() != null){
                if(respVO.getBillingDirection() == 0){
                    String invoiceIssueStatus = SystemDictEnum.ACT_OPEN_INVOICE_ISSUE_STATUS.getDictCode() + "-" + respVO.getInvoiceIssueStatus();
                    respVO.setInvoiceIssueStatusDictName(dictMap.get(invoiceIssueStatus));
                }

                if(respVO.getBillingDirection() == 1){
                    String invoiceIssueStatus = SystemDictEnum.ACT_RECEIPT_INVOICE_ISSUE_STATUS.getDictCode() + "-" + respVO.getInvoiceIssueStatus();
                    respVO.setInvoiceIssueStatusDictName(dictMap.get(invoiceIssueStatus));
                }
            }
        }
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respList
     */
    private void batchFillVoProperties(List<InvoiceIssueRespVO> respList) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), SystemDictEnum.ACT_OPEN_INVOICE_ISSUE_STATUS.getDictCode(),
                SystemDictEnum.ACT_RECEIPT_INVOICE_ISSUE_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = compositeService.dictMap(dictCodeList);

        //查询责任部门
        Map<String, String> orgNameMap = compositeService.getOrgNameMap();

        Map<Long, String> empNameMap = new HashMap<>();
        Map<Long, String> customerNameMap = new HashMap<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> empIdList = new ArrayList<>();
//        List<Long> customerIdList = new ArrayList<>();
        for (InvoiceIssueRespVO deatilResp: respList){
            empIdList.add(deatilResp.getDirectorId());
            //customerIdList.add(deatilResp.getCustomerId());
        }

        //查询客户名称
        //customerNameMap = compositeService.getERPCustomerNameByIdList(customerIdList);

        //查询负责人
        empNameMap = compositeService.getEmpNameByIdList(empIdList);

        for (InvoiceIssueRespVO deatilResp: respList){

            //客户名称
           // deatilResp.setCustomerName(customerNameMap.get(deatilResp.getCustomerId()));

            //责任人属性填充
            deatilResp.setDirectorName(empNameMap.get(deatilResp.getDirectorId()));

            //责任部门属性填充
            deatilResp.setDirectorOrgName(orgNameMap.get(deatilResp.getDirectorOrgId().toString()));

            // 币种
            if(StrUtilX.isNotEmpty(deatilResp.getCurrencyDictId())){
                String currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + deatilResp.getCurrencyDictId();
                deatilResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = deatilResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                deatilResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 发票状态
            if(deatilResp.getInvoiceIssueStatus() != null){
                if(deatilResp.getBillingDirection() == 0){
                    String invoiceIssueStatus = SystemDictEnum.ACT_OPEN_INVOICE_ISSUE_STATUS.getDictCode() + "-" + deatilResp.getInvoiceIssueStatus();
                    deatilResp.setInvoiceIssueStatusDictName(dictMap.get(invoiceIssueStatus));
                }

                if(deatilResp.getBillingDirection() == 1){
                    String invoiceIssueStatus = SystemDictEnum.ACT_RECEIPT_INVOICE_ISSUE_STATUS.getDictCode() + "-" + deatilResp.getInvoiceIssueStatus();
                    deatilResp.setInvoiceIssueStatusDictName(dictMap.get(invoiceIssueStatus));
                }
            }

            //审核状态
            if(deatilResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + deatilResp.getDataStatus();
                deatilResp.setDataStatusDictName(dictMap.get(dataStatus));
            }
        }
    }

    private void detailBatchFillVoProperties(List<InvoiceDetailBaseVO> respList) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }


        //List<Long> empIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        for (InvoiceDetailBaseVO deatilResp: respList){
            //empIdList.add(deatilResp.getDirectorId());
            materialIdList.add(deatilResp.getMaterialId());
        }
        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }
        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        for (InvoiceDetailBaseVO respVO: respList){

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(respVO.getMaterialId());
            if (erpMaterialDO!=null){
                respVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                respVO.setMaterialName(erpMaterialDO.getMaterialName());
                respVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                respVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                respVO.setSpecModel(erpMaterialDO.getSpecModel());
                respVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
            respVO.setMainUnitDictName(dictMap.get(respVO.getMainUnitDictId()));
        }
    }

    private void detailMsgBatchFillVoProperties(List<InvoiceDetailMsgResp> respList, int red) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        for (InvoiceDetailMsgResp deatilResp: respList){
            materialIdList.add(deatilResp.getMaterialId());
            invoiceTypeIdList.add(deatilResp.getInvoiceTypeDictId());
        }
        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }

        Long i = 0L;
        for (InvoiceDetailMsgResp respVO: respList){
            i ++;
            respVO.setRowNo(i);

            if (red == 1){
                //红冲发票，金额取反
                respVO.setInvoiceAmtInclTax(respVO.getInvoiceAmtInclTax().negate());
                respVO.setInvoiceAmtExclTax(respVO.getInvoiceAmtExclTax().negate());
            }
            respVO.setTaxAmt(respVO.getInvoiceAmtInclTax().subtract(respVO.getInvoiceAmtExclTax()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(respVO.getMaterialId());
            if (erpMaterialDO!=null){
                respVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                respVO.setMaterialName(erpMaterialDO.getMaterialName());
                respVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                respVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                respVO.setSpecModel(erpMaterialDO.getSpecModel());
                respVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }

            respVO.setMainUnitDictName(dictMap.getOrDefault(respVO.getMainUnitDictId(), ""));
            if (respVO.getInvoiceTypeDictId()!=null){
                //票据类型
                respVO.setInvoiceTypeDictName(invTypeMap.get(respVO.getInvoiceTypeDictId()));
            }
        }
    }

    private void invoiceDetailListAdd (List<InvoiceDetailListRespVO> detailList,Long invoiceId){
        for (InvoiceDetailListRespVO item : detailList) {
            item.setInvoiceId(invoiceId);
        }
        List<InvoiceDetailListDO> listDOList = BeanUtilX.copy(detailList, InvoiceDetailListDO::new);
        invoiceDetailListMapper.insertBatch(listDOList);
    }
}
