package com.mongoso.mgs.module.purchase.controller.admin.deduction.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 采购扣费单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PurchaseDeductionBaseVO implements Serializable {

    /** 采购扣费单ID */
    private Long purchaseDeductionId;

    /** 采购扣费单号 */
    private String purchaseDeductionCode;

    /** 采购订单ID */
    private Long purchaseOrderId;

    /** 采购订单号 */
    private String purchaseOrderCode;

    /** 采购订单类型字典ID */
    private String purchaseTypeDictId;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate deliveryDate;

    /** 币种 */
    private String currencyDictId;

    /** 主体公司 */
    private String companyOrgId;

    /** 退款条件 */
    private String refundConditionDictId;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 扣费总金额(不含税) */
    private BigDecimal exclTaxTotalAmt;

    /** 扣费总金额(含税) */
    private BigDecimal inclTaxTotalAmt;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币扣费总金额(含税) */
    private BigDecimal inclTaxLocalCurrencyTotalAmt;
}
