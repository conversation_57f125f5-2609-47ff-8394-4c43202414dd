package com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDate;
import java.math.BigDecimal;
import java.util.List;


/**
 * 生产订单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ErpProdOrderQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long prodOrderId;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 生产订单类型 */
    private String prodOrderTypeDictId;

    /** 生产订单业务类型 */
    private Integer prodOrderBizType;
    private List<Integer> prodOrderBizTypeList;

    /** 关联单据id */
    private Long relatedOrderId;
    private List<Long> relatedOrderIdList;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanStartDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanEndDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanEndDate;

    /** 生产流程配置['工单工序','工序'] */
    private Integer processConfig;

    /** 生产计划总数 */
    private BigDecimal prodPlanTotalQty;

    /** 工单计划总数 */
    private BigDecimal workPlanTotalQty;

    /** 实际生产总数 */
    private BigDecimal prodActTotalQty;

    /** 审核状态 */
    private Integer dataStatus;

    /** 单据状态 */
    private Integer formStatus;
    private List<Integer> formStatusList;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    //生产订单变更单id
    private Long prodOrderChangeId;

    /** 是否全部已采购 */
    private Integer isFullPurchased;

    /**
     * 客户ID
     */
    private Long customerId;


    /**
     * 下发生产工单
     */
    private Short isIssueProdWork;

    /**
     * 下发委外采购订单
     */
    private Short isIssueOutsource;
}
