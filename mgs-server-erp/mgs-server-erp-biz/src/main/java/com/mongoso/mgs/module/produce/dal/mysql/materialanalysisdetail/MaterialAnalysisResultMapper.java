package com.mongoso.mgs.module.produce.dal.mysql.materialanalysisdetail;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisRespVO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysisdetail.MaterialAnalysisResultDO;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料分析生产物料 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialAnalysisResultMapper extends BaseMapperX<MaterialAnalysisResultDO> {

    default List<MaterialAnalysisResultDO> selectByMaterialAnalysisId(Long materialAnalysisId){
        return selectList(LambdaQueryWrapperX.<MaterialAnalysisResultDO>lambdaQueryX()
                .eq(MaterialAnalysisResultDO::getMaterialAnalysisId, materialAnalysisId));
    }

    default int deleteByMaterialAnalysisId(Long materialAnalysisId){
        return delete(LambdaQueryWrapperX.<MaterialAnalysisResultDO>lambdaQueryX()
                .eq(MaterialAnalysisResultDO::getMaterialAnalysisId, materialAnalysisId));
    }


    List<StockBookDetailRespVO> queryAnalysisResultListByBook(@Param("materialAnalysisId") Long materialAnalysisId);

}