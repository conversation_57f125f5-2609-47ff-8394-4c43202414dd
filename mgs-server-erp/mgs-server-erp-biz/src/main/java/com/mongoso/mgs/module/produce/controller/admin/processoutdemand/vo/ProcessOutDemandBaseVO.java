package com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
 import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import java.math.BigDecimal;
 
/**
 * 工序委外需求清单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProcessOutDemandBaseVO implements Serializable {

    /** 主键ID */
    private Long processOutDemandId;

    /** 工序委外需求单号 */
    private String processOutDemandCode;

    /** 生产订单ID */
    private Long prodOrderId;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 生产工单ID */
    private Long prodWorkId;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 加工方式 */
    private Long processMethod;

    //是否最终工序['否','是']
    private Integer finalProcess;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planEndDate;

    /** 工单计划生产数量 */
    private BigDecimal workPlanTotalQty;

    /** 派工方式['工单工序派工','工序派工'] */
    private Integer dispatchMethod;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 委外数量 */
    private BigDecimal outDemandQty;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 采购状态 */
    private Integer formStatus;

    /** 已采购采购数量 */
    private BigDecimal purchasedQty;

    /** 可采购数量 */
    private BigDecimal purchaseableQty;

    /** 良品数量 */
    private BigDecimal okQty;

    /** 优先级 */
    private Integer priority;

    /** 用户id */
    private Long userId;

    /** 收货数量 */
    private BigDecimal receiptedQty;

    /** 计件方式 */
    private String pieceworkMethodDictId;

    /** 派工策略["手动派工","自动派工"] */
    private Integer dispatchStrategyConfig;

}
