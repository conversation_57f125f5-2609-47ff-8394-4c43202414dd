package com.mongoso.mgs.module.sale.service.salechang;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.sale.controller.admin.salechang.vo.*;
import com.mongoso.mgs.module.sale.dal.db.salechang.SaleChangDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 销售订单变更单 Service 接口
 *
 * <AUTHOR>
 */
public interface SaleChangService {

    /**
     * 创建销售订单变更单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long saleChangAdd(@Valid SaleChangAditReqVO reqVO);

    /**
     * 更新销售订单变更单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long saleChangEdit(@Valid SaleChangAditReqVO reqVO);

    /**
     * 删除销售订单变更单
     *
     * @param id 编号
     */
    void saleChangDel(Long id);

    /**
     * 删除销售订单变更单
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> saleChangDelBatch(IdReq reqVO);

    /**
     * 获得销售订单变更单信息
     *
     * @param saleChangId 编号
     * @return 销售订单变更单信息
     */
    SaleChangRespVO saleChangDetail(Long saleChangId);

    /**
     * 获得销售订单变更单列表
     *
     * @param reqVO 查询条件
     * @return 销售订单变更单列表
     */
    List<SaleChangDO> saleChangList(@Valid SaleChangQueryReqVO reqVO);

    /**
     * 获得销售订单变更单分页
     *
     * @param reqVO 查询条件
     * @return 销售订单变更单分页
     */
    PageResult<SaleChangDetailResp> saleChangPage(@Valid SaleChangPageReqVO reqVO);

    PageResult<SaleChangDetailResp> querySaleChangDetailPage(SaleChangPageReqVO reqVO);

    BatchResult saleChangApprove(FlowApprove reqVO);

    Object saleChangFlowCallback(FlowCallback reqVO);
}
