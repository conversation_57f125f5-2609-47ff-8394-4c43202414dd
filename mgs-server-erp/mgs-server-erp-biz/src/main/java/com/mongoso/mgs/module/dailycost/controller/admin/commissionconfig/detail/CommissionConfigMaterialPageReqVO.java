package com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.detail;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 提成规则关联物料 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CommissionConfigMaterialPageReqVO extends PageParam {

    /** 提成规则id */
    private Long commissionConfigId;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料类别字典Id */
    private String materialCategoryDictId;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 物料来源 */
    private Long materialSourceDictId;

    /** 物料类型 */
    private String materialTypeDictId;

    /** 提成比例 */
    private BigDecimal commissionRatio;

    /** 创建人ID */
    private Long createdId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 版本号 */
    private Integer version;

    /** 行号 */
    private Short rowNo;

}
