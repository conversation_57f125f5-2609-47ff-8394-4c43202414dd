package com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购退货单明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PurchaseReturnDetailBaseVO implements Serializable {

    /** 采购退货单明细ID */
    private Long purchaseReturnDetailId;

    /** 采购退货单ID */
    private Long purchaseReturnId;

    /** 采购退货单号 */
    private String purchaseReturnCode;

    /** 采购订单明细ID */
    private Long purchaseOrderDetailId;

    /** 行号 */
    private Integer rowNo;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 物料编码 */
    private String materialCode;

    /** 退货数量 */
    private BigDecimal returnQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 可退货出库数量 */
    private BigDecimal outboundableQty;

    /** 已出库数量 */
    private BigDecimal outboundedQty;

    /** 是否完成出库 */
    private Integer isMaterialFullOutbounded;

    /** 是否填报税价 **/
    private Integer includingTax;

    /** 币种字典ID */
    private String currencyDictId;
    private String currencyDictName;

    /** 本币币种字典ID */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币订单总金额 */
    private BigDecimal inclTaxLocalCurrencyTotalAmt;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;

}
