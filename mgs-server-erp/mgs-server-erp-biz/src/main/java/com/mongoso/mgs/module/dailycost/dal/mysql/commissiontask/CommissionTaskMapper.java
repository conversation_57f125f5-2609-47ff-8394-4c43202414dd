package com.mongoso.mgs.module.dailycost.dal.mysql.commissiontask;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.CommissionTaskPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.CommissionTaskQueryReqVO;
import com.mongoso.mgs.module.dailycost.dal.db.commissiontask.CommissionTaskDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 提成任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CommissionTaskMapper extends BaseMapperX<CommissionTaskDO> {

    default PageResult<CommissionTaskDO> selectPage(CommissionTaskPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<CommissionTaskDO>lambdaQueryX()
                .likeIfPresent(CommissionTaskDO::getCommissionTaskCode, reqVO.getCommissionTaskCode())
                .eqIfPresent(CommissionTaskDO::getSaleOrderId, reqVO.getSaleOrderId())
                .likeIfPresent(CommissionTaskDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .eqIfPresent(CommissionTaskDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(CommissionTaskDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(CommissionTaskDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(CommissionTaskDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(CommissionTaskDO::getQty, reqVO.getQty())
                .eqIfPresent(CommissionTaskDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(CommissionTaskDO::getCommissionRatio, reqVO.getCommissionRatio())
                .eqIfPresent(CommissionTaskDO::getCommissionAmt, reqVO.getCommissionAmt())
                .inIfPresent(CommissionTaskDO::getMaterialId, reqVO.getMaterialIdList())
                .inIfPresent(CommissionTaskDO::getCustomerId, reqVO.getCustomerIdList())
                .eqIfPresent(CommissionTaskDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(CommissionTaskDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(CommissionTaskDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(CommissionTaskDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(CommissionTaskDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(CommissionTaskDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(CommissionTaskDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(CommissionTaskDO::getCreatedDt));
    }




    default List<CommissionTaskDO> selectList(CommissionTaskQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<CommissionTaskDO>lambdaQueryX()
                .likeIfPresent(CommissionTaskDO::getCommissionTaskCode, reqVO.getCommissionTaskCode())
                .eqIfPresent(CommissionTaskDO::getSaleOrderId, reqVO.getSaleOrderId())
                .likeIfPresent(CommissionTaskDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .eqIfPresent(CommissionTaskDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(CommissionTaskDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(CommissionTaskDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(CommissionTaskDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(CommissionTaskDO::getQty, reqVO.getQty())
                .eqIfPresent(CommissionTaskDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(CommissionTaskDO::getCommissionRatio, reqVO.getCommissionRatio())
                .eqIfPresent(CommissionTaskDO::getCommissionAmt, reqVO.getCommissionAmt())
                .eqIfPresent(CommissionTaskDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(CommissionTaskDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(CommissionTaskDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(CommissionTaskDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(CommissionTaskDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(CommissionTaskDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(CommissionTaskDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(CommissionTaskDO::getCreatedDt));
    }


    default CommissionTaskDO selectOne(Long saleOrderId, Long materialId,Short dataStatus) {
        return selectOne(LambdaQueryWrapperX.<CommissionTaskDO>lambdaQueryX()
                .eq(CommissionTaskDO::getSaleOrderId, saleOrderId)
                .eq(CommissionTaskDO::getMaterialId, materialId)
                .eqIfPresent(CommissionTaskDO::getDataStatus, dataStatus));
    }

    default int deleteBysaleOrderId(Long saleOrderId){
        return delete(LambdaQueryWrapperX.<CommissionTaskDO>lambdaQueryX()
                .eq(CommissionTaskDO::getSaleOrderId,saleOrderId)
        );
    }


}