package com.mongoso.mgs.module.produce.dal.db.device;

import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 设备台账 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_device", autoResultMap = true)
//@KeySequence("erp.u_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long deviceId;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 设备编码 */
    private String deviceCode;

    /** 设备名称 */
    private String deviceName;

    /** 设备类型id */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceTypeDictId;

    /** 设备型号 */
    private String deviceModel;

    /** 设备简称 */
    private String deviceAbbr;

    /** 单位 */
    private String deviceUnit;

    /** 设备状态 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceStatusDictId;

    /** 使用状态 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String usageStatusDictId;

    /** 设备来源 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceSourceDictId;

    /** 设备等级 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceLevelDictId;

    /** 品牌 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String brandDictId;

    /** 生产厂家 */
    private String producer;

    /** 出厂编号 */
    private String factoryNo;

    /** 所属部门 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long deptId;

    /** 资产编码 */
    private String assetCode;

    /** 备注 */
    private String remark;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
//    private Integer version;

    /** 机台吨位 */
    private String machineTonnage;


}
