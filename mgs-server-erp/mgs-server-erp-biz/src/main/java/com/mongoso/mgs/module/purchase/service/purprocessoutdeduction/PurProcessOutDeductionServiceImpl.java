package com.mongoso.mgs.module.purchase.service.purprocessoutdeduction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail.vo.PurProcessOutDeductionDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail.vo.PurProcessOutDeductionDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail.vo.PurProcessOutDeductionDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutdeductiondetail.PurProcessOutDeductionDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutdeductiondetail.PurProcessOutDeductionDetailMapper;
import com.mongoso.mgs.module.purchase.handler.approve.PurProcessOutDeductionApproveHandler;
import com.mongoso.mgs.module.purchase.handler.flowcallback.PurProcessOutDeductionFlowCallBackHandler;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.InvTypeManageQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeduction.vo.*;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutdeduction.PurProcessOutDeductionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutdeduction.PurProcessOutDeductionMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_DELETE_NOT_APPROVED;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_EDIT_NOT_APPROVED;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 工序委外采购扣费单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurProcessOutDeductionServiceImpl implements PurProcessOutDeductionService {

    @Resource
    private PurProcessOutDeductionMapper purProcessOutDeductionMapper;

    @Resource
    private PurProcessOutDeductionDetailMapper purProcessOutDeductionDetailMapper;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private InvTypeManageMapper invTypeManageMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    @Lazy
    private PurProcessOutDeductionApproveHandler purProcessOutDeductionApproveHandler;

    @Resource
    private PurProcessOutDeductionFlowCallBackHandler purProcessOutDeductionFlowCallBackHandler;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long purProcessOutDeductionAdd(PurProcessOutDeductionAditReqVO reqVO) {

        //编号
        String code = seqService.getGenerateCode(reqVO.getProcessOutDeductionCode(), MenuEnum.PROCESS_OUTSOURCING_PURCHASE_DEDUCTION_ORDER.menuId);

        PurProcessOutDeductionDO purProcessOutDeduction = BeanUtilX.copy(reqVO, PurProcessOutDeductionDO::new);
        purProcessOutDeduction.setProcessOutDeductionId(IDUtilX.getId());
        purProcessOutDeduction.setProcessOutDeductionCode(code);
//        purProcessOutDeduction.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        List<PurProcessOutDeductionDetailDO> detailDOList = this.fillDetailList(reqVO, purProcessOutDeduction);

        // 插入
        purProcessOutDeductionMapper.insert(purProcessOutDeduction);
        purProcessOutDeductionDetailMapper.insertBatch(detailDOList);

        // 返回
        return purProcessOutDeduction.getProcessOutDeductionId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long purProcessOutDeductionEdit(PurProcessOutDeductionAditReqVO reqVO) {
        // 校验存在
        PurProcessOutDeductionDO purProcessOutDeductionDO = this.purProcessOutDeductionValidateExists(reqVO.getProcessOutDeductionId());
        //校验是否存在和版本号
        EntityUtilX.checkVersion(purProcessOutDeductionDO, reqVO);

        // 更新
        PurProcessOutDeductionDO deductionDO = BeanUtilX.copy(reqVO, PurProcessOutDeductionDO::new);
        List<PurProcessOutDeductionDetailDO> returnDetailDOList = this.fillDetailList(reqVO, deductionDO);

        purProcessOutDeductionMapper.updateById(deductionDO);
        if (CollUtilX.isNotEmpty(returnDetailDOList)) {
            purProcessOutDeductionDetailMapper.deleteByPurchaseId(reqVO.getProcessOutDeductionId());
            purProcessOutDeductionDetailMapper.insertBatch(returnDetailDOList);
        }

        // 返回
        return deductionDO.getProcessOutDeductionId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void purProcessOutDeductionDel(Long processOutDeductionId) {
        // 校验存在
        PurProcessOutDeductionDO deductionDO = this.purProcessOutDeductionValidateExists(processOutDeductionId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(deductionDO.getDataStatus())){
            throw exception(ORDER_DELETE_NOT_APPROVED);
        }

        // 删除
        purProcessOutDeductionMapper.deleteById(processOutDeductionId);
        purProcessOutDeductionDetailMapper.deleteByPurchaseId(processOutDeductionId);
    }

    private PurProcessOutDeductionDO purProcessOutDeductionValidateExists(Long processOutDeductionId) {
        PurProcessOutDeductionDO purProcessOutDeduction = purProcessOutDeductionMapper.selectById(processOutDeductionId);
        if (purProcessOutDeduction == null) {
            // throw exception(PUR_PROCESS_OUT_DEDUCTION_NOT_EXISTS);
            throw new BizException("5001", "工序委外采购扣费单不存在");
        }
        return purProcessOutDeduction;
    }

    @Override
    public PurProcessOutDeductionRespVO purProcessOutDeductionDetail(Long processOutDeductionId) {
        PurProcessOutDeductionDO data = purProcessOutDeductionMapper.selectById(processOutDeductionId);
        if (data == null) {
            throw new BizException("5001", "工序委外采购扣费单不存在");
        }
        PurProcessOutDeductionRespVO respVO = BeanUtilX.copy(data, PurProcessOutDeductionRespVO::new);

        this.fillVoProperties(Collections.singletonList(respVO));

        //产品明细详情处理
        PurProcessOutDeductionDetailQueryReqVO detailQuery = new PurProcessOutDeductionDetailQueryReqVO();
        detailQuery.setProcessOutDeductionId(respVO.getProcessOutDeductionId());
        List<PurProcessOutDeductionDetailDO> detailDOList = purProcessOutDeductionDetailMapper.selectListOld(detailQuery);

        List<PurProcessOutDeductionDetailRespVO> detailRespVOS = getDetailRespList(detailDOList);
        respVO.setDetailList(detailRespVOS);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(processOutDeductionId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));


        return respVO;
    }

    @Override
    public List<PurProcessOutDeductionRespVO> purProcessOutDeductionList(PurProcessOutDeductionQueryReqVO reqVO) {
        List<PurProcessOutDeductionDO> data = purProcessOutDeductionMapper.selectList(reqVO);
        List<PurProcessOutDeductionRespVO> respVOS = BeanUtilX.copy(data, PurProcessOutDeductionRespVO::new);

        //属性填充
        fillVoProperties(respVOS);

        return respVOS;
    }

    @Override
    public PageResult<PurProcessOutDeductionRespVO> purProcessOutDeductionPage(PurProcessOutDeductionPageReqVO reqVO) {
        PageResult<PurProcessOutDeductionDO> data = purProcessOutDeductionMapper.selectPage(reqVO);
        PageResult<PurProcessOutDeductionRespVO> pageResult = BeanUtilX.copy(data, PurProcessOutDeductionRespVO::new);

        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        fillVoProperties(pageResult.getList());

        return pageResult;
    }

    @Override
    public BatchResult purProcessOutDeductionApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PurProcessOutDeductionDO> list = purProcessOutDeductionMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PurProcessOutDeductionDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();


                //流程处理
                FailItem failItem = purProcessOutDeductionApproveHandler.process(item,flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                exception.printStackTrace();
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getProcessOutDeductionCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (PurProcessOutDeductionDO item : list) {
                String reason = reasonMap.get(item.getProcessOutDeductionCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProcessOutDeductionId());
                    messageInfoBO.setObjCode(item.getProcessOutDeductionCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProcessOutDeductionId());
                    messageInfoBO.setObjCode(item.getProcessOutDeductionCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object purProcessOutDeductionFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PurProcessOutDeductionDO item = this.purProcessOutDeductionValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return purProcessOutDeductionFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public PageResult<PurProcessOutDeductionDetailResp> queryProcessOutDeductionDetailPage(PurProcessOutDeductionPageReqVO reqVO) {
        //物料条件查询
        if (StrUtilX.isNotEmpty(reqVO.getMaterialCode()) || StrUtilX.isNotEmpty(reqVO.getMaterialName())
                || reqVO.getMaterialCategoryDictId()!=null || StrUtilX.isNotEmpty(reqVO.getSpecModel())){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialCode(reqVO.getMaterialCode());
            erpMaterialQuery.setMaterialName(reqVO.getMaterialName());
            erpMaterialQuery.setSpecModel(reqVO.getSpecModel());
            erpMaterialQuery.setMaterialCategoryDictId(reqVO.getMaterialCategoryDictId());
            List<Long> materialIdList = erpMaterialService.findMaterialIdList(erpMaterialQuery);
            reqVO.setMaterialIdList(materialIdList);
            if (CollUtilX.isEmpty(materialIdList)){
                return PageResult.empty();
            }
        }

        IPage<PurProcessOutDeductionDetailResp> respVOIPage = purProcessOutDeductionMapper.queryProcessOutDeductionDetailPage(PageUtilX.buildParam(reqVO),reqVO);
        PageResult<PurProcessOutDeductionDetailResp> pageResult = PageUtilX.buildResult(respVOIPage);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }

        //属性填充
        batchFillDetailProperties(pageResult.getList());

        return pageResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultX<BatchResult> purProcessOutDeductionDelBatch(IdReq reqVO) {
        String id = EntityUtilX.getPropertyName(PurProcessOutDeductionDO::getProcessOutDeductionId);
        String code = EntityUtilX.getPropertyName(PurProcessOutDeductionDO::getProcessOutDeductionCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), PurProcessOutDeductionDO.class, PurProcessOutDeductionDetailDO.class, id, code);

    }

    /**
     * VO属性填充-批量处理
     *
     * @param detailResps
     */
    private void batchFillDetailProperties(List<PurProcessOutDeductionDetailResp> detailResps) {

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();

        for(PurProcessOutDeductionDetailResp item : detailResps) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            orgIdList.add(item.getCompanyOrgId());
            supplierIdList.add(item.getRelatedSupplierId());

            materialIdList.add(item.getMaterialId());
            invoiceTypeIdList.add(item.getInvoiceTypeId());
        }

        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        //查询关联供应
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(),
                CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        for (PurProcessOutDeductionDetailResp detailResp: detailResps){
            //供应商
            detailResp.setRelatedSupplierName(supplierNameMap.get(detailResp.getRelatedSupplierId()));
            //公司主体
            detailResp.setCompanyOrgName(directorOrgMap.get(detailResp.getCompanyOrgId()));
            //责任部门
            detailResp.setDirectorOrgName(directorOrgMap.get(detailResp.getDirectorOrgId()));
            //责任人
            detailResp.setDirectorName(directorMap.get(detailResp.getDirectorId()));
            // 币种
            String currencyDictId = detailResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                detailResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = detailResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                detailResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = detailResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                detailResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 退款条件
            String refundConditionDictId = detailResp.getRefundConditionDictId();
            if(StrUtilX.isNotEmpty(refundConditionDictId)){
                refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
                detailResp.setRefundConditionDictName(dictMap.get(refundConditionDictId));
            }

            //票据类型
            detailResp.setInvoiceTypeName(invTypeMap.get(detailResp.getInvoiceTypeId()));

            // 审核状态
            if(detailResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + detailResp.getDataStatus();
                detailResp.setDataStatusDictName(dictMap.get(dataStatus));;
            }

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialDO!=null){
                detailResp.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
                detailResp.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detailResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailResp.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailResp.setMaterialTypeDictName(erpMaterialDO.getMaterialTypeDictName());
                detailResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detailResp.setSpecModel(erpMaterialDO.getSpecModel());
            }
        }

    }



    private List<PurProcessOutDeductionDetailDO> fillDetailList(PurProcessOutDeductionAditReqVO reqVO, PurProcessOutDeductionDO deductionDO) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        BigDecimal exclTotalAmt = BigDecimal.ZERO;
        List<PurProcessOutDeductionDetailDO> returnDetailList = new ArrayList<>();
        List<PurProcessOutDeductionDetailAditReqVO> detailList = reqVO.getDetailList();

        // 校验相同物料编码的可操作数量合并
        Map<String, BigDecimal> materialCodeReturnQtyMap = new HashMap<>();
        Map<String, BigDecimal> materialCodeOperableQtyMap = new HashMap<>();

        // 第一步：收集每个物料编码的退货数量和可操作数量
        for (PurProcessOutDeductionDetailAditReqVO detail : detailList) {
            //需要用物料编码+关联行号保证唯一
            String materialCode = detail.getMaterialCode()+"-"+detail.getRelatedRowNo();
            BigDecimal deductionQty = detail.getDeductionQty();
            BigDecimal operableQty = detail.getOperableQty() != null ? detail.getOperableQty() : deductionQty;

            // 累加相同物料编码的退货数量
            materialCodeReturnQtyMap.merge(materialCode, deductionQty, BigDecimal::add);

            // 记录相同物料编码的可操作数量（取第一次遇到的值）
            materialCodeOperableQtyMap.putIfAbsent(materialCode, operableQty);
        }

        // 第二步：校验每个物料编码的退货数量是否超过可操作数量
        for (Map.Entry<String, BigDecimal> entry : materialCodeReturnQtyMap.entrySet()) {
            String materialCode = entry.getKey();
            BigDecimal totalReturnQty = entry.getValue();
            BigDecimal operableQty = materialCodeOperableQtyMap.get(materialCode);

            if (operableQty != null && totalReturnQty.compareTo(operableQty) > 0) {
                throw new BizException("5002", "明细存在物料扣费数量超过可扣费数量，请重新编辑！");
            }
        }

        for (PurProcessOutDeductionDetailAditReqVO detail : detailList){
            BigDecimal unitPrice = BigDecimal.ZERO;
            BigDecimal amt = BigDecimal.ZERO;
            BigDecimal inclTaxPrice = BigDecimal.ZERO;
            BigDecimal inclTaxAmt = BigDecimal.ZERO;

            //以含税价价算未税单价,未税金额
            if (detail.getIncludingTax() != null && detail.getIncludingTax().equals(1)){
                //单价不含税
                unitPrice = MathUtilX.getUnitPrice(detail.getInclTaxUnitPrice(),detail.getTaxRate(), detail.getCalculatType());
                //不含税金额
                amt = MathUtilX.getAmt(unitPrice, detail.getDeductionQty());
                //含税单价
                inclTaxPrice = MathUtilX.stripTrailingZeros(detail.getInclTaxUnitPrice());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getDeductionQty());
            }else {
                //单价不含税
                unitPrice = MathUtilX.stripTrailingZeros(detail.getExclTaxUnitPrice());
                //不含税金额
                amt = MathUtilX.getAmt(detail.getExclTaxUnitPrice(), detail.getDeductionQty());
                //含税单价
                inclTaxPrice = MathUtilX.getInclTaxPrice(detail.getExclTaxUnitPrice(), detail.getTaxRate(), detail.getCalculatType());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getDeductionQty());
            }

            detail.setExclTaxUnitPrice(unitPrice);
            detail.setExclTaxAmt(amt);
            detail.setInclTaxUnitPrice(inclTaxPrice);
            detail.setInclTaxAmt(inclTaxAmt);
            detail.setProcessOutDeductionId(deductionDO.getProcessOutDeductionId());
            detail.setProcessOutDeductionCode(deductionDO.getProcessOutDeductionCode());

            //订单含税总金额
            totalAmt = totalAmt.add(inclTaxAmt);
            exclTotalAmt = exclTotalAmt.add(amt);

            PurProcessOutDeductionDetailDO returnDetailDO = BeanUtilX.copy(detail, PurProcessOutDeductionDetailDO::new);
            returnDetailList.add(returnDetailDO);
        }

        //重算含税金额
        deductionDO.setInclTaxTotalAmt(totalAmt);
        deductionDO.setExclTaxTotalAmt(exclTotalAmt);
        return returnDetailList;
    }

    /**
     * 产品明细详情处理
     *
     * @param detailDOList
     * @return
     */
    private List<PurProcessOutDeductionDetailRespVO> getDetailRespList(List<PurProcessOutDeductionDetailDO> detailDOList) {

        List<PurProcessOutDeductionDetailRespVO> detailRespVOS = BeanUtilX.copyList(detailDOList, PurProcessOutDeductionDetailRespVO::new);

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        List<Long> processIdList = new ArrayList<>();
        for (PurProcessOutDeductionDetailRespVO detailResp: detailRespVOS){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
            processIdList.add(detailResp.getProcessId());
        }

        //查询物料信息
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        //填充物料基本信息
        for (PurProcessOutDeductionDetailRespVO detailRespVO : detailRespVOS){
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailRespVO.getMaterialId());
            if (erpMaterialDO !=null){
                detailRespVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailRespVO.setMaterialName(erpMaterialDO.getMaterialName());
                detailRespVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailRespVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailRespVO.setSpecModel(erpMaterialDO.getSpecModel());
                detailRespVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }

            //基本单位
            if(StrUtilX.isNotEmpty(detailRespVO.getMainUnitDictId())){
                detailRespVO.setMainUnitDictName(dictMap.get(detailRespVO.getMainUnitDictId()));
            }

            //票据类型
            detailRespVO.setInvoiceTypeName(invTypeMap.get(detailRespVO.getInvoiceTypeId()));
        }

        return detailRespVOS;
    }

    private void fillVoProperties(List<PurProcessOutDeductionRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)){
            return;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        for(PurProcessOutDeductionRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            orgIdList.add(item.getCompanyOrgId());
            supplierIdList.add(item.getRelatedSupplierId());
            invoiceTypeIdList.add(item.getInvoiceTypeId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(), CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(),
                CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        //票据类型
        Map<Long, String> invTypeNameMap = erpBaseService.getInvTypeNameMap(invoiceTypeIdList);

        for (PurProcessOutDeductionRespVO returnResp: respVOList){
            //供应商
            returnResp.setRelatedSupplierName(supplierNameMap.get(returnResp.getRelatedSupplierId()));
            //公司主体
            returnResp.setCompanyOrgName(directorOrgMap.get(returnResp.getCompanyOrgId()));
            //责任部门
            returnResp.setDirectorOrgName(directorOrgMap.get(returnResp.getDirectorOrgId()));
            //责任人
            returnResp.setDirectorName(directorMap.get(returnResp.getDirectorId()));

            //票据类型
            returnResp.setInvoiceTypeName(invTypeNameMap.get(returnResp.getInvoiceTypeId()));

            // 币种
            String currencyDictId = returnResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                returnResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = returnResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                returnResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = returnResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                returnResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 退款条件
            String refundConditionDictId = returnResp.getRefundConditionDictId();
            if(StrUtilX.isNotEmpty(refundConditionDictId)){
                refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
                returnResp.setRefundConditionDictName(dictMap.get(refundConditionDictId));
            }

            // 审核状态
            if(returnResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + returnResp.getDataStatus();
                returnResp.setDataStatusDictName(dictMap.get(dataStatus));
            }
        }
    }

}
