package com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购收货通知单明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseReceiptNoticeDetailPageReqVO extends PageParam {

    /** 收货通知单号 */
    private String receiptNoticeCode;

    /** 采购订单类型 */
    private String purchaseTypeDictId;

    /** 采购订单号 */
    private String purchaseOrderCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 物料名称 */
    private String materialName;

    /** 物料类别 */
    private String materialCategoryDictId;

    /** 规格型号 */
    private String specModel;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 收货通知单ID */
    private Long receiptNoticeId;

    /** 行号 */
    private Integer rowNo;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 通知数量 */
    private BigDecimal noticeQty;

    /** 是否完成收货 */
    private Integer isMaterialFullReceipted;

    /** 备注 */
    private String remark;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
