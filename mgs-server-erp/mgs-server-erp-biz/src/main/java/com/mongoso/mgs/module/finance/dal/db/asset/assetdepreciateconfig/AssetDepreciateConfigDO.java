package com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciateconfig;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 折旧方法配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_asset_depreciate_config", autoResultMap = true)
//@KeySequence("erp.u_asset_depreciate_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetDepreciateConfigDO extends BaseDO {

    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long depreciateConfigId;

    /** 方法名称 */
    private String methodName;

    /** 公式 */
    private String formula;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 是否系统自动生成 */
    private Integer isSystemGenerate;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 单据状态 */
    private Short dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
//    private Integer version;


}
