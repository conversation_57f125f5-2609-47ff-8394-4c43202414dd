package com.mongoso.mgs.module.produce.service.materialbom;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.*;
import com.mongoso.mgs.module.produce.service.materialanalysis.bo.MaterialBomTreeBO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 物料BOM Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialBomNewService {

    /**
     * 查询BOM树
     * @param reqVO 请求对象
     * @return BOM树
     */
    List<MaterialBomTreeRespVO> materialBomTree(MaterialBomQueryReqVO reqVO);
}
