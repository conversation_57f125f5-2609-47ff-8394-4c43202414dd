package com.mongoso.mgs.module.payment.service.paymentrelation;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.*;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.payment.dal.mysql.paymentrelation.PaymentRelationMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.payment.enums.ErrorCodeConstants.*;


/**
 * 收款关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PaymentRelationServiceImpl implements PaymentRelationService {

    @Resource
    private PaymentRelationMapper relationMapper;

    @Override
    public Long paymentRelationAdd(PaymentRelationAditReqVO reqVO) {
        // 插入
        PaymentRelationDO relation = BeanUtilX.copy(reqVO, PaymentRelationDO::new);
        relationMapper.insert(relation);
        // 返回
        return relation.getId();
    }

    @Override
    public Long paymentRelationEdit(PaymentRelationAditReqVO reqVO) {
        // 校验存在
        this.paymentRelationValidateExists(reqVO.getId());
        // 更新
        PaymentRelationDO relation = BeanUtilX.copy(reqVO, PaymentRelationDO::new);
        relationMapper.updateById(relation);
        // 返回
        return relation.getId();
    }

    @Override
    public void paymentRelationDelete(Long id) {
        // 校验存在
        this.paymentRelationValidateExists(id);
        // 删除
        relationMapper.deleteById(id);
    }

    private PaymentRelationDO paymentRelationValidateExists(Long id) {
        PaymentRelationDO relation = relationMapper.selectById(id);
        if (relation == null) {
            // throw exception(RELATION_NOT_EXISTS);
            throw new BizException("5001", "收款关系不存在");
        }
        return relation;
    }

    @Override
    public PaymentRelationRespVO paymentRelationDetail(Long id) {
        PaymentRelationDO data = relationMapper.selectById(id);
        return BeanUtilX.copy(data, PaymentRelationRespVO::new);
    }

    @Override
    public List<PaymentRelationRespVO> paymentRelationList(PaymentRelationQueryReqVO reqVO) {
        List<PaymentRelationDO> data = relationMapper.selectList(reqVO);
        return BeanUtilX.copy(data, PaymentRelationRespVO::new);
    }

    @Override
    public PageResult<PaymentRelationRespVO> paymentRelationPage(PaymentRelationPageReqVO reqVO) {
        PageResult<PaymentRelationDO> data = relationMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, PaymentRelationRespVO::new);
    }

}
