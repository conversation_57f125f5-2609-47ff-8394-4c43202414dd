package com.mongoso.mgs.module.sale.dal.mysql.materialprice;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.sale.dal.db.cutomerrel.CutomerRelDO;
import com.mongoso.mgs.module.sale.dal.db.materialprice.MaterialPriceDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.sale.controller.admin.materialprice.vo.*;

/**
 * 商品价格 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialPriceMapper extends BaseMapperX<MaterialPriceDO> {

    default PageResult<MaterialPriceDO> selectPage(MaterialPricePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<MaterialPriceDO>lambdaQueryX()
                .betweenIfPresent(MaterialPriceDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MaterialPriceDO::getCreatedId, reqVO.getCreatedId())
                .eqIfPresent(MaterialPriceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MaterialPriceDO::getCustomerOrderPrice, reqVO.getCustomerOrderPrice())
                .eqIfPresent(MaterialPriceDO::getMaterialId, reqVO.getMaterialId())
                .orderByDesc(MaterialPriceDO::getMaterialPriceId));
    }




    default List<MaterialPriceDO> selectList(MaterialPriceQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<MaterialPriceDO>lambdaQueryX()
                .eqIfPresent(MaterialPriceDO::getMaterialPriceId, reqVO.getMaterialPriceId())
                .eqIfPresent(MaterialPriceDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(MaterialPriceDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MaterialPriceDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(MaterialPriceDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                .eqIfPresent(MaterialPriceDO::getCreatedId, reqVO.getCreatedId())
                .eqIfPresent(MaterialPriceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MaterialPriceDO::getCustomerOrderPrice, reqVO.getCustomerOrderPrice())
                .eqIfPresent(MaterialPriceDO::getMaterialId, reqVO.getMaterialId())
                .orderByDesc(MaterialPriceDO::getMaterialPriceId));
    }

    default int batchDelete(Long relatedOrderId){
        return delete(LambdaQueryWrapperX.<MaterialPriceDO>lambdaQueryX()
                .eq(MaterialPriceDO::getRelatedOrderId,relatedOrderId)
        );
    }


}