package com.mongoso.mgs.module.produce.controller.admin.devicemoldlog.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 设备模具关联 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class DeviceMoldLogQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long deviceMoldId;

    /** 上模记录编码 */
    private String deviceMoldCode;

    /** 工作中心ID */
    private Long workCenterId;

    /** 工作中心编码 */
    private String workCenterCode;

    /** 工作中心名称 */
    private String workCenterName;

    /** 设备主键ID */
    private Long deviceId;

    /** 设备编码 */
    private String deviceCode;

    /** 设备名称 */
    private String deviceName;

    /** 设备简称 */
    private String deviceAbbr;

    /** 模具主键ID */
    private Long moldId;

    /** 模具编码 */
    private String moldCode;

    /** 模具名称 */
    private String moldName;

    /** 操作类型 */
    private Integer opType;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 用户id */
    private Long userId;

}
