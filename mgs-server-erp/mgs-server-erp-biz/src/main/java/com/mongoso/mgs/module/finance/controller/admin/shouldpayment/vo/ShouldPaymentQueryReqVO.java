package com.mongoso.mgs.module.finance.controller.admin.shouldpayment.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 应收付账款 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ShouldPaymentQueryReqVO extends CommonParam{

    /** 应收款主键ID */
    private Long paymentId;
    private List<Long> paymentIdList;

    /** 应收账款状态 */
    private Short accountsPayableStatus;

    /** 单据类型 */
    @NotNull(message = "单据类型不能为空")
    @Min(value = 1, message = "单据类型值1：销售，2：采购")
    @Max(value = 2, message = "单据类型值1：销售，2：采购")
    private Short formType;

    /** 应收账款单号 */
    private String accountsPayableNumber;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单号 */
    private String sourceOrderNumber;
    private Long customerId;
    /** 供应商Id */
    private Long supplierId;

    /** 供应商名称 */
    private String supplierName;

    /** 币种名称 */
    private String currencyDictName;

    /** 币种id */
    private String currencyDictId;

    /** 剩余应收金额 */
    private BigDecimal amountPayable;

    /** 订单总金额 */
    private BigDecimal totalAmt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 应收日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startActRecDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endActRecDate;

    /** 计划应收日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startPlanRecDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endPlanRecDate;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 审批状态 */
    private Short dataStatus;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 应收账款状态 */
    private Integer accountsPayableStatusNe;

    /** 应收账款状态 */
    private List<Integer> exclAccountsPayableStatus;

    /** 源头单据id */
    private Long originOrderId;
}
