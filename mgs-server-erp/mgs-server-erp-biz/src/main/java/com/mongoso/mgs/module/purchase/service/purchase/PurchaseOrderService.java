package com.mongoso.mgs.module.purchase.service.purchase;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkMaterialDetailTreeReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkMaterialDetailTreeRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.*;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.PurchaseProcessOutPrimaryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.*;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailTreeReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailTreeRespVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;

import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDetailDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 采购订单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseOrderService {

    /**
     * 创建采购订单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseOrderAdd(@Valid PurchaseOrderAditReqVO reqVO);

    /**
     * 更新采购订单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseOrderEdit(@Valid PurchaseOrderAditReqVO reqVO);

    /**
     * 删除采购订单
     *
     * @param purchaseOrderId 编号
     */
    void purchaseOrderDel(Long purchaseOrderId);

    /**
     * 获得采购订单信息
     *
     * @param purchaseOrderId 编号
     * @return 采购订单信息
     */
    PurchaseOrderRespVO purchaseOrderDetail(Long purchaseOrderId);

    /**
     * 获得采购订单信息
     *
     * @param reqVO
     * @return 采购订单信息
     */
    PurchaseOrderRespVO purchaseOrderDetail(PurchaseOrderQueryReqVO reqVO);
    /**
     * 获得采购订单被引用信息
     *
     * @param purchaseOrderId 编号
     * @return 采购订单被引用信息
     */
    PurchaseOrderRespVO purchaseOrderQuotedDetail(Long purchaseOrderId);

    /**
     * 委外直接退料获得采购订单被引用信息
     *
     * @param purchaseOrderId 编号
     * @return 采购订单被引用信息
     */
    PurchaseOrderRespVO purchaseOrderDirectReturnQuotedDetail(Long purchaseOrderId);

    PurchaseOrderRespVO purchaseOrderDetailForChange(Long purchaseOrderId);

    /**
     * 获得采购订单列表
     *
     * @param reqVO 查询条件
     * @return 采购订单列表
     */
    List<PurchaseOrderRespVO> purchaseOrderList(@Valid PurchaseOrderQueryReqVO reqVO);

    /**
     * 获得采购订单分页
     *
     * @param reqVO 查询条件
     * @return 采购订单分页
     */
    PageResult<PurchaseOrderRespVO> purchaseOrderPage(@Valid PurchaseOrderPageReqVO reqVO);

    /**
     * 判断需求单是否有已审核的采购单
     * @param failItem
     * @param item
     * @return
     */
    public Boolean isHavePurchase(FailItem failItem, PurchaseDemandDO item);

    /**
     * 批量删除采购订单
     * @param reqVO
     * @return
     */
    ResultX<BatchResult> purchaseOrderDelBatch(IdReq reqVO);

    BatchResult erpPurchaseOrderApprove(PurchaseFlowApprove reqVO);

    Object erpPurchaseOrderFlowCallback(PurchaseFlowCallback reqVO);

    /**
     * 更新采购订单表的字段，是否全部退料，isFullPickingReturn
     * @param relatedOrderId
     */
    void refreshIsFullPickingReturn(Long relatedOrderId);

    /**
     * 采购订单强制关闭
     * @param reqVO
     */
    void erpPurchaseOrderForceClose(PurchaseOrderPrimaryReqVO reqVO);

    List<PurchaseOrderDO> forewarnJob(Integer dataStatus, List<Integer> formStatusList, List<Integer> statusList);

    void editChildrenOrderCount (Long purchaseOrderId, Integer dataStatus);

    /**
     * 销售订单修改单据状态
     *
     * @param reqVO
     * @return
     */
    void erpPurchaseOrderEditFormStatus(PurchaseOrderPrimaryReqVO reqVO);

    Map<Long, List<PurchaseOrderDetailDO>> forewarnJobByIdList(List<Long> idList);

    /**
     * 委外订单直接领料出库单
     *
     * @param reqVO
     * @return
     */
    List<PurchaseOrderDetailTreeRespVO> purchaseOrderMaterialDetailTree(PurchaseOrderDetailTreeReqVO reqVO);

    /**
     * 根据采购订单号判断是否可以修改是否带料
     */
    Integer getPurchaseIsApproveAble(Long purchaseOrderId);
}
