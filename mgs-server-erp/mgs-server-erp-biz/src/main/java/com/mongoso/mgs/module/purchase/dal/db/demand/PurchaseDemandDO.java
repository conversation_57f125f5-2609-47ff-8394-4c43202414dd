package com.mongoso.mgs.module.purchase.dal.db.demand;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 采购需求 DO
 *
 * <AUTHOR>
 */
@TableName(value = "u_purchase_demand", autoResultMap = true)
//@KeySequence("u_purchase_demand_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseDemandDO extends OperateDO {

    /** 采购需求ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long purchaseDemandId;

    /** 采购需求单号 */
    private String purchaseDemandCode;

    /**  采购需求主题 */
    private String purchaseDemandName;

    /** 采购需求单类型 */
    private String demandTypeDictId;

    /** 关联单据ID */
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 备注 */
    private String remark;

    /** 业务类型 */
    private Short purchaseDemandBizType;

    /** 是否可下发需求采购订单 */
    private Short isFullPurchased;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;


}
