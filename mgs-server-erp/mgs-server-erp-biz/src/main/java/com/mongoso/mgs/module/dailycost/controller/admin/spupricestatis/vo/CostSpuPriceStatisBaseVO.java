package com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 产品实际理论单价统计 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CostSpuPriceStatisBaseVO implements Serializable {

    /** 主键ID */
    private Long priceStatisId;

    /** 承担对象 */
    private String undertakeOrgId;

    /** 日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate statisDate;

//    /** SPUID */
//    private Long spuId;
//
//    /** SPU编码 */
//    private String spuCode;

    /** 承担物料ID */
    private Long undertakeMaterialId;

    /** 承担物料编码 */
    private String undertakeMaterialCode;
    private String undertakeMaterialName;

}
