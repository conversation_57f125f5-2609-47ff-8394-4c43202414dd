package com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.vo.*;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.service.invoice.invoiceissue.InvoiceIssueService;

/**
 * 实开发票 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/invoice")
@Validated
public class InvoiceIssueController {

    @Resource
    private InvoiceIssueService issueService;

    @OperateLog("实开发票添加或编辑")
    @PostMapping("/invoiceIssueAdit")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:adit')")
    public ResultX<Long> invoiceIssueAdit(@Valid @RequestBody InvoiceIssueAditReqVO reqVO) {
        return success(reqVO.getInvoiceId() == null
                            ? issueService.invoiceIssueAdd(reqVO)
                            : issueService.invoiceIssueEdit(reqVO));
    }

    @OperateLog("新增红冲")
    @PostMapping("/invoiceIssueRedAdd")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:adit')")
    public ResultX<Long> invoiceIssueRedAdd(@Valid @RequestBody InvoiceIssueAditReqVO reqVO) {
        return success(issueService.invoiceIssueRedAdd(reqVO));
    }

    @OperateLog("实开发票删除")
    @PostMapping("/invoiceIssueDel")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:delete')")
    public ResultX<Boolean> invoiceIssueDel(@Valid @RequestBody InvoiceIssuePrimaryReqVO reqVO) {
        issueService.invoiceIssueDel(reqVO.getInvoiceId());
        return success(true);
    }

    @OperateLog("开票主体配置删除(批量)")
    @PostMapping("/invoiceIssueDelBatch")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:delete')")
    public ResultX<BatchResult> invoiceIssueDelBatch(@Valid @RequestBody IdReq idReq) {

        return issueService.invoiceIssueDelBatch(idReq);
    }

    @OperateLog("实开发票详情")
    @PostMapping("/invoiceIssueDetail")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:query')")
    public ResultX<InvoiceIssueRespVO> invoiceIssueDetail(@Valid @RequestBody InvoiceIssuePrimaryReqVO reqVO) {
        return success(issueService.invoiceIssueDetail(reqVO.getInvoiceId()));
    }

    @OperateLog("实开发票列表")
    @PostMapping("/invoiceIssueList")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:query')")
    @DataPermission
    public ResultX<List<InvoiceIssueRespVO>> invoiceIssueList(@Valid @RequestBody InvoiceIssueQueryReqVO reqVO) {
        return success(issueService.invoiceIssueList(reqVO));
    }

    @OperateLog("实开发票分页")
    @PostMapping("/invoiceIssuePage")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:query')")
    @DataPermission
    public ResultX<PageResult<InvoiceIssueRespVO>> invoiceIssuePage(@Valid @RequestBody InvoiceIssuePageReqVO reqVO) {
        return success(issueService.invoiceIssuePage(reqVO));
    }

    @OperateLog("实开发票审核")
    @PostMapping("/invoiceIssueApprove")
    @PreAuthorize("@ss.hasPermission('invoiceIssue:adit')")
    public ResultX<BatchResult> invoiceIssueApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = issueService.invoiceIssueApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("实开发票回调接口")
    @PostMapping("/invoiceIssueFlowCallback")
    public ResultX<Object> invoiceIssueFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(issueService.invoiceIssueFlowCallback(reqVO));
    }

}
