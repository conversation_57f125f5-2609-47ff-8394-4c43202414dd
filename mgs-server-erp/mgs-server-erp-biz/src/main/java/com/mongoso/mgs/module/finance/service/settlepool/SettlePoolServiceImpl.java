package com.mongoso.mgs.module.finance.service.settlepool;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.controller.admin.erpsupplier.vo.ERPSupplierQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.erpcustomer.ERPCustomerDO;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpcustomer.ERPCustomerMapper;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.base.service.erpsupplier.ERPSupplierService;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolRespVO;
import com.mongoso.mgs.module.finance.dal.db.settlepool.SettlePoolDO;
import com.mongoso.mgs.module.finance.dal.mysql.settlepool.SettlePoolMapper;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.settle.enums.ErrorCodeConstants.*;


/**
 * 结算池 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SettlePoolServiceImpl implements SettlePoolService {

    @Resource
    private SettlePoolMapper poolMapper;

    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ERPSupplierService erpSupplierService;
    @Resource
    private ERPMaterialService erpMaterialService;
    @Resource
    private ERPCustomerMapper erpCustomerMapper;
    @Resource
    private ERPSupplierMapper erpSupplierMapper;
    @Resource
    private SeqService seqService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long settlePoolAdd(SettlePoolAditReqVO reqVO) {
        // 插入
        SettlePoolDO pool = addPool(reqVO);
        if (Objects.isNull(pool)){
            throw new BizException("5001", "来源单行号的结算池已生成");
        }
        poolMapper.insert(pool);
        // 返回
        return pool.getSettleId();
    }

    @Override
    public SettlePoolDO addPool(SettlePoolAditReqVO reqVO) {
        if(reqVO.getReversal()){
            //转为负
            BigDecimal minusOne = BigDecimal.valueOf(-1);
            reqVO.setSourceDocumentQty(reqVO.getSourceDocumentQty().multiply(minusOne));
            reqVO.setInclTaxAmt(reqVO.getInclTaxAmt().multiply(minusOne));
            reqVO.setExclTaxAmt(reqVO.getExclTaxAmt().multiply(minusOne));
        }
        // 如果存在，做更新操作
        SettlePoolDO exist = poolMapper.selectOne(LambdaQueryWrapperX.<SettlePoolDO>lambdaQueryX()
                .eq(SettlePoolDO::getSourceLineId, reqVO.getSourceLineId())
        );
        if (Objects.nonNull(exist)){
            //做更新 单价 行金额 来源数量 可对账金额，可对账数量
            exist.setInclTaxUnitPrice(reqVO.getInclTaxUnitPrice());
            exist.setExclTaxUnitPrice(reqVO.getExclTaxUnitPrice());
            exist.setExclTaxAmt(reqVO.getExclTaxAmt());
            exist.setInclTaxAmt(reqVO.getInclTaxAmt());
            exist.setSourceDocumentQty(reqVO.getSourceDocumentQty());
            exist.setReconcilableQty(reqVO.getSourceDocumentQty().subtract(exist.getReconciledQty()));
            exist.setReconcilableAmt(exist.getReconcilableQty().multiply(exist.getInclTaxUnitPrice()));
            return exist;
        }
        SettlePoolDO pool = BeanUtilX.copy(reqVO, SettlePoolDO::new);

        LocalDate now = LocalDate.now();
        String tableName = SeqEnum.salesettlepoolorder.getTableName();
        CodeGenUtil.CodeTypeEnums type = CodeGenUtil.CodeTypeEnums.SALE_SETTLEPOOL;
        if(reqVO.getFormType() == 2){//采购
            tableName = SeqEnum.purchasesettlepoolorder.getTableName();
            type = CodeGenUtil.CodeTypeEnums.PURCHASE_SETTLEPOOL;
        }
        Long seq = seqService.lockInsertLamb(tableName, now, null);
        String code = CodeGenUtil.generateCode(type, seq);

        pool.setSettleTransactionCode(code);
        return pool;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean settlePoolSave(List<SettlePoolAditReqVO> reqList) {
        if(ObjUtilX.isEmpty(reqList)){
            throw new BizException("5001", "请传入明细数据");
        }
        //查询是否有结算池
        List<SettlePoolDO> addPools = new LinkedList<>();
        List<SettlePoolDO> updPools = new ArrayList<>(reqList.size());
        List<Long> delPools = new ArrayList<>(reqList.size());
        for (SettlePoolAditReqVO reqVO : reqList) {

            SettlePoolDO pool = addPool(reqVO);
            if(Objects.nonNull(pool)) {
                if(Objects.nonNull(pool.getSettleId())){
                    updPools.add(pool);
                    if (reqVO.getReconcilableQty().compareTo(BigDecimal.ZERO) == 0){
                        delPools.add(pool.getSettleId());
                    }
                }else {
                    addPools.add(pool);
                }
            }
        }

        // 在插入之前按照 sourceLineNumber 属性正序排序 addPools
        addPools.sort(Comparator.comparing(SettlePoolDO::getSourceLineNumber));

        // 执行批量插入和更新操作
        if (!addPools.isEmpty()) {
            poolMapper.insertBatch(addPools); //结算池
        }
        if (!updPools.isEmpty()) {
            poolMapper.updateBatch(updPools);
        }
        // 执行删除的操作
        if (!delPools.isEmpty()){
            poolMapper.deleteBatchIds(delPools);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long settlePoolEdit(SettlePoolAditReqVO reqVO) {
        // 校验存在
        SettlePoolDO exists = this.settlePoolValidateExists(reqVO.getSettleId());
        // 更新
        SettlePoolDO pool = BeanUtilX.copy(reqVO, SettlePoolDO::new);
        pool.setSettleTransactionCode(exists.getSettleTransactionCode());
        poolMapper.updateById(pool);
        // 返回
        return pool.getSettleId();
    }

    @Override
    public void settlePoolDel(Long id) {
        // 校验存在
        this.settlePoolValidateExists(id);
        // 删除
        poolMapper.deleteById(id);
    }

    @Override
    public void settlePoolDelBySourceOrderId(Long sourceOrderId) {
        if (checkOrder(sourceOrderId)) return;
        // 删除
        poolMapper.delete(LambdaQueryWrapperX.<SettlePoolDO>lambdaQueryX()
                .eq(SettlePoolDO::getSourceOrderId, sourceOrderId)
        );
    }

    @Override
    public boolean checkOrder(Long sourceOrderId) {
        List<SettlePoolDO> poolList = poolMapper.selectList(LambdaQueryWrapperX.<SettlePoolDO>lambdaQueryX()
                .eq(SettlePoolDO::getSourceOrderId, sourceOrderId)
        );
        if(ObjUtilX.isEmpty(poolList)){
            return true;
        }
        for (SettlePoolDO settlePoolDO : poolList) {
            if(!settlePoolDO.getAccountStatus().equals(OrderStatusEnum.NOT_ACCOUNT.getCode().shortValue())){
                throw new BizException("5001", "结算池不是未对账状态，不能操作");
            }
        }
        return false;
    }

    private SettlePoolDO settlePoolValidateExists(Long id) {
        SettlePoolDO pool = poolMapper.selectById(id);
        if (pool == null) {
            // throw exception(POOL_NOT_EXISTS);
            throw new BizException("5001", "结算池不存在");
        }
        return pool;
    }

    @Override
    public SettlePoolRespVO settlePoolDetail(Long id) {
        SettlePoolDO data = poolMapper.selectById(id);
        SettlePoolRespVO resp = BeanUtilX.copy(data, SettlePoolRespVO::new);
        fillVoProperties(resp);
        return resp;
    }

    @Override
    public List<SettlePoolRespVO> settlePoolList(SettlePoolQueryReqVO reqVO) {
        List<SettlePoolDO> data = poolMapper.selectList(reqVO);
        if (CollUtilX.isEmpty(data)){
            return new ArrayList<>(0);
        }
        List<SettlePoolRespVO> list = BeanUtilX.copy(data, SettlePoolRespVO::new);
        //属性填充
        batchFillVoProperties(list);
        return list;
    }

    @Override
    public PageResult<SettlePoolRespVO> settlePoolPage(SettlePoolPageReqVO reqVO) {
        //物料条件查询
        if (StrUtilX.isNotEmpty(reqVO.getMaterialCode()) || StrUtilX.isNotEmpty(reqVO.getMaterialName())
                || reqVO.getMaterialCategoryDictId()!=null || StrUtilX.isNotEmpty(reqVO.getSpecModel())){
            ERPMaterialQueryReqVO maReq = new ERPMaterialQueryReqVO();
            maReq.setMaterialCode(reqVO.getMaterialCode());
            maReq.setMaterialName(reqVO.getMaterialName());
            maReq.setSpecModel(reqVO.getSpecModel());
            maReq.setMaterialCategoryDictId(reqVO.getMaterialCategoryDictId());
            List<Long> materialIdList = erpMaterialService.findMaterialIdList(maReq);
            reqVO.setMaterialIdList(materialIdList);
            if (CollUtilX.isEmpty(materialIdList)){
                return PageResult.empty();
            }
        }


        if (StrUtilX.isNotEmpty(reqVO.getCustomerName())) {
            List<Long> customerIdList = null;
            if (reqVO.getFormType() == 1) {//销售结算池
                //查询客户名称
                ERPCustomerQueryReqVO custmReq = new ERPCustomerQueryReqVO();
                custmReq.setCustomerName(reqVO.getCustomerName());
                List<ERPCustomerDO> customerList = erpCustomerMapper.selectList(custmReq);
                if (CollUtilX.isEmpty(customerList)) {
                    return PageResult.empty();
                }
                customerIdList = customerList.stream().map(ERPCustomerDO::getCustomerId).collect(Collectors.toList());
            } else {//采购结算池
                //查询供应商
                ERPSupplierQueryReqVO custmReq = new ERPSupplierQueryReqVO();
                custmReq.setSupplierName(reqVO.getCustomerName());
                List<ERPSupplierDO> customerList = erpSupplierMapper.selectList(custmReq);
                if (CollUtilX.isEmpty(customerList)) {
                    return PageResult.empty();
                }
                customerIdList = customerList.stream().map(ERPSupplierDO::getSupplierId).collect(Collectors.toList());
            }
            reqVO.setCustomerIdList(customerIdList);
        }
        PageResult<SettlePoolDO> data = poolMapper.selectPage(reqVO);
        if (CollUtilX.isEmpty(data.getList())){
            return PageResult.empty();
        }
        List<SettlePoolRespVO> list = data.getList().stream().map((item) -> {
            SettlePoolRespVO resp = BeanUtilX.copy(item, SettlePoolRespVO::new);
            // 查询物料

            return resp;
        }).collect(Collectors.toList());
        //属性填充
        batchFillVoProperties(list);
        return PageResult.init(data, list);

    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(SettlePoolRespVO respVO) {

        String customerName = "";
        if(respVO.getFormType() == 1){//销售结算池
            //查询客户名称
            customerName = erpBaseService.getERPCustomerNameById(respVO.getCustomerId());
        }else{//采购结算池
            //查询供应商
            customerName = erpSupplierService.getErpSupplierById(respVO.getCustomerId());
        }
        respVO.setCustomerName(customerName);

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        // 基本单位
        respVO.setMainUnitDictName(dictMap.getOrDefault(respVO.getMainUnitDictId(),""));

        //查询负责人
//        String directorName = compositeService.getEmpNameById(respVO.getDirectorId());
//        respVO.setDirectorName(directorName);

        //查询责任部门
//        String directorOrgName = compositeService.getOrgNameById(respVO.getDirectorOrgId().toString());
//        respVO.setDirectorOrgName(directorOrgName);

    }

    /**
     * VO属性填充-批量处理
     *
     * @param respList
     *
     */
    private void batchFillVoProperties(List<SettlePoolRespVO> respList) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.CURRENCY.getDictCode(),SystemDictEnum.COLLECTION_PLAN_STRATEGY.getDictCode(),
                SystemDictEnum.COLLECTION_PLAN_STRATEGY_CHILDREN.getDictCode(), SystemDictEnum.INVOICING_PLAN_STRATEGY.getDictCode(),
                SystemDictEnum.SALE_REFUND_PLAN_STRATEGY.getDictCode(),SystemDictEnum.PAYMENT_PLAN_STRATEGY.getDictCode(), SystemDictEnum.PAYMENT_PLAN_STRATEGY_CHILDREN.getDictCode(),
                SystemDictEnum.RECEIPT_PLAN_STRATEGY.getDictCode(), SystemDictEnum.PURCHASE_REFUND_PLAN_STRATEGY.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询责任部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameMap();

        Map<Long, String> empNameMap = new HashMap<>();
        Map<Long, String> customerNameMap = new HashMap<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> empIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        List<Long> customerIdList = new ArrayList<>();
        for (SettlePoolRespVO deatilResp: respList){
//            empIdList.add(deatilResp.getDirectorId());
            materialIdList.add(deatilResp.getMaterialId());
            customerIdList.add(deatilResp.getCustomerId());
        }
        if(respList.get(0).getFormType() == 1) {//销售结算池
            //查询客户名称
            customerNameMap = erpBaseService.getERPCustomerNameByIdList(customerIdList);
        } else {//采购结算池
            //查询供应商
            customerNameMap = erpSupplierService.erpSupplierNameMap(customerIdList);
        }

        //查询负责人
        empNameMap = erpBaseService.getEmpNameByIdList(empIdList);


        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        for (SettlePoolRespVO respVO: respList){

            // 本币币种
            String localCurrencyDictId = respVO.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                respVO.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 基本单位
            respVO.setMainUnitDictName(dictMap.getOrDefault(CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + respVO.getMainUnitDictId(),""));

            //客户名称
            respVO.setCustomerName(customerNameMap.getOrDefault(respVO.getCustomerId(),""));

            if (respVO.getFormType() == 1) {
                /** 收款计划策略--取父集 */
                String relatedCollectionPlanStrategy = respVO.getRelatedCollectionPlanStrategy();
                if(StrUtilX.isNotEmpty(relatedCollectionPlanStrategy)){
                    relatedCollectionPlanStrategy = SystemDictEnum.COLLECTION_PLAN_STRATEGY.getDictCode() + "-" + relatedCollectionPlanStrategy;
                    respVO.setRelatedCollectionPlanStrategyDictName(dictMap.get(relatedCollectionPlanStrategy));
                }

                /** 收款计划策略--取子集 */
                String collectionPlanStrategy = respVO.getCollectionPlanStrategy();
                if(StrUtilX.isNotEmpty(collectionPlanStrategy)){
                    collectionPlanStrategy = SystemDictEnum.COLLECTION_PLAN_STRATEGY_CHILDREN.getDictCode() + "-" + collectionPlanStrategy;
                    respVO.setCollectionPlanStrategyDictName(dictMap.get(collectionPlanStrategy));
                }

                /** 开票计划策略 */
                String invoiceStrategy = respVO.getInvoicingPlanStrategy();
                if(StrUtilX.isNotEmpty(invoiceStrategy)){
                    invoiceStrategy = SystemDictEnum.INVOICING_PLAN_STRATEGY.getDictCode() + "-" + invoiceStrategy;
                    respVO.setInvoiceStrategyDictName(dictMap.get(invoiceStrategy));
                }
                /** 开票结算池策略--取子集 */
                String invoicingSettlementStrategy = respVO.getInvoicingSettlementStrategy();
                if(StrUtilX.isNotEmpty(invoicingSettlementStrategy)){
                    invoicingSettlementStrategy = SystemDictEnum.COLLECTION_PLAN_STRATEGY_CHILDREN.getDictCode() + "-" + invoicingSettlementStrategy;
                    respVO.setInvoicingSettlementStrategyDictName(dictMap.get(invoicingSettlementStrategy));
                }

                /** 退款策略 */
                String saleRefundPlanStrategy = respVO.getSaleRefundPlanStrategy();
                if(StrUtilX.isNotEmpty(saleRefundPlanStrategy)){
                    saleRefundPlanStrategy = SystemDictEnum.SALE_REFUND_PLAN_STRATEGY.getDictCode() + "-" + saleRefundPlanStrategy;
                    respVO.setSaleRefundPlanStrategyDictName(dictMap.get(saleRefundPlanStrategy));
                }
            }else {
                /** 收款计划策略--取父集 */
                String relatedCollectionPlanStrategy = respVO.getRelatedCollectionPlanStrategy();
                if(StrUtilX.isNotEmpty(relatedCollectionPlanStrategy)){
                    relatedCollectionPlanStrategy = SystemDictEnum.PAYMENT_PLAN_STRATEGY.getDictCode() + "-" + relatedCollectionPlanStrategy;
                    respVO.setRelatedCollectionPlanStrategyDictName(dictMap.get(relatedCollectionPlanStrategy));
                }

                /** 收款计划策略--取父集 */
                String collectionPlanStrategy = respVO.getCollectionPlanStrategy();
                if(StrUtilX.isNotEmpty(collectionPlanStrategy)){
                    collectionPlanStrategy = SystemDictEnum.PAYMENT_PLAN_STRATEGY_CHILDREN.getDictCode() + "-" + collectionPlanStrategy;
                    respVO.setCollectionPlanStrategyDictName(dictMap.get(collectionPlanStrategy));
                }

                /** 开票计划策略 */
                String invoiceStrategy = respVO.getInvoicingPlanStrategy();
                if(StrUtilX.isNotEmpty(invoiceStrategy)){
                    invoiceStrategy = SystemDictEnum.RECEIPT_PLAN_STRATEGY.getDictCode() + "-" + invoiceStrategy;
                    respVO.setInvoiceStrategyDictName(dictMap.get(invoiceStrategy));
                }
                /** 开票结算池策略--取子集 */
                String invoicingSettlementStrategy = respVO.getInvoicingSettlementStrategy();
                if(StrUtilX.isNotEmpty(invoicingSettlementStrategy)){
                    invoicingSettlementStrategy = SystemDictEnum.PAYMENT_PLAN_STRATEGY_CHILDREN.getDictCode() + "-" + invoicingSettlementStrategy;
                    respVO.setInvoicingSettlementStrategyDictName(dictMap.get(invoicingSettlementStrategy));
                }

                /** 退款策略 */
                String saleRefundPlanStrategy = respVO.getSaleRefundPlanStrategy();
                if(StrUtilX.isNotEmpty(saleRefundPlanStrategy)){
                    saleRefundPlanStrategy = SystemDictEnum.PURCHASE_REFUND_PLAN_STRATEGY.getDictCode() + "-" + saleRefundPlanStrategy;
                    respVO.setSaleRefundPlanStrategyDictName(dictMap.get(saleRefundPlanStrategy));
                }

            }
            //责任人属性填充
//            respVO.setDirectorName(empNameMap.getOrDefault(respVO.getDirectorId(),""));

            //责任部门属性填充
//            respVO.setDirectorOrgName(orgNameMap.getOrDefault(respVO.getDirectorOrgId().toString(),""));

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(respVO.getMaterialId());
            if (erpMaterialDO!=null){
                respVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                respVO.setMaterialName(erpMaterialDO.getMaterialName());
                respVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                respVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                respVO.setSpecModel(erpMaterialDO.getSpecModel());
                respVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
        }
    }
}
