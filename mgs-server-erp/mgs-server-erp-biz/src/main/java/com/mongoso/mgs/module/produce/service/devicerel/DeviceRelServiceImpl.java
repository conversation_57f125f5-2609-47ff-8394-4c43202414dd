package com.mongoso.mgs.module.produce.service.devicerel;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.produce.controller.admin.devicerel.vo.*;
import com.mongoso.mgs.module.produce.dal.db.devicerel.DeviceRelDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.devicerel.DeviceRelMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.produce.enums.ErrorCodeConstants.*;


/**
 * 设备台账引用 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceRelServiceImpl implements DeviceRelService {

    @Resource
    private DeviceRelMapper deviceRelMapper;

    @Override
    public Long deviceRelAdd(DeviceRelAditReqVO reqVO) {
        // 插入
        DeviceRelDO deviceRel = BeanUtilX.copy(reqVO, DeviceRelDO::new);
        deviceRelMapper.insert(deviceRel);
        // 返回
        return deviceRel.getDeviceRelId();
    }

    @Override
    public Long deviceRelEdit(DeviceRelAditReqVO reqVO) {
        // 校验存在
        this.deviceRelValidateExists(reqVO.getDeviceRelId());
        // 更新
        DeviceRelDO deviceRel = BeanUtilX.copy(reqVO, DeviceRelDO::new);
        deviceRelMapper.updateById(deviceRel);
        // 返回
        return deviceRel.getDeviceRelId();
    }

    @Override
    public void deviceRelDelete(Long deviceRelId) {
        // 校验存在
        this.deviceRelValidateExists(deviceRelId);
        // 删除
        deviceRelMapper.deleteById(deviceRelId);
    }

    private DeviceRelDO deviceRelValidateExists(Long deviceRelId) {
        DeviceRelDO deviceRel = deviceRelValidateExists(deviceRelId);
        if (deviceRel == null) {
            // throw exception(DEVICE_REL_NOT_EXISTS);
            throw new BizException("5001", "设备台账引用不存在");
        }
        return deviceRel;
    }

    @Override
    public DeviceRelRespVO deviceRelDetail(Long deviceRelId) {
        DeviceRelDO data = deviceRelMapper.selectById(deviceRelId);
        return BeanUtilX.copy(data, DeviceRelRespVO::new);
    }

    @Override
    public List<DeviceRelRespVO> deviceRelList(DeviceRelQueryReqVO reqVO) {
        List<DeviceRelDO> data = deviceRelMapper.selectList(reqVO);
        return BeanUtilX.copy(data, DeviceRelRespVO::new);
    }

    @Override
    public PageResult<DeviceRelRespVO> deviceRelPage(DeviceRelPageReqVO reqVO) {
        PageResult<DeviceRelDO> data = deviceRelMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, DeviceRelRespVO::new);
    }

}
