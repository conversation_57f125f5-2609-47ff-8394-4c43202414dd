package com.mongoso.mgs.module.sale.handler.approve;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.util.LocalDateTimeUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.sale.dal.db.customerpriceplan.CustomerPricePlanDO;
import com.mongoso.mgs.module.sale.dal.mysql.customerpriceplan.CustomerPricePlanMapper;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: zhiling
 * @date: 2024/11/26 18:34
 * @description: 销售发货通知单审批流程处理类
 */

@Component
public class CustomerPricePlanApproveHandler extends FlowApproveHandler<CustomerPricePlanDO> {

    @Resource
    private CustomerPricePlanMapper customerPricePlanMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(CustomerPricePlanDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(CustomerPricePlanDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(CustomerPricePlanDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getPricePlanId())
                .objCode(item.getPlanCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(CustomerPricePlanDO item, BaseApproveRequest request) {
        return true;
    }

    @Override
    public Integer handleBusinessData(CustomerPricePlanDO pricePlanDO, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

//        JSONObject baseInfo = pricePlanDO.getBaseInfo();
        if (pricePlanDO == null){
            return 1;
        }

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
//            String startDate = baseInfo.getString("startDate");
//            String endDate = baseInfo.getString("endDate");
            LocalDate startDate = pricePlanDO.getStartDate();
            LocalDate endDate = pricePlanDO.getEndDate();
//            LocalDate startDateTime = LocalDateTimeUtilX.parseDate(startDate);
//            LocalDate endDateTime = LocalDateTimeUtilX.parseDate(endDate);

            if (LocalDate.now().compareTo(startDate)>=0 && LocalDate.now().compareTo(endDate)<=0){
//                baseInfo.put("isEffective","1");
                pricePlanDO.setIsEffective("1");
            }
            if (LocalDate.now().compareTo(endDate)>0){
//                baseInfo.put("isEffective","2");
                pricePlanDO.setIsEffective("2");
            }
        }else if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
//            baseInfo.put("isEffective","0");
            pricePlanDO.setIsEffective("0");
        }

        pricePlanDO.setApprovedBy(loginUser.getFullUserName());
        pricePlanDO.setApprovedDt(LocalDateTime.now());
        pricePlanDO.setDataStatus(dataStatus);
//        pricePlanDO.setBaseInfo(baseInfo);

        int updateCount = customerPricePlanMapper.updateById(pricePlanDO);
        return updateCount;
    }

}