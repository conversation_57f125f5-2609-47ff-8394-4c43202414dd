package com.mongoso.mgs.module.finance.service.asset.assetdepreciate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.*;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciateconfig.vo.AssetDepreciateConfigRespVO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciate.AssetDepreciateDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetregister.AssetRegisterDO;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetdepreciate.AssetDepreciateMapper;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetregister.AssetRegisterMapper;
import com.mongoso.mgs.module.finance.handler.approve.asset.AssetDepreciateHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.asset.AssetDepreciateFlowCallBackHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.asset.AssetRegisterFlowCallBackHandler;
import com.mongoso.mgs.module.finance.service.asset.assetdepreciateconfig.AssetDepreciateConfigService;
import com.mongoso.mgs.module.finance.service.asset.assetregister.AssetRegisterService;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.finance.enums.ErrorCodeConstants.*;


/**
 * 资产折旧 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssetDepreciateServiceImpl implements AssetDepreciateService {

    @Resource
    private AssetDepreciateMapper assetDepreciateMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private AssetRegisterService assetRegisterService;

    @Resource
    private AssetDepreciateConfigService depreciateConfigService;

    @Resource
    private AssetDepreciateHandler assetDepreciateHandler;

    @Resource
    private AssetRegisterMapper assetRegisterMapper;

    @Resource
    private AssetDepreciateFlowCallBackHandler assetDepreciateFlowCallBackHandler;

    @Override
    public Long assetDepreciateAdd(AssetDepreciateAditReqVO reqVO) {
        // 插入
        AssetDepreciateDO assetDepreciate = BeanUtilX.copy(reqVO, AssetDepreciateDO::new);
        assetDepreciateMapper.insert(assetDepreciate);
        // 返回
        return assetDepreciate.getAssetDepreciateId();
    }

    @Override
    public Long assetDepreciateEdit(AssetDepreciateAditReqVO reqVO) {
        // 校验存在
//        this.assetDepreciateValidateExists(reqVO.getAssetDepreciateId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.assetDepreciateValidateExists(reqVO.getAssetDepreciateId()), reqVO);

        // 更新
        AssetDepreciateDO assetDepreciate = BeanUtilX.copy(reqVO, AssetDepreciateDO::new);
        //查询公式
        AssetDepreciateBaseVO baseVO = BeanUtilX.copy(reqVO, AssetDepreciateBaseVO::new);
        AssetDepreciateConfigRespVO depreciateRespVO = depreciateConfigService.assetDepreciateConfigDetail(reqVO.getDepreciationsConfigId());
        if (depreciateRespVO != null) {
            BigDecimal currentAccrualAmt = assetRegisterService.calculateFormula(depreciateRespVO.getFormula(),baseVO);
            if (currentAccrualAmt.compareTo(reqVO.getAssetPurchasePrice().subtract(reqVO.getNetResidualValue())) > 0){
                currentAccrualAmt = reqVO.getAssetPurchasePrice().subtract(reqVO.getNetResidualValue());
            }
            assetDepreciate.setCurrentAccrualAmt(currentAccrualAmt);
        }

        assetDepreciateMapper.updateById(assetDepreciate);
        // 返回
        return assetDepreciate.getAssetDepreciateId();
    }

    @Override
    public void assetDepreciateDel(Long assetDepreciateId) {
        // 校验存在
        AssetDepreciateDO returnDo = this.assetDepreciateValidateExists(assetDepreciateId);
        if(returnDo.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey().shortValue()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        assetDepreciateMapper.deleteById(assetDepreciateId);
    }

    private AssetDepreciateDO assetDepreciateValidateExists(Long assetDepreciateId) {
        AssetDepreciateDO assetDepreciate = assetDepreciateMapper.selectById(assetDepreciateId);
        if (assetDepreciate == null) {
            // throw exception(ASSET_DEPRECIATE_NOT_EXISTS);
            throw new BizException("5001", "资产折旧不存在");
        }
        return assetDepreciate;
    }

    @Override
    public AssetDepreciateRespVO assetDepreciateDetail(Long assetDepreciateId) {
        AssetDepreciateRespVO assetDepreciateRespVO = assetDepreciateMapper.selectByIdDetail(assetDepreciateId);
        Optional.ofNullable(approveService.detailByObjId(assetDepreciateId.toString())).ifPresent(approveTask -> assetDepreciateRespVO.setApproveTaskId(approveTask.getApproveTaskId()));
        return assetDepreciateRespVO;
    }

    @Override
    public List<AssetDepreciateRespVO> assetDepreciateList(AssetDepreciateQueryReqVO reqVO) {
        List<AssetDepreciateDO> data = assetDepreciateMapper.selectList(reqVO);
        return BeanUtilX.copy(data, AssetDepreciateRespVO::new);
    }

    @Override
    public ResultX<BatchResult> assetDepreciateDelBatch(IdReq reqVO) {
        //获取对象属性名
        String id = EntityUtilX.getPropertyName(AssetDepreciateDO:: getAssetDepreciateId);
        String code = EntityUtilX.getPropertyName(AssetDepreciateDO::getAccrualMonth);

        return erpBaseService.batchDelete(reqVO.getIdList(), AssetDepreciateDO.class, null, id, code);
    }

    @Override
    public PageResult<AssetDepreciateRespVO> assetDepreciatePage(AssetDepreciatePageReqVO reqVO) {
        IPage<AssetDepreciateRespVO> assetIPage = assetDepreciateMapper.selectPage(PageUtilX.buildParam(reqVO),reqVO);
        PageResult<AssetDepreciateRespVO> pageResult = PageUtilX.buildResult(assetIPage);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    @Override
    public BatchResult assetDepreciateApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);

        //1.查询业务数据
        List<AssetDepreciateDO> list = assetDepreciateMapper.selectBatchIdList(reqVO.getIdList(),buttonType);
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作

        List<FailItem> failItemList = new ArrayList<>();
        for (AssetDepreciateDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus().intValue());
                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = assetDepreciateHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getAccrualMonth());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (AssetDepreciateDO item : list) {

                // 没有编码，用名称代替
                // 查询资产详情
                AssetRegisterDO assetRegisterDO = assetRegisterMapper.selectById(item.getAssetId());

                String code = item.getAccrualMonth() + " " + assetRegisterDO.getAssetCode() + " " + assetRegisterDO.getAssetName();

                String reason = reasonMap.get(code);
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getAssetDepreciateId());
                    messageInfoBO.setObjCode(code);
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getAssetDepreciateId());
                    messageInfoBO.setObjCode(code);
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object assetDepreciateFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        AssetDepreciateDO item = this.assetDepreciateValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return assetDepreciateFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(AssetDepreciateRespVO respVO) {
        List<AssetDepreciateRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<AssetDepreciateRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        for(AssetDepreciateRespVO respVO : respVOList){
            directorIdList.add(respVO.getDirectorId());
            directorOrgIdList.add(respVO.getDirectorOrgId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.ASSET_TYPE.getDictCode(), SystemDictEnum.PROVISION_PERIOD.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        // 属性填充
        for (AssetDepreciateRespVO item : respVOList) {
            // 资产分类
            if(StrUtilX.isNotEmpty(item.getAssetTypeDictId())){
                String assetTypeDictId = CustomerDictEnum.ASSET_TYPE.getDictCode() + "-" + item.getAssetTypeDictId();
                item.setAssetTypeDictName(dictMap.get(assetTypeDictId));
            }

            // 计提周期
            String accrualUnitType = SystemDictEnum.PROVISION_PERIOD.getDictCode() + "-" + item.getAccrualUnitType();
            item.setAccrualPeriodDictName(dictMap.get(accrualUnitType));

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if(item.getDirectorId() != null){
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }

        }
    }

}
