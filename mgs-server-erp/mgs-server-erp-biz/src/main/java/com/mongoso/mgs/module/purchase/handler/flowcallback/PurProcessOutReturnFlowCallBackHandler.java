package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseFlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutreturn.PurProcessOutReturnDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author：
 * @description：工序委外采购退货单审批回调
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurProcessOutReturnFlowCallBackHandler extends FlowCallbackHandler<PurProcessOutReturnDO> {
    protected PurProcessOutReturnFlowCallBackHandler(FlowApproveHandler<PurProcessOutReturnDO> approveHandler) {
        super(approveHandler);
    }
}
