package com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.detail;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
import java.math.BigDecimal;

import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 替代物料明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialAlternativeDetailPageReqVO extends PageParam {

    /** 替代物料id */
    private Long alternativeMaterialId;

    /** 替代物料编码 */
    private String alternativeMaterialCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 行号 */
    private Integer rowNo;

    /** 主数量 */
    private BigDecimal mainQty;

    /** 替代数量 */
    private BigDecimal alternativeQty;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
