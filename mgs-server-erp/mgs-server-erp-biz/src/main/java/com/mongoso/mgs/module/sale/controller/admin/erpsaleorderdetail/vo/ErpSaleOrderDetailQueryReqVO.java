package com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 销售订单明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ErpSaleOrderDetailQueryReqVO extends CommonParam{

    /** 销售订单id */
    private Long saleOrderId;
    private List<Long> saleOrderIdList;

    /** 行号 */
    private Short rowNo;

    /** 关联行号 */
    private Short relatedRowNo;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 基本单位 */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 数量 */
    private BigDecimal qty;

    /** 单价 */
    private BigDecimal exclTaxPrice;

    /** 金额 */
    private BigDecimal exclTaxAmt;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 含税单价 */
    private BigDecimal inclTaxPrice;

    /** 含税金额 */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 排除物料ID */
    private List<Long> excludeSaleOrderDetailIdList;

    /** 排除物料ID */
    private List<Long> exclMaterialIdList;

    /** 物料IDList */
    private List<Long> materialIdList;

    /** 销售发货通知单id */
    private List<Long> shipNoticeIdList;

    /** 关联单据id */
    private Long relatedOrderId;

    /** 已通知数量 */
    private BigDecimal notifiedQty;

    /** 已退货数量 */
    private BigDecimal returnedQty;

    /** 已采购数量 */
    private BigDecimal purchasedQty;

    /** 是否通知完成 */
    private Integer isMaterialFulNotified;

    /** 是否退货完成 */
    private Integer isMaterialFulReturned;

    /** 是否采购完成 */
    private Integer isMaterialFulPurchased;

    /** 是否出库完成 */
    private Integer isMaterialFullOutbounded;
}
