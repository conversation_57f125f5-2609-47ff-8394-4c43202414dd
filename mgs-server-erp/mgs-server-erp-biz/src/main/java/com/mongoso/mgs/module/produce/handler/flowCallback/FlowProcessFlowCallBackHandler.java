package com.mongoso.mgs.module.produce.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.produce.dal.db.flowprocess.FlowProcessDO;
import org.springframework.stereotype.Component;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:40
 * @description: 工艺路线回调处理类
 */

@Component
public class FlowProcessFlowCallBackHandler extends FlowCallbackHandler<FlowProcessDO> {


    protected FlowProcessFlowCallBackHandler(FlowApproveHandler<FlowProcessDO> approveHandler) {
        super(approveHandler);
    }
}
