package com.mongoso.mgs.module.warehouse.service.erpinventory;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailRespVO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 盘点单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpInventoryDetailService {

    /**
     * 创建盘点单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpInventoryDetailAdd(@Valid ErpInventoryDetailAditReqVO reqVO);

    /**
     * 更新盘点单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpInventoryDetailEdit(@Valid ErpInventoryDetailAditReqVO reqVO);

    /**
     * 删除盘点单明细
     *
     * @param inventoryDetailId 编号
     */
    void erpInventoryDetailDel(Long inventoryDetailId);

    /**
     * 获得盘点单明细信息
     *
     * @param inventoryDetailId 编号
     * @return 盘点单明细信息
     */
    ErpInventoryDetailRespVO erpInventoryDetailDetail(Long inventoryDetailId);

    /**
     * 获得盘点单明细列表
     *
     * @param reqVO 查询条件
     * @return 盘点单明细列表
     */
    List<ErpInventoryDetailRespVO> erpInventoryDetailList(@Valid ErpInventoryDetailQueryReqVO reqVO);

    /**
     * 获得盘点单明细分页
     *
     * @param reqVO 查询条件
     * @return 盘点单明细分页
     */
    PageResult<ErpInventoryDetailRespVO> erpInventoryDetailPage(@Valid ErpInventoryDetailPageReqVO reqVO);

}
