package com.mongoso.mgs.module.salary.service.salaryitem;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.salary.controller.admin.salaryitem.vo.*;
import com.mongoso.mgs.module.salary.dal.db.salaryitem.SalaryItemDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.salary.dal.mysql.salaryitem.SalaryItemMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.salary.enums.ErrorCodeConstants.*;


/**
 * 薪酬项目归集 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SalaryItemServiceImpl implements SalaryItemService {

    @Resource
    private SalaryItemMapper itemMapper;

    @Override
    public Long salaryItemAdd(SalaryItemAditReqVO reqVO) {
        // 插入
        SalaryItemDO item = BeanUtilX.copy(reqVO, SalaryItemDO::new);
        itemMapper.insert(item);
        // 返回
        return item.getSalaryItemId();
    }

    @Override
    public Long salaryItemEdit(SalaryItemAditReqVO reqVO) {
        // 校验存在
        this.salaryItemValidateExists(reqVO.getSalaryItemId());
        // 更新
        SalaryItemDO item = BeanUtilX.copy(reqVO, SalaryItemDO::new);
        itemMapper.updateById(item);
        // 返回
        return item.getSalaryItemId();
    }

    @Override
    public void salaryItemDelete(Long salaryItemId) {
        // 校验存在
        this.salaryItemValidateExists(salaryItemId);
        // 删除
        itemMapper.deleteById(salaryItemId);
    }

    private SalaryItemDO salaryItemValidateExists(Long salaryItemId) {
        SalaryItemDO item = itemMapper.selectById(salaryItemId);
        if (item == null) {
            // throw exception(ITEM_NOT_EXISTS);
            throw new BizException("5001", "薪酬项目归集不存在");
        }
        return item;
    }

    @Override
    public SalaryItemRespVO salaryItemDetail(Long salaryItemId) {
        SalaryItemDO data = itemMapper.selectById(salaryItemId);
        return BeanUtilX.copy(data, SalaryItemRespVO::new);
    }

    @Override
    public List<SalaryItemRespVO> salaryItemList(SalaryItemQueryReqVO reqVO) {
        List<SalaryItemDO> data = itemMapper.selectList(reqVO);
        return BeanUtilX.copy(data, SalaryItemRespVO::new);
    }

    @Override
    public PageResult<SalaryItemRespVO> salaryItemPage(SalaryItemPageReqVO reqVO) {
        PageResult<SalaryItemDO> data = itemMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, SalaryItemRespVO::new);
    }

}
