package com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;




/**
 * 库存预订明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StockBookDetailRespVO extends StockBookDetailBaseVO {

    /** 单据ID */
    private Long orderId;

    /** 库存数量 */
    private BigDecimal stockQty;

    /** 单据明细数量 */
    private BigDecimal orderDetailQty;

    /** 预估用量 */
    private BigDecimal estimatedQty;

    /** 基本单位*/
    private String mainUnitDictName;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictName;

    /** 规格属性 */
    private String specAttributeStr;

    /** 规格型号 */
    private String specModel;

    /** 仓库ID */
    private String warehouseOrgName;

    /** 已预订数量 */
    private BigDecimal bookedQty;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 物料ID+仓库ID KEY */
    private String materialWarehouseKey;

    /** 预订单IDS */
    private String stockBookIds;

}
