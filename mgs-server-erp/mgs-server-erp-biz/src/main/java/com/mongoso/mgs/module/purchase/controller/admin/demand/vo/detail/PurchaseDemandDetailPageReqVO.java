package com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购需求明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseDemandDetailPageReqVO extends PageParam {

    /** 采购需求单号 */
    private String purchaseDemandCode;

    /**  采购需求主题 */
    private String purchaseDemandName;

    /** 关联单据号 */
    private String relatedOrderCode;
    private Long relatedOrderId;

    /** 采购需求单类型 */
    private String demandTypeDictId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别 */
    private String materialCategoryDictId;

    /** 规格型号 */
    private String specModel;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 采购需求单ID */
    private Long purchaseDemandId;

    /** 行号 */
    private Integer rowNo;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 基本单位 */
    private String mainUnitDictId;

    /** 规划需求数量 */
    private BigDecimal planDemandQty;

    /** 已采购数量 */
    private BigDecimal purchasedQty;

    /** 可采购数量 */
    private BigDecimal purchasableQty;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
