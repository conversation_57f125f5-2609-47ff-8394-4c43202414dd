package com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 外借单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialLoanQueryReqVO extends CommonParam{

    /** 外借单ID */
    private Long loanId;

    /** 外借单号 */
    private String loanCode;

    /** 外借单主题 */
    private String loanName;

    /** 外借单类型ID */
    private String loanTypeDictId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 外借类型 */
    private Integer loanType;

    /** 外借对象类型 */
    private Integer loanObjType;

    /** 外借对象ID */
    private Long loanObjId;

    /** 外借对象名称 */
    private String loanObjName;

    /** 外借组织ID */
    private String loanOrgId;

    /** 外借联系人ID */
    private Long contactId;

    /** 外借联系人姓名 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 是否归还完成 */
    private Short isReturnCompleted;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 单据状态 */
    private Integer dataStatus;

}
