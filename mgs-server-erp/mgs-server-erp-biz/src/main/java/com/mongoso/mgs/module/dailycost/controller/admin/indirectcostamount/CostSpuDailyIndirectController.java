package com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.spu.*;
import com.mongoso.mgs.module.dailycost.service.indirectcostamount.CostSpuDailyIndirectService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 产品日间接成本 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostSpuDailyIndirectController {

    @Resource
    private CostSpuDailyIndirectService spuDailyIndirectService;

    @OperateLog("产品日间接成本分页")
    @PostMapping("/costSpuDailyIndirectPage")
    @PreAuthorize("@ss.hasPermission('costSpuDailyIndirect:query')")
    @DataPermission
    public ResultX<PageResult<CostSpuDailyIndirectRespVO>> costSpuDailyIndirectPage(@Valid @RequestBody CostSpuDailyIndirectPageReqVO reqVO) {
        return success(spuDailyIndirectService.costSpuDailyIndirectPage(reqVO));
    }

}
