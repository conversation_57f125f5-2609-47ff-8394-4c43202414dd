package com.mongoso.mgs.module.finance.controller.admin.payment.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 收款单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PaymentPageReqVO extends PageParam {
    private List<Long> customerIdList;
    private String customerName;

    /** 单据类型 1：销售，2：采购 */
    @NotNull(message = "单据类型不能为空")
    @Min(value = 1, message = "单据类型值1：销售，2：采购")
    @Max(value = 2, message = "单据类型值1：销售，2：采购")
    private Short formType;

    /** 收款单号 */
    private String payCode;

    /** 申请单号 */
    private String applyCode;

    /** 申请主键ID */
    private Long reciveApplyId;

    /** 币种id */
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 客户id */
    private Long customerId;

    /** 收款总额 */
    private BigDecimal payTotalAmt;

    /** 剩余金额 */
    private BigDecimal remainingAmt;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 审核人 */
    private String approvedBy;

    /** 入账账户 */
    private Long inBillAccountId;

    /** 入账金额 */
    private BigDecimal inBillAmt;

    /** 账户名称 */
    private String accountName;

    /** 折扣金额 */
    private BigDecimal discountAmt;

    /** 预收款核销金额 */
    private BigDecimal payWriteAmt;

    /** 汇票核销金额 */
    private BigDecimal billWriteAmt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
