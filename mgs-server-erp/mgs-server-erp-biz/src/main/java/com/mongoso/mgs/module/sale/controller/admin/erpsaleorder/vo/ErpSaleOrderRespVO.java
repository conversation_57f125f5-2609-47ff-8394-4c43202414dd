package com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 销售订单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpSaleOrderRespVO extends ErpSaleOrderBaseVO {
    /** 收货人List */
    List<JSONObject> receiptList;

    /** 出库状态 */
    private String outboundStatusDictName;

    /** 开票计划策略 */
    private String invoiceStrategyDictName;

    private String invoicingSettlementStrategyDictName;

    /** 收款计划策略--取父集 */
    private String relatedCollectionPlanStrategyDictName;

    /** 收款计划策略--取子集 */
    private String collectionPlanStrategyDictName;

    /** 退款策略 */
    private String saleRefundPlanStrategyDictName;

    /** 单据状态 */
    private String formStatusDictName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    private Short sourceFormType;

    /** 源头单据id */
    private Long originOrderId;

    /** 审批任务id */
    private Long approveTaskId;

    /** 判断是否是销售发货出库单或者销售发货通知出库单 */
    private boolean isSaleDeliveryOutbound;

}
