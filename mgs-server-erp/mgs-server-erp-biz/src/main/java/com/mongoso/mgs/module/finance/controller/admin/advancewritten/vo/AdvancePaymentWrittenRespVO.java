package com.mongoso.mgs.module.finance.controller.admin.advancewritten.vo;

import lombok.*;

  
 import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 预收款已核销单据 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AdvancePaymentWrittenRespVO extends AdvancePaymentWrittenBaseVO {

    /**
     * 排序
     */
    private Integer sort;
    /** 标题 */
    private String title;
    /** 预收金额 */
    private BigDecimal advanceAmt;

    /** 剩余金额 */
    private BigDecimal remainingAmt;
    private String customerName;
    private String directorName;
    /** 责任部门 */
    private String directorOrgName;
    private String inBillAccountName;
    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 审批任务id */
    private Long approveTaskId;
}
