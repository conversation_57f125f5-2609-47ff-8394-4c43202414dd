package com.mongoso.mgs.module.warehouse.job;

import com.mongoso.mgs.framework.tenant.core.aop.TenantIgnore;
import com.mongoso.mgs.framework.tenant.core.job.TenantJob;
import com.mongoso.mgs.module.warehouse.service.erpinventory.ErpInventoryService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @author: Fashoin.Liu
 * @date: 2024/12/24 19:54
 * @description: 盘点计划定时任务
 */

@Async
@Slf4j
@Component
public class InventoryPlanJob {

    @Resource
    private ErpInventoryService erpInventoryService;

    @TenantJob
    @XxlJob("inventoryPlanTask")
    public void inventoryPlanTask() {

        XxlJobHelper.log("【盘点计划|生成盘点单定时任务】开始执行=========================》》》");
        //生成盘点单
        erpInventoryService.erpInventoryPlanGenTask();

        XxlJobHelper.log("【盘点计划|生成盘点单定时任务】执行完成=========================》》》");
    }



}
