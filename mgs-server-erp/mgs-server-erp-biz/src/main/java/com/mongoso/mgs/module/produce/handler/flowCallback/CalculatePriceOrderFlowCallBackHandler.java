package com.mongoso.mgs.module.produce.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.produce.dal.db.calculatepriceorder.CalculatePriceOrderDO;
import org.springframework.stereotype.Component;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:40
 * @description: 物料bom回调处理类
 */

@Component
public class CalculatePriceOrderFlowCallBackHandler extends FlowCallbackHandler<CalculatePriceOrderDO> {

    protected CalculatePriceOrderFlowCallBackHandler(FlowApproveHandler<CalculatePriceOrderDO> approveHandler) {
        super(approveHandler);
    }

}
