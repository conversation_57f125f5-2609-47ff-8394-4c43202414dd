package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.purchasechange.PurchaseChangeDO;
import com.mongoso.mgs.module.purchase.dal.db.purchasereturn.PurchaseReturnDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： LinShuiQiang
 * @date： 2025/5/29
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurchaseReturnFlowCallBackHandler extends FlowCallbackHandler<PurchaseReturnDO> {


    protected PurchaseReturnFlowCallBackHandler(FlowApproveHandler<PurchaseReturnDO> approveHandler) {
        super(approveHandler);
    }
}