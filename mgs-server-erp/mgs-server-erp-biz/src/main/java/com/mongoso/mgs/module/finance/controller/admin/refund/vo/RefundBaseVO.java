package com.mongoso.mgs.module.finance.controller.admin.refund.vo;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 退款单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class RefundBaseVO implements Serializable {

    /** 退款主键ID */
    private Long refundId;

    /** 单据类型 */
    @NotNull(message = "单据类型不能为空")
    @Min(value = 1, message = "单据类型值1：销售，2：采购")
    @Max(value = 2, message = "单据类型值1：销售，2：采购")
    private Short formType;

    /** 客户id */
    @NotNull(message = "客户/供应商ID不能为空")
    private Long customerId;

    /** 退款单号 */
    private String refundCode;

    /** 币种id */
    @NotNull(message = "币种不能为空")
    private String currencyDictId;

    /** 应收账款主键ID */
    //@NotNull(message = "应收账款主键ID不能为空")
    private Long paymentId;

    /** 币种名称 */
    private String currencyDictName;

    /** 应退金额 */
    @NotNull(message = "应退金额不能为空")
    private BigDecimal planRefundAmt;

    /** 实退金额 */
    private BigDecimal actRefundAmt;

    /** 出账账户ID */
    @NotNull(message = "出账账户不能为空")
    private Long outBillAccountId;
    private String outBillAccountName;

    /** 实退日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate actRefundDate;

    /** 账户名称 */
    private String accountName;
    /** 应收账款单号 */
    //@NotEmpty(message = "应收账款单号不能为空")
    private String accountsPayableNumber;
    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @NotNull(message = "单据时间不能为空")
    private LocalDateTime formDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核人 */
    private String approvedBy;

    /** 责任人 */
    @NotNull(message = "责任人不能为空")
    private Long directorId;

    /** 责任部门 */
    @NotNull(message = "责任部门不能为空")
    private String directorOrgId;

    /** 备注 */
    private String remark;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    //@NotNull(message = "汇率不能为空")
    private BigDecimal exchangeRate;

    /** 本币订单总金额 */
    private BigDecimal localCurrencyActRefundAmt;
    //
    ///** 关联单据id集合 */
    //@TableField(typeHandler = LongListTypeHandler.class)
    //private List<Long> relatedOrderIds;
}
