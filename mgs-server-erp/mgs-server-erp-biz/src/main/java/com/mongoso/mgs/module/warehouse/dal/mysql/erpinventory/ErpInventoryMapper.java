package com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.ErpInventoryPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.ErpInventoryQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 盘点单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpInventoryMapper extends BaseMapperX<ErpInventoryDO> {

    default PageResult<ErpInventoryDO> selectPage(ErpInventoryPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ErpInventoryDO>lambdaQueryX()
                .likeIfPresent(ErpInventoryDO::getInventoryCode, reqVO.getInventoryCode())
                .likeIfPresent(ErpInventoryDO::getInventoryName, reqVO.getInventoryName())
                .eqIfPresent(ErpInventoryDO::getInventoryTypeDictId, reqVO.getInventoryTypeDictId())
                .likeIfPresent(ErpInventoryDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(ErpInventoryDO::getInventoryMethod, reqVO.getInventoryMethod())
                .betweenIfPresent(ErpInventoryDO::getPlanInventoryDt, reqVO.getStartPlanInventoryDt(), reqVO.getEndPlanInventoryDt())
                .eqIfPresent(ErpInventoryDO::getInventoryStatus, reqVO.getInventoryStatus())
                .eqIfPresent(ErpInventoryDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpInventoryDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(ErpInventoryDO::getAdjustStatus, reqVO.getAdjustStatus())
                .betweenIfPresent(ErpInventoryDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpInventoryDO::getDataStatus, reqVO.getDataStatus())
                        .orderByDesc(ErpInventoryDO::getCreatedDt));
    }

    default List<ErpInventoryDO> selectList(ErpInventoryQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ErpInventoryDO>lambdaQueryX()
                .likeIfPresent(ErpInventoryDO::getInventoryCode, reqVO.getInventoryCode())
                .likeIfPresent(ErpInventoryDO::getInventoryName, reqVO.getInventoryName())
                .eqIfPresent(ErpInventoryDO::getInventoryTypeDictId, reqVO.getInventoryTypeDictId())
                .eqIfPresent(ErpInventoryDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(ErpInventoryDO::getInventoryMethod, reqVO.getInventoryMethod())
                .betweenIfPresent(ErpInventoryDO::getPlanInventoryDt, reqVO.getStartPlanInventoryDt(), reqVO.getEndPlanInventoryDt())
                .eqIfPresent(ErpInventoryDO::getInventoryStatus, reqVO.getInventoryStatus())
                .eqIfPresent(ErpInventoryDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpInventoryDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(ErpInventoryDO::getAdjustStatus, reqVO.getAdjustStatus())
                .betweenIfPresent(ErpInventoryDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpInventoryDO::getDataStatus, reqVO.getDataStatus())
                        .orderByDesc(ErpInventoryDO::getCreatedDt));
    }

    default List<ErpInventoryDO> forewarnJob(Integer dataStatus, List<Integer> statusList){
        return selectList(new LambdaQueryWrapperX<ErpInventoryDO>()
                .eq(ErpInventoryDO::getDataStatus, dataStatus)
                .in(ErpInventoryDO::getInventoryStatus, statusList));
    }
}