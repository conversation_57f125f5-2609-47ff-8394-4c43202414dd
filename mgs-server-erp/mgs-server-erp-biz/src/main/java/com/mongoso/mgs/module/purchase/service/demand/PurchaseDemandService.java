package com.mongoso.mgs.module.purchase.service.demand;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.PurchaseDemandRespAI;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.purchaseDemandRespStatAI;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购需求 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseDemandService {

    /**
     * 创建采购需求
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseDemandAdd(@Valid PurchaseDemandAditReqVO reqVO);

    /**
     * 更新采购需求
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseDemandEdit(@Valid PurchaseDemandAditReqVO reqVO);

    /**
     * 删除采购需求
     *
     * @param purchaseDemandId 编号
     */
    void purchaseDemandDel(Long purchaseDemandId);

    /**
     * 批量删除采购需求
     * @param reqVO
     * @return
     */
    ResultX<BatchResult> purchaseDemandDelBatch(IdReq reqVO);

    /**
     * 获得采购需求信息
     *
     * @param purchaseDemandId 编号
     * @return 采购需求信息
     */
    PurchaseDemandRespVO purchaseDemandDetail(Long purchaseDemandId);

    /**
     * 获得采购需求列表
     *
     * @param reqVO 查询条件
     * @return 采购需求列表
     */
    List<PurchaseDemandRespVO> purchaseDemandList(@Valid PurchaseDemandQueryReqVO reqVO);

    /**
     * 获得采购需求分页
     *
     * @param reqVO 查询条件
     * @return 采购需求分页
     */
    PageResult<PurchaseDemandRespVO> purchaseDemandPage(@Valid PurchaseDemandPageReqVO reqVO);

    BatchResult erpPurchaseDemandApprove(PurchaseFlowApprove reqVO);

    Object erpPurchaseDemandFlowCallback(PurchaseFlowCallback reqVO);

    PageResult<PurchaseDemandRespVO> demandForPurchasePage(PurchaseDemandPageReqVO reqVO);

    List<PurchaseDemandRespVO> demandForPurchaseList(PurchaseDemandQueryReqVO reqVO);

    PurchaseDemandRespAI purchaseDemandPageAI();


    purchaseDemandRespStatAI purchaseDemandStatAI();


}
