package com.mongoso.mgs.module.produce.controller.admin.mold.vo.molduse;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;

/**
 * 模具领用明细 DO
 *
 * <AUTHOR>
 */
@Data
public class MoldUseDetailReqVO {

    /** 模具id */
    private Long moldId;
    private Short rowNo;
    /** 领用人 */
    private Long usedBy;

    /** 预计归还时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate planReturnDate;

    /** 归还人 */
    private Long returnedBy;

    /** 备注 */
    private String remark;

}
