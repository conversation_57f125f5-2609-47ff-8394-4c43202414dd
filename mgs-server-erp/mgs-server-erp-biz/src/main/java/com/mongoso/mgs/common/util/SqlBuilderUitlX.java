package com.mongoso.mgs.common.util;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentReqBO;

/**
 * @author: zhiling
 * @date: 2024/11/23 14:46
 * @description: sql封装类
 */
public class SqlBuilderUitlX {

    /**
     * 封装sumQty查询方法 条件对象
     *
     * @param params
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T, R> DocumentReqBO buildDocumentReqBO(SqlQueryParam<T, R> params) {

        //参数校验
        checkParam(params);

        //通过实体获取表名
        String tableMaster = EntityUtilX.getTableNameByAnnot(params.getClazz());
        String tableDetail = EntityUtilX.getTableNameByAnnot(params.getDetailClazz());
        if (StrUtilX.isEmpty(tableMaster) || StrUtilX.isEmpty(tableDetail)){
            throw new BizException("500","实体类表名不允许为空!");
        }

        //获取查询属性名
        String qryFieldId = EntityUtilX.getPropertyName(params.getQryFieldIdName());
        String qryAggField = EntityUtilX.getPropertyName(params.getQryAggFieldName());

        //获取where条件字段名
        String condFieldIdName = EntityUtilX.getPropertyName(params.getCondFieldIdName());
        String condExcludeFieldName = EntityUtilX.getPropertyName(params.getCondExcludeFieldName());

        //获取连接属性名
        String ljoinFieldName = EntityUtilX.getPropertyName(params.getJoinFieldName1());

        //如果joinFieldName2为空,默认认为和joinFieldName1字段名称一致
        String rjoinFieldName = "";
        if (params.getJoinFieldName2() == null){
            rjoinFieldName = ljoinFieldName;
        }else {
            rjoinFieldName = EntityUtilX.getPropertyName(params.getJoinFieldName2());
        }

        //表名
        DocumentReqBO documentReqBO = new DocumentReqBO();
        documentReqBO.setTableMaster(tableMaster);
        documentReqBO.setTableDetail(tableDetail);

        //查询字段
        documentReqBO.setQryFieldId(qryFieldId);
        documentReqBO.setQryAggField(qryAggField);

        //连接字段
        documentReqBO.setJoinFieldName1(ljoinFieldName);
        documentReqBO.setJoinFieldName2(rjoinFieldName);

        //where条件名
        documentReqBO.setCondFieldIdName(condFieldIdName);
        documentReqBO.setCondExcludeFieldName(condExcludeFieldName);

        //where条件值
        documentReqBO.setRelatedOrderId(params.getRelatedOrderId());
        documentReqBO.setExcludeIdList(params.getExcludeIdList());

        return documentReqBO;
    }


    /**
     * 参数校验
     *
     * @param params
     * @param <R>
     * @param <T>
     */
    private static <R, T> void checkParam(SqlQueryParam<T, R> params) {

        if (params.getClazz() == null || params.getDetailClazz() == null){
            throw new BizException("500","clazz或detailClazz 不允许为空!");
        }

        if (params.getJoinFieldName1() == null){
            throw new BizException("500","表连接字段不允许为空!");
        }

        if (params.getQryFieldIdName() == null || params.getQryAggFieldName() == null){
            throw new BizException("500","sql查询字段不允许为空!");
        }

        if (params.getCondFieldIdName() == null || params.getCondExcludeFieldName() == null){
            throw new BizException("500","条件字段名不允许为空!");
        }

        if (params.getRelatedOrderId() == null){
            throw new BizException("500","条件值不允许为空!");
        }

    }


}
