package com.mongoso.mgs.module.dailycost.controller.admin.costprodmaterialdetail.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 生产物料成本明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostProdMaterialDetailRespVO extends CostProdMaterialDetailBaseVO {

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 物料编码 */
    private String materialCode;

    /** 报工时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime reportedDt;

    /** 工序名称 */
    private String processName;

    /** 物料名称 */
    private String materialName;

    /** 规格属性 */
    private String specAttributeStr;

    /** 原物料名称 */
    private String rawMaterialName;

    /** 物料SPU编码 */
    private String spuCode;

    /** 成本科目名称 */
    private String costSubjectName;

    /** 承担对象名称 */
    private String undertakeOrgName;

    /** 报工数量 */
    private BigDecimal reportedQty;

    /** 原料编码 */
    private String rawMaterialCode;

    /** 单价 */
    private BigDecimal price;

    /** 总金额 */
    private BigDecimal totalAmt;

    /** 承担物料编码 */
    private String undertakeMaterialCode;
    private String undertakeMaterialName;



}
