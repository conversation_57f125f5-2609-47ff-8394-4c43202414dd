package com.mongoso.mgs.module.finance.dal.enums;

import com.mongoso.mgs.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-*********** 段
 */
public interface ErrorCodeConstants {

    ErrorCode EXCHANGE_RATE_NOT_EXISTS = new ErrorCode("TODO 补充编号", "汇率不存在");

    ErrorCode TAX_RATE_NOT_EXISTS = new ErrorCode("TODO 补充编号", "税率不存在");
    ErrorCode TAX_RATE_ALREADY_EXISTS = new ErrorCode("TODO 补充编号", "税率已存在");

    ErrorCode CURRENCY_NAME_CNY_NOT_EXISTS = new ErrorCode("TODO 补充编号", "本币币种为CNY的不存在");

    ErrorCode ORIGINAL_CURRENCY_NOT_EXISTS = new ErrorCode("TODO 补充编号", "本币币种为{}，原币币种为CNY的汇率不存在");
    ErrorCode LOCAL_CURRENCY_NOT_EXISTS = new ErrorCode("TODO 补充编号", "本币币种为CNY，原币币种为{}的汇率不存在");

}
