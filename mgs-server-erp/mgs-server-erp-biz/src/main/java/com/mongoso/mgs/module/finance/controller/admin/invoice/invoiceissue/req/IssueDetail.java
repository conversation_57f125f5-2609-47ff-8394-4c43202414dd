package com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.req;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class IssueDetail {
//    /** 申请开票金额（含税） */
//    private BigDecimal applyInvoiceAmtInclTax;
//
//    /** 申请开票金额（不含税） */
//    private BigDecimal applyInvoiceAmtExclTax;
//
//    /** 数量 */
//    private BigDecimal qty;
//
//    /** 剩余开票数量 */
//    private BigDecimal remainingInvoiceQty;
//
//    /** 剩余开票金额 */
//    private BigDecimal remainingInvoiceAmt;

    private Long invoiceApplyDetailId;

    private Long invoicePlanDetailId;

    private Long invoiceDetailId;

    private BigDecimal invoiceQty;

    private BigDecimal invoiceAmtInclTax;

    private BigDecimal invoiceAmtExclTax;

    private BigDecimal taxAmt;
    /** 物料编码 */
    private String materialCode;
    private Long rowNo;
}
