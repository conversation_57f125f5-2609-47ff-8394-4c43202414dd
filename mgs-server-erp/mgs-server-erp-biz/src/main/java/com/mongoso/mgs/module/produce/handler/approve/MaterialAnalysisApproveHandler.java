package com.mongoso.mgs.module.produce.handler.approve;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.purchase.InboundStatusEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.common.util.tree.TreeUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.base.service.orderrelation.OrderRelationService;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo.ErpProdOrderQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkQtyBO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysis.MaterialAnalysisDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysisdetail.MaterialAnalysisDetailDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysisdetail.MaterialAnalysisResultDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.MaterialAnalysisTotalDO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderdetail.ErpProdOrderDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysis.MaterialAnalysisMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysisdetail.MaterialAnalysisDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysisdetail.MaterialAnalysisResultMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysistotal.MaterialAnalysisTotalMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.enums.AnalyzeParameEnum;
import com.mongoso.mgs.module.produce.enums.MaterialAnalysisEnum;
import com.mongoso.mgs.module.produce.service.materialanalysis.MaterialAnalysisService;
import com.mongoso.mgs.module.produce.service.materialanalysis.bo.MaterialBomTreeBO;
import com.mongoso.mgs.module.produce.service.materialbom.MaterialBomService;
import com.mongoso.mgs.module.produce.service.materialbom.bo.MaterialBO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseDemandQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseQtyBO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderQueryReqVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.purchase.dal.mysql.demand.PurchaseDemandMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.enums.FormStatusEnum;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.db.materialstock.ErpMaterialStockDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;
import static com.mongoso.mgs.module.sale.enums.ErrorCodeConstants.RELATED_SALE_FORCE_CLOSE;

/**
 * @author: zhiling
 * @date: 2024/11/26 18:34
 * @description: 审批流程处理类
 */

@Component
public class MaterialAnalysisApproveHandler extends FlowApproveHandler<MaterialAnalysisDO> {

    @Resource
    private MaterialAnalysisMapper materialAnalysisMapper;

//    @Resource
//    private MaterialAnalysisDetailMapper materialAnalysisDetailMapper;
//
//    @Resource
//    private MaterialBomService materialBomService;
//
//    @Resource
//    private MaterialAnalysisResultMapper materialAnalysisResultMapper;
//
//    @Resource
//    private MaterialAnalysisTotalMapper materialAnalysisTotalMapper;
//
//    @Resource
//    private ErpMaterialStockMapper erpMaterialStockMapper;
//
//    @Resource
//    private ErpBaseService erpBaseService;

    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Resource
    private OrderRelationService orderRelationService;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private StockBookService stockBookService;

//    @Resource
//    private ErpProdOrderDetailMapper erpProdOrderDetailMapper;
//
//    @Resource
//    private ErpSaleOrderDetailMapper erpSaleOrderDetailMapper;
//
//    @Resource
//    private ProdWorkMapper prodWorkMapper;
//
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;

    @Resource
    private PurchaseDemandMapper purchaseDemandMapper;

    @Resource
    @Lazy
    private MaterialAnalysisService materialAnalysisService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(MaterialAnalysisDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(MaterialAnalysisDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(MaterialAnalysisDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getMaterialAnalysisId())
                .objCode(item.getMaterialAnalysisCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(MaterialAnalysisDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            // 反审核
            if (item.getBizType().equals(1L)) {
                //查询销售订单
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getRelatedOrderId());
                if (erpSaleOrder!=null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type){
                    failItem.setCode(item.getMaterialAnalysisCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }
            //判断是否下发生产订单
            ErpProdOrderQueryReqVO prodReqVO = new ErpProdOrderQueryReqVO();
            prodReqVO.setRelatedOrderId(item.getMaterialAnalysisId());
            prodReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<ErpProdOrderDO> prodOrderDOList = erpProdOrderMapper.selectList(prodReqVO);
            if (CollUtilX.isNotEmpty(prodOrderDOList)){
                failItem.setCode(item.getMaterialAnalysisCode());
                failItem.setReason("该分析单已关联已审核的生产订单");
                return false;
            }
            //判断是否下发物料分析委外采购订单
            PurchaseOrderQueryReqVO purchaseReqVO = new PurchaseOrderQueryReqVO();
            purchaseReqVO.setRelatedOrderId(item.getMaterialAnalysisId());
            purchaseReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<PurchaseOrderDO> purchaseOrderDOList = purchaseOrderMapper.selectList(purchaseReqVO);
            if (CollUtilX.isNotEmpty(purchaseOrderDOList)){
                failItem.setCode(item.getMaterialAnalysisCode());
                failItem.setReason("该分析单已关联已审核的物料分析委外采购订单");
                return false;
            }
            //判断是否下发物料分析采购需求单
            PurchaseDemandQueryReqVO demandReqVO = new PurchaseDemandQueryReqVO();
            demandReqVO.setRelatedOrderId(item.getMaterialAnalysisId());
            demandReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<PurchaseDemandDO> demandDOList = purchaseDemandMapper.selectList(demandReqVO);
            if (CollUtilX.isNotEmpty(demandDOList)){
                failItem.setCode(item.getMaterialAnalysisCode());
                failItem.setReason("该分析单已关联已审核的物料分析采购需求单");
                return false;
            }
        } else if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            // 审核
            if (item.getBizType().equals(1L)) {
                //查询销售订单
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getRelatedOrderId());
                if (erpSaleOrder!=null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type){
                    failItem.setCode(item.getMaterialAnalysisCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }
        } else if (buttonType == DataButtonEnum.CANCEL.getKey()) {
            // 作废
        } else if (buttonType == DataButtonEnum.NOT_CANCEL.getKey()) {
            // 反作废
        }
        return true;
    }

    @Override
    public Integer handleBusinessData(MaterialAnalysisDO currentDO, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        //当前对象
        if (currentDO == null) {
            return 1;
        }

        Integer dataStatus = request.getDataStatus();
        Integer buttonType = request.getButtonType();
        Long id = currentDO.getMaterialAnalysisId();

//        Long materialAnalysisId = currentDO.getMaterialAnalysisId();
//        Long bizType = currentDO.getBizType();

        //分析结果处理
        materialAnalysisService.doAalysisResult(currentDO,dataStatus);

//        // 查询bom生产物料
//        List<MaterialAnalysisDetailDO> materialAnalysisDetailDOS = materialAnalysisDetailMapper.selectListByMaterialAnalysisId(materialAnalysisId);
//
//        // bom生产物料需求数量map
//        Map<Long, BigDecimal> bomMap = new HashMap<>();
//        Map<Long, MaterialAnalysisDetailDO> bomDOMap = new HashMap<>();
//
//        // bom生产物料id
//        List<Long> parentMaterialIdList = new ArrayList<>();
//        Set<Long> parentMaterialIdSet = new HashSet<>();
//
//        for (MaterialAnalysisDetailDO item : materialAnalysisDetailDOS) {
//            bomMap.put(item.getMaterialId(), item.getDemandQty());
//            bomDOMap.put(item.getMaterialId(), item);
//
//            parentMaterialIdList.add(item.getMaterialId());
//            parentMaterialIdSet.add(item.getMaterialId());
//        }
//
//
//        if (buttonType == DataButtonEnum.APPROVE.getKey()) {// 审核
//
//            //构造每个顶级节点的树结构列表
//            Map<Long, List<MaterialBomTreeBO>> treeMap = new HashMap<>();
//
//            //所有物料id集合
//            Set<Long> materialIdSet = new HashSet<>();
//
//            //所有子物料id集合
//            Set<Long> childMaterialIdSet = new HashSet<>();
//
//            // 1.查询bom树详情
//            for (Long bomMaterialId : parentMaterialIdList) {
//
//                // 添加自己
//                materialIdSet.add(bomMaterialId);
//
//                // 获取bom结构列表数据
//                List<MaterialBomTreeBO> bomList = getBomDate(Arrays.asList(bomMaterialId));
//
//                //构造每个顶级节点的树结构列表
//                treeMap.put(bomMaterialId, bomList);
//
//                //所有非0级物料id集合,校验当前顶级物料是否在其他树节点中
//                Set<Long> childMaterialIds = new HashSet<>();
//
//                for (MaterialBomTreeBO materialBomTreeBO : bomList) {
//                    // 添加所有的子物料
//                    materialIdSet.add(materialBomTreeBO.getMaterialId());
//
//                    // 添加不是0级的物料
//                    if (!"0".equals(materialBomTreeBO.getParentItemId())) {
//                        childMaterialIds.add(materialBomTreeBO.getMaterialId());
//                        childMaterialIdSet.add(materialBomTreeBO.getMaterialId());
//                    }
//                }
//
//                //校验当前顶级物料是否在其他树节点中
//                for (Long parentMaterialId : parentMaterialIdSet) {
//                    boolean b = childMaterialIds.add(parentMaterialId);
//                    if (!b) {
//                        MaterialAnalysisDetailDO materialAnalysisDetailDO = bomDOMap.get(parentMaterialId);
//                        String materialCode = materialAnalysisDetailDO.getMaterialCode();
//                        throw exceptionMsg("数据问题，0级物料在其他0级物料的子物料中，请修复数据："+materialCode);
//                    }
//                }
//            }
//
//            //根据物料id查询对应的物料信息
//            Map<Long, MaterialBO> bomMaterialBOMap = erpBaseService.getDataStatusMaterialByMaterialId(materialIdSet);
//
//            if (bomMaterialBOMap == null || bomMaterialBOMap.isEmpty()) {
//                throw exceptionMsg("数据问题，分析物料或子物料数据有被删除或未审核，请检查数据！");
//            }
//
//            if (bomMaterialBOMap.size() != materialIdSet.size()) {
//                throw exceptionMsg("数据问题，分析物料或子物料数据有被删除或未审核，请检查数据！");
//            }
//
//            //分析参数
//            String analyzeParam = currentDO.getAnalyzeParam();
//            List<Integer> analyzeParamList = new ArrayList<>();
//            if (StrUtilX.isNotEmpty(analyzeParam)) {
//                analyzeParamList = Arrays.stream(analyzeParam.split(",")).map(Integer::parseInt).collect(Collectors.toList());
//            }
//
//            // 2.根据物料id和仓库idList，查询物料的总库存
//            Map<Long, BigDecimal> materialStockMap = getMaterielStock(materialIdSet,childMaterialIdSet, currentDO,analyzeParamList);
//
//            //预估可用数量处理
//            Map<Long, MaterialAnalysisResultDO> estimateQtyMap = processEstimateQty(currentDO,materialStockMap,analyzeParamList);
//
//            // 临时物料库存数map,只是记录不做计算
//            Map<Long, BigDecimal> historyMaterialStockMap = new HashMap<>();
//            for (Map.Entry<Long, BigDecimal> entry : materialStockMap.entrySet()) {
//                historyMaterialStockMap.put(entry.getKey(), entry.getValue());
//            }
//
//            // 3.分析结果对象list，分析统计对象list
//            List<MaterialAnalysisResultDO> resultDOList = new ArrayList<>();
//            List<MaterialAnalysisTotalDO> totalDOList = new ArrayList<>();
//
//            for (Long bomMaterialId : parentMaterialIdList) {
//
//                // 获取每颗树的物料信息
//                List<MaterialBomTreeBO> bomList = treeMap.get(bomMaterialId);
//                if (CollUtilX.isNotEmpty(bomList)) {
//
//                    // 填充子级物料的需求数量
//                    demandQty(bomList, bomMap);
//
//                    // 4.计算每个bom的净需求量，并保存到分析结果对象list和分析统计对象list
//                    computeBomNetDemandQty(bizType, materialAnalysisId, materialStockMap, historyMaterialStockMap, bomList, bomMaterialBOMap, resultDOList, totalDOList,estimateQtyMap);
//
//                } else {
//
//                    MaterialBO materialBO = bomMaterialBOMap.get(bomMaterialId);
//
//                    // 没有物料bom
//                    MaterialAnalysisDetailDO materialAnalysisDetailDO = bomDOMap.get(bomMaterialId);
//
//                    // 构建bom分析统计表
//                    MaterialAnalysisTotalDO materialAnalysisTotalDO = buildNullTotalBomDO(bizType, materialAnalysisDetailDO, materialBO);
//                    totalDOList.add(materialAnalysisTotalDO);
//
//                    // 构建子物料分析统计表
//                    MaterialAnalysisResultDO materialAnalysisResultDO = buildNullResultMaterialDO(bizType, materialAnalysisDetailDO, materialBO);
//                    resultDOList.add(materialAnalysisResultDO);
//                }
//            }
//
//            // 重复物料进行汇总
//            List<MaterialAnalysisTotalDO> newTotalDOList = newTotalDOList(totalDOList);
//
//            // 5.新增分析结果对象
//            materialAnalysisResultMapper.insertBatch(resultDOList);
//
//            // 6.新增分析统计对象
//            for (MaterialAnalysisTotalDO totalDO : newTotalDOList) {
//                totalDO.setPurchaseableQty(totalDO.getNetDemandQty());
//                totalDO.setPurchasedQty(BigDecimal.ZERO);
//                totalDO.setIsMaterialFullPurchased(0);
//                totalDO.setPlanableDemandQty(totalDO.getNetDemandQty());
//                totalDO.setPlannedDemandQty(BigDecimal.ZERO);
//                totalDO.setIsMaterialFullPlaned(0);
//                totalDO.setOutsourcedQty(BigDecimal.ZERO);
//            }
//            materialAnalysisTotalMapper.insertBatch(newTotalDOList);
//        }

        //关联信息处理
        orderRelationService.orderRelationAditOrDel(buttonType, id, currentDO.getMaterialAnalysisCode(), currentDO.getRelatedOrderId());

        if (currentDO.getBizType().equals(1L)) {
            //销售销售订单状态
            erpSaleOrderService.editChildrenOrderCount(currentDO.getRelatedOrderId(), buttonType);
        }


        //处理单据关联的预订任务
        stockBookService.bookReleaseByOrderApprove(buttonType, id);

        //更新状态
        currentDO.setApprovedBy(loginUser.getFullUserName());
        currentDO.setApprovedDt(LocalDateTime.now());
        currentDO.setDataStatus(dataStatus);
        return materialAnalysisMapper.updateById(currentDO);

    }

    /**
     * 分析结果处理
     *
     */
//    public void doAalysisResult2(MaterialAnalysisDO currentDO,Integer dataStatus) {
//        Long materialAnalysisId = currentDO.getMaterialAnalysisId();
//        Long bizType = currentDO.getBizType();
//
//        // 查询bom生产物料
//        List<MaterialAnalysisDetailDO> materialAnalysisDetailDOS = materialAnalysisDetailMapper.selectListByMaterialAnalysisId(materialAnalysisId);
//
//        // bom生产物料需求数量map
//        Map<Long, BigDecimal> bomMap = new HashMap<>();
//        Map<Long, MaterialAnalysisDetailDO> bomDOMap = new HashMap<>();
//
//        // bom生产物料id
//        List<Long> parentMaterialIdList = new ArrayList<>();
//        Set<Long> parentMaterialIdSet = new HashSet<>();
//
//        for (MaterialAnalysisDetailDO item : materialAnalysisDetailDOS) {
//            bomMap.put(item.getMaterialId(), item.getDemandQty());
//            bomDOMap.put(item.getMaterialId(), item);
//
//            parentMaterialIdList.add(item.getMaterialId());
//            parentMaterialIdSet.add(item.getMaterialId());
//        }
//
//        // 审核
//        if (dataStatus == DataButtonEnum.APPROVE.getKey()) {
//
//            //构造每个顶级节点的树结构列表
//            Map<Long, List<MaterialBomTreeBO>> treeMap = new HashMap<>();
//
//            //所有物料id集合
//            Set<Long> materialIdSet = new HashSet<>();
//
//            //所有子物料id集合
//            Set<Long> childMaterialIdSet = new HashSet<>();
//
//            // 1.查询bom树详情
//            for (Long bomMaterialId : parentMaterialIdList) {
//
//                // 添加自己
//                materialIdSet.add(bomMaterialId);
//
//                // 获取bom结构列表数据
//                List<MaterialBomTreeBO> bomList = getBomDate(Arrays.asList(bomMaterialId));
//
//                //构造每个顶级节点的树结构列表
//                treeMap.put(bomMaterialId, bomList);
//
//                //所有非0级物料id集合,校验当前顶级物料是否在其他树节点中
//                Set<Long> childMaterialIds = new HashSet<>();
//
//                for (MaterialBomTreeBO materialBomTreeBO : bomList) {
//                    // 添加所有的子物料
//                    materialIdSet.add(materialBomTreeBO.getMaterialId());
//
//                    // 添加不是0级的物料
//                    if (!"0".equals(materialBomTreeBO.getParentItemId())) {
//                        childMaterialIds.add(materialBomTreeBO.getMaterialId());
//                        childMaterialIdSet.add(materialBomTreeBO.getMaterialId());
//                    }
//                }
//
//                //校验当前顶级物料是否在其他树节点中
//                for (Long parentMaterialId : parentMaterialIdSet) {
//                    boolean b = childMaterialIds.add(parentMaterialId);
//                    if (!b) {
//                        MaterialAnalysisDetailDO materialAnalysisDetailDO = bomDOMap.get(parentMaterialId);
//                        String materialCode = materialAnalysisDetailDO.getMaterialCode();
//                        throw exceptionMsg("数据问题，0级物料在其他0级物料的子物料中，请修复数据："+materialCode);
//                    }
//                }
//            }
//
//            //根据物料id查询对应的物料信息
//            Map<Long, MaterialBO> bomMaterialBOMap = erpBaseService.getDataStatusMaterialByMaterialId(materialIdSet);
//
//            if (bomMaterialBOMap == null || bomMaterialBOMap.isEmpty()) {
//                throw exceptionMsg("数据问题，分析物料或子物料数据有被删除或未审核，请检查数据！");
//            }
//
//            if (bomMaterialBOMap.size() != materialIdSet.size()) {
//                throw exceptionMsg("数据问题，分析物料或子物料数据有被删除或未审核，请检查数据！");
//            }
//
//            //分析参数
//            String analyzeParam = currentDO.getAnalyzeParam();
//            List<Integer> analyzeParamList = new ArrayList<>();
//            if (StrUtilX.isNotEmpty(analyzeParam)) {
//                analyzeParamList = Arrays.stream(analyzeParam.split(",")).map(Integer::parseInt).collect(Collectors.toList());
//            }
//
//            // 2.根据物料id和仓库idList，查询物料的总库存
//            Map<Long, BigDecimal> materialStockMap = getMaterielStock(materialIdSet,childMaterialIdSet, currentDO,analyzeParamList);
//
//            //预估可用数量处理
//            Map<Long, MaterialAnalysisResultDO> estimateQtyMap = processEstimateQty(currentDO,materialStockMap,analyzeParamList);
//
//            // 临时物料库存数map,只是记录不做计算
//            Map<Long, BigDecimal> historyMaterialStockMap = new HashMap<>();
//            for (Map.Entry<Long, BigDecimal> entry : materialStockMap.entrySet()) {
//                historyMaterialStockMap.put(entry.getKey(), entry.getValue());
//            }
//
//            // 3.分析结果对象list，分析统计对象list
//            List<MaterialAnalysisResultDO> resultDOList = new ArrayList<>();
//            List<MaterialAnalysisTotalDO> totalDOList = new ArrayList<>();
//
//            for (Long bomMaterialId : parentMaterialIdList) {
//
//                // 获取每颗树的物料信息
//                List<MaterialBomTreeBO> bomList = treeMap.get(bomMaterialId);
//                if (CollUtilX.isNotEmpty(bomList)) {
//
//                    // 填充子级物料的需求数量
//                    demandQty(bomList, bomMap);
//
//                    // 4.计算每个bom的净需求量，并保存到分析结果对象list和分析统计对象list
//                    computeBomNetDemandQty(bizType, materialAnalysisId, materialStockMap, historyMaterialStockMap, bomList, bomMaterialBOMap, resultDOList, totalDOList,estimateQtyMap);
//
//                } else {
//
//                    MaterialBO materialBO = bomMaterialBOMap.get(bomMaterialId);
//
//                    // 没有物料bom
//                    MaterialAnalysisDetailDO materialAnalysisDetailDO = bomDOMap.get(bomMaterialId);
//
//                    // 构建bom分析统计表
//                    MaterialAnalysisTotalDO materialAnalysisTotalDO = buildNullTotalBomDO(bizType, materialAnalysisDetailDO, materialBO);
//                    totalDOList.add(materialAnalysisTotalDO);
//
//                    // 构建子物料分析统计表
//                    MaterialAnalysisResultDO materialAnalysisResultDO = buildNullResultMaterialDO(bizType, materialAnalysisDetailDO, materialBO);
//                    resultDOList.add(materialAnalysisResultDO);
//                }
//            }
//
//            // 重复物料进行汇总
//            List<MaterialAnalysisTotalDO> newTotalDOList = newTotalDOList(totalDOList);
//
//            // 5.新增分析结果对象
//            materialAnalysisResultMapper.insertBatch(resultDOList);
//
//            // 6.新增分析统计对象
//            for (MaterialAnalysisTotalDO totalDO : newTotalDOList) {
//                totalDO.setPurchaseableQty(totalDO.getNetDemandQty());
//                totalDO.setPurchasedQty(BigDecimal.ZERO);
//                totalDO.setIsMaterialFullPurchased(0);
//                totalDO.setPlanableDemandQty(totalDO.getNetDemandQty());
//                totalDO.setPlannedDemandQty(BigDecimal.ZERO);
//                totalDO.setIsMaterialFullPlaned(0);
//                totalDO.setOutsourcedQty(BigDecimal.ZERO);
//            }
//            materialAnalysisTotalMapper.insertBatch(newTotalDOList);
//        }
//    }

//    /**
//     * 预估可用数量处理
//     *
//     * @param currentDO
//     * @param materialStockMap
//     */
//    private Map<Long, MaterialAnalysisResultDO> processEstimateQty(MaterialAnalysisDO currentDO, Map<Long, BigDecimal> materialStockMap,List<Integer> analyzeParamList) {
//        Map<Long, MaterialAnalysisResultDO> estimateQtyMap = new HashMap<>();
//
//        if (materialStockMap.isEmpty()) {
//            return estimateQtyMap;
//        }
//
//        Set<Long> materialIdSet = materialStockMap.keySet();
//        List<Long> materialIdList = new ArrayList<>(materialIdSet);
//
//        //需求日期
//        boolean isNullDemandDate = currentDO.getDemandDate() == null;
//
//        //查询采购在途
//        Map<Long,PurchaseQtyBO> purchaseQtyMap = new HashMap<>();
//
//        //查询工单在制
//        Map<Long,ProdWorkQtyBO> prodWorkQtyMap = new HashMap<>();
//
//        if (CollUtilX.isNotEmpty(analyzeParamList)){
//
//            //采购在途 todo 需求日期查询条件有问题
//            if (analyzeParamList.contains(AnalyzeParameEnum.PURCHASE_IN_TRANSIT.key)){
//
//                List<Short> inboundStatusList = new ArrayList<>();
//                inboundStatusList.add(InboundStatusEnum.NOT_INBOUND.getCode());
//                inboundStatusList.add(InboundStatusEnum.INBOUNDING.getCode());
//
//                List<Integer> formStatusList = new ArrayList<>();
//                formStatusList.add(FormStatusEnum.NOT_STARTED.getType());
//                formStatusList.add(FormStatusEnum.IN_PROGERSS.getType());
//
//                PurchaseOrderQueryReqVO purchaseOrderQuery = new PurchaseOrderQueryReqVO();
//                purchaseOrderQuery.setInboundStatusList(inboundStatusList);
//                purchaseOrderQuery.setFormStatusList(formStatusList);
//                purchaseOrderQuery.setMaterialIdList(materialIdList);
//
//                if (!isNullDemandDate) {
//                    purchaseOrderQuery.setDemandDate(currentDO.getDemandDate());
//                }
//
//                List<PurchaseQtyBO> purchaseList = purchaseOrderMapper.selectSumQty(purchaseOrderQuery);
//
//                for (PurchaseQtyBO item : purchaseList) {
//                    PurchaseQtyBO purchase = purchaseQtyMap.get(item.getMaterialId());
//                    BigDecimal inboundableQty = EntityUtilX.getBigDecDefault(item.getInboundableQty());
//
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("id", item.getPurchaseOrderId());
//                    jsonObject.put("code", item.getPurchaseOrderCode());
//                    jsonObject.put("qty", inboundableQty);
//
//                    if (purchase == null) {
//                        List<JSONObject> detailList = new ArrayList<>();
//                        detailList.add(jsonObject);
//
//                        purchase = new PurchaseQtyBO();
//                        purchase.setInboundableQty(inboundableQty);
//                        purchase.setDetailList(detailList);
//
//                        purchaseQtyMap.put(item.getMaterialId(), purchase);
//                    }else {
//                        List<JSONObject> detailList = purchase.getDetailList();
//                        detailList.add(jsonObject);
//
//                        BigDecimal inQty = purchase.getInboundableQty().add(inboundableQty);
//                        purchase.setInboundableQty(inQty);
//                        purchase.setDetailList(detailList);
//
//                        purchaseQtyMap.put(item.getMaterialId(), purchase);
//                    }
//                }
//            }
//
//            //工单在制
//            if (analyzeParamList.contains(AnalyzeParameEnum.PURCHASE_IN_TRANSIT.key)){
//
//                List<Integer> workFormStatusList = new ArrayList<>();
//                workFormStatusList.add(OrderStatusEnum.PENDING.getCode());
//                workFormStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());
//
//                ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
//                prodWorkQuery.setFormStatusList(workFormStatusList);
//                prodWorkQuery.setMaterialIdList(materialIdList);
//
//                if (!isNullDemandDate) {
//                    prodWorkQuery.setDemandDate(currentDO.getDemandDate());
//                }
//
//                List<ProdWorkQtyBO> workList = prodWorkMapper.selectSumQty(prodWorkQuery);
//
//                for (ProdWorkQtyBO item : workList) {
//                    ProdWorkQtyBO prodWork = prodWorkQtyMap.get(item.getMaterialId());
//
//                    BigDecimal workPlanTotalQty = EntityUtilX.getBigDecDefault(item.getWorkPlanTotalQty());
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("id", item.getProdWorkId());
//                    jsonObject.put("code", item.getProdWorkCode());
//                    jsonObject.put("qty", workPlanTotalQty);
//
//                    if (prodWork == null) {
//                        List<JSONObject> detailList = new ArrayList<>();
//                        detailList.add(jsonObject);
//
//                        prodWork = new ProdWorkQtyBO();
//                        prodWork.setWorkPlanTotalQty(workPlanTotalQty);
//                        prodWork.setDetailList(detailList);
//
//                        prodWorkQtyMap.put(item.getMaterialId(), prodWork);
//                    }else {
//                        List<JSONObject> detailList = prodWork.getDetailList();
//                        detailList.add(jsonObject);
//
//                        BigDecimal workQty = prodWork.getWorkPlanTotalQty().add(workPlanTotalQty);
//                        prodWork.setWorkPlanTotalQty(workQty);
//                        prodWork.setDetailList(detailList);
//
//                        prodWorkQtyMap.put(item.getMaterialId(), prodWork);
//                    }
//                }
//            }
//        }
//
//        //预估数量处理
//        for (Map.Entry<Long, BigDecimal> entry : materialStockMap.entrySet()) {
//            Long materialId = entry.getKey();
//            BigDecimal stockableQty = entry.getValue();
//
//            PurchaseQtyBO purchaseQtyBO = purchaseQtyMap.get(materialId);
//            ProdWorkQtyBO workQtyBO = prodWorkQtyMap.get(materialId);
//
//            BigDecimal inQty = BigDecimal.ZERO;
//            BigDecimal workQty = BigDecimal.ZERO;
//            List<JSONObject> purDetailList = new ArrayList<>();
//            List<JSONObject> workDetailList = new ArrayList<>();
//
//            if (purchaseQtyBO!=null){
//                inQty = purchaseQtyBO.getInboundableQty();
//                purDetailList = purchaseQtyBO.getDetailList();
//            }
//
//            if (workQtyBO!=null){
//                workQty = workQtyBO.getWorkPlanTotalQty();
//                workDetailList = workQtyBO.getDetailList();
//            }
//
//            if (!analyzeParamList.contains(AnalyzeParameEnum.NEGATIVE_INVENTORY.key) && stockableQty.compareTo(BigDecimal.ZERO)<0){
//                stockableQty = BigDecimal.ZERO;
//            }
//
//            //预估可用数量
//            BigDecimal stockQty = stockableQty.add(inQty).add(workQty);
//
//            MaterialAnalysisResultDO resultDO = new MaterialAnalysisResultDO();
//            resultDO.setMaterialId(materialId);//物料id
//            resultDO.setStockableQty(stockableQty);//可用库存数量
//            resultDO.setPurchaseInQty(inQty);//采购在制
//            resultDO.setWorkOrderInQty(workQty);//工单在制
//            resultDO.setStockQty(stockQty);//预估可用数量
//            resultDO.setAvailableStockQty(stockQty);//实际可用数量
//            resultDO.setPurDetailList(purDetailList);//采购数量明细
//            resultDO.setWorkDetailList(workDetailList);//工单数量明细
//
//            estimateQtyMap.put(materialId, resultDO);
//            materialStockMap.put(materialId, stockQty);
//        }
//
//        return estimateQtyMap;
//
//    }
//
//    private List<MaterialAnalysisTotalDO> newTotalDOList(List<MaterialAnalysisTotalDO> totalDOList) {
//        Map<Long, BigDecimal> netDemandQtyMap = new HashMap<>();
//        List<MaterialAnalysisTotalDO> newTotalDOList = new ArrayList<>();
//        Set<Long> materialIdSet = new HashSet<>();
//
//        for (MaterialAnalysisTotalDO item : totalDOList) {
//            BigDecimal bigDecimal = netDemandQtyMap.get(item.getMaterialId());
//            if (bigDecimal == null) {
//                bigDecimal = BigDecimal.ZERO;
//            }
//
//            BigDecimal demandQty = item.getNetDemandQty();
//            bigDecimal = bigDecimal.add(demandQty);
//
//            netDemandQtyMap.put(item.getMaterialId(), bigDecimal);
//
//            if (materialIdSet.add(item.getMaterialId())) {
//                newTotalDOList.add(item);
//            }
//        }
//
//        Iterator<MaterialAnalysisTotalDO> iterator = newTotalDOList.iterator();
//        while (iterator.hasNext()) {
//            MaterialAnalysisTotalDO next = iterator.next();
//            BigDecimal netDemandQty = netDemandQtyMap.get(next.getMaterialId());
//            int comparisonResult = netDemandQty.compareTo(BigDecimal.ZERO); // 与零进行比较
//            if (comparisonResult == 0) {
//                // 净需求数量等于0移除
//                iterator.remove();
//            } else {
//                // 净需求数量大于0更新
//                next.setNetDemandQty(netDemandQty);
//            }
//        }
//
//        // 生成行号
//        Map<Long, Integer> longIntegerMap = new HashMap<>();
//        for (MaterialAnalysisTotalDO item : newTotalDOList) {
//            Long materialSourceDictId = item.getMaterialSourceDictId();
//            Integer rowNo = longIntegerMap.get(materialSourceDictId);
//            if (rowNo == null){
//                rowNo = 1;
//            }
//            item.setRowNo(rowNo);
//            rowNo++;
//            longIntegerMap.put(materialSourceDictId,rowNo);
//        }
//        return newTotalDOList;
//    }
//
//    private MaterialAnalysisTotalDO buildNullTotalBomDO(Long bizType, MaterialAnalysisDetailDO materialAnalysisDetailDO, MaterialBO materialBO) {
//
//        List<MaterialAnalysisTotalDO> list = new ArrayList<>();
//
//        int rowNo = 1;
//
//        MaterialAnalysisTotalDO totalDO = new MaterialAnalysisTotalDO();
//        totalDO.setBizType(bizType);
//        totalDO.setMaterialId(materialAnalysisDetailDO.getMaterialId());
//        totalDO.setMaterialAnalysisId(materialAnalysisDetailDO.getMaterialAnalysisId());
//        totalDO.setRowNo(rowNo);
//        totalDO.setNetDemandQty(materialAnalysisDetailDO.getDemandQty());
//        totalDO.setPriority(1);
//        totalDO.setIsBom(1);
//
//        totalDO.setMaterialCode(materialAnalysisDetailDO.getMaterialCode());
//
//        totalDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//        totalDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//        totalDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//        list.add(totalDO);
//
//        return totalDO;
//
//    }
//
//    private MaterialAnalysisResultDO buildNullResultMaterialDO(Long bizType, MaterialAnalysisDetailDO materialAnalysisTotalDO, MaterialBO materialBO) {
//        MaterialAnalysisResultDO totalDO = new MaterialAnalysisResultDO();
//        totalDO.setBizType(bizType);
//        totalDO.setMaterialId(materialAnalysisTotalDO.getMaterialId());
//        totalDO.setMaterialAnalysisCode(materialAnalysisTotalDO.getMaterialAnalysisCode());
//        totalDO.setMaterialAnalysisId(materialAnalysisTotalDO.getMaterialAnalysisId());
//        totalDO.setRowNo(1);
//        totalDO.setParentId(0L);
//        totalDO.setDemandQty(materialAnalysisTotalDO.getDemandQty());
//        totalDO.setNetDemandQty(materialAnalysisTotalDO.getDemandQty());
//        totalDO.setMaterialCode(materialAnalysisTotalDO.getMaterialCode());
//
//        totalDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//        totalDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//        totalDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//        return totalDO;
//
//    }
//
//    /**
//     * 填充子级物料的需求数量
//     *
//     * @param bomList
//     * @param bomMap
//     */
//    private void demandQty(List<MaterialBomTreeBO> bomList, Map<Long, BigDecimal> bomMap) {
//        for (MaterialBomTreeBO materialBomTreeBO : bomList) {
//            if ("0".equals(materialBomTreeBO.getParentItemId())) {
//                BigDecimal bigDecimal = bomMap.get(materialBomTreeBO.getMaterialId());
//                materialBomTreeBO.setDemandQty(bigDecimal);
//            }
//        }
//    }
//
//    private void doDemandQty(List<MaterialBomTreeBO> bomList, Map<Long, BigDecimal> bomMap) {
//        for (MaterialBomTreeBO materialBomTreeBO : bomList) {
//            if (materialBomTreeBO.getIsParent() == 1) {
//                BigDecimal bigDecimal = bomMap.get(materialBomTreeBO.getMaterialId());
//                materialBomTreeBO.setDemandQty(bigDecimal);
//            }
//
//            if (CollUtilX.isNotEmpty(materialBomTreeBO.getChildren())) {
//                doDemandQty(materialBomTreeBO.getChildren(), bomMap);
//            }
//        }
//    }

//    /**
//     *
//     *
//     * @param bizType 物料分析业务类型
//     * @param materialAnalysisId 物料分析id
//     * @param materialStockMap 库存map
//     * @param historyMaterialStockMap 历史库存map
//     * @param bomList 一个0级和所有子级的物料 集合
//     * @param bomMaterialBOMap 物料基本信息 集合
//     * @param resultDOList 分析结果 集合
//     * @param totalDOList  汇总结果 集合
//     */
//    private void computeBomNetDemandQty(Long bizType,
//                                        Long materialAnalysisId,
//                                        Map<Long, BigDecimal> materialStockMap,
//                                        Map<Long, BigDecimal> historyMaterialStockMap,
//                                        List<MaterialBomTreeBO> bomList,
//                                        Map<Long, MaterialBO> bomMaterialBOMap,
//                                        List<MaterialAnalysisResultDO> resultDOList,
//                                        List<MaterialAnalysisTotalDO> totalDOList,
//                                        Map<Long, MaterialAnalysisResultDO> estimateQtyMap) {
//
//        // 顶级的bom
//        List<Long> topBomList = new ArrayList<>();
//        // 中间层 根节点 bom
//        Set<Long> middleBomList = new HashSet<>();
//        // 所有的子物料id
//        Set<Long> childlList = new HashSet<>();
//
//
//        // 物料对象，k：物料id，v：物料对象
//        Map<Long, MaterialBomTreeBO> materialBOMap = new HashMap<>();
//        // 顶级bom的净需求数量,k：物料id，v：净需求数量
//        Map<Long, BigDecimal> topNetDemandQtyMap = new HashMap<>();
//        // 所有物料的净需求数量,k：物料id，v：净需求数量
//        Map<Long, BigDecimal> netDemandQtyMap = new HashMap<>();
//
//        //树元素
//        for (MaterialBomTreeBO materialBomTreeBO : bomList) {
//
//            materialBOMap.put(materialBomTreeBO.getMaterialId(), materialBomTreeBO);
//            netDemandQtyMap.put(materialBomTreeBO.getMaterialId(), BigDecimal.ZERO);
//
//            if ("0".equals(materialBomTreeBO.getParentItemId())) {
//                // 顶级bom的净需求数量
//                topNetDemandQtyMap.put(materialBomTreeBO.getMaterialId(), materialBomTreeBO.getDemandQty());
//                // 顶级bom
//                topBomList.add(materialBomTreeBO.getMaterialId());
//            } else {
//
//                if (materialBomTreeBO.getIsParent() == 0) {
//                    // 子物料
//                    childlList.add(materialBomTreeBO.getMaterialId());
//                } else {
//                    middleBomList.add(materialBomTreeBO.getMaterialId());
//                }
//            }
//        }
//
//        // 将bom转成树形结构
//        List<MaterialBomTreeBO> bomTree = TreeUtilX.listToTree(bomList);
//
//        //处理物料分析结果
//        for (MaterialBomTreeBO topBomBO : bomTree) {
//            // 先处理顶级的bom，第一级
//            doTopBom(bizType, materialAnalysisId, topBomBO, materialStockMap, bomMaterialBOMap, historyMaterialStockMap, netDemandQtyMap, resultDOList,estimateQtyMap);
//        }
//
//        // 构建顶层bom分析统计表
//        List<MaterialAnalysisTotalDO> totalDOList2 = buildTotalTopBomDO(bizType, topBomList, materialAnalysisId, materialBOMap, bomMaterialBOMap, topNetDemandQtyMap);
//        totalDOList.addAll(totalDOList2);
//
//        // 构建中间层bom分析统计表
//        List<MaterialAnalysisTotalDO> totalDOList3 = buildTotalMiddleBomDO(bizType, middleBomList, materialAnalysisId, materialBOMap, bomMaterialBOMap, netDemandQtyMap, bomTree);
//        totalDOList.addAll(totalDOList3);
//
//        // 构建子物料分析统计表
//        List<MaterialAnalysisTotalDO> totalDOList4 = buildTotalMaterialDO(bizType, childlList, materialAnalysisId, materialBOMap, bomMaterialBOMap, netDemandQtyMap, bomTree);
//        totalDOList.addAll(totalDOList4);
//
//
//    }

//    private List<MaterialAnalysisTotalDO> buildTotalMaterialDO(Long bizType,
//                                                               Set<Long> childlList,
//                                                               Long materialAnalysisId,
//                                                               Map<Long, MaterialBomTreeBO> materialBOMap,
//                                                               Map<Long, MaterialBO> bomMaterialBOMap,
//                                                               Map<Long, BigDecimal> netDemandQtyMap,
//                                                               List<MaterialBomTreeBO> bomTree) {
//
//        List<MaterialAnalysisTotalDO> list = new ArrayList<>();
//        int rowNo = 0;
//        for (Long materialId : childlList) {
//            rowNo++;
//            BigDecimal netDemandQty = netDemandQtyMap.get(materialId);
//            MaterialBomTreeBO materialBomTreeBO = materialBOMap.get(materialId);
//
//            // 优先级=树的深度
//            int priority = TreeUtilX.getMaxLevelByCode(bomTree, materialId + "");
//            priority = priority + 1;
//            MaterialAnalysisTotalDO totalDO = new MaterialAnalysisTotalDO();
//            totalDO.setBizType(bizType);
//            totalDO.setMaterialId(materialId);
//            totalDO.setMaterialAnalysisId(materialAnalysisId);
//            totalDO.setRowNo(rowNo);
//            totalDO.setNetDemandQty(netDemandQty);
//            totalDO.setPriority(priority);
//            totalDO.setIsBom(0);
//            totalDO.setMaterialCode(materialBomTreeBO.getMaterialCode());
//
//            MaterialBO materialBO = bomMaterialBOMap.get(materialId);
//            if (materialBO != null) {
//                totalDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//                totalDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//                totalDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//            }
//            list.add(totalDO);
//        }
//        return list;
//    }
//
//
//    private List<MaterialAnalysisTotalDO> buildTotalTopBomDO(Long bizType,
//                                                             List<Long> topBomlList,
//                                                             Long materialAnalysisId,
//                                                             Map<Long, MaterialBomTreeBO> materialBOMap,
//                                                             Map<Long, MaterialBO> bomMaterialBOMap,
//                                                             Map<Long, BigDecimal> netDemandQtyMap) {
//
//        List<MaterialAnalysisTotalDO> list = new ArrayList<>();
//        for (int i = 0; i < topBomlList.size(); i++) {
//
//            int rowNo = i + 1;
//            Long materialId = topBomlList.get(i);
//
//            BigDecimal netDemandQty = netDemandQtyMap.get(materialId);
//            MaterialBomTreeBO materialBomTreeBO = materialBOMap.get(materialId);
//
//            MaterialAnalysisTotalDO totalDO = new MaterialAnalysisTotalDO();
//            totalDO.setBizType(bizType);
//            totalDO.setMaterialId(materialId);
//            totalDO.setMaterialAnalysisId(materialAnalysisId);
//            totalDO.setRowNo(rowNo);
//            totalDO.setNetDemandQty(netDemandQty);
//            totalDO.setPriority(1);
//            totalDO.setIsBom(1);
//
//            totalDO.setMaterialCode(materialBomTreeBO.getMaterialCode());
//            MaterialBO materialBO = bomMaterialBOMap.get(materialId);
//            if (materialBO != null) {
//                totalDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//                totalDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//                totalDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//            }
//            list.add(totalDO);
//        }
//
//        return list;
//    }
//
//
//    private List<MaterialAnalysisTotalDO> buildTotalMiddleBomDO(Long bizType,
//                                                                Set<Long> middleBomList,
//                                                                Long materialAnalysisId,
//                                                                Map<Long, MaterialBomTreeBO> materialBOMap,
//                                                                Map<Long, MaterialBO> bomMaterialBOMap,
//                                                                Map<Long, BigDecimal> netDemandQtyMap,
//                                                                List<MaterialBomTreeBO> bomTree) {
//
//        List<MaterialAnalysisTotalDO> list = new ArrayList<>();
//
//        int rowNo = 0;
//        for (Long materialId : middleBomList) {
//
//            rowNo++;
//            int priority = TreeUtilX.getMaxLevelByCode(bomTree, materialId + "");
//            priority = priority + 1;
//
//            BigDecimal netDemandQty = netDemandQtyMap.get(materialId);
//            MaterialBomTreeBO materialBomTreeBO = materialBOMap.get(materialId);
//            MaterialAnalysisTotalDO totalDO = new MaterialAnalysisTotalDO();
//            totalDO.setBizType(bizType);
//            totalDO.setMaterialId(materialId);
//            totalDO.setMaterialAnalysisId(materialAnalysisId);
//            totalDO.setRowNo(rowNo);
//            totalDO.setNetDemandQty(netDemandQty);
//            totalDO.setPriority(priority);
//            totalDO.setIsBom(0);
//
//            totalDO.setMaterialCode(materialBomTreeBO.getMaterialCode());
//            MaterialBO materialBO = bomMaterialBOMap.get(materialId);
//            if (materialBO != null) {
//                totalDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//                totalDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//                totalDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//            }
//            list.add(totalDO);
//        }
//
//        return list;
//    }


//    /**
//     * 处理子bom物料分析结果
//     *
//     * @param child                   当前bom对象
//     * @param parentDO                父级对象
//     * @param materialStockMap        库存map
//     * @param historyMaterialStockMap 历史库存map
//     * @param netDemandQtyMap         净需求数量map
//     * @param resultDOList            分析结果list
//     */
//    private void doChildBom(Long bizType,
//                            MaterialBomTreeBO child,
//                            MaterialAnalysisResultDO parentDO,
//                            Map<Long, BigDecimal> materialStockMap,
//                            Map<Long, BigDecimal> historyMaterialStockMap,
//                            Map<Long, MaterialBO> bomMaterialBOMap,
//                            Map<Long, BigDecimal> netDemandQtyMap,
//                            List<MaterialAnalysisResultDO> resultDOList,
//                            Map<Long, MaterialAnalysisResultDO> estimateQtyMap) {
//
//        // 构建bomBO
//        MaterialAnalysisResultDO resultDO = buildRootBomResultDO2(bizType, child, parentDO, materialStockMap, historyMaterialStockMap, netDemandQtyMap, bomMaterialBOMap);
//
//        // 添加到分析结果里
//        resultDOList.add(resultDO);
//
//        // 处理当前bom的子物料
//        List<MaterialBomTreeBO> children = child.getChildren();
//        for (MaterialBomTreeBO item : children) {
//            if (CollUtilX.isEmpty(item.getChildren())) {
//                doChildMaterial(bizType, item, resultDO, materialStockMap, historyMaterialStockMap, bomMaterialBOMap, netDemandQtyMap, resultDOList,estimateQtyMap);
//            }
//        }
//
//        // 处理当前bom的子bom
//        for (MaterialBomTreeBO item : children) {
//            if (CollUtilX.isNotEmpty(item.getChildren())) {
//                doChildBom(bizType, item, resultDO, materialStockMap, historyMaterialStockMap, bomMaterialBOMap, netDemandQtyMap, resultDOList,estimateQtyMap);
//            }
//        }
//    }

//    /**
//     * 处理子物料分析结果
//     *
//     * @param child                   当前物料对象
//     * @param parentDO                父级对象
//     * @param materialStockMap        库存map
//     * @param historyMaterialStockMap 历史库存map
//     * @param netDemandQtyMap         净需求数量map
//     * @param resultDOList            分析结果list
//     */
//    private void doChildMaterial(Long bizType,
//                                 MaterialBomTreeBO child,
//                                 MaterialAnalysisResultDO parentDO,
//                                 Map<Long, BigDecimal> materialStockMap,
//                                 Map<Long, BigDecimal> historyMaterialStockMap,
//                                 Map<Long, MaterialBO> bomMaterialBOMap,
//                                 Map<Long, BigDecimal> netDemandQtyMap,
//                                 List<MaterialAnalysisResultDO> resultDOList,
//                                 Map<Long, MaterialAnalysisResultDO> estimateQtyMap) {
//
//        // 父级物料的净需求数量
//        BigDecimal bomDemandQty = parentDO.getNetDemandQty();
//
//        Long pid = parentDO.getMaterialAnalysisResultId();
//        Long materialAnalysisId = parentDO.getMaterialAnalysisId();
//
//        Long materialId = child.getMaterialId();
//        String materialCode = child.getMaterialCode();
//        BigDecimal oneDemandQty = child.getDemandQty();
//        BigDecimal lossRate = child.getLossRate();
//        BigDecimal estimatedQty = child.getEstimatedQty();
//        Long materialSourceDictId = child.getMaterialSourceDictId();
//
//        // 子级 预估总数量 = 父级净需求数量 * 子级物料的预估数量
//        BigDecimal estimatedTotalQty = bomDemandQty.multiply(estimatedQty);
//
//        // 当前物料库存
//        BigDecimal stockQty = materialStockMap.get(materialId);
//        BigDecimal historyStockQty = historyMaterialStockMap.get(materialId);
//
//        //预估数量
//        MaterialAnalysisResultDO resultDO = estimateQtyMap.get(materialId);
//
//        // 可用库存 = 当前库存
//        BigDecimal availableStockQty = stockQty;
//
//        // 净需求数量
//        BigDecimal netDemandQty = BigDecimal.ZERO;
//
//        // 剩余库存 = 库存数量 - 预估总量
//        BigDecimal residualStockQty = availableStockQty.subtract(estimatedTotalQty);
//        int comparisonResult = residualStockQty.compareTo(BigDecimal.ZERO); // 与零进行比较
//
//        // 更新当前物料的最新库存
//        if (comparisonResult < 0) {
//            // 剩余库存 < 0 ，库存不够
//
//            // 子级净需求数量 = 负数转换为正数
//            netDemandQty = residualStockQty.negate();
//
//            // 剩余库存< 0 ，剩余库存归0
//            materialStockMap.put(materialId, BigDecimal.ZERO);
//        } else {
//            // 剩余库存 >= 0，库存足够
//            materialStockMap.put(materialId, residualStockQty);
//        }
//
//        // 累积计算相同物料的净需求数量
//        BigDecimal bigDecimal = netDemandQtyMap.get(materialId);
//        BigDecimal add = netDemandQty.add(bigDecimal);
//        netDemandQtyMap.put(materialId, add);
//
//        // 创建分析结果对象
//        MaterialAnalysisResultDO newDO = new MaterialAnalysisResultDO();
//        newDO.setParentId(pid);
//        newDO.setMaterialAnalysisResultId(IDUtilX.getId());
//        newDO.setMaterialAnalysisId(materialAnalysisId);
//        newDO.setBizType(bizType);
//
//        newDO.setMaterialId(materialId);
//        newDO.setMaterialCode(materialCode);
//        newDO.setMaterialSourceDictId(materialSourceDictId);// 物料来源类型
//
//        newDO.setOneDemandQty(oneDemandQty);// 单个需求数量
//        newDO.setLossRate(lossRate);// 损耗率
//        newDO.setEstimatedQty(estimatedQty);// 预估数量
//        newDO.setEstimatedTotalQty(estimatedTotalQty);// 预估总数量
//        newDO.setStockQty(historyStockQty);// 预估可用数量
//        newDO.setAvailableStockQty(availableStockQty);// 实际可用数量
//        newDO.setNetDemandQty(netDemandQty);// 净需求数量
//
//        newDO.setStockableQty(resultDO.getStockableQty());// 可用库存数量
//        newDO.setPurchaseInQty(resultDO.getPurchaseInQty());// 采购在制
//        newDO.setWorkOrderInQty(resultDO.getWorkOrderInQty());// 工单在制
//        newDO.setPurDetailList(resultDO.getPurDetailList());// 采购数量明细
//        newDO.setWorkDetailList(resultDO.getWorkDetailList());// 工单数量明细
//
//
//        MaterialBO materialBO = bomMaterialBOMap.get(materialId);
//        if (materialBO != null) {
//            newDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//            newDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//            newDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//        }
//        resultDOList.add(newDO);
//    }


//    private MaterialAnalysisResultDO buildRootBomResultDO2(Long bizType,
//                                                           MaterialBomTreeBO bomBO,
//                                                           MaterialAnalysisResultDO parentDO,
//                                                           Map<Long, BigDecimal> materialStockMap,
//                                                           Map<Long, BigDecimal> historyMaterialStockMap,
//                                                           Map<Long, BigDecimal> netDemandQtyMap,
//                                                           Map<Long, MaterialBO> bomMaterialBOMap) {
//
//        // 父级物料的净需求数量
//        BigDecimal bomDemandQty = parentDO.getNetDemandQty();
//
//        Long pid = parentDO.getMaterialAnalysisResultId();
//        Long materialAnalysisId = parentDO.getMaterialAnalysisId();
//
//        Long materialId = bomBO.getMaterialId();
//        String materialCode = bomBO.getMaterialCode();
//        BigDecimal oneDemandQty = bomBO.getDemandQty();
//        BigDecimal lossRate = bomBO.getLossRate();
//        BigDecimal estimatedQty = bomBO.getEstimatedQty();
//
//
//        // 预估总数量 = bom的净需求数量 * 物料的预估数量
//        BigDecimal estimatedTotalQty = bomDemandQty.multiply(estimatedQty);
//
//        // 当前物料库存
//        BigDecimal stockQty = materialStockMap.get(materialId);
//        BigDecimal historyStockQty = historyMaterialStockMap.get(materialId);
//
//        // 可用库存 = 当前库存
//        BigDecimal availableStockQty = stockQty;
//
//        // 净需求数量
//        BigDecimal netDemandQty = BigDecimal.ZERO;
//        // 剩余库存 = 库存数量 - 预估总量
//        BigDecimal residualStockQty = availableStockQty.subtract(estimatedTotalQty);
//        int comparisonResult = residualStockQty.compareTo(BigDecimal.ZERO); // 与零进行比较
//
//        // 更新当前物料的最新库存
//        materialStockMap.put(materialId, residualStockQty);
//        if (comparisonResult < 0) {
//            // 剩余库存 < 0 ，库存不够
//
//            // 净需求数量 = 负数转换为正数
//            netDemandQty = residualStockQty.negate();
//
//            // 剩余库存< 0 ，剩余库存归0
//            materialStockMap.put(materialId, BigDecimal.ZERO);
//        } else {
//            // 剩余库存 >= 0，库存足够
//            materialStockMap.put(materialId, residualStockQty);
//        }
//
//        // 累积计算相同物料的净需求数量
//        BigDecimal bigDecimal = netDemandQtyMap.get(materialId);
//        BigDecimal add = netDemandQty.add(bigDecimal);
//        netDemandQtyMap.put(materialId, add);
//
//        MaterialAnalysisResultDO newDO = new MaterialAnalysisResultDO();
//        newDO.setParentId(pid);
//        newDO.setBizType(bizType);
//        newDO.setMaterialAnalysisResultId(IDUtilX.getId());
//        newDO.setMaterialAnalysisId(materialAnalysisId);
//        newDO.setMaterialId(materialId);
//        newDO.setMaterialCode(materialCode);
//        newDO.setOneDemandQty(oneDemandQty);// 单个需求数量
//        newDO.setLossRate(lossRate);// 损耗率
//        newDO.setEstimatedQty(estimatedQty);// 预估数量
//        newDO.setEstimatedTotalQty(estimatedTotalQty);// 预估总数量
//        newDO.setStockQty(historyStockQty);// 历史库存
//        newDO.setAvailableStockQty(availableStockQty);// 可用库存
//        newDO.setNetDemandQty(netDemandQty);// 净需求数量
//
//        MaterialBO materialBO = bomMaterialBOMap.get(materialId);
//        if (materialBO != null) {
//            newDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//            newDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//            newDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//        }
//        return newDO;
//    }

//    private MaterialAnalysisResultDO buildRootBomResultDO(Long bizType,
//                                                          Long pid,
//                                                          Long materialAnalysisId,
//                                                          MaterialBomTreeBO bomBO,
//                                                          Map<Long, MaterialBO> bomMaterialBOMap) {
//        MaterialAnalysisResultDO bomDO = new MaterialAnalysisResultDO();
//        bomDO.setParentId(pid);
//        bomDO.setBizType(bizType);
//        bomDO.setMaterialAnalysisResultId(IDUtilX.getId());
//        bomDO.setMaterialAnalysisId(materialAnalysisId);
//        bomDO.setMaterialId(bomBO.getMaterialId());
//        bomDO.setMaterialCode(bomBO.getMaterialCode());
//        bomDO.setDemandQty(bomBO.getDemandQty());
//        bomDO.setNetDemandQty(bomBO.getDemandQty());
//
//        MaterialBO materialBO = bomMaterialBOMap.get(bomBO.getMaterialId());
//        if (materialBO != null) {
//            bomDO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
//            bomDO.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
//            bomDO.setMainUnitDictId(materialBO.getMainUnitDictId());
//        }
//        return bomDO;
//    }

//    /**
//     * 处理物料分析结果
//     *
//     * @param materialAnalysisId      物料分析id
//     * @param topBomBO                顶级bom对象
//     * @param materialStockMap        库存map
//     * @param historyMaterialStockMap 历史库存map
//     * @param netDemandQtyMap         净需求数量map
//     * @param resultDOList            分析结果list
//     */
//    private void doTopBom(Long bizType,
//                          Long materialAnalysisId,
//                          MaterialBomTreeBO topBomBO,
//                          Map<Long, BigDecimal> materialStockMap,
//                          Map<Long, MaterialBO> bomMaterialBOMap,
//                          Map<Long, BigDecimal> historyMaterialStockMap,
//                          Map<Long, BigDecimal> netDemandQtyMap,
//                          List<MaterialAnalysisResultDO> resultDOList,
//                          Map<Long, MaterialAnalysisResultDO> estimateQtyMap) {
//
//
//        // 构建顶级bomBO，pid传0
//        MaterialAnalysisResultDO bomDO = buildRootBomResultDO(bizType, 0L, materialAnalysisId, topBomBO, bomMaterialBOMap);
//
//        // 添加到分析结果表里
//        resultDOList.add(bomDO);
//
//        // 处理当前bom的子物料
//        List<MaterialBomTreeBO> children = topBomBO.getChildren();
//        for (MaterialBomTreeBO child : children) {
//            if (CollUtilX.isEmpty(child.getChildren())) {
//                doChildMaterial(bizType, child, bomDO, materialStockMap, historyMaterialStockMap, bomMaterialBOMap, netDemandQtyMap, resultDOList,estimateQtyMap);
//            }
//        }
//
//        // 处理当前bom的子bom
//        for (MaterialBomTreeBO child : children) {
//            if (CollUtilX.isNotEmpty(child.getChildren())) {
//                doChildBom(bizType, child, bomDO, materialStockMap, historyMaterialStockMap, bomMaterialBOMap, netDemandQtyMap, resultDOList,estimateQtyMap);
//            }
//        }
//    }

//    /**
//     * 根据物料和仓库查库存
//     *
//     * @param materialIdList
//     * @param currentDO
//     * @return
//     */
//    private Map<Long, BigDecimal> getMaterielStock(Collection<Long> materialIdList,Set<Long> childMaterialIdSet,MaterialAnalysisDO currentDO,List<Integer> analyzeParamList) {
//
//        String analysisScope = currentDO.getAnalysisScope();
//        List<String> orgIdList = StrUtilX.stringToList(analysisScope);
//
//        Map<Long, BigDecimal> stockQtyMap = new HashMap<>();
//
//        // 没查到，直接返回map
//        if (CollUtilX.isEmpty(materialIdList)) {
//            return stockQtyMap;
//        }
//
//        // 初始化所有库存是0
//        for (Long materialId : materialIdList) {
//            stockQtyMap.put(materialId, BigDecimal.ZERO);
//        }
//
//        Long bizType = currentDO.getBizType();
//        Long relatedOrderId = currentDO.getRelatedOrderId();
//
//        //普通物料分析 查询库存
//        if(bizType.equals(MaterialAnalysisEnum.STANDARD_MATERIAL.key)) {
//
//            // 查询所有物料对应仓库的库存
//            List<ErpMaterialStockDO> list = erpMaterialStockMapper.selectListByMaterialIdAndOrgId(materialIdList, orgIdList);
//
//            // 没查到，直接返回map
//            if (CollUtilX.isEmpty(list)) {
//                return stockQtyMap;
//            }
//
//            for (ErpMaterialStockDO item : list) {
//                BigDecimal stockQty = EntityUtilX.getBigDecDefault(item.getStockQty());
//                BigDecimal lockedQty = EntityUtilX.getBigDecDefault(item.getLockedQty());
//                BigDecimal availableQty = stockQty.subtract(lockedQty);
//                BigDecimal bigDecimal = stockQtyMap.get(item.getMaterialId());
//                if (availableQty != null) {
//                    BigDecimal add = bigDecimal.add(availableQty);
//                    stockQtyMap.put(item.getMaterialId(), add);
//                }
//            }
//
//            return stockQtyMap;
//        }
//
//        //考虑预定
//        boolean book = bizType.equals(MaterialAnalysisEnum.SALES_MATERIAL.key) || bizType.equals(MaterialAnalysisEnum.PRODUCTION_MATERIAL.key);
//
//        if (book && analyzeParamList.contains(AnalyzeParameEnum.CONSIDERED_RESERVATION.key)) {
//            List<Long> materialAllIdList = new ArrayList<>();
//
//            //生产物料分析,查询生产订单
//            if(bizType.equals(MaterialAnalysisEnum.PRODUCTION_MATERIAL.key)) {
//                List<Long> prodMaterialIdList = erpProdOrderDetailMapper.selectMaterialIdList(relatedOrderId,null);
//                materialAllIdList.addAll(prodMaterialIdList);
//            }
//
//            //销售物料分析
//            if(bizType.equals(MaterialAnalysisEnum.SALES_MATERIAL.key)) {
//                List<Long> prodMaterialIdList = erpProdOrderDetailMapper.selectMaterialIdList(null,relatedOrderId);
//                List<Long> saleMaterialIdList = erpSaleOrderDetailMapper.selectMaterialIdList(relatedOrderId);
//
//                materialAllIdList.addAll(prodMaterialIdList);
//                materialAllIdList.addAll(saleMaterialIdList);
//            }
//
//            // 计算交集
//            childMaterialIdSet.retainAll(materialAllIdList);
//
//            if (CollUtilX.isEmpty(childMaterialIdSet)) {
//                return stockQtyMap;
//            }
//
//            //查询计算预定单之后的可用库存
//            for (Long materialId : childMaterialIdSet){
//                for (String orgId : orgIdList){
//                    BigDecimal stockAleQty = EntityUtilX.getBigDecDefault(stockBookService.getAvailableQty(relatedOrderId, materialId, orgId));
//
//                    BigDecimal availableQty = stockQtyMap.get(materialId);
//                    if (availableQty != null) {
//                        BigDecimal calQty = availableQty.add(stockAleQty);
//                        stockQtyMap.put(materialId, calQty);
//                    }else {
//                        stockQtyMap.put(materialId, stockAleQty);
//                    }
//                }
//            }
//        }
//
//        return stockQtyMap;
//    }
//
//
//    private List<MaterialBomTreeBO> getBomDate(List<Long> materialIdList) {
//
//        MaterialBomQueryReqVO reqVO = new MaterialBomQueryReqVO();
//        reqVO.setMaterialIdList(materialIdList);
//        List<MaterialBomTreeBO> allMaterialBomList = materialBomService.findAllMaterialBomList(reqVO);
//
//        return allMaterialBomList;
//    }

}