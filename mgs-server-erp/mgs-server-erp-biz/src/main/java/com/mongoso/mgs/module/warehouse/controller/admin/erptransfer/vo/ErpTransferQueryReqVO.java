package com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 调拨单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ErpTransferQueryReqVO extends CommonParam{

    /** 调拨单ID */
    private Long transferId;

    /** 调拨单号 */
    private String transferCode;

    /** 关联单据ID */
    private Long relatedOrderId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 调拨单类型ID */
    private String transferTypeDictId;

    /** 调出仓库ID */
    private String transferOutOrgId;

    /** 调入仓库ID */
    private String transferInOrgId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审核状态 */
    private Integer dataStatus;

}
