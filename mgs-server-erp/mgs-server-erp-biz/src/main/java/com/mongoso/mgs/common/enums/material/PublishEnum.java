package com.mongoso.mgs.common.enums.material;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON>
 * @date： 2024/11/20
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum PublishEnum implements Serializable {

    PUBLISH(1, "上架"),
    PUBLISHING(2, "上架中"),
    NOT_PUBLISH(0, "下架");
    public final Integer key;
    public final String value;

}
