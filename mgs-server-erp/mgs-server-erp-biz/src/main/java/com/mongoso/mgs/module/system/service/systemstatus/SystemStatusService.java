package com.mongoso.mgs.module.system.service.systemstatus;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.system.controller.admin.systemstatus.vo.SystemStatusAditReqVO;
import com.mongoso.mgs.module.system.controller.admin.systemstatus.vo.SystemStatusPageReqVO;
import com.mongoso.mgs.module.system.controller.admin.systemstatus.vo.SystemStatusQueryReqVO;
import com.mongoso.mgs.module.system.controller.admin.systemstatus.vo.SystemStatusRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 系统状态 Service 接口
 *
 * <AUTHOR>
 */
public interface SystemStatusService {

    /**
     * 创建系统状态
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Short systemStatusAdd(@Valid SystemStatusAditReqVO reqVO);

    /**
     * 更新系统状态
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Short systemStatusEdit(@Valid SystemStatusAditReqVO reqVO);

    /**
     * 删除系统状态
     *
     * @param id 编号
     */
    void systemStatusDel(Short id);

    /**
     * 获得系统状态信息
     *
     * @return 系统状态信息
     */
    SystemStatusRespVO systemStatusDetail();

    /**
     * 获得系统状态列表
     *
     * @param reqVO 查询条件
     * @return 系统状态列表
     */
    List<SystemStatusRespVO> systemStatusList(@Valid SystemStatusQueryReqVO reqVO);

    /**
     * 获得系统状态分页
     *
     * @param reqVO 查询条件
     * @return 系统状态分页
     */
    PageResult<SystemStatusRespVO> systemStatusPage(@Valid SystemStatusPageReqVO reqVO);

}
