package com.mongoso.mgs.module.produce.controller.admin.mold.vo.mold;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 模具台账 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MoldBaseVO implements Serializable {

    /** 主键ID */
    private Long moldId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 模具编码 */
    private String moldCode;

    /** 模具名称 */
    private String moldName;

    /** 模具来源 */
    private String moldSourceDictId;
    private String moldSourceDictName;

    /** 模具类型 */
    private String moldTypeDictId;
    private String moldTypeDictName;

    /** 模具型号 */
    private String moldModel;

    /** 模具图号 */
    private String moldNo;

    /** 存放位置 */
    private String moldPosition;

    /** 客户id */
    private Long customerId;
    private String customerName;

    /** 购入时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate purchaseDate;

    /** 库存状态 */
    private Long stockStatus;

    /** 模具状态 */
    private Long moldStatus;

    /** 备注 */
    private String remark;

    /** 审核状态 */
    private Integer dataStatus;

    /** 加工模数 */
    private BigDecimal processMod;
    /** 穴数 */
    private BigDecimal cavity;

//    /** 模具参数 */
//    private JSONObject moldParam;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

    /** 存放位置["对内","对外"] */
    private Integer locationType;

    /** 位置 */
    private Long locationId;
    private String locationName;


    /** 长度 */
    private BigDecimal length;

    /** 步距 */
    private BigDecimal step;

    /** 宽度 */
    private BigDecimal width;

    /** 高度 */
    private BigDecimal height;

    /** 预计寿命 */
    private BigDecimal expectedLife;

}
