package com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 单据引用 ReqVO
 *
 * <AUTHOR>
 */
@Data
public class ErpReceiptQuoteReqVO {

    /** 业务类型 **/
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;


    /** 业务字段  **/
    /** 采购订单号 */
    private String purchaseOrderCode;

    /** 采购收货通知单号 */
    private String receiptNoticeCode;

    /** 销售退货单号 */
    private String salesReturnCode;

    /** 工序委外采购订单号 */
    private String purchaseProcessOutCode;

    /** 是否强制关闭 */
    private Integer isForceClose;

    /** 单据状态 */
    private List<Integer> formStatusList;

}
