package com.mongoso.mgs.module.finance.controller.admin.workbench;

import com.mongoso.mgs.common.charts.vo.ChartsLineRespVO;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.workbench.vo.FinanceAccountDetailsRespVO;
import com.mongoso.mgs.module.finance.controller.admin.workbench.vo.FinanceDataRespVO;
import com.mongoso.mgs.module.finance.controller.admin.workbench.vo.FinanceOrderDataRespVO;
import com.mongoso.mgs.module.finance.service.workbench.FinanceWorkbenchService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 财务工作台 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/finance")
@Validated
public class FinanceWorkbenchController {

    @Resource
    private FinanceWorkbenchService financeWorkbenchService;

    @OperateLog("财务数据统计")
    @PostMapping("/financeDataStat")
    //@PreAuthorize("@ss.hasPermission('erpFinanceWorkbench:query')")
    public ResultX<FinanceDataRespVO> financeDataStat() {
        return success(financeWorkbenchService.financeDataStat());
    }

    @OperateLog("财务订单数据统计")
    @PostMapping("/financeOrderDataStat")
    //@PreAuthorize("@ss.hasPermission('erpFinanceWorkbench:query')")
    public ResultX<FinanceOrderDataRespVO> financeOrderDataStat() {
        return success(financeWorkbenchService.financeOrderDataStat());
    }

    @OperateLog("财务支出收入趋势")
    @PostMapping("/financeAccountDetailLineStat")
    //@PreAuthorize("@ss.hasPermission('erpFinanceWorkbench:query')")
    public ResultX<ChartsLineRespVO> financeAccountDetailLineStat() {
        return success(financeWorkbenchService.bankAccountDetailLineStat());
    }

    @OperateLog("财务账户近期流水")
    @PostMapping("/financeAccountDetailList")
    //@PreAuthorize("@ss.hasPermission('erpFinanceWorkbench:query')")
    public ResultX<List<FinanceAccountDetailsRespVO>> financeAccountDetailList() {
        return success(financeWorkbenchService.queryBankAccountDetailList());
    }

}
