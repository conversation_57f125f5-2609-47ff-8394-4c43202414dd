package com.mongoso.mgs.module.purchase.controller.admin.demand;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.*;
import com.mongoso.mgs.module.purchase.service.demand.PurchaseDemandService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderDetailResp;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderPageReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderQueryReqVO;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 采购需求 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchase")
@Validated
public class PurchaseDemandController {

    @Resource
    private PurchaseDemandService demandService;
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @OperateLog("采购需求添加或编辑")
    @PostMapping("/erpPurchaseDemandAdit")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:adit')")
    public ResultX<Long> purchaseDemandAdit(@Valid @RequestBody PurchaseDemandAditReqVO reqVO) {
        return success(reqVO.getPurchaseDemandId() == null
                            ? demandService.purchaseDemandAdd(reqVO)
                            : demandService.purchaseDemandEdit(reqVO));
    }

    @OperateLog("采购需求删除")
    @PostMapping("/erpPurchaseDemandDel")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:delete')")
    public ResultX<Boolean> purchaseDemandDel(@Valid @RequestBody PurchaseDemandPrimaryReqVO reqVO) {
        demandService.purchaseDemandDel(reqVO.getPurchaseDemandId());
        return success(true);
    }

    @OperateLog("采购需求删除(批量)")
    @PostMapping("/erpPurchaseDemandDelBatch")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:delete')")
    public ResultX<BatchResult> erpPurchaseDemandDelBatch(@Valid @RequestBody IdReq reqVO) {
        return demandService.purchaseDemandDelBatch(reqVO);
    }



    @OperateLog("采购需求详情")
    @PostMapping("/erpPurchaseDemandDetail")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<PurchaseDemandRespVO> purchaseDemandDetail(@Valid @RequestBody PurchaseDemandPrimaryReqVO reqVO) {
        return success(demandService.purchaseDemandDetail(reqVO.getPurchaseDemandId()));
    }

    @OperateLog("采购需求列表")
    @PostMapping("/erpPurchaseDemandList")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    @DataPermission
    public ResultX<List<PurchaseDemandRespVO>> purchaseDemandList(@Valid @RequestBody PurchaseDemandQueryReqVO reqVO) {
        return success(demandService.purchaseDemandList(reqVO));
    }

    @OperateLog("采购需求列表")
    @PostMapping("/erpPurchaseDemandQuoteList")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<List<PurchaseDemandRespVO>> purchaseDemandQuoteList(@Valid @RequestBody PurchaseDemandQueryReqVO reqVO) {
        return success(demandService.purchaseDemandList(reqVO));
    }

    @OperateLog("采购需求分页")
    @PostMapping("/erpPurchaseDemandPage")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    @DataPermission
    public ResultX<PageResult<PurchaseDemandRespVO>> purchaseDemandPage(@Valid @RequestBody PurchaseDemandPageReqVO reqVO) {
        return success(demandService.purchaseDemandPage(reqVO));
    }

    @OperateLog("采购需求分页")
    @PostMapping("/erpPurchaseDemandQuotePage")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<PageResult<PurchaseDemandRespVO>> purchaseDemandQuotePage(@Valid @RequestBody PurchaseDemandPageReqVO reqVO) {
        return success(demandService.purchaseDemandPage(reqVO));
    }

    @OperateLog("销售采购需求关联销售单列表（分页）")
    @PostMapping("/erpSaleForDemandPage")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<PageResult<ErpSaleOrderDetailResp>> erpSaleForDemandPage(@Valid @RequestBody ErpSaleOrderPageReqVO reqVO) {
        return success(erpSaleOrderService.erpSaleForDemandPage(reqVO));
    }

    @OperateLog("销售采购需求关联销售单列表")
    @PostMapping("/erpSaleForDemandList")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<List<ErpSaleOrderDetailResp>> erpSaleForDemandList(@Valid @RequestBody ErpSaleOrderQueryReqVO reqVO) {
        return success(erpSaleOrderService.erpSaleForDemandList(reqVO));
    }

    @OperateLog("生产采购需求关联生产订单列表（分页）")
    @PostMapping("/erpProduceForDemandPage")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<PageResult<ErpSaleOrderDetailResp>> erpProduceForDemandPage(@Valid @RequestBody ErpSaleOrderPageReqVO reqVO) {
        return success(erpSaleOrderService.erpSaleForDemandPage(reqVO));
    }

    @OperateLog("生产采购需求关联生产订单列表")
    @PostMapping("/erpProduceForDemandList")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<List<ErpSaleOrderDetailResp>> erpProduceForDemandList(@Valid @RequestBody ErpSaleOrderQueryReqVO reqVO) {
        return success(erpSaleOrderService.erpSaleForDemandList(reqVO));
    }

    @OperateLog("物料分析需求关联生产订单列表（分页）")
    @PostMapping("/erpMaterialAnalyseForDemandPage")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<PageResult<ErpSaleOrderDetailResp>> erpMaterialAnalyseForDemandPage(@Valid @RequestBody ErpSaleOrderPageReqVO reqVO) {
        return success(erpSaleOrderService.erpSaleForDemandPage(reqVO));
    }

    @OperateLog("生产采购需求关联生产订单列表")
    @PostMapping("/erpMaterialAnalyseForDemandList")
    @PreAuthorize("@ss.hasPermission('purchaseDemand:query')")
    public ResultX<List<ErpSaleOrderDetailResp>> erpMaterialAnalyseForDemandList(@Valid @RequestBody ErpSaleOrderQueryReqVO reqVO) {
        return success(erpSaleOrderService.erpSaleForDemandList(reqVO));
    }

    @OperateLog("采购需求审核")
    @PostMapping("/erpPurchaseDemandApprove")
    @PreAuthorize("@ss.hasPermission('container:adit')")
    public ResultX<BatchResult> erpPurchaseDemandApprove(@Valid @RequestBody PurchaseFlowApprove reqVO) {
        BatchResult resultList = demandService.erpPurchaseDemandApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("采购需求回调接口")
    @PostMapping("/erpPurchaseDemandFlowCallback")
    public ResultX<Object> erpPurchaseDemandFlowCallback(@Valid @RequestBody PurchaseFlowCallback reqVO) {
        return success(demandService.erpPurchaseDemandFlowCallback(reqVO));
    }
}
