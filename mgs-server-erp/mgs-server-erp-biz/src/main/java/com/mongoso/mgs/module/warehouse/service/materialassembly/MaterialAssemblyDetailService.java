package com.mongoso.mgs.module.warehouse.service.materialassembly;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail.MaterialAssemblyDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail.MaterialAssemblyDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail.MaterialAssemblyDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail.MaterialAssemblyDetailRespVO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 组装拆卸单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialAssemblyDetailService {

    /**
     * 创建组装拆卸单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long materialAssemblyDetailAdd(@Valid MaterialAssemblyDetailAditReqVO reqVO);

    /**
     * 更新组装拆卸单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long materialAssemblyDetailEdit(@Valid MaterialAssemblyDetailAditReqVO reqVO);

    /**
     * 删除组装拆卸单明细
     *
     * @param assemblyDetailId 编号
     */
    void materialAssemblyDetailDel(Long assemblyDetailId);

    /**
     * 获得组装拆卸单明细信息
     *
     * @param assemblyDetailId 编号
     * @return 组装拆卸单明细信息
     */
    MaterialAssemblyDetailRespVO materialAssemblyDetailDetail(Long assemblyDetailId);

    /**
     * 获得组装拆卸单明细列表
     *
     * @param reqVO 查询条件
     * @return 组装拆卸单明细列表
     */
    List<MaterialAssemblyDetailRespVO> materialAssemblyDetailList(@Valid MaterialAssemblyDetailQueryReqVO reqVO);

    /**
     * 获得组装拆卸单明细分页
     *
     * @param reqVO 查询条件
     * @return 组装拆卸单明细分页
     */
    PageResult<MaterialAssemblyDetailRespVO> materialAssemblyDetailPage(@Valid MaterialAssemblyDetailPageReqVO reqVO);

}
