package com.mongoso.mgs.module.produce.controller.admin.workcenterperson.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 工作中心关联人员 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class WorkCenterPersonQueryReqVO {

    /** 主键 */
    private Long centerPersonId;

    /** 创建人ID */
    private Long createdId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 工作中心ID */
    private Long workCenterId;

    /** 工作中心编码 */
    private String workCenterCode;

    /** 行号 */
    private Integer rowNo;

    /** 用户ID */
    private Long userId;

}
