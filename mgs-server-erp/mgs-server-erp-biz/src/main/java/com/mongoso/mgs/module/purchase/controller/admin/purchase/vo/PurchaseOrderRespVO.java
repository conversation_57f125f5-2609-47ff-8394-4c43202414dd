package com.mongoso.mgs.module.purchase.controller.admin.purchase.vo;

import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购订单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseOrderRespVO extends PurchaseOrderBaseVO {

    /** 采购明细列表 */
    private List<PurchaseOrderDetailRespVO> detailList;

    /** 采购订单类型 */
    private String purchaseTypeDictName;

    /** 币种 */
    private String currencyDictName;

    /** 结算方式 */
    private String settlementMethodDictName;

    /** 关联供应商名 */
    private String relatedSupplierName;

    /** 票据类型 */
    private String  invoiceTypeName;

    /** 付款条件 */
    private String paymentTermsDictName;

    /** 主体公司 */
    private String companyOrgName;

    /** 入库状态["未入库","部分入库","已完成"] */
    private String inboundStatusDictName;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 责任人 */
    private String directorName;

    /** 责任部门 */
    private String directorOrgName;

    /** 创建人 */
    private String createdBy;

    /** 是否可下发采购收货通知单 */
    private Integer isCanIssueReceiptNotice;

    /** 是否可下发采购退货单 */
    private Integer isCanIssueReturn;

    /** 是否可下发采购收货单 */
    private Integer isCanIssueReceipt;

    /** 是否可下发采购入库单 */
    private Integer isCanIssueInbound;

    /** 是否可下发委外领料单 */
    private Integer isCanIssueOutGetMaterial;
    /** 是否可下发委外退料单 */
    private Integer isCanIssueOutReturnMaterial;

    /** 单据状态 */
    private String formStatusDictName;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 付款计划策略--取子集 */
    private String paymentPlanStrategyDictName;

    /** 付款计划策略--取父集 */
    private String relatedPaymentPlanStrategyDictName;

    /** 收票计划策略 */
    private String receiptPlanStrategyDictName;

    /** 收票结算池策略 */
    private String receiptSettlementStrategyDictName;

    /** 采购退款计划策略 */
    private String purchaseRefundPlanStrategyDictName;

    /** 审批任务id */
    private Long approveTaskId;

    /** 入库流程配置名称 */
    private String inProcessConfigDictName;

    /** 出库流程配置名称 */
    private String outProcessConfigDictName;

}
