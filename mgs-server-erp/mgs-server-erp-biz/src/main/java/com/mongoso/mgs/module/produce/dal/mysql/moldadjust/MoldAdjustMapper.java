package com.mongoso.mgs.module.produce.dal.mysql.moldadjust;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.produce.dal.db.moldadjust.MoldAdjustDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.produce.controller.admin.moldadjust.vo.*;

/**
 * 模具调整记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MoldAdjustMapper extends BaseMapperX<MoldAdjustDO> {

    default PageResult<MoldAdjustDO> selectPage(MoldAdjustPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<MoldAdjustDO>lambdaQueryX()
                .eqIfPresent(MoldAdjustDO::getMoldId, reqVO.getMoldId())
                .inIfPresent(MoldAdjustDO::getMoldId, reqVO.getMoldIdList())
                .eqIfPresent(MoldAdjustDO::getOldMoldStatus, reqVO.getOldMoldStatus())
                .eqIfPresent(MoldAdjustDO::getMoldStatus, reqVO.getMoldStatus())
                .eqIfPresent(MoldAdjustDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(MoldAdjustDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(MoldAdjustDO::getCreatedId, reqVO.getCreatedId())
                .betweenIfPresent(MoldAdjustDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MoldAdjustDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(MoldAdjustDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(MoldAdjustDO::getDataStatus, reqVO.getDataStatus())
                .orderByDesc(MoldAdjustDO::getCreatedDt));
    }




    default List<MoldAdjustDO> selectList(MoldAdjustQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<MoldAdjustDO>lambdaQueryX()
                .eqIfPresent(MoldAdjustDO::getMoldAdjustId, reqVO.getMoldAdjustId())
                .eqIfPresent(MoldAdjustDO::getMoldId, reqVO.getMoldId())
                .inIfPresent(MoldAdjustDO::getMoldId, reqVO.getMoldIdList())
                .eqIfPresent(MoldAdjustDO::getOldMoldStatus, reqVO.getOldMoldStatus())
                .eqIfPresent(MoldAdjustDO::getMoldStatus, reqVO.getMoldStatus())
                .eqIfPresent(MoldAdjustDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(MoldAdjustDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(MoldAdjustDO::getCreatedBy, reqVO.getCreatedBy())
                .eqIfPresent(MoldAdjustDO::getCreatedId, reqVO.getCreatedId())
                .betweenIfPresent(MoldAdjustDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MoldAdjustDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(MoldAdjustDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                .eqIfPresent(MoldAdjustDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(MoldAdjustDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(MoldAdjustDO::getDataStatus, reqVO.getDataStatus())
                    .orderByDesc(MoldAdjustDO::getCreatedDt));
    }


}