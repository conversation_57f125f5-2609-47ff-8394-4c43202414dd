package com.mongoso.mgs.module.utility.controller.admin.handler.approve;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.utility.dal.db.utilityarchives.UtilityArchivesDO;
import com.mongoso.mgs.module.utility.dal.db.utilityconfig.UtilityConfigDO;
import com.mongoso.mgs.module.utility.dal.db.utilitylog.UtilityLogDO;
import com.mongoso.mgs.module.utility.dal.mysql.utilityarchives.UtilityArchivesMapper;
import com.mongoso.mgs.module.utility.dal.mysql.utilityconfig.UtilityConfigMapper;
import com.mongoso.mgs.module.utility.dal.mysql.utilitylog.UtilityLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class UtilityArchiveApprove extends FlowApproveHandler<UtilityArchivesDO> {

    @Resource
    private UtilityArchivesMapper utilityArchivesMapper;

    //@Resource
    //private UtilityCostMapper costMapper;

    @Resource
    private UtilityLogMapper logMapper;

    @Resource
    private UtilityConfigMapper configMapper;

    //@Resource
    //private SeqService seqService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(UtilityArchivesDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(UtilityArchivesDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(UtilityArchivesDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getUtilityArchivesId())
                .objCode(item.getUtilityArchivesCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(UtilityArchivesDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Long utilityArchivesId = item.getUtilityArchivesId();
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        UtilityConfigDO configDO = configMapper.selectById(item.getUtilityConfigId());
        if(ObjUtilX.isEmpty(configDO)){
            failItem.setReason("水电气费用标准不存在");
            failItem.setCode(item.getUtilityArchivesCode());
            return false;
        }
        //审核和没有限制，直接审核
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()){
            //先判断是否生成过水电气费用数据,如果生成过，则不在进行生成
            Long count = logMapper.selectCount(new LambdaQueryWrapper<UtilityLogDO>()
                    .eq(UtilityLogDO::getUtilityArchivesId, utilityArchivesId)
                    .eq(UtilityLogDO::getDataStatus, DataStatusEnum.APPROVED.key)
            );
            if (count > 0){
                failItem.setReason("该档案下存在已审核的抄表记录，不可反审");
                failItem.setCode(item.getUtilityArchivesCode());
                return false;
            }
        }else{

        }

        return true;
    }


    @Override
    public Integer handleBusinessData(UtilityArchivesDO item, BaseApproveRequest request) {
        //直接修改数据的状态
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        Long id = item.getUtilityArchivesId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        UtilityArchivesDO utilityArchivesDO = utilityArchivesMapper.selectById(id);
        utilityArchivesDO.setApprovedBy(loginUser.getFullUserName());
        utilityArchivesDO.setApprovedDt(LocalDateTime.now());
        utilityArchivesDO.setDataStatus(dataStatus.shortValue());

//        if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
//
//
//        }else{
//            //先判断是否生成过水电气费用数据,如果生成过，则不在进行生成
//            Long count = logMapper.selectCount(new LambdaQueryWrapper<UtilityLogDO>()
//                    .eq(UtilityLogDO::getUtilityArchivesId, id)
//                    .eq(UtilityLogDO::getDataStatus, DataStatusEnum.APPROVED.key)
//            );
//            if (count > 0){
//                failItem.setReason("该档案下存在已审核的抄表记录，不可反审");
//                failItem.setCode(utilityArchivesDO.getUtilityArchivesCode());
//                return 0;
//            }
//        }
        utilityArchivesDO.setLastDt(utilityArchivesDO.getUtilityInitTime());
        utilityArchivesDO.setLastNum(utilityArchivesDO.getUtilityInitNum());
        utilityArchivesDO.setLastLogId(0L);

        return utilityArchivesMapper.updateById(utilityArchivesDO);
    }
}
