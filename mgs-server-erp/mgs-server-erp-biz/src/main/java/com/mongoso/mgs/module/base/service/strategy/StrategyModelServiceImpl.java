package com.mongoso.mgs.module.base.service.strategy;

import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.enums.strategy.StrategyEnum;
import com.mongoso.mgs.common.enums.strategyconfig.StrategyConfigEnum;
import com.mongoso.mgs.common.vo.StrategyDelModelVO;
import com.mongoso.mgs.common.vo.StrategyModelVO;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.module.finance.service.invoice.invoicependingplan.InvoicePendingPlanService;
import com.mongoso.mgs.module.finance.service.pendingpaymentplan.PendingPaymentPlanService;
import com.mongoso.mgs.module.finance.service.settlepool.SettlePoolService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class StrategyModelServiceImpl implements StrategyModelService{

    @Lazy
    @Resource
    private InvoicePendingPlanService invoicePendingPlanService;

    @Lazy
    @Resource
    private PendingPaymentPlanService pendingPaymentPlanService;

    @Resource
    private SettlePoolService settlePoolService;

    public Long insertModelData(StrategyModelVO vo){
        log.info("上游传递的数据是：{}", vo);
        Long id = null;
        //判断策略
        int automatic = checkStrategy(vo);
        if (automatic == 0){
            return id;
        }
        //设置策略
        if (vo.getStrategyType() == StrategyEnum.INVOICE.getType()){
            if (Objects.isNull(vo.getInvoiceVO())){
                throw new BizException("5001","待开票计划数据不能为空");
            }
            vo.getInvoiceVO().setAutomatic(automatic);
            //判断是审核还是反审核
            if (vo.getButtonType() == DataButtonEnum.APPROVE.key.intValue()) {
                id = invoicePendingPlanService.invoicePendingPlanAdd(vo.getInvoiceVO());
            }else {
                invoicePendingPlanService.invoicePendingPlanDel(vo.getInvoiceVO().getUpId());
            }
        }else if (vo.getStrategyType() == StrategyEnum.RECEIVE_PAYMENT.getType()){
            //收款 收付款 正数
            if (vo.getPaymentVO().getTotalAmt().compareTo(BigDecimal.ZERO) < 0){
                throw new BizException("5001","对账金额必须大于0");
            }
            vo.getPaymentVO().setAutomatic(automatic);
            //判断是审核还是反审核
            if (vo.getButtonType() == DataButtonEnum.APPROVE.key.intValue()) {
                id = pendingPaymentPlanService.pendingPaymentPlanAdd(vo.getPaymentVO());
            }else {
                pendingPaymentPlanService.pendingPaymentPlanDelBySourceOrderId(vo.getPaymentVO().getSourceOrderId());
            }
        }else if (vo.getStrategyType() == StrategyEnum.REFUND_PAYMENT.getType()){
            //退款 收付款 负数
            if (vo.getPaymentVO().getTotalAmt().compareTo(BigDecimal.ZERO) > 0){
                throw new BizException("5001","对账金额必须小于0");
            }
            vo.getPaymentVO().setAutomatic(automatic);
            if (vo.getButtonType() == DataButtonEnum.APPROVE.key.intValue()) {
                id = pendingPaymentPlanService.pendingPaymentPlanAdd(vo.getPaymentVO());
            }else {
                pendingPaymentPlanService.pendingPaymentPlanDelBySourceOrderId(vo.getPaymentVO().getSourceOrderId());
            }
        }else if (vo.getStrategyType() == StrategyEnum.SETTLE_POOL.getType()){
            //结算池
            //判断是审核还是反审核
            if (vo.getButtonType() == DataButtonEnum.APPROVE.key.intValue()) {
                settlePoolService.settlePoolSave(vo.getPoolList());
            }else {
                settlePoolService.settlePoolDelBySourceOrderId(vo.getPoolList().get(0).getSourceOrderId());
            }
        }else{
            throw new BizException("5001","策略类型错误");
        }

        return id;
    }

    @Override
    public void insertModelDataBatch(List<StrategyModelVO> modelVOList) {
        for (StrategyModelVO vo : modelVOList){
            log.info("上游传递的数据是：{}", vo);
            //Long id = null;
            //判断策略
            int automatic = checkStrategy(vo);
            if (automatic == 0){
                continue;
            }
            //设置策略
            vo.getInvoiceVO().setAutomatic(automatic);
            if (vo.getStrategyType() == StrategyEnum.INVOICE.getType()){
                if (Objects.isNull(vo.getInvoiceVO())){
                    log.error("待开票计划数据不能为空");
                    continue;
                    //throw new BizException("5001","待开票计划数据不能为空");
                }
                invoicePendingPlanService.invoicePendingPlanAdd(vo.getInvoiceVO());
            }else if (vo.getStrategyType() == StrategyEnum.RECEIVE_PAYMENT.getType()){
                //收付款
                pendingPaymentPlanService.pendingPaymentPlanAdd(vo.getPaymentVO());
            }
        }
    }

    @Override
    public int delModelData(StrategyDelModelVO vo) {
        if (vo.getStrategyType() == StrategyEnum.INVOICE.getType()){
            invoicePendingPlanService.invoicePendingPlanDel(vo.getUpId());
        }else if (vo.getStrategyType() == StrategyEnum.RECEIVE_PAYMENT.getType()){
            //收付款
            pendingPaymentPlanService.pendingPaymentPlanDel(vo.getUpId());
        }
        return 0;
    }

    @Override
    public void delModelDataBatch(List<StrategyDelModelVO> delModelVOList) {
        for (StrategyDelModelVO vo : delModelVOList){
            if (vo.getStrategyType() == StrategyEnum.INVOICE.getType()){
                invoicePendingPlanService.invoicePendingPlanDel(vo.getUpId());
            }else if (vo.getStrategyType() == StrategyEnum.RECEIVE_PAYMENT.getType()){
                //收付款
                pendingPaymentPlanService.pendingPaymentPlanDel(vo.getUpId());
            }
        }
    }


    public static int checkStrategy(StrategyModelVO reqVO) {
        //校验参数：
        checkVO(reqVO);
        //定义数据生成方式
        int automatic = 0;//0 不生成 1自动 2手动

        //获取策略
        String strategy = reqVO.getStrategy();
        if (StrUtilX.isEmpty(strategy)){
            return automatic;
        }

        try {
            //先判断策略类型：0发票 1收付款 2退款 3结算池
            if (reqVO.getStrategyType() == 0) {
                if (reqVO.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.type.shortValue()
                        || reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.type.shortValue()) {
                    return 2;
                }
                //判断对应单据类型
                //判断是否是销售发货出库单或者销售发货通知出库单
                if (reqVO.getIsSaleDeliveryOutbound() == 1 && reqVO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue()) {
                    //判断是自动还是手动
                    if (strategy.equals(StrategyConfigEnum.TYPE_203.getKey())) {
                        automatic = 1;
                    } else if (strategy.equals(StrategyConfigEnum.TYPE_005103.getKey())) {
                        automatic = 2;
                    }
                    //automatic = 1;
                }

                if (strategy.equals(StrategyConfigEnum.TYPE_201.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_202.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_203.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_INVOICES_ORDER.type.shortValue()) {
                    automatic = 1;
                }else if (strategy.equals(StrategyConfigEnum.TYPE_601.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_602.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_RECEIPT_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_603.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_IN_BOUND_ORDER.type.shortValue()) {
                    automatic = 1;
                }
            } else if (reqVO.getStrategyType() == 1){
                //收款
//                if (reqVO.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.type.shortValue()
//                        || reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.type.shortValue()) {
//                    return 2;
//                }
                //判断对应单据类型
                //判断是否是销售发货出库单或者销售发货通知出库单
                if (reqVO.getIsSaleDeliveryOutbound() == 1 && reqVO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue()) {
                    //判断是自动还是手动
                    if (strategy.equals(StrategyConfigEnum.TYPE_203.getKey())) {
                        automatic = 1;
                    } else if (strategy.equals(StrategyConfigEnum.TYPE_005103.getKey())) {
                        automatic = 2;
                    }
                    //automatic = 1;
                }

                // 销售
                if (strategy.equals(StrategyConfigEnum.TYPE_001.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_002.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_003.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_INVOICES_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_004.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE1.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_005.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.type.shortValue()) {
                    // 这个好像不用了
                    automatic = 1;
                }// 采购
                else if (strategy.equals(StrategyConfigEnum.TYPE_401.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_402.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_RECEIPT_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_403.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_IN_BOUND_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_404.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE2.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_405.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.type.shortValue()) {
                    automatic = 1;
                }
            } else if (reqVO.getStrategyType() == 2){// 退款策略
                // 销售
                if (strategy.equals(StrategyConfigEnum.TYPE_301.getKey()) && (reqVO.getSourceFormType() == OrderTypeEnum.SALE_RETURN_ORDER.type.shortValue()
                        || reqVO.getSourceFormType() == OrderTypeEnum.SALE_CHARGE_SLIPS_ORDER.type.shortValue())) {
                    automatic = 1;
                }// 采购
                else if (strategy.equals(StrategyConfigEnum.TYPE_701.getKey()) && (reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_RETURN_ORDER.type.shortValue()
                        || reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_CHARGE_SLIPS_ORDER.type.shortValue())) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_004.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE1.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_404.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE2.type.shortValue()) {
                    automatic = 1;
                }
            } else if (reqVO.getStrategyType() == 3) {
                //判断是否是销售发货出库单或者销售发货通知出库单
                if (reqVO.getIsSaleDeliveryOutbound() == 1 && reqVO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue()) {
                    //判断是自动还是手动
                    if (strategy.equals(StrategyConfigEnum.TYPE_203.getKey())) {
                        automatic = 1;
                    } else if (strategy.equals(StrategyConfigEnum.TYPE_005103.getKey())) {
                        automatic = 2;
                    }
                    //automatic = 1;
                }

                // 销售结算池
                if (strategy.equals(StrategyConfigEnum.TYPE_005101.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_005102.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_005103.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.SALE_INVOICES_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_302.getKey()) && (reqVO.getSourceFormType() == OrderTypeEnum.SALE_RETURN_ORDER.type.shortValue()
                        || reqVO.getSourceFormType() == OrderTypeEnum.SALE_CHARGE_SLIPS_ORDER.type.shortValue())) {
                    automatic = 1;
                }// 采购结算池
                else if (strategy.equals(StrategyConfigEnum.TYPE_405501.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_405502.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_RECEIPT_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_405503.getKey()) && reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_IN_BOUND_ORDER.type.shortValue()) {
                    automatic = 1;
                } else if (strategy.equals(StrategyConfigEnum.TYPE_702.getKey()) && (reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_RETURN_ORDER.type.shortValue()
                        || reqVO.getSourceFormType() == OrderTypeEnum.PURCHASE_CHARGE_SLIPS_ORDER.type.shortValue())) {
                    automatic = 1;
                }
            }
        } catch (Exception e) {
            log.error("获取策略异常。。。。。。。。。。。", e);
        }
        return automatic;
    }

    public static void checkVO(StrategyModelVO vo){
        if (vo.getStrategyType() == null){
            throw new BizException("5001","策略类型不能为空");
        }
        if (vo.getSourceFormType() == null){
            throw new BizException("5001","单据类型不能为空");
        }
    }
}
