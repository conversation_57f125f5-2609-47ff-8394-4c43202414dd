package com.mongoso.mgs.module.base.service.region;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.base.controller.admin.region.vo.RegionAditReqVO;
import com.mongoso.mgs.module.base.controller.admin.region.vo.RegionPageReqVO;
import com.mongoso.mgs.module.base.controller.admin.region.vo.RegionQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.regin.RegionDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 国家地区 Service 接口
 *
 * <AUTHOR>
 */
public interface RegionService {

    /**
     * 获得国家地区信息
     *
     * @param regionId 编号
     * @return 国家地区信息
     */
    RegionDO regionDetail(Integer regionId);

    /**
     * 获得国家地区列表
     *
     * @param reqVO 查询条件
     * @return 国家地区列表
     */
    List<RegionDO> regionList(@Valid RegionQueryReqVO reqVO);

    /**
     * 获得国家地区分页
     *
     * @param reqVO 查询条件
     * @return 国家地区分页
     */
    PageResult<RegionDO> regionPage(@Valid RegionPageReqVO reqVO);

}
