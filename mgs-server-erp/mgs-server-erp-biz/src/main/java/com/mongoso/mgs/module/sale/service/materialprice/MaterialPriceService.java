package com.mongoso.mgs.module.sale.service.materialprice;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.sale.controller.admin.materialprice.vo.*;
import com.mongoso.mgs.module.sale.dal.db.materialprice.MaterialPriceDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 商品价格 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialPriceService {

    /**
     * 创建商品价格
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long materialPriceAdd(@Valid MaterialPriceAditReqVO reqVO);

    /**
     * 更新商品价格
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long materialPriceEdit(@Valid MaterialPriceAditReqVO reqVO);

    /**
     * 删除商品价格
     *
     * @param materialPriceId 编号
     */
    void materialPriceDelete(Long materialPriceId);

    /**
     * 获得商品价格信息
     *
     * @param materialPriceId 编号
     * @return 商品价格信息
     */
    MaterialPriceRespVO materialPriceDetail(Long materialPriceId);

    /**
     * 获得商品价格列表
     *
     * @param reqVO 查询条件
     * @return 商品价格列表
     */
    List<MaterialPriceRespVO> materialPriceList(@Valid MaterialPriceQueryReqVO reqVO);

    /**
     * 获得商品价格分页
     *
     * @param reqVO 查询条件
     * @return 商品价格分页
     */
    PageResult<MaterialPriceRespVO> materialPricePage(@Valid MaterialPricePageReqVO reqVO);

}
