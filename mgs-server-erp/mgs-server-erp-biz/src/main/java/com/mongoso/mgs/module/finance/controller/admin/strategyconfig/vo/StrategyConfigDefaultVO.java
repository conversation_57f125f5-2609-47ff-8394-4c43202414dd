package com.mongoso.mgs.module.finance.controller.admin.strategyconfig.vo;

import lombok.Data;

@Data
public class StrategyConfigDefaultVO {
    /** 收款计划策略 */
    private String collectionPlanStrategy = "001";

    /** 收款结算池策略 */
    private String collectionSettlementStrategy;

    /** 开票计划策略 */
    private String invoicingPlanStrategy = "201";

    /** 销售退款计划策略 */
    private String saleRefundPlanStrategy = "301";

    /** 付款计划策略 */
    private String paymentPlanStrategy = "401";

    /** 付款结算池策略 */
    private String paymentSettlementStrategy;

    /** 收票计划策略 */
    private String receiptPlanStrategy = "601";

    /** 采购退款计划策略 */
    private String purchaseRefundPlanStrategy = "701";

    /** 跳过开票申请 */
    private Integer skipInvoiceApply = 0;

    /** 跳过付款申请 */
    private Integer skipPaymentApply = 0;
}
