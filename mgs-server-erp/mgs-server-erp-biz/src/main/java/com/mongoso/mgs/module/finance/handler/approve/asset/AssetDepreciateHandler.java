package com.mongoso.mgs.module.finance.handler.approve.asset;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateRespVO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciate.AssetDepreciateDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetregister.AssetRegisterDO;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetdepreciate.AssetDepreciateMapper;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetregister.AssetRegisterMapper;
import com.mongoso.mgs.module.finance.service.asset.assetdepreciate.AssetDepreciateService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Component
public class AssetDepreciateHandler extends FlowApproveHandler<AssetDepreciateDO> {
    @Resource
    private AssetDepreciateMapper assetDepreciateMapper;

    @Resource
    @Lazy
    private AssetDepreciateService assetDepreciateService;

    @Resource
    private AssetRegisterMapper assetRegisterMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(AssetDepreciateDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(AssetDepreciateDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(AssetDepreciateDO.class);
        AssetDepreciateRespVO respVO = assetDepreciateService.assetDepreciateDetail(item.getAssetDepreciateId());
        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getAssetDepreciateId())
                .objCode(item.getAccrualMonth()+ " " + respVO.getAssetCode() + " " + respVO.getAssetName())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(AssetDepreciateDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        AssetDepreciateRespVO respVO = assetDepreciateService.assetDepreciateDetail(item.getAssetDepreciateId());
        AssetDepreciateQueryReqVO reqVO = new AssetDepreciateQueryReqVO();
        reqVO.setAssetId(item.getAssetId());
        reqVO.setDataStatus(DataStatusEnum.APPROVED.key.shortValue());
        List<AssetDepreciateRespVO> depreciateRespVOList = assetDepreciateService.assetDepreciateList(reqVO);
        AssetDepreciateRespVO lastRespVO = null;
        if (depreciateRespVOList != null && depreciateRespVOList.size() > 0) {
            lastRespVO = depreciateRespVOList.get(0);
        }
        BigDecimal currentPeriod = BigDecimal.ZERO;
        if (respVO.getIsPreAccrued() == 0) {
            currentPeriod = respVO.getPreAccruedPeriod();
        }

        AssetRegisterDO assetRegisterDO = assetRegisterMapper.selectById(item.getAssetId());

        // 编码需要拼接
        String code = item.getAccrualMonth() + " " + assetRegisterDO.getAssetCode() + " " + assetRegisterDO.getAssetName();

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            if (respVO.getAccrualUnitType() > 1) {
                if (respVO.getWorkload() == null) {
//                    failItem.setCode(item.getAccrualMonth());
                    failItem.setCode(code);
                    failItem.setReason("该资产折旧未录入工作量");
                    return false;
                }
                if (respVO.getWorkload().compareTo(respVO.getRemainingPeriod()) > 0) {
//                    failItem.setCode(item.getAccrualMonth());
                    failItem.setCode(code);
                    failItem.setReason("本期工作量不能大于剩余周期");
                    return false;
                }
            }
            if ((lastRespVO != null && BigDecimal.valueOf(lastRespVO.getCurrentPeriod().intValue()+1).compareTo(respVO.getCurrentPeriod()) != 0) || (lastRespVO == null && respVO.getCurrentPeriod().compareTo(BigDecimal.valueOf(1+currentPeriod.intValue())) != 0)) {
//                failItem.setCode(item.getAccrualMonth());
                failItem.setCode(code);
                failItem.setReason("资产折旧不能跨月审核");
                return false;
            }

            if (lastRespVO != null && lastRespVO.getAccruedAmt().compareTo(respVO.getAssetPurchasePrice().subtract(respVO.getNetResidualValue())) == 0) {
//                failItem.setCode(item.getAccrualMonth());
                failItem.setCode(code);
                failItem.setReason("已提金额不能大于资产购价减去净残值");
                return false;
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
//            if (respVO.getAccrualUnitType() > 1) {
                if (BigDecimal.valueOf(lastRespVO.getCurrentPeriod().intValue()).compareTo(respVO.getCurrentPeriod()) != 0) {
//                    failItem.setCode(item.getAccrualMonth());
                    failItem.setCode(code);
                    failItem.setReason("资产折旧未不能跨月反审核");
                    return false;
                }
//            }
        }

        return true;
    }


    @Override
    public Integer handleBusinessData(AssetDepreciateDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = item.getAssetDepreciateId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        AssetDepreciateDO exist  = assetDepreciateMapper.selectById(id);
        exist.setApprovedBy(loginUser.getFullUserName());
        exist.setApprovedDt(LocalDateTime.now());
        exist.setDataStatus(dataStatus.shortValue());
        AssetDepreciateRespVO respVO = assetDepreciateService.assetDepreciateDetail(id);
        //获取最后一期
        AssetDepreciateQueryReqVO reqVO = new AssetDepreciateQueryReqVO();
        reqVO.setAssetId(exist.getAssetId());
        reqVO.setDataStatus(DataStatusEnum.APPROVED.key.shortValue());
        List<AssetDepreciateRespVO> depreciateRespVOList = assetDepreciateService.assetDepreciateList(reqVO);
        AssetDepreciateRespVO lastRespVO = null;
        if (depreciateRespVOList != null && depreciateRespVOList.size() > 0) {
            lastRespVO = depreciateRespVOList.get(0);
        }
        BigDecimal preAccruedAmt = BigDecimal.ZERO;
        if (respVO.getIsPreAccrued() == 0) {
            preAccruedAmt = respVO.getPreAccruedAmt();
        }
        AssetRegisterDO assetRegisterDO = assetRegisterMapper.selectById(exist.getAssetId());
        // todo 审核需上游 已审核，反审核 需下游 未审核
        // 审核通过，反审核通过
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            if (lastRespVO != null) {
                if (lastRespVO.getAccruedAmt().add(exist.getCurrentAccrualAmt()).compareTo(respVO.getAssetPurchasePrice().subtract(respVO.getNetResidualValue())) <= 0) {
                    exist.setAccruedAmt(lastRespVO.getAccruedAmt().add(exist.getCurrentAccrualAmt()));
                }else {
                    exist.setAccruedAmt(respVO.getAssetPurchasePrice().subtract(respVO.getNetResidualValue()));
                }

            }else {
                exist.setAccruedAmt(exist.getCurrentAccrualAmt().add(preAccruedAmt));
            }
            if (respVO.getAccrualUnitType() > 1) {
                assetRegisterDO.setRemainingPeriod(respVO.getRemainingPeriod().subtract(exist.getWorkload()));
            }
            assetRegisterDO.setAssetAccruedAmt(exist.getAccruedAmt());
            assetRegisterDO.setAssetNetValue(assetRegisterDO.getAssetPurchasePrice().subtract(exist.getAccruedAmt()));
            assetRegisterDO.setApprovedBy(loginUser.getFullUserName());
            assetRegisterDO.setApprovedDt(LocalDateTime.now());
            assetRegisterMapper.updateById(assetRegisterDO);

            //非作量法判断下一期已提金额是否大于剩余金额，大于则修改下期的本期计提金额
            AssetDepreciateRespVO assetDepreciateRespVO = assetDepreciateMapper.selectIdAndCurrentPeriod(exist.getAssetId(),BigDecimal.valueOf(exist.getCurrentPeriod().intValue()+1));
            if (assetDepreciateRespVO != null && exist.getAccruedAmt().add(assetDepreciateRespVO.getCurrentAccrualAmt()).compareTo(respVO.getAssetPurchasePrice().subtract(respVO.getNetResidualValue())) > 0
                    && exist.getAccruedAmt().compareTo(respVO.getAssetPurchasePrice().subtract(respVO.getNetResidualValue())) != 0) {
                AssetDepreciateDO depreciateDO  = new AssetDepreciateDO();
                depreciateDO.setAssetDepreciateId(assetDepreciateRespVO.getAssetDepreciateId());
                depreciateDO.setCurrentAccrualAmt(respVO.getAssetPurchasePrice().subtract(respVO.getNetResidualValue()).subtract(exist.getAccruedAmt()));
                assetDepreciateMapper.updateById(depreciateDO);
            }
        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
            assetRegisterDO.setAssetAccruedAmt(exist.getAccruedAmt().subtract(exist.getCurrentAccrualAmt()));
            assetRegisterDO.setAssetNetValue(assetRegisterDO.getAssetPurchasePrice().subtract(assetRegisterDO.getAssetAccruedAmt()));
            exist.setAccruedAmt(BigDecimal.ZERO);
            if (respVO.getAccrualUnitType() > 1) {
                assetRegisterDO.setRemainingPeriod(respVO.getRemainingPeriod().add(exist.getWorkload()));
            }
            assetRegisterDO.setApprovedBy(loginUser.getFullUserName());
            assetRegisterDO.setApprovedDt(LocalDateTime.now());
            assetRegisterMapper.updateById(assetRegisterDO);
        }
        //更新业务数据
        Integer updateCount = assetDepreciateMapper.updateById(exist);

        return updateCount;
    }
}
