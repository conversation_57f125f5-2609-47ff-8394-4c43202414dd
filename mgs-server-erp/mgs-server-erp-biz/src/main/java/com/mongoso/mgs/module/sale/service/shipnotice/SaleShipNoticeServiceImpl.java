package com.mongoso.mgs.module.sale.service.shipnotice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.enums.NoticeTemplateEnum;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.sale.controller.admin.shipnotice.vo.*;
import com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo.ShipNoticeDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo.ShipNoticeDetailRespVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail.ErpSaleOrderDetailDO;
import com.mongoso.mgs.module.sale.dal.db.shipnotice.SaleShipNoticeDO;
import com.mongoso.mgs.module.sale.dal.db.shipnoticedetail.SaleShipNoticeDetailDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.shipnotice.SaleShipNoticeMapper;
import com.mongoso.mgs.module.sale.dal.mysql.shipnoticedetail.SaleShipNoticeDetailMapper;
import com.mongoso.mgs.module.sale.enums.ConfirmStatusEnum;
import com.mongoso.mgs.module.sale.handler.approve.SaleShipNoticeApproveHandler;
import com.mongoso.mgs.module.sale.handler.flowCallback.SaleShipNoticeFlowCallBackHandler;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundDetailMapper;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;

/**
 * 销售发货通知单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SaleShipNoticeServiceImpl implements SaleShipNoticeService {

    @Resource
    private SaleShipNoticeMapper shipNoticeMapper;

    @Resource
    private SaleShipNoticeDetailMapper shipNoticeDetailMapper;

    @Resource
    private StockBookService stockBookService;

    @Lazy
    @Resource
    private SaleShipNoticeApproveHandler saleShipNoticeApproveHandler;

    @Resource
    private SaleShipNoticeFlowCallBackHandler saleShipNoticeFlowCallBackHandler;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpSaleOrderDetailMapper erpSaleOrderDetailMapper;

    @Resource
    private ErpOutboundDetailMapper erpOutboundDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long shipNoticeAdd(ShipNoticeAditReqVO reqVO) {

        // 生成单号
        String code = seqService.getGenerateCode(reqVO.getShipNoticeCode(), MenuEnum.SALES_OUTBOUND_NOTICE_ORDER.menuId);

        // 插入
        SaleShipNoticeDO shipNoticeDO = BeanUtilX.copy(reqVO, SaleShipNoticeDO::new);
        shipNoticeDO.setShipNoticeCode(code);
        shipNoticeMapper.insert(shipNoticeDO);

        //产品明细新增处理
        reqVO.setShipNoticeId(shipNoticeDO.getShipNoticeId());
        reqVO.setShipNoticeCode(shipNoticeDO.getShipNoticeCode());
        List<SaleShipNoticeDetailDO> saleDetailList = getDetailEditList(reqVO);
        shipNoticeDetailMapper.insertBatch(saleDetailList);

        // 返回
        return shipNoticeDO.getShipNoticeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long shipNoticeEdit(ShipNoticeAditReqVO reqVO) {
        // 校验存在
//        this.shipNoticeValidateExists(reqVO.getShipNoticeId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.shipNoticeValidateExists(reqVO.getShipNoticeId()), reqVO);

        // 更新
        SaleShipNoticeDO shipNotice = BeanUtilX.copy(reqVO, SaleShipNoticeDO::new);
        List<SaleShipNoticeDetailDO> saleDetailList = getDetailEditList(reqVO);

        shipNoticeDetailMapper.batchDelete(reqVO.getShipNoticeId());
        shipNoticeMapper.updateById(shipNotice);
        shipNoticeDetailMapper.insertBatch(saleDetailList);

        // 返回
        return shipNotice.getShipNoticeId();
    }

    @Override
    public void shipNoticeDel(Long id) {
        // 校验存在
//        this.shipNoticeValidateExists(shipNoticeId);

        shipNoticeMapper.deleteById(id);
        shipNoticeDetailMapper.batchDelete(id);
    }

    @Override
    public ResultX<BatchResult> shipNoticeDelBatch(IdReq reqVO) {
        // 校验存在
//        this.saleDeductionValidateExists(deductionOrderId);

        //获取对象属性名
        String id = EntityUtilX.getPropertyName(SaleShipNoticeDO::getShipNoticeId);
        String code = EntityUtilX.getPropertyName(SaleShipNoticeDO::getShipNoticeCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), SaleShipNoticeDO.class, SaleShipNoticeDetailDO.class, id, code);

    }

    private SaleShipNoticeDO shipNoticeValidateExists(Long shipNoticeId) {
        SaleShipNoticeDO shipNotice = shipNoticeMapper.selectById(shipNoticeId);
        if (shipNotice == null) {
            // throw exception(SHIP_NOTICE_NOT_EXISTS);
            throw new BizException("5001", "销售发货通知单不存在");
        }
        return shipNotice;
    }

    @Override
    public ShipNoticeRespVO shipNoticeDetail(ShipNoticePrimaryReqVO reqVO) {
        SaleShipNoticeDO shipNoticeDO = this.shipNoticeValidateExists(reqVO.getShipNoticeId());

        ShipNoticeRespVO shipNoticeResp = BeanUtilX.copy(shipNoticeDO, ShipNoticeRespVO::new);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(reqVO.getShipNoticeId().toString())).ifPresent(approveTask -> shipNoticeResp.setApproveTaskId(approveTask.getApproveTaskId()));
        //VO属性填充
        fillVoProperties(shipNoticeResp);

        //产品明细详情处理
        ShipNoticeDetailQueryReqVO detailQuery = new ShipNoticeDetailQueryReqVO();
        detailQuery.setShipNoticeId(shipNoticeResp.getShipNoticeId());
        List<ShipNoticeDetailRespVO> respVOList = BeanUtilX.copyList(shipNoticeDetailMapper.selectListOld(detailQuery), ShipNoticeDetailRespVO::new);
        //属性填充
        getDetailRespList(respVOList, reqVO);
        shipNoticeResp.setDetailList(respVOList);

        return shipNoticeResp;
    }

    @Override
    public ShipNoticeRespVO shipNoticeQuotedDetail(Long shipNoticeId) {
        SaleShipNoticeDO shipNoticeDO = shipNoticeMapper.selectById(shipNoticeId);

        ShipNoticeRespVO shipNoticeResp = BeanUtilX.copy(shipNoticeDO, ShipNoticeRespVO::new);
        if (ObjUtilX.isEmpty(shipNoticeResp)) {
            return shipNoticeResp;
        }

        //查询引用明细
        List<ShipNoticeDetailRespVO> respVOList = shipNoticeDetailMapper.outboundDetailQuotedList(shipNoticeResp.getShipNoticeId());
        //属性填充
        getDetailRespList(respVOList);
        shipNoticeResp.setDetailList(respVOList);

        // 可操作数量处理
        for (ShipNoticeDetailRespVO respVO : shipNoticeResp.getDetailList()) {

            //计算可用数量
            BigDecimal availableQty = stockBookService.getAvailableQty(shipNoticeResp.getShipNoticeId(), respVO.getMaterialId(),
                    respVO.getWarehouseOrgId(), respVO.getStockQty(), respVO.getLockedQty());
            respVO.setStockableQty(availableQty);

            if (respVO.getStockableQty() == null) {
                respVO.setOperableQty(BigDecimal.ZERO);
            } else if (respVO.getOutboundableQty().compareTo(respVO.getStockableQty()) <= 0) {
                respVO.setOperableQty(respVO.getOutboundableQty());
            } else {
                respVO.setOperableQty(respVO.getStockableQty());
            }
        }

        //处理可用数量小于等于0的数据
        List<ShipNoticeDetailRespVO> finalList = new ArrayList<>();
        Map<Long, List<ShipNoticeDetailRespVO>> materialIdMap = shipNoticeResp.getDetailList().stream().collect(Collectors.groupingBy(ShipNoticeDetailRespVO::getMaterialId));
        for (Map.Entry<Long, List<ShipNoticeDetailRespVO>> entry : materialIdMap.entrySet()) {
            List<ShipNoticeDetailRespVO> detailRespVOList = entry.getValue();
            Integer flag = detailRespVOList.size();
            for (ShipNoticeDetailRespVO detailRespVO : detailRespVOList) {
                if (detailRespVO.getStockableQty() != null && detailRespVO.getStockableQty().compareTo(BigDecimal.ZERO) > 0) {
                    finalList.add(detailRespVO);
                } else {
                    flag--;
                }
            }
            if (flag == 0) {
                ShipNoticeDetailRespVO detailRespVO = detailRespVOList.get(0);
                detailRespVO.setWarehouseOrgId(null);
                detailRespVO.setWarehouseOrgName(null);
                detailRespVO.setOperableQty(null);
                detailRespVO.setStockableQty(null);
                detailRespVO.setStockQty(null);
                finalList.add(detailRespVO);
            }
        }
        finalList.sort(Comparator.comparing(ShipNoticeDetailRespVO::getRowNo));
        shipNoticeResp.setDetailList(finalList);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(shipNoticeId.toString())).ifPresent(approveTask -> shipNoticeResp.setApproveTaskId(approveTask.getApproveTaskId()));


        return shipNoticeResp;
    }


    @Override
    public List<SaleShipNoticeDO> shipNoticeList(ShipNoticeQueryReqVO reqVO) {
        return shipNoticeMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ShipNoticeDetailResp> shipNoticePage(ShipNoticePageReqVO reqVO) {
        PageResult<SaleShipNoticeDO> shipNoticeDOPageResult = shipNoticeMapper.selectPage(reqVO);
        PageResult<ShipNoticeDetailResp> pageResult = BeanUtilX.copyPage(shipNoticeDOPageResult, ShipNoticeDetailResp::new);
        if (CollUtilX.isEmpty(shipNoticeDOPageResult.getList())) {
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    @Override
    public PageResult<ShipNoticeDetailResp> queryShipNoticeDetailPage(ShipNoticePageReqVO reqVO) {
        //物料条件查询
        if (StrUtilX.isNotEmpty(reqVO.getMaterialCode()) || StrUtilX.isNotEmpty(reqVO.getMaterialName())
                || reqVO.getMaterialCategoryDictId() != null || StrUtilX.isNotEmpty(reqVO.getSpecModel())) {
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialCode(reqVO.getMaterialCode());
            erpMaterialQuery.setMaterialName(reqVO.getMaterialName());
            erpMaterialQuery.setSpecModel(reqVO.getSpecModel());
            erpMaterialQuery.setMaterialCategoryDictId(reqVO.getMaterialCategoryDictId());
            List<Long> materialIdList = erpMaterialService.findMaterialIdList(erpMaterialQuery);
            reqVO.setMaterialIdList(materialIdList);
            if (CollUtilX.isEmpty(materialIdList)) {
                return PageResult.empty();
            }
        }

        IPage<ShipNoticeDetailResp> respVOIPage = shipNoticeMapper.queryShipNoticeDetailPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<ShipNoticeDetailResp> pageResult = PageUtilX.buildResult(respVOIPage);
        if (CollUtilX.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        // 查询可用数量(待处理)
        for (ShipNoticeDetailResp respVO : pageResult.getList()) {
            respVO.setOutboundableQty(new BigDecimal(20));
            respVO.setStockQty(respVO.getOutboundableQty());
            respVO.setStockableQty(respVO.getOutboundableQty());
            respVO.setOperableQty(respVO.getOutboundableQty());
        }

        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }


    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(ShipNoticeRespVO respVO) {
        //查询客户名称
        String customerName = erpBaseService.getERPCustomerNameById(respVO.getCustomerId());
        respVO.setCustomerName(customerName);

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.SALES_ORDER_TYPE.getDictCode(), SystemDictEnum.CONFIRM_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        String salesOrderTypeDictId = CustomerDictEnum.SALES_ORDER_TYPE.getDictCode() + "-" + respVO.getSalesOrderTypeDictId();
        respVO.setSalesOrderTypeDictName(dictMap.get(salesOrderTypeDictId));

        //查询负责人
        String directorName = erpBaseService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);

        //查询责任部门
        String directorOrgName = erpBaseService.getOrgNameById(respVO.getDirectorOrgId().toString());
        respVO.setDirectorOrgName(directorOrgName);

        //确认状态
        if (respVO.getConfirmStatus() != null) {
            String confirmStatus = SystemDictEnum.CONFIRM_STATUS.getDictCode() + "-" + respVO.getConfirmStatus();
            respVO.setConfirmStatusDictName(dictMap.get(confirmStatus));
        }
        if (respVO.getWarehouseOrgId() != null) {
            String warehouseOrgName = erpBaseService.getOrgNameById(respVO.getWarehouseOrgId());
            respVO.setWarehouseOrgName(warehouseOrgName);
        }

    }

    /**
     * VO属性填充-批量处理
     *
     * @param deatilRespList
     */
    private void batchFillVoProperties(List<ShipNoticeDetailResp> deatilRespList) {

        if (CollUtilX.isEmpty(deatilRespList)) {
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.SALES_ORDER_TYPE.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode(), SystemDictEnum.CONFIRM_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询责任部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameMap();

        Map<Long, String> empNameMap = new HashMap<>();
        Map<Long, String> customerNameMap = new HashMap<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> empIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        List<Long> customerIdList = new ArrayList<>();
        for (ShipNoticeDetailResp deatilResp : deatilRespList) {
            empIdList.add(deatilResp.getDirectorId());
            materialIdList.add(deatilResp.getMaterialId());
            customerIdList.add(deatilResp.getCustomerId());
        }

        //查询客户名称
        customerNameMap = erpBaseService.getERPCustomerNameByIdList(customerIdList);

        //查询负责人
        empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)) {
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        for (ShipNoticeDetailResp deatilResp : deatilRespList) {

            //客户名称
            deatilResp.setCustomerName(customerNameMap.get(deatilResp.getCustomerId()));

            // 销售订单类型
            String salesOrderTypeDictId = CustomerDictEnum.SALES_ORDER_TYPE.getDictCode() + "-" + deatilResp.getSalesOrderTypeDictId();
            deatilResp.setSalesOrderTypeDictName(dictMap.get(salesOrderTypeDictId));

            //责任人属性填充
            deatilResp.setDirectorName(empNameMap.get(deatilResp.getDirectorId()));

            //责任部门属性填充
            deatilResp.setDirectorOrgName(orgNameMap.get(deatilResp.getDirectorOrgId().toString()));

            //收货仓库属性填充
            deatilResp.setWarehouseOrgName(orgNameMap.get(deatilResp.getWarehouseOrgId()));

            //基本单位
            String mainUnitDictId = deatilResp.getMainUnitDictId();
            if (StrUtilX.isNotEmpty(mainUnitDictId)) {
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                deatilResp.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            //审核状态
            if (deatilResp.getDataStatus() != null) {
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + deatilResp.getDataStatus();
                deatilResp.setDataStatusDictName(dictMap.get(dataStatus));
                ;
            }

            //确认状态
            if (deatilResp.getConfirmStatus() != null) {
                String confirmStatus = SystemDictEnum.CONFIRM_STATUS.getDictCode() + "-" + deatilResp.getConfirmStatus();
                deatilResp.setConfirmStatusDictName(dictMap.get(confirmStatus));
                ;
            }

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(deatilResp.getMaterialId());
            if (erpMaterialDO != null) {
                deatilResp.setMaterialCode(erpMaterialDO.getMaterialCode());
                deatilResp.setMaterialName(erpMaterialDO.getMaterialName());
                deatilResp.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                deatilResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                deatilResp.setSpecModel(erpMaterialDO.getSpecModel());
                deatilResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
        }
    }

    /**
     * 产品明细新增/编辑处理
     *
     * @param reqVO
     * @return
     */
    private List<SaleShipNoticeDetailDO> getDetailEditList(ShipNoticeAditReqVO reqVO) {
        List<SaleShipNoticeDetailDO> saleDetailList = new ArrayList<>();
        for (ShipNoticeDetailRespVO detail : reqVO.getDetailList()) {
            SaleShipNoticeDetailDO shipNoticeDetailDO = BeanUtilX.copy(detail, SaleShipNoticeDetailDO::new);
            shipNoticeDetailDO.setShipNoticeId(reqVO.getShipNoticeId());
            shipNoticeDetailDO.setShipNoticeCode(reqVO.getShipNoticeCode());
            shipNoticeDetailDO.setOutboundableQty(detail.getNoticeQty());
            saleDetailList.add(shipNoticeDetailDO);
        }
        return saleDetailList;
    }

    private List<ShipNoticeDetailRespVO> getDetailRespList(List<ShipNoticeDetailRespVO> respVOList) {
        return getDetailRespList(respVOList, null);
    }

    /**
     * 产品明细详情处理
     *
     * @param respVOList
     * @return
     */
    private List<ShipNoticeDetailRespVO> getDetailRespList(List<ShipNoticeDetailRespVO> respVOList, ShipNoticePrimaryReqVO reqVO) {
        if (CollUtilX.isEmpty(respVOList)) {
            return respVOList;
        }

        List<Long> materialIdList = new ArrayList<>();
        List<String> whOrgIdList = new ArrayList<>();
        for (ShipNoticeDetailRespVO item : respVOList) {
            materialIdList.add(item.getMaterialId());
            whOrgIdList.add(item.getWarehouseOrgId());
        }

        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        if (erpMaterialDOMap.isEmpty()) {
            return respVOList;
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());


        Map<String, String> whOrgMap = erpBaseService.getOrgNameByIds(whOrgIdList);

        Map<Long, BigDecimal> deliveredQtyMap = new HashMap<>();
        if (reqVO != null && reqVO.getIsPendingSaleNotice() != null && reqVO.getIsPendingSaleNotice() == 1) {
            Integer isPendingSaleNotice = reqVO.getIsPendingSaleNotice();
            List<DocumentRespBO> deliveredQtyList = erpOutboundDetailMapper.deliveredQtyList(reqVO.getShipNoticeId());
            deliveredQtyMap = deliveredQtyList.stream().collect(Collectors.toMap(DocumentRespBO::getMaterialId, DocumentRespBO::getSumQty));
        }

        //填充物料基本信息
        for (ShipNoticeDetailRespVO detailRespVO : respVOList) {
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailRespVO.getMaterialId());
            if (erpMaterialDO != null) {
                detailRespVO.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailRespVO.setMaterialName(erpMaterialDO.getMaterialName());
                detailRespVO.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailRespVO.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailRespVO.setSpecModel(erpMaterialDO.getSpecModel());
                detailRespVO.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }

            //基本单位
            if (StrUtilX.isNotEmpty(detailRespVO.getMainUnitDictId())) {
                detailRespVO.setMainUnitDictName(dictMap.get(detailRespVO.getMainUnitDictId()));
            }

            //仓库名称
            if (detailRespVO.getWarehouseOrgId() != null) {
                detailRespVO.setWarehouseOrgName(whOrgMap.get(detailRespVO.getWarehouseOrgId()));
            }

            //已发货数量
            if (reqVO != null && reqVO.getIsPendingSaleNotice() != null && reqVO.getIsPendingSaleNotice() == 1) {
                BigDecimal deliveredQty = deliveredQtyMap.get(detailRespVO.getMaterialId());
                if (deliveredQty == null) {
                    deliveredQty = BigDecimal.ZERO;
                }
                detailRespVO.setDeliveredQty(deliveredQty);
            }
        }

        return respVOList;
    }

    @Override
    public BatchResult shipNoticeApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<SaleShipNoticeDO> list = shipNoticeMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (SaleShipNoticeDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();
                //流程处理
                FailItem failItem = saleShipNoticeApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                exception.printStackTrace();
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getShipNoticeCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()) {
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (SaleShipNoticeDO item : list) {
                String reason = reasonMap.get(item.getShipNoticeCode());
                if (StrUtilX.isEmpty(reason)) {
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getShipNoticeId());
                    messageInfoBO.setObjCode(item.getShipNoticeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                } else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getShipNoticeId());
                    messageInfoBO.setObjCode(item.getShipNoticeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object shipNoticeFlowCallback(FlowCallback reqVO) {

        String objId = reqVO.getObjId();
        SaleShipNoticeDO item = this.shipNoticeValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();
        return saleShipNoticeFlowCallBackHandler.handleFlowCallback(item, flowCallbackBO);
    }

    @Transactional
    @Override
    public void shipNoticeConfirmStatus(ShipNoticePrimaryReqVO reqVO) {
        SaleShipNoticeDO shipNoticeDO = this.shipNoticeValidateExists(reqVO.getShipNoticeId());

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String curMenuId = loginUser.getCurMenuId();
        String userName = loginUser.getUserName();
        String templateCode = NoticeTemplateEnum.L1.templateCode;

        String button = ConfirmStatusEnum.APPROVED.desc;
        if (reqVO.getConfirmStatus() == ConfirmStatusEnum.REJECTED.type) {
            button = ConfirmStatusEnum.REJECTED.desc;
        }

        if (shipNoticeDO.getConfirmStatus() != ConfirmStatusEnum.PENDING.type) {
            throw exceptionMsg("待确认状态下才可进行" + button + "操作");
        }

        //操作内容
        String operate = "「" + userName + "」" + button;

        //更新确认状态
        shipNoticeDO.setConfirmStatus(reqVO.getConfirmStatus());
        shipNoticeDO.setRejectReason(reqVO.getRejectReason());

        shipNoticeMapper.updateById(shipNoticeDO);

        //拒绝不需要回写已通知数量
        if (reqVO.getConfirmStatus() == ConfirmStatusEnum.REJECTED.type) {
            messageTemplateService.sendMessage(templateCode, curMenuId, shipNoticeDO.getShipNoticeId(), shipNoticeDO.getShipNoticeCode(), Arrays.asList(shipNoticeDO.getCreatedId()), operate);
            return;
        }

        //查询销售订单
        ErpSaleOrderDetailQueryReqVO erpSaleOrderDetailQuery = new ErpSaleOrderDetailQueryReqVO();
        erpSaleOrderDetailQuery.setSaleOrderId(shipNoticeDO.getSaleOrderId());
        List<ErpSaleOrderDetailDO> erpSaleOrderDetailList = erpSaleOrderDetailMapper.selectList(erpSaleOrderDetailQuery);

        //当前发货明细
        ShipNoticeDetailQueryReqVO shipNoticeDetailQuery = new ShipNoticeDetailQueryReqVO();
        shipNoticeDetailQuery.setShipNoticeId(reqVO.getShipNoticeId());
        List<SaleShipNoticeDetailDO> detailDOS = shipNoticeDetailMapper.selectListOld(shipNoticeDetailQuery);
        Map<Long, SaleShipNoticeDetailDO> detailQtyMap = detailDOS.stream().collect(Collectors.toMap(SaleShipNoticeDetailDO::getMaterialId, detailDO -> detailDO));

        for (ErpSaleOrderDetailDO saleOrderDetail : erpSaleOrderDetailList) {
            BigDecimal updateQty = BigDecimal.ZERO;
            BigDecimal notifiedQty = saleOrderDetail.getNotifiedQty();

            SaleShipNoticeDetailDO detailDO = detailQtyMap.get(saleOrderDetail.getMaterialId());
            if (detailDO != null) {
                updateQty = notifiedQty.add(detailDO.getNoticeQty());
                saleOrderDetail.setNotifiedQty(updateQty);
            }
        }

        //更新销售物料已通知数量
        if (CollUtilX.isNotEmpty(erpSaleOrderDetailList)) {
            erpSaleOrderDetailMapper.updateBatch(erpSaleOrderDetailList);
        }

        messageTemplateService.sendMessage(templateCode, curMenuId, shipNoticeDO.getShipNoticeId(), shipNoticeDO.getShipNoticeCode(), Arrays.asList(shipNoticeDO.getCreatedId()), operate);
    }

}
