package com.mongoso.mgs.module.finance.controller.admin.workbench.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 财务数据 RespVO
 *
 * <AUTHOR>
 */
@Data
public class FinanceDataRespVO implements Serializable {

    /** 银行账户余额 */
    private BigDecimal accountBalance;

    /** 应收账款 */
    private BigDecimal salePendingPaymentAmt;

    /** 应付账款 */
    private BigDecimal purchasePendingPaymentAmt;

    /** 固定资产净值 */
    private BigDecimal assetNetValue;

    /** 预收款 */
    private BigDecimal advanceReceivePaymentAmt;

    /** 预付款 */
    private BigDecimal advanceExpensePaymentAmt;

    /** 未入账承兑汇票 */
    private BigDecimal acceptBillPendingInBillAmt;
}
