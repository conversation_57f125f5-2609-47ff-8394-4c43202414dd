package com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 


/**
 * 库存预订明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StockBookDetailPageReqVO extends PageParam {

    /** 库存预订任务ID */
    private Long stockBookId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 仓库ID */
    private String warehouseOrgId;

    /** 预订数量 */
    private BigDecimal bookQty;

    /** 可预订数量 */
    private BigDecimal bookableQty;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
