package com.mongoso.mgs.module.warehouse.service.erpreceipt;

import java.math.BigDecimal;
import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailRespVO;

/**
 * 收货单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpReceiptDetailService {

    /**
     * 创建收货单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpReceiptDetailAdd(@Valid ErpReceiptDetailAditReqVO reqVO);

    /**
     * 更新收货单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpReceiptDetailEdit(@Valid ErpReceiptDetailAditReqVO reqVO);

    /**
     * 删除收货单明细
     *
     * @param id 编号
     */
    void erpReceiptDetailDel(Long id);

    /**
     * 获得收货单明细信息
     *
     * @param id 编号
     * @return 收货单明细信息
     */
    ErpReceiptDetailRespVO erpReceiptDetailDetail(Long id);

    /**
     * 获得收货单明细列表
     *
     * @param reqVO 查询条件
     * @return 收货单明细列表
     */
    List<ErpReceiptDetailRespVO> erpReceiptDetailList(@Valid ErpReceiptDetailQueryReqVO reqVO);

    /**
     * 获得收货单明细分页
     *
     * @param reqVO 查询条件
     * @return 收货单明细分页
     */
    PageResult<ErpReceiptDetailRespVO> erpReceiptDetailPage(@Valid ErpReceiptDetailPageReqVO reqVO);

    /**
     * 更新检验结果
     *
     * @param receiptDetailId 收货单明细ID
     * @param checkReuslt 检验结果
     * @param okQty 良品数
     * @param ngQty 不良品数
     */
     void updateCheckResult(Long receiptDetailId, Integer checkReuslt, BigDecimal okQty, BigDecimal ngQty);

    /**
     * 更新入库数量(入库审核更新)
     *
     * @param receiptDetailId 收货单明细ID
     * @param inboundQty 入库数量
     */
    void updateInboundedQty(Long receiptDetailId, BigDecimal inboundQty);

    /**
     * 更新是否完成检验
     *
     * @param receiptDetailId 收货单明细ID
     * @param isMaterialFullCheck 是否检验完成 0-否 1-是
     * @param checkQty 检验数量
     * @param allowDefectsQty 允许缺陷数量
     */
    void updateIsFullCheck(Long receiptDetailId, Integer isMaterialFullCheck,
                           BigDecimal checkQty, BigDecimal allowDefectsQty);
}
