package com.mongoso.mgs.module.sale.controller.admin.customerpriceplan.vo;

import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 客户价格方案 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustomerPricePlanRespVO extends CustomerPricePlanBaseVO {

    /** 审核状态 */
    private String dataStatusDictName;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;


    /** 物料ID */
    private Long materialId;

    /** 客户订货价 */
    private BigDecimal customerOrderPrice;

    /** 审批任务id */
    private Long approveTaskId;



}
