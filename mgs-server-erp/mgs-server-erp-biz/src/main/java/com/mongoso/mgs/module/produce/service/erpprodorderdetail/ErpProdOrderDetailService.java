package com.mongoso.mgs.module.produce.service.erpprodorderdetail;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderdetail.vo.*;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 生产物料 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpProdOrderDetailService {

    /**
     * 创建生产物料
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpProdOrderDetailAdd(@Valid ErpProdOrderDetailAditReqVO reqVO);

    /**
     * 更新生产物料
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpProdOrderDetailEdit(@Valid ErpProdOrderDetailAditReqVO reqVO);

    /**
     * 更新生产物料
     *
     * @param reqVOList 更新信息
     * @return 编号
     */
    boolean erpProdOrderDetailEdit(List<ErpProdOrderDetailAditReqVO> reqVOList);


    /**
     * 删除生产物料
     *
     * @param prodOrderDetailId 编号
     */
    void erpProdOrderDetailDel(Long prodOrderDetailId);

    /**
     * 获得生产物料信息
     *
     * @param prodOrderDetailId 编号
     * @return 生产物料信息
     */
    ErpProdOrderDetailRespVO erpProdOrderDetailDetail(Long prodOrderDetailId);

    /**
     * 获得生产物料信息
     *
     * @param prodOrderId 编号
     * @return 生产物料信息
     */
    ErpProdOrderDetailRespVO erpProdOrderDetail(Long prodOrderId,Long materialId);

    /**
     * 获得生产物料列表
     *
     * @param reqVO 查询条件
     * @return 生产物料列表
     */
    List<ErpProdOrderDetailRespVO> erpProdOrderDetailList(@Valid ErpProdOrderDetailQueryReqVO reqVO);

    /**
     * 获得生产物料分页
     *
     * @param reqVO 查询条件
     * @return 生产物料分页
     */
    PageResult<ErpProdOrderDetailRespVO> erpProdOrderDetailPage(@Valid ErpProdOrderDetailPageReqVO reqVO);

    Long queryMaterialBomId(Long materialId);
}
