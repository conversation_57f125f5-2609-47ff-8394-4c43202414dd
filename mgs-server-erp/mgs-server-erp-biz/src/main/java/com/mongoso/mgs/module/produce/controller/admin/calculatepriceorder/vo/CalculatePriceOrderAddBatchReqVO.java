package com.mongoso.mgs.module.produce.controller.admin.calculatepriceorder.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 计价单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CalculatePriceOrderAddBatchReqVO extends CalculatePriceOrderBaseVO {

    @NotEmpty(message = "规则 不能为空")
    private List<CalculatePriceOrderRuleAddReqVO> detailList;



}
