package com.mongoso.mgs.module.finance.service.shouldpaymentconf;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpsupplier.vo.ERPSupplierQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.erpcustomer.ERPCustomerDO;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpcustomer.ERPCustomerMapper;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpsupplier.ERPSupplierService;
import com.mongoso.mgs.module.finance.controller.admin.shouldpaymentconf.vo.*;
import com.mongoso.mgs.module.finance.dal.db.shouldpaymentconf.ShouldPaymentConfDO;
import com.mongoso.mgs.module.finance.dal.mysql.shouldpaymentconf.ShouldPaymentConfMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.should.enums.ErrorCodeConstants.*;


/**
 * 应收账龄配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShouldPaymentConfServiceImpl implements ShouldPaymentConfService {

    @Resource
    private ShouldPaymentConfMapper paymentConfMapper;
    @Resource
    private ERPCustomerMapper erpCustomerMapper;
    @Resource
    private ERPSupplierMapper erpSupplierMapper;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ERPSupplierService erpSupplierService;

    @Override
    public Long shouldPaymentConfAdd(ShouldPaymentConfAditReqVO reqVO) {
        // 插入
        ShouldPaymentConfDO paymentConf = BeanUtilX.copy(reqVO, ShouldPaymentConfDO::new);
        paymentConfMapper.insert(paymentConf);
        // 返回
        return paymentConf.getConfId();
    }

    @Override
    public Long shouldPaymentConfEdit(ShouldPaymentConfAditReqVO reqVO) {
        // 校验存在
        this.shouldPaymentConfValidateExists(reqVO.getConfId());
        // 更新
        ShouldPaymentConfDO paymentConf = BeanUtilX.copy(reqVO, ShouldPaymentConfDO::new);
        paymentConfMapper.updateById(paymentConf);
        // 返回
        return paymentConf.getConfId();
    }

    @Override
    public void shouldPaymentConfDel(Long confId) {
        // 校验存在
        this.shouldPaymentConfValidateExists(confId);
        // 删除
        paymentConfMapper.deleteById(confId);
    }

    private ShouldPaymentConfDO shouldPaymentConfValidateExists(Long confId) {
        ShouldPaymentConfDO paymentConf = paymentConfMapper.selectById(confId);
        if (paymentConf == null) {
            // throw exception(PAYMENT_CONF_NOT_EXISTS);
            throw new BizException("5001", "应收账龄配置不存在");
        }
        return paymentConf;
    }

    @Override
    public ShouldPaymentConfRespVO shouldPaymentConfDetail(Long confId) {
        ShouldPaymentConfDO data = paymentConfMapper.selectById(confId);
        return BeanUtilX.copy(data, ShouldPaymentConfRespVO::new);
    }

    @Override
    public List<ShouldPaymentConfRespVO> shouldPaymentConfList(ShouldPaymentConfQueryReqVO reqVO) {
        List<ShouldPaymentConfDO> data = paymentConfMapper.selectList(reqVO);
//        if(ObjUtilX.isEmpty(data)){
//            // 当查询结果为空时，添加初始化数据
//            List<ShouldPaymentConfDO> defaultConfigs = initializeDefaultConfigs(reqVO.getFormType());
//
//            // 可选择性的插入到数据库
//            paymentConfMapper.insertBatch(defaultConfigs); // 请确保有此批量插入方法
//
//            // 返回初始化的数据
//            return BeanUtilX.copy(defaultConfigs, ShouldPaymentConfRespVO::new);
//        }
        return BeanUtilX.copy(data, ShouldPaymentConfRespVO::new);
    }

    // 初始化默认配置的方法
    private List<ShouldPaymentConfDO> initializeDefaultConfigs(Short formType) {
        List<ShouldPaymentConfDO> defaultConfigs = new ArrayList<>();

        String[] titles = {"30天", "90天", "半年", "一年", "三年"};
        Integer[] distanceDays = {30, 90, 180, 365, 1095};

        for (int i = 0; i < titles.length; i++) {
            ShouldPaymentConfDO config = new ShouldPaymentConfDO();
            config.setFormType(formType);
            config.setTitle(titles[i]);
            config.setDistanceDays(distanceDays[i]);
            config.setSort(i); // 可以根据需要设置排序
            defaultConfigs.add(config);
        }

        return defaultConfigs;
    }

    @Override
    public PageResult<ShouldPaymentConfRespVO> shouldPaymentConfPage(ShouldPaymentConfPageReqVO reqVO) {
        PageResult<ShouldPaymentConfDO> data = paymentConfMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ShouldPaymentConfRespVO::new);
    }

    @Override
    @Transactional
    public Boolean shouldPaymentConfSave(ShouldPaymentConfSaveReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getConfList())){
            throw new BizException("5001", "请输入应收账龄配置");
        }
        // 清空现有的配置
        paymentConfMapper.delete(LambdaQueryWrapperX.<ShouldPaymentConfDO>lambdaQueryX()
                .eq(ShouldPaymentConfDO::getFormType, reqVO.getFormType())
        );

        // 使用集合来存储已存在的标题和距离天数
        Set<String> titleSet = new HashSet<>();
        Set<Integer> distanceDaysSet = new HashSet<>();
        // 保存新的配置
        List<ShouldPaymentConfDO> batchs = new ArrayList<>();
        // 按照 distanceDays 从小到大排序
        reqVO.getConfList().sort(Comparator.comparingInt(ShouldPaymentConfAditReqVO::getDistanceDays));
        int sort = 0;
        for (ShouldPaymentConfAditReqVO config : reqVO.getConfList()) {
            // 验证标题是否重复
            if (!titleSet.add(config.getTitle())) {
                throw new BizException("5002", "标题 '" + config.getTitle() + "' 不能重复添加");
            }

            // 验证距离天数是否重复
            if (!distanceDaysSet.add(config.getDistanceDays())) {
                throw new BizException("5003", "距离天数 '" + config.getDistanceDays() + "' 不能重复添加");
            }
            ShouldPaymentConfDO conf = BeanUtilX.copy(config, ShouldPaymentConfDO::new);
            conf.setSort(sort++);
            conf.setFormType(reqVO.getFormType());
            // 可以进行必要的验证或处理
            batchs.add(conf);
        }
        return paymentConfMapper.insertBatch(batchs);
    }

    @Override
    public PageResult<Map<String, Object>> shouldPaymentAnalysis(ShouldPaymentAnalysisReqVO reqVO) {
        //查询配置
        List<ShouldPaymentConfDO> confList = paymentConfMapper.selectList(LambdaQueryWrapperX.<ShouldPaymentConfDO>lambdaQueryX()
                .eq(ShouldPaymentConfDO::getFormType, reqVO.getFormType())
                .orderByAsc(ShouldPaymentConfDO::getDistanceDays)
        );
        if (CollUtilX.isEmpty(confList)){
            // 初始化
            confList = initializeDefaultConfigs(reqVO.getFormType());
                        // 可选择性的插入到数据库
            paymentConfMapper.insertBatch(confList); // 请确保有此批量插入方法
        }

        // 客户列表
        if(ObjUtilX.isNotEmpty(reqVO.getCustomerName())) {
            List<Long> customerIdList = null;
            if (reqVO.getFormType() == 1) {//销售
                //查询客户名称
                ERPCustomerQueryReqVO custmReq = new ERPCustomerQueryReqVO();
                custmReq.setCustomerName(reqVO.getCustomerName());
                List<ERPCustomerDO> customerList = erpCustomerMapper.selectList(custmReq);
                if (CollUtilX.isEmpty(customerList)) {
                    return PageResult.empty();
                }
                customerIdList = customerList.stream().map(ERPCustomerDO::getCustomerId).collect(Collectors.toList());
            } else {//采购结算池
                //查询供应商
                ERPSupplierQueryReqVO custmReq = new ERPSupplierQueryReqVO();
                custmReq.setSupplierName(reqVO.getCustomerName());
                List<ERPSupplierDO> customerList = erpSupplierMapper.selectList(custmReq);
                if (CollUtilX.isEmpty(customerList)) {
                    return PageResult.empty();
                }
                customerIdList = customerList.stream().map(ERPSupplierDO::getSupplierId).collect(Collectors.toList());
            }
            reqVO.setCustomerIdList(customerIdList);
        }

        // 动态表头
        List<String> titleList = new ArrayList<>();
//        titleList.add("客户名称");
//        titleList.add("币种");
        JSONObject custJson = new JSONObject();
        if (reqVO.getFormType() == 1) {//销售
            custJson.put("title", "客户名称");
        } else {
            custJson.put("title", "供应商名称");
        }
        titleList.add(custJson.toJSONString());

        JSONObject currJson = new JSONObject();
        currJson.put("title", "结算币种");
        titleList.add(currJson.toJSONString());

        // 计算日期范围
        // 应收日期 为空就取今天
        LocalDate today = ObjUtilX.isNotEmpty(reqVO.getRecDate()) ? reqVO.getRecDate() : LocalDate.now();
        // 按照 distanceDays 从小到大排序
        confList.sort(Comparator.comparingInt(ShouldPaymentConfDO::getDistanceDays));
        // 初始化上一条记录的 distanceDays
        Integer previousDistanceDays = 0;
        for (ShouldPaymentConfDO conf : confList) {
            // 当前记录的 distanceDays
            Integer currentDistanceDays = conf.getDistanceDays();

            // 开始日期: 今天 - 当前记录的距离天数
            LocalDate startDate = today.minusDays(currentDistanceDays);

            // 结束日期: 如果有上一条记录则为 今天 - 上一条记录的距离天数, 否则为 0
            LocalDate endDate;
            if (previousDistanceDays != null) {
                endDate = today.minusDays(previousDistanceDays);
            } else {
                endDate = today;
            }
            endDate = endDate.minusDays(1);
            // 打印
//            System.out.println("标题: " + conf.getTitle()
//                    + ", 开始日期: " + startDate
//                    + ", 结束日期: " + endDate);
            conf.setStartDay(startDate);
            conf.setEndDay(endDate);
            // 返回日期
//            titleList.add(conf.getTitle());
            JSONObject json = new JSONObject();
            json.put("title", conf.getTitle());
            json.put("startDate", startDate);
            json.put("endDate", endDate);
            titleList.add(json.toJSONString());

            // 更新上一条记录的 distanceDays
            previousDistanceDays = currentDistanceDays;
        }

        String totalStr = "行合计";
        if(confList.size() > 0){
            ShouldPaymentConfDO lastConf = confList.get(confList.size() -1);
            Integer distanceDays = lastConf.getDistanceDays();
            LocalDate startDay = lastConf.getStartDay();
            LocalDate endDate = today.minusDays(1);
            JSONObject json = new JSONObject();
            json.put("title", totalStr);
            json.put("startDate", startDay);
            json.put("endDate", endDate);
            titleList.add(json.toJSONString());
            ShouldPaymentConfDO conf = new ShouldPaymentConfDO();
            conf.setTitle(totalStr);
            conf.setDistanceDays(distanceDays);
            conf.setStartDay(startDay);
            conf.setEndDay(endDate);
            confList.add(conf);
//            System.out.println("标题: " + conf.getTitle()
//                    + ", 开始日期: " + startDay
//                    + ", 结束日期: " + endDate);
        }
//        for (ShouldPaymentConfDO confDO : confList) {
//            confDO.setDayStr(reqVO.getRecDate().minusDays(confDO.getDistanceDays()));
//            titleList.add(confDO.getTitle());
//        }
        reqVO.setConfList(confList);

        IPage<Map<String, Object>> analysisList = paymentConfMapper.getAnalysisList(PageUtilX.buildParam(reqVO), reqVO);
        if (CollUtilX.isEmpty(analysisList.getRecords())){
            return PageResult.empty();
        }

        List<Long> customerIdList = new ArrayList<>();
        for (Map<String, Object> record : analysisList.getRecords()) {
            if(record.containsKey("customer_id")) {
                customerIdList.add(Long.valueOf(record.get("customer_id").toString()));
            }
        }
        //查询客户名称
        PageResult<Map<String, Object>> pageResult = PageUtilX.buildResult(analysisList);

        Map<Long, String> customerNameMap = new HashMap<>();
        if(confList.get(0).getFormType() == 1){//销售
            //查询客户名称
            customerNameMap = erpBaseService.getERPCustomerNameByIdList(customerIdList);
        } else {//采购结算池
            //查询供应商
            customerNameMap = erpSupplierService.erpSupplierNameMap(customerIdList);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.CURRENCY.getDictCode());

        for (Map<String, Object> map : pageResult.getList()) {
            //客户名称
            Map<String, Object> customer = new HashMap<>(2);
            Long customerId = Long.valueOf(map.getOrDefault("customer_id", "0").toString());
            customer.put("name", customerNameMap.getOrDefault(customerId, ""));
            customer.put("id", customerId);
            map.remove("customer_id");
            if (reqVO.getFormType() == 1) {//销售
                map.put("客户名称", customer);
            } else {
                map.put("供应商名称", customer);
            }

            //币种
            Map<String, Object> currency = new HashMap<>(2);
            String currencyId = map.getOrDefault("currency_dict_id", "0").toString();
            currency.put("name", dictMap.getOrDefault(currencyId, ""));
            currency.put("id", currencyId);
            map.remove("currency_dict_id");
            map.put("结算币种", currency);

            for (ShouldPaymentConfDO conf : confList) {
                Map<String, Object> datas = new HashMap<>(1);
                BigDecimal amt = new BigDecimal(map.getOrDefault(conf.getTitle(), "0").toString());
                datas.put("name", amt);
                map.remove(conf.getTitle());
                map.put(conf.getTitle(), datas);
            }
        }
        return PageResult.init(pageResult, pageResult.getList(), titleList);
    }


    @Override
    public PageResult<SalePaymentAnalysisRespVO> salePaymentAnalysis(SalePaymentAnalysisReqVO reqVO) {
        if (StrUtilX.isNotEmpty(reqVO.getStrEndMonth()) && StrUtilX.isNotEmpty(reqVO.getStrEndMonth())){
            LocalDate startDate = parseToFirstDayOfMonth(reqVO.getStrStartMonth());
            LocalDate endDate  = parseToLastDayOfMonth(reqVO.getStrEndMonth());
            reqVO.setStartMonth(startDate);
            reqVO.setEndMonth(endDate);
        }


        // 客户列表
        List<Long> customerIdList = new ArrayList<>();
        if (reqVO.getFormType() == 1){ //销售
            if (StringUtils.isNotEmpty(reqVO.getCustomerName())){
                ERPCustomerQueryReqVO custmReq = new ERPCustomerQueryReqVO();
                custmReq.setCustomerName(reqVO.getCustomerName());
                List<ERPCustomerDO> customerList = erpCustomerMapper.selectList(custmReq);
                if (CollUtilX.isNotEmpty(customerList)) {
                    customerIdList = customerList.stream().map(ERPCustomerDO::getCustomerId).collect(Collectors.toList());
                    reqVO.setCustomerIdList(customerIdList);
                }else {
                    return PageResult.empty();
                }

            }
        }else { //采购
            ERPSupplierQueryReqVO custmReq = new ERPSupplierQueryReqVO();
            custmReq.setSupplierName(reqVO.getCustomerName());
            List<ERPSupplierDO> customerList = erpSupplierMapper.selectList(custmReq);
            if (CollUtilX.isNotEmpty(customerList)) {
                customerIdList = customerList.stream().map(ERPSupplierDO::getSupplierId).collect(Collectors.toList());
                reqVO.setCustomerIdList(customerIdList);
            }else {
                return PageResult.empty();
            }
        }
        IPage<SalePaymentAnalysisRespVO> paymentIPage = paymentConfMapper.salePaymentAnalysis(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<SalePaymentAnalysisRespVO> pageResult = PageUtilX.buildResult(paymentIPage);

        if(CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList(),reqVO.getFormType());

        return pageResult;
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<SalePaymentAnalysisRespVO> respVOList,Integer formType) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> customerIdList = new ArrayList<>();
        List<String> currencyDictIdList = new ArrayList<>();

        for(SalePaymentAnalysisRespVO respVO : respVOList){
            customerIdList.add(respVO.getCustomerId());
            currencyDictIdList.add(respVO.getCurrencyDictId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.CURRENCY.getDictCode(),SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        Map<Long, String> customerMap = new HashMap<>();
        if (formType == 1){
            // 查询客户信息
            customerMap = erpBaseService.getERPCustomerNameByIdList(customerIdList);
        }else {
            // 查询客户信息
            customerMap = erpBaseService.getERPSupplierNameByIdList(customerIdList);
        }
        // 属性填充
        for (SalePaymentAnalysisRespVO item : respVOList) {

            //关联客户
            if(item.getCustomerId() != null){
                item.setCustomerName(customerMap.get(item.getCustomerId()));
            }

            // 币种
            String currencyDictId = item.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                item.setCurrencyDictName(dictMap.get(currencyDictId));
            }
        }
    }


    /**
     * 将 "yyyy-MM" 格式的字符串转换为当月的第一天
     */
    public LocalDate parseToFirstDayOfMonth(String dateStr) {
        YearMonth yearMonth = YearMonth.parse(dateStr);
        return yearMonth.atDay(1); // 返回当月第一天
    }

    /**
     * 将 "yyyy-MM" 格式的字符串转换为当月的最后一天
     */
    public LocalDate parseToLastDayOfMonth(String dateStr) {
        YearMonth yearMonth = YearMonth.parse(dateStr);
        return yearMonth.atEndOfMonth(); // 自动计算最后一天（28/29/30/31）
    }
}
