package com.mongoso.mgs.common.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 编码&单号统一生成工具
 *
 * <AUTHOR>
 */
public class CodeGenUtil {

    /**
     * 编码类型枚举, 用于维护编码场景
     *
     * <AUTHOR>
     */
    public enum CodeTypeEnums {

        // 物料编码: MA+5位序列号
        HZMATERIAL("MA", 9, "物料编码"),
        CUSTOMER("CUS", 5, "客户编码"),
        SPEC("SPEC", 5, "规格值编码"),
        ERPMATERIAL("MA", 8, "物料编码"),
        SPU("COM", 7, "SPU编码"),
        ERP_CUSTOMER("CUS", 5, "客户表"),
        ERP_SUPPLIER("SUP", 5, "供应商表"),
        PURCHASE_DEMAND("PRF", 5, true,  "采购需求单"),
        PURCHASE_ORDER("PO", 5, true,  "采购订单"),
        PURCHASE_PROCESS_OUT("OPPO", 5, true,  "工序委外采购订单"),
        PURCHASE_RECEIPT_NOTICE("PRN", 3, true, "采购收货通知单"),
        PURCHASE_RETURN("PR", 3, true, "采购退货单"),
        PUR_PROCESSOUT_RETURN("PPR", 3, true, "工序委外采购退货单"),
        PUR_PROCESSOUT_DEDUCTION("PPR", 3, true, "工序委外采购扣费单"),
        PURCHASE_EXCHANGE("PEG", 3, true, "采购换货单"),

        PURCHASE_DEDUCTION("PDF", 3, true, "采购扣费单"),
        SALE_ACCOUNT("SRC", 5, true,"销售对账单"),
        PURCHASE_ACCOUNT("ST", 5, true,"采购对账单"),
        SALE_SETTLEPOOL("SSA", 5, true,"销售结算单"),
        PURCHASE_SETTLEPOOL("SAT", 5, true,"采购结算单"),
        SALE_PLAN("PR", 4, true,"待收款计划"),
        PURCHASE_PLAN("PP", 4, true,"待付款计划"),
        SHOULD_SALE_PAYMENT("AR", 4, true,"销售应收账款"),
        SHOULD_PURCHASE_PAYMENT("AP", 4, true,"采购应付账款"),
        SALE_PAYMENT_APPLY("AC", 4, true,"销售收款申请"),
        PURCHASE_PAYMENT_APPLY("RFP", 4, true,"采购付款申请"),
        SALE_PAYMENT("RP", 4, true,"销售收款"),
        PURCHASE_PAYMENT("DP", 4, true,"采购付款"),
        SALE_REFUND("SR", 4, true,"销售退款"),
        PURCHASE_REFUND("PRFD", 4, true,"采购退款"),
        SALE_ADVANCE_REFUND("RUR", 4, true,"销售退预收款"),
        PURCHASE_ADVANCE_REFUND("RAPM", 4, true,"采购退预付款"),
        PURCHASE_CHANGE("PCO", 3, true,"采购订单变更单号"),
        SALE_ADVANCE("UR", 4, true,"销售预收款"),
        PURCHASE_ADVANCE("APM", 4, true,"采购预付款"),
        FEE_APPLY("EC", 4, true,"费用申请"),
        FEE_REIMBURSE("ERB", 4, true,"费用报销"),
        FEE_REIMBURSE_PAYMENT("DP", 4, true,"报销付款单号"),
        FEE_LOAN("EL", 4, true,"费用借款"),
        FEE_LOAN_PAYMENT("DP", 4, true,"费用借款付款"),
        FEE_RETURN("ERF", 4, true,"费用借款"),
        IN_BILL("DC", 4, true,"直接入账"),
        OUT_BILL("DB", 4, true,"直接出账"),
        INTER_ACCOUNT_TRAN("TBA", 4, true,"账间转账"),
        ACCEPT_BILL_PAYMENT("DP", 4, true,"承兑汇票付款"),
        ACCEPT_BILL_RECEIPT("RP", 4, true,"承兑汇票收款"),
        ASSET_REGISTER("AS", 5, "资产登记"),
        ASSET_CHANGRE("ASC", 4, true,"资产变动"),


        CUSTOMER_PRICE_PLAN_CODE("PS", 3, true, "客户价格方案"),
        SALE_ORDER_CODE("SO", 5, true, "销售订单"),
        SALE_QUOTE_CODE("SQ", 3, true, "销售报价单"),
        SALES_ORDER_CHANGE_CODE("SCO", 3, true, "销售订单变更单"),
        SHIP_NOTICE_CODE("SDN", 3, true, "销售发货通知单"),
        SALES_RETURN_CODE("SR", 3, true, "销售退货单"),
        DEDUCTION_ORDER_CODE("SD", 3, true, "销售扣费单"),

        SALE_EXCHANGE_CODE("SEG", 3, true, "销售换货单"),

        // 仓库
        STOCK_LOCK("ILO", 3, true, "库存锁定单表"),
        STOCK_UNLOCK("IUO", 3, true, "库存锁定单表"),
        MATERIAL_LOAN("LF", 5, true, "外借单"),
        MATERIAL_RETURN("RF", 5, true, "归还单"),
        RECEIPT("RN", 5, true, "收货单"),
        MATERIAL_CHECK("QC", 5, true, "检验单"),
        MATERIAL_CHECK_RESULT("RES", 5, true, "检验结果录入单"),
        INBOUND("MI", 5, true, "入库单"),
        OUTBOUND("MO", 5, true, "出库单"),
        DELIVERY("DN", 5, true, "发货单"),
        TRANSFER("TO", 5, true, "调拨单"),
        INVENTORY("IS", 5, true, "盘点单"),
        INVENTORY_PLAN("IPS", 5, true, "盘点计划"),
        ASSEMBLY("AO", 5, true, "组装单"),
        DISASSEMBLY("DO", 5, true, "拆卸单"),
        INVENTORY_SECURITY_STRATEGY("SSW", 3, true, "安全策略"),
        STOCK_ADJUST("AIF", 5, true, "库存调整"),

        INVOICE_PLAN_CODE("IP",4,true,"开票计划单号"),
        INVOICE_APPLY_CODE("IA",4,true,"开票申请单号"),
        INVOICE_COLLECT_PLAN_CODE("ICP",4,true,"收票计划单号"),
        INVOICE_COLLECT_APPLY_CODE("ICA",4,true,"收票申请单号"),

        PAYROLL("PRL", 5, true, "工资单"),
        PAYROLLPAYMENT("DP", 5, true, "工资单付款单"),


        //生产
        WORK_CENTER_CODE("WCC",3,true,"工作中心编码"),
        PROCESS_CODE("OR",3,true,"工序中心编码"),
        FLOW_PROCESS_CODE("OL",3,true,"工艺路线编码"),
        PROD_ORDER_CODE("PROP",5,true,"生产订单编码"),
        PROD_ORDER_CHANGE_CODE("ROC",5,true,"生产订单变更单编码"),
        PROD_WORK_CODE("WT",5,true,"生产工单编码"),
        DISPATCH_WORK_CODE("WOD",5,true,"派工单编码"),
        PROCESS_OPER_ATION_CODE("OPE",3,true,"操作流水编码"),
        PROCESS_OUT_DEMAND_CODE("ORL",3,true,"工序委外需求单号"),
        REPORTED_WORK_CODE("OP",5,true,"报工记录编码"),
        FLOWCARD_CODE("PCARD",3,true,"流转卡编码"),
        REPORT_BATCH_CODE("OBN",3,true,"报工批次编码"),
        DEVICEMOLD_CODE("MOLD",3,true,"上摸记录编码"),
        GOOD_PRO_CODE("GPV",3,true,"良品维度值编码"),
        REWORK_CODE("RDV",3,true,"返工维度值编码"),
        QUALITY_CODE("QIV",3,true,"质检维度值编码"),


        // 生产，模具
        WORK_MOLD_USE_CODE("REF",3,true,"模具领用单编码"),
        WORK_MOLD_RETURN_CODE("MRF",3,true,"模具归还单编码"),

        // 生产，物料分析
        WORK_MATERIAL_ANALYSIS_CODE("MAC",3,true,"物料分析编码"),


        WORK_WORK_PICKING_CODE("MAQ",3,true,"领料单编码"),
        WORK_WORK_PICKING_RETURN_CODE("MAT",3,true,"退料单编码"),

        // 不良原因编码
        WORK_DEFECT_REASON_CODE("DCC",3,true,"不良原因编码"),
        WORK_MATERIAL_ALTERNATIVE_CODE("TDLGX",4,true,"替代物料编码"),
        WORK_DAILY_AMPLING("Sam",3,true,"抽检记录编码"),
        WORK_CALCULATE_PRICE_DIMENSION("DVC",5,"计价维度编码"),
        WORK_CALCULATE_PRICE_ORDER_TIME("PJ",4,true,"计时单编码"),
        WORK_CALCULATE_PRICE_ORDER_UNIT("TM",4,true,"计件单编码"),

        //日成本
        COST_SUBJECT_MANAGE("CA", 4, true, "成本科目管理"),
        COST_PROD_PURCHASE_DETAIL ("PPC", 4, true, "生产采购成本单号"),
        COST_PROD_MATERIAL_DETAIL ("PMC", 4, true, "生产物料成本单号"),
        COST_PROD_LABOR_DETAIL ("PLC", 4, true, "生产人工成本单号"),
        FIXED_INVEST("FI", 3, true,"固定投入"),
        FIXED_INVEST_DETAIL("DAFI", 4, true,"固定投入日摊销"),
        FIXED_EXPENSE_TERM("FEI", 3, true, "固定支出项"),
        FIXED_EXPENSE("FE", 3, true, "固定支出"),
        FIXED_EXPENSE_DETAIL("DAFE", 4, true, "固定支出日摊销"),
        DEFERRED_CHARGES("DE", 3, true, "递延费用"),
        DEFERRED_CHARGES_DETAIL("DADE", 4, true, "递延费用日摊销"),
        DAILY_EXPENSE("DEX", 3, true, "每日支出"),
        DAILY_EXPENSE_DETAIL("DADP", 4, true, "每日支出日摊销"),
        SALARY_STRATEGY_CODE("PAS", 4, true, "工资归集策略编码"),
        COST_SALE_INCOME_DETAIL("SRE", 4, true, "销售收入明细"),
        COMMISSION_CONFIG("CS", 4, true, "提成规则"),
        COMMISSION_CONFIG_DETAIL("RDC", 4, true, "规则明细"),
        COMMISSION_TASK("CT", 4, true, "规则明细"),
        COMMISSION_GRANT("CP", 4, true, "规则明细"),



        UTILITY_ARCHIVE_CODE("UF",4,true,"水电气档案编号"),
        UTILITY_LOG_CODE("URF",4,true,"水电气记录单号"),
        UTILITY_COST_CODE("UE",4,true,"水电气费用单号"),

        ;

        //  前缀
        private final String prefix;

        //  序列号长度
        private final int length;

        //  是否包含日期
        private final boolean inclDate;

        //  描述
        private final String desc;

        CodeTypeEnums(String prefix, int length, String desc) {
            this(prefix, length, false, desc);
        }

        CodeTypeEnums(String prefix, int length, boolean inclDate, String desc) {
            this.prefix = prefix;
            this.length = length;
            this.inclDate = inclDate;
            this.desc = desc;
        }

        public String getPrefix() {
            return prefix;
        }

        public int getLength() {
            return length;
        }

        public boolean isInclDate() {
            return inclDate;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 生成编码
     *
     * @param type           编码类型
     * @param seqNumber 序列号
     * @return 生成的编码
     */
    public static String generateCode(CodeTypeEnums type, Long seqNumber) {
        StringBuilder code = new StringBuilder();
        code.append(type.getPrefix());

        if (type.isInclDate()) {
            // yyMMdd
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
            String date = LocalDate.now().format(formatter);
            code.append(date);
        }

        String sequenceStr = String.format("%0" + type.getLength() + "d", seqNumber);
        code.append(sequenceStr);

        return code.toString();
    }
}
