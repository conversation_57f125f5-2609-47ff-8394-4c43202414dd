package com.mongoso.mgs.module.finance.controller.admin.accountorder.vo;

import com.mongoso.mgs.module.finance.controller.admin.accountorderdetail.vo.AccountOrderDetailRespVO;
import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 对账单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountOrderRespVO extends AccountOrderBaseVO {

    private List<AccountOrderDetailRespVO> detailList;
    // 附件
    private List<FileLogRespVO> accountFileList;
    private String customerName;

    /** 审核状态 */
    private String dataStatusDictName;

    private String directorName;
    /** 责任部门 */
    private String directorOrgName;
    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 审批任务id */
    private Long approveTaskId;
}
