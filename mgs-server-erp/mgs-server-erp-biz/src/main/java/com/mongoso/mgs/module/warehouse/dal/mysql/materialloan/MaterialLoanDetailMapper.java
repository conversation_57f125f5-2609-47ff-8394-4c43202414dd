package com.mongoso.mgs.module.warehouse.dal.mysql.materialloan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo.detail.MaterialLoanDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo.detail.MaterialLoanDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialloan.vo.detail.MaterialLoanDetailRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.materialloan.MaterialLoanDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 外借单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialLoanDetailMapper extends BaseMapperX<MaterialLoanDetailDO> {

    default PageResult<MaterialLoanDetailDO> selectPage(MaterialLoanDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<MaterialLoanDetailDO>lambdaQueryX()
                .eqIfPresent(MaterialLoanDetailDO::getLoanId, reqVO.getLoanId())
                .likeIfPresent(MaterialLoanDetailDO::getLoanCode, reqVO.getLoanCode())
                .eqIfPresent(MaterialLoanDetailDO::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(MaterialLoanDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(MaterialLoanDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                        .orderByDesc(MaterialLoanDetailDO::getCreatedDt));
    }

    default List<MaterialLoanDetailDO> selectList(MaterialLoanDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<MaterialLoanDetailDO>lambdaQueryX()
                .eqIfPresent(MaterialLoanDetailDO::getLoanId, reqVO.getLoanId())
                .likeIfPresent(MaterialLoanDetailDO::getLoanCode, reqVO.getLoanCode())
                .eqIfPresent(MaterialLoanDetailDO::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(MaterialLoanDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(MaterialLoanDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                        .orderByDesc(MaterialLoanDetailDO::getCreatedDt));
    }

    default BigDecimal queryPendingReturnQty(Long loanDetailId) {
        MaterialLoanDetailDO materialLoanDetail =  selectOne(LambdaQueryWrapperX.<MaterialLoanDetailDO>lambdaQueryX()
                .eq(MaterialLoanDetailDO::getLoanDetailId, loanDetailId));
        if(materialLoanDetail == null){
            return BigDecimal.ZERO;
        }
        return materialLoanDetail.getPendingReturnQty();
    }

    default int deleteByLoanId(Long loanId){
        return delete(LambdaQueryWrapperX.<MaterialLoanDetailDO>lambdaQueryX()
                .eq(MaterialLoanDetailDO:: getLoanId, loanId)
        );
    }

    default Long selectUnReturnCount(Long loanId){
        return selectCount(LambdaQueryWrapperX.<MaterialLoanDetailDO>lambdaQueryX()
                .eq(MaterialLoanDetailDO:: getLoanId, loanId)
                .eq(MaterialLoanDetailDO:: getIsMaterialReturned, 0)
        );
    }

    IPage<MaterialLoanDetailRespVO> queryMaterialLoanDetailPage(Page<MaterialLoanDetailPageReqVO> page,
                                                                @Param("reqVO") MaterialLoanDetailPageReqVO reqVO);

    List<MaterialLoanDetailRespVO> queryMaterialLoanDetailList(@Param("reqVO") MaterialLoanDetailQueryReqVO reqVO);

    List<MaterialLoanDetailRespVO> queryMaterialLoanDetailQuoteList(@Param("reqVO") MaterialLoanDetailQueryReqVO reqVO);

    List<DocumentRespBO> loanQtyList(@Param("loanId") Long loanId);

    List<DocumentRespBO> pendingReturnQtyList(@Param("loanId") Long loanId);

    List<MaterialWarehouseBO> loanMaterialWarehouseList(@Param("loanIdList") List<Long> loanIdList);

    default List<MaterialLoanDetailDO> forewarnJobByIdList(List<Long> idList){
        return selectList(LambdaQueryWrapperX.<MaterialLoanDetailDO>lambdaQueryX()
                .in(MaterialLoanDetailDO::getLoanId, idList)
        );
    }
}