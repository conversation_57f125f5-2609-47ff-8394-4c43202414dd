package com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 销售换货明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SaleExchangeDetailPageReqVO extends PageParam {

    /** 销售换货单ID */
    private Long saleExchangeId;

    /** 销售换货单号 */
    private String saleExchangeCode;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 销售订单类型id */
    private String salesOrderTypeDictId;

    /** 关联客户ID */
    private Long relatedCustomerId;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 换货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startExchangeDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endExchangeDate;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 单据状态 */
    private Integer dataStatus;

    /** 责任人名称 */
    private Long directorId;

    /** 责任部门名称 */
    private String directorOrgId;

    /** 行号 */
    private Integer rowNo;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;
    private String materialName;

    /** 物料编码 */
    private String materialCode;

    /** 物料类别 */
    private String materialCategoryDictId;

    /** 规格型号 */
    private String specModel;

    /** 换货数量 */
    private BigDecimal exchangeQty;

    /** 已入库数量 */
    private BigDecimal inboundedQty;

    /** 已出库数量 */
    private BigDecimal outboundedQty;

    /** 可入库数量 */
    private BigDecimal inboundableQty;

    /** 物料是否全部已入库 */
    private Short isMaterialFullInbounded;

    /** 可换货数量 */
    private BigDecimal canExchangeQty;

    /** 物料是否全部已出库 */
    private Short isExchangeMaterialFullOutbounded;

    /** 可出库数量 */
    private BigDecimal outboundableQty;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
