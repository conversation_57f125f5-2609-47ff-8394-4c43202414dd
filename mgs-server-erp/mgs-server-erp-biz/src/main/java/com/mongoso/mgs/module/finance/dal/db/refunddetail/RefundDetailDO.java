package com.mongoso.mgs.module.finance.dal.db.refunddetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 退款明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_refund_detail", autoResultMap = true)
//@KeySequence("u_refund_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundDetailDO extends OperateDO {

    /** 退款详情主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long refundDetailId;

    /** 退款主键ID */
    private Long refundId;

    /** 退款单号 */
    private String refundCode;

    /** 应收账款详情主键ID */
    private Long paymentDetailId;

    /** 单据类型 */
    private Short formType;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 来源单行号 */
    private Long sourceLineNumber;

    /** 来源单据数量 */
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 主单位ID */
    private String mainUnitDictId;

    /** 票据类型ID */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 主单位名称 */
    private String mainUnitDictName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 剩余应退数量 */
    private BigDecimal remainingRefundQty;

    /** 本次退款数量 */
    private BigDecimal currentRefundQty;

    /** 剩余退款金额(含税) */
    private BigDecimal refundableAmt;

    /** 本次退款金额(含税) */
    private BigDecimal inclCurrentRefundAmt;


}
