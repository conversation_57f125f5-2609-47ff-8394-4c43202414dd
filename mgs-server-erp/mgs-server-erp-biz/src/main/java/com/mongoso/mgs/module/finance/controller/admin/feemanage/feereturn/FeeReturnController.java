package com.mongoso.mgs.module.finance.controller.admin.feemanage.feereturn;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereturn.vo.*;
import com.mongoso.mgs.module.finance.service.feemanage.feereturn.FeeReturnService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 费用返还 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/finance")
@Validated
public class FeeReturnController {

    @Resource
    private FeeReturnService feeReturnService;

    @OperateLog("费用返还添加或编辑")
    @PostMapping("/feeReturnAdit")
    @PreAuthorize("@ss.hasPermission('feeReturn:adit')")
    public ResultX<Long> feeReturnAdit(@Valid @RequestBody FeeReturnAditReqVO reqVO) {
        return success(reqVO.getFeeReturnId() == null
                            ? feeReturnService.feeReturnAdd(reqVO)
                            : feeReturnService.feeReturnEdit(reqVO));
    }

    @OperateLog("费用返还删除")
    @PostMapping("/feeReturnDel")
    @PreAuthorize("@ss.hasPermission('feeReturn:delete')")
    public ResultX<Boolean> feeReturnDel(@Valid @RequestBody FeeReturnPrimaryReqVO reqVO) {
        feeReturnService.feeReturnDel(reqVO.getFeeReturnId());
        return success(true);
    }

    @OperateLog("费用返还详情")
    @PostMapping("/feeReturnDetail")
    @PreAuthorize("@ss.hasPermission('feeReturn:query')")
    public ResultX<FeeReturnRespVO> feeReturnDetail(@Valid @RequestBody FeeReturnPrimaryReqVO reqVO) {
        return success(feeReturnService.feeReturnDetail(reqVO.getFeeReturnId()));
    }

    @OperateLog("费用返还列表")
    @PostMapping("/feeReturnList")
    @PreAuthorize("@ss.hasPermission('feeReturn:query')")
    @DataPermission
    public ResultX<List<FeeReturnRespVO>> feeReturnList(@Valid @RequestBody FeeReturnQueryReqVO reqVO) {
        return success(feeReturnService.feeReturnList(reqVO));
    }

    @OperateLog("费用返还分页")
    @PostMapping("/feeReturnPage")
    @PreAuthorize("@ss.hasPermission('feeReturn:query')")
    @DataPermission
    public ResultX<PageResult<FeeReturnRespVO>> feeReturnPage(@Valid @RequestBody FeeReturnPageReqVO reqVO) {
        return success(feeReturnService.feeReturnPage(reqVO));
    }

    @OperateLog("费用返还批量删除")
    @PostMapping("/feeReturnDelBatch")
    @PreAuthorize("@ss.hasPermission('feeReturn:del')")
    public ResultX<BatchResult> feeReturnDelBatch(@Valid @RequestBody IdReq reqVO) {
        return feeReturnService.feeReturnDelBatch(reqVO);
    }

    @OperateLog("费用返还审核")
    @PostMapping("/feeReturnApprove")
    @PreAuthorize("@ss.hasPermission('feeReturn:adit')")
    public ResultX<BatchResult> feeReturnApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(feeReturnService.feeReturnApprove(reqVO));
    }

    @OperateLog("费用返还回调接口")
    @PostMapping("/feeReturnFlowCallback")
    public ResultX<Object> feeReturnFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(feeReturnService.feeReturnFlowCallback(reqVO));
    }

}
