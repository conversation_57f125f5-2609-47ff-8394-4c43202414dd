package com.mongoso.mgs.module.purchase.controller.admin.deduction;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.*;
import com.mongoso.mgs.module.purchase.service.deduction.PurchaseDeductionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 采购扣费单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchase")
@Validated
public class PurchaseDeductionController {

    @Resource
    private PurchaseDeductionService deductionService;

    @OperateLog("采购扣费单添加或编辑")
    @PostMapping("/erpPurchaseDeductionAdit")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:adit')")
    public ResultX<Long> purchaseDeductionAdit(@Valid @RequestBody PurchaseDeductionAditReqVO reqVO) {
        return success(reqVO.getPurchaseDeductionId() == null
                ? deductionService.purchaseDeductionAdd(reqVO)
                : deductionService.purchaseDeductionEdit(reqVO));
    }

    @OperateLog("采购扣费单删除")
    @PostMapping("/erpPurchaseDeductionDel")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:delete')")
    public ResultX<Boolean> purchaseDeductionDel(@Valid @RequestBody PurchaseDeductionPrimaryReqVO reqVO) {
        deductionService.purchaseDeductionDel(reqVO.getPurchaseDeductionId());
        return success(true);
    }

    @OperateLog("采购扣费单(批量)")
    @PostMapping("/erpPurchaseDeductionDelBatch")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:delete')")
    public ResultX<BatchResult> erpPurchaseDeductionDelBatch(@Valid @RequestBody IdReq reqVO) {
        return deductionService.purchaseDeductionDelBatch(reqVO);
    }

    @OperateLog("采购扣费单详情")
    @PostMapping("/erpPurchaseDeductionDetail")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:query')")
    public ResultX<PurchaseDeductionRespVO> purchaseDeductionDetail(@Valid @RequestBody PurchaseDeductionPrimaryReqVO reqVO) {
        return success(deductionService.purchaseDeductionDetail(reqVO.getPurchaseDeductionId()));
    }

    @OperateLog("采购扣费单列表")
    @PostMapping("/erpPurchaseDeductionList")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:query')")
    @DataPermission
    public ResultX<List<PurchaseDeductionRespVO>> purchaseDeductionList(@Valid @RequestBody PurchaseDeductionQueryReqVO reqVO) {
        return success(deductionService.purchaseDeductionList(reqVO));
    }

    @OperateLog("采购扣费单列表")
    @PostMapping("/erpPurchaseDeductionQuoteList")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:query')")
    public ResultX<List<PurchaseDeductionRespVO>> purchaseDeductionQuoteList(@Valid @RequestBody PurchaseDeductionQueryReqVO reqVO) {
        return success(deductionService.purchaseDeductionList(reqVO));
    }

    @OperateLog("采购扣费单分页")
    @PostMapping("/erpPurchaseDeductionPage")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:query')")
    @DataPermission
    public ResultX<PageResult<PurchaseDeductionRespVO>> purchaseDeductionPage(@Valid @RequestBody PurchaseDeductionPageReqVO reqVO) {
        return success(deductionService.purchaseDeductionPage(reqVO));
    }

    @OperateLog("采购扣费单分页")
    @PostMapping("/erpPurchaseDeductionQuotePage")
    @PreAuthorize("@ss.hasPermission('purchaseDeduction:query')")
    public ResultX<PageResult<PurchaseDeductionRespVO>> purchaseDeductionQuotePage(@Valid @RequestBody PurchaseDeductionPageReqVO reqVO) {
        return success(deductionService.purchaseDeductionPage(reqVO));
    }

    @OperateLog("采购扣费单审核")
    @PostMapping("/erpPurchaseDeductionApprove")
    @PreAuthorize("@ss.hasPermission('container:adit')")
    public ResultX<BatchResult> erpPurchaseDeductionApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = deductionService.purchaseDeductionApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("采购扣费单回调接口")
    @PostMapping("/erpPurchaseDeductionCallback")
    public ResultX<Object> erpPurchaseDeductionCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(deductionService.purchaseDeductionCallback(reqVO));
    }

}
