package com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategy.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 安全库存策略明细 RespVO
 *
 * <AUTHOR>
 */
@Data
public class InventorySecurityStrategyDetailRespVO2 implements Serializable {

    /** 主键ID */
    private Long id;

    /** 安全库存策略id */
    private Long inventorySecurityStrategyId;
    private String inventorySecurityStrategyCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 最小值 */
    private BigDecimal minValue;

    /** 最大值 */
    private BigDecimal maxValue;

    /** 行号 */
    private Integer rowNo;

    /** 备注 */
    private String remark;


    private String materialName;// 物料名称
    private String materialCategoryDictId;// 物料类别id
    private String materialCategoryDictName;// 物料类别
    private Integer materialSourceDictId;//物料来源
    private String materialSourceDictName;//物料来源
    private String mainUnitDictId;// 基本单位
    private String mainUnitDictName;// 基本单位
    private String specModel;// 规格型号
    private String specAttributeStr;// 规格属性

}
