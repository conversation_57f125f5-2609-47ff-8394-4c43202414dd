package com.mongoso.mgs.module.produce.service.reportedwork;

import java.util.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.produce.controller.admin.reportedwork.bo.ReportedEditReqBO;
import jakarta.validation.*;
import com.mongoso.mgs.module.produce.controller.admin.reportedwork.vo.*;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 报工记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ReportedWorkService {

    /**
     * 创建报工记录
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long reportedWorkAdd(@Valid ReportedWorkAditReqVO reqVO);

    /**
     * 更新报工记录
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long reportedWorkEdit(@Valid ReportedWorkAditReqVO reqVO);

    /**
     * 删除报工记录
     *
     * @param reportedWorkId 编号
     */
    void reportedWorkDel(Long reportedWorkId);

    /**
     * 获得报工记录信息
     *
     * @param reportedWorkId 编号
     * @return 报工记录信息
     */
    ReportedWorkRespVO reportedWorkDetail(Long reportedWorkId);

    /**
     * 获得报工记录列表
     *
     * @param reqVO 查询条件
     * @return 报工记录列表
     */
    List<ReportedWorkRespVO> reportedWorkList(@Valid ReportedWorkQueryReqVO reqVO);

    /**
     * 获得报工记录分页
     *
     * @param reqVO 查询条件
     * @return 报工记录分页
     */
    PageResult<ReportedWorkRespVO> reportedWorkPage(@Valid ReportedWorkPageReqVO reqVO);

    ReportedWorkRespVO scanFlowCard(ReportedWorkQueryReqVO reqVO);

    /**
     *报工进度查询
     *
     * @param reqVO
     * @return
     */
    ReportedWorkRespVO reportedWorkProgressQuery(ReportedWorkProgressQuery reqVO);

    BatchResult reportedWorkApprove(FlowApprove reqVO);

    Object reportedWorkFlowCallback(FlowCallback reqVO);

    /**
     * 生产 审核后置处理
     *
     * @param reqVO
     */
    void prodApprovePostTask(ReportedWorkAditReqVO reqVO);

    /**
     * 生产 反审核后置处理
     *
     * @param reqVO
     */
    void prodNotApprovePostTask(ReportedWorkAditReqVO reqVO);

    /**
     * 委外 反审核后置处理
     *
     * @param reqVO
     */
    void outNotApprovePostTask(ReportedWorkAditReqVO reqVO);

    /**
     * 委外 审核后置处理
     *
     * @param reqVO
     */
    void outApprovePostTask(ReportedWorkAditReqVO reqVO);

    /**
     * 返工 审核后置处理
     *
     * @param reqVO
     */
    void reworkAndCheckApprovePostTask(ReportedWorkAditReqVO reqVO);

    /**
     * 返工 反审核后置处理
     *
     * @param reqVO
     */
    void reworkAndCheckNotApprovePostTask(ReportedWorkAditReqVO reqVO);

    /**
     * 更新报工数据
     *
     * @param reportedEditReqBO
     */
    void upateReportedWork(ReportedEditReqBO reportedEditReqBO);
}
