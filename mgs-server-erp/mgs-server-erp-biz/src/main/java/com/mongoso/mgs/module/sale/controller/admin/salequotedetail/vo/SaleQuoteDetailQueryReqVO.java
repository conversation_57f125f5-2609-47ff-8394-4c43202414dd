package com.mongoso.mgs.module.sale.controller.admin.salequotedetail.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 销售报价明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SaleQuoteDetailQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long saleQuoteDetailId;

    /** 排除物料IDList */
    private List<Long> exclMaterialIdList;

    /** 销售报价单id */
    private Long saleQuoteId;

    /** 物料id */
    private Long materialId;

    /** 基本单位 */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
