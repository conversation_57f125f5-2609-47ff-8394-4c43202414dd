package com.mongoso.mgs.module.produce.service.mold;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.mold.vo.molduse.*;

/**
 * 模具领用单 Service 接口
 *
 * <AUTHOR>
 */
public interface MoldUseService {

    /**
     * 创建模具领用单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long moldUseAdd(@Valid MoldUseAditReqVO reqVO);

    /**
     * 更新模具领用单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long moldUseEdit(@Valid MoldUseAditReqVO reqVO);

    /**
     * 删除模具领用单
     *
     * @param moldUseId 编号
     */
    void moldUseDel(Long moldUseId);

    /**
     * 获得模具领用单信息
     *
     * @param moldUseId 编号
     * @return 模具领用单信息
     */
    MoldUseRespVO moldUseDetail(Long moldUseId);

    /**
     * 获得模具领用单列表
     *
     * @param reqVO 查询条件
     * @return 模具领用单列表
     */
    List<MoldUseRespVO> moldUseList(@Valid MoldUseQueryReqVO reqVO);

    /**
     * 获得模具领用单分页
     *
     * @param reqVO 查询条件
     * @return 模具领用单分页
     */
    PageResult<MoldUseRespVO> moldUsePage(@Valid MoldUsePageReqVO reqVO);

    ResultX<BatchResult> moldUseDelBatch(IdReq reqVO);

    BatchResult moldUseApprove(FlowApprove reqVO);

    Object moldUseFlowCallback(FlowCallback reqVO);

    List<MoldUseDetailRespVO> moldUseItemList(Long moldUseId);

}
