package com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.CommonParam;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreobj.vo.SalaryAggreObjRespVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryitem.vo.SalaryItemRespVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 


/**
 * 工资单归集策略 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SalaryAggrePloyQueryReqVO extends CommonParam {

    /** 工资单归集策略ID */
    private Long payrollAggreStrategyId;

    private Long employeeArchivesId;

    /** 策略编码 */
    private String strategyCode;

    /** 策略名称 */
    private String strategyName;

    /** 适用人员ids */
    private List<Long> applyPersonIds;

    /** 承担对象 */
    private List<SalaryAggreObjRespVO>  undertakeOrg;


    /** 计提周期 */
    private Integer provisionCycle;

    /** 固定支出成本科目id {成本科目：成本用途} */
    private Long fixedExpendCostSubjectId;

    /** 固定支出成本用途 */
    private Integer fixedExpendCostUsage;

    /** 递延费用成本科目id */
    private Long deferFeeCostSubjectId;

    /** 递延费用成本用途 */
    private Integer deferFeeCostUsage;

    /** 薪酬项目归集 */
    private List<SalaryItemRespVO>  salaryItemCollection;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
