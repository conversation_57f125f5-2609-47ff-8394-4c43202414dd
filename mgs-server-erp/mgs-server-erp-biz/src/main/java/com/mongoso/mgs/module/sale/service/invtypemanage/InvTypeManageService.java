package com.mongoso.mgs.module.sale.service.invtypemanage;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.*;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 票据类型管理 Service 接口
 *
 * <AUTHOR>
 */
public interface InvTypeManageService {

    /**
     * 创建票据类型管理
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long invTypeManageAdd(@Valid InvTypeManageAditReqVO reqVO);

    /**
     * 更新票据类型管理
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long invTypeManageEdit(@Valid InvTypeManageAditReqVO reqVO);

    /**
     * 删除票据类型管理
     *
     * @param invoiceTypeId 编号
     */
    void invTypeManageDel(Long invoiceTypeId);

    /**
     * 获得票据类型管理信息
     *
     * @param invoiceTypeId 编号
     * @return 票据类型管理信息
     */
    InvTypeManageDO invTypeManageDetail(Long invoiceTypeId);

    /**
     * 获得票据类型管理列表
     *
     * @param reqVO 查询条件
     * @return 票据类型管理列表
     */
    List<InvTypeManageRespVO> invTypeManageList(@Valid InvTypeManageQueryReqVO reqVO);

    /**
     * 获得票据类型管理分页
     *
     * @param reqVO 查询条件
     * @return 票据类型管理分页
     */
    PageResult<InvTypeManageRespVO> invTypeManagePage(@Valid InvTypeManagePageReqVO reqVO);

}
