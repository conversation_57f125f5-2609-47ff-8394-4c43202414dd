package com.mongoso.mgs.module.sale.dal.db.cutomerrel;

import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 客户中间 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_cutomer_rel", autoResultMap = true)
//@KeySequence("u_cutomer_rel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CutomerRelDO extends OperateDO {

    /** 主键 */
        @TableId(type = IdType.ASSIGN_ID)
    private Long customerRelId;

    /** 创建人ID */
    private Long createdId;

    /** 客户id */
    private Long customerId;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /**
     * 关联单据Code
     */
    private String relatedOrderCode;

    /** 行号 */
    private Integer rowNo;


}
