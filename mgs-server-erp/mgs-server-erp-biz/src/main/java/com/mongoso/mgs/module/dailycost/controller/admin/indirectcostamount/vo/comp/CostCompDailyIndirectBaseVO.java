package com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.comp;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 企业日间接成本 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CostCompDailyIndirectBaseVO implements Serializable {

    /** 主键ID */
    private Long compDailyIndirectId;

    /** 承担对象ID */
    private String undertakeOrgId;
    private String undertakeOrgName;

    /** 费用日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate costDate;

}
