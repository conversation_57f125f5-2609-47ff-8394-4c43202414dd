package com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 工序委外采购订单明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseProcessOutDetailPageReqVO extends PageParam {

    /** 工序委外采购订单号 */
    private String purchaseProcessOutCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 币种 */
    private String currencyDictId;

    /** 付款条件 */
    private String paymentTermsDictId;

    /** 工序委外需求单号 */
    private String processOutDemandCode;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 工序ID */
    private Long processId;

    /** 工序名称 */
    private String processName;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;


    /** 工序委外采购订单ID */
    private Long purchaseProcessOutId;

    /** 工序委外需求ID */
    private Long processOutDemandId;

    /** 行号 */
    private Integer rowNo;

    /** 物料ID */
    private Long materialId;

    /** 工序编码 */
    private String processCode;

    /** 采购数量 */
    private BigDecimal purchaseQty;

    /** 是否填报税价 */
    private Integer includingTax;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 单价(含税) */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 已收货数量 */
    private BigDecimal receiptedQty;

    /** 已操作数量 */
    private BigDecimal operedQty;

    /** 物料是否全部收货 */
    private Integer isMaterialFullReceipted;

    /** 物料是否全部已操作 */
    private Integer isMaterialFullOpered;

    /** 结算方式 */
    private String settlementMethodDictId;

}
