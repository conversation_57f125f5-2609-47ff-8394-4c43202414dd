package com.mongoso.mgs.module.produce.dal.mysql.erpprodorder;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.bo.ErpProdOrderRespBO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.bo.ErpProdOrderDetailBO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 生产订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpProdOrderMapper extends BaseMapperX<ErpProdOrderDO> {

    default PageResult<ErpProdOrderDO> selectPageOld(ErpProdOrderPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ErpProdOrderDO>lambdaQueryX()
                .likeIfPresent(ErpProdOrderDO::getProdOrderCode, reqVO.getProdOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpProdOrderDO::getProdOrderTypeDictId, reqVO.getProdOrderTypeDictId())
                .eqIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizType())
                .inIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizTypeList())
                .eqIfPresent(ErpProdOrderDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(ErpProdOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderDO::getProcessConfig, reqVO.getProcessConfig())
                .eqIfPresent(ErpProdOrderDO::getProdPlanTotalQty, reqVO.getProdPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getWorkPlanTotalQty, reqVO.getWorkPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getProdActTotalQty, reqVO.getProdActTotalQty())
                .eqIfPresent(ErpProdOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatus())
                .inIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(ErpProdOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ErpProdOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(ErpProdOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .isNull(ErpProdOrderDO::getProdOrderChangeId)
                .orderByDesc(ErpProdOrderDO::getCreatedDt));
    }



    default PageResult<ErpProdOrderDO> selectPage(ErpProdOrderPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ErpProdOrderDO>lambdaQueryX()
                .likeIfPresent(ErpProdOrderDO::getProdOrderCode, reqVO.getProdOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpProdOrderDO::getProdOrderTypeDictId, reqVO.getProdOrderTypeDictId())
                .eqIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizType())
                .inIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizTypeList())
                .eqIfPresent(ErpProdOrderDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(ErpProdOrderDO::getCustomerId, reqVO.getCustomerId())
                .likeIfPresent(ErpProdOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderDO::getProcessConfig, reqVO.getProcessConfig())
                .eqIfPresent(ErpProdOrderDO::getProdPlanTotalQty, reqVO.getProdPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getWorkPlanTotalQty, reqVO.getWorkPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getProdActTotalQty, reqVO.getProdActTotalQty())
                .eqIfPresent(ErpProdOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatus())
                .inIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(ErpProdOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderDO::getIsFullPurchased, reqVO.getIsFullPurchased())
                .eqIfPresent(ErpProdOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ErpProdOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(ErpProdOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .isNull(ErpProdOrderDO::getProdOrderChangeId)
                        .orderByDesc(ErpProdOrderDO::getCreatedDt));
    }

    default List<ErpProdOrderDO> selectListOld(ErpProdOrderQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ErpProdOrderDO>lambdaQueryX()
                .likeIfPresent(ErpProdOrderDO::getProdOrderCode, reqVO.getProdOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpProdOrderDO::getProdOrderTypeDictId, reqVO.getProdOrderTypeDictId())
                .eqIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizType())
                .inIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizTypeList())
                .eqIfPresent(ErpProdOrderDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(ErpProdOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderDO::getProcessConfig, reqVO.getProcessConfig())
                .eqIfPresent(ErpProdOrderDO::getProdPlanTotalQty, reqVO.getProdPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getWorkPlanTotalQty, reqVO.getWorkPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getProdActTotalQty, reqVO.getProdActTotalQty())
                .eqIfPresent(ErpProdOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatus())
                .inIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(ErpProdOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ErpProdOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(ErpProdOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .isNull(ErpProdOrderDO::getProdOrderChangeId)
                    .orderByDesc(ErpProdOrderDO::getCreatedDt));
    }

    default List<ErpProdOrderDO> selectList(ErpProdOrderQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ErpProdOrderDO>lambdaQueryX()
                .likeIfPresent(ErpProdOrderDO::getProdOrderCode, reqVO.getProdOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpProdOrderDO::getProdOrderTypeDictId, reqVO.getProdOrderTypeDictId())
                .eqIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizType())
                .inIfPresent(ErpProdOrderDO::getProdOrderBizType, reqVO.getProdOrderBizTypeList())
                .eqIfPresent(ErpProdOrderDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(ErpProdOrderDO::getCustomerId, reqVO.getCustomerId())
                .likeIfPresent(ErpProdOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(ErpProdOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderDO::getProcessConfig, reqVO.getProcessConfig())
                .eqIfPresent(ErpProdOrderDO::getProdPlanTotalQty, reqVO.getProdPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getWorkPlanTotalQty, reqVO.getWorkPlanTotalQty())
                .eqIfPresent(ErpProdOrderDO::getProdActTotalQty, reqVO.getProdActTotalQty())
                .eqIfPresent(ErpProdOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatus())
                .inIfPresent(ErpProdOrderDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(ErpProdOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(ErpProdOrderDO::getIsFullPurchased, reqVO.getIsFullPurchased())
                .betweenIfPresent(ErpProdOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ErpProdOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .inIfPresent(ErpProdOrderDO::getRelatedOrderId, reqVO.getRelatedOrderIdList())
                .betweenIfPresent(ErpProdOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .isNull(ErpProdOrderDO::getProdOrderChangeId)
                        .orderByDesc(ErpProdOrderDO::getCreatedDt));
    }

    default int batchDelete(ErpProdOrderQueryReqVO reqVO){
        if (reqVO == null){
            return 0;
        }
        return delete(LambdaQueryWrapperX.<ErpProdOrderDO>lambdaQueryX()
                .eqIfPresent(ErpProdOrderDO::getProdOrderChangeId,reqVO.getProdOrderChangeId())
        );
    }

    List<ErpProdOrderRespBO> findErpProdOrderQty(@Param("saleOrderIds") List<Long> saleOrderIds);

    default List<ErpProdOrderDO> forewarnJob(Integer dataStatus, List<Integer> statusList){
        return selectList(LambdaQueryWrapperX.<ErpProdOrderDO>lambdaQueryX()
                .eq(ErpProdOrderDO::getDataStatus, dataStatus)
                .in(ErpProdOrderDO::getFormStatus, statusList)
        );
    }

    List<ErpProdOrderDetailBO> selectListByRelatedOrderIdAndMaterialIdList(@Param("relatedOrderId") Long relatedOrderId, @Param("materialIdList") List<Long> materialIdList);

    IPage<ErpProdOrderResp> queryProdDetailPage(Page<ErpProdOrderPageReqVO> page,  @Param("reqVO") ErpProdOrderPageReqVO reqVO);
}