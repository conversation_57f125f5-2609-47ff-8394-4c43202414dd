package com.mongoso.mgs.common.feign;

import com.mongoso.mgs.module.company.api.company.CompanyApi;
import com.mongoso.mgs.module.order.api.ProdOrderApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;


/**
 * 配置注入feign调用api类
 * SystemFeignApiConfig 名称不能重复
 * 规则：项目名+FeignApiConfig
 *
 * 如果需要调用其他服务的api，需要把对应的api类注入进来，参考下面的 PingFeign.clss
 *
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {
        CompanyApi.class,
        ProdOrderApi.class
        })
public class FeignApiBeanConfig {
}
