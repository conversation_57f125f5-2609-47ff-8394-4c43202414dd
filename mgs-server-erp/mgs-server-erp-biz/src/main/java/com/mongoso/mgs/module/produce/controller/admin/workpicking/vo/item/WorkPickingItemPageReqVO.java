package com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;


/**
 * 领料单明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkPickingItemPageReqVO extends PageParam {

    /** 工单领料业务类型 */
    private Integer workPickingBizType;
    /** 领料单号 */
    private String workPickingCode;

    /** 领料单类型 */
    private String workPickingTypeDictId;

    /** 关联单据号 */
    private String relatedOrderCode;


    private String mainMaterialName;
    private List<Long> mainMaterialIdList;
    private String mainMaterialCode;
    private String mainMaterialCategoryDictId;


    private String materialName;
    private List<Long> materialIdList;
    private String materialCode;
    private String warehouseOrgId;
    private String materialCategoryDictId;

    /** 是否出库完成 */
    private Integer isMaterialFullOutbounded;
}
