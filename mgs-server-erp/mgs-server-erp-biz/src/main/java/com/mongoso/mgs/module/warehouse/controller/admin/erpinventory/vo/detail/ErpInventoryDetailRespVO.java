package com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail;

import lombok.*;

import java.math.BigDecimal;


/**
 * 盘点单明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpInventoryDetailRespVO extends ErpInventoryDetailBaseVO {

    /** 物料名称*/
    private String materialName;

    /** 物料类别字典ID */
    private String materialCategoryDictId;

    /** 物料类别 */
    private String materialCategoryDictName;

    /** 基本单位字典名称 */
    private String mainUnitDictName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 可调整数量 */
    private BigDecimal adjustableQty;
}
