package com.mongoso.mgs.module.purchase.service.workbench;

import com.mongoso.mgs.common.charts.vo.ChartsBaseY;
import com.mongoso.mgs.common.charts.vo.ChartsLineRespVO;
import com.mongoso.mgs.common.charts.vo.ChartsRankRespVO;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.PurchaseWorkbenchMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseLineBO;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.bo.PurchaseRankBO;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.vo.PurchaseOrderDataRespVO;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.vo.PurchaseSupplierDataRespVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 采购工作台 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseWorkbenchServiceImpl implements PurchaseWorkbenchService {

    @Resource
    private PurchaseWorkbenchMapper purchaseWorkbenchMapper;

    @Override
    public PurchaseOrderDataRespVO purchaseOrderDataStat(){
        // 待完成需求单数量
        Integer pendingDemandCount = purchaseWorkbenchMapper.getPendingDemandCount();
        // 待完成订单数量
        Integer pendingOrderCount = purchaseWorkbenchMapper.getPendingOrderCount();
        // 待出库物料数量
        BigDecimal pendingInboundQty = purchaseWorkbenchMapper.getPendingInboundQty();
        // 工序委外待采购数量
        BigDecimal podPendingPurchaseQty = purchaseWorkbenchMapper.getPODPendingPurchaseQty();
        // 待退货物料数量
        BigDecimal returnPendingInboundedQty = purchaseWorkbenchMapper.getReturnPendingInboundedQty();

        PurchaseOrderDataRespVO respVO = new PurchaseOrderDataRespVO();
        respVO.setPendingDemandCount(pendingDemandCount);
        respVO.setPendingOrderCount(pendingOrderCount);
        respVO.setPendingInboundQty(pendingInboundQty);
        respVO.setPodPendingPurchaseQty(podPendingPurchaseQty);
        respVO.setReturnPendingInboundedQty(returnPendingInboundedQty);
        return respVO;
    }

    @Override
    public PurchaseSupplierDataRespVO purchaseSupplierDataStat(){
        // 供应商数量
        Integer supplierNumber = purchaseWorkbenchMapper.getSupplierNumber();
        // 活跃供应商数量
        Integer activeSupplierNumber = purchaseWorkbenchMapper.getActiveSupplierNumber();
        PurchaseSupplierDataRespVO respVO = new PurchaseSupplierDataRespVO();
        respVO.setSupplierNumber(supplierNumber);
        respVO.setActiveSupplierNumber(activeSupplierNumber);
        return respVO;
    }

    @Override
    public ChartsLineRespVO purchaseAmtLineStat(){
        // 查询采购折线图数据
        List<PurchaseLineBO> purchaseLineBOList = purchaseWorkbenchMapper.queryPurchaseAmtLineList();
        // 复合键转Map
        Map<String, PurchaseLineBO> purchaseLineMap = purchaseLineBOList.stream().collect(Collectors.toMap(
                record -> record.getDate(), item -> item));

        ChartsLineRespVO respVO = new ChartsLineRespVO();
        // 获取横轴
        List<String> lableList = getPurchaseAmtLableList();

        // 获取纵轴
        ChartsBaseY purchaseOrderY = new ChartsBaseY();
        purchaseOrderY.setLabel("采购");
        purchaseOrderY.setType(1);
        respVO.getyList().add(purchaseOrderY);

        ChartsBaseY returnOrderY = new ChartsBaseY();
        returnOrderY.setLabel("退货");
        returnOrderY.setType(1);
        respVO.getyList().add(returnOrderY);

        ChartsBaseY deductionOrderY = new ChartsBaseY();
        deductionOrderY.setLabel("扣费");
        deductionOrderY.setType(1);
        respVO.getyList().add(deductionOrderY);

        for(int i=0; i < lableList.size(); i++){
            String lable = lableList.get(i);
            PurchaseLineBO purchaseLineBO = purchaseLineMap.get(lable);
            if (purchaseLineBO != null){
                purchaseOrderY.getValueList().add(purchaseLineBO.getPurchaseInclTaxAmt());
                returnOrderY.getValueList().add(purchaseLineBO.getReturnInclTaxAmt());
                deductionOrderY.getValueList().add(purchaseLineBO.getDeductionInclTaxAmt());
            } else {
                purchaseOrderY.getValueList().add(BigDecimal.ZERO);
                returnOrderY.getValueList().add(BigDecimal.ZERO);
                deductionOrderY.getValueList().add(BigDecimal.ZERO);
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date = LocalDate.parse(lable, formatter);
            lableList.set(i, date.getDayOfMonth()+"日");
        }

        // 设置横轴
        respVO.getxList().addAll(lableList);

        return respVO;
    }

    public List<String> getPurchaseAmtLableList() {
        // 标签
        List<String> lableList = new ArrayList<>();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = LocalDate.now().minusDays(29); //因为包含了当天

        //获取标题
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        while(startDate.compareTo(endDate) <= 0){
            lableList.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }
        return lableList;
    }

    @Override
    public List<ChartsRankRespVO> purchaseQtyRankStat(){
        List<ChartsRankRespVO> respList = new ArrayList<>();
        List<PurchaseRankBO> rankList = purchaseWorkbenchMapper.queryPurchaseQtyRank();
        if(CollUtilX.isEmpty(rankList)){
            return respList;
        }

        for(PurchaseRankBO item : rankList){
            ChartsRankRespVO respVO = new ChartsRankRespVO();
            respVO.setName(item.getMaterialCode() + " " + item.getMaterialName() + "\n\n" + item.getSpecAttributeStr());
            respVO.setValue(item.getValue());
            respList.add(respVO);
        }

        return respList;
    }

    @Override
    public List<ChartsRankRespVO> purchaseAmtRankStat(){
        List<ChartsRankRespVO> respList = new ArrayList<>();
        List<PurchaseRankBO> rankList = purchaseWorkbenchMapper.queryPurchaseAmtRank();
        if(CollUtilX.isEmpty(rankList)){
            return respList;
        }

        for(PurchaseRankBO item : rankList){
            ChartsRankRespVO respVO = new ChartsRankRespVO();
            respVO.setName(item.getMaterialCode() + " " + item.getMaterialName() + "\n\n" + item.getSpecAttributeStr());
            respVO.setValue(item.getValue());
            respList.add(respVO);
        }

        return respList;
    }
}
