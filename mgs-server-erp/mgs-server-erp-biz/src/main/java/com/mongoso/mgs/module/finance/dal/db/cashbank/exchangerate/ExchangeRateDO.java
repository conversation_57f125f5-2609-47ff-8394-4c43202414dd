package com.mongoso.mgs.module.finance.dal.db.cashbank.exchangerate;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 汇率 DO
 *
 * <AUTHOR>
 */
@TableName("platform.t_exchange_rate")
@KeySequence("platform.t_exchange_rate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRateDO extends OperateDO {

    /** 主键ID */
    @TableId
    private Long id;

    /** 原币币种字典ID */
    private Long originalCurrencyDictId;

    /** 本币币种字典ID */
    private Long localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;


}
