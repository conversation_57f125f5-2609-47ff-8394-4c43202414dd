package com.mongoso.mgs.module.sale.dal.mysql.saleexchangedetail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbillpayment.vo.AcceptBillPaymentPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbillpayment.vo.AcceptBillPaymentRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailPageReqVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO;
import com.mongoso.mgs.module.sale.service.saleexchangedetail.bo.ExchangeableQtyUpdateBO;
import com.mongoso.mgs.module.sale.controller.admin.salereturndetail.vo.SaleReturnDetailQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.saleexchangedetail.SaleExchangeDetailDO;
import com.mongoso.mgs.module.sale.dal.db.salereturndetail.SaleReturnDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 销售换货明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SaleExchangeDetailMapper extends BaseMapperX<SaleExchangeDetailDO> {

//    default PageResult<SaleExchangeDetailDO> selectPage(SaleExchangeDetailPageReqVO reqVO) {
//        return selectPage(reqVO, LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
//                .eqIfPresent(SaleExchangeDetailDO::getSaleExchangeId, reqVO.getSaleExchangeId())
//                .likeIfPresent(SaleExchangeDetailDO::getSaleExchangeCode, reqVO.getSaleExchangeCode())
//                .eqIfPresent(SaleExchangeDetailDO::getRowNo, reqVO.getRowNo())
//                .eqIfPresent(SaleExchangeDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
//                .eqIfPresent(SaleExchangeDetailDO::getMaterialId, reqVO.getMaterialId())
//                .likeIfPresent(SaleExchangeDetailDO::getMaterialCode, reqVO.getMaterialCode())
//                .eqIfPresent(SaleExchangeDetailDO::getExchangeQty, reqVO.getExchangeQty())
//                .eqIfPresent(SaleExchangeDetailDO::getInboundedQty, reqVO.getInboundedQty())
//                .eqIfPresent(SaleExchangeDetailDO::getOutboundedQty, reqVO.getOutboundedQty())
//                .eqIfPresent(SaleExchangeDetailDO::getInboundableQty, reqVO.getInboundableQty())
//                .eqIfPresent(SaleExchangeDetailDO::getIsMaterialFullInbounded, reqVO.getIsMaterialFullInbounded())
//                .betweenIfPresent(SaleExchangeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
//                .orderByDesc(SaleExchangeDetailDO::getCreatedDt));
//    }

    IPage<SaleExchangeDetailRespVO> selectSaleExchangeDetailPage(Page<SaleExchangeDetailRespVO> page, @Param("reqVO") SaleExchangeDetailPageReqVO reqVO);

    List<SaleExchangeDetailRespVO> selectSaleExchangeDetailList(@Param("reqVO") SaleExchangeDetailQueryReqVO reqVO);


//    default List<SaleExchangeDetailDO> selectList(SaleExchangeDetailQueryReqVO reqVO) {
//        return selectList(LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
//                .eqIfPresent(SaleExchangeDetailDO::getSaleExchangeId, reqVO.getSaleExchangeId())
//                .likeIfPresent(SaleExchangeDetailDO::getSaleExchangeCode, reqVO.getSaleExchangeCode())
//                .eqIfPresent(SaleExchangeDetailDO::getRowNo, reqVO.getRowNo())
//                .eqIfPresent(SaleExchangeDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
//                .eqIfPresent(SaleExchangeDetailDO::getMaterialId, reqVO.getMaterialId())
//                .likeIfPresent(SaleExchangeDetailDO::getMaterialCode, reqVO.getMaterialCode())
//                .eqIfPresent(SaleExchangeDetailDO::getExchangeQty, reqVO.getExchangeQty())
//                .eqIfPresent(SaleExchangeDetailDO::getInboundedQty, reqVO.getInboundedQty())
//                .eqIfPresent(SaleExchangeDetailDO::getOutboundedQty, reqVO.getOutboundedQty())
//                .eqIfPresent(SaleExchangeDetailDO::getInboundableQty, reqVO.getInboundableQty())
//                .eqIfPresent(SaleExchangeDetailDO::getIsMaterialFullInbounded, reqVO.getIsMaterialFullInbounded())
//                .betweenIfPresent(SaleExchangeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
//                    .orderByDesc(SaleExchangeDetailDO::getCreatedDt));
//    }

    default int batchDelete(Long saleExchangeId){
        return delete(LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
                .eq(SaleExchangeDetailDO::getSaleExchangeId,saleExchangeId)
        );
    }

    default BigDecimal queryInboundableQty(Long saleExchangeDetailId) {
        SaleExchangeDetailDO saleExchangeDetailDO =  selectOne(LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
                .eq(SaleExchangeDetailDO:: getSaleExchangeDetailId, saleExchangeDetailId));
        if(saleExchangeDetailDO == null){
            return BigDecimal.ZERO;
        }
        return saleExchangeDetailDO.getInboundableQty();
    }

    default BigDecimal queryOutboundableQty(Long saleExchangeDetailId) {
        SaleExchangeDetailDO saleExchangeDetailDO =  selectOne(LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
                .eq(SaleExchangeDetailDO:: getSaleExchangeDetailId, saleExchangeDetailId));
        if(saleExchangeDetailDO == null){
            return BigDecimal.ZERO;
        }
        return saleExchangeDetailDO.getOutboundableQty();
    }

    default Long selectUnFullOpered(Long getSaleExchangeId){
        return selectCount(LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
                .eq(SaleExchangeDetailDO:: getSaleExchangeId, getSaleExchangeId)
                .eq(SaleExchangeDetailDO:: getIsMaterialFullInbounded, 0)
        );
    }

    default Long selectUnFullOutOpered(Long getSaleExchangeId){
        return selectCount(LambdaQueryWrapperX.<SaleExchangeDetailDO>lambdaQueryX()
                .eq(SaleExchangeDetailDO:: getSaleExchangeId, getSaleExchangeId)
                .eq(SaleExchangeDetailDO:: getIsExchangeMaterialFullOutbounded, 0)
        );
    }

    List<DocumentRespBO> sumQty(@Param("reqVO") SaleExchangeDetailQueryReqVO reqVO);

    List<DocumentRespBO> inboundableQtyList(@Param("saleExchangeId") Long saleExchangeId);

    List<DocumentRespBO> outboundableQtyList(@Param("saleExchangeId") Long saleExchangeId);

    /**
     * 出库明细引用列表
     * @param saleExchangeId 采购换货单ID
     * @return
     */
    List<SaleExchangeDetailRespVO> outboundDetailQuotedList(@Param("saleExchangeId") Long saleExchangeId);

    /**
     * 批量更新销售换货明细的可换货数量
     * @param updateParams 更新参数列表
     * @return 更新记录数
     */
    int batchUpdateExchangeableQtyByMaterialId(@Param("updateParams") List<ExchangeableQtyUpdateBO> updateParams);

}