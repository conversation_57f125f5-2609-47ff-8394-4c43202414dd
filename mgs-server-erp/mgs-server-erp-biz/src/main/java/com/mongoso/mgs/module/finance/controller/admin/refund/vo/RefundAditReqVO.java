package com.mongoso.mgs.module.finance.controller.admin.refund.vo;

import com.mongoso.mgs.module.finance.controller.admin.refunddetail.vo.RefundDetailAditReqVO;
import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogReqVO;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.PaymentRelationAditReqVO;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 退款单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RefundAditReqVO extends RefundBaseVO {
    /**
     * 明细记录
     */
    //@NotEmpty(message = "明细记录不能为空")
    @Valid
    private List<RefundDetailAditReqVO> detailList;
    /** 附件 */
    private List<FileLogReqVO> fileList;

    //是否自动生成：0不生成 1自动生成 2手动生成
    private int automatic = 2;

    /** 关联单据 */
    private List<PaymentRelationAditReqVO> relationOrderList;
}
