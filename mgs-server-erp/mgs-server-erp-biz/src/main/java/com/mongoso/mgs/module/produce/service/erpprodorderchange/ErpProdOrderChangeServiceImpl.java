package com.mongoso.mgs.module.produce.service.erpprodorderchange;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.employee.service.personal.bo.UserBaseRespBO;
import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogRespVO;
import com.mongoso.mgs.module.infra.service.file.FileService;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo.ErpProdOrderChangeDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo.ErpProdOrderChangeDetailRespVO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderchangedetail.ErpProdOrderChangeDetailDO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderchangedetail.ErpProdOrderChangeDetailMapper;
import com.mongoso.mgs.module.produce.handler.approve.ErpProdOrderChangeApproveHandler;
import com.mongoso.mgs.module.produce.handler.flowCallback.ErpProdOrderChangeFlowCallBackHandler;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchange.vo.*;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderchange.ErpProdOrderChangeDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderchange.ErpProdOrderChangeMapper;
import com.mongoso.mgs.framework.common.exception.BizException;
import static com.mongoso.mgs.common.enums.FileTableEnum.PRODORDERCHANGE_FILE;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;


/**
 * 生产订单变更单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpProdOrderChangeServiceImpl implements ErpProdOrderChangeService {

    @Resource
    private ErpProdOrderChangeMapper erpProdOrderChangeMapper;

    @Resource
    private ErpProdOrderChangeDetailMapper erpProdOrderChangeDetailMapper;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private FileService fileService;

    @Resource
    @Lazy
    private ErpProdOrderChangeApproveHandler erpProdOrderChangeApproveHandler;

    @Resource
    private ErpProdOrderChangeFlowCallBackHandler erpProdOrderChangeFlowCallBackHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long erpProdOrderChangeAdd(ErpProdOrderChangeAditReqVO reqVO) {

        // 生成单号
        String code = seqService.getGenerateCode(reqVO.getProdOrderChangeCode(), MenuEnum.PRODUCTION_ORDER_CHANGE.menuId);

        // 插入
        Long prodOrderChangeId = IDUtilX.getId();
        reqVO.setProdOrderChangeId(prodOrderChangeId);
        reqVO.setProdOrderChangeCode(code);
        ErpProdOrderChangeDO erpProdOrderChange = BeanUtilX.copy(reqVO, ErpProdOrderChangeDO::new);

        //明细处理
        List<ErpProdOrderChangeDetailDO> detailDOS = getDetailEditList(reqVO);

        //生产计划总数
        erpProdOrderChange.setProdOrderChangeCode(code);
        erpProdOrderChange.setProdPlanTotalQty(reqVO.getProdPlanTotalQty());

        erpProdOrderChangeMapper.insert(erpProdOrderChange);
        erpProdOrderChangeDetailMapper.insertBatch(detailDOS);

        //保存附件
        if (CollUtilX.isNotEmpty(reqVO.getProdOrderChangeFileList())){
            List<String> fileIdList = reqVO.getProdOrderChangeFileList().stream().map(FileLogRespVO::getFileId).collect(Collectors.toList());
            String objId = PRODORDERCHANGE_FILE.getFieldName() + erpProdOrderChange.getProdOrderChangeId();
            fileService.bind(fileIdList,objId,PRODORDERCHANGE_FILE.getTableName(),PRODORDERCHANGE_FILE.getFieldName());
        }

        // 返回
        return erpProdOrderChange.getProdOrderChangeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long erpProdOrderChangeEdit(ErpProdOrderChangeAditReqVO reqVO) {
        // 校验存在
//        this.erpProdOrderChangeValidateExists(reqVO.getProdOrderChangeId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.erpProdOrderChangeValidateExists(reqVO.getProdOrderChangeId()), reqVO);

        // 更新
        ErpProdOrderChangeDO erpProdOrderChange = BeanUtilX.copy(reqVO, ErpProdOrderChangeDO::new);

        //明细处理
        List<ErpProdOrderChangeDetailDO> detailDOS = getDetailEditList(reqVO);
        erpProdOrderChange.setProdPlanTotalQty(reqVO.getProdPlanTotalQty());

        //先删后增
        erpProdOrderChangeDetailMapper.batchDelete(reqVO.getProdOrderChangeId());
        erpProdOrderChangeMapper.updateById(erpProdOrderChange);
        erpProdOrderChangeDetailMapper.insertBatch(detailDOS);

        //保存附件
        if (CollUtilX.isNotEmpty(reqVO.getProdOrderChangeFileList())){
            List<String> fileIdList = reqVO.getProdOrderChangeFileList().stream().map(FileLogRespVO::getFileId).collect(Collectors.toList());
            String objId = PRODORDERCHANGE_FILE.getFieldName() + erpProdOrderChange.getProdOrderChangeId();
            fileService.bind(fileIdList,objId,PRODORDERCHANGE_FILE.getTableName(),PRODORDERCHANGE_FILE.getFieldName());
        }

        // 返回
        return erpProdOrderChange.getProdOrderChangeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void erpProdOrderChangeDel(Long prodOrderChangeId) {
        // 校验存在
        ErpProdOrderChangeDO currentDO = this.erpProdOrderChangeValidateExists(prodOrderChangeId);
        if(currentDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        
        // 删除
        erpProdOrderChangeMapper.deleteById(prodOrderChangeId);
        erpProdOrderChangeDetailMapper.batchDelete(prodOrderChangeId);
    }

    private ErpProdOrderChangeDO erpProdOrderChangeValidateExists(Long prodOrderChangeId) {
        ErpProdOrderChangeDO erpProdOrderChange = erpProdOrderChangeMapper.selectById(prodOrderChangeId);
        if (erpProdOrderChange == null) {
            // throw exception(ERP_PROD_ORDER_CHANGE_NOT_EXISTS);
            throw new BizException("5001", "生产订单变更单不存在");
        }
        return erpProdOrderChange;
    }

    @Override
    public ErpProdOrderChangeRespVO erpProdOrderChangeDetail(Long prodOrderChangeId) {
        ErpProdOrderChangeDO data = erpProdOrderChangeValidateExists(prodOrderChangeId);
        if (data == null){
            return null;
        }
        ErpProdOrderChangeRespVO respVO = BeanUtilX.copy(data, ErpProdOrderChangeRespVO::new);

        //VO属性填充
        fillVoProperties(Collections.singletonList(respVO));

        //生产物料详情处理
        ErpProdOrderChangeDetailQueryReqVO detailQuery = new ErpProdOrderChangeDetailQueryReqVO();
        detailQuery.setProdOrderChangeId(respVO.getProdOrderChangeId());
        List<ErpProdOrderChangeDetailDO> detailDOList = erpProdOrderChangeDetailMapper.selectListOld(detailQuery);
        List<ErpProdOrderChangeDetailRespVO> detailRespVOS = BeanUtilX.copy(detailDOList,ErpProdOrderChangeDetailRespVO::new);

        getDetailRespList(detailRespVOS);
        respVO.setDetailList(detailRespVOS);

        //获取附件
        respVO.setProdOrderChangeFileList(fileService.listByObjId(PRODORDERCHANGE_FILE.getFieldName() + respVO.getProdOrderChangeId()));

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(prodOrderChangeId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<ErpProdOrderChangeRespVO> erpProdOrderChangeList(ErpProdOrderChangeQueryReqVO reqVO) {
        List<ErpProdOrderChangeDO> data = erpProdOrderChangeMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ErpProdOrderChangeRespVO::new);
    }

    @Override
    public PageResult<ErpProdOrderChangeRespVO> erpProdOrderChangePage(ErpProdOrderChangePageReqVO reqVO) {
        PageResult<ErpProdOrderChangeDO> data = erpProdOrderChangeMapper.selectPage(reqVO);
        PageResult<ErpProdOrderChangeRespVO> pageResult = BeanUtilX.copy(data, ErpProdOrderChangeRespVO::new);

        //VO属性填充
        if (CollUtilX.isNotEmpty(pageResult.getList())){
            fillVoProperties(pageResult.getList());
        }

        return pageResult;
    }

    @Override
    public ResultX<BatchResult> erpProdOrderChangeDelBatch(IdReq reqVO) {
        // 删除
        String id = EntityUtilX.getPropertyName(ErpProdOrderChangeDO::getProdOrderChangeId);
        String code = EntityUtilX.getPropertyName(ErpProdOrderChangeDO::getProdOrderChangeCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), ErpProdOrderChangeDO.class, ErpProdOrderChangeDetailDO.class, id, code);

    }

    @Override
    public BatchResult erpProdOrderChangeApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ErpProdOrderChangeDO> list = erpProdOrderChangeMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ErpProdOrderChangeDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = erpProdOrderChangeApproveHandler.process(item,flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getProdOrderCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ErpProdOrderChangeDO item : list) {
                String reason = reasonMap.get(item.getProdOrderChangeCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProdOrderChangeId());
                    messageInfoBO.setObjCode(item.getProdOrderChangeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProdOrderChangeId());
                    messageInfoBO.setObjCode(item.getProdOrderChangeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object erpProdOrderChangeFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        ErpProdOrderChangeDO item = this.erpProdOrderChangeValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return erpProdOrderChangeFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    /**
     * 明细新增/编辑处理
     *
     * @param reqVO
     * @return
     */
    private List<ErpProdOrderChangeDetailDO> getDetailEditList(ErpProdOrderChangeAditReqVO reqVO) {
        List<ErpProdOrderChangeDetailDO> detailDOS = new ArrayList<>();
        List<ErpProdOrderChangeDetailRespVO> detailList = reqVO.getDetailList();
        BigDecimal prodPlanTotalQty = BigDecimal.ZERO;
        for (ErpProdOrderChangeDetailRespVO detail : detailList){
            detail.setProdOrderChangeId(reqVO.getProdOrderChangeId());
            detail.setProdOrderChangeCode(reqVO.getProdOrderChangeCode());
            ErpProdOrderChangeDetailDO detailDO = BeanUtilX.copy(detail, ErpProdOrderChangeDetailDO::new);
            prodPlanTotalQty = prodPlanTotalQty.add(detailDO.getProdPlanQty());

            detailDOS.add(detailDO);
        }

        //生产计划总数
        reqVO.setProdPlanTotalQty(prodPlanTotalQty);

        return detailDOS;
    }

    /**
     * VO属性填充
     *
     * @param itemList
     */
    private void fillVoProperties(List<ErpProdOrderChangeRespVO> itemList) {
        if (CollUtilX.isEmpty(itemList)){
            return;
        }

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();
        for (ErpProdOrderChangeRespVO item : itemList){
            empIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PROD_ORDER_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        //查询员工信息
        Map<Long, UserBaseRespBO> empNameMap = erpBaseService.getEmpByIdList(empIdList);

        for (ErpProdOrderChangeRespVO item : itemList){
            //查询负责人
            UserBaseRespBO dirEmployee = empNameMap.get(item.getDirectorId());
            if (dirEmployee!=null){
                item.setDirectorName(dirEmployee.getEmployeeName());
            }

            //查询责任部门
            item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));

            //生产订单类型
            if(StrUtilX.isNotEmpty(item.getProdOrderTypeDictId())){
                String prodOrderTypeDictId = CustomerDictEnum.PROD_ORDER_TYPE.getDictCode() + "-" + item.getProdOrderTypeDictId();
                item.setProdOrderTypeDictName(dictMap.get(prodOrderTypeDictId));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

        }
    }

    /**
     * 物料明细
     *
     * @param detailRespVOS
     * @return
     */
    private void getDetailRespList(List<ErpProdOrderChangeDetailRespVO> detailRespVOS) {
        //查询物料信息
        List<Long> materialIdList = new ArrayList<>();
        for (ErpProdOrderChangeDetailRespVO detailRespVO : detailRespVOS){
            materialIdList.add(detailRespVO.getMaterialId());
        }

        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        for (ErpProdOrderChangeDetailRespVO detail : detailRespVOS){
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detail.getMaterialId());
            if (erpMaterialDO!=null){
                detail.setMaterialName(erpMaterialDO.getMaterialName());
                detail.setSpecModel(erpMaterialDO.getSpecModel());
                detail.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detail.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detail.setMainUnitDictId(erpMaterialDO.getMainUnitDictId());
                detail.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detail.setMaterialSourceDictId(erpMaterialDO.getMaterialSourceDictId());
                detail.setMaterialSourceDictName(erpMaterialDO.getMaterialSourceDictName());
                detail.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
        }
    }

}
