package com.mongoso.mgs.module.dailycost.controller.admin.costsaleincomedetail.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 销售收入明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CostSaleIncomeDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long saleIncomeDetailId;

    /** 销售收入单号 */
    private String saleIncomeCode;

    /** 销售收入单ID */
    @NotNull(message = "销售收入单ID不能为空")
    private Long saleIncomeId;

    /** 订单类型 */
    @NotNull(message = "订单类型不能为空")
    private Short orderType;

    /** 物料编码 */
    private String materialCode;

    /** 物料ID */
    private Long materialId;

    /** 成本用途 */
    private short costUsage;

    /** 成本科目管理ID */
    private Long costSubjectId;

    /** 数量 */
    private BigDecimal qty;

    /** 单价 */
    private BigDecimal price;

    /** 总金额 */
    private BigDecimal totalAmt;

    /** 承担对象 */
    private String undertakeOrgId;

    /** 明细类型 */
    private Integer detailType;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单id */
    private Long sourceOrderId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

}
