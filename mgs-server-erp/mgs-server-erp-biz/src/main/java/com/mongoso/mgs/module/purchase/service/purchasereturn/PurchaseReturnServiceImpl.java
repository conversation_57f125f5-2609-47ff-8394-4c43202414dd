package com.mongoso.mgs.module.purchase.service.purchasereturn;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.*;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.PurchaseReturnAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.PurchaseReturnPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.PurchaseReturnQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.PurchaseReturnRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.purchasereturn.PurchaseReturnDO;
import com.mongoso.mgs.module.purchase.dal.db.purchasereturn.PurchaseReturnDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchasereturn.PurchaseReturnDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchasereturn.PurchaseReturnMapper;
import com.mongoso.mgs.module.purchase.handler.approve.PurchaseReturnApproveHandler;
import com.mongoso.mgs.module.purchase.handler.flowcallback.PurchaseReturnFlowCallBackHandler;
import com.mongoso.mgs.module.purchase.service.purchasereturn.detail.PurchaseReturnDetailService;
import com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo.ShipNoticeDetailRespVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.stockunlock.StockUnlockDO;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_DELETE_NOT_APPROVED;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_EDIT_NOT_APPROVED;


/**
 * 采购退货单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReturnServiceImpl implements PurchaseReturnService {

    @Resource
    private PurchaseReturnMapper returnMapper;
    @Resource
    private PurchaseReturnDetailMapper returnDetailMapper;
    @Resource
    private PurchaseReturnDetailService returnDetailService;

    @Resource
    private StockBookService stockBookService;

    @Resource
    private SeqService seqService;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private InvTypeManageMapper invTypeManageMapper;
    @Resource
    private ERPSupplierMapper erpSupplierMapper;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;

    @Lazy
    @Resource
    private PurchaseReturnApproveHandler purchaseReturnApproveHandler;

    @Resource
    private PurchaseReturnFlowCallBackHandler purchaseReturnFlowCallBackHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseReturnAdd(PurchaseReturnAditReqVO reqVO) {
        //编号
        String code = seqService.getGenerateCode(reqVO.getPurchaseReturnCode(), MenuEnum.PURCHASE_RETURN_ORDER.menuId);
        // 插入
        PurchaseReturnDO purchaseReturnDO = BeanUtilX.copy(reqVO, PurchaseReturnDO::new);
        purchaseReturnDO.setPurchaseReturnId(IDUtilX.getId());
        purchaseReturnDO.setPurchaseReturnCode(code);
        purchaseReturnDO.setApprovedBy(null);
        purchaseReturnDO.setApprovedDt(null);
        purchaseReturnDO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        List<PurchaseReturnDetailDO> returnDetailDOList = this.fillDetailList(reqVO, purchaseReturnDO);
        returnMapper.insert(purchaseReturnDO);
        //新增明细
        returnDetailMapper.insertBatch(returnDetailDOList);
        // 返回
        return purchaseReturnDO.getPurchaseReturnId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseReturnEdit(PurchaseReturnAditReqVO reqVO) {
        // 校验存在
        PurchaseReturnDO purchaseReturnDO = this.purchaseReturnValidateExists(reqVO.getPurchaseReturnId());
        //校验是否存在和版本号
        EntityUtilX.checkVersion(purchaseReturnDO, reqVO);

        // 更新
        PurchaseReturnDO purchaseReturn = BeanUtilX.copy(reqVO, PurchaseReturnDO::new);
        List<PurchaseReturnDetailDO> returnDetailDOList = this.fillDetailList(reqVO, purchaseReturn);
        returnMapper.updateById(purchaseReturn);

        //明细更新，先删后增
        returnDetailMapper.deleteByPurchaseId(reqVO.getPurchaseReturnId());
        returnDetailMapper.insertBatch(returnDetailDOList);

        // 返回
        return purchaseReturn.getPurchaseReturnId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseReturnDel(Long purchaseReturnId) {
        // 校验存在
        PurchaseReturnDO purchaseReturnDO = this.purchaseReturnValidateExists(purchaseReturnId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(purchaseReturnDO.getDataStatus())){
            throw exception(ORDER_DELETE_NOT_APPROVED);
        }
        // 删除
        returnMapper.deleteById(purchaseReturnId);
        returnDetailMapper.deleteByPurchaseId(purchaseReturnId);
    }

    private PurchaseReturnDO purchaseReturnValidateExists(Long purchaseReturnId) {
        PurchaseReturnDO purchaseReturnDO = returnMapper.selectById(purchaseReturnId);
        if (purchaseReturnDO == null) {
            // throw exception(RETURN_NOT_EXISTS);
            throw new BizException("5001", "采购退货单不存在");
        }
        return purchaseReturnDO;
    }

    @Override
    public PurchaseReturnRespVO purchaseReturnDetail(Long purchaseReturnId) {
        //校验存在
        PurchaseReturnDO purchaseReturnDO = this.purchaseReturnValidateExists(purchaseReturnId);
        //对象转换
        PurchaseReturnRespVO respVO = BeanUtilX.copy(purchaseReturnDO, PurchaseReturnRespVO::new);
        this.fillVoProperties(respVO);
        //查询明细列表
        PurchaseReturnDetailQueryReqVO returnDetailQueryReqVO = new PurchaseReturnDetailQueryReqVO();
        returnDetailQueryReqVO.setPurchaseReturnId(purchaseReturnId);
        List<PurchaseReturnDetailRespVO> detailList = returnDetailService.purchaseReturnDetailList(returnDetailQueryReqVO);
        respVO.setDetailList(detailList);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(purchaseReturnId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));



        return respVO ;
    }

    @Override
    public PurchaseReturnRespVO purchaseReturnQuotedDetail(Long purchaseReturnId){
        //校验存在
        PurchaseReturnDO purchaseReturnDO = this.purchaseReturnValidateExists(purchaseReturnId);
        //对象转换
        PurchaseReturnRespVO respVO = BeanUtilX.copy(purchaseReturnDO, PurchaseReturnRespVO::new);
        //查询明细列表
        PurchaseReturnDetailQueryReqVO returnDetailQueryReqVO = new PurchaseReturnDetailQueryReqVO();
        returnDetailQueryReqVO.setPurchaseReturnId(purchaseReturnId);
        returnDetailQueryReqVO.setIsMaterialFullOutbounded(0);
        List<PurchaseReturnDetailRespVO> detailList = returnDetailService.purchaseReturnDetailQuotedList(returnDetailQueryReqVO);
        respVO.setDetailList(detailList);

        // 可操作数量处理
        for(PurchaseReturnDetailRespVO detailRespVO : respVO.getDetailList()){

            //计算可用数量
            BigDecimal availableQty = stockBookService.getAvailableQty(respVO.getPurchaseReturnId(), detailRespVO.getMaterialId(),
                    detailRespVO.getWarehouseOrgId(), detailRespVO.getStockQty(), detailRespVO.getLockedQty());
            detailRespVO.setStockableQty(availableQty);

            if(detailRespVO.getStockableQty() == null) {
                detailRespVO.setOperableQty(BigDecimal.ZERO);
            }else if(detailRespVO.getOutboundableQty().compareTo(detailRespVO.getStockableQty()) <= 0){
                detailRespVO.setOperableQty(detailRespVO.getOutboundableQty());
            }else {
                detailRespVO.setOperableQty(detailRespVO.getStockableQty());
            }
        }

        //处理可用数量小于等于0的数据
        List<PurchaseReturnDetailRespVO> finalList = new ArrayList<>();
        Map<Long, List<PurchaseReturnDetailRespVO>> materialIdMap = respVO.getDetailList().stream().collect(Collectors.groupingBy(PurchaseReturnDetailRespVO::getMaterialId));
        for (Map.Entry<Long, List<PurchaseReturnDetailRespVO>> entry : materialIdMap.entrySet()) {
            List<PurchaseReturnDetailRespVO> detailRespVOList = entry.getValue();
            int flag = detailRespVOList.size();
            for (PurchaseReturnDetailRespVO detailRespVO : detailRespVOList) {
                if (detailRespVO.getStockableQty() != null && detailRespVO.getStockableQty().compareTo(BigDecimal.ZERO) > 0) {
                    finalList.add(detailRespVO);
                } else {
                    flag--;
                }
            }
            if (flag == 0){
                PurchaseReturnDetailRespVO detailRespVO = detailRespVOList.get(0);
                detailRespVO.setWarehouseOrgId(null);
                detailRespVO.setWarehouseOrgName(null);
                detailRespVO.setOperableQty(null);
                detailRespVO.setStockableQty(null);
                detailRespVO.setStockQty(null);
                finalList.add(detailRespVO);
            }
        }

        finalList.sort(Comparator.comparing(PurchaseReturnDetailRespVO::getRowNo));
        respVO.setDetailList(finalList);

        return respVO ;
    }

    private void fillVoProperties(PurchaseReturnRespVO respVO) {
        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), CustomerDictEnum.PURCHASE_RETURN_TYPE.getDictCode(),
                SystemDictEnum.CURRENCY.getDictCode(), CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //票据类型
        InvTypeManageDO invTypeManageDO = invTypeManageMapper.selectById(respVO.getInvoiceTypeId());
        if (ObjUtilX.isNotEmpty(invTypeManageDO)){
            respVO.setInvoiceTypeName(invTypeManageDO.getInvoiceName());
        }

        // 采购单类型
        String purchaseTypeDictId = respVO.getPurchaseTypeDictId();
        if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
            purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
            respVO.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
        }

        // 采购退货单类型
        String returnTypeDictId = respVO.getReturnTypeDictId();
        if(StrUtilX.isNotEmpty(returnTypeDictId)){
            returnTypeDictId = CustomerDictEnum.PURCHASE_RETURN_TYPE.getDictCode() + "-" + returnTypeDictId;
            respVO.setReturnTypeDictName(dictMap.get(returnTypeDictId));
        }

        // 币种
        String currencyDictId = respVO.getCurrencyDictId();
        if(StrUtilX.isNotEmpty(currencyDictId)){
            currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
            respVO.setCurrencyDictName(dictMap.get(currencyDictId));
        }

        // 本币币种
        String localCurrencyDictId = respVO.getLocalCurrencyDictId();
        if(StrUtilX.isNotEmpty(localCurrencyDictId)){
            localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
            respVO.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
        }

        // 结算方式
        String settlementMethodDictId = respVO.getSettlementMethodDictId();
        if(StrUtilX.isNotEmpty(settlementMethodDictId)){
            settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
            respVO.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
        }

        // 退款条件
        String refundConditionDictId = respVO.getRefundConditionDictId();
        if(StrUtilX.isNotEmpty(refundConditionDictId)){
            refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
            respVO.setRefundConditionDictName(dictMap.get(refundConditionDictId));
        }

        //查询负责人
        String directorName = erpBaseService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);

        //查询责任部门
        String directorOrgName = erpBaseService.getOrgNameById(respVO.getDirectorOrgId());
        respVO.setDirectorOrgName(directorOrgName);

        //查询主体公司
        String companyOrgName = erpBaseService.getOrgNameById(respVO.getCompanyOrgId());
        respVO.setCompanyOrgName(companyOrgName);

        //查询供应商名称
        ERPSupplierDO erpSupplierDO = erpSupplierMapper.selectById(respVO.getRelatedSupplierId());
        if(erpSupplierDO != null){
            respVO.setRelatedSupplierName(erpSupplierDO.getSupplierName());
        }
    }

    @Override
    public List<PurchaseReturnRespVO> purchaseReturnList(PurchaseReturnQueryReqVO reqVO) {
        List<PurchaseReturnDO> data = returnMapper.selectList(reqVO);
        return BeanUtilX.copy(data, PurchaseReturnRespVO::new);
    }

    @Override
    public PageResult<PurchaseReturnRespVO> purchaseReturnPage(PurchaseReturnPageReqVO reqVO) {
        PageResult<PurchaseReturnDO> data = returnMapper.selectPage(reqVO);
        PageResult<PurchaseReturnRespVO> pageResult = BeanUtilX.copy(data, PurchaseReturnRespVO::new);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    private void batchFillVoProperties(List<PurchaseReturnRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)){
            return;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for(PurchaseReturnRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            orgIdList.add(item.getCompanyOrgId());
            supplierIdList.add(item.getRelatedSupplierId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(),
                CustomerDictEnum.PURCHASE_RETURN_TYPE.getDictCode(), SystemDictEnum.CURRENCY.getDictCode(),
                CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for (PurchaseReturnRespVO returnResp: respVOList){
            //供应商
            if (returnResp.getRelatedSupplierId() != null){
                returnResp.setRelatedSupplierName(supplierNameMap.get(returnResp.getRelatedSupplierId()));
            }

            //公司主体
            if (StrUtilX.isNotEmpty(returnResp.getCompanyOrgId())){
                returnResp.setCompanyOrgName(directorOrgMap.get(returnResp.getCompanyOrgId()));
            }

            //责任部门
            if (StrUtilX.isNotEmpty(returnResp.getDirectorOrgId())){
                returnResp.setDirectorOrgName(directorOrgMap.get(returnResp.getDirectorOrgId()));
            }

            //责任人
            if (returnResp.getDirectorId() != null){
                returnResp.setDirectorName(directorMap.get(returnResp.getDirectorId()));
            }

            // 采购单类型
            String purchaseTypeDictId = returnResp.getPurchaseTypeDictId();
            if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
                purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
                returnResp.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            // 采购退货单类型
            String returnTypeDictId = returnResp.getReturnTypeDictId();
            if(StrUtilX.isNotEmpty(returnTypeDictId)){
                returnTypeDictId = CustomerDictEnum.PURCHASE_RETURN_TYPE.getDictCode() + "-" + returnTypeDictId;
                returnResp.setReturnTypeDictName(dictMap.get(returnTypeDictId));
            }

            // 币种
            String currencyDictId = returnResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                returnResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = returnResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                returnResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = returnResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                returnResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 退款条件
            String refundConditionDictId = returnResp.getRefundConditionDictId();
            if(StrUtilX.isNotEmpty(refundConditionDictId)){
                refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
                returnResp.setRefundConditionDictName(dictMap.get(refundConditionDictId));
            }

            //审核状态
            if(returnResp.getDataStatus() != null){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + returnResp.getDataStatus();
                returnResp.setDataStatusDictName(dictMap.get(dataStatus));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> purchaseReturnDelBatch(IdReq reqVO) {
        //获取对象属性名
        String purchaseReturnId = EntityUtilX.getPropertyName(PurchaseReturnDO::getPurchaseReturnId);
        String purchaseReturnCode = EntityUtilX.getPropertyName(PurchaseReturnDO::getPurchaseReturnCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), PurchaseReturnDO.class, PurchaseReturnDetailDO.class,
                purchaseReturnId, purchaseReturnCode);
    }

    @Override
    public BatchResult erpPurchaseReturnApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PurchaseReturnDO> list = returnMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PurchaseReturnDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());


                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();


                //流程处理
                FailItem failItem = purchaseReturnApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getPurchaseReturnCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }
        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (PurchaseReturnDO item : list) {
                String reason = reasonMap.get(item.getPurchaseReturnCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPurchaseReturnId());
                    messageInfoBO.setObjCode(item.getPurchaseReturnCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPurchaseReturnId());
                    messageInfoBO.setObjCode(item.getPurchaseReturnCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object erpPurchaseReturnFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PurchaseReturnDO item = this.purchaseReturnValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return purchaseReturnFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    private List<PurchaseReturnDetailDO> fillDetailList(PurchaseReturnAditReqVO reqVO, PurchaseReturnDO returnDO) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        BigDecimal exclTotalAmt = BigDecimal.ZERO;
        List<PurchaseReturnDetailDO> purchaseReturnDetailList = new ArrayList<>();
        List<PurchaseReturnDetailAditReqVO> detailList = reqVO.getDetailList();


        // 校验相同物料编码的可操作数量合并
        Map<String, BigDecimal> materialCodeReturnQtyMap = new HashMap<>();
        Map<String, BigDecimal> materialCodeOperableQtyMap = new HashMap<>();

        // 第一步：收集每个物料编码的退货数量和可操作数量
        for (PurchaseReturnDetailAditReqVO detail : detailList) {
            String materialCode = detail.getMaterialCode();
            BigDecimal returnQty = detail.getReturnQty();
            BigDecimal operableQty = detail.getCompleteableQty() != null ? detail.getCompleteableQty() : returnQty;

            // 累加相同物料编码的退货数量
            materialCodeReturnQtyMap.merge(materialCode, returnQty, BigDecimal::add);

            // 记录相同物料编码的可操作数量（取第一次遇到的值）
            materialCodeOperableQtyMap.putIfAbsent(materialCode, operableQty);
        }

        // 第二步：校验每个物料编码的退货数量是否超过可操作数量
        for (Map.Entry<String, BigDecimal> entry : materialCodeReturnQtyMap.entrySet()) {
            String materialCode = entry.getKey();
            BigDecimal totalReturnQty = entry.getValue();
            BigDecimal operableQty = materialCodeOperableQtyMap.get(materialCode);

            if (operableQty != null && totalReturnQty.compareTo(operableQty) > 0) {
                throw new BizException("5002", "明细存在物料退货数量超过可退货数量，请重新编辑！");
            }
        }

        for (PurchaseReturnDetailAditReqVO detail : detailList){
            BigDecimal unitPrice = BigDecimal.ZERO;
            BigDecimal amt = BigDecimal.ZERO;
            BigDecimal inclTaxPrice = BigDecimal.ZERO;
            BigDecimal inclTaxAmt = BigDecimal.ZERO;

            //以含税价价算未税单价,未税金额
            if (detail.getIncludingTax() != null && detail.getIncludingTax().equals(1)){
                //单价不含税
                unitPrice = MathUtilX.getUnitPrice(detail.getInclTaxUnitPrice(),detail.getTaxRate(), detail.getCalculatType());
                //不含税金额
                amt = MathUtilX.getAmt(unitPrice, detail.getReturnQty());
                //含税单价
                inclTaxPrice = MathUtilX.stripTrailingZeros(detail.getInclTaxUnitPrice());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getReturnQty());
            }else {
                //单价不含税
                unitPrice = MathUtilX.stripTrailingZeros(detail.getExclTaxUnitPrice());
                //不含税金额
                amt = MathUtilX.getAmt(detail.getExclTaxUnitPrice(), detail.getReturnQty());
                //含税单价
                inclTaxPrice = MathUtilX.getInclTaxPrice(detail.getExclTaxUnitPrice(), detail.getTaxRate(), detail.getCalculatType());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getReturnQty());
            }

            detail.setExclTaxUnitPrice(unitPrice);
            detail.setExclTaxAmt(amt);
            detail.setInclTaxUnitPrice(inclTaxPrice);
            detail.setInclTaxAmt(inclTaxAmt);
            detail.setPurchaseReturnId(returnDO.getPurchaseReturnId());
            detail.setPurchaseReturnCode(returnDO.getPurchaseReturnCode());

            //订单含税总金额
            totalAmt = totalAmt.add(inclTaxAmt);
            exclTotalAmt = exclTotalAmt.add(amt);

            PurchaseReturnDetailDO purchaseReturnDetailDO = BeanUtilX.copy(detail, PurchaseReturnDetailDO::new);
            //初始化可出库数量
            purchaseReturnDetailDO.setOutboundableQty(detail.getReturnQty());
            purchaseReturnDetailList.add(purchaseReturnDetailDO);
        }

        //重算含税金额
        returnDO.setInclTaxTotalAmt(totalAmt);
        returnDO.setExclTaxTotalAmt(exclTotalAmt);
        return purchaseReturnDetailList;
    }

}
