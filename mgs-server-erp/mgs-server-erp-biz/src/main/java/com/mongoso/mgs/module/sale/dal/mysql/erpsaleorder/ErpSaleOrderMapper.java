package com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.AccountBalanceVOAI4;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.*;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpSaleOrderMapper extends BaseMapperX<ErpSaleOrderDO> {

    default PageResult<ErpSaleOrderDO> selectPageOld(ErpSaleOrderPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .likeIfPresent(ErpSaleOrderDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .betweenIfPresent(ErpSaleOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpSaleOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(ErpSaleOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(ErpSaleOrderDO::getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderTypeDictId, reqVO.getSalesOrderTypeDictId())
                .likeIfPresent(ErpSaleOrderDO::getContactPersonName, reqVO.getContactPersonName())
                .eqIfPresent(ErpSaleOrderDO::getContactPhone, reqVO.getContactPhone())
                .likeIfPresent(ErpSaleOrderDO::getRecipientName, reqVO.getRecipientName())
                .eqIfPresent(ErpSaleOrderDO::getRecipientPhone, reqVO.getRecipientPhone())
                .eqIfPresent(ErpSaleOrderDO::getReceiptAddress, reqVO.getReceiptAddress())
                .eqIfPresent(ErpSaleOrderDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(ErpSaleOrderDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .eqIfPresent(ErpSaleOrderDO::getPaymentTermsDictId, reqVO.getPaymentTermsDictId())
                .eqIfPresent(ErpSaleOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(ErpSaleOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(ErpSaleOrderDO::getSettlementMethodDictId, reqVO.getSettlementMethodDictId())
                .betweenIfPresent(ErpSaleOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .eqIfPresent(ErpSaleOrderDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(ErpSaleOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatus())
                .inIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatusList())
                .eqIfPresent(ErpSaleOrderDO::getIsForceClose, reqVO.getIsForceClose())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueOut, reqVO.getIsCanIssueOut())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueNotice, reqVO.getIsCanIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueReturn, reqVO.getIsCanIssueReturn())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueFee, reqVO.getIsCanIssueFee())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueExchange, reqVO.getIsCanIssueExchange())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueNotice, reqVO.getIsIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getShippingProcessConfig, reqVO.getShippingProcessConfig())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueMaterialAnalysis, reqVO.getIsIssueMaterialAnalysis())
                .eqIfPresent(ErpSaleOrderDO::getIsIssuePurchaseReq, reqVO.getIsIssuePurchaseReq())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueSaleOut, reqVO.getIsIssueSaleOut())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderBizType, reqVO.getSalesOrderBizType())
                .eqIfPresent(ErpSaleOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueSalePur, reqVO.getIsCanIssueSalePur())
                .eqIfPresent(ErpSaleOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpSaleOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .isNull(ErpSaleOrderDO::getSaleChangId)
                .orderByDesc(ErpSaleOrderDO::getCreatedDt));
    }



    default PageResult<ErpSaleOrderDO> selectPage(ErpSaleOrderPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .likeIfPresent(ErpSaleOrderDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .betweenIfPresent(ErpSaleOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .likeIfPresent(ErpSaleOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(ErpSaleOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(ErpSaleOrderDO::getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderTypeDictId, reqVO.getSalesOrderTypeDictId())
                .likeIfPresent(ErpSaleOrderDO::getContactPersonName, reqVO.getContactPersonName())
                .eqIfPresent(ErpSaleOrderDO::getContactPhone, reqVO.getContactPhone())
                .likeIfPresent(ErpSaleOrderDO::getRecipientName, reqVO.getRecipientName())
                .eqIfPresent(ErpSaleOrderDO::getRecipientPhone, reqVO.getRecipientPhone())
                .eqIfPresent(ErpSaleOrderDO::getReceiptAddress, reqVO.getReceiptAddress())
                .eqIfPresent(ErpSaleOrderDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(ErpSaleOrderDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .eqIfPresent(ErpSaleOrderDO::getPaymentTermsDictId, reqVO.getPaymentTermsDictId())
                .eqIfPresent(ErpSaleOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(ErpSaleOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(ErpSaleOrderDO::getSettlementMethodDictId, reqVO.getSettlementMethodDictId())
                .betweenIfPresent(ErpSaleOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .eqIfPresent(ErpSaleOrderDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(ErpSaleOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatus())
                .inIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatusList())
                .eqIfPresent(ErpSaleOrderDO::getIsForceClose, reqVO.getIsForceClose())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueOut, reqVO.getIsCanIssueOut())
                .eqIfPresent(ErpSaleOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueReturn, reqVO.getIsCanIssueReturn())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueFee, reqVO.getIsCanIssueFee())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueExchange, reqVO.getIsCanIssueExchange())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueNotice, reqVO.getIsIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueNotice, reqVO.getIsCanIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getShippingProcessConfig, reqVO.getShippingProcessConfig())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueMaterialAnalysis, reqVO.getIsIssueMaterialAnalysis())
                .eqIfPresent(ErpSaleOrderDO::getIsIssuePurchaseReq, reqVO.getIsIssuePurchaseReq())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueSaleOut, reqVO.getIsIssueSaleOut())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderBizType, reqVO.getSalesOrderBizType())
                .eqIfPresent(ErpSaleOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpSaleOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueSalePur, reqVO.getIsCanIssueSalePur())
                .betweenIfPresent(ErpSaleOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .inIfPresent(ErpSaleOrderDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(ErpSaleOrderDO::getFormStatus, reqVO.getFormStatus())
                .eqIfPresent(ErpSaleOrderDO::getIsSelfMadeOutsource, reqVO.getIsSelfMadeOutsource())
                .isNull(ErpSaleOrderDO::getSaleChangId)
                        .orderByDesc(ErpSaleOrderDO::getCreatedDt));
    }

    default List<ErpSaleOrderDO> selectListOld(ErpSaleOrderQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .likeIfPresent(ErpSaleOrderDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .betweenIfPresent(ErpSaleOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpSaleOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(ErpSaleOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(ErpSaleOrderDO::getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(ErpSaleOrderDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderTypeDictId, reqVO.getSalesOrderTypeDictId())
                .likeIfPresent(ErpSaleOrderDO::getContactPersonName, reqVO.getContactPersonName())
                .eqIfPresent(ErpSaleOrderDO::getContactPhone, reqVO.getContactPhone())
                .likeIfPresent(ErpSaleOrderDO::getRecipientName, reqVO.getRecipientName())
                .eqIfPresent(ErpSaleOrderDO::getRecipientPhone, reqVO.getRecipientPhone())
                .eqIfPresent(ErpSaleOrderDO::getReceiptAddress, reqVO.getReceiptAddress())
                .eqIfPresent(ErpSaleOrderDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(ErpSaleOrderDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .eqIfPresent(ErpSaleOrderDO::getPaymentTermsDictId, reqVO.getPaymentTermsDictId())
                .eqIfPresent(ErpSaleOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(ErpSaleOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(ErpSaleOrderDO::getSettlementMethodDictId, reqVO.getSettlementMethodDictId())
                .betweenIfPresent(ErpSaleOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .eqIfPresent(ErpSaleOrderDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(ErpSaleOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueOut, reqVO.getIsCanIssueOut())
                .eqIfPresent(ErpSaleOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatus())
                .inIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatusList())
                .eqIfPresent(ErpSaleOrderDO::getIsForceClose, reqVO.getIsForceClose())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueReturn, reqVO.getIsCanIssueReturn())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueFee, reqVO.getIsCanIssueFee())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueExchange, reqVO.getIsCanIssueExchange())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueNotice, reqVO.getIsIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueNotice, reqVO.getIsCanIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getShippingProcessConfig, reqVO.getShippingProcessConfig())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueMaterialAnalysis, reqVO.getIsIssueMaterialAnalysis())
                .eqIfPresent(ErpSaleOrderDO::getIsIssuePurchaseReq, reqVO.getIsIssuePurchaseReq())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueSaleOut, reqVO.getIsIssueSaleOut())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderBizType, reqVO.getSalesOrderBizType())
                .eqIfPresent(ErpSaleOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueSalePur, reqVO.getIsCanIssueSalePur())
                .eqIfPresent(ErpSaleOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpSaleOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .isNull(ErpSaleOrderDO::getSaleChangId)
                    .orderByDesc(ErpSaleOrderDO::getCreatedDt));
    }

    default List<ErpSaleOrderDO> selectList(ErpSaleOrderQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .likeIfPresent(ErpSaleOrderDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .betweenIfPresent(ErpSaleOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(ErpSaleOrderDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(ErpSaleOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(ErpSaleOrderDO::getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderTypeDictId, reqVO.getSalesOrderTypeDictId())
                .likeIfPresent(ErpSaleOrderDO::getContactPersonName, reqVO.getContactPersonName())
                .eqIfPresent(ErpSaleOrderDO::getContactPhone, reqVO.getContactPhone())
                .likeIfPresent(ErpSaleOrderDO::getRecipientName, reqVO.getRecipientName())
                .eqIfPresent(ErpSaleOrderDO::getRecipientPhone, reqVO.getRecipientPhone())
                .eqIfPresent(ErpSaleOrderDO::getReceiptAddress, reqVO.getReceiptAddress())
                .eqIfPresent(ErpSaleOrderDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .likeIfPresent(ErpSaleOrderDO::getInvoiceTypeName, reqVO.getInvoiceTypeName())
                .eqIfPresent(ErpSaleOrderDO::getPaymentTermsDictId, reqVO.getPaymentTermsDictId())
                .eqIfPresent(ErpSaleOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(ErpSaleOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(ErpSaleOrderDO::getSettlementMethodDictId, reqVO.getSettlementMethodDictId())
                .betweenIfPresent(ErpSaleOrderDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .eqIfPresent(ErpSaleOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(ErpSaleOrderDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatus())
                .inIfPresent(ErpSaleOrderDO::getOutboundStatus, reqVO.getOutboundStatusList())
                .eqIfPresent(ErpSaleOrderDO::getIsForceClose, reqVO.getIsForceClose())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueNotice, reqVO.getIsCanIssueNotice())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueReturn, reqVO.getIsCanIssueReturn())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssuePur, reqVO.getIsCanIssuePur())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueOut, reqVO.getIsCanIssueOut())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueFee, reqVO.getIsCanIssueFee())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueExchange, reqVO.getIsCanIssueExchange())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssuePro, reqVO.getIsCanIssuePro())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueMater, reqVO.getIsCanIssueMater())
                .eqIfPresent(ErpSaleOrderDO::getShippingProcessConfig, reqVO.getShippingProcessConfig())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueMaterialAnalysis, reqVO.getIsIssueMaterialAnalysis())
                .eqIfPresent(ErpSaleOrderDO::getIsIssuePurchaseReq, reqVO.getIsIssuePurchaseReq())
                .eqIfPresent(ErpSaleOrderDO::getIsIssueSaleOut, reqVO.getIsIssueSaleOut())
                .eqIfPresent(ErpSaleOrderDO::getSalesOrderBizType, reqVO.getSalesOrderBizType())
                .eqIfPresent(ErpSaleOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpSaleOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(ErpSaleOrderDO::getIsCanIssueSalePur, reqVO.getIsCanIssueSalePur())
                .betweenIfPresent(ErpSaleOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .inIfPresent(ErpSaleOrderDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(ErpSaleOrderDO::getFormStatus, reqVO.getFormStatus())
                .eqIfPresent(ErpSaleOrderDO::getIsSelfMadeOutsource, reqVO.getIsSelfMadeOutsource())
                .isNull(ErpSaleOrderDO::getSaleChangId)
                        .orderByDesc(ErpSaleOrderDO::getCreatedDt));
    }

    default int batchDelete(ErpSaleOrderQueryReqVO reqVO){
        if (reqVO == null){
            return 0;
        }
        return delete(LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .eqIfPresent(ErpSaleOrderDO::getSaleChangId,reqVO.getSaleChangId())
        );
    }

    IPage<ErpSaleOrderDetailResp> queryErpSaleOrderDetailPage(Page<ErpSaleOrderPageReqVO> page, @Param("reqVO") ErpSaleOrderPageReqVO reqVO);

    IPage<ErpSaleOrderRespVO> selectSaleForDemandPage(Page<ErpSaleOrderPageReqVO> page, @Param("reqVO") ErpSaleOrderPageReqVO reqVO);

    List<ErpSaleOrderDetailResp> selectSaleForDemandList(@Param("reqVO") ErpSaleOrderQueryReqVO reqVO);

    default List<ErpSaleOrderDO> selectListByDataStatus(Integer dataStatus){
        return selectList(LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .eq(ErpSaleOrderDO::getDataStatus, dataStatus));
    }

    default ErpSaleOrderDO selectOne(ErpSaleOrderQueryReqVO reqVO) {
        return selectOne(LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .eqIfPresent(ErpSaleOrderDO::getSaleOrderCode, reqVO.getSaleOrderCode())
                .isNull(ErpSaleOrderDO::getSaleChangId));
    }

    default List<ErpSaleOrderDO> selectListJob(Integer dataStatus, List<Integer> formStatusList, List<Integer> outboundStatusList){
        return selectList(LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .isNull(ErpSaleOrderDO::getSaleChangId)
                .eq(ErpSaleOrderDO::getDataStatus, dataStatus)
                .in(ErpSaleOrderDO::getFormStatus, formStatusList)
                .in(ErpSaleOrderDO::getOutboundStatus, outboundStatusList));
    }

    default ErpSaleOrderDO selectOneByCode(String saleOrderCode){
        return selectOne(LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .eq(ErpSaleOrderDO::getSaleOrderCode, saleOrderCode));
    }

    default PageResult<ErpSaleOrderDO> selectPageAI(ErpSaleOrderPageAIReqVO reqVO){
        return selectPage(reqVO,LambdaQueryWrapperX.<ErpSaleOrderDO>lambdaQueryX()
                .inIfPresent(ErpSaleOrderDO::getCustomerId, reqVO.getCustomerIdList()));
    }

    List<AccountBalanceVOAI4> selectBy();

}