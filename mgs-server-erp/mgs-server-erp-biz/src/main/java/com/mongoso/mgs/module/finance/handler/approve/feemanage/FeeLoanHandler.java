package com.mongoso.mgs.module.finance.handler.approve.feemanage;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feeapply.FeeApplyDO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feeloan.FeeLoanDO;
import com.mongoso.mgs.module.finance.dal.mysql.feemanage.feeloan.FeeLoanMapper;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;


@Component
public class FeeLoanHandler extends FlowApproveHandler<FeeLoanDO> {
    @Resource
    private FeeLoanMapper feeLoanMapper;


    @Override
    protected ApproveCommonAttrs approvalAttributes(FeeLoanDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(FeeLoanDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(FeeLoanDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getFeeLoanId())
                .objCode(item.getLoanCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(FeeLoanDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {

        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            if (item.getOutBillStatus() == 1){
                failItem.setCode(item.getLoanCode());
                failItem.setReason("已出账状态下不能反审");
                return false;
            }
        }

        return true;
    }



    @Override
    public Integer handleBusinessData(FeeLoanDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = item.getFeeLoanId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        FeeLoanDO exist  = feeLoanMapper.selectById(id);
        exist.setApprovedBy(loginUser.getFullUserName());
        exist.setApprovedDt(LocalDateTime.now());
        exist.setDataStatus(dataStatus.shortValue());
        // todo 审核需上游 已审核，反审核 需下游 未审核
        // 审核通过，反审核通过
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {

        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){

        }
        //更新业务数据
        Integer updateCount = feeLoanMapper.updateById(exist);

        return updateCount;
    }
}
