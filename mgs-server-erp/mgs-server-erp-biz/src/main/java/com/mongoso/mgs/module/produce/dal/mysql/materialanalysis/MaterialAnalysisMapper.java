package com.mongoso.mgs.module.produce.dal.mysql.materialanalysis;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.produce.dal.db.materialanalysis.MaterialAnalysisDO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticePageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 物料分析 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialAnalysisMapper extends BaseMapperX<MaterialAnalysisDO> {

    default PageResult<MaterialAnalysisDO> selectPageOld(MaterialAnalysisPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<MaterialAnalysisDO>lambdaQueryX()
                .betweenIfPresent(MaterialAnalysisDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .likeIfPresent(MaterialAnalysisDO::getMaterialAnalysisCode, reqVO.getMaterialAnalysisCode())
                .eqIfPresent(MaterialAnalysisDO::getMaterialAnalysisTypeDictId, reqVO.getMaterialAnalysisTypeDictId())
                .eqIfPresent(MaterialAnalysisDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(MaterialAnalysisDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(MaterialAnalysisDO::getAnalysisScope, reqVO.getAnalysisScope())
                .eqIfPresent(MaterialAnalysisDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(MaterialAnalysisDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MaterialAnalysisDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(MaterialAnalysisDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(MaterialAnalysisDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MaterialAnalysisDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(MaterialAnalysisDO::getUseEnable,1)
                .betweenIfPresent(MaterialAnalysisDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(MaterialAnalysisDO::getCreatedDt));
    }



    default PageResult<MaterialAnalysisDO> selectPage(MaterialAnalysisPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<MaterialAnalysisDO>lambdaQueryX()
                .eqIfPresent(MaterialAnalysisDO::getBizType, reqVO.getBizType())
                .betweenIfPresent(MaterialAnalysisDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .likeIfPresent(MaterialAnalysisDO::getMaterialAnalysisCode, reqVO.getMaterialAnalysisCode())
                .eqIfPresent(MaterialAnalysisDO::getMaterialAnalysisTypeDictId, reqVO.getMaterialAnalysisTypeDictId())
                .eqIfPresent(MaterialAnalysisDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(MaterialAnalysisDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .likeIfPresent(MaterialAnalysisDO::getAnalysisScope, reqVO.getAnalysisScope())
                .eqIfPresent(MaterialAnalysisDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(MaterialAnalysisDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MaterialAnalysisDO::getIsFullPurchased, reqVO.getIsFullPurchased())
                .eqIfPresent(MaterialAnalysisDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(MaterialAnalysisDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(MaterialAnalysisDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(MaterialAnalysisDO::getUseEnable,1)
                .betweenIfPresent(MaterialAnalysisDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(MaterialAnalysisDO::getCreatedDt));
    }

    default List<MaterialAnalysisDO> selectListOld(MaterialAnalysisQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<MaterialAnalysisDO>lambdaQueryX()
                .betweenIfPresent(MaterialAnalysisDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .likeIfPresent(MaterialAnalysisDO::getMaterialAnalysisCode, reqVO.getMaterialAnalysisCode())
                .eqIfPresent(MaterialAnalysisDO::getMaterialAnalysisTypeDictId, reqVO.getMaterialAnalysisTypeDictId())
                .eqIfPresent(MaterialAnalysisDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(MaterialAnalysisDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(MaterialAnalysisDO::getAnalysisScope, reqVO.getAnalysisScope())
                .eqIfPresent(MaterialAnalysisDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(MaterialAnalysisDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MaterialAnalysisDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(MaterialAnalysisDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(MaterialAnalysisDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MaterialAnalysisDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(MaterialAnalysisDO::getUseEnable,1)
                .inIfPresent(MaterialAnalysisDO::getRelatedOrderId,reqVO.getRelatedOrderIdList())
                .betweenIfPresent(MaterialAnalysisDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(MaterialAnalysisDO::getCreatedDt));
    }

    default List<MaterialAnalysisDO> selectList(MaterialAnalysisQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<MaterialAnalysisDO>lambdaQueryX()
                .eqIfPresent(MaterialAnalysisDO::getBizType, reqVO.getBizType())
                .betweenIfPresent(MaterialAnalysisDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .likeIfPresent(MaterialAnalysisDO::getMaterialAnalysisCode, reqVO.getMaterialAnalysisCode())
                .eqIfPresent(MaterialAnalysisDO::getMaterialAnalysisTypeDictId, reqVO.getMaterialAnalysisTypeDictId())
                .eqIfPresent(MaterialAnalysisDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(MaterialAnalysisDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(MaterialAnalysisDO::getAnalysisScope, reqVO.getAnalysisScope())
                .eqIfPresent(MaterialAnalysisDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(MaterialAnalysisDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MaterialAnalysisDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(MaterialAnalysisDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(MaterialAnalysisDO::getIsFullPurchased, reqVO.getIsFullPurchased())
                .betweenIfPresent(MaterialAnalysisDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(MaterialAnalysisDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(MaterialAnalysisDO::getUseEnable,1)
                .betweenIfPresent(MaterialAnalysisDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(MaterialAnalysisDO::getCreatedDt));
    }

    /**
     * 查询已审核，且单据下存在【委外】类型且可采购数量不为0物料的物料分析单
     * @param reqVO
     * @return
     */
    List<MaterialAnalysisRespVO> queryList4Purchase(@Param("reqVO") MaterialAnalysisQueryReqVO reqVO);

    IPage<MaterialAnalysisRespVO> queryPage(Page<MaterialAnalysisRespVO> page, @Param("reqVO") MaterialAnalysisPageReqVO reqVO);
}