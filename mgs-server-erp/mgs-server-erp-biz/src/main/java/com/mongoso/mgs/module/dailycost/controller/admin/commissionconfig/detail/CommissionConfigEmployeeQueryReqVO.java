package com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.detail;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 提成规则关联员工 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class CommissionConfigEmployeeQueryReqVO {

    /** 提成规则关联员工表主键ID */
    private Long commissionConfigEmployeeId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 创建人ID */
    private Long createdId;

    /** 员工档案ID */
    private Long employeeArchivesId;

    /** 创建人 */
    private String createdBy;

    /** 员工工号 */
    private String employeeNumber;

    /** 分配比例 */
    private BigDecimal assignRatio;

    /** 分配金额 */
    private BigDecimal assignAmt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 版本号 */
    private Integer version;

    /** 行号 */
    private Short rowNo;

}
