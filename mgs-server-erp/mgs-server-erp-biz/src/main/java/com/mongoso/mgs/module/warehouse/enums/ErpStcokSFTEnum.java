package com.mongoso.mgs.module.warehouse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存来源单据类型
 */

@AllArgsConstructor
@Getter
public enum ErpStcokSFTEnum {

    MI_INBOUND(0,"入库单入库"),
    AO_INBOUND(1,"组装单入库"),
    DO_INBOUND(2,"拆卸单入库"),
    RF_INBOUND(3,"归还单入库"),
    TO_INBOUND(4,"调拨单入库"),

    MO_OUTBOUND(10,"出库单出库"),
    AO_OUTBOUND(11,"组装单出库"),
    DO_OUTBOUND(12,"拆卸单出库"),
    LF_OUTBOUND(13,"外借单出库"),
    TO_OUTBOUND(14,"调拨单出库"),

    BOOK(15, "预定任务"),

    ADJUST(16,"库存调整");


    public final int type;// 类型
    public final String desc;// 描述
}
