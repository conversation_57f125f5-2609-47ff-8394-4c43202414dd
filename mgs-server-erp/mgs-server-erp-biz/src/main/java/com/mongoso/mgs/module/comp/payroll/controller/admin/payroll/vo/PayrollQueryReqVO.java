package com.mongoso.mgs.module.comp.payroll.controller.admin.payroll.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 工资单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class PayrollQueryReqVO {

    /** 工资单号 */
    private String payrollCode;

    /** 工资单主题 */
    private String payrollName;

    /** 组织ID */
    private String companyOrgId;

    /** 状态 ["未发放", "已发放"] */
    private Integer payrollStatus;

    /** 工资单月份 */
    private String payrollMonth;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据状态 */
    private Integer dataStatus;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;
}
