package com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimburse;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimburse.vo.*;
import com.mongoso.mgs.module.finance.service.feemanage.feereimburse.FeeReimburseService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 费用报销 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/finance")
@Validated
public class FeeReimburseController {

    @Resource
    private FeeReimburseService feeReimburseService;

    @OperateLog("费用报销添加或编辑")
    @PostMapping("/feeReimburseAdit")
    @PreAuthorize("@ss.hasPermission('feeReimburse:adit')")
    public ResultX<Long> feeReimburseAdit(@Valid @RequestBody FeeReimburseAditReqVO reqVO) {
        return success(reqVO.getFeeReimburseId() == null
                            ? feeReimburseService.feeReimburseAdd(reqVO)
                            : feeReimburseService.feeReimburseEdit(reqVO));
    }

    @OperateLog("费用报销删除")
    @PostMapping("/feeReimburseDel")
    @PreAuthorize("@ss.hasPermission('feeReimburse:delete')")
    public ResultX<Boolean> feeReimburseDel(@Valid @RequestBody FeeReimbursePrimaryReqVO reqVO) {
        feeReimburseService.feeReimburseDel(reqVO.getFeeReimburseId());
        return success(true);
    }

    @OperateLog("费用报销详情")
    @PostMapping("/feeReimburseDetail")
    @PreAuthorize("@ss.hasPermission('feeReimburse:query')")
    public ResultX<FeeReimburseRespVO> feeReimburseDetail(@Valid @RequestBody FeeReimbursePrimaryReqVO reqVO) {
        return success(feeReimburseService.feeReimburseDetail(reqVO.getFeeReimburseId()));
    }

    @OperateLog("费用报销列表")
    @PostMapping("/feeReimburseList")
    @PreAuthorize("@ss.hasPermission('feeReimburse:query')")
    @DataPermission
    public ResultX<List<FeeReimburseRespVO>> feeReimburseList(@Valid @RequestBody FeeReimburseQueryReqVO reqVO) {
        return success(feeReimburseService.feeReimburseList(reqVO));
    }

    @OperateLog("费用报销分页")
    @PostMapping("/feeReimbursePage")
    @PreAuthorize("@ss.hasPermission('feeReimburse:query')")
    @DataPermission
    public ResultX<PageResult<FeeReimburseRespVO>> feeReimbursePage(@Valid @RequestBody FeeReimbursePageReqVO reqVO) {
        return success(feeReimburseService.feeReimbursePage(reqVO));
    }

    @OperateLog("费用报销批量删除")
    @PostMapping("/feeReimburseDelBatch")
    @PreAuthorize("@ss.hasPermission('feeReimburse:del')")
    public ResultX<BatchResult> feeReimburseDelBatch(@Valid @RequestBody IdReq reqVO) {
        return feeReimburseService.feeReimburseDelBatch(reqVO);
    }

    @OperateLog("费用报销审核")
    @PostMapping("/feeReimburseApprove")
    @PreAuthorize("@ss.hasPermission('feeReimburse:adit')")
    public ResultX<BatchResult> feeReimburseApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(feeReimburseService.feeReimburseApprove(reqVO));
    }

    @OperateLog("费用报销回调接口")
    @PostMapping("/feeReimburseFlowCallback")
    public ResultX<Object> feeReimburseFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(feeReimburseService.feeReimburseFlowCallback(reqVO));
    }

}
