package com.mongoso.mgs.module.warehouse.dal.mysql.materialstock;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockChangeStatPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockChangeStatRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.materialstock.ErpMaterialStockRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpMaterialStockRecordMapper extends BaseMapperX<ErpMaterialStockRecordDO> {

    IPage<ErpMaterialStockChangeStatRespVO> queryStockChangeStatPage(Page<ErpMaterialStockChangeStatPageReqVO> page,
                                                                     @Param("reqVO") ErpMaterialStockChangeStatPageReqVO reqVO);

    List<ErpMaterialStockChangeStatRespVO> queryStartStockList(@Param("minStockRecordIdList") List<Long> minStockRecordIdList);
}