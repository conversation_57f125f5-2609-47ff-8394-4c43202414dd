package com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 库存锁定单明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StockLockDetailPageReqVO extends PageParam {

    /** 库存锁定单号 */
    private String lockCode;

    /** 锁定单类型 */
    private String lockTypeDictId;

    /** 锁定原因 */
    private String lockReason;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料编码 */
    private String materialName;

    /** 规格型号 */
    private String specModel;

    /** 物料类别字典ID */
    private String materialCategoryDictId;

    /** 责任人D */
    private Long directorId;

    /** 责任人组织ID */
    private String directorOrgId;

    /** 仓库组织ID */
    private String warehouseOrgId;

    /** 审核状态 */
    private Short dataStatus;

    /** 单据开始时间 */
    private LocalDateTime startFormDt;

    /** 单据结束时间 */
    private LocalDateTime endFormDt;
}
