package com.mongoso.mgs.module.produce.handler.approve;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.produce.DictToEnum;
import com.mongoso.mgs.common.enums.produce.ProduceEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO;
import com.mongoso.mgs.module.base.dal.mysql.erpmaterial.ERPMaterialMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.orderrelation.OrderRelationService;
import com.mongoso.mgs.module.produce.controller.admin.dispatchstrategy.vo.DispatchStrategyQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.vo.DispatchWorkAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.vo.DispatchWorkQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.bo.ErpProdOrderBO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocessdetail.vo.FlowProcessDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal.bo.MaterialAnalysisTotalBO;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.ItemReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingAditReqVO;
import com.mongoso.mgs.module.produce.dal.db.dispatchstrategy.DispatchStrategyDO;
import com.mongoso.mgs.module.produce.dal.db.dispatchwork.DispatchWorkDO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.produce.dal.db.flowprocessdetail.FlowProcessDetailDO;
import com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.prodworkmaterialbom.ProdWorkMaterialBomDO;
import com.mongoso.mgs.module.produce.dal.db.workcenter.WorkCenterDO;
import com.mongoso.mgs.module.produce.dal.mysql.dispatchstrategy.DispatchStrategyMapper;
import com.mongoso.mgs.module.produce.dal.mysql.dispatchwork.DispatchWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.flowprocessdetail.FlowProcessDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodworkmaterialbom.ProdWorkMaterialBomMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workcenter.WorkCenterMapper;
import com.mongoso.mgs.module.produce.enums.ErrorCodeConstants;
import com.mongoso.mgs.module.produce.service.dispatchstrategy.enums.MaterialApplyRangeEnum;
import com.mongoso.mgs.module.produce.service.dispatchwork.DispatchWorkService;
import com.mongoso.mgs.module.produce.service.erpprodorder.ErpProdOrderService;
import com.mongoso.mgs.module.produce.service.materialanalysistotal.MaterialAnalysisTotalService;
import com.mongoso.mgs.module.produce.service.processoutdemand.ProcessOutDemandService;
import com.mongoso.mgs.module.produce.service.workpicking.WorkPickingService;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictNewQueryReqVO;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictNewRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.ErpOutboundAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.enums.ErpOutboundBizTypeEnum;
import com.mongoso.mgs.module.warehouse.service.erpoutbound.ErpOutboundService;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhiling
 * @date: 2024/11/26 18:34
 * @description: 生产工单审批流程处理类
 */

@Component
public class ProdWorkApproveHandler extends FlowApproveHandler<ProdWorkDO> {

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;

    @Lazy
    @Resource
    private ErpProdOrderService erpProdOrderService;

    @Resource
    private MaterialAnalysisTotalService materialAnalysisTotalService;

    @Lazy
    @Resource
    private DispatchWorkMapper dispatchWorkMapper;

    @Lazy
    @Resource
    private ProcessOutDemandMapper processOutDemandMapper;

    @Lazy
    @Resource
    private DispatchWorkService dispatchWorkService;

    @Lazy
    @Resource
    private ProcessOutDemandService processOutDemandService;

    @Resource
    private FlowProcessDetailMapper flowProcessDetailMapper;

    @Resource
    private OrderRelationService orderRelationService;

    @Resource
    private StockBookService stockBookService;

    @Resource
    private DispatchStrategyMapper dispatchStrategyMapper;

    @Resource
    private ERPMaterialMapper erpMaterialMapper;

    @Lazy
    @Resource
    private WorkCenterMapper workCenterMapper;


    @Resource
    private ProdWorkMaterialBomMapper prodWorkMaterialBomMapper;

    @Lazy
    @Resource
    private WorkPickingService workPickingService;


    @Override
    protected ApproveCommonAttrs approvalAttributes(ProdWorkDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(ProdWorkDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(ProdWorkDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getProdWorkId())
                .objCode(item.getProdWorkCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(ProdWorkDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            ErpProdOrderDO erpProdOrderDO = erpProdOrderMapper.selectById(item.getProdOrderId());
            if (erpProdOrderDO == null) {
                failItem.setCode(item.getProdWorkCode());
                failItem.setReason(ErrorCodeConstants.NOT_EXIST.getMsg());
                return false;
            }
            if (erpProdOrderDO.getDataStatus() != DataStatusEnum.APPROVED.key) {
                failItem.setCode(item.getProdWorkCode());
                failItem.setReason(ErrorCodeConstants.NOT_APPROVED.getMsg());
                return false;
            }

            //校验工艺路线
            FlowProcessDetailQueryReqVO flowProcessDetailQuery = new FlowProcessDetailQueryReqVO();
            flowProcessDetailQuery.setFlowProcessId(item.getProdWorkId());
            flowProcessDetailQuery.setFlowProcessType(1);
            List<FlowProcessDetailDO> flowProcessDetailDOList = flowProcessDetailMapper.selectListOld(flowProcessDetailQuery);
            if (CollUtilX.isEmpty(flowProcessDetailDOList)) {
                failItem.setCode(item.getProdWorkCode());
                failItem.setReason("生产工单未设置工艺路线,审核失败!");
                return false;
            }

        }

        //todo 反审核限制
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //查询派工单数据
            DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
            dispatchWorkQuery.setProdWorkId(item.getProdWorkId());

            List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
            if (CollUtilX.isNotEmpty(dispatchWorkDOList)) {
                failItem.setCode(item.getProdWorkCode());
                failItem.setReason("已关联派工单数据,不允许进行反审核操作!");
                return false;
            }

            ProcessOutDemandQueryReqVO processOutDemandQuery = new ProcessOutDemandQueryReqVO();
            processOutDemandQuery.setProdWorkId(item.getProdWorkId());
            processOutDemandQuery.setFormStatusNeq(ProduceEnum.PENDING_PURCHASE.code);
            List<ProcessOutDemandDO> outDemandList = processOutDemandMapper.selectListOld(processOutDemandQuery);
            if (CollUtilX.isNotEmpty(outDemandList)) {
                failItem.setCode(item.getProdWorkCode());
                failItem.setReason("工序委外需求清单已有实际采购数量不允许反审核!");
                return false;
            }
        }

        return true;
    }

    @Override
    public Integer handleBusinessData(ProdWorkDO currentDO, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        //当前对象
        if (currentDO == null) {
            return 1;
        }

        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        Long id = currentDO.getProdWorkId();

        Boolean updateWorkPlanQty = false;
        BigDecimal workPlanTotalQty = currentDO.getWorkPlanTotalQty();
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            updateWorkPlanQty = true;

            //自动派工策略
            doAutoDispatch(currentDO);

            // 判断是否需要自动下发工单领料单
            if (currentDO.getIsIssueWorkPicking() != null && currentDO.getIsIssueWorkPicking() == 1) {
                autoIssueWorkPicking(currentDO);
            }

            // 判断是否需要自动下发工单直接领料出库单
//            if (currentDO.getIsIssueOutbound() != null && currentDO.getIsIssueOutbound() == 1) {
//                autoIssueOutbound(currentDO);
//            }
        }

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            updateWorkPlanQty = true;
            workPlanTotalQty = workPlanTotalQty.negate();

            //自动派工删除委外需求单
            if (currentDO.getDispatchStrategyConfig() != null && currentDO.getDispatchStrategyConfig().equals(1)) {
                processOutDemandMapper.batchDelete(currentDO.getProdWorkId(), currentDO.getDispatchStrategyConfig());
            }
        }

        if (updateWorkPlanQty) {
            //更新生产订单计划数量和状态
            ErpProdOrderBO orderBO = new ErpProdOrderBO();
            orderBO.setProdOrderId(currentDO.getProdOrderId());
            orderBO.setMaterialId(currentDO.getMaterialId());
            orderBO.setWorkPlanQty(workPlanTotalQty);
            erpProdOrderService.writeBackErpProdOrder(orderBO);

            //更新物料分析数量汇总表 已下发数量
            MaterialAnalysisTotalBO analysisTotalBO = new MaterialAnalysisTotalBO();
            analysisTotalBO.setProdOrderId(currentDO.getProdOrderId());
            analysisTotalBO.setMaterialId(currentDO.getMaterialId());
            analysisTotalBO.setIssuedQty(workPlanTotalQty);
            materialAnalysisTotalService.writebackMaterialAnalysisTotal(analysisTotalBO);
        }

        //关联信息处理
        orderRelationService.orderRelationAditOrDel(buttonType, id, currentDO.getProdWorkCode(), currentDO.getProdOrderId());

        //处理单据关联的预订任务
        stockBookService.bookReleaseByOrderApprove(buttonType, id);

        //更新状态
        currentDO.setApprovedBy(loginUser.getFullUserName());
        currentDO.setApprovedDt(LocalDateTime.now());
        currentDO.setDataStatus(dataStatus);

        int updateCount = prodWorkMapper.updateById(currentDO);
        return updateCount;
    }


    /**
     * 自动派工策略
     *
     * @param currentDO
     * @return
     */
    private void doAutoDispatch(ProdWorkDO currentDO) {

        Long prodWorkId = currentDO.getProdWorkId();

        //查询生产工单下的工艺路线
        FlowProcessDetailQueryReqVO flowProcessDetailQuery = new FlowProcessDetailQueryReqVO();
        flowProcessDetailQuery.setFlowProcessId(prodWorkId);
        flowProcessDetailQuery.setFlowProcessType(1);
        List<FlowProcessDetailDO> flowProcessDetailDOList = flowProcessDetailMapper.selectListOld(flowProcessDetailQuery);
        if (CollUtilX.isEmpty(flowProcessDetailDOList)) {
            return;
        }

        //查询物料
        ERPMaterialDO materialDO = erpMaterialMapper.selectById(currentDO.getMaterialId());

        //委外工序自动派工
        for (FlowProcessDetailDO item : flowProcessDetailDOList) {
            Long processMethod = item.getProcessMethod();

            if (!processMethod.equals(DictToEnum.OUTSOURCE.key)) {
                continue;
            }
            ProcessOutDemandAditReqVO aditReqVO = BeanUtilX.copy(currentDO, ProcessOutDemandAditReqVO::new);
            aditReqVO.setDispatchMethod(currentDO.getProcessConfig());
            aditReqVO.setOutDemandQty(currentDO.getWorkPlanTotalQty());
            aditReqVO.setPriority(currentDO.getPriority());
            aditReqVO.setLogSource(currentDO.getProcessConfig());

            aditReqVO.setProcessId(item.getProcessId());
            aditReqVO.setProcessCode(item.getProcessCode());
            aditReqVO.setProcessName(item.getProcessName());
            aditReqVO.setProcessMethod(item.getProcessMethod());
            aditReqVO.setFinalProcess(item.getFinalProcess());
            aditReqVO.setPieceworkMethodDictId(item.getPieceworkMethodDictId());

            aditReqVO.setMaterialName(materialDO.getMaterialName());
            aditReqVO.setProcessOperationType(0);
            aditReqVO.setDispatchStrategyConfig(currentDO.getDispatchStrategyConfig());

            processOutDemandService.workProcessOutDemandAdd(aditReqVO);

            //更新工单状态为生产中
            currentDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
        }

        //手动派工
        if (currentDO.getDispatchStrategyConfig() == null || currentDO.getDispatchStrategyConfig().equals(0)) {
            return;
        }

        //查询派工策略
        DispatchStrategyDO dispatchStrategy = null;
        DispatchStrategyQueryReqVO dispatchStrategyQuery = new DispatchStrategyQueryReqVO();
        dispatchStrategyQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        dispatchStrategyQuery.setMaterialApplyRange(MaterialApplyRangeEnum.material_all.key);
        List<DispatchStrategyDO> dispatchStrategyList = dispatchStrategyMapper.selectList(dispatchStrategyQuery);
        if (CollUtilX.isNotEmpty(dispatchStrategyList)) {
            dispatchStrategy = dispatchStrategyList.get(0);
        }else {
            dispatchStrategy = dispatchStrategyMapper.queryMaterial(null, null, currentDO.getMaterialId());
        }

        if (dispatchStrategy == null) {
            return;
        }

        //查询工作中心
        WorkCenterDO workCenterDO = workCenterMapper.selectById(dispatchStrategy.getWorkCenterId());

        //自制工序自动派工
        for (FlowProcessDetailDO item : flowProcessDetailDOList) {
            Long processMethod = item.getProcessMethod();

            if (!processMethod.equals(DictToEnum.SELF_MADE.key)) {
                continue;
            }

            DispatchWorkAditReqVO aditReqVO = BeanUtilX.copy(currentDO, DispatchWorkAditReqVO::new);
            aditReqVO.setDispatchMethod(currentDO.getProcessConfig());
            aditReqVO.setDispatchQty(currentDO.getWorkPlanTotalQty());
            aditReqVO.setPriority(currentDO.getPriority());
            aditReqVO.setLogSource(currentDO.getProcessConfig());

            aditReqVO.setProcessId(item.getProcessId());
            aditReqVO.setProcessCode(item.getProcessCode());
            aditReqVO.setProcessName(item.getProcessName());
            aditReqVO.setProcessMethod(item.getProcessMethod());
            aditReqVO.setFinalProcess(item.getFinalProcess());
            aditReqVO.setPieceworkMethodDictId(item.getPieceworkMethodDictId());

            aditReqVO.setWorkCenterId(workCenterDO.getWorkCenterId());
            aditReqVO.setWorkCenterCode(workCenterDO.getWorkCenterCode());
            aditReqVO.setWorkCenterName(workCenterDO.getWorkCenterName());

            aditReqVO.setMaterialName(materialDO.getMaterialName());
            aditReqVO.setProcessOperationType(0);
            aditReqVO.setDispatchStrategyConfig(currentDO.getDispatchStrategyConfig());

            dispatchWorkService.workProcessDispatchAdd(aditReqVO);
        }

        //更新工单状态为生产中
        currentDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
    }

    /**
     * 自动下发工单领料单
     * 只能下发生产物料的BOM清单中正向领料的物料数据
     *
     * @param currentDO 生产工单
     */
    private void autoIssueWorkPicking(ProdWorkDO currentDO) {
        Long prodWorkId = currentDO.getProdWorkId();

        // 查询生产工单BOM清单中正向领料的物料
        List<ProdWorkMaterialBomDO> workMaterialBomList = prodWorkMaterialBomMapper.selectListByRelatedOrderId(prodWorkId);
        if (CollUtilX.isEmpty(workMaterialBomList)) {
            return;
        }

        // 筛选正向领料的物料
        List<ProdWorkMaterialBomDO> forwardPickingMaterials = workMaterialBomList.stream()
                .filter(bom -> bom.getPickingMethodDictId() == null ||
                        bom.getPickingMethodDictId().equals(Math.toIntExact(DictToEnum.FORWARD.key)))
                .collect(Collectors.toList());

        if (CollUtilX.isEmpty(forwardPickingMaterials)) {
            return;
        }

        // 创建工单领料单
        WorkPickingAditReqVO workPickingReqVO = new WorkPickingAditReqVO();

        workPickingReqVO.setWorkPickingTypeDictId("0");
        workPickingReqVO.setWorkPickingBizType(0); // 工单领料单
        workPickingReqVO.setRelatedOrderId(prodWorkId);
        workPickingReqVO.setRelatedOrderCode(currentDO.getProdWorkCode());
        workPickingReqVO.setMaterialId(currentDO.getMaterialId());
        workPickingReqVO.setMaterialCode(currentDO.getMaterialCode());
        workPickingReqVO.setDirectorId(currentDO.getDirectorId());
        workPickingReqVO.setDirectorOrgId(currentDO.getDirectorOrgId());
        workPickingReqVO.setFormDt(currentDO.getFormDt());

        // 设置明细
        List<ItemReqVO> detailList = new ArrayList<>();
        int rowNo = 1;

        for (ProdWorkMaterialBomDO bom : forwardPickingMaterials) {
            ItemReqVO detailReqVO = new ItemReqVO();
            detailReqVO.setRowNo(rowNo++);
            detailReqVO.setMaterialId(bom.getMaterialId());
            detailReqVO.setMaterialCode(bom.getMaterialCode());

            // 设置需求数量和损耗率
            detailReqVO.setDemandQty(bom.getDemandQty());
            detailReqVO.setLossRate(bom.getLossRate());

            // 计算预估数量 = 计划生产数量 * 需求数量 * (1 + 损耗率/100)
            BigDecimal workPlanQty = EntityUtilX.getBigDecDefault(currentDO.getWorkPlanTotalQty());
            BigDecimal demandQty = EntityUtilX.getBigDecDefault(bom.getDemandQty());
            BigDecimal lossRate = EntityUtilX.getBigDecDefault(bom.getLossRate());

            // 计算损耗率系数 (1 + 损耗率/100)
            BigDecimal lossRateFactor = BigDecimal.ONE.add(
                    lossRate.divide(BigDecimal.valueOf(100), 12, RoundingMode.HALF_UP));

            // 计算预估数量
            BigDecimal estimatedQty = workPlanQty.multiply(demandQty).multiply(lossRateFactor)
                    .setScale(6, RoundingMode.HALF_UP);

            detailReqVO.setEstimatedQty(estimatedQty);
            // 领料数量等于预估数量
            detailReqVO.setPickingQty(estimatedQty);

            detailList.add(detailReqVO);
        }
        workPickingReqVO.setItemList(detailList);
        // 保存工单领料单
        workPickingService.workPickingAdd(workPickingReqVO);
    }

    /**
     * 自动下发工单直接领料出库单
     * 只能下发生产物料的BOM清单中正向领料的物料数据
     *
     * @param currentDO 生产工单
     */
//    private void autoIssueOutbound(ProdWorkDO currentDO) {
//        Long prodWorkId = currentDO.getProdWorkId();
//
//        // 查询生产工单BOM清单中正向领料的物料
//        List<ProdWorkMaterialBomDO> workMaterialBomList = prodWorkMaterialBomMapper.selectListByRelatedOrderId(prodWorkId);
//        if (CollUtilX.isEmpty(workMaterialBomList)) {
//            return;
//        }
//
//        // 筛选正向领料的物料
//        List<ProdWorkMaterialBomDO> forwardPickingMaterials = workMaterialBomList.stream()
//                .filter(bom -> bom.getPickingMethodDictId() == null ||
//                        bom.getPickingMethodDictId().equals(Integer.valueOf(Math.toIntExact(DictToEnum.FORWARD.key))))
//                .collect(Collectors.toList());
//
//        if (CollUtilX.isEmpty(forwardPickingMaterials)) {
//            return;
//        }
//
//        // 创建工单直接领料出库单
//        ErpOutboundAditReqVO erpOutboundReqVO = new ErpOutboundAditReqVO();
//        erpOutboundReqVO.setBizType(ErpOutboundBizTypeEnum.PROD_WORK_DIRECT_PICKING_OUTBOUND.getType());
//        erpOutboundReqVO.setRelatedOrderId(prodWorkId);
//        erpOutboundReqVO.setRelatedOrderCode(currentDO.getProdWorkCode());
//        erpOutboundReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
//        erpOutboundReqVO.setFormDt(LocalDateTime.now());
//
//        // 查询出库单类型
//        DictNewQueryReqVO dictQueryReqVO = new DictNewQueryReqVO();
//        dictQueryReqVO.setParentDictCode(CustomerDictEnum.OUTBOUND_TYPE.getDictCode());
//        dictQueryReqVO.setDictName("工单直接领料出库");
//        List<DictNewRespVO> dictList = erpBaseService.getDictList(dictQueryReqVO);
//        if (CollUtilX.isNotEmpty(dictList)) {
//            erpOutboundReqVO.setOutboundTypeDictId(dictList.get(0).getDictCode());
//        }
//
//        // 设置明细
//        List<ErpOutboundDetailAditReqVO> detailList = new ArrayList<>();
//        int rowNo = 1;
//
//        for (ProdWorkMaterialBomDO bom : forwardPickingMaterials) {
//            ErpOutboundDetailAditReqVO detailReqVO = new ErpOutboundDetailAditReqVO();
//            detailReqVO.setRowNo(rowNo++);
//            detailReqVO.setMaterialId(bom.getMaterialId());
//            detailReqVO.setMaterialCode(bom.getMaterialCode());
//
//            // 设置出库数量等于预估用量
//            detailReqVO.setOutboundQty(bom.getEstimatedQty());
//            // 获取默认仓库
//            // 这里需要根据实际情况设置仓库信息
//            detailList.add(detailReqVO);
//        }
//
//        erpOutboundReqVO.setDetailList(detailList);
//
//        // 保存工单直接领料出库单
//        erpOutboundService.erpOutboundAdd(erpOutboundReqVO);
//    }
}