package com.mongoso.mgs.module.purchase.handler.approve;

import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.finance.service.common.FinanceConnectService;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutreturndetail.vo.PurProcessOutReturnDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutreturndetail.vo.PurProcessOutReturnDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDetailDO;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutreturn.PurProcessOutReturnDO;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutreturndetail.PurProcessOutReturnDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutreturn.PurProcessOutReturnMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutreturndetail.PurProcessOutReturnDetailMapper;
import com.mongoso.mgs.module.purchase.service.purprocessoutreturndetail.PurProcessOutReturnDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.PURCHASE_PROCESS_OUT_NOT_EXIST;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.RELATED_ORDER_NOT_APPROVED;

/**
 * Created with IntelliJ IDEA.
 *
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurProcessOutReturnApproveHandler extends FlowApproveHandler<PurProcessOutReturnDO> {

    @Resource
    private PurchaseProcessOutMapper processOutMapper;

    @Resource
    private PurchaseProcessOutDetailMapper processOutDetailMapper;

    @Resource
    private PurProcessOutReturnMapper purProcessOutReturnMapper;

    @Resource
    private PurProcessOutReturnDetailMapper purProcessOutReturnDetailMapper;

    @Resource
    private PurProcessOutReturnDetailService purProcessOutReturnDetailService;

    @Resource
    private FinanceConnectService financeConnectService;

    @Resource
    private ErpBaseService erpBaseService;


    @Override
    protected ApproveCommonAttrs approvalAttributes(PurProcessOutReturnDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(PurProcessOutReturnDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(PurProcessOutReturnDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getProcessOutReturnId())
                .objCode(item.getProcessOutReturnCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(PurProcessOutReturnDO item,  BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        if(buttonType == DataButtonEnum.APPROVE.getKey()) {

            //关联的工序委外采购订单必须存在且是已审核状态
            PurchaseProcessOutDO processOutDO = processOutMapper.selectById(item.getPurchaseProcessOutId());
            if(ObjUtilX.isEmpty(processOutDO)){
                failItem.setCode(item.getProcessOutReturnCode());
                failItem.setReason(PURCHASE_PROCESS_OUT_NOT_EXIST.getMsg());
                return false;
            }
            if (!Objects.equals(processOutDO.getDataStatus(), DataStatusEnum.APPROVED.getKey())){
                failItem.setCode(item.getProcessOutReturnCode());
                failItem.setReason(RELATED_ORDER_NOT_APPROVED.getMsg());
                return false;
            }

            //查询退货明细
            List<PurProcessOutReturnDetailDO> returnDetailDOList = purProcessOutReturnDetailMapper.selectList(PurProcessOutReturnDetailDO::getProcessOutReturnId,item.getProcessOutReturnId());

            //查询工序委外采购明细
            List<PurchaseProcessOutDetailDO> purchaseProcessOutDetailList = processOutDetailMapper.selectList(PurchaseProcessOutDetailDO::getPurchaseProcessOutId, item.getPurchaseProcessOutId());
            Map<String, PurchaseProcessOutDetailDO> erpDetailMap = purchaseProcessOutDetailList.stream().collect(Collectors.toMap(detail -> detail.getMaterialId() + "-" + detail.getProcessId() + "-" + detail.getRowNo(), detailResp -> detailResp));

            //数量校验
            if (erpDetailMap.isEmpty()){
                failItem.setCode(item.getProcessOutReturnCode());
                failItem.setReason("物料退货数量已超出可退货数量,审核失败!");
                return false;
            }

            // 按MaterialId+ProcessId+RelatedRowNo累加ReturnQty
            Map<String, BigDecimal> materialReturnQtyMap = new HashMap<>();
            for (PurProcessOutReturnDetailDO returnDetailDO : returnDetailDOList) {
                String key = returnDetailDO.getMaterialId() + "-" + returnDetailDO.getProcessId() + "-" + returnDetailDO.getRelatedRowNo();
                BigDecimal currentQty = materialReturnQtyMap.getOrDefault(key, BigDecimal.ZERO);
                materialReturnQtyMap.put(key, currentQty.add(returnDetailDO.getReturnQty()));
            }

            // 校验累加后的退货数量是否超出可操作数量
            for (Map.Entry<String, BigDecimal> entry : materialReturnQtyMap.entrySet()) {
                String key = entry.getKey();
                BigDecimal totalReturnQty = entry.getValue();

                PurchaseProcessOutDetailDO processOutDetail = erpDetailMap.get(key);
                if (processOutDetail == null) {
                    failItem.setCode(item.getProcessOutReturnCode());
                    failItem.setReason("工序委外采购明细不存在,审核失败!");
                    return false;
                }

                BigDecimal operableQty = processOutDetail.getReceiptedQty().subtract(processOutDetail.getOperedQty());
                if (totalReturnQty.compareTo(operableQty) > 0) {
                    failItem.setCode(item.getProcessOutReturnCode());
                    failItem.setReason("物料退货数量已超出可操作数量,审核失败!");
                    return false;
                }
            }
        }

        if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //财务反审校验
            financeConnectService.checkOrder(item.getProcessOutReturnId());
        }

        return true;
    }



    @Override
    public Integer handleBusinessData(PurProcessOutReturnDO item,  BaseApproveRequest request) {
        Long id = item.getProcessOutReturnId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        PurProcessOutReturnDO processOutReturnDO = purProcessOutReturnMapper.selectById(id);
        if (processOutReturnDO == null){
            return 1;
        }

        //查询工序委外采购明细
        List<PurchaseProcessOutDetailDO> purchaseProcessOutDetailList = processOutDetailMapper.selectList(PurchaseProcessOutDetailDO::getPurchaseProcessOutId, processOutReturnDO.getPurchaseProcessOutId());

        //查询退货明细
        List<PurProcessOutReturnDetailDO> returnDetailList = purProcessOutReturnDetailMapper.selectList(PurProcessOutReturnDetailDO::getProcessOutReturnId,id);

        // 按MaterialId+ProcessId累加ReturnQty
        Map<String, BigDecimal> materialProcessQtyMap = new HashMap<>();
        for (PurProcessOutReturnDetailDO detailDO : returnDetailList) {
            String materialProcessKey = detailDO.getMaterialId() + "-" + detailDO.getProcessId();
            BigDecimal currentQty = materialProcessQtyMap.getOrDefault(materialProcessKey, BigDecimal.ZERO);
            materialProcessQtyMap.put(materialProcessKey, currentQty.add(detailDO.getReturnQty()));
        }

        boolean isMaterialFullOpered = true;

        // 以materialProcessQtyMap作为循环体，确保每个物料+工序的累加数量都能被处理
        for (Map.Entry<String, BigDecimal> entry : materialProcessQtyMap.entrySet()) {
            String materialProcessKey = entry.getKey();
            BigDecimal totalReturnQty = entry.getValue();

            // 解析key获取materialId和processId
            String[] keyParts = materialProcessKey.split("-");
            if (keyParts.length != 2) {
                continue;
            }

            Long materialId = Long.valueOf(keyParts[0]);
            Long processId = Long.valueOf(keyParts[1]);

            // 查找匹配的工序委外采购明细并直接修改
            for (PurchaseProcessOutDetailDO detail : purchaseProcessOutDetailList) {
                if (Objects.nonNull(detail)
                        && detail.getMaterialId().equals(materialId)
                        && detail.getProcessId().equals(processId)) {

                    //本次退货数量
                    BigDecimal returnQty = totalReturnQty;
                    if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                        returnQty = returnQty.negate();
                    }

                    //已收货数量
                    BigDecimal receiptedQty = detail.getReceiptedQty();
                    //已操作数量
                    BigDecimal operedQty = detail.getOperedQty();
                    //已退货数量
                    BigDecimal returnedQty = detail.getReturnedQty();

                    operedQty = operedQty.add(returnQty);
                    returnedQty = returnedQty.add(returnQty);

                    //是否操作完成
                    if (operedQty.compareTo(BigDecimal.ZERO)>=0 && operedQty.subtract(receiptedQty).compareTo(BigDecimal.ZERO)<0){
                        detail.setIsMaterialFullOpered(0);
                    }else {
                        detail.setIsMaterialFullOpered(1);
                    }

                    detail.setOperedQty(operedQty);
                    detail.setReturnedQty(returnedQty);
                    break; // 只更新第一个匹配的记录
                }
            }
        }

        //更新物料完成状态和数量
        if (CollUtilX.isNotEmpty(purchaseProcessOutDetailList)){
            processOutDetailMapper.updateBatch(purchaseProcessOutDetailList);

            for (PurchaseProcessOutDetailDO processOutDetailDO : purchaseProcessOutDetailList){
                if (processOutDetailDO.getIsMaterialFullOpered() == 0){
                    isMaterialFullOpered = false;
                    break;
                }
            }

            PurchaseProcessOutDO processOutDO = new PurchaseProcessOutDO();
            processOutDO.setPurchaseProcessOutId(processOutReturnDO.getPurchaseProcessOutId());
            if (buttonType == DataButtonEnum.APPROVE.getKey()) {
                //更新是否全部操作标识
                if (isMaterialFullOpered){
                    processOutDO.setIsFullOpered(1);
                }
            }

            if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                processOutDO.setIsFullOpered(0);
            }

            processOutMapper.updateById(processOutDO);
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        processOutReturnDO.setApprovedBy(loginUser.getFullUserName());
        processOutReturnDO.setApprovedDt(LocalDateTime.now());
        processOutReturnDO.setDataStatus(dataStatus);
        purProcessOutReturnMapper.updateById(processOutReturnDO);

        //数据进入财务
        processFinancialData(processOutReturnDO,buttonType);

        return 1;
    }

    private void processFinancialData(PurProcessOutReturnDO processOutReturnDO, Integer buttonType) {

        //组装主表
        PurchaseProcessOutDO processOutDO = processOutMapper.selectById(processOutReturnDO.getPurchaseProcessOutId());
        PurchaseOrderRespVO purchaseOrderRespVO = BeanUtilX.copy(processOutDO, PurchaseOrderRespVO::new);

        purchaseOrderRespVO.setSourceFormType(OrderTypeEnum.PURCHASE_RETURN_ORDER.type.shortValue());
        purchaseOrderRespVO.setInclTaxTotalAmt(processOutReturnDO.getInclTaxTotalAmt().negate());
        purchaseOrderRespVO.setFormDt(processOutReturnDO.getFormDt());
        purchaseOrderRespVO.setRelatedSupplierId(processOutReturnDO.getRelatedSupplierId());
        purchaseOrderRespVO.setCurrencyDictId(processOutReturnDO.getCurrencyDictId());

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.CURRENCY.getDictCode());
        //币种
        purchaseOrderRespVO.setCurrencyDictName(dictMap.get(processOutReturnDO.getCurrencyDictId()));

        purchaseOrderRespVO.setPurchaseOrderId(processOutReturnDO.getProcessOutReturnId());//来源单据id
        purchaseOrderRespVO.setPurchaseOrderCode(processOutReturnDO.getProcessOutReturnCode());//来源单据code
        purchaseOrderRespVO.setDirectorId(processOutReturnDO.getDirectorId());
        purchaseOrderRespVO.setDirectorOrgId(processOutReturnDO.getDirectorOrgId());

        //组装子表
        PurProcessOutReturnDetailQueryReqVO returnDetailQueryReqVO = new PurProcessOutReturnDetailQueryReqVO();
        returnDetailQueryReqVO.setProcessOutReturnId(processOutReturnDO.getProcessOutReturnId());
        List<PurProcessOutReturnDetailRespVO> returnDetailRespVOList = purProcessOutReturnDetailService.purProcessOutReturnDetailList(returnDetailQueryReqVO);

        List<PurchaseOrderDetailRespVO> detailList = new ArrayList<>();
        for (PurProcessOutReturnDetailRespVO returnDetailRespVO : returnDetailRespVOList) {
            PurchaseOrderDetailRespVO purchaseDetailRespVO = new PurchaseOrderDetailRespVO();
            purchaseDetailRespVO.setRowNo(returnDetailRespVO.getRowNo());
            purchaseDetailRespVO.setMaterialCode(returnDetailRespVO.getMaterialCode());
            purchaseDetailRespVO.setMaterialId(returnDetailRespVO.getMaterialId());
            purchaseDetailRespVO.setMainUnitDictId(returnDetailRespVO.getMainUnitDictId());
            purchaseDetailRespVO.setInclTaxAmt(returnDetailRespVO.getInclTaxAmt().negate());
            purchaseDetailRespVO.setExclTaxAmt(returnDetailRespVO.getExclTaxAmt().negate());
            purchaseDetailRespVO.setPurchaseQty(returnDetailRespVO.getReturnQty().negate());
            purchaseDetailRespVO.setInclTaxUnitPrice(returnDetailRespVO.getInclTaxUnitPrice());
            purchaseDetailRespVO.setExclTaxUnitPrice(returnDetailRespVO.getExclTaxUnitPrice());
            purchaseDetailRespVO.setInvoiceTypeId(returnDetailRespVO.getInvoiceTypeId());
            purchaseDetailRespVO.setInvoiceTypeName(returnDetailRespVO.getInvoiceTypeName());
            purchaseDetailRespVO.setTaxRate(returnDetailRespVO.getTaxRate());
            purchaseDetailRespVO.setCalculatType(returnDetailRespVO.getCalculatType());
            purchaseDetailRespVO.setPurchaseOrderDetailId(returnDetailRespVO.getProcessOutReturnDetailId());//来源单据id
            detailList.add(purchaseDetailRespVO);
        }
        purchaseOrderRespVO.setDetailList(detailList);

        //源头单据id
        purchaseOrderRespVO.setOriginOrderId(processOutReturnDO.getPurchaseProcessOutId());

        financeConnectService.purchaseInsertFinance(purchaseOrderRespVO, buttonType);

    }
}
