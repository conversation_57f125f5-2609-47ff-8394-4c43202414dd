package com.mongoso.mgs.module.produce.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingDO;
import org.springframework.stereotype.Component;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:40
 * @description: 设备回调处理类
 */

@Component
public class WorkPickingFlowCallBackHandler extends FlowCallbackHandler<WorkPickingDO> {

    protected WorkPickingFlowCallBackHandler(FlowApproveHandler<WorkPickingDO> approveHandler) {
        super(approveHandler);
    }

}
