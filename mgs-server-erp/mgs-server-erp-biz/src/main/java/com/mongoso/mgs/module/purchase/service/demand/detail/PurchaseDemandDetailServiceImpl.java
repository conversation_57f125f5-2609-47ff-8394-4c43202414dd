package com.mongoso.mgs.module.purchase.service.demand.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.demand.detail.PurchaseDemandDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.detail.PurchaseOrderDetailMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.*;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购需求明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseDemandDetailServiceImpl implements PurchaseDemandDetailService {

    @Resource
    private PurchaseDemandDetailMapper demandDetailMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseDetailMapper;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ERPMaterialService erpMaterialService;

    @Override
    public Long purchaseDemandDetailAdd(PurchaseDemandDetailAditReqVO reqVO) {
        // 插入
        PurchaseDemandDetailDO demandDetail = BeanUtilX.copy(reqVO, PurchaseDemandDetailDO::new);
        demandDetailMapper.insert(demandDetail);
        // 返回
        return demandDetail.getPurchaseDemandDetailId();
    }

    @Override
    public Long purchaseDemandDetailEdit(PurchaseDemandDetailAditReqVO reqVO) {
        // 校验存在
        this.purchaseDemandDetailValidateExists(reqVO.getPurchaseDemandDetailId());
        // 更新
        PurchaseDemandDetailDO demandDetail = BeanUtilX.copy(reqVO, PurchaseDemandDetailDO::new);
        demandDetailMapper.updateById(demandDetail);
        // 返回
        return demandDetail.getPurchaseDemandDetailId();
    }

    @Override
    public void purchaseDemandDetailDel(Long purchaseDemandDetailId) {
        // 校验存在
        this.purchaseDemandDetailValidateExists(purchaseDemandDetailId);
        // 删除
        demandDetailMapper.deleteById(purchaseDemandDetailId);
    }

    private PurchaseDemandDetailDO purchaseDemandDetailValidateExists(Long purchaseDemandDetailId) {
        PurchaseDemandDetailDO demandDetail = demandDetailMapper.selectById(purchaseDemandDetailId);
        if (demandDetail == null) {
            // throw exception(DEMAND_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "采购需求明细不存在");
        }
        return demandDetail;
    }

    @Override
    public PurchaseDemandDetailRespVO purchaseDemandDetailDetail(Long purchaseDemandDetailId) {
        PurchaseDemandDetailDO data = demandDetailMapper.selectById(purchaseDemandDetailId);
        return BeanUtilX.copy(data, PurchaseDemandDetailRespVO::new);
    }

    @Override
    public List<PurchaseDemandDetailRespVO> purchaseDemandDetailList(PurchaseDemandDetailQueryReqVO reqVO) {
        List<PurchaseDemandDetailDO> data = demandDetailMapper.selectList(reqVO);
        List<PurchaseDemandDetailRespVO> respVOList = BeanUtilX.copy(data, PurchaseDemandDetailRespVO::new);
        this.batchFillVoProperties(respVOList);
        return respVOList;
    }

    @Override
    public List<PurchaseDemandDetailRespVO> demandDetailListForPurchase(PurchaseDemandDetailQueryReqVO reqVO) {
        List<PurchaseDemandDetailRespVO> detailRespVOS = purchaseDetailMapper.selectDemandDetailForPurchase(reqVO);
        this.batchFillVoProperties(detailRespVOS);
        return detailRespVOS;
    }

    @Override
    public PageResult<PurchaseDemandDetailRespVO> purchaseDemandDetailPage(PurchaseDemandDetailPageReqVO reqVO) {
        IPage<PurchaseDemandDetailRespVO> respVOIPage = demandDetailMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult pageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    /**
     * VO属性填充-批量处理
     *
     * @param purchaseDemandDetailRespList
     */
    private void batchFillVoProperties(List<PurchaseDemandDetailRespVO> purchaseDemandDetailRespList) {

        if (CollUtilX.isEmpty(purchaseDemandDetailRespList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.DEMAND_TYPE.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> materialIdList = new ArrayList<>();
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        for (PurchaseDemandDetailRespVO detailResp: purchaseDemandDetailRespList){
            materialIdList.add(detailResp.getMaterialId());
            directorIdList.add(detailResp.getDirectorId());
            directorOrgIdList.add(detailResp.getDirectorOrgId());
        }

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        for (PurchaseDemandDetailRespVO detailResp: purchaseDemandDetailRespList){
            // 采购需求单类型
            String demandTypeDictId = detailResp.getDemandTypeDictId();
            if(StrUtilX.isNotEmpty(demandTypeDictId)){
                demandTypeDictId = CustomerDictEnum.DEMAND_TYPE.getDictCode() + "-" + demandTypeDictId;
                detailResp.setDemandTypeDictName(dictMap.get(demandTypeDictId));
            }

            // 基本单位
            String mainUnitDictId = detailResp.getMainUnitDictId();
            if(StrUtilX.isNotEmpty(mainUnitDictId)) {
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                detailResp.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            // 审核状态
            if(detailResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + detailResp.getDataStatus();
                detailResp.setDataStatusDictName(dictMap.get(dataStatus));;
            }

            //责任部门
            detailResp.setDirectorOrgName(directorOrgMap.get(detailResp.getDirectorOrgId()));
            //责任人
            detailResp.setDirectorName(directorMap.get(detailResp.getDirectorId()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialRespVO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialRespVO!=null){
                detailResp.setMaterialCode(erpMaterialRespVO.getMaterialCode());
                detailResp.setMaterialName(erpMaterialRespVO.getMaterialName());
                detailResp.setMaterialCategoryDictId(erpMaterialRespVO.getMaterialCategoryDictId());
                detailResp.setMaterialCategoryDictName(erpMaterialRespVO.getMaterialCategoryDictName());
                detailResp.setSpecAttributeStr(erpMaterialRespVO.getSpecAttributeStr());
                detailResp.setSpecModel(erpMaterialRespVO.getSpecModel());
            }
        }
    }

}
