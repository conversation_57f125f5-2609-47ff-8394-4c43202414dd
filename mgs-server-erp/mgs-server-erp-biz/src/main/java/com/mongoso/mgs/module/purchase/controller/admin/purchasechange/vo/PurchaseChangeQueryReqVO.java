package com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购订单变更 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class PurchaseChangeQueryReqVO extends CommonParam{

    /** 采购订单变更单ID */
    private Long purchaseChangeId;
    private List<Long> purchaseChangeIdList;

    /** 采购订单变更单号 */
    private String purchaseChangeCode;

    /** 采购订单号 */
    private String purchaseOrderCode;

    /** 采购订单ID */
    private Long purchaseOrderId;

    /** 采购订单类型 */
    private String purchaseTypeDictId;

    /** 关联单据id */
    private Long relatedOrderId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 联系人 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 币种 */
    private String currencyDictId;

    /** 主体公司 */
    private String companyOrgId;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 付款条件 */
    private String paymentTermsDictId;

    /** 是否带料 */
    private Short isTakeMaterial;

    /** 是否带模具 */
    private Short isTakeMold;

    /** 订单总金额（不含税） */
    private BigDecimal exclTaxTotalAmt;

    /** 订单总金额（含税） */
    private BigDecimal inclTaxTotalAmt;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审批状态 */
    private Integer dataStatus;

    /** 初始采购变更单ID */
    private Long beginPurchaseChangeId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 采购订单业务类型 */
    private Short purchaseOrderBizType;

    /** 入库状态["未入库","部分入库","已完成"] */
    private Short inboundStatus;

    /** 采购收货流程配置 ["采购收货单","采购收货通知单"]*/
    private Short purchaseReceiptProcessConfig;

    /** 出库流程配置 */
    private Integer outProcessConfigDictId;

    /** 入库流程配置 */
    private Integer inProcessConfigDictId;
}
