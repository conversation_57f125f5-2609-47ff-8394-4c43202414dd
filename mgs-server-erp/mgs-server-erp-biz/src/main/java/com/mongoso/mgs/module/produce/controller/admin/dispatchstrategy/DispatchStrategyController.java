package com.mongoso.mgs.module.produce.controller.admin.dispatchstrategy;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.dispatchstrategy.vo.*;
import com.mongoso.mgs.module.produce.service.dispatchstrategy.DispatchStrategyService;

/**
 * 派工策略 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class DispatchStrategyController {

    @Resource
    private DispatchStrategyService dispatchStrategyService;

    @OperateLog("派工策略添加或编辑")
    @PostMapping("/dispatchStrategyAdit")
    @PreAuthorize("@ss.hasPermission('dispatchStrategy:adit')")
    public ResultX<Long> dispatchStrategyAdit(@Valid @RequestBody DispatchStrategyAditReqVO reqVO) {
        return success(reqVO.getDispatchStrategyId() == null
                            ? dispatchStrategyService.dispatchStrategyAdd(reqVO)
                            : dispatchStrategyService.dispatchStrategyEdit(reqVO));
    }

    @OperateLog("派工策略删除")
    @PostMapping("/dispatchStrategyDelete")
    @PreAuthorize("@ss.hasPermission('dispatchStrategy:delete')")
    public ResultX<Boolean> dispatchStrategyDelete(@Valid @RequestBody DispatchStrategyPrimaryReqVO reqVO) {
        dispatchStrategyService.dispatchStrategyDelete(reqVO.getDispatchStrategyId());
        return success(true);
    }

    @OperateLog("派工策略详情")
    @PostMapping("/dispatchStrategyDetail")
    @PreAuthorize("@ss.hasPermission('dispatchStrategy:query')")
    public ResultX<DispatchStrategyRespVO> dispatchStrategyDetail(@Valid @RequestBody DispatchStrategyPrimaryReqVO reqVO) {
        return success(dispatchStrategyService.dispatchStrategyDetail(reqVO.getDispatchStrategyId()));
    }

    @DataPermission
    @OperateLog("派工策略列表")
    @PostMapping("/dispatchStrategyList")
    @PreAuthorize("@ss.hasPermission('dispatchStrategy:query')")
    public ResultX<List<DispatchStrategyRespVO>> dispatchStrategyList(@Valid @RequestBody DispatchStrategyQueryReqVO reqVO) {
        return success(dispatchStrategyService.dispatchStrategyList(reqVO));
    }

    @DataPermission
    @OperateLog("派工策略分页")
    @PostMapping("/dispatchStrategyPage")
    @PreAuthorize("@ss.hasPermission('dispatchStrategy:query')")
    public ResultX<PageResult<DispatchStrategyRespVO>> dispatchStrategyPage(@Valid @RequestBody DispatchStrategyPageReqVO reqVO) {
        return success(dispatchStrategyService.dispatchStrategyPage(reqVO));
    }

    @OperateLog("派工策略删除")
    @PostMapping("/dispatchStrategyDelBatch")
    @PreAuthorize("@ss.hasPermission('mold:del')")
    public ResultX<BatchResult> dispatchStrategyDelBatch(@Valid @RequestBody IdReq reqVO) {
        return dispatchStrategyService.dispatchStrategyDelBatch(reqVO);
    }

    @OperateLog("派工策略审核")
    @PostMapping("/dispatchStrategyApprove")
    @PreAuthorize("@ss.hasPermission('dispatchStrategy:adit')")
    public ResultX<BatchResult> dispatchStrategyApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = dispatchStrategyService.dispatchStrategyApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("派工策略回调接口")
    @PostMapping("/dispatchStrategyFlowCallback")
    public ResultX<Object> dispatchStrategyFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(dispatchStrategyService.dispatchStrategyFlowCallback(reqVO));
    }

}
