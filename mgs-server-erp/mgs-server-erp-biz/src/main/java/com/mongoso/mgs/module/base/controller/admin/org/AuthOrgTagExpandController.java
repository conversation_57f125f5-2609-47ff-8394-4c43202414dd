package com.mongoso.mgs.module.base.controller.admin.org;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.controller.admin.org.vo.AuthOrgTagExpandAditReqVO;
import com.mongoso.mgs.module.base.controller.admin.org.vo.AuthOrgTagExpandPageVO;
import com.mongoso.mgs.module.base.controller.admin.org.vo.AuthOrgTagExpandReqVO;
import com.mongoso.mgs.module.base.controller.admin.org.vo.AuthOrgTagExpandRespVO;
import com.mongoso.mgs.module.base.service.org.AuthOrgTagExpandService;
import com.mongoso.mgs.module.produce.controller.admin.workcenter.vo.WorkCenterPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workcenter.vo.WorkCenterRespVO;
import com.mongoso.mgs.module.system.controller.admin.org.vo.org.AuthOrgTreeReqVO;
import com.mongoso.mgs.module.system.controller.admin.org.vo.org.AuthOrgTreeRespVO;
import com.mongoso.mgs.module.system.service.org.AuthOrgService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 组织架构 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system")
@Validated
public class AuthOrgTagExpandController {

    @Resource
    private AuthOrgTagExpandService orgTagExpandService;

    @OperateLog("设置标签")
    @PostMapping("/authOrgTagAdit")
    @PreAuthorize("@ss.hasPermission('authOrgTag:adit')")
    public ResultX<Boolean> authOrgTagAdit(@Valid @RequestBody AuthOrgTagExpandAditReqVO reqVO) {
        orgTagExpandService.authOrgTagAdit(reqVO);

        return success(true);
    }

    @OperateLog("设置标签分页")
    @PostMapping("/authOrgTagPage")
    @PreAuthorize("@ss.hasPermission('workCenter:query')")
    public ResultX<PageResult<AuthOrgTagExpandRespVO>> authOrgTagPage(@Valid @RequestBody AuthOrgTagExpandPageVO reqVO) {
        return success(orgTagExpandService.authOrgTagPage(reqVO));
    }



}
