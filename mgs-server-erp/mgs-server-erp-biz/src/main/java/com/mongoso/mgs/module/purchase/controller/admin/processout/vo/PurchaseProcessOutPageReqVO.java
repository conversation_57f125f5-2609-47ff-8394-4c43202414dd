package com.mongoso.mgs.module.purchase.controller.admin.processout.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 工序委外采购订单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseProcessOutPageReqVO extends PageParam {

    /** 工序委外采购订单号 */
    private String purchaseProcessOutCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 联系人 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 币种 */
    private String currencyDictId;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 付款条件 */
    private String paymentTermsDictId;

    /** 票据类型ID */
    private Long invoiceTypeId;

    /** 主体公司 */
    private String companyOrgId;

    /** 订单总金额（不含税） */
    private BigDecimal exclTaxTotalAmt;

    /** 订单总金额（含税） */
    private BigDecimal inclTaxTotalAmt;

    /** 备注 */
    private String remark;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 强制关闭 */
    private Integer isForceClose;

    /** 是否全部收货 */
    private Integer isFullReceipted;

    /** 是否全部操作 */
    private Integer isFullOpered;

    /** 单据状态 */
    private List<Integer> formStatusList;
}
