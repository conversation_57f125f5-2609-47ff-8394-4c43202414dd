package com.mongoso.mgs.module.purchase.service.purchaseexchange;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.purchaseexchange.PurchaseExchangeDetailDO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO;
import com.mongoso.mgs.module.sale.dal.db.saleexchangedetail.SaleExchangeDetailDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDetailDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDetailDO;
import jakarta.validation.Valid;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购换货单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseExchangeDetailService {

    /**
     * 创建采购换货单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseExchangeDetailAdd(@Valid PurchaseExchangeDetailAditReqVO reqVO);

    /**
     * 更新采购换货单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseExchangeDetailEdit(@Valid PurchaseExchangeDetailAditReqVO reqVO);

    /**
     * 删除采购换货单明细
     *
     * @param purchaseExchangeDetailId 编号
     */
    void purchaseExchangeDetailDel(Long purchaseExchangeDetailId);

    /**
     * 获得采购换货单明细信息
     *
     * @param purchaseExchangeDetailId 编号
     * @return 采购换货单明细信息
     */
    PurchaseExchangeDetailRespVO purchaseExchangeDetailDetail(Long purchaseExchangeDetailId);

    /**
     * 获得采购换货单明细列表
     *
     * @param reqVO 查询条件
     * @return 采购换货单明细列表
     */
    List<PurchaseExchangeDetailRespVO> purchaseExchangeDetailList(@Valid PurchaseExchangeDetailQueryReqVO reqVO);

    /**
     * 获得采购换货单明细分页
     *
     * @param reqVO 查询条件
     * @return 采购换货单明细分页
     */
    PageResult<PurchaseExchangeDetailRespVO> purchaseExchangeDetailPage(@Valid PurchaseExchangeDetailPageReqVO reqVO);

    /**
     * 更新采购换货单明细已入库数量(入库审批使用)
     *
     * @param purchaseExchangeDetailId 采购换货单ID
     * @param inboundQty 入库数量
     * @return
     */
    void updateInboundedQtyByInbound(Long purchaseExchangeDetailId, BigDecimal inboundQty);

    /**
     * 更新采购换货单明细已出库数量(出库审批使用)
     *
     * @param purchaseExchangeDetailId 采购换货单ID
     * @param inboundQty 入库数量
     * @return
     */
    void updateOutboundedQtyByInbound(Long purchaseExchangeDetailId, BigDecimal inboundQty);

    public void handleInboundExchange(List<ErpInboundDetailDO> inboundDetailList, Long exchangeOrderId, Integer buttonType);

    public void handleOutboundExchange(List<ErpOutboundDetailDO> outboundDetailList, Long exchangeOrderId, Integer buttonType);

    /**
     * 获得采购换货被引用明细列表
     *
     * @param reqVO 查询条件
     * @return 采购换货明细列表
     */
    List<PurchaseExchangeDetailRespVO> purchaseExchangeDetailQuotedList(@Valid PurchaseExchangeDetailQueryReqVO reqVO);
}
