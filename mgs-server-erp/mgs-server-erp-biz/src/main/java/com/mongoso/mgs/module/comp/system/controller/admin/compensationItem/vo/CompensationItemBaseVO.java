package com.mongoso.mgs.module.comp.system.controller.admin.compensationItem.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 薪酬项 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CompensationItemBaseVO implements Serializable {

    /** 项目主键ID */
    private Long itemId;

    /** 项目类型 */
    @NotNull(message = "项目类型不能为空")
    private Integer itemType;

    private Integer type;

    /** 项目编码 */
    //@NotEmpty(message = "项目编码不能为空")
    private String itemCode;

    /** 项目名称 */
    @NotEmpty(message = "项目名称不能为空")
    private String itemName;

    /** 项目描述 */
//    @NotEmpty(message = "项目描述不能为空")
    private String itemDesc;

    /** 数值类型 ["加项", "减项"] */
    //@NotNull(message = "数值类型 ['加项', '减项']不能为空")
    private Integer valueType;

    /** 计税类型 ["计税", "不计税"] */
    //@NotNull(message = "计税类型 ['计税', '不计税']不能为空")
    private Integer taxType;

    /** 是否为默认 ["否", "是"] */
    //@NotNull(message = "是否为默认 ['否', '是']不能为空")
    private Integer isDefault;

    /** 排序 */
    //@NotNull(message = "排序不能为空")
    private Integer itemSort;

}
