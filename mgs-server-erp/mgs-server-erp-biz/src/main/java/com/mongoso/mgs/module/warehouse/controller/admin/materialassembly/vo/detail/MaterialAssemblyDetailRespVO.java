package com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.detail;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 组装拆卸单明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialAssemblyDetailRespVO extends MaterialAssemblyDetailBaseVO {

    /** 组装拆卸单名称 */
    private String assemblyName;

    /** 组装拆卸单类型 */
    private String assemblyTypeDictId;

    /** 组装拆卸单类型名称 */
    private String assemblyTypeDictName;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 组装物料ID */
    private Long assemblyMaterialId;

    /** 组装物料编码 */
    private String assemblyMaterialCode;

    /** 物料名称*/
    private String assemblyMaterialName;

    /** 物料类别ID */
    private String assemblyMaterialCategoryDictId;

    /** 物料类别 */
    private String assemblyMaterialCategoryDictName;

    /** 组装物料基本单位ID */
    private String assemblyMainUnitDictId;

    /** 基本单位字典名称 */
    private String assemblyMainUnitDictName;

    /** 规格型号 */
    private String assemblySpecModel;

    /** 规格属性 */
    private String assemblySpecAttributeStr;

    /** 物料BOM_ID */
    private Long materialBomId;

    /** 物料BOM版本 */
    private Integer materialBomVersion;

    /** 组装拆卸仓库ID */
    private String assemblyWarehouseOrgId;

    /** 组装拆卸仓库名称 */
    private String assemblyWarehouseOrgName;

    /** 组装拆卸数量 */
    private BigDecimal assemblyQty;

    /** 责任人 */
    private Long directorId;

    /** 责任人姓名 */
    private String directorName;

    /** 责任部门组织ID */
    private String directorOrgId;

    /** 责任部门组织名称 */
    private String directorOrgName;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 出入库仓库名称 */
    private String warehouseOrgName;

    /** 物料编码 */
    private String materialName;

    /** 物料类别字典ID */
    private String materialCategoryDictId;

    /** 物料类别 */
    private String materialCategoryDictName;

    /** 基本单位字典名称 */
    private String mainUnitDictName;

    /** 规格设置 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 物料库存可用数量 */
    private BigDecimal availableQty;
}
