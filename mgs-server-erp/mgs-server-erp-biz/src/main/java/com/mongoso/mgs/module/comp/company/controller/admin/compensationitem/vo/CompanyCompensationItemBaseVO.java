package com.mongoso.mgs.module.comp.company.controller.admin.compensationitem.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 公司薪酬项配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CompanyCompensationItemBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 薪酬项目ID */
    @NotNull(message = "薪酬项目ID不能为空")
    private Long compensationItemId;

    /** 组织ID */
    @NotEmpty(message = "组织ID不能为空")
    private String orgId;

    /** 是否启用 ["未启用", "启用"] */
    @NotNull(message = "是否启用 ['未启用', '启用']不能为空")
    private Integer isEnable;

}
