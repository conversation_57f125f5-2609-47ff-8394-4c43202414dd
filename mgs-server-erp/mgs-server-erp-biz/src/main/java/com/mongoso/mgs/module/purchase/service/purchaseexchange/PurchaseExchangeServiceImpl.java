package com.mongoso.mgs.module.purchase.service.purchaseexchange;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.produce.controller.admin.prodworkmaterialbom.vo.ProdWorkMaterialBomQuotedRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.PurchaseExchangeAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.PurchaseExchangePageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.PurchaseExchangeQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.PurchaseExchangeRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.purchaseexchange.PurchaseExchangeDO;
import com.mongoso.mgs.module.purchase.dal.db.purchaseexchange.PurchaseExchangeDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchaseexchange.PurchaseExchangeDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchaseexchange.PurchaseExchangeMapper;
import com.mongoso.mgs.module.purchase.handler.approve.PurchaseExchangeApproveHandler;
import com.mongoso.mgs.module.purchase.handler.flowcallback.PurchaseExchangeFlowCallBackHandler;
import com.mongoso.mgs.module.sale.controller.admin.saleexchange.vo.SaleExchangeRespVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.saleexchange.SaleExchangeDO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购换货 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PurchaseExchangeServiceImpl implements PurchaseExchangeService {

    @Resource
    private PurchaseExchangeMapper purchaseExchangeMapper;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private PurchaseExchangeDetailMapper purchaseExchangeDetailMapper;

    @Resource
    private PurchaseExchangeDetailService purchaseExchangeDetailService;

    @Resource
    private StockBookService stockBookService;

    @Resource
    private PurchaseExchangeApproveHandler purchaseExchangeApproveHandler;

    @Resource
    private SeqService seqService;

    @Resource
    private PurchaseExchangeFlowCallBackHandler purchaseExchangeFlowCallBackHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseExchangeAdd(PurchaseExchangeAditReqVO reqVO) {

        //编号
        String code = seqService.getGenerateCode(reqVO.getPurchaseExchangeCode(), MenuEnum.PURCHASE_EXCHANGE_ORDER.menuId);

        // 插入
        PurchaseExchangeDO exchange = BeanUtilX.copy(reqVO, PurchaseExchangeDO::new);
        exchange.setPurchaseExchangeId(IDUtilX.getId());
        exchange.setPurchaseExchangeCode(code);
        exchange.setIsFullInbounded((short) 0);
        exchange.setIsFullOutbounded((short) 0);
        exchange.setDataStatus(0);
        purchaseExchangeMapper.insert(exchange);

        //新增明细
        List<PurchaseExchangeDetailDO> exchangeDetailDOList = this.fillDetailList(reqVO, exchange);
        purchaseExchangeDetailMapper.insertBatch(exchangeDetailDOList);

        // 返回
        return exchange.getPurchaseExchangeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseExchangeEdit(PurchaseExchangeAditReqVO reqVO) {
        // 校验存在
        this.purchaseExchangeValidateExists(reqVO.getPurchaseExchangeId());

        // 更新
        PurchaseExchangeDO exchange = BeanUtilX.copy(reqVO, PurchaseExchangeDO::new);
        List<PurchaseExchangeDetailDO> exchangeDetailDOList = this.fillDetailList(reqVO, exchange);
        purchaseExchangeMapper.updateById(exchange);

        //明细更新，先删后增
        purchaseExchangeDetailMapper.deleteByExchangeId(reqVO.getPurchaseExchangeId());
        purchaseExchangeDetailMapper.insertBatch(exchangeDetailDOList);

        // 返回
        return exchange.getPurchaseExchangeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseExchangeDel(Long purchaseExchangeId) {
        // 校验存在
        PurchaseExchangeDO purchaseExchangeDO = this.purchaseExchangeValidateExists(purchaseExchangeId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(purchaseExchangeDO.getDataStatus())){
            throw new BizException("5002", "采购换货未审批，不能删除");
        }

        // 删除
        purchaseExchangeMapper.deleteById(purchaseExchangeId);
        purchaseExchangeDetailMapper.deleteByExchangeId(purchaseExchangeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> purchaseExchangeDelBatch(IdReq reqVO) {
        //获取对象属性名
        String purchaseExchangeId = EntityUtilX.getPropertyName(PurchaseExchangeDO::getPurchaseExchangeId);
        String purchaseExchangeCode = EntityUtilX.getPropertyName(PurchaseExchangeDO::getPurchaseExchangeCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), PurchaseExchangeDO.class, PurchaseExchangeDetailDO.class,
                purchaseExchangeId, purchaseExchangeCode);
    }

    private PurchaseExchangeDO purchaseExchangeValidateExists(Long purchaseExchangeId) {
        PurchaseExchangeDO exchange = purchaseExchangeMapper.selectById(purchaseExchangeId);
        if (exchange == null) {
            // throw exception(EXCHANGE_NOT_EXISTS);
            throw new BizException("5001", "采购换货不存在");
        }
        return exchange;
    }

    @Override
    public PurchaseExchangeRespVO purchaseExchangeDetail(Long purchaseExchangeId) {
        // 校验存在
        PurchaseExchangeDO purchaseExchangeDO = this.purchaseExchangeValidateExists(purchaseExchangeId);

        // 对象转换
        PurchaseExchangeRespVO respVO = BeanUtilX.copy(purchaseExchangeDO, PurchaseExchangeRespVO::new);

        //属性填充
        fillVoProperties(respVO);

        //查询明细列表
        PurchaseExchangeDetailQueryReqVO detailQueryReqVO = new PurchaseExchangeDetailQueryReqVO();
        detailQueryReqVO.setPurchaseExchangeId(purchaseExchangeId);
        List<PurchaseExchangeDetailRespVO> detailList = purchaseExchangeDetailService.purchaseExchangeDetailList(detailQueryReqVO);
        respVO.setDetailList(detailList);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(purchaseExchangeId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private PurchaseExchangeRespVO fillVoProperties(PurchaseExchangeRespVO respVO) {
        List<PurchaseExchangeRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        List<PurchaseExchangeRespVO> erpRespVOList = batchFillVoProperties(respVOList);
        return erpRespVOList.get(0);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private List<PurchaseExchangeRespVO> batchFillVoProperties(List<PurchaseExchangeRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return respVOList;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for(PurchaseExchangeRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            directorOrgIdList.add(item.getDirectorOrgId());
            supplierIdList.add(item.getRelatedSupplierId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for(PurchaseExchangeRespVO item : respVOList) {

            // 采购订单类型
            if(StrUtilX.isNotEmpty(item.getPurchaseTypeDictId())){
                String purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + item.getPurchaseTypeDictId();
                item.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            //关联供应商
            if(item.getRelatedSupplierId() != null){
                item.setRelatedSupplierName(supplierNameMap.get(item.getRelatedSupplierId()));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if (item.getDirectorId() != null) {
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
        return respVOList;
    }

    @Override
    public List<PurchaseExchangeRespVO> purchaseExchangeList(PurchaseExchangeQueryReqVO reqVO) {
        List<PurchaseExchangeDO> data = purchaseExchangeMapper.selectList(reqVO);
        return BeanUtilX.copy(data, PurchaseExchangeRespVO::new);
    }

    @Override
    public PageResult<PurchaseExchangeRespVO> purchaseExchangePage(PurchaseExchangePageReqVO reqVO) {
        PageResult<PurchaseExchangeDO> data = purchaseExchangeMapper.selectPage(reqVO);
        PageResult<PurchaseExchangeRespVO> pageResult = BeanUtilX.copy(data, PurchaseExchangeRespVO::new);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }

        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    @Override
    public BatchResult erpPurchaseExchangeApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PurchaseExchangeDO> list = purchaseExchangeMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PurchaseExchangeDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = purchaseExchangeApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getPurchaseExchangeCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }
        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        try {
            if (!flowConfigBO.getToFlow()){
                List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
                Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
                for (PurchaseExchangeDO item : list) {
                    String reason = reasonMap.get(item.getPurchaseExchangeCode());
                    if (StrUtilX.isEmpty(reason)){
                        // 正常
                        MessageInfoBO messageInfoBO = new MessageInfoBO();
                        messageInfoBO.setMenuId(curMenuId);
                        messageInfoBO.setObjId(item.getPurchaseExchangeId());
                        messageInfoBO.setObjCode(item.getPurchaseExchangeCode());
                        messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                        messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                        messageInfoBOS.add(messageInfoBO);
                    }else {
                        // 错误
                        MessageInfoBO messageInfoBO = new MessageInfoBO();
                        messageInfoBO.setMenuId(curMenuId);
                        messageInfoBO.setObjId(item.getPurchaseExchangeId());
                        messageInfoBO.setObjCode(item.getPurchaseExchangeCode());
                        messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                        messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                        messageInfoBO.setError(reason);
                        messageInfoBOS.add(messageInfoBO);
                    }
                }
                messageTemplateService.sendMessage(messageInfoBOS);
            }
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
        return batchResult;
    }

    @Override
    public Object erpPurchaseExchangeFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PurchaseExchangeDO item = this.purchaseExchangeValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return purchaseExchangeFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);

    }

    @Override
    public PurchaseExchangeRespVO purchaseExchangeQuotedOutDetail(Long purchaseExchangeId) {
        // 校验存在
        PurchaseExchangeDO purchaseExchangeDO = this.purchaseExchangeValidateExists(purchaseExchangeId);

        PurchaseExchangeDO data = purchaseExchangeMapper.selectById(purchaseExchangeId);
        PurchaseExchangeRespVO exchangeRespVO = BeanUtilX.copy(data, PurchaseExchangeRespVO::new);
        PurchaseExchangeDetailQueryReqVO reqVO = new PurchaseExchangeDetailQueryReqVO();
        reqVO.setPurchaseExchangeId(purchaseExchangeId);
        reqVO.setIsMaterialFullOutbounded((short) 0);
        List<PurchaseExchangeDetailRespVO> detailDOList = purchaseExchangeDetailService.purchaseExchangeDetailQuotedList(reqVO);
        // 物料填充
        exchangeRespVO.setDetailList(detailDOList);

        //VO属性填充
        fillVoProperties(exchangeRespVO);

        // 可操作数量处理
        for(PurchaseExchangeDetailRespVO detailRespVO : exchangeRespVO.getDetailList()){

            //计算可用数量
            BigDecimal availableQty = stockBookService.getAvailableQty(exchangeRespVO.getPurchaseExchangeId(), detailRespVO.getMaterialId(),
                    detailRespVO.getWarehouseOrgId(), detailRespVO.getStockQty(), detailRespVO.getLockedQty());
            detailRespVO.setStockableQty(availableQty);

            //可操作数量处理
            if(detailRespVO.getStockableQty() == null) {
                detailRespVO.setOperableQty(BigDecimal.ZERO);
            }else if(detailRespVO.getOutboundableQty().compareTo(detailRespVO.getStockableQty()) <= 0){
                detailRespVO.setOperableQty(detailRespVO.getOutboundableQty());
            }else {
                detailRespVO.setOperableQty(detailRespVO.getStockableQty());
            }
        }

        //处理可用数量小于等于0的数据
        List<PurchaseExchangeDetailRespVO> finalList = new ArrayList<>();
        Map<Long, List<PurchaseExchangeDetailRespVO>> materialIdMap = exchangeRespVO.getDetailList().stream()
                .collect(Collectors.groupingBy(PurchaseExchangeDetailRespVO::getMaterialId));
        for (Map.Entry<Long, List<PurchaseExchangeDetailRespVO>> entry : materialIdMap.entrySet()) {
            List<PurchaseExchangeDetailRespVO> detailRespVOList = entry.getValue();
            Integer flag = detailRespVOList.size();
            for (PurchaseExchangeDetailRespVO detailRespVO : detailRespVOList) {
                if (detailRespVO.getStockableQty() != null && detailRespVO.getStockableQty().compareTo(BigDecimal.ZERO) > 0) {
                    finalList.add(detailRespVO);
                } else {
                    flag--;
                }
            }
            if (flag == 0){
                PurchaseExchangeDetailRespVO detailRespVO = detailRespVOList.get(0);
                detailRespVO.setWarehouseOrgId(null);
                detailRespVO.setWarehouseOrgName(null);
                detailRespVO.setOperableQty(null);
                detailRespVO.setStockableQty(null);
                detailRespVO.setStockQty(null);
                finalList.add(detailRespVO);
            }
        }

        finalList.sort(Comparator.comparing(PurchaseExchangeDetailRespVO::getRowNo));
        exchangeRespVO.setDetailList(finalList);
        return exchangeRespVO;
    }

    @Override
    public PurchaseExchangeRespVO purchaseExchangeQuotedDetail(Long purchaseExchangeId) {
        // 校验存在
        PurchaseExchangeDO purchaseExchangeDO = this.purchaseExchangeValidateExists(purchaseExchangeId);

        PurchaseExchangeDO data = purchaseExchangeMapper.selectById(purchaseExchangeId);
        PurchaseExchangeRespVO exchangeRespVO = BeanUtilX.copy(data, PurchaseExchangeRespVO::new);
        PurchaseExchangeDetailQueryReqVO reqVO = new PurchaseExchangeDetailQueryReqVO();
        reqVO.setPurchaseExchangeId(purchaseExchangeId);
        reqVO.setIsMaterialFullInbounded((short) 0);
        List<PurchaseExchangeDetailRespVO> detailDOList = purchaseExchangeDetailService.purchaseExchangeDetailList(reqVO);
        if (CollUtilX.isNotEmpty(detailDOList)){
            // 物料填充
            exchangeRespVO.setDetailList(detailDOList);
        }

        //VO属性填充
        fillVoProperties(exchangeRespVO);
        return exchangeRespVO;
    }

    private List<PurchaseExchangeDetailDO> fillDetailList(PurchaseExchangeAditReqVO reqVO, PurchaseExchangeDO exchange) {
        List<PurchaseExchangeDetailDO> purchaseReturnDetailList = new ArrayList<>();
        List<PurchaseExchangeDetailAditReqVO> detailList = reqVO.getDetailList();

        // 校验相同物料编码的可操作数量合并
        Map<String, BigDecimal> materialCodeReturnQtyMap = new HashMap<>();
        Map<String, BigDecimal> materialCodeOperableQtyMap = new HashMap<>();

        // 第一步：收集每个物料编码的退货数量和可操作数量
        for (PurchaseExchangeDetailAditReqVO detail : detailList) {
            String materialCode = detail.getMaterialCode();
            BigDecimal exchangeQty = detail.getExchangeQty();
            BigDecimal operableQty = detail.getCompleteableQty() != null ? detail.getCompleteableQty() : exchangeQty;

            // 累加相同物料编码的退货数量
            materialCodeReturnQtyMap.merge(materialCode, exchangeQty, BigDecimal::add);

            // 记录相同物料编码的可操作数量（取第一次遇到的值）
            materialCodeOperableQtyMap.putIfAbsent(materialCode, operableQty);
        }

        // 第二步：校验每个物料编码的退货数量是否超过可操作数量
        for (Map.Entry<String, BigDecimal> entry : materialCodeReturnQtyMap.entrySet()) {
            String materialCode = entry.getKey();
            BigDecimal totalReturnQty = entry.getValue();
            BigDecimal operableQty = materialCodeOperableQtyMap.get(materialCode);

            if (operableQty != null && totalReturnQty.compareTo(operableQty) > 0) {
                throw new BizException("5002", "明细存在物料换货数量超过可换货数量，请重新编辑！");
            }
        }


        for (PurchaseExchangeDetailAditReqVO detail : detailList){
            PurchaseExchangeDetailDO purchaseReturnDetailDO = BeanUtilX.copy(detail, PurchaseExchangeDetailDO::new);

            purchaseReturnDetailDO.setPurchaseExchangeId(exchange.getPurchaseExchangeId());
            purchaseReturnDetailDO.setPurchaseExchangeCode(exchange.getPurchaseExchangeCode());

            //初始化可出库数量
            purchaseReturnDetailDO.setOutboundableQty(detail.getExchangeQty());

            //初始化可入库数量
            purchaseReturnDetailDO.setInboundableQty(detail.getExchangeQty());

            purchaseReturnDetailDO.setInboundedQty(BigDecimal.ZERO);
            purchaseReturnDetailDO.setOutboundedQty(BigDecimal.ZERO);
            purchaseReturnDetailDO.setIsMaterialFullInbounded((short) 0);
            purchaseReturnDetailDO.setIsMaterialFullOutbounded((short) 0);

            purchaseReturnDetailList.add(purchaseReturnDetailDO);
        }

        return purchaseReturnDetailList;
    }

}
