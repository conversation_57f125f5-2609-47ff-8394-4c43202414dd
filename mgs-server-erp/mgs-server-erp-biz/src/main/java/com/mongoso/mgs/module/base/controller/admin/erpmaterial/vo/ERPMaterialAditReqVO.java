package com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo;

import com.mongoso.mgs.module.base.service.erpmaterial.bo.MaterialSupplierBO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 物料 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ERPMaterialAditReqVO extends ERPMaterialBaseVO {


    /** 物料 */
    private List<MaterialSupplierBO> materialSupplierList;

}
