package com.mongoso.mgs.module.finance.service.invoice.invoicedetaillist;

import java.util.*;

import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListRespVO;
import jakarta.validation.*;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 实开发票明细集合 Service 接口
 *
 * <AUTHOR>
 */
public interface InvoiceDetailListService {

    /**
     * 创建实开发票明细集合
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long invoiceDetailListAdd(@Valid InvoiceDetailListAditReqVO reqVO);

    /**
     * 更新实开发票明细集合
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long invoiceDetailListEdit(@Valid InvoiceDetailListAditReqVO reqVO);

    /**
     * 删除实开发票明细集合
     *
     * @param invoiceDetailListId 编号
     */
    void invoiceDetailListDel(Long invoiceDetailListId);

    /**
     * 获得实开发票明细集合信息
     *
     * @param invoiceDetailListId 编号
     * @return 实开发票明细集合信息
     */
    InvoiceDetailListRespVO invoiceDetailListDetail(Long invoiceDetailListId);

    /**
     * 获得实开发票明细集合列表
     *
     * @param reqVO 查询条件
     * @return 实开发票明细集合列表
     */
    List<InvoiceDetailListRespVO> invoiceDetailListList(@Valid InvoiceDetailListQueryReqVO reqVO);

    /**
     * 获得实开发票明细集合分页
     *
     * @param reqVO 查询条件
     * @return 实开发票明细集合分页
     */
    PageResult<InvoiceDetailListRespVO> invoiceDetailListPage(@Valid InvoiceDetailListPageReqVO reqVO);

}
