package com.mongoso.mgs.module.produce.dal.mysql.materialanalysistotal;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.BomDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.KcDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物料分析数量汇总 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BomMapper extends BaseMapperX<BomDO> {


    default List<BomDO> selectListByEnable(){
        return selectList(LambdaQueryWrapperX.<BomDO>lambdaQueryX()
                .eq(BomDO::getEnable, 1));
    }

}