package com.mongoso.mgs.module.comp.payroll.controller.admin.payrollpayment;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollpayment.vo.*;
import com.mongoso.mgs.module.comp.payroll.service.payrollpayment.PayrollPaymentService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;

/**
 * 工资付款单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/compensation")
@Validated
public class PayrollPaymentController {

    @Resource
    private PayrollPaymentService payrollPaymentService;

    @OperateLog("工资付款单添加或编辑")
    @PostMapping("/payrollPaymentAdit")
    @PreAuthorize("@ss.hasPermission('payrollPayment:adit')")
    public ResultX<Long> payrollPaymentAdit(@Valid @RequestBody PayrollPaymentAditReqVO reqVO) {
        return success(reqVO.getPayrollPaymentId() == null
                            ? payrollPaymentService.payrollPaymentAdd(reqVO)
                            : payrollPaymentService.payrollPaymentEdit(reqVO));
    }

    @OperateLog("工资付款单删除")
    @PostMapping("/payrollPaymentDel")
    @PreAuthorize("@ss.hasPermission('payrollPayment:delete')")
    public ResultX<Boolean> payrollPaymentDel(@Valid @RequestBody PayrollPaymentPrimaryReqVO reqVO) {
        payrollPaymentService.payrollPaymentDel(reqVO.getPayrollPaymentId());
        return success(true);
    }

    @OperateLog("工资付款单批量删除")
    @PostMapping("/payrollPaymentDelBatch")
    @PreAuthorize("@ss.hasPermission('payrollPayment:delete')")
    public ResultX<BatchResult> payrollPaymentDelBatch(@Valid @RequestBody IdReq reqVO) {
        return payrollPaymentService.payrollPaymentDelBatch(reqVO);
    }

    @OperateLog("工资付款单详情")
    @PostMapping("/payrollPaymentDetail")
    @PreAuthorize("@ss.hasPermission('payrollPayment:query')")
    public ResultX<PayrollPaymentRespVO> payrollPaymentDetail(@Valid @RequestBody PayrollPaymentPrimaryReqVO reqVO) {
        return success(payrollPaymentService.payrollPaymentDetail(reqVO.getPayrollPaymentId()));
    }

    @OperateLog("工资付款单列表")
    @PostMapping("/payrollPaymentList")
    @PreAuthorize("@ss.hasPermission('payrollPayment:query')")
    @DataPermission
    public ResultX<List<PayrollPaymentRespVO>> payrollPaymentList(@Valid @RequestBody PayrollPaymentQueryReqVO reqVO) {
        return success(payrollPaymentService.payrollPaymentList(reqVO));
    }

    @OperateLog("工资付款单分页")
    @PostMapping("/payrollPaymentPage")
    @PreAuthorize("@ss.hasPermission('payrollPayment:query')")
    @DataPermission
    public ResultX<PageResult<PayrollPaymentRespVO>> payrollPaymentPage(@Valid @RequestBody PayrollPaymentPageReqVO reqVO) {
        return success(payrollPaymentService.payrollPaymentPage(reqVO));
    }

    @OperateLog("工资付款单审核")
    @PostMapping("/payrollPaymentApprove")
    @PreAuthorize("@ss.hasPermission('payrollPayment:adit')")
    public ResultX<BatchResult> payrollPaymentApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(payrollPaymentService.payrollPaymentApprove(reqVO));
    }

    @OperateLog("工资付款单审核回调接口")
    @PostMapping("/payrollPaymentFlowCallback")
    public ResultX<Object> payrollPaymentFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(payrollPaymentService.payrollPaymentFlowCallback(reqVO));
    }

}
