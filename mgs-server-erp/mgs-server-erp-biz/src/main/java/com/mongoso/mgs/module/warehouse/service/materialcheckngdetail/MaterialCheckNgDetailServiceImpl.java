package com.mongoso.mgs.module.warehouse.service.materialcheckngdetail;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheckngdetail.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.materialcheckngdetail.MaterialCheckNgDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialcheckngdetail.MaterialCheckNgDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.warehouse.enums.ErrorCodeConstants.*;


/**
 * 检验单不良品明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialCheckNgDetailServiceImpl implements MaterialCheckNgDetailService {

    @Resource
    private MaterialCheckNgDetailMapper materialCheckNgDetailMapper;

    @Override
    public Long materialCheckNgDetailAdd(MaterialCheckNgDetailAditReqVO reqVO) {
        // 插入
        MaterialCheckNgDetailDO materialCheckNgDetail = BeanUtilX.copy(reqVO, MaterialCheckNgDetailDO::new);
        materialCheckNgDetailMapper.insert(materialCheckNgDetail);
        // 返回
        return materialCheckNgDetail.getMaterialCheckNgDetailId();
    }

    @Override
    public Long materialCheckNgDetailEdit(MaterialCheckNgDetailAditReqVO reqVO) {
        // 校验存在
        this.materialCheckNgDetailValidateExists(reqVO.getMaterialCheckNgDetailId());
        // 更新
        MaterialCheckNgDetailDO materialCheckNgDetail = BeanUtilX.copy(reqVO, MaterialCheckNgDetailDO::new);
        materialCheckNgDetailMapper.updateById(materialCheckNgDetail);
        // 返回
        return materialCheckNgDetail.getMaterialCheckNgDetailId();
    }

    @Override
    public void materialCheckNgDetailDelete(Long materialCheckNgDetailId) {
        // 校验存在
        this.materialCheckNgDetailValidateExists(materialCheckNgDetailId);
        // 删除
        materialCheckNgDetailMapper.deleteById(materialCheckNgDetailId);
    }

    private MaterialCheckNgDetailDO materialCheckNgDetailValidateExists(Long materialCheckNgDetailId) {
        MaterialCheckNgDetailDO materialCheckNgDetail = materialCheckNgDetailMapper.selectById(materialCheckNgDetailId);
        if (materialCheckNgDetail == null) {
            // throw exception(MATERIAL_CHECK_NG_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "检验单不良品明细不存在");
        }
        return materialCheckNgDetail;
    }

    @Override
    public MaterialCheckNgDetailRespVO materialCheckNgDetailDetail(Long materialCheckNgDetailId) {
        MaterialCheckNgDetailDO data = materialCheckNgDetailMapper.selectById(materialCheckNgDetailId);
        return BeanUtilX.copy(data, MaterialCheckNgDetailRespVO::new);
    }

    @Override
    public List<MaterialCheckNgDetailRespVO> materialCheckNgDetailList(MaterialCheckNgDetailQueryReqVO reqVO) {
        List<MaterialCheckNgDetailDO> data = materialCheckNgDetailMapper.selectList(reqVO);
        return BeanUtilX.copy(data, MaterialCheckNgDetailRespVO::new);
    }

    @Override
    public PageResult<MaterialCheckNgDetailRespVO> materialCheckNgDetailPage(MaterialCheckNgDetailPageReqVO reqVO) {
        PageResult<MaterialCheckNgDetailDO> data = materialCheckNgDetailMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, MaterialCheckNgDetailRespVO::new);
    }

}
