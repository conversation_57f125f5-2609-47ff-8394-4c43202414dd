package com.mongoso.mgs.module.produce.dal.db.calculatepriceorder;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import org.springframework.format.annotation.DateTimeFormat;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 计价单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_calculate_price_order", autoResultMap = true)
//@KeySequence("u_calculate_price_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalculatePriceOrderDO extends OperateDO {

    /** 计价单id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long calculatePriceOrderId;

    /** 计价单编码 */
    private String calculatePriceOrderCode;

    /** 业务类型，0：生产计件，1：生产计时，2：返工计件，3：返工计时 */
    private Integer bizType;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 计时单价 */
    private BigDecimal calculateTimeAmt;

    /** 良品计件单价 */
    private BigDecimal okCalculateUnitAmt;

    /** 不良品计件单价 */
    private BigDecimal ngCalculateUnitAmt;

    /** 标准工时 */
    private BigDecimal standardWorkingHours;

    /** 备注 */
    private String remark;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;


    private String calculatePriceDimensionIds;

    private String pieceworkMethodDictId;


}
