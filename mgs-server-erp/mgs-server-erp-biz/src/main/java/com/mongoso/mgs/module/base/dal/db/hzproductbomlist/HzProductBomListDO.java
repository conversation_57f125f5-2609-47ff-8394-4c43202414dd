package com.mongoso.mgs.module.base.dal.db.hzproductbomlist;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 产品关联BOM清单 DO
 *
 * <AUTHOR>
 */
@TableName("platform.t_hz_product_bom_list")
@KeySequence("platform.t_hz_product_bom_list_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HzProductBomListDO extends OperateDO {

    /** 主键ID */
    @TableId
    private Long id;

    /** bomId */
    private Long bomId;

    /** 物料id */
    private Long materialId;

    /** 用量 */
    private BigDecimal usageQty;

    /** 损耗率 */
    private BigDecimal lossRate;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类型 */
    private String dictMaterialTypeId;

    /** 字典物料类型名称 */
    private String dictMaterialTypeName;

    /** 主单位 */
    private String mainUnit;

    /** 辅单位 leftVal:1, leftUnit:"盒", rightVal:5, rightUnit:"瓶" */
    private String auxUnit;

    /** 物料属性，0：基本物料，1：订单物料 */
    private Short mattr;

    /** 规格设置 */
    private String specSetting;

    /** 备注 */
    private String remark;

    /** 颜色 */
    private String color;


}
