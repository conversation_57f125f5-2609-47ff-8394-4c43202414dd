package com.mongoso.mgs.module.base.controller.admin.bankaccountinfo;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.controller.admin.bankaccountinfo.vo.*;
import com.mongoso.mgs.module.base.dal.db.bankaccountinfo.BankAccountInfoDO;
import com.mongoso.mgs.module.base.service.bankaccountinfo.BankAccountInfoService;

/**
 * 银行账户信息 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/base")
@Validated
public class BankAccountInfoController {

    @Resource
    private BankAccountInfoService bankAccountInfoService;

    @OperateLog("银行账户信息添加或编辑")
    @PostMapping("/bankAccountInfoAdit")
    @PreAuthorize("@ss.hasPermission('bankAccountInfo:adit')")
    public ResultX<Long> bankAccountInfoAdit(@Valid @RequestBody BankAccountInfoAditReqVO reqVO) {
        return success(reqVO.getBankAccountInfoId() == null
                            ? bankAccountInfoService.bankAccountInfoAdd(reqVO)
                            : bankAccountInfoService.bankAccountInfoEdit(reqVO));
    }

    @OperateLog("银行账户信息删除")
    @PostMapping("/bankAccountInfoDelete")
    @PreAuthorize("@ss.hasPermission('bankAccountInfo:delete')")
    public ResultX<Boolean> bankAccountInfoDelete(@Valid @RequestBody BankAccountInfoPrimaryReqVO reqVO) {
        bankAccountInfoService.bankAccountInfoDelete(reqVO.getBankAccountInfoId());
        return success(true);
    }

    @OperateLog("银行账户信息详情")
    @PostMapping("/bankAccountInfoDetail")
    @PreAuthorize("@ss.hasPermission('bankAccountInfo:query')")
    public ResultX<BankAccountInfoRespVO> bankAccountInfoDetail(@Valid @RequestBody BankAccountInfoPrimaryReqVO reqVO) {
        return success(bankAccountInfoService.bankAccountInfoDetail(reqVO.getBankAccountInfoId()));
    }

    @OperateLog("银行账户信息列表")
    @PostMapping("/bankAccountInfoList")
    @PreAuthorize("@ss.hasPermission('bankAccountInfo:query')")
    public ResultX<List<BankAccountInfoRespVO>> bankAccountInfoList(@Valid @RequestBody BankAccountInfoQueryReqVO reqVO) {
        return success(bankAccountInfoService.bankAccountInfoList(reqVO));
    }

    @OperateLog("银行账户信息分页")
    @PostMapping("/bankAccountInfoPage")
    @PreAuthorize("@ss.hasPermission('bankAccountInfo:query')")
    public ResultX<PageResult<BankAccountInfoRespVO>> bankAccountInfoPage(@Valid @RequestBody BankAccountInfoPageReqVO reqVO) {
        return success(bankAccountInfoService.bankAccountInfoPage(reqVO));
    }

}
