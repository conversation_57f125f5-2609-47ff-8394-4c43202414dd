package com.mongoso.mgs.module.produce.dal.db.employeeskillratio;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 技能分配比例 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_employee_skill_ratio", autoResultMap = true)
//@KeySequence("u_employee_skill_ratio_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeSkillRatioDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long skillRatioId;

    /** 工作中心主键ID */
    private Long workCenterId;

    /** 工作中心编码 */
    private String workCenterCode;

    /** 行号 */
    private Integer rowNo;

    /** 无技能分配比例 */
    private BigDecimal defalutRatio;

    /** 技能等级 */
    private String skillLevelDictId;

    /** 分配比例 */
    private BigDecimal skillRatio;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;


}
