package com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购需求明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseDemandDetailRespVO extends PurchaseDemandDetailBaseVO {

    private String purchaseDemandName;

    /** 需求订单类型 */
    private String demandTypeDictId;
    private String demandTypeDictName;

    private String relatedOrderCode;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    private BigDecimal planAbleDemandQty;

    private BigDecimal purchaseAbleQty;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核状态 */
    private String dataStatusDictName;

    private String approvedBy;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 基本单位ID */
    private String mainUnitDictName;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 标准订货价 */
    private BigDecimal purchaseStandardPrice;

    /** 规格设置 */
    private String specModel;

    private String specAttributeStr;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
