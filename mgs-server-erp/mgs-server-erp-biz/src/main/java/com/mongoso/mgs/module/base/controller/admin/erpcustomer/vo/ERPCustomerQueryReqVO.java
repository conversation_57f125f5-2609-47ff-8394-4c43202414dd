package com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.CommonParam;
import com.mongoso.mgs.framework.common.util.DateUtilX_backup;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;


/**
 * 客户 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ERPCustomerQueryReqVO extends CommonParam{

    /** 客户编号 */
    private String customerCode;
    /** 客户名 */
    private String customerName;
    /** 简称 */
    private String customerShortName;
    /** 客户类型字典ID */
    private String customerTypeDictId;
    /** 国家 */
    private String countryName;
    /** 开始时间 */
    private String startTime;
    /** 结束时间 */
    private String endTime;
    /** 审核状态 */
    private Integer dataStatus;

    /** 客户ID */
    private Long customerId;

    private List<Long> customerIdList;
    private List<Long> exclCustomerIdList;

    /** 基本信息 */
    private JSONObject baseInfo;

    /** 财务信息 */
    private JSONObject financeInfo;

    /** 联系人信息 */
    private List<JSONObject> contactList;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 版本号 */
    private Integer version;

    /** 客户行业字典ID */
    private String customerIndustryDictId;

    /** 公司网址 */
    private String companyUrl;

    /** 传真 */
    private String fax;

    /** 详细地址 */
    private String detailedAddress;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = DateUtilX_backup.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDt;

    /** 纳税人识别号 */
    private String companyTaxNo;

    /** 支付宝账号 */
    private String alipayAccount;

    /** 结算方式字典Id */
    private String settlementMethodDictId;

    /** 微信号 */
    private String wechatAccount;

    /** 收款条件字典Id */
    private String paymentTermsDictId;

    /** 账期 */
    private String billPeriod;

    /** 结算日期 */
    private Integer settlementDate;

    /** 开户银行 */
    private String bankName;

    /** 银行账号 */
    private String bankAccount;

}
