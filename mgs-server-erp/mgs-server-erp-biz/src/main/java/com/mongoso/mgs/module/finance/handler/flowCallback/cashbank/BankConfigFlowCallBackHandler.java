package com.mongoso.mgs.module.finance.handler.flowCallback.cashbank;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.finance.dal.db.cashbank.bankconfig.BankConfigDO;
import org.springframework.stereotype.Component;

/**
 * @author: AI Assistant
 * @date: 2024/12/19
 * @description: 实开发票回调处理类
 */

@Component
public class BankConfigFlowCallBackHandler extends FlowCallbackHandler<BankConfigDO> {

    protected BankConfigFlowCallBackHandler(FlowApproveHandler<BankConfigDO> approveHandler) {
        super(approveHandler);
    }
}
