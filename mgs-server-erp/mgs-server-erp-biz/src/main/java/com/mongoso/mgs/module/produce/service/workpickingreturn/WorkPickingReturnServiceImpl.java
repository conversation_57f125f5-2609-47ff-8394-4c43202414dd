package com.mongoso.mgs.module.produce.service.workpickingreturn;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.*;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.item.WorkPickingReturnItemPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.item.WorkPickingReturnItemQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.item.WorkPickingReturnItemRespVO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingMaterialTotalDO;
import com.mongoso.mgs.module.produce.dal.db.workpickingreturn.WorkPickingReturnDO;
import com.mongoso.mgs.module.produce.dal.db.workpickingreturn.WorkPickingReturnDetailDO;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMaterialTotalMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd.WorkPickingReturnDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd.WorkPickingReturnMapper;
import com.mongoso.mgs.module.produce.handler.approve.WorkPickingReturnApproveHandler;
import com.mongoso.mgs.module.produce.handler.flowCallback.WorkPickingReturnFlowCallBackHandler;
import com.mongoso.mgs.module.produce.service.BaseWorkPickingService;
import com.mongoso.mgs.module.produce.service.materialbom.bo.BomMaterialBO;
import com.mongoso.mgs.module.produce.service.materialbom.bo.MaterialBO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.detail.PurchaseOrderDetailMapper;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.stockunlock.StockUnlockDO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;
// import static com.mongoso.mgs.module.workpicking.enums.ErrorCodeConstants.*;


/**
 * 工单退料单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WorkPickingReturnServiceImpl implements WorkPickingReturnService {

    @Resource
    private WorkPickingReturnMapper workPickingReturnMapper;


    @Resource
    private WorkPickingReturnDetailMapper workPickingReturnDetailMapper;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    @Lazy
    private WorkPickingReturnApproveHandler approveHandler;

    @Resource
    private WorkPickingReturnFlowCallBackHandler flowCallBackHandler;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private WorkPickingMaterialTotalMapper workPickingMaterialTotalMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long workPickingReturnAdd(WorkPickingReturnAditReqVO reqVO) {

        List<ReturnItemReqVO> itemList = reqVO.getItemList();

        List<Long> materialIdList = itemList.stream().map(ReturnItemReqVO::getMaterialId).collect(Collectors.toList());
        Map<Long, MaterialBO> materialMap = erpBaseService.getDataStatusMaterialByMaterialId(materialIdList);

        // 生成单号
        String code = null;
        if (reqVO.getWorkPickingReturnBizType() == 0) {
            code = seqService.getGenerateCode(reqVO.getWorkPickingReturnCode(), MenuEnum.WORK_ORDER_RETURN_MATERIAL.menuId);
        }else if (reqVO.getWorkPickingReturnBizType() == 1) {
            code = seqService.getGenerateCode(reqVO.getWorkPickingReturnCode(), MenuEnum.OUTSOURCING_ORDER_RETURN_MATERIAL.menuId);
        }
        Long workPickingId = IDUtilX.getId();

        WorkPickingReturnDO newDO = BeanUtilX.copy(reqVO, WorkPickingReturnDO::new);
        newDO.setWorkPickingReturnId(workPickingId);
        newDO.setWorkPickingReturnCode(code);

        BigDecimal totalQty = BigDecimal.ZERO;
        List<WorkPickingReturnDetailDO> addList = new ArrayList<>();

        for (ReturnItemReqVO item : itemList) {

            Long childMaterialId = item.getMaterialId();
            String childMaterialCode = item.getMaterialCode();
            MaterialBO bomBO = materialMap.get(childMaterialId);
            if (bomBO == null) {
                throw exceptionMsg("数据问题，当前物料没有查到对应的bom子物料详情：" + childMaterialCode);
            }

            WorkPickingReturnDetailDO detailDO = new WorkPickingReturnDetailDO();
            detailDO.setWorkPickingReturnId(workPickingId);
            detailDO.setWorkPickingReturnCode(code);
            detailDO.setRowNo(item.getRowNo());
            detailDO.setWarehouseOrgId(item.getWarehouseOrgId());
            detailDO.setRemark(item.getRemark());
            detailDO.setQty(item.getQty());
            detailDO.setInboundableQty(item.getQty());

            detailDO.setMaterialId(bomBO.getMaterialId());
            detailDO.setMaterialCode(childMaterialCode);
            detailDO.setMaterialCategoryDictId(bomBO.getMaterialCategoryDictId());

            totalQty = totalQty.add(item.getQty());

            addList.add(detailDO);
        }

        //退料总数
        newDO.setTotalQty(totalQty);

        if (reqVO.getWorkPickingReturnBizType() == 0) {
            // 工单领料单，查工单数量
            ProdWorkDO prodWorkDO = prodWorkMapper.selectById(reqVO.getRelatedOrderId());
            if (prodWorkDO != null) {
                newDO.setQty(prodWorkDO.getWorkPlanTotalQty());
            }
        } else {
            // 采购订单，查询采购单数量
            PurchaseOrderDetailDO purchaseOrderDO = purchaseOrderDetailMapper.selectByMaterialId(reqVO.getRelatedOrderId(), reqVO.getMaterialId());
            if (purchaseOrderDO != null) {
                newDO.setQty(purchaseOrderDO.getPurchaseQty());
            }
        }

        workPickingReturnDetailMapper.insertBatch(addList);

        workPickingReturnMapper.insert(newDO);

        // 返回
        return workPickingId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long workPickingReturnEdit(WorkPickingReturnAditReqVO reqVO) {

        Long workPickingReturnId = reqVO.getWorkPickingReturnId();
        // 校验存在
//        this.workPickingReturnValidateExists(workPickingReturnId);

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.workPickingReturnValidateExists(workPickingReturnId), reqVO);

        WorkPickingReturnDO newDO = BeanUtilX.copy(reqVO, WorkPickingReturnDO::new);
        newDO.setWorkPickingReturnTypeDictId(null);
        newDO.setWorkPickingReturnCode(null);

        BigDecimal totalQty = BigDecimal.ZERO;

        List<ReturnItemReqVO> itemList = reqVO.getItemList();

        List<Long> materialIdList = itemList.stream().map(ReturnItemReqVO::getMaterialId).collect(Collectors.toList());
        Map<Long, MaterialBO> materialMap = erpBaseService.getDataStatusMaterialByMaterialId(materialIdList);

        List<WorkPickingReturnDetailDO> addList = new ArrayList<>();
        for (ReturnItemReqVO item : itemList) {
            Long childMaterialId = item.getMaterialId();
            String childMaterialCode = item.getMaterialCode();
            MaterialBO bomBO = materialMap.get(childMaterialId);
            if (bomBO == null) {
                throw exceptionMsg("数据问题，当前物料没有查到对应的bom子物料详情：" + childMaterialCode);
            }

            WorkPickingReturnDetailDO detailDO = new WorkPickingReturnDetailDO();
            detailDO.setWorkPickingReturnId(workPickingReturnId);
            detailDO.setRowNo(item.getRowNo());
            detailDO.setWarehouseOrgId(item.getWarehouseOrgId());
            detailDO.setRemark(item.getRemark());
            detailDO.setQty(item.getQty());
            detailDO.setInboundableQty(item.getQty());
            detailDO.setMaterialId(bomBO.getMaterialId());
            detailDO.setMaterialCode(bomBO.getMaterialCode());
            detailDO.setMaterialCategoryDictId(bomBO.getMaterialCategoryDictId());

            totalQty = totalQty.add(item.getQty());

            addList.add(detailDO);
        }

        //退料总数
        newDO.setTotalQty(totalQty);

        if (reqVO.getWorkPickingReturnBizType() == 0) {
            // 工单领料单，查工单数量
            ProdWorkDO prodWorkDO = prodWorkMapper.selectById(reqVO.getRelatedOrderId());
            if (prodWorkDO != null) {
                newDO.setQty(prodWorkDO.getWorkPlanTotalQty());
            }
        } else {
            // 采购订单，查询采购单数量
            PurchaseOrderDetailDO purchaseOrderDO = purchaseOrderDetailMapper.selectByMaterialId(reqVO.getRelatedOrderId(), reqVO.getMaterialId());
            if (purchaseOrderDO != null) {
                newDO.setQty(purchaseOrderDO.getPurchaseQty());
            }
        }

        // 先删除
        workPickingReturnDetailMapper.deleteByWorkPickingReturnId(workPickingReturnId);

        // 在新增
        workPickingReturnDetailMapper.insertBatch(addList);

        workPickingReturnMapper.updateById(newDO);

        // 返回
        return workPickingReturnId;
    }

    @Override
    public void workPickingReturnDel(Long workPickingReturnId) {
        // 校验存在
        this.workPickingReturnValidateExists(workPickingReturnId);
        // 删除
        workPickingReturnMapper.deleteById(workPickingReturnId);

        int a = workPickingReturnDetailMapper.deleteByWorkPickingReturnId(workPickingReturnId);
    }

    @Override
    public ResultX<BatchResult> workPickingReturnDelBatch(IdReq reqVO) {
        String id = EntityUtilX.getPropertyName(WorkPickingReturnDO::getWorkPickingReturnId);
        String code = EntityUtilX.getPropertyName(WorkPickingReturnDO::getWorkPickingReturnCode);
        ResultX<BatchResult> batchResultResultX = erpBaseService.batchDelete(reqVO.getIdList(), WorkPickingReturnDO.class, null, id, code);

        // 删除关联表
        int a = workPickingReturnDetailMapper.deleteByWorkPickingReturnId(reqVO.getIdList());

        return batchResultResultX;
    }

    private WorkPickingReturnDO workPickingReturnValidateExists(Long workPickingReturnId) {
        WorkPickingReturnDO workPickingReturn = workPickingReturnMapper.selectById(workPickingReturnId);
        if (workPickingReturn == null) {
            // throw exception(WORK_PICKING_RETURN_NOT_EXISTS);
            throw new BizException("5001", "工单退料单不存在");
        }
        return workPickingReturn;
    }

    @Override
    public WorkPickingReturnRespVO workPickingReturnDetail(Long workPickingReturnId) {
        WorkPickingReturnDO data = workPickingReturnValidateExists(workPickingReturnId);

        if (data == null) {
            return null;
        }
        WorkPickingReturnRespVO respVO = BeanUtilX.copy(data, WorkPickingReturnRespVO::new);

        // 查询明细
        List<WorkPickingReturnDetailDO> workPickingDetailDOList = workPickingReturnDetailMapper.selectListByWorkPickingReturnId(workPickingReturnId);
        List<String> collect1 = workPickingDetailDOList.stream().map(WorkPickingReturnDetailDO::getWarehouseOrgId).collect(Collectors.toList());

        List<String> orgIdList = new ArrayList<>();
        orgIdList.add(data.getDirectorOrgId());
        if (CollUtilX.isNotEmpty(collect1)){
            orgIdList.addAll(collect1);
        }
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(orgIdList);

        //查询负责人
        Map<Long, String> empNameMap = erpBaseService.getEmpNameByIdList(Arrays.asList(data.getDirectorId()));

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode());

        respVO.setDirectorOrgName(orgNameMap.get(data.getDirectorOrgId()));
        respVO.setDirectorName(empNameMap.get(data.getDirectorId()));

        respVO.setWorkPickingReturnTypeDictName(dictMap.get(data.getWorkPickingReturnTypeDictId()));

        BomMaterialBO bomMaterialBO = erpBaseService.getBomByMaterialId(data.getMaterialId());

        if (bomMaterialBO != null) {
            respVO.setVersion(bomMaterialBO.getVersion());
            respVO.setMaterialName(bomMaterialBO.getMaterialName());

            respVO.setMainUnitDictId(bomMaterialBO.getMainUnitDictId());
            respVO.setMainUnitDictName(bomMaterialBO.getMainUnitDictName());

            respVO.setMaterialSourceDictId(bomMaterialBO.getMaterialSourceDictId());
            respVO.setMaterialSourceDictName(bomMaterialBO.getMaterialSourceDictName());

            respVO.setMaterialCategoryDictId(bomMaterialBO.getMaterialCategoryDictId());
            respVO.setMaterialCategoryDictName(bomMaterialBO.getMaterialCategoryDictName());

            respVO.setSpecModel(bomMaterialBO.getSpecModel());
            respVO.setSpecAttributeStr(bomMaterialBO.getSpecAttributeStr());
        }

        List<WorkPickingMaterialTotalDO> list = workPickingMaterialTotalMapper.selectListByRelatedOrderId(data.getRelatedOrderId());
        Map<Long, WorkPickingMaterialTotalDO> workPickingMaterialTotalDOMap = list.stream().collect(Collectors.toMap(WorkPickingMaterialTotalDO::getMaterialId, Function.identity()));

        // 查询明细
        List<ReturnItemRespVO> returnItemRespVOList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(workPickingDetailDOList)) {
            List<Long> collect = workPickingDetailDOList.stream().map(WorkPickingReturnDetailDO::getMaterialId).collect(Collectors.toList());

            Map<Long, MaterialBO> childMap = erpBaseService.getMaterialByMaterialId(collect);
            for (WorkPickingReturnDetailDO item : workPickingDetailDOList) {
                WorkPickingMaterialTotalDO workPickingMaterialTotalDO = workPickingMaterialTotalDOMap.get(item.getMaterialId());

                if (workPickingMaterialTotalDO != null){
                    ReturnItemRespVO copy = BeanUtilX.copy(item, ReturnItemRespVO::new);
                    copy.setWarehouseOrgName(orgNameMap.get(item.getWarehouseOrgId()));

                    BigDecimal outboundedQty = workPickingMaterialTotalDO.getOutboundedQty();
                    BigDecimal returnedQty = workPickingMaterialTotalDO.getReturnedQty();

                    // 工单可退数量 = 工单已领数量 - 工单已退数量
                    BigDecimal subtract = outboundedQty.subtract(returnedQty);
                    copy.setReturnAbleQty(subtract);
                    copy.setQty(item.getQty());

                    MaterialBO materialBO = childMap.get(item.getMaterialId());
                    if (materialBO != null) {
                        copy.setMaterialName(materialBO.getMaterialName());
                        copy.setMainUnitDictId(materialBO.getMainUnitDictId());
                        copy.setMainUnitDictName(materialBO.getMainUnitDictName());

                        copy.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
                        copy.setMaterialSourceDictName(materialBO.getMaterialSourceDictName());

                        copy.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
                        copy.setMaterialCategoryDictName(materialBO.getMaterialCategoryDictName());

                        copy.setSpecModel(materialBO.getSpecModel());
                        copy.setSpecAttributeStr(materialBO.getSpecAttributeStr());

                        returnItemRespVOList.add(copy);
                    }
                }
            }
        }
        respVO.setItemList(returnItemRespVOList);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(workPickingReturnId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public WorkPickingReturnRespVO workPickingReturnQuotedDetail(Long workPickingReturnId) {
        WorkPickingReturnRespVO respVO = BeanUtilX.copy(workPickingReturnMapper.selectById(workPickingReturnId),
                WorkPickingReturnRespVO :: new);
        if (respVO == null) {
            return null;
        }

        //查询明细
        WorkPickingReturnItemQueryReqVO itemQueryReqVO = new WorkPickingReturnItemQueryReqVO();
        itemQueryReqVO.setWorkPickingReturnId(workPickingReturnId);
        itemQueryReqVO.setIsMaterialFullInbounded(0);
        List<WorkPickingReturnItemRespVO> itemRespVOList = workPickingReturnItemQuotedList(itemQueryReqVO);
        respVO.setDetailList(itemRespVOList);

        return respVO;
    }

    @Override
    public List<WorkPickingReturnRespVO> workPickingReturnList(WorkPickingReturnQueryReqVO reqVO) {
        List<WorkPickingReturnDO> data = workPickingReturnMapper.selectList(reqVO);
        return BeanUtilX.copy(data, WorkPickingReturnRespVO::new);
    }

    @Override
    public PageResult<WorkPickingReturnRespVO> workPickingReturnPage(WorkPickingReturnPageReqVO reqVO) {
        PageResult<WorkPickingReturnDO> data = workPickingReturnMapper.selectPage(reqVO);
        PageResult<WorkPickingReturnRespVO> pageResult = BeanUtilX.copy(data, WorkPickingReturnRespVO::new);

        //VO属性填充
        fillVoProperties(pageResult.getList());

        return pageResult;
    }

    @Override
    public BatchResult workPickingReturnApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<WorkPickingReturnDO> list = workPickingReturnMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (WorkPickingReturnDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = approveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getWorkPickingReturnCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (WorkPickingReturnDO item : list) {
                String reason = reasonMap.get(item.getWorkPickingReturnCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getWorkPickingReturnId());
                    messageInfoBO.setObjCode(item.getWorkPickingReturnCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getWorkPickingReturnId());
                    messageInfoBO.setObjCode(item.getWorkPickingReturnCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object workPickingReturnFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        WorkPickingReturnDO item = this.workPickingReturnValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return flowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public PageResult<WorkPickingReturnItemRespVO> workPickingReturnItemPage(WorkPickingReturnItemPageReqVO reqVO) {

        if (StrUtilX.isNotEmpty(reqVO.getMaterialName())){
            List<Long> materialIdList = erpBaseService.getMaterialIdByName(reqVO.getMaterialName());
            // 查不到直接返回空
            if (CollUtilX.isEmpty(materialIdList)){
                return PageResult.empty();
            }
            reqVO.setMaterialIdList(materialIdList);
        }

        IPage<WorkPickingReturnItemRespVO> page = workPickingReturnMapper.selectPage2(PageUtilX.buildParam(reqVO), reqVO);

        PageResult<WorkPickingReturnItemRespVO> pageResult = PageUtilX.buildResult(page);

        List<WorkPickingReturnItemRespVO> list = pageResult.getList();

        if (CollUtilX.isEmpty(list)) {
            return pageResult;
        }

        List<Long> materialIdList = list.stream().map(WorkPickingReturnItemRespVO::getMaterialId).collect(Collectors.toList());
        List<String> orgIdList = list.stream().map(WorkPickingReturnItemRespVO::getWarehouseOrgId).collect(Collectors.toList());

        Map<Long, MaterialBO> materialMap = erpBaseService.getMaterialByMaterialId(materialIdList);
        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode(), CustomerDictEnum.MATERIAL_CATEGORY.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        Map<String, String> orgMap = erpBaseService.getOrgNameByIds(orgIdList);

        for (WorkPickingReturnItemRespVO item : list) {
            item.setWarehouseOrgName(orgMap.get(item.getWarehouseOrgId()));

            // 退料单类型
            String workPickingReturnTypeDictId = item.getWorkPickingReturnTypeDictId();
            if(StrUtilX.isNotEmpty(workPickingReturnTypeDictId)){
                workPickingReturnTypeDictId = CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode() + "-" + workPickingReturnTypeDictId;
                item.setWorkPickingReturnTypeDictName(dictMap.get(workPickingReturnTypeDictId));
            }

            // 物料类别
            String materialCategoryDictId = item.getMaterialCategoryDictId();
            if(StrUtilX.isNotEmpty(materialCategoryDictId)){
                materialCategoryDictId = CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
                item.setMaterialCategoryDictName(dictMap.get(materialCategoryDictId));
            }

            MaterialBO materialBO = materialMap.get(item.getMaterialId());

            if (materialBO != null) {
                // 子物料
                item.setMaterialId(materialBO.getMaterialId());
                item.setMaterialCode(materialBO.getMaterialCode());
                item.setMaterialName(materialBO.getMaterialName());

                item.setMainUnitDictId(materialBO.getMainUnitDictId());
                item.setMainUnitDictName(materialBO.getMainUnitDictName());

                item.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
                item.setMaterialSourceDictName(materialBO.getMaterialSourceDictName());

                item.setSpecModel(materialBO.getSpecModel());
                item.setSpecAttributeStr(materialBO.getSpecAttributeStr());
            }
        }
        return pageResult;
    }


    @Override
    public List<WorkPickingReturnItemRespVO> workPickingReturnItemQuotedList(WorkPickingReturnItemQueryReqVO reqVO){
        List<WorkPickingReturnItemRespVO> respVOList = BeanUtilX.copy(workPickingReturnDetailMapper.selectList(reqVO),
                WorkPickingReturnItemRespVO :: new);
        if (CollUtilX.isEmpty(respVOList)){
            return respVOList;
        }

        // 属性转换输出
        for(WorkPickingReturnItemRespVO respVO : respVOList){
            respVO.setReturnQty(respVO.getQty());
        }

        //填充数据
        List<Long> materialIdList = respVOList.stream().map(WorkPickingReturnItemRespVO::getMaterialId).collect(Collectors.toList());
        List<String> orgIdList = respVOList.stream().map(WorkPickingReturnItemRespVO::getWarehouseOrgId).collect(Collectors.toList());

        Map<Long, MaterialBO> materialMap = erpBaseService.getMaterialByMaterialId(materialIdList);

        //查询字典数据
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(), CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        Map<String, String> orgMap = erpBaseService.getOrgNameByIds(orgIdList);

        for (WorkPickingReturnItemRespVO item : respVOList) {
            item.setWarehouseOrgName(orgMap.get(item.getWarehouseOrgId()));

            // 退料单类型
            String workPickingReturnTypeDictId = item.getWorkPickingReturnTypeDictId();
            if(StrUtilX.isNotEmpty(workPickingReturnTypeDictId)){
                workPickingReturnTypeDictId = CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode() + "-" + workPickingReturnTypeDictId;
                item.setWorkPickingReturnTypeDictName(dictMap.get(workPickingReturnTypeDictId));
            }

            // 物料类别
            item.setMaterialCategoryDictId(item.getMaterialCategoryDictId());
            String materialCategoryDictId = item.getMaterialCategoryDictId();
            if(StrUtilX.isNotEmpty(materialCategoryDictId)){
                item.setMaterialCategoryDictId(materialCategoryDictId);
                materialCategoryDictId =  CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
                item.setMaterialCategoryDictName(dictMap.get(materialCategoryDictId));
            }

            MaterialBO materialBO = materialMap.get(item.getMaterialId());

            if (materialBO != null) {
                // 子物料
                item.setMaterialId(materialBO.getMaterialId());
                item.setMaterialCode(materialBO.getMaterialCode());
                item.setMaterialName(materialBO.getMaterialName());

                item.setMainUnitDictId(materialBO.getMainUnitDictId());
                item.setMainUnitDictName(materialBO.getMainUnitDictName());

                item.setMaterialSourceDictId(materialBO.getMaterialSourceDictId());
                item.setMaterialSourceDictName(materialBO.getMaterialSourceDictName());

                item.setSpecModel(materialBO.getSpecModel());
                item.setSpecAttributeStr(materialBO.getSpecAttributeStr());
            }
        }

        return respVOList;
    }

    private List<WorkPickingReturnRespVO> fillVoProperties(List<WorkPickingReturnRespVO> list) {
        if (CollUtilX.isEmpty(list)) {
            return list;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        for (WorkPickingReturnRespVO item : list) {
            empIdList.add(item.getDirectorId());
            deptOrgIdList.add(item.getDirectorOrgId());
            materialIdList.add(item.getMaterialId());
        }

        Map<Long, BomMaterialBO> parentMap = erpBaseService.getBomByMaterialId(materialIdList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIdList);

        //查询负责人
        Map<Long, String> empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        for (WorkPickingReturnRespVO item : list) {

            // 退料单类型
            if(StrUtilX.isNotEmpty(item.getWorkPickingReturnTypeDictId())){
                String workPickingReturnTypeDictId = CustomerDictEnum.WORK_PICKING_RETURN_TYPE.getDictCode() + "-" + item.getWorkPickingReturnTypeDictId();
                item.setWorkPickingReturnTypeDictName(dictMap.get(workPickingReturnTypeDictId));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //查询负责人
            item.setDirectorName(empNameMap.get(item.getDirectorId()));

            //查询责任部门
            item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));

            BomMaterialBO parentMaterialBaseBO = parentMap.get(item.getMaterialId());
            if (parentMaterialBaseBO != null) {
                item.setVersion(parentMaterialBaseBO.getVersion());
                item.setMaterialId(parentMaterialBaseBO.getMaterialId());
                item.setMaterialCode(parentMaterialBaseBO.getMaterialCode());
                item.setMaterialName(parentMaterialBaseBO.getMaterialName());

                item.setMainUnitDictId(parentMaterialBaseBO.getMainUnitDictId());
                item.setMainUnitDictName(parentMaterialBaseBO.getMainUnitDictName());

                item.setMaterialSourceDictId(parentMaterialBaseBO.getMaterialSourceDictId());
                item.setMaterialSourceDictName(parentMaterialBaseBO.getMaterialSourceDictName());

                item.setMaterialCategoryDictId(parentMaterialBaseBO.getMaterialCategoryDictId());
                item.setMaterialCategoryDictName(parentMaterialBaseBO.getMaterialCategoryDictName());

                item.setSpecModel(parentMaterialBaseBO.getSpecModel());
                item.setSpecAttributeStr(parentMaterialBaseBO.getSpecAttributeStr());
            }
        }
        return list;
    }


    @Override
    public void updateInboundedQty(Long workPickingReturnDetailId, BigDecimal inboundQty){
        WorkPickingReturnDetailDO pickingReturnDetailDO = workPickingReturnDetailMapper.selectById(workPickingReturnDetailId);
        if(pickingReturnDetailDO == null){
            return;
        }

        //已入库数量
        pickingReturnDetailDO.setInboundedQty(pickingReturnDetailDO.getInboundedQty().add(inboundQty));
        //可入库数量
        BigDecimal returnableQty = pickingReturnDetailDO.getQty().subtract(pickingReturnDetailDO.getInboundedQty());
        pickingReturnDetailDO.setInboundableQty(returnableQty);
        // 是否全部入库, 可退料数量为0,则为全部完成
        if(returnableQty.compareTo(BigDecimal.ZERO) == 0){
            pickingReturnDetailDO.setIsMaterialFullInbounded(1);
        }else{
            pickingReturnDetailDO.setIsMaterialFullInbounded(0);
        }
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        pickingReturnDetailDO.setUpdatedBy(loginUser.getFullUserName());
        pickingReturnDetailDO.setUpdatedDt(LocalDateTime.now());
        workPickingReturnDetailMapper.updateById(pickingReturnDetailDO);

        WorkPickingReturnDO workPickingReturnDO = workPickingReturnMapper.selectById(pickingReturnDetailDO.getWorkPickingReturnId());
        Long unInboundCount = workPickingReturnDetailMapper.selectUnInboundCount(pickingReturnDetailDO.getWorkPickingReturnId());
        if(unInboundCount == 0){
            workPickingReturnDO.setIsFullInbounded(1);
        }else{
            workPickingReturnDO.setIsFullInbounded(0);
        }
        workPickingReturnDO.setUpdatedBy(loginUser.getFullUserName());
        workPickingReturnDO.setUpdatedDt(LocalDateTime.now());
        workPickingReturnMapper.updateById(workPickingReturnDO);

        //更新退料物料统计表
        WorkPickingMaterialTotalDO workPickingMaterialTotalDO = workPickingMaterialTotalMapper.selectOneByMaterial(
                workPickingReturnDO.getRelatedOrderId(), pickingReturnDetailDO.getMaterialId());
        if(workPickingMaterialTotalDO == null){
            return;
        }
        workPickingMaterialTotalDO.setInboundedQty(workPickingMaterialTotalDO.getInboundedQty().add(inboundQty));
        workPickingMaterialTotalDO.setUpdatedBy(loginUser.getFullUserName());
        workPickingMaterialTotalDO.setUpdatedDt(LocalDateTime.now());
        workPickingMaterialTotalMapper.updateById(workPickingMaterialTotalDO);
    }
}
