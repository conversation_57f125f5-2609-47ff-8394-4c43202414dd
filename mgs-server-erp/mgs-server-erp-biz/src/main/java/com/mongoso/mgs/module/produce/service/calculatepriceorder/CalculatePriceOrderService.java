package com.mongoso.mgs.module.produce.service.calculatepriceorder;

import java.util.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.service.calculatepriceorder.bo.CalculatePriceOrderRuleBO2;
import jakarta.validation.*;
import com.mongoso.mgs.module.produce.controller.admin.calculatepriceorder.vo.*;
import com.mongoso.mgs.module.produce.dal.db.calculatepriceorder.CalculatePriceOrderDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 计价单 Service 接口
 *
 * <AUTHOR>
 */
public interface CalculatePriceOrderService {

    /**
     * 创建计价单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long calculatePriceOrderAdd(@Valid CalculatePriceOrderAditReqVO reqVO);

    Long calculatePriceOrderAddBatch(@Valid CalculatePriceOrderAddBatchReqVO reqVO);

    /**
     * 更新计价单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long calculatePriceOrderEdit(@Valid CalculatePriceOrderAditReqVO reqVO);

    /**
     * 删除计价单
     *
     * @param calculatePriceOrderId 编号
     */
    void calculatePriceOrderDelete(Long calculatePriceOrderId);

    /**
     * 获得计价单信息
     *
     * @param calculatePriceOrderId 编号
     * @return 计价单信息
     */
    CalculatePriceOrderRespVO calculatePriceOrderDetail(Long calculatePriceOrderId);

    /**
     * 获得计价单列表
     *
     * @param reqVO 查询条件
     * @return 计价单列表
     */
    List<CalculatePriceOrderRespVO> calculatePriceOrderList(@Valid CalculatePriceOrderQueryReqVO reqVO);

    /**
     * 获得计价单分页
     *
     * @param reqVO 查询条件
     * @return 计价单分页
     */
    PageResult<CalculatePriceOrderRespVO> calculatePriceOrderPage(@Valid CalculatePriceOrderPageReqVO reqVO);

    ResultX<BatchResult> calculatePriceOrderDeleteBatch(@Valid IdReq reqVO);

    BatchResult calculatePriceOrderApprove(@Valid FlowApprove reqVO);

    Object calculatePriceOrderFlowCallback(@Valid FlowCallback reqVO);


    List<CalculatePriceOrderRuleBO2> calculatePriceOrderRule(@Valid CalculatePriceOrderPageReqVO reqVO);


    String getPieceworkMethodDictId(@Valid CalculatePriceOrderPageReqVO reqVO);
}
