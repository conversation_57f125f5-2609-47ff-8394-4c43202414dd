package com.mongoso.mgs.module.base.controller.admin.hzproduct.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class HzProductBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 产品编码 */
    @NotEmpty(message = "产品编码不能为空")
    @Size(max = 20, message = "产品编码最大可输入20字符数")
    private String productCode;

    /** 产品名称 */
    @NotEmpty(message = "产品名称不能为空")
    @Size(max = 100, message = "产品名称最大可输入100字符数")
    private String productName;

    /** 物料类型 */
    @NotNull(message = "物料类型不能为空")
    private String dictMaterialTypeId;
    @Size(max = 20, message = "物料类型最大可输入20字符数")
    private String dictMaterialTypeName;

    /** 主单位 */
    @NotEmpty(message = "主单位不能为空")
    private String mainUnit;
    /** 是否bom */
    private Short isBom;
    /** 辅单位 leftVal:1, leftUnit:"盒", rightVal:5, rightUnit:"瓶" */
    private String auxUnit;

    /** 产品描述 */
    @Size(max = 300, message = "产品描述最大可输入300字符数")
    private String productDesc;

    /** 规格设置 */
//    @NotEmpty(message = "规格设置不能为空")
    private String specSetting;

    /** 工艺流程编码 */
    private String flowCode;

    /** 流程名称 */
    private String flowName;

    /** 流程描述 */
    private String flowDesc;
//    工单价状态 ["待确认",“已确认”]
    private Integer unitPriceStatus;
    // 总工单价
    private BigDecimal totalUnitPrice;

}
