package com.mongoso.mgs.module.utility.controller.admin.utilitylog.vo;

import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 水电气抄表记录 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class UtilityLogAddBatchReqVO implements Serializable {

    /** 所属公司 */
    //@NotNull(message = "所属公司不能为空")
    private Long companyId;

    /** 抄表日期 */
    @NotNull(message = "抄表时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime readTime;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 责任人 */
    @NotNull(message = "责任人不能为空")
    private Long directorId;

    /** 责任部门 */
    @NotEmpty(message = "责任部门不能为空")
    private String directorOrgId;

    @NotNull(message = "抄表记录不能为空")
    List<UtilityLogAditReqVO> addList;
}
