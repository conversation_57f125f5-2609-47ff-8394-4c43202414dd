package com.mongoso.mgs.module.produce.service.prodworkchangematerialbom;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.produce.controller.admin.prodworkchangematerialbom.vo.*;
import com.mongoso.mgs.module.produce.dal.db.prodworkchangematerialbom.ProdWorkChangeMaterialBomDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.prodworkchangematerialbom.ProdWorkChangeMaterialBomMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.produce.enums.ErrorCodeConstants.*;


/**
 * 生产工单变更单bom Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProdWorkChangeMaterialBomServiceImpl implements ProdWorkChangeMaterialBomService {

    @Resource
    private ProdWorkChangeMaterialBomMapper prodWorkChangeMaterialBomMapper;

    @Override
    public Long prodWorkChangeMaterialBomAdd(ProdWorkChangeMaterialBomAditReqVO reqVO) {
        // 插入
        ProdWorkChangeMaterialBomDO prodWorkChangeMaterialBom = BeanUtilX.copy(reqVO, ProdWorkChangeMaterialBomDO::new);
        prodWorkChangeMaterialBomMapper.insert(prodWorkChangeMaterialBom);
        // 返回
        return prodWorkChangeMaterialBom.getMaterialBomId();
    }

    @Override
    public Long prodWorkChangeMaterialBomEdit(ProdWorkChangeMaterialBomAditReqVO reqVO) {
        // 校验存在
        this.prodWorkChangeMaterialBomValidateExists(reqVO.getMaterialBomId());
        // 更新
        ProdWorkChangeMaterialBomDO prodWorkChangeMaterialBom = BeanUtilX.copy(reqVO, ProdWorkChangeMaterialBomDO::new);
        prodWorkChangeMaterialBomMapper.updateById(prodWorkChangeMaterialBom);
        // 返回
        return prodWorkChangeMaterialBom.getMaterialBomId();
    }

    @Override
    public void prodWorkChangeMaterialBomDelete(Long materialBomId) {
        // 校验存在
        this.prodWorkChangeMaterialBomValidateExists(materialBomId);
        // 删除
        prodWorkChangeMaterialBomMapper.deleteById(materialBomId);
    }

    private ProdWorkChangeMaterialBomDO prodWorkChangeMaterialBomValidateExists(Long materialBomId) {
        ProdWorkChangeMaterialBomDO prodWorkChangeMaterialBom = prodWorkChangeMaterialBomMapper.selectById(materialBomId);
        if (prodWorkChangeMaterialBom == null) {
            // throw exception(PROD_WORK_CHANGE_MATERIAL_BOM_NOT_EXISTS);
            throw new BizException("5001", "生产工单变更单bom不存在");
        }
        return prodWorkChangeMaterialBom;
    }

    @Override
    public ProdWorkChangeMaterialBomRespVO prodWorkChangeMaterialBomDetail(Long materialBomId) {
        ProdWorkChangeMaterialBomDO data = prodWorkChangeMaterialBomValidateExists(materialBomId);
        return BeanUtilX.copy(data, ProdWorkChangeMaterialBomRespVO::new);
    }

    @Override
    public List<ProdWorkChangeMaterialBomRespVO> prodWorkChangeMaterialBomList(ProdWorkChangeMaterialBomQueryReqVO reqVO) {
        List<ProdWorkChangeMaterialBomDO> data = prodWorkChangeMaterialBomMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProdWorkChangeMaterialBomRespVO::new);
    }

    @Override
    public PageResult<ProdWorkChangeMaterialBomRespVO> prodWorkChangeMaterialBomPage(ProdWorkChangeMaterialBomPageReqVO reqVO) {
        PageResult<ProdWorkChangeMaterialBomDO> data = prodWorkChangeMaterialBomMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProdWorkChangeMaterialBomRespVO::new);
    }

}
