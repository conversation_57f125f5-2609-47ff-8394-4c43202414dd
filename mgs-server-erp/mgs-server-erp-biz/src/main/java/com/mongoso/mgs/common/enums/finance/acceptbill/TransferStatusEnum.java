package com.mongoso.mgs.common.enums.finance.acceptbill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 转让状态
 */
@AllArgsConstructor
@Getter
public enum TransferStatusEnum implements Serializable {
    NOT_TRANSFERRED(0,"未转让"),
    PARTIALLY_TRANSFERRED(1,"部分转让"),
    TRANSFERRED(2,"已转让"),
    NO_TRANSFERRED_NEEDED(3,"无需转让");

    public final int key;// 类型
    public final String value;// 描述
}