package com.mongoso.mgs.module.dailycost.controller.admin.spuconfig;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.dal.db.spu.SpuDO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.*;
import com.mongoso.mgs.module.dailycost.service.spuconfig.CostSpuConfigService;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * SPU配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostSpuConfigController {

    @Resource
    private CostSpuConfigService spuConfigService;

    @OperateLog("SPU配置添加或编辑")
    @PostMapping("/costSpuConfigAdit")
    @PreAuthorize("@ss.hasPermission('costSpuConfig:adit')")
    public ResultX<Long> costSpuConfigAdit(@Valid @RequestBody CostSpuConfigAditReqVO reqVO) {
        return success(reqVO.getSpuConfigId() == null
                            ? spuConfigService.costSpuConfigAdd(reqVO)
                            : spuConfigService.costSpuConfigEdit(reqVO));
    }

    @OperateLog("SPU配置删除")
    @PostMapping("/costSpuConfigDel")
    @PreAuthorize("@ss.hasPermission('costSpuConfig:delete')")
    public ResultX<Boolean> costSpuConfigDel(@Valid @RequestBody CostSpuConfigPrimaryReqVO reqVO) {
        spuConfigService.costSpuConfigDel(reqVO.getSpuConfigId());
        return success(true);
    }

    @OperateLog("SPU配置删除(批量)")
    @PostMapping("/costSpuConfigDelBatch")
    @PreAuthorize("@ss.hasPermission('costProcessConfig:delete')")
    public ResultX<BatchResult> costProcessConfigDelBatch(@Valid @RequestBody IdReq reqVO) {
        return spuConfigService.costSpuConfigDelBatch(reqVO);
    }

    @OperateLog("SPU配置详情")
    @PostMapping("/costSpuConfigDetail")
    @PreAuthorize("@ss.hasPermission('costSpuConfig:query')")
    public ResultX<CostSpuConfigRespVO> costSpuConfigDetail(@Valid @RequestBody CostSpuConfigPrimaryReqVO reqVO) {
        return success(spuConfigService.costSpuConfigDetail(reqVO.getSpuConfigId()));
    }

    @OperateLog("SPU配置列表")
    @PostMapping("/costSpuConfigList")
    @PreAuthorize("@ss.hasPermission('costSpuConfig:query')")
    public ResultX<List<CostSpuConfigRespVO>> costSpuConfigList(@Valid @RequestBody CostSpuConfigQueryReqVO reqVO) {
        return success(spuConfigService.costSpuConfigList(reqVO));
    }

    @OperateLog("查询物料对应的所有上级的SPU")
    @PostMapping("/materialBomSpuList")
    @PreAuthorize("@ss.hasPermission('materialBom:query')")
    public ResultX<List<SpuDO>> materialBomParentList(@Valid @RequestBody MaterialBomQueryReqVO reqVO) {
        return success(spuConfigService.materialBomSpuList(reqVO));
    }

    @OperateLog("SPU配置分页")
    @PostMapping("/costSpuConfigPage")
    @PreAuthorize("@ss.hasPermission('costSpuConfig:query')")
    public ResultX<PageResult<CostSpuConfigRespVO>> costSpuConfigPage(@Valid @RequestBody CostSpuConfigPageReqVO reqVO) {
        return success(spuConfigService.costSpuConfigPage(reqVO));
    }

    @OperateLog("SPU配置审核")
    @PostMapping("/costSpuConfigApprove")
    @PreAuthorize("@ss.hasPermission('costSpuConfig:adit')")
    public ResultX<BatchResult> costSpuConfigApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = spuConfigService.costSpuConfigApprove(reqVO);
        return success(resultList);
    }

}
