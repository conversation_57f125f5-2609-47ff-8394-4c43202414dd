package com.mongoso.mgs.module.warehouse.service.erpinventory;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.*;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailBaseVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.detail.ErpInventoryDetailRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.plan.ErpInventoryPlanQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDetailDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryPlanDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory.ErpInventoryDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory.ErpInventoryMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory.ErpInventoryPlanMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import com.mongoso.mgs.module.warehouse.enums.InventoryAdjustStatusEnum;
import com.mongoso.mgs.module.warehouse.enums.InventoryCycleUnitEnums;
import com.mongoso.mgs.module.warehouse.enums.InventoryStatusEnum;
import com.mongoso.mgs.module.warehouse.handler.approve.ErpInventoryApproveHandler;
import com.mongoso.mgs.module.warehouse.handler.flowCallback.ErpInventoryFlowCallBackHandler;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 盘点单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpInventoryServiceImpl implements ErpInventoryService {

    @Resource
    private ErpInventoryMapper erpInventoryMapper;

    @Resource
    private ErpInventoryDetailMapper erpInventoryDetailMapper;

    @Resource
    private ErpInventoryPlanMapper erpInventoryPlanMapper;

    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;

    @Resource
    private ErpInventoryDetailService erpInventoryDetailService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    @Lazy
    private ErpInventoryApproveHandler erpInventoryApproveHandler;

    @Resource
    private ErpInventoryFlowCallBackHandler erpInventoryFlowCallBackHandler;

    @Override
    public Long erpInventoryAdd(ErpInventoryAditReqVO reqVO) {

        // 生成盘点单号
        reqVO.setInventoryCode(seqService.getGenerateCode(reqVO.getInventoryCode(), MenuEnum.INVENTORY_COUNT_ORDER.menuId));

        // 插入
        ErpInventoryDO erpInventory = BeanUtilX.copy(reqVO, ErpInventoryDO::new);
        String warehouseOrgId = reqVO.getWarehouseOrgIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
        erpInventory.setWarehouseOrgId(warehouseOrgId);
        erpInventoryMapper.insert(erpInventory);

        // 返回
        return erpInventory.getInventoryId();
    }

    @Override
    public Long erpInventoryEdit(ErpInventoryAditReqVO reqVO) {
        // 校验存在
        ErpInventoryDO oldErpInventoryDO = this.erpInventoryValidateExists(reqVO.getInventoryId());

        //校验是否存在和版本号,
        EntityUtilX.checkVersion(oldErpInventoryDO, reqVO);

        ErpInventoryDO erpInventory = BeanUtilX.copy(reqVO, ErpInventoryDO::new);
        String warehouseOrgId = reqVO.getWarehouseOrgIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
        erpInventory.setWarehouseOrgId(warehouseOrgId);
        // 更新
        erpInventoryMapper.updateById(erpInventory);

        // 返回
        return erpInventory.getInventoryId();
    }

    @Override
    public Long erpInventoryStatusEdit(ErpInventoryAditReqVO reqVO) {
        // 校验存在
        ErpInventoryDO oldErpInventoryDO = this.erpInventoryValidateExists(reqVO.getInventoryId());
        // 盘点中保存
        if (oldErpInventoryDO.getInventoryStatus() == InventoryStatusEnum.INVENTORYING.getStatus()
                && reqVO.getInventoryStatus() == InventoryStatusEnum.INVENTORYING.getStatus()) {
            // 先删除后新增
            erpInventoryDetailMapper.deleteByInventoryId(reqVO.getInventoryId());
            List<ErpInventoryDetailDO> detailDOList = new ArrayList<>();
            List<ErpInventoryDetailAditReqVO> detailList = reqVO.getDetailList();
            for (ErpInventoryDetailAditReqVO item : detailList) {
                ErpInventoryDetailDO detailDO = BeanUtilX.copy(item, ErpInventoryDetailDO::new);
                detailDO.setInventoryId(oldErpInventoryDO.getInventoryId());
                detailDO.setInventoryCode(oldErpInventoryDO.getInventoryCode());
                detailDOList.add(detailDO);
            }
            erpInventoryDetailMapper.insertBatch(detailDOList);
        } else if (reqVO.getInventoryStatus() == InventoryStatusEnum.INVENTORYING.getStatus()) {
            if (oldErpInventoryDO.getInventoryStatus() != InventoryStatusEnum.UNINVENTORY.getStatus()) {
                throw new BizException("5001", "盘点状态不等于未盘点, 不能操作开始盘点!");
            }
        } else if (reqVO.getInventoryStatus() == InventoryStatusEnum.INVENTORIED.getStatus()) {
            if (oldErpInventoryDO.getInventoryStatus() != InventoryStatusEnum.INVENTORYING.getStatus()) {
                throw new BizException("5001", "盘点状态不等于盘点中, 不能操作完成盘点!");
            }
        } else if (reqVO.getInventoryStatus() == InventoryStatusEnum.CLOSED.getStatus()) {
            if (oldErpInventoryDO.getInventoryStatus() == InventoryStatusEnum.INVENTORIED.getStatus()) {
                throw new BizException("5001", "盘点状态等于已完成, 不能操作关闭盘点!");
            }
        }

        ErpInventoryDO erpInventory = BeanUtilX.copy(reqVO, ErpInventoryDO::new);
        // 开始盘点
        if (reqVO.getInventoryStatus() == InventoryStatusEnum.INVENTORYING.getStatus() &&
                oldErpInventoryDO.getInventoryStatus() != InventoryStatusEnum.INVENTORYING.getStatus()) {
            //更新实际开始时间
            erpInventory.setActStartDt(LocalDateTime.now());
            //查询仓库库存
            ErpMaterialStockQueryReqVO stockQueryReqVO = new ErpMaterialStockQueryReqVO();
            stockQueryReqVO.setWarehouseOrgIdList(Arrays.asList(oldErpInventoryDO.getWarehouseOrgId().split(",")));
            stockQueryReqVO.setStockType(2);
            List<ErpMaterialStockRespVO> materialStockRespList = erpMaterialStockMapper.queryWarehouseStockList(stockQueryReqVO);
            materialStockRespList.sort(Comparator.comparing(ErpMaterialStockRespVO::getWarehouseOrgId));
            //新增明细
            List<ErpInventoryDetailDO> detailDOList = new ArrayList<>();
            for(int i = 0; i < materialStockRespList.size(); i++){
                ErpMaterialStockRespVO materialStockRespVO = materialStockRespList.get(i);
                ErpInventoryDetailDO detailDO = new ErpInventoryDetailDO();
                detailDO.setInventoryId(erpInventory.getInventoryId());
                detailDO.setInventoryCode(erpInventory.getInventoryCode());
                detailDO.setRowNo(i+1);
                detailDO.setMaterialId(materialStockRespVO.getMaterialId());
                detailDO.setMaterialCode(materialStockRespVO.getMaterialCode());
                detailDO.setMainUnitDictId(materialStockRespVO.getMainUnitDictId());
                detailDO.setInventoryQty(materialStockRespVO.getStockQty());
                detailDO.setWarehouseOrgId(materialStockRespVO.getWarehouseOrgId());
                detailDOList.add(detailDO);
            }
            erpInventoryDetailMapper.insertBatch(detailDOList);
        }

        // 完成盘点
        if (reqVO.getInventoryStatus() == InventoryStatusEnum.INVENTORIED.getStatus()) {
            //更新实际结束时间
            erpInventory.setActEndDt(LocalDateTime.now());
            // 先删除后新增
            erpInventoryDetailMapper.deleteByInventoryId(reqVO.getInventoryId());
            List<ErpInventoryDetailDO> detailDOList = new ArrayList<>();
            List<ErpInventoryDetailAditReqVO> detailList = reqVO.getDetailList();
            Integer isDeviation = 0;//是否无偏差
            for (ErpInventoryDetailAditReqVO item : detailList) {
                ErpInventoryDetailDO detailDO = BeanUtilX.copy(item, ErpInventoryDetailDO::new);
                detailDO.setInventoryId(erpInventory.getInventoryId());
                detailDO.setInventoryCode(erpInventory.getInventoryCode());
                detailDOList.add(detailDO);

                if (item.getInventoryQty() != null && item.getActInventoryQty() != null
                        && item.getInventoryQty().compareTo(item.getActInventoryQty()) != 0) {
                    isDeviation = 1;
                }
            }

            if (isDeviation == 1) {
                erpInventory.setAdjustStatus(InventoryAdjustStatusEnum.NEED_ADJUST.getStatus());
            } else {
                erpInventory.setAdjustStatus(InventoryAdjustStatusEnum.NO_ADJUST.getStatus());
            }

            erpInventoryDetailMapper.insertBatch(detailDOList);
        }

        if (reqVO.getInventoryStatus() == InventoryStatusEnum.CLOSED.getStatus()) {
            erpInventory.setAdjustStatus(InventoryAdjustStatusEnum.NO_ADJUST.getStatus());
        }

        // 更新
        erpInventoryMapper.updateById(erpInventory);

        // 返回
        return erpInventory.getInventoryId();
    }

    @Override
    public Long erpInventoryAdjustStatusEdit(ErpInventoryAditReqVO reqVO) {

        ErpInventoryDO inventoryDO = erpInventoryMapper.selectById(reqVO.getInventoryId());

        if (inventoryDO.getInventoryStatus() != InventoryStatusEnum.INVENTORIED.getStatus()) {
            throw new BizException("5001", "盘点状态不等于已完成, 不能修改调整状态!");
        }

        //判断盘点明细是否都无偏差
        Integer isDeviation = checkDetailDeviation(reqVO);
        if (isDeviation != 1) {
            throw new BizException("5001", "该盘点单据没有盈亏，不能修改调整状态");
        }

        ErpInventoryDO erpInventoryDO = new ErpInventoryDO();
        erpInventoryDO.setInventoryId(reqVO.getInventoryId());
        erpInventoryDO.setAdjustStatus(reqVO.getAdjustStatus());
        // 更新
        erpInventoryMapper.updateById(erpInventoryDO);
        return erpInventoryDO.getInventoryId();
    }

    private Integer checkDetailDeviation(ErpInventoryAditReqVO reqVO) {
        ErpInventoryDetailQueryReqVO detailQueryReqVO = new ErpInventoryDetailQueryReqVO();
        detailQueryReqVO.setInventoryId(reqVO.getInventoryId());
        List<ErpInventoryDetailDO> detailDOList = erpInventoryDetailMapper.selectList(detailQueryReqVO);
        return checkDetailDeviation(detailDOList);
    }

    private Integer checkDetailDeviation(List<ErpInventoryDetailDO> detailDOList) {
        Integer isDeviation = 0;//是否无偏差
        for (ErpInventoryDetailDO item : detailDOList) {
            if (item.getInventoryQty() != null && item.getActInventoryQty() != null
                    && item.getInventoryQty().compareTo(item.getActInventoryQty()) != 0) {
                isDeviation = 1;
            }
        }
        return isDeviation;
    }

    @Override
    public void erpInventoryDel(Long inventoryId) {
        // 校验存在
        this.erpInventoryValidateExists(inventoryId);
        // 删除
        erpInventoryMapper.deleteById(inventoryId);
        erpInventoryDetailMapper.deleteByInventoryId(inventoryId);
    }

    @Override
    public ResultX<BatchResult> erpInventoryDelBatch(IdsReqVO reqVO) {
        String id = EntityUtilX.getPropertyName(ErpInventoryDO::getInventoryId);
        String code = EntityUtilX.getPropertyName(ErpInventoryDO::getInventoryCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), ErpInventoryDO.class, ErpInventoryDetailDO.class, id, code);
    }

    private ErpInventoryDO erpInventoryValidateExists(Long inventoryId) {
        ErpInventoryDO erpInventory = erpInventoryMapper.selectById(inventoryId);
        if (erpInventory == null) {
            // throw exception(ERP_INVENTORY_NOT_EXISTS);
            throw new BizException("5001", "盘点单不存在");
        }
        return erpInventory;
    }

    @Override
    public ErpInventoryRespVO erpInventoryDetail(ErpInventoryPrimaryReqVO reqVO) {

        Long inventoryId = reqVO.getInventoryId();

        //校验存在
        ErpInventoryDO erpInventoryDO = this.erpInventoryValidateExists(inventoryId);
        //对象转换
        ErpInventoryRespVO respVO = BeanUtilX.copy(erpInventoryDO, ErpInventoryRespVO::new);
        //查询明细列表
        ErpInventoryDetailQueryReqVO detailReqVO = new ErpInventoryDetailQueryReqVO();
        detailReqVO.setInventoryId(inventoryId);
        detailReqVO.setIsIssueAdjust(reqVO.getIsIssueAdjust());
        List<ErpInventoryDetailRespVO> detailList = erpInventoryDetailService.erpInventoryDetailList(detailReqVO);
        respVO.setDetailList(detailList);

        //属性填充
        fillVoProperties(respVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(inventoryId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<ErpInventoryRespVO> erpInventoryList(ErpInventoryQueryReqVO reqVO) {
        List<ErpInventoryRespVO> respVOList = BeanUtilX.copy(erpInventoryMapper.selectList(reqVO), ErpInventoryRespVO::new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<ErpInventoryRespVO> erpInventoryPage(ErpInventoryPageReqVO reqVO) {
        PageResult<ErpInventoryRespVO> pageResult = BeanUtilX.copy(erpInventoryMapper.selectPage(reqVO), ErpInventoryRespVO::new);
        if (CollUtilX.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private ErpInventoryRespVO fillVoProperties(ErpInventoryRespVO respVO) {
        List<ErpInventoryRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        List<ErpInventoryDetailRespVO> detailList = respVO.getDetailList();
        //是否可手动更改为无需调整
        Integer isChangeAdjust = 0;
        if (CollUtilX.isNotEmpty(detailList)) {
            List<String> orgIdList = detailList.stream().map(ErpInventoryDetailBaseVO::getWarehouseOrgId).collect(Collectors.toList());
            //查询责任部门信息
            Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);
            for (ErpInventoryDetailRespVO item : detailList) {
                if (respVO.getInventoryStatus() == InventoryStatusEnum.INVENTORIED.getStatus()
                        && item.getInventoryQty() != null && item.getActInventoryQty() != null
                        && item.getInventoryQty().compareTo(item.getActInventoryQty()) != 0) {
                    isChangeAdjust = 1;
                }
                if (item.getWarehouseOrgId() != null) {
                    item.setWarehouseOrgName(directorOrgMap.get(item.getWarehouseOrgId()));
                }
            }
        }
        if(respVO.getWarehouseOrgId()!=null){
            respVO.setWarehouseOrgIdList(Arrays.asList(respVO.getWarehouseOrgId().split(",")));
        }
        respVO.setIsChangeAdjust(isChangeAdjust);

        // 批量处理
        List<ErpInventoryRespVO> erpRespVOList = batchFillVoProperties(respVOList);
        return erpRespVOList.get(0);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private List<ErpInventoryRespVO> batchFillVoProperties(List<ErpInventoryRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return respVOList;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        for (ErpInventoryRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            if (item.getWarehouseOrgId() != null) {
                orgIdList.addAll(Arrays.asList(item.getWarehouseOrgId().split(",")));
            }
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.INVENTORY_TYPE.getDictCode(), SystemDictEnum.INVENTORY_METHOD.getDictCode(),
                SystemDictEnum.INVENTORY_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        for (ErpInventoryRespVO item : respVOList) {
            // 盘点单类型
            if (StrUtilX.isNotEmpty(item.getInventoryTypeDictId())) {
                String inventoryTypeDictId = CustomerDictEnum.INVENTORY_TYPE.getDictCode() + "-" + item.getInventoryTypeDictId();
                item.setInventoryTypeDictName(dictMap.get(inventoryTypeDictId));
            }

            // 盘点方式
            if (item.getInventoryMethod() != null) {
                String inventoryMethodStr = SystemDictEnum.INVENTORY_METHOD.getDictCode() + "-" + item.getInventoryMethod();
                item.setInventoryMethodDictName(dictMap.get(inventoryMethodStr));
            }

            // 盘点状态
            if (item.getInventoryStatus() != null) {
                String inventoryStatusStr = SystemDictEnum.INVENTORY_STATUS.getDictCode() + "-" + item.getInventoryStatus();
                item.setInventoryStatusDictName(dictMap.get(inventoryStatusStr));
            }
            //盘点仓库列表
            if (item.getWarehouseOrgId() != null) {
                item.setWarehouseOrgName(Arrays.stream(item.getWarehouseOrgId().split(","))
                        .map(directorOrgMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining("、")));
            }


            // 审核状态
            if (item.getDataStatus() != null) {
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if (item.getDirectorId() != null) {
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if (item.getDirectorOrgId() != null) {
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
        return respVOList;
    }

    @Override
    public BatchResult erpInventoryApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ErpInventoryDO> list = erpInventoryMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ErpInventoryDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = erpInventoryApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                exception.printStackTrace();
                FailItem failItem = new FailItem();
                failItem.setCode(item.getInventoryCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()) {
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ErpInventoryDO item : list) {
                String reason = reasonMap.get(item.getInventoryCode());
                if (StrUtilX.isEmpty(reason)) {
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInventoryId());
                    messageInfoBO.setObjCode(item.getInventoryCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                } else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInventoryId());
                    messageInfoBO.setObjCode(item.getInventoryCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }

        return batchResult;
    }

    @Override
    public Object erpInventoryFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        ErpInventoryDO currentDO = erpInventoryMapper.selectById(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return erpInventoryFlowCallBackHandler.handleFlowCallback(currentDO, flowCallbackBO);
    }

    @Override
    public void erpInventoryPlanGenTask() {
        // 查询盘点计划
        ErpInventoryPlanQueryReqVO planQueryReqVO = new ErpInventoryPlanQueryReqVO();
        planQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
        planQueryReqVO.setIsEnable(BaseEnum.ENABLE.getKey());
        List<ErpInventoryPlanDO> planDOList = erpInventoryPlanMapper.selectList(planQueryReqVO);
        if (CollUtilX.isEmpty(planDOList)) {
            return;
        }

        //生成盘点单
        LocalDateTime curNow = LocalDateTime.now();
        for (ErpInventoryPlanDO planDO : planDOList) {
            buildPlanGenTask(planDO, curNow);
        }
    }

    @Override
    public List<ErpInventoryDO> forewarnJob(Integer dataStatus, List<Integer> statusList) {
        return erpInventoryMapper.forewarnJob(dataStatus, statusList);
    }


    private void buildPlanGenTask(ErpInventoryPlanDO planDO, LocalDateTime curNow) {
        // 提前生成时间
        Integer leadTime = planDO.getLeadTime();
        // 提前生成单位
        Integer leadTimeUnit = planDO.getLeadTimeUnit();
        // 计划开始时间
        LocalDateTime planInventoryDt = planDO.getPlanInventoryDt();
        // 目标生成时间
        LocalDateTime generateTargetDt;
        if (leadTimeUnit == 0) {//小时
            generateTargetDt = planInventoryDt.minusHours(leadTime);
        } else if (leadTimeUnit == 1) {//天
            generateTargetDt = planInventoryDt.minusDays(leadTime);
        } else {
            generateTargetDt = planInventoryDt;
        }

        if (generateTargetDt.isEqual(curNow) || generateTargetDt.isBefore(curNow)) {
            //盘点单信息
            ErpInventoryDO erpInventoryDO = new ErpInventoryDO();
            erpInventoryDO.setInventoryName(planDO.getInventoryPlanName());
            erpInventoryDO.setInventoryTypeDictId(planDO.getInventoryPlanTypeDictId());
            erpInventoryDO.setWarehouseOrgId(planDO.getWarehouseOrgId());
            erpInventoryDO.setInventoryMethod(planDO.getInventoryMethod());
            erpInventoryDO.setPlanInventoryDt(planDO.getPlanInventoryDt());
            erpInventoryDO.setRemark(planDO.getRemark());
            erpInventoryDO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            erpInventoryDO.setDirectorId(planDO.getDirectorId());
            erpInventoryDO.setDirectorOrgId(planDO.getDirectorOrgId());
            erpInventoryDO.setFormDt(planDO.getPlanInventoryDt());
            erpInventoryDO.setCreatedBy("系统");
            erpInventoryDO.setCreatedDt(curNow);
            erpInventoryDO.setUpdatedBy("系统");
            erpInventoryDO.setUpdatedDt(curNow);
            erpInventoryDO.setApprovedBy("系统");
            erpInventoryDO.setApprovedDt(curNow);
            // 生成盘点单号
            LocalDate curDate = curNow.toLocalDate();
            Long seq = seqService.lockInsertLamb(SeqEnum.inventory.getTableName(), curDate, null);
            erpInventoryDO.setInventoryCode(CodeGenUtil.generateCode(CodeGenUtil.CodeTypeEnums.INVENTORY, seq));
            //新增盘点单
            erpInventoryMapper.insert(erpInventoryDO);
            // 改变盘点计划的计划开始日期
            // 循环周期值
            Integer cycleValue = planDO.getInventoryCycle();
            Integer cycleUnit = planDO.getInventoryCycleUnit();
            if (cycleUnit.equals(InventoryCycleUnitEnums.DAY.getType())) {
                planInventoryDt = planInventoryDt.plusDays(cycleValue);
            } else if (cycleUnit.equals(InventoryCycleUnitEnums.WEEK.getType())) {
                planInventoryDt = planInventoryDt.plusWeeks(cycleValue);
            } else if (cycleUnit.equals(InventoryCycleUnitEnums.MONTH.getType())) {
                planInventoryDt = planInventoryDt.plusMonths(cycleValue);
            } else if (cycleUnit.equals(InventoryCycleUnitEnums.SEASON.getType())) {
                planInventoryDt = planInventoryDt.plusMonths(3 * cycleValue);
            } else if (cycleUnit.equals(InventoryCycleUnitEnums.YEAR.getType())) {
                planInventoryDt = planInventoryDt.plusYears(cycleValue);
            }
            planDO.setPlanInventoryDt(planInventoryDt);
            erpInventoryPlanMapper.updateById(planDO);
        }
    }
}
