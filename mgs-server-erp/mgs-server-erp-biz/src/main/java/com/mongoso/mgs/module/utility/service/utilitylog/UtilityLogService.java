package com.mongoso.mgs.module.utility.service.utilitylog;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.utility.controller.admin.utilitylog.vo.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 水电气抄表记录 Service 接口
 *
 * <AUTHOR>
 */
public interface UtilityLogService {

    /**
     * 创建水电气抄表记录
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long utilityLogAdd(@Valid UtilityLogAditReqVO reqVO);

    /**
     * 更新水电气抄表记录
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Integer utilityLogBatchEdit(@Valid List<UtilityLogAditReqVO> reqVO);
    Long utilityLogEdit(@Valid UtilityLogAditReqVO reqVO);

    /**
     * 删除水电气抄表记录
     *
     * @param utilityLogId 编号
     */
    void utilityLogDel(Long utilityLogId);

    /**
     * 批量删除
     * @param idReq
     * @return
     */
    ResultX<BatchResult> utilityLogDelBatch(IdReq idReq);

    /**
     * 获得水电气抄表记录信息
     *
     * @param utilityLogId 编号
     * @return 水电气抄表记录信息
     */
    UtilityLogRespVO utilityLogDetail(Long utilityLogId);

    /**
     * 获得水电气抄表记录列表
     *
     * @param reqVO 查询条件
     * @return 水电气抄表记录列表
     */
    List<UtilityLogRespVO> utilityLogList(@Valid UtilityLogQueryReqVO reqVO);

    /**
     * 获得水电气抄表记录分页
     *
     * @param reqVO 查询条件
     * @return 水电气抄表记录分页
     */
    PageResult<UtilityLogRespVO> utilityLogPage(@Valid UtilityLogPageReqVO reqVO);

    /**
     * 审核
     * @param reqVO
     * @return
     */
    BatchResult utilityLogApprove(FlowApprove reqVO);

    Integer utilityLogAddBatch(@Valid UtilityLogAddBatchReqVO reqVO);

    Object utilityLogFlowCallback(FlowCallback reqVO);
}
