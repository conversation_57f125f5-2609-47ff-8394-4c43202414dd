package com.mongoso.mgs.module.base.controller.admin.prodorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 生产订单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProdOrderBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 生产订单编码 */
    private String prodOrderCode;

    /** 对方销售编码 */
    private String saleOrderNo;

    /** 销售订单编码【系统生成】 */
    private String saleOrderCode;

    @NotNull(message = "产品主键id不允许为空")
    private Long productId;

    /** 产品编码 */
    private String productCode;

    /** 产品名称 */
    @Size(max = 100, message = "产品名称最大可输入100字符数")
    private String productName;

    /** 开始日期 */
    @JsonFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate startDate;

    /** 完成日期 */
    @JsonFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate completionDate;

    /** 订单状态 待计划/计划中/已计划/已完成/已关闭 */
    private Integer orderState;

    /** 订单总数 */
    private Integer orderQty;

    /** 已计划总数 */
    private Integer planCount;

    /** 实际生产总数 */
    private Integer actProdCount;

    /** 委外订单已计划数量 */
    private Integer outPlanCount;

    /** 委外订单实际生产数量 */
    private Integer outActProdCount;

    /** 生产物料 */
    private List<ProdOrderMaterialBaseVO> materialList;

    /** 操作类型 关闭/确认完成/生产工单影响 */
    private Integer type;

    //关闭原因
    @Size(max = 300, message = "关闭原因最大可输入300字符数")
    private String closeReason;

    //    bom确认状态 ["待确认",“已确认”]
    private Integer bomStatus;

    //    工单价状态 ["待确认",“已确认”]
    private Integer unitPriceStatus;

    //    工艺路线确认状态 ["待确认",“已确认”]
    private Integer flowProcessStatus;

    // bom类型 ["自定义bom",“产品bom”,"物料bom"]
    @NotNull(message = "请选择bom类型")
    private Integer bomType;

    // 工艺路线类型["自定义",“产品”]
//    @NotNull(message = "请选择工艺路线类型")
    private Integer flowType;

    // 总工单价
    private BigDecimal totalUnitPrice;
    /** 工艺流程编码 */
//    @NotNull(message = "产品工艺路线不能为空")
    private String flowCode;

    /** 备注 */
    @Size(max = 300, message = "备注最大可输入300字符数")
    private String remark;

    //是否需要容差
    private Integer isTolerance;

    /** 限制下限 */
    private BigDecimal lowerLimit;

    /** 限制上限 */
    private BigDecimal upperLimit;

    //容差维度[按订单总数,按单物料总数]
    private Integer toleranceType;

    //负责人
    @NotNull(message = "负责人不允许为空")
    private String directorUserIds;

    //负责人名称
    private String directorUserNames;

    //执行人
    private String executorIds;

    //执行人名称
    private String executorNames;

    //执行组织
    private String executeOrgs;

    //执行组织名称
    private String executeOrgNames;

    //物料类型[单物料,多物料]
    private Integer materialQtyType;

    /** 生产编码拼接销售编码 */
    private String prodSaleOrderCode;

    /** 裁剪工艺要求 */
    private String ext01;

    /** 车缝工艺要求 */
    private String ext02;

    /** 洗水工艺要求 */
    private String ext03;

    /** 包装工艺要求 */
    private String ext04;

    /** 板房要求 */
    private String ext05;

    /** 面料 */
    private String productExt01;

    /** 面料成份 */
    private String productExt02;

    /** 袋布 */
    private String productExt03;

    /** 洗水 */
    private String productExt04;

}
