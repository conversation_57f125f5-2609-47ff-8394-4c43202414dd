package com.mongoso.mgs.module.dailycost.service.indirectcostamount;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.comp.CostCompDailyIndirectPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.comp.CostCompDailyIndirectRespVO;

import jakarta.validation.Valid;

/**
 * 企业日间接成本 Service 接口
 *
 * <AUTHOR>
 */
public interface CostCompDailyIndirectService {

    /**
     * 获得企业日间接成本分页
     *
     * @param reqVO 查询条件
     * @return 企业日间接成本分页
     */
    PageResult<CostCompDailyIndirectRespVO> costCompDailyIndirectPage(@Valid CostCompDailyIndirectPageReqVO reqVO);

}
