package com.mongoso.mgs.module.purchase.service.purchasechange;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseFlowApprove;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseFlowCallback;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.PurchaseChangeAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.PurchaseChangePageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.PurchaseChangeQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.PurchaseChangeRespVO;

import com.mongoso.mgs.module.sale.controller.admin.salechangdetail.vo.ChangUnitPriceCheckReqVO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购订单变更 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseChangeService {

    /**
     * 创建采购订单变更
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseChangeAdd(@Valid PurchaseChangeAditReqVO reqVO);

    /**
     * 更新采购订单变更
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseChangeEdit(@Valid PurchaseChangeAditReqVO reqVO);

    /**
     * 删除采购订单变更
     *
     * @param purchaseChangeId 编号
     */
    void purchaseChangeDel(Long purchaseChangeId);

    /**
     * 获得采购订单变更信息
     *
     * @param purchaseChangeId 编号
     * @return 采购订单变更信息
     */
    PurchaseChangeRespVO purchaseChangeDetail(Long purchaseChangeId);

    /**
     * 获得采购订单变更前的信息
     *
     * @param purchaseChangeId 编号
     * @return 采购订单变更信息
     */
    PurchaseChangeRespVO purchaseChangeBeforeAfterDetail(Long purchaseChangeId);

    /**
     * 获得采购订单变更列表
     *
     * @param reqVO 查询条件
     * @return 采购订单变更列表
     */
    List<PurchaseChangeRespVO> purchaseChangeList(@Valid PurchaseChangeQueryReqVO reqVO);

    /**
     * 获得采购订单变更分页
     *
     * @param reqVO 查询条件
     * @return 采购订单变更分页
     */
    PageResult<PurchaseChangeRespVO> purchaseChangePage(@Valid PurchaseChangePageReqVO reqVO);

    BatchResult purchaseChangeDelBatch(IdReq reqVO);

    BatchResult purchaseChangeApprove(PurchaseFlowApprove reqVO);

    Object purchaseChangeFlowCallback(PurchaseFlowCallback reqVO);

    PurchaseChangeRespVO purchaseDetailForChange(Long purchaseOrderId);
}
