package com.mongoso.mgs.module.finance.service.payment;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.enums.FileTableEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.finance.acceptbill.UsageStatusEnum;
import com.mongoso.mgs.common.service.file.MyFileService;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.controller.admin.erpsupplier.vo.ERPSupplierQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.erpcustomer.ERPCustomerDO;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpcustomer.ERPCustomerMapper;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpsupplier.ERPSupplierService;
import com.mongoso.mgs.module.finance.controller.admin.advancewritten.vo.AdvancePaymentWrittenRespVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbill.vo.AcceptBillQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbill.vo.AcceptBillRespVO;
import com.mongoso.mgs.module.finance.controller.admin.payment.vo.*;
import com.mongoso.mgs.module.finance.controller.admin.shouldpayment.vo.ShouldPaymentQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.shouldpayment.vo.ShouldPaymentRespVO;
import com.mongoso.mgs.module.finance.dal.db.advancepayment.AdvancePaymentDO;
import com.mongoso.mgs.module.finance.dal.db.advancewritten.AdvancePaymentWrittenDO;
import com.mongoso.mgs.module.finance.dal.db.cashbank.acceptbill.AcceptBillDO;
import com.mongoso.mgs.module.finance.dal.db.cashbank.bankconfig.BankConfigDO;
import com.mongoso.mgs.module.finance.dal.db.payment.PaymentOrderDO;
import com.mongoso.mgs.module.finance.dal.db.paymentapply.PaymentApplyDO;
import com.mongoso.mgs.module.finance.dal.db.shouldpayment.ShouldPaymentDO;
import com.mongoso.mgs.module.finance.dal.mysql.advancepayment.AdvancePaymentMapper;
import com.mongoso.mgs.module.finance.dal.mysql.advancewritten.AdvancePaymentWrittenMapper;
import com.mongoso.mgs.module.finance.dal.mysql.cashbank.acceptbill.AcceptBillMapper;
import com.mongoso.mgs.module.finance.dal.mysql.cashbank.bankconfig.BankConfigMapper;
import com.mongoso.mgs.module.finance.dal.mysql.payment.PaymentOrderMapper;
import com.mongoso.mgs.module.finance.dal.mysql.paymentapply.PaymentApplyMapper;
import com.mongoso.mgs.module.finance.dal.mysql.shouldpayment.ShouldPaymentMapper;
import com.mongoso.mgs.module.finance.handler.approve.payment.PaymentApproveHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.payment.PaymentFlowCallBackHandler;
import com.mongoso.mgs.module.finance.service.shouldpayment.ShouldPaymentService;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.PaymentRelationAditReqVO;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.PaymentRelationRespVO;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import com.mongoso.mgs.module.payment.dal.mysql.paymentrelation.PaymentRelationMapper;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.payment.enums.ErrorCodeConstants.*;


/**
 * 收款单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PaymentServiceImpl implements PaymentService {

    @Resource
    private BankConfigMapper bankConfigMapper;
    @Resource
    private PaymentOrderMapper paymentOrderMapper;
    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private SeqService seqService;
    @Resource
    private MyFileService fileService;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private PaymentApproveHandler approveHandler;
    @Resource
    private PaymentFlowCallBackHandler callbackHandler;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private ErpBaseService compositeService;
    @Resource
    private ERPSupplierService erpSupplierService;
    @Resource
    private ERPCustomerMapper erpCustomerMapper;
    @Resource
    private ERPSupplierMapper erpSupplierMapper;
    @Resource
    private AcceptBillMapper acceptBillMapper;

    @Resource
    private PaymentWriteOffService paymentWriteOffService;
    @Resource
    private AdvancePaymentWrittenMapper advancePaymentWrittenMapper;
    @Resource
    private AdvancePaymentMapper advancePaymentMapper;
    @Resource
    private ShouldPaymentMapper shouldPaymentMapper;
    @Resource
    private ShouldPaymentService shouldPaymentService;
    @Resource
    private PaymentRelationMapper paymentRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long paymentAdd(PaymentAditReqVO reqVO) {
        // 插入
        PaymentOrderDO payment = BeanUtilX.copy(reqVO, PaymentOrderDO::new);
        // 生成单号，默认是采购
        String code = null;

        if (reqVO.getFormType() == 2) {
            code = seqService.getGenerateCode(reqVO.getPayCode(), MenuEnum.PURCHASE_PAYMENT.menuId);
        }else {
            code = seqService.getGenerateCode(reqVO.getPayCode(), MenuEnum.SALES_COLLECTION.menuId);
        }

        payment.setPayCode(code);
        //超收金额 = 本次剩余收款总额
        BigDecimal overAmt = payment.getRemainingAmt().compareTo(BigDecimal.ZERO)>0 ? BigDecimal.ZERO : payment.getRemainingAmt().abs();
        payment.setOverAmt(overAmt);

        //收款金额处理
        dealBill(reqVO, payment);

        paymentOrderMapper.insert(payment);

        // 绑定附件
        fileService.saveFile(payment.getPayId(), reqVO.getFileList(), FileTableEnum.REC_FILE);

        // 返回
        return payment.getPayId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long paymentEdit(PaymentAditReqVO reqVO) {
        // 校验存在
        PaymentOrderDO exist = this.paymentValidateExists(reqVO.getPayId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(exist, reqVO);

        // 更新
        PaymentOrderDO payment = BeanUtilX.copy(reqVO, PaymentOrderDO::new);
        payment.setPayCode(exist.getPayCode());

        ////收款金额处理
        dealBill(reqVO, payment);

        //超收金额
        BigDecimal overAmt = payment.getRemainingAmt().compareTo(BigDecimal.ZERO)>0 ? BigDecimal.ZERO : payment.getRemainingAmt().abs();
        payment.setOverAmt(overAmt);
        paymentOrderMapper.updateById(payment);

        // 绑定附件
        fileService.saveFile(payment.getPayId(), reqVO.getFileList(), FileTableEnum.REC_FILE);
        // 返回
        return payment.getPayId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long paymentAddNew(PaymentAditReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getRelationOrderList())){
            throw new BizException("5001", "请选择关联单据");
        }
        if(ObjUtilX.isEmpty(reqVO.getExchangeRate())){
            throw new BizException("5001", "请传入汇率");
        }
        // 插入
        PaymentOrderDO payment = BeanUtilX.copy(reqVO, PaymentOrderDO::new);
        // 生成单号，默认是采购
        String code = null;
        if (reqVO.getFormType() == 2) {
            code = seqService.getGenerateCode(reqVO.getPayCode(), MenuEnum.PURCHASE_PAYMENT.menuId);
        }else {
            code = seqService.getGenerateCode(reqVO.getPayCode(), MenuEnum.SALES_COLLECTION.menuId);
        }

        payment.setPayCode(code);
        //超收金额 = 本次剩余收款总额
        BigDecimal overAmt = payment.getRemainingAmt().compareTo(BigDecimal.ZERO)>0 ? BigDecimal.ZERO : payment.getRemainingAmt().abs();
        payment.setOverAmt(overAmt);


        Long payId = IDUtilX.getId();
        payment.setPayId(payId);
        // 绑定附件
        fileService.saveFile(payId, reqVO.getFileList(), FileTableEnum.REC_FILE);

        // 添加关联上游单据
        List<PaymentRelationDO> relationOrders = new ArrayList<>(reqVO.getRelationOrderList().size());
        List<Long> relatedOrderIds = new ArrayList<>(reqVO.getRelationOrderList().size());
        BigDecimal payRealTotal = BigDecimal.ZERO;
        for (PaymentRelationAditReqVO order : reqVO.getRelationOrderList()) {
            relatedOrderIds.add(order.getRelatedOrderId());
            PaymentRelationDO relation = BeanUtilX.copy(order, PaymentRelationDO::new);
            relation.setOrderId(payId);

            if(reqVO.getFormType() == 1) {// 销售收款
                relation.setFormType(OrderTypeEnum.SALE_PAY.getType());
            }else{// 采购付款
                relation.setFormType(OrderTypeEnum.PURCHASE_PAY.getType());
            }

            if(reqVO.getHaveApply() == 1) {// 付款申请
                relation.setRelatedOrderType(OrderTypeEnum.PURCHASE_PAY_APPLY.getType());
            }else{// 收款/付款
                if(reqVO.getFormType() == 1) {//
                    relation.setRelatedOrderType(OrderTypeEnum.SALE_SHOULD_PAY.getType());
                }else{// 采购付款
                    relation.setRelatedOrderType(OrderTypeEnum.PURCHASE_SHOULD_PAY.getType());
                }
            }
            relation.setRelatedDetailType(0);
            relationOrders.add(relation);
            payRealTotal = payRealTotal.add(relation.getAmt());
        }
        paymentRelationMapper.insertBatch(relationOrders);
        // 主单据添加关联ID集合
        payment.setRelatedOrderIds(relatedOrderIds);

        //收款金额处理
        // todo 预收款总额不能超过 剩余应收款金额
        dealBillNew(reqVO, payment);
        //更新本币币种及汇率
        if(payment.getExchangeRate() != null){
            payment.setLocalCurrencyPayTotalAmt(payment.getPayTotalAmt().multiply(payment.getExchangeRate()).setScale(2, RoundingMode.HALF_UP));
        }

        // 保存预收款和承兑汇票核销关系
        saveWriteOffRelations(payment.getPayId(), reqVO);

        paymentOrderMapper.insert(payment);

        // 返回
        return payment.getPayId();
    }

    /**
     * 收款金额处理
     *
     * @param reqVO
     * @param payment
     */
    private void dealBillNew(PaymentAditReqVO reqVO, PaymentOrderDO payment) {

        BigDecimal payTotal = BigDecimal.ZERO;
        BigDecimal billTotal = BigDecimal.ZERO;
        if(reqVO.getInBillTypeList().contains(0)){//直接入账
            if(payment.getFormType() == 1) {
                if(ObjUtilX.isEmpty(reqVO.getInBillAccountId())){
                    throw new BizException("5001", "请选择入账账户");
                }
                if(ObjUtilX.isEmpty(reqVO.getInBillAmt()) || reqVO.getInBillAmt().compareTo(BigDecimal.ZERO) == 0){
                    throw new BizException("5001", "请输入入账金额");
                }
                //查询账户币种
                BankConfigDO bankConfigDO = bankConfigMapper.selectById(reqVO.getInBillAccountId());
                if(ObjUtilX.isEmpty(bankConfigDO)){
                    throw new BizException("5001", "入账账户不存在");
                }
                if(!payment.getCurrencyDictId().equals(bankConfigDO.getCurrencyDictId())){
                    throw new BizException("5001", "入账账户币种和单据币种不匹配");
                }
            }else{
                if(ObjUtilX.isEmpty(reqVO.getInBillAccountId())){
                    throw new BizException("5001", "请选择入出账户");
                }
                if(ObjUtilX.isEmpty(reqVO.getInBillAmt()) || reqVO.getInBillAmt().compareTo(BigDecimal.ZERO) == 0){
                    throw new BizException("5001", "请输入出账金额");
                }
                //查询账户币种
                BankConfigDO bankConfigDO = bankConfigMapper.selectById(reqVO.getInBillAccountId());
                if(ObjUtilX.isEmpty(bankConfigDO)){
                    throw new BizException("5001", "出账账户不存在");
                }
                if(!payment.getCurrencyDictId().equals(bankConfigDO.getCurrencyDictId())){
                    throw new BizException("5001", "出账账户币种和单据币种不匹配");
                }
            }

            payTotal = payTotal.add(reqVO.getInBillAmt());
        }else{
            payment.setInBillAmt(BigDecimal.ZERO);
        }

        if(reqVO.getInBillTypeList().contains(1)){// 预收款
            if(ObjUtilX.isEmpty(reqVO.getAdvancePayList())){
                if(payment.getFormType() == 1) {
                    throw new BizException("5001", "请选择预收款");
                }else{
                    throw new BizException("5001", "请选择预付款");
                }
            }
            // 预收款总额不能超过 剩余应收
            BigDecimal oldReceiveableAmt = BigDecimal.ZERO;
            Short formType = reqVO.getFormType();
            //todo 请求里的 reqVO.getRelationOrders().amt 每一条金额都需在下面 for 循环要校验，用 relationOrderId 做MAP，先让前端校验
            if (payment.getHaveApply() == 1){
                List<PaymentApplyDO> applys = paymentApplyMapper.selectBatchIds(payment.getRelatedOrderIds());
                for (PaymentApplyDO apply : applys) {
                    // 之前的剩余可申请金额 累加
                    oldReceiveableAmt = oldReceiveableAmt.add(apply.getReceiveableAmt());
                }
            }else {
                List<ShouldPaymentDO> shouldPaymentList = shouldPaymentMapper.selectBatchIds(payment.getRelatedOrderIds());
                for (ShouldPaymentDO shouldPaymentDO : shouldPaymentList) {
                    // 之前的剩余付款金额 累加
                    oldReceiveableAmt = oldReceiveableAmt.add(shouldPaymentDO.getApplyableAmt());
                }
            }
            BigDecimal writeAmt = reqVO.getAdvancePayList().stream().map(json -> new BigDecimal(json.getOrDefault("payWriteAmt", "0").toString()))
//                            .filter(billAmt -> billAmt != null) // 过滤掉空值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);// 汇总
            if (writeAmt.compareTo(oldReceiveableAmt.subtract(payment.getDiscountAmt())) > 0) {//剩余金额
                if(formType == 1) {
                    throw new BizException("5001", "预收款总核销金额不能超过剩余收款总额减收款折扣金额");
                }else{
                    throw new BizException("5001", "预付款总核销金额不能超过剩余付款总额减付款折扣金额");
                }
            }
            payTotal = payTotal.add(writeAmt);
        }

        if(reqVO.getInBillTypeList().contains(2)){// 承兑汇票
            if(ObjUtilX.isEmpty(reqVO.getBillList())){
                throw new BizException("5001", "请选择要核销的承兑汇票");
            }
            AcceptBillQueryReqVO reqVO1 = new AcceptBillQueryReqVO();
            reqVO1.setIdList(reqVO.getBillList());
            if(payment.getFormType() == 1) {//销售收款
                reqVO1.setBillDirection(1);//收票方向
                reqVO1.setDrawerId(payment.getCustomerId());// 出票人为客户
            }else{
                reqVO1.setBillDirection(0);//出票方向
                reqVO1.setPayeeId(payment.getCustomerId());// 收票人为供应商
            }

            List<AcceptBillDO> billList = acceptBillMapper.selectList(reqVO1);
            if(ObjUtilX.isEmpty(billList)){
                throw new BizException("5001", "找不到承兑汇票");
            }
            for (AcceptBillDO billDO : billList) {
                if(billDO.getDataStatus() != DataStatusEnum.APPROVED.key){
                    throw new BizException("5001",  "勾选的承兑汇票["+billDO.getTicketBillPackageNo() +"]不是已审核状态");
                }
                if(billDO.getUsageStatus() != UsageStatusEnum.NOT_SETTLED.key){
                    throw new BizException("5001",  "勾选的承兑汇票["+billDO.getTicketBillPackageNo() +"]已核销");
                }
                billTotal = billTotal.add(billDO.getBillAmt());
            }

            payTotal = payTotal.add(billTotal);
        }
        //加上折扣金额
        if(ObjUtilX.isNotEmpty(payment.getDiscountAmt())){
            payTotal = payTotal.add(payment.getDiscountAmt());
        }
        // 收款总额
        payment.setPayTotalAmt(payTotal);
    }

    private void dealBill(PaymentAditReqVO reqVO, PaymentOrderDO payment) {

        BigDecimal payTotal = BigDecimal.ZERO;
        BigDecimal billTotal = BigDecimal.ZERO;
        if(reqVO.getInBillTypeList().contains(0)){//直接入账
            if(payment.getFormType() == 1) {
                if(ObjUtilX.isEmpty(reqVO.getInBillAccountId())){
                    throw new BizException("5001", "请选择入账账户");
                }
                if(ObjUtilX.isEmpty(reqVO.getInBillAmt()) || reqVO.getInBillAmt().compareTo(BigDecimal.ZERO) == 0){
                    throw new BizException("5001", "请输入入账金额");
                }
                //查询账户币种
                BankConfigDO bankConfigDO = bankConfigMapper.selectById(reqVO.getInBillAccountId());
                if(ObjUtilX.isEmpty(bankConfigDO)){
                    throw new BizException("5001", "入账账户不存在");
                }
                if(!payment.getCurrencyDictId().equals(bankConfigDO.getCurrencyDictId())){
                    throw new BizException("5001", "入账账户币种和单据币种不匹配");
                }
            }else{
                if(ObjUtilX.isEmpty(reqVO.getInBillAccountId())){
                    throw new BizException("5001", "请选择入出账户");
                }
                if(ObjUtilX.isEmpty(reqVO.getInBillAmt()) || reqVO.getInBillAmt().compareTo(BigDecimal.ZERO) == 0){
                    throw new BizException("5001", "请输入出账金额");
                }
                //查询账户币种
                BankConfigDO bankConfigDO = bankConfigMapper.selectById(reqVO.getInBillAccountId());
                if(ObjUtilX.isEmpty(bankConfigDO)){
                    throw new BizException("5001", "出账账户不存在");
                }
                if(!payment.getCurrencyDictId().equals(bankConfigDO.getCurrencyDictId())){
                    throw new BizException("5001", "出账账户币种和单据币种不匹配");
                }
            }

            payTotal = payTotal.add(reqVO.getInBillAmt());
        }else{
            payment.setInBillAmt(BigDecimal.ZERO);
        }

        if(reqVO.getInBillTypeList().contains(1)){// 预收款
            if(ObjUtilX.isEmpty(reqVO.getAdvancePayList())){
                if(payment.getFormType() == 1) {
                    throw new BizException("5001", "请选择预收款");
                }else{
                    throw new BizException("5001", "请选择预付款");
                }
            }
            // 预收款总额不能超过 剩余应收
            BigDecimal oldReceiveableAmt = BigDecimal.ZERO;
            Short formType = null;
            if (payment.getReciveApplyId() != null){
                PaymentApplyDO apply = paymentApplyMapper.selectById(payment.getReciveApplyId());
                // 之前的剩余可申请金额
                oldReceiveableAmt = apply.getReceiveableAmt();
                formType = apply.getFormType();
            }else {
                ShouldPaymentDO shouldPaymentDO = shouldPaymentMapper.selectById(payment.getRecivePaymentId());
                oldReceiveableAmt = shouldPaymentDO.getApplyableAmt();
                formType = shouldPaymentDO.getFormType();
            }
            BigDecimal writeAmt = reqVO.getAdvancePayList().stream().map(json -> new BigDecimal(json.getOrDefault("payWriteAmt", "0").toString()))
//                            .filter(billAmt -> billAmt != null) // 过滤掉空值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);// 汇总
            if (writeAmt.compareTo(oldReceiveableAmt.subtract(payment.getDiscountAmt())) > 0) {//剩余金额
                if(formType == 1) {
                    throw new BizException("5001", "预收款总核销金额不能超过剩余收款总额减收款折扣金额");
                }else{
                    throw new BizException("5001", "预付款总核销金额不能超过剩余付款总额减付款折扣金额");
                }
            }
            payTotal = payTotal.add(writeAmt);
        }

        if(reqVO.getInBillTypeList().contains(2)){// 承兑汇票
            if(ObjUtilX.isEmpty(reqVO.getBillList())){
                throw new BizException("5001", "请选择要核销的承兑汇票");
            }
            AcceptBillQueryReqVO reqVO1 = new AcceptBillQueryReqVO();
            reqVO1.setIdList(reqVO.getBillList());
            if(payment.getFormType() == 1) {//销售收款
                reqVO1.setBillDirection(1);//收票方向
                reqVO1.setDrawerId(payment.getCustomerId());// 出票人为客户
            }else{
                reqVO1.setBillDirection(0);//出票方向
                reqVO1.setPayeeId(payment.getCustomerId());// 收票人为供应商
            }

            List<AcceptBillDO> billList = acceptBillMapper.selectList(reqVO1);
            if(ObjUtilX.isEmpty(billList)){
                throw new BizException("5001", "找不到承兑汇票");
            }
            for (AcceptBillDO billDO : billList) {
                if(billDO.getDataStatus() != DataStatusEnum.APPROVED.key){
                    throw new BizException("5001",  "勾选的承兑汇票["+billDO.getTicketBillPackageNo() +"]不是已审核状态");
                }
                if(billDO.getUsageStatus() != UsageStatusEnum.NOT_SETTLED.key){
                    throw new BizException("5001",  "勾选的承兑汇票["+billDO.getTicketBillPackageNo() +"]已核销");
                }
                billTotal = billTotal.add(billDO.getBillAmt());
            }

            payTotal = payTotal.add(billTotal);
        }
        //加上折扣金额
        if(ObjUtilX.isNotEmpty(payment.getDiscountAmt())){
            payTotal = payTotal.add(payment.getDiscountAmt());
        }
        // 收款总额
        payment.setPayTotalAmt(payTotal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long paymentEditNew(PaymentAditReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getRelationOrderList())){
            throw new BizException("5001", "请选择关联单据");
        }
        if(ObjUtilX.isEmpty(reqVO.getExchangeRate())){
            throw new BizException("5001", "请传入汇率");
        }
        // 校验存在
        PaymentOrderDO exist = this.paymentValidateExists(reqVO.getPayId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(exist, reqVO);

        // 更新
        PaymentOrderDO payment = BeanUtilX.copy(reqVO, PaymentOrderDO::new);
        payment.setPayCode(exist.getPayCode());

        ////收款金额处理
        dealBillNew(reqVO, payment);

        // 添加关联上游单据
        List<PaymentRelationDO> relationOrders = new ArrayList<>(reqVO.getRelationOrderList().size());
        List<Long> relatedOrderIds = new ArrayList<>(reqVO.getRelationOrderList().size());
        BigDecimal payRealTotal = BigDecimal.ZERO;
        for (PaymentRelationAditReqVO order : reqVO.getRelationOrderList()) {
            relatedOrderIds.add(order.getRelatedOrderId());
            PaymentRelationDO relation = BeanUtilX.copy(order, PaymentRelationDO::new);
            relation.setOrderId(payment.getPayId());
            relation.setFormType(reqVO.getFormType().intValue());
            if(reqVO.getFormType() == 1) {// 销售收款
                relation.setFormType(OrderTypeEnum.SALE_PAY.getType());
            }else{// 采购付款
                relation.setFormType(OrderTypeEnum.PURCHASE_PAY.getType());
            }
            relationOrders.add(relation);
            payRealTotal = payRealTotal.add(order.getAmt());
        }
        paymentRelationMapper.deleteByOrderId(payment.getPayId());
        paymentRelationMapper.insertBatch(relationOrders);
        // 主单据添加关联ID集合
        payment.setRelatedOrderIds(relatedOrderIds);

        //超收金额
        BigDecimal overAmt = payment.getRemainingAmt().compareTo(BigDecimal.ZERO)>0 ? BigDecimal.ZERO : payment.getRemainingAmt().abs();
        payment.setOverAmt(overAmt);
        //更新本币币种及汇率
        if(payment.getExchangeRate() != null){
            payment.setLocalCurrencyPayTotalAmt(payment.getPayTotalAmt().multiply(payment.getExchangeRate()).setScale(2, RoundingMode.HALF_UP));
        }

        // 更新预收款和承兑汇票核销关系
        saveWriteOffRelations(payment.getPayId(), reqVO);

        paymentOrderMapper.updateById(payment);

        // 绑定附件
        fileService.saveFile(payment.getPayId(), reqVO.getFileList(), FileTableEnum.REC_FILE);
        // 返回
        return payment.getPayId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paymentDel(Long payId) {
        // 校验存在
        this.paymentValidateExists(payId);
        // 删除预收款和承兑汇票核销关系
        paymentWriteOffService.deletePaymentWriteOffRelations(payId);
        // 删除
        paymentOrderMapper.deleteById(payId);
        // 删除关联单据
        paymentRelationMapper.deleteByOrderId(payId);
    }

    private PaymentOrderDO paymentValidateExists(Long payId) {
        PaymentOrderDO payment = paymentOrderMapper.selectById(payId);
        if (payment == null) {
            // throw exception(PAYMENT_NOT_EXISTS);
            throw new BizException("5001", "单据不存在");
        }
        return payment;
    }

    @Override
    public PaymentRespVO paymentDetail(Long payId) {
        PaymentOrderDO data = paymentOrderMapper.selectById(payId);
        // 查询上游
        PaymentRespVO resp = BeanUtilX.copy(data, PaymentRespVO::new);
        if (data.getReciveApplyId() != null) {
            PaymentApplyDO applyDO = paymentApplyMapper.selectById(data.getReciveApplyId());
            if(Objects.nonNull(applyDO)) {
                resp.setRemainingAmt(applyDO.getReceiveableAmt());//本次剩余收款总额
                resp.setShouldPayTotalAmt(applyDO.getApplyTotalAmt());// 本次收款总额
            }
        }else {
            ShouldPaymentDO shouldPaymentDO = shouldPaymentMapper.selectById(data.getRecivePaymentId());

            // 根据主表id 查询明细
//            ShouldPaymentDetailQueryReqVO reqVO = new ShouldPaymentDetailQueryReqVO();
//            reqVO.setPaymentId(data.getRecivePaymentId());
//            reqVO.setFormType(data.getFormType());
//            List<ShouldPaymentDetailRespVO> list = paymentDetailService.shouldPaymentDetailList(reqVO);
//            BigDecimal inclCurrentApplyAmt = BigDecimal.ZERO;
//            for (ShouldPaymentDetailRespVO detail : list) {
//                inclCurrentApplyAmt = inclCurrentApplyAmt.add(detail.getInclCurrentReceiveAmt());
//            }
            resp.setShouldPayTotalAmt(shouldPaymentDO.getTotalAmt().subtract(shouldPaymentDO.getRealAmt()));// 本次收款总额
            resp.setRemainingAmt(shouldPaymentDO.getTotalAmt().subtract(shouldPaymentDO.getRealAmt()));//本次剩余收款总额
        }


        fillVoProperties(resp);
        resp.setFileList(fileService.getFiles(payId));

        // 从中间表读取预收款和承兑汇票核销数据
        fillWriteOffData(resp);

        // 核销记录 (保留原有逻辑作为备用)
        if(data.getInBillTypeList() != null && data.getInBillTypeList().contains(1)) {
            List<AdvancePaymentWrittenDO> writtenDOS = advancePaymentWrittenMapper.selectList(LambdaQueryWrapperX.<AdvancePaymentWrittenDO>lambdaQueryX()
                    .in(AdvancePaymentWrittenDO::getSourceOrderId, data.getPayId())
            );
            AtomicInteger i = new AtomicInteger(1);
            List<AdvancePaymentWrittenRespVO> collect = writtenDOS.stream().map(item -> {
                AdvancePaymentWrittenRespVO convert = BeanUtilX.copy(item, AdvancePaymentWrittenRespVO::new);
                convert.setSort(i.getAndIncrement());
                return convert;
            }).collect(Collectors.toList());
            resp.setAdvancePaymentWrittenList(collect);
        }
        // 查询承兑汇票记录 (保留原有逻辑作为备用)
        if(data.getInBillTypeList() != null && data.getInBillTypeList().contains(2) && data.getBillList() != null) {
            List<AcceptBillDO> billList = acceptBillMapper.selectList(LambdaQueryWrapperX.<AcceptBillDO>lambdaQueryX()
                    .in(AcceptBillDO::getAcceptBillId, data.getBillList())
            );
            AtomicInteger i = new AtomicInteger(1);
            List<AcceptBillRespVO> collect = billList.stream().map(item -> {
                AcceptBillRespVO convert = BeanUtilX.copy(item, AcceptBillRespVO::new);
                convert.setSort(i.getAndIncrement());
                return convert;
            }).collect(Collectors.toList());
            resp.setAcceptBillList(collect);
        }
        // 设置审批任务 ID
        Optional.ofNullable(approveService.detailByObjId(payId.toString()))
                .ifPresent(approveTask -> resp.setApproveTaskId(approveTask.getApproveTaskId()));

        return resp;
    }

    @Override
    public PaymentRespVO paymentDetailNew(Long payId) {
        PaymentOrderDO data = paymentOrderMapper.selectById(payId);
        // 查询上游
        PaymentRespVO resp = BeanUtilX.copy(data, PaymentRespVO::new);
        BigDecimal remainingAmt = BigDecimal.ZERO;
        BigDecimal shouldPayTotalAmt = BigDecimal.ZERO;

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.ACCOUNTS_PAYABLE_STATUS.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode(), SystemDictEnum.RECEIVABLE_PAYABLE_STATUS.getDictCode(),
                SystemDictEnum.CURRENCY.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);
        if (data.getHaveApply() == 1) {
            List<PaymentApplyDO> applyList = paymentApplyMapper.selectBatchIds(data.getRelatedOrderIds());
            Map<Long, PaymentApplyDO> respMap = new HashMap<>(applyList.size());
            Map<Long, ShouldPaymentDO> shouldMap = new HashMap<>(applyList.size());
            for (PaymentApplyDO applyDO : applyList) {
                remainingAmt = remainingAmt.add(applyDO.getReceiveableAmt());
                shouldPayTotalAmt = shouldPayTotalAmt.add(applyDO.getApplyTotalAmt());

                respMap.put(applyDO.getReciveApplyId(), applyDO);

                ShouldPaymentDO should = shouldPaymentMapper.selectById(applyDO.getPaymentId());
                shouldMap.put(applyDO.getReciveApplyId(), should);
            }

            // 返回关联单据  付款申请
            List<PaymentRelationDO> relationDOS = paymentRelationMapper.selectListByOrderId(payId, 0);
            List<PaymentRelationRespVO> respList = relationDOS.stream().map((item) -> {
                PaymentApplyDO apply = respMap.get(item.getRelatedOrderId());
                if(ObjUtilX.isEmpty(apply)){
                    return null;
                }
                PaymentRelationRespVO temp = new PaymentRelationRespVO();
                BeanUtil.copyProperties(item, temp);
                //ShouldPaymentDO should = shouldMap.get(temp.getRelatedOrderId());

                temp.setFormStatus(apply.getPaymentStatus().intValue());
                // 付款申请单据来源取应付账款的 单据来源，还是改为 固定 应付账款
                temp.setSourceFormType(OrderTypeEnum.PURCHASE_SHOULD_PAY.getType());
                temp.setSourceOrderCode(apply.getSourceOrderCode());
                temp.setRelatedOrderCode(apply.getApplyCode());
                temp.setShouldAmt(apply.getApplyTotalAmt());
                temp.setAbleAmt(apply.getApplyTotalAmt().subtract(apply.getReceivedAmt()));
                temp.setLocalCurrencyDictId(apply.getLocalCurrencyDictId());
                temp.setExchangeRate(apply.getExchangeRate());

                // 本币币种
                String localCurrencyDictId = temp.getLocalCurrencyDictId();
                if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                    localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                    temp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
                }
                // 应付账款状态
                if(temp.getFormStatus() != null){
                    if (temp.getFormStatus() == 0) {
                        temp.setFormStatusName("未付款");
                    }else if (temp.getFormStatus() == 1) {
                        temp.setFormStatusName("部分付款");
                    }else{
                        temp.setFormStatusName("已付款");
                    }
                }

                return temp;
            }).toList();

            resp.setRelationOrderList(respList);
        }else {
            if (ObjUtilX.isNotEmpty(data.getRelatedOrderIds())) {
                //List<ShouldPaymentDO> shouldPaymentList = shouldPaymentMapper.selectBatchIds(data.getRelatedOrderIds());
                ShouldPaymentQueryReqVO reqVO = new ShouldPaymentQueryReqVO();
                reqVO.setPaymentIdList(data.getRelatedOrderIds());
                reqVO.setFormType(data.getFormType());
                List<ShouldPaymentRespVO> shouldPaymentList = shouldPaymentService.shouldPaymentList(reqVO);
                Map<Long, ShouldPaymentRespVO> respMap = new HashMap<>(shouldPaymentList.size());
                for (ShouldPaymentRespVO shouldPaymentDO : shouldPaymentList) {
                    remainingAmt = remainingAmt.add(shouldPaymentDO.getTotalAmt().subtract(shouldPaymentDO.getRealAmt()));
                    shouldPayTotalAmt = shouldPayTotalAmt.add(shouldPaymentDO.getTotalAmt().subtract(shouldPaymentDO.getRealAmt()));

                    respMap.put(shouldPaymentDO.getPaymentId(), shouldPaymentDO);
                }

                // 返回关联单据 应收/应付
                List<PaymentRelationDO> relationDOS = paymentRelationMapper.selectListByOrderId(payId, 0);
                List<PaymentRelationRespVO> respList = relationDOS.stream().map((item) -> {
                    PaymentRelationRespVO temp = new PaymentRelationRespVO();
                    BeanUtil.copyProperties(item, temp);
                    ShouldPaymentRespVO should = respMap.get(temp.getRelatedOrderId());

                    temp.setFormStatus(should.getAccountsPayableStatus().intValue());
                    temp.setSourceFormType(should.getSourceFormType().intValue());
                    temp.setSourceOrderCode(should.getSourceOrderCode());
                    temp.setLocalCurrencyDictId(should.getLocalCurrencyDictId());
                    temp.setExchangeRate(should.getExchangeRate());
                    temp.setRelatedOrderCode(should.getAccountsPayableNumber());
                    temp.setShouldAmt(should.getTotalAmt());
                    temp.setAbleAmt(should.getUnpaidAmt());
                    // 本币币种
                    String localCurrencyDictId = temp.getLocalCurrencyDictId();
                    if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                        localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                        temp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
                    }
                    // 应付账款状态
                    String statusKey = "";
                    if(temp.getFormStatus() != null){
                        if (data.getFormType() == 2) {
                            statusKey = SystemDictEnum.ACCOUNTS_PAYABLE_STATUS.getDictCode() + "-" + temp.getFormStatus();
                        }else{
                            statusKey =  SystemDictEnum.RECEIVABLE_PAYABLE_STATUS.getDictCode() + "-" + temp.getFormStatus();
                        }
                        temp.setFormStatusName(dictMap.getOrDefault(statusKey, "无"));
                    }
                    return temp;
                }).toList();

                resp.setRelationOrderList(respList);
            }
        }
        resp.setRemainingAmt(remainingAmt);//本次剩余收款总额
        resp.setShouldPayTotalAmt(shouldPayTotalAmt);// 本次收款总额

        fillVoProperties(resp);
        resp.setFileList(fileService.getFiles(payId));

        // 从中间表读取预收款和承兑汇票核销数据
        //fillWriteOffData(resp);
        // 从中间表读取预收款核销数据，转换为JSON格式兼容原有接口
        Long paymentId = resp.getPayId();
        //List<JSONObject> advancePayList = paymentWriteOffService.getAdvancePaymentWriteOffAsJson(paymentId);


        // 从中间表读取承兑汇票核销数据，转换为ID列表兼容原有接口
        List<Long> billIdList = paymentWriteOffService.getAcceptBillWriteOffAsIdList(paymentId);
        resp.setBillList(billIdList);

        // 核销记录 (保留原有逻辑作为备用)
        if(data.getInBillTypeList() != null && data.getInBillTypeList().contains(1)) {
            List<PaymentRelationDO> relations = paymentWriteOffService.getAdvancePaymentWriteOff(paymentId);
            List<Long> advancePayIdList = new ArrayList<>();
            List<JSONObject> advancePayList = new ArrayList<>();
            List<AdvancePaymentWrittenRespVO> collect = new ArrayList<>();

            //没核销也要展示预收款
            for (PaymentRelationDO relation : relations) {
                JSONObject json = new JSONObject();
                json.put("advanceId", relation.getRelatedOrderId());
                json.put("sort", relation.getSort());
                json.put("payWriteAmt", relation.getAmt());
                json.put("advanceCode", relation.getRelatedOrderCode());
                advancePayList.add(json);
                advancePayIdList.add(relation.getRelatedOrderId());
                AdvancePaymentDO advancePaymentDO = advancePaymentMapper.selectById(relation.getRelatedOrderId());
                AdvancePaymentWrittenRespVO written = BeanUtilX.copy(advancePaymentDO, AdvancePaymentWrittenRespVO::new);
                written.setSort(relation.getSort());
                written.setPayWriteAmt(relation.getAmt());
                collect.add(written);
            }
            resp.setAdvancePaymentWrittenList(collect);
            resp.setAdvancePayList(advancePayList);
        }
        // 查询承兑汇票记录 (保留原有逻辑作为备用)
        if(data.getInBillTypeList() != null && data.getInBillTypeList().contains(2) && data.getBillList() != null) {
            List<AcceptBillDO> billList = acceptBillMapper.selectList(LambdaQueryWrapperX.<AcceptBillDO>lambdaQueryX()
                    //.in(AcceptBillDO::getAcceptBillId, data.getBillList())
                    .in(AcceptBillDO::getAcceptBillId, billIdList)
            );
            AtomicInteger i = new AtomicInteger(1);
            List<AcceptBillRespVO> collect = billList.stream().map(item -> {
                AcceptBillRespVO convert = BeanUtilX.copy(item, AcceptBillRespVO::new);
                convert.setSort(i.getAndIncrement());
                return convert;
            }).collect(Collectors.toList());
            resp.setAcceptBillList(collect);
        }
        // 设置审批任务 ID
        Optional.ofNullable(approveService.detailByObjId(payId.toString()))
                .ifPresent(approveTask -> resp.setApproveTaskId(approveTask.getApproveTaskId()));

        return resp;
    }

    @Override
    public List<PaymentRespVO> paymentList(PaymentQueryReqVO reqVO) {
        List<PaymentOrderDO> data = paymentOrderMapper.selectList(reqVO);
        return BeanUtilX.copy(data, PaymentRespVO::new);
    }

    @Override
    public PageResult<PaymentRespVO> paymentPage(PaymentPageReqVO reqVO) {

        // 客户列表
        if(ObjUtilX.isNotEmpty(reqVO.getCustomerName())) {
            List<Long> customerIdList = null;
            if (reqVO.getFormType() == 1) {//销售结算池
                //查询客户名称
                ERPCustomerQueryReqVO custmReq = new ERPCustomerQueryReqVO();
                custmReq.setCustomerName(reqVO.getCustomerName());
                List<ERPCustomerDO> customerList = erpCustomerMapper.selectList(custmReq);
                if (CollUtilX.isEmpty(customerList)) {
                    return PageResult.empty();
                }
                customerIdList = customerList.stream().map(ERPCustomerDO::getCustomerId).collect(Collectors.toList());
            } else {//采购结算池
                //查询供应商
                ERPSupplierQueryReqVO custmReq = new ERPSupplierQueryReqVO();
                custmReq.setSupplierName(reqVO.getCustomerName());
                List<ERPSupplierDO> customerList = erpSupplierMapper.selectList(custmReq);
                if (CollUtilX.isEmpty(customerList)) {
                    return PageResult.empty();
                }
                customerIdList = customerList.stream().map(ERPSupplierDO::getSupplierId).collect(Collectors.toList());
            }
            reqVO.setCustomerIdList(customerIdList);
        }

        PageResult<PaymentOrderDO> data = paymentOrderMapper.selectPage(reqVO);
        if (CollUtilX.isEmpty(data.getList())){
            return PageResult.empty();
        }
        PageResult<PaymentRespVO> result = BeanUtilX.copy(data, PaymentRespVO::new);
        //属性填充
        batchFillVoProperties(result.getList());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> paymentDelBatch(IdReq reqVO) {
        //获取对象属性名
        String id = EntityUtilX.getPropertyName(PaymentOrderDO:: getPayId);
        String code = EntityUtilX.getPropertyName(PaymentOrderDO::getPayCode);
        // 删除关联单据
        paymentRelationMapper.deleteByOrderIds(reqVO.getIdList());
        return erpBaseService.batchDelete(reqVO.getIdList(), PaymentOrderDO.class, null, id, code);
    }

    @Override
    public BatchResult paymentApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PaymentOrderDO> list = paymentOrderMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PaymentOrderDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus().intValue());

                // 封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder()
                        .conditionMap(null)
                        .flowFunctionCode(flowFunctionCode)
                        .flowConfigBO(flowConfigBO)
                        .buttonType(buttonType)
                        .build();

                // 流程处理
                FailItem failItem = approveHandler.process(item, flowApproveBO);
                if (ObjUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                exception.printStackTrace();
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getPayCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (PaymentOrderDO item : list) {
                String reason = reasonMap.get(item.getPayCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPayId());
                    messageInfoBO.setObjCode(item.getPayCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPayId());
                    messageInfoBO.setObjCode(item.getPayCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }


    @Override
    public Object paymentFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PaymentOrderDO item = this.paymentValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return callbackHandler.handleFlowCallback(item, flowCallbackBO);
    }


    /**
     * 审批拒绝 回调业务逻辑处理
     *
     * @param reqVO
     * @param dataStatus
     */
    private boolean handleApproveRefuse(FlowCallback reqVO, Integer dataStatus) {
//        Integer count = saleShipNoticeApproveHandler.updateBusinessData(Long.parseLong(reqVO.getObjId()), dataStatus);

//        return  count == 1 ? true : false;
        return false;
    }
    /**
     * 审核成功 回调业务逻辑处理
     *
     * @param reqVO
     * @param dataStatus
     */
    private boolean handleApprove(FlowCallback reqVO, Integer dataStatus) {
//        Integer count = approveHandler.updateBusinessData(Long.parseLong(reqVO.getObjId()), dataStatus);
//
//        return  count == 1 ? true : false;
        return false;
    }

    /**
     * 反审核成功 回调业务逻辑处理
     *
     * @param reqVO
     * @param dataStatus
     */
    private boolean handleNotApprove(FlowCallback reqVO, Integer dataStatus) {
//        saleShipNoticeApproveHandler.updateBusinessData(Long.parseLong(reqVO.getObjId()),0);
        return false;
    }

    /**
     * 作废成功 回调业务逻辑处理
     *
     * @param reqVO
     * @param dataStatus
     */
    private boolean handleCancel(FlowCallback reqVO, Integer dataStatus) {
//        saleShipNoticeApproveHandler.updateBusinessData(Long.parseLong(reqVO.getObjId()),2);
        return false;
    }

    /**
     * 反作废成功 回调业务逻辑处理
     *
     * @param reqVO
     * @param dataStatus
     */
    private boolean handleNotCancel(FlowCallback reqVO, Integer dataStatus) {
//        saleShipNoticeApproveHandler.updateBusinessData(Long.parseLong(reqVO.getObjId()),3);
        return false;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(PaymentRespVO respVO) {
        List<PaymentRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
//        //查询客户名称
//        String customerName = "";
//        if(respVO.getFormType() == 1){//销售结算池
//            //查询客户名称
//            customerName = compositeService.getERPCustomerNameById(respVO.getCustomerId());
//        }else{//采购结算池
//            //查询供应商
//            customerName = erpSupplierService.getErpSupplierById(respVO.getCustomerId());
//        }
//        respVO.setCustomerName(customerName);
//
//        //查询字典库信息
////        DictQueryReqVO dictQueryReqVO = new DictQueryReqVO();
////        dictQueryReqVO.setCategory(1);
////        Map<Long,String> dictMap = compositeService.getTenantDictMap(dictQueryReqVO);
//
//        //查询负责人
//        String directorName = compositeService.getEmpNameById(respVO.getDirectorId());
//        respVO.setDirectorName(directorName);
//
//        //查询责任部门
//        String directorOrgName = compositeService.getOrgNameById(respVO.getDirectorOrgId().toString());
//        respVO.setDirectorOrgName(directorOrgName);
//
//        //查询入账账户
//        //入账账户
//        String account = erpBaseService.getBankAccountById(respVO.getInBillAccountId());
//        respVO.setInBillAccountName(account);
//
//        //查询字典库信息
//        List<String> dictCodeList = Arrays.asList(SystemDictEnum.IN_BILL_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
//        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);
//
//        // 入账方式
//        String inBillTypeStr = "";
//        for(Integer inBillType :  respVO.getInBillTypeList()){
//            if(StrUtilX.isEmpty(inBillTypeStr)){
//                String key = SystemDictEnum.IN_BILL_TYPE.getDictCode() + "-" + inBillType.toString();
//                inBillTypeStr =  inBillTypeStr + dictMap.get(key);
//            } else {
//                String key = SystemDictEnum.IN_BILL_TYPE.getDictCode() + "-" + inBillType.toString();
//                inBillTypeStr =  inBillTypeStr + "," + dictMap.get(inBillType.toString());
//            }
//        }
//        respVO.setInBillTypeStr(inBillTypeStr);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respList
     */
    private void batchFillVoProperties(List<PaymentRespVO> respList) {

        if (CollUtilX.isEmpty(respList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.IN_BILL_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode(),
                SystemDictEnum.CURRENCY.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询责任部门
        Map<String, String> orgNameMap = compositeService.getOrgNameMap();

        Map<Long, String> empNameMap = new HashMap<>();
        Map<Long, String> customerNameMap = new HashMap<>();
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> empIdList = new ArrayList<>();
        List<Long> customerIdList = new ArrayList<>();
        List<Long> inAccountIdList = new ArrayList<>();
        for (PaymentRespVO deatilResp: respList){
            empIdList.add(deatilResp.getDirectorId());
            customerIdList.add(deatilResp.getCustomerId());
            inAccountIdList.add(deatilResp.getInBillAccountId());
        }

        //查询客户名称
        if(respList.get(0).getFormType() == 1) {//销售结算池
            //查询客户名称
            customerNameMap = compositeService.getERPCustomerNameByIdList(customerIdList);
        } else {//采购结算池
            //查询供应商
            customerNameMap = erpSupplierService.erpSupplierNameMap(customerIdList);
        }

        //入账账户
        Map<Long, String> inAccountMap = erpBaseService.getBankAccountByIdList(inAccountIdList);

        //查询负责人
        empNameMap = compositeService.getEmpNameByIdList(empIdList);

        for (PaymentRespVO deatilResp: respList){

            //客户名称
            deatilResp.setCustomerName(customerNameMap.get(deatilResp.getCustomerId()));

            //入账账户
            if(deatilResp.getInBillAccountId() != null){
                deatilResp.setInBillAccountName(inAccountMap.get(deatilResp.getInBillAccountId()));
            }

            // 入账方式
            String inBillTypeStr = "";
            for(Integer inBillType :  deatilResp.getInBillTypeList()){
                if(StrUtilX.isEmpty(inBillTypeStr)){
                    String key = SystemDictEnum.IN_BILL_TYPE.getDictCode() + "-" + inBillType.toString();
                    inBillTypeStr =  inBillTypeStr + dictMap.get(key);
                } else {
                    String key = SystemDictEnum.IN_BILL_TYPE.getDictCode() + "-" + inBillType.toString();
                    inBillTypeStr =  inBillTypeStr + "," + dictMap.get(key);
                }
            }
            deatilResp.setInBillTypeStr(inBillTypeStr);

            //审核状态
            if(deatilResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + deatilResp.getDataStatus();
                deatilResp.setDataStatusDictName(dictMap.get(dataStatus));
            }

            // 本币币种
            String localCurrencyDictId = deatilResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                deatilResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            //责任人属性填充
            deatilResp.setDirectorName(empNameMap.get(deatilResp.getDirectorId()));

            //责任部门属性填充
            deatilResp.setDirectorOrgName(orgNameMap.get(deatilResp.getDirectorOrgId().toString()));

        }
    }

    /**
     * 保存预收款和承兑汇票核销关系
     *
     * @param paymentId 收款/付款单ID
     * @param reqVO 请求VO
     */
    private void saveWriteOffRelations(Long paymentId, PaymentOrderBaseVO reqVO) {
        // 保存预收款核销关系
        if (reqVO.getInBillTypeList() != null && reqVO.getInBillTypeList().contains(1)) {
            paymentWriteOffService.saveAdvancePaymentWriteOff(paymentId, reqVO.getFormType(), reqVO.getAdvancePayList());
        }

        // 保存承兑汇票核销关系
        if (reqVO.getInBillTypeList() != null && reqVO.getInBillTypeList().contains(2)) {
            paymentWriteOffService.saveAcceptBillWriteOff(paymentId, reqVO.getFormType(), reqVO.getBillList(), null);
        }
    }

    /**
     * 填充核销数据到响应VO
     *
     * @param resp 响应VO
     */
    private void fillWriteOffData(PaymentRespVO resp) {
        Long paymentId = resp.getPayId();

        // 从中间表读取预收款核销数据，转换为JSON格式兼容原有接口
        List<JSONObject> advancePayList = paymentWriteOffService.getAdvancePaymentWriteOffAsJson(paymentId);
        resp.setAdvancePayList(advancePayList);

        // 从中间表读取承兑汇票核销数据，转换为ID列表兼容原有接口
        List<Long> billList = paymentWriteOffService.getAcceptBillWriteOffAsIdList(paymentId);
        resp.setBillList(billList);

        // 计算核销总金额
        //BigDecimal advanceWriteOffTotal = paymentWriteOffService.calculateAdvanceWriteOffTotal(paymentId);
        //resp.setPayWriteAmt(advanceWriteOffTotal);
        //
        //BigDecimal billWriteOffTotal = paymentWriteOffService.calculateBillWriteOffTotal(paymentId);
        //resp.setBillWriteAmt(billWriteOffTotal);
    }
}
