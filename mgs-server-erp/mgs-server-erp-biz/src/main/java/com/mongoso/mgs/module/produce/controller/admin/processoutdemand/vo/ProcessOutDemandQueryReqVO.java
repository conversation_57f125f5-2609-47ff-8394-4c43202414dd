package com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import java.math.BigDecimal;
import java.util.List;


/**
 * 工序委外需求清单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProcessOutDemandQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long processOutDemandId;
    private List<Long> processOutDemandIdList;

    /** 工序委外需求单号 */
    private String processOutDemandCode;

    /** 生产订单ID */
    private Long prodOrderId;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 生产工单ID */
    private Long prodWorkId;
    private List<Long> prodWorkIdList;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 工序id */
    private Long processId;
    private List<Long> processIdList;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 加工方式 */
    private Long processMethod;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanStartDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanEndDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanEndDate;

    /** 工单计划生产数量 */
    private BigDecimal workPlanTotalQty;

    /** 派工方式['工单工序派工','工序派工'] */
    private Integer dispatchMethod;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 委外数量 */
    private BigDecimal outDemandQty;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 采购状态 */
    private Integer formStatus;
    private Integer formStatusNeq;
    private List<Integer> formStatusList;

    /** 实际采购数量 */
    private BigDecimal purchaseableQty;

    private BigDecimal zero;

    /** 用户id */
    private Long userId;

    /** 计件方式 */
    private String pieceworkMethodDictId;

    /** 派工策略["手动派工","自动派工"] */
    private Integer dispatchStrategyConfig;

}
