package com.mongoso.mgs.module.produce.dal.mysql.device;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.produce.controller.admin.device.vo.DevicePageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.device.vo.DeviceQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.device.DeviceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备台账 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceMapper extends BaseMapperX<DeviceDO> {

    default PageResult<DeviceDO> selectPageOld(DevicePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<DeviceDO>lambdaQueryX()
                .betweenIfPresent(DeviceDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .notInIfPresent(DeviceDO::getDeviceId, reqVO.getExclDeviceIdList())
                .likeIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceDO::getDeviceTypeDictId, reqVO.getDeviceTypeDictId())
                .likeIfPresent(DeviceDO::getDeviceModel, reqVO.getDeviceModel())
                .likeIfPresent(DeviceDO::getDeviceAbbr, reqVO.getDeviceAbbr())
                .eqIfPresent(DeviceDO::getDeviceUnit, reqVO.getDeviceUnit())
                .eqIfPresent(DeviceDO::getDeviceStatusDictId, reqVO.getDeviceStatusDictId())
                .eqIfPresent(DeviceDO::getUsageStatusDictId, reqVO.getUsageStatusDictId())
                .eqIfPresent(DeviceDO::getDeviceSourceDictId, reqVO.getDeviceSourceDictId())
                .eqIfPresent(DeviceDO::getDeviceLevelDictId, reqVO.getDeviceLevelDictId())
                .eqIfPresent(DeviceDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(DeviceDO::getProducer, reqVO.getProducer())
                .eqIfPresent(DeviceDO::getFactoryNo, reqVO.getFactoryNo())
                .eqIfPresent(DeviceDO::getDeptId, reqVO.getDeptId())
                .likeIfPresent(DeviceDO::getAssetCode, reqVO.getAssetCode())
                .eqIfPresent(DeviceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DeviceDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(DeviceDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(DeviceDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(DeviceDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(DeviceDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(DeviceDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(DeviceDO::getCreatedDt));
    }



    default PageResult<DeviceDO> selectPage(DevicePageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<DeviceDO>lambdaQueryX()
                .betweenIfPresent(DeviceDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .likeIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .notInIfPresent(DeviceDO::getDeviceId, reqVO.getExclDeviceIdList())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceDO::getDeviceTypeDictId, reqVO.getDeviceTypeDictId())
                .likeIfPresent(DeviceDO::getDeviceModel, reqVO.getDeviceModel())
                .likeIfPresent(DeviceDO::getDeviceAbbr, reqVO.getDeviceAbbr())
                .eqIfPresent(DeviceDO::getDeviceUnit, reqVO.getDeviceUnit())
                .eqIfPresent(DeviceDO::getDeviceStatusDictId, reqVO.getDeviceStatusDictId())
                .eqIfPresent(DeviceDO::getUsageStatusDictId, reqVO.getUsageStatusDictId())
                .eqIfPresent(DeviceDO::getDeviceSourceDictId, reqVO.getDeviceSourceDictId())
                .eqIfPresent(DeviceDO::getDeviceLevelDictId, reqVO.getDeviceLevelDictId())
                .eqIfPresent(DeviceDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(DeviceDO::getProducer, reqVO.getProducer())
                .eqIfPresent(DeviceDO::getFactoryNo, reqVO.getFactoryNo())
                .eqIfPresent(DeviceDO::getDeptId, reqVO.getDeptId())
                .likeIfPresent(DeviceDO::getAssetCode, reqVO.getAssetCode())
                .eqIfPresent(DeviceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DeviceDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(DeviceDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(DeviceDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(DeviceDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(DeviceDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(DeviceDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(DeviceDO::getCreatedDt));
    }

    default List<DeviceDO> selectListOld(DeviceQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<DeviceDO>lambdaQueryX()
                .betweenIfPresent(DeviceDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .notInIfPresent(DeviceDO::getDeviceId, reqVO.getExclDeviceIdList())
                .likeIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceDO::getDeviceCode, reqVO.getEqDeviceCode())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceDO::getDeviceTypeDictId, reqVO.getDeviceTypeDictId())
                .likeIfPresent(DeviceDO::getDeviceModel, reqVO.getDeviceModel())
                .likeIfPresent(DeviceDO::getDeviceAbbr, reqVO.getDeviceAbbr())
                .eqIfPresent(DeviceDO::getDeviceUnit, reqVO.getDeviceUnit())
                .eqIfPresent(DeviceDO::getDeviceStatusDictId, reqVO.getDeviceStatusDictId())
                .eqIfPresent(DeviceDO::getUsageStatusDictId, reqVO.getUsageStatusDictId())
                .eqIfPresent(DeviceDO::getDeviceSourceDictId, reqVO.getDeviceSourceDictId())
                .eqIfPresent(DeviceDO::getDeviceLevelDictId, reqVO.getDeviceLevelDictId())
                .eqIfPresent(DeviceDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(DeviceDO::getProducer, reqVO.getProducer())
                .eqIfPresent(DeviceDO::getFactoryNo, reqVO.getFactoryNo())
                .eqIfPresent(DeviceDO::getDeptId, reqVO.getDeptId())
                .likeIfPresent(DeviceDO::getAssetCode, reqVO.getAssetCode())
                .eqIfPresent(DeviceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DeviceDO::getDataStatus, reqVO.getDataStatus())
                .neIfPresent(DeviceDO::getDataStatus, reqVO.getNeDataStatus())
                .eqIfPresent(DeviceDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(DeviceDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(DeviceDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(DeviceDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(DeviceDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(DeviceDO::getCreatedDt));
    }

    default List<DeviceDO> selectList(DeviceQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<DeviceDO>lambdaQueryX()
                .betweenIfPresent(DeviceDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .notInIfPresent(DeviceDO::getDeviceId, reqVO.getExclDeviceIdList())
                .inIfPresent(DeviceDO::getDeviceId, reqVO.getDeviceIdList())
                .likeIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceDO::getDeviceTypeDictId, reqVO.getDeviceTypeDictId())
                .eqIfPresent(DeviceDO::getDeviceModel, reqVO.getDeviceModel())
                .likeIfPresent(DeviceDO::getDeviceAbbr, reqVO.getDeviceAbbr())
                .eqIfPresent(DeviceDO::getDeviceUnit, reqVO.getDeviceUnit())
                .eqIfPresent(DeviceDO::getDeviceStatusDictId, reqVO.getDeviceStatusDictId())
                .eqIfPresent(DeviceDO::getUsageStatusDictId, reqVO.getUsageStatusDictId())
                .eqIfPresent(DeviceDO::getDeviceSourceDictId, reqVO.getDeviceSourceDictId())
                .eqIfPresent(DeviceDO::getDeviceLevelDictId, reqVO.getDeviceLevelDictId())
                .eqIfPresent(DeviceDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(DeviceDO::getProducer, reqVO.getProducer())
                .eqIfPresent(DeviceDO::getFactoryNo, reqVO.getFactoryNo())
                .eqIfPresent(DeviceDO::getDeptId, reqVO.getDeptId())
                .likeIfPresent(DeviceDO::getAssetCode, reqVO.getAssetCode())
                .eqIfPresent(DeviceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DeviceDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(DeviceDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(DeviceDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(DeviceDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(DeviceDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(DeviceDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(DeviceDO::getCreatedDt));
    }

    void updateAssetCode(@Param("assetCode") String assetCode, @Param("deviceId") Long deviceId);

}