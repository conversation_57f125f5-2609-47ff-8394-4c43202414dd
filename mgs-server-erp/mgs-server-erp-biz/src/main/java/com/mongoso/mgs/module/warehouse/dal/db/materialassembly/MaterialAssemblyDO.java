package com.mongoso.mgs.module.warehouse.dal.db.materialassembly;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 组装拆卸单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_material_assembly", autoResultMap = true)
//@KeySequence("u_material_assembly_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialAssemblyDO extends OperateDO {

    /** 组装拆卸单ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long assemblyId;

    /** 组装拆卸单号 */
    private String assemblyCode;

    /** 组装拆卸名称 */
    private String assemblyName;

    /** 组装拆卸单类型 */
    private String assemblyTypeDictId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 组装物料ID */
    private Long assemblyMaterialId;

    /** 组装物料编码 */
    private String assemblyMaterialCode;

    /** 组装物料基本单位ID */
    private String assemblyMainUnitDictId;

    /** 物料BOM_ID */
    private Long materialBomId;

    /** 物料BOM版本 */
    private Integer materialBomVersion;

    /** 组装拆卸数量 */
    private BigDecimal assemblyQty;

    /** 组装拆卸仓库ID */
    private String assemblyWarehouseOrgId;

    /** 备注 */
    private String remark;

    /** 业务类型 */
    private Short bizType;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;


}
