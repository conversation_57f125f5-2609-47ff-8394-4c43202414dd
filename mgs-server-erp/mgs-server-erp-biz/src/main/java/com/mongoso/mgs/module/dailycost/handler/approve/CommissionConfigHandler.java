package com.mongoso.mgs.module.dailycost.handler.approve;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.detail.CommissionConfigCustomerQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.detail.CommissionConfigEmployeeQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.detail.CommissionConfigMaterialQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.vo.CommissionConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.vo.CommissionConfigQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfig.vo.EmployeeCustomerMergeVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfigdetail.vo.CommissionConfigDetailAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfigdetail.vo.CommissionConfigDetailQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissionconfigdetail.vo.CommissionConfigDetailRespVO;
import com.mongoso.mgs.module.dailycost.dal.db.commissionconfig.CommissionConfigCustomerDO;
import com.mongoso.mgs.module.dailycost.dal.db.commissionconfig.CommissionConfigDO;
import com.mongoso.mgs.module.dailycost.dal.db.commissionconfig.CommissionConfigEmployeeDO;
import com.mongoso.mgs.module.dailycost.dal.db.commissionconfig.CommissionConfigMaterialDO;
import com.mongoso.mgs.module.dailycost.dal.db.costaggre.CostAggreDO;
import com.mongoso.mgs.module.dailycost.dal.mysql.commissionconfig.CommissionConfigCustomerMapper;
import com.mongoso.mgs.module.dailycost.dal.mysql.commissionconfig.CommissionConfigEmployeeMapper;
import com.mongoso.mgs.module.dailycost.dal.mysql.commissionconfig.CommissionConfigMapper;
import com.mongoso.mgs.module.dailycost.dal.mysql.commissionconfig.CommissionConfigMaterialMapper;
import com.mongoso.mgs.module.dailycost.service.commissionconfig.CommissionConfigService;
import com.mongoso.mgs.module.dailycost.service.commissionconfigdetail.CommissionConfigDetailService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * @author: meijin.feng
 * @date: 2024-12-4 18:23:03
 * @description: 提成规则审批流程处理类
 */

@Component
public class CommissionConfigHandler extends FlowApproveHandler<CommissionConfigDO> {
    @Resource
    private CommissionConfigMapper commissionConfigMapper;

    @Resource
    private CommissionConfigDetailService detailService;

    @Resource
    @Lazy
    private CommissionConfigService commissionConfigService;

    @Resource
    private CommissionConfigCustomerMapper commissionConfigCustomerMapper;

    @Resource
    private CommissionConfigMaterialMapper commissionConfigMaterialMapper;

    @Resource
    private CommissionConfigEmployeeMapper commissionConfigEmployeeMapper;



    @Override
    protected ApproveCommonAttrs approvalAttributes(CommissionConfigDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(CommissionConfigDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(CommissionConfigDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getCommissionConfigId())
                .objCode(item.getCommissionConfigCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(CommissionConfigDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
//            CommissionConfigDO commissionConfigDO = commissionConfigMapper.selectOne();
//            if (commissionConfigDO != null){
//                failItem.setCode(item.getCommissionConfigCode());
//                failItem.setReason("客户加物料已存在");
//                return false;
//            }
            if (item.getMaterialApplyRange() == 0 && item.getCustomerApplyRange() == 0){
                CommissionConfigQueryReqVO queryReqVO = new CommissionConfigQueryReqVO();
                queryReqVO.setDataStatus(DataStatusEnum.APPROVED.key.shortValue());
                List<CommissionConfigDO> configDOList = commissionConfigMapper.selectList(queryReqVO);
                if (CollUtilX.isNotEmpty(configDOList)){
                    failItem.setCode(item.getCommissionConfigCode());
                    failItem.setReason("客户加物料已存在");
                    return false;
                }
            }

            CommissionConfigAditReqVO reqVO = BeanUtilX.copy(item, CommissionConfigAditReqVO::new);
            List<EmployeeCustomerMergeVO> reqVOList = commissionConfigService.employeeCustomerMerge(reqVO);
            for (EmployeeCustomerMergeVO sItem : reqVOList) {
                CommissionConfigDetailQueryReqVO queryReqVO = new CommissionConfigDetailQueryReqVO();
                queryReqVO.setCustomerId(sItem.getCustomerId());
                queryReqVO.setMaterialId(sItem.getMaterialId());
                List<CommissionConfigDetailRespVO> detailDOList = detailService.commissionConfigDetailList(queryReqVO);
                if (CollUtilX.isNotEmpty(detailDOList)) {
                    String title = null;
//                    String code = null;
                    if (StringUtils.isEmpty(sItem.getCustomerCode()) && StringUtils.isNotEmpty(sItem.getMaterialCode())){
                        title = "客户加物料"+sItem.getMaterialCode()+"已存在";
//                        code = sItem.getMaterialCode();
                    }
                    if (StringUtils.isEmpty(sItem.getMaterialCode()) && StringUtils.isNotEmpty(sItem.getCustomerCode())){
                        title = "客户"+sItem.getCustomerCode()+"加物料已存在";
//                        code = sItem.getCustomerCode();
                    }
                    if (StringUtils.isNotEmpty(sItem.getMaterialCode()) && StringUtils.isNotEmpty(sItem.getCustomerCode())){
                        title = "客户"+sItem.getCustomerCode()+"加物料"+sItem.getMaterialCode()+"已存在";
//                        code = sItem.getCustomerCode()+"-"+sItem.getMaterialCode();
                    }
                    failItem.setCode(item.getCommissionConfigCode());
                    failItem.setReason(title);
                    return false;
                }
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

        }

        return true;
    }



    @Override
    public Integer handleBusinessData(CommissionConfigDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = item.getCommissionConfigId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        CommissionConfigDO exist  = commissionConfigMapper.selectById(id);

        //查询关联客户名称
        CommissionConfigCustomerQueryReqVO customerQueryReqVO = new CommissionConfigCustomerQueryReqVO();
        customerQueryReqVO.setCommissionConfigId(exist.getCommissionConfigId());
        List<CommissionConfigCustomerDO> customerInfo = commissionConfigCustomerMapper.selectList(customerQueryReqVO);
        //查询关联物料名称
        CommissionConfigMaterialQueryReqVO materialQueryReqVO = new CommissionConfigMaterialQueryReqVO();
        materialQueryReqVO.setCommissionConfigId(exist.getCommissionConfigId());
        List<CommissionConfigMaterialDO> materialInfo = commissionConfigMaterialMapper.selectList(materialQueryReqVO);

        //查询关联物料名称
        CommissionConfigEmployeeQueryReqVO employeeQueryReqVO = new CommissionConfigEmployeeQueryReqVO();
        employeeQueryReqVO.setRelatedOrderId(exist.getCommissionConfigId());
        List<CommissionConfigEmployeeDO> employeeInfo = commissionConfigEmployeeMapper.selectList(employeeQueryReqVO);

        exist.setApprovedBy(loginUser.getFullUserName());
        exist.setApprovedDt(LocalDateTime.now());
        exist.setDataStatus(dataStatus.shortValue());
        // todo 审核需上游 已审核，反审核 需下游 未审核
        // 审核通过，反审核通过
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            //选择了所有客户和所有物料
            if (exist.getCustomerApplyRange() == 0 && exist.getMaterialApplyRange() == 0){
                //新增规则明细
                for (CommissionConfigEmployeeDO check : employeeInfo){
                    CommissionConfigDetailAditReqVO reqVO = new CommissionConfigDetailAditReqVO();
                    Long employeeId = check.getEmployeeArchivesId();
                    BigDecimal assignRatio = check.getAssignRatio();
                    reqVO.setCommissionConfigId(exist.getCommissionConfigId());
                    reqVO.setCommissionRatio(exist.getCommissionRatio());
                    reqVO.setCustomerApplyRange(exist.getCustomerApplyRange());
                    reqVO.setMaterialApplyRange(exist.getMaterialApplyRange());
                    reqVO.setEmployeeId(employeeId);
                    reqVO.setAssignRatio(assignRatio);
                    detailService.commissionConfigDetailAdd(reqVO);
                }
            }else {
                List<CommissionConfigDetailAditReqVO> configDetailDOList = new ArrayList<>();
                if (exist.getCustomerApplyRange() == 1 && exist.getMaterialApplyRange() == 0) {
                    for (CommissionConfigEmployeeDO check : employeeInfo){
                        for (CommissionConfigCustomerDO customer : customerInfo) {
                            CommissionConfigDetailAditReqVO detailDO = new CommissionConfigDetailAditReqVO();
                            Long customerId = customer.getCustomerId();
                            Long employeeId = check.getEmployeeArchivesId();
                            BigDecimal assignRatio = check.getAssignRatio();
                            detailDO.setCommissionConfigId(exist.getCommissionConfigId());
                            detailDO.setCustomerApplyRange(exist.getCustomerApplyRange());
                            detailDO.setMaterialApplyRange(exist.getMaterialApplyRange());
                            detailDO.setCustomerId(customerId);
                            detailDO.setCommissionRatio(exist.getCommissionRatio());
                            detailDO.setEmployeeId(employeeId);
                            detailDO.setAssignRatio(assignRatio);
                            configDetailDOList.add(detailDO);
                        }
                    }

                } else if (exist.getCustomerApplyRange() == 0 && exist.getMaterialApplyRange() == 1) {
                    for (CommissionConfigEmployeeDO check : employeeInfo){
                        for (CommissionConfigMaterialDO material : materialInfo) {
                            CommissionConfigDetailAditReqVO detailDO = new CommissionConfigDetailAditReqVO();
                            Long materialId = material.getMaterialId();
                            BigDecimal commissionRatio = material.getCommissionRatio();
                            Long employeeId = check.getEmployeeArchivesId();
                            BigDecimal assignRatio = check.getAssignRatio();
                            detailDO.setCommissionConfigId(exist.getCommissionConfigId());
                            detailDO.setCustomerApplyRange(exist.getCustomerApplyRange());
                            detailDO.setMaterialApplyRange(exist.getMaterialApplyRange());
                            detailDO.setMaterialId(materialId);
                            detailDO.setCommissionRatio(commissionRatio);
                            detailDO.setEmployeeId(employeeId);
                            detailDO.setAssignRatio(assignRatio);
                            configDetailDOList.add(detailDO);
                        }
                    }
                } else if (exist.getCustomerApplyRange() == 1 && exist.getMaterialApplyRange() == 1) {
                    for (CommissionConfigEmployeeDO check : employeeInfo){
                        for (CommissionConfigMaterialDO material : materialInfo) {
                            for (CommissionConfigCustomerDO customer : customerInfo) {
                                CommissionConfigDetailAditReqVO detailDO = new CommissionConfigDetailAditReqVO();
                                Long customerId = customer.getCustomerId();
                                Long materialId = material.getMaterialId();
                                BigDecimal commissionRatio = material.getCommissionRatio();
                                Long employeeId = check.getEmployeeArchivesId();
                                BigDecimal assignRatio = check.getAssignRatio();
                                detailDO.setCommissionConfigId(exist.getCommissionConfigId());
                                detailDO.setCustomerApplyRange(exist.getCustomerApplyRange());
                                detailDO.setMaterialApplyRange(exist.getMaterialApplyRange());
                                detailDO.setCustomerId(customerId);
                                detailDO.setMaterialId(materialId);
                                detailDO.setCommissionRatio(commissionRatio);
                                detailDO.setEmployeeId(employeeId);
                                detailDO.setAssignRatio(assignRatio);
                                configDetailDOList.add(detailDO);
                            }
                        }
                    }
                }
                for (CommissionConfigDetailAditReqVO sItem : configDetailDOList){
                    detailService.commissionConfigDetailAdd(sItem);
                }
            }
        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
            detailService.commissionConfigIdDel(exist.getCommissionConfigId());
        }
        //更新业务数据
        Integer updateCount = commissionConfigMapper.updateById(exist);

        return updateCount;
    }
}