package com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo;

import lombok.*;

  
 import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;



/**
 * 销售订单明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpSaleOrderDetailRespVO extends ErpSaleOrderDetailBaseVO {

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 通知数量 */
    private BigDecimal noticeQty;

    /** 可通知数量 */
    private BigDecimal canNoticeQty;

    /** 已退货数量 */
    private BigDecimal returnQty;

    /** 可退货数量 */
    private BigDecimal canReturnQty;

    /** 已扣费数量 */
    private BigDecimal deductionQty;

    /** 可扣费数量 */
    private BigDecimal canDeductionQty;

    /** 已换货数量 */
    private BigDecimal exchangeQty;

    /** 可换货数量 */
    private BigDecimal canExchangeQty;

    /** 已完成数量 */
    private BigDecimal completedQty;

    /** 已换货入库数量 */
    private BigDecimal exchangeInboundQty;


    /** ------出库单引用字段------ **/

    /** 物料库存ID */
    private Long materialStockId;

    /** 仓库ID */
    private String warehouseOrgId;

    /** 仓库名称 */
    private String warehouseOrgName;

    /** 已出库数量 */
    private BigDecimal saleQty;

    /** 已出库数量 */
    private BigDecimal outboundedQty;

    /** 可出库数量 */
    private BigDecimal outboundableQty;

    /** 库存数量 **/
    private BigDecimal stockQty;

    /** 锁定数量 */
    private BigDecimal lockedQty;

    /** 可用库存数量  **/
    private BigDecimal stockableQty;

    /** 可操作数量 */
    private BigDecimal operableQty;
    /** ------出库单引用字段------ **/

    /** 销售订单物料总数  **/
    private BigDecimal saleOrderMaterialQty;

    /** 已下发订单总数 */
    private BigDecimal issueOrderQty;

}
