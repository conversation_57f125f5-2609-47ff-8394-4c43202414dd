package com.mongoso.mgs.module.dailycost.controller.admin.commissiongrant.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 提成发放 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class CommissionGrantQueryReqVO {

    /** 提成发放ID */
    private Long commissionGrantId;

    /** 提成发放单号 */
    private String commissionGrantCode;

    /** 提成任务ID */
    private Long commissionTaskId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单行号 */
    private Integer sourceOrderRowNo;

    /** 单据类型 */
    private Integer formType;

    /** 数量 */
    private BigDecimal qty;

    /** 行金额 (不含税) */
    private BigDecimal exclTaxAmt;

    /** 提成比例 */
    private BigDecimal commissionRatio;

    /** 提成金额 */
    private BigDecimal commissionAmt;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

}
