package com.mongoso.mgs.module.base.service.customeraddress;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.base.controller.admin.customeraddress.vo.*;
import com.mongoso.mgs.module.base.dal.db.customeraddress.CustomerAddressDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.base.dal.mysql.customeraddress.CustomerAddressMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.*;


/**
 * 客户地址 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustomerAddressServiceImpl implements CustomerAddressService {

    @Resource
    private CustomerAddressMapper customerAddressMapper;

    @Override
    public Long customerAddressAdd(CustomerAddressAditReqVO reqVO) {
        // 插入
        CustomerAddressDO customerAddress = BeanUtilX.copy(reqVO, CustomerAddressDO::new);
        customerAddressMapper.insert(customerAddress);
        // 返回
        return customerAddress.getCustomerAddressId();
    }

    @Override
    public Long customerAddressEdit(CustomerAddressAditReqVO reqVO) {
        // 校验存在
        this.customerAddressValidateExists(reqVO.getCustomerAddressId());
        // 更新
        CustomerAddressDO customerAddress = BeanUtilX.copy(reqVO, CustomerAddressDO::new);
        customerAddressMapper.updateById(customerAddress);
        // 返回
        return customerAddress.getCustomerAddressId();
    }

    @Override
    public void customerAddressDelete(Long customerAddressId) {
        // 校验存在
        this.customerAddressValidateExists(customerAddressId);
        // 删除
        customerAddressMapper.deleteById(customerAddressId);
    }

    private CustomerAddressDO customerAddressValidateExists(Long customerAddressId) {
        CustomerAddressDO customerAddress = customerAddressMapper.selectById(customerAddressId);
        if (customerAddress == null) {
            // throw exception(CUSTOMER_ADDRESS_NOT_EXISTS);
            throw new BizException("5001", "客户地址不存在");
        }
        return customerAddress;
    }

    @Override
    public CustomerAddressRespVO customerAddressDetail(Long customerAddressId) {
        CustomerAddressDO data = customerAddressMapper.selectById(customerAddressId);
        return BeanUtilX.copy(data, CustomerAddressRespVO::new);
    }

    @Override
    public List<CustomerAddressRespVO> customerAddressList(CustomerAddressQueryReqVO reqVO) {
        List<CustomerAddressDO> data = customerAddressMapper.selectList(reqVO);
        return BeanUtilX.copy(data, CustomerAddressRespVO::new);
    }

    @Override
    public PageResult<CustomerAddressRespVO> customerAddressPage(CustomerAddressPageReqVO reqVO) {
        PageResult<CustomerAddressDO> data = customerAddressMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, CustomerAddressRespVO::new);
    }

}
