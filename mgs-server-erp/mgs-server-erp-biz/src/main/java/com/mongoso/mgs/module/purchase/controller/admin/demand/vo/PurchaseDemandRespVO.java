package com.mongoso.mgs.module.purchase.controller.admin.demand.vo;

import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购需求 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseDemandRespVO extends PurchaseDemandBaseVO {

    private Short isCanIssuePurchase;

    /** 采购需求单类型 */
    private String demandTypeDictName;

    /** 需求明细列表 */
    private List<PurchaseDemandDetailRespVO> detailList;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 责任人 */
    private String directorName;

    /** 责任部门 */
    private String directorOrgName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审批任务id */
    private Long approveTaskId;
}
