package com.mongoso.mgs.module.comp.payroll.dal.mysql.payrollItem;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payroll.bo.PayrollNoteItem;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo.PayrollItemPageReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo.PayrollItemQueryReqVO;
import com.mongoso.mgs.module.comp.payroll.dal.db.payrollItem.PayrollItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工资单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PayrollItemMapper extends BaseMapperX<PayrollItemDO> {

    default PayrollItemDO selectDetail(Long payrollMemberId, Long compensationItemId) {
        return selectOne(LambdaQueryWrapperX.<PayrollItemDO>lambdaQueryX()
                .eq(PayrollItemDO::getPayrollMemberId, payrollMemberId)
                .eq(PayrollItemDO::getCompensationItemId, compensationItemId));
    }

    default PageResult<PayrollItemDO> selectPage(PayrollItemPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<PayrollItemDO>lambdaQueryX()
                .eqIfPresent(PayrollItemDO::getPayrollId, reqVO.getPayrollId())
                .eqIfPresent(PayrollItemDO::getPayrollMemberId, reqVO.getPayrollMemberId())
                .eqIfPresent(PayrollItemDO::getCompensationItemId, reqVO.getCompensationItemId())
                .orderByDesc(PayrollItemDO::getCreatedDt));
    }

    default List<PayrollItemDO> selectList(PayrollItemQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<PayrollItemDO>lambdaQueryX()
                .eqIfPresent(PayrollItemDO::getPayrollMemberId, reqVO.getPayrollMemberId())
                .inIfPresent(PayrollItemDO::getPayrollMemberId, reqVO.getPayrollMemberIdList())
                .inIfPresent(PayrollItemDO::getCompensationItemId, reqVO.getCompensationItemIdList())
                .orderByDesc(PayrollItemDO::getCreatedDt));
    }

    List<PayrollItemDO> calcItemSumList(PayrollItemQueryReqVO reqVO);

    default int payrollMemberDelByPayrollId(Long payrollId) {
        return delete(LambdaQueryWrapperX.<PayrollItemDO>lambdaQueryX()
                .eqIfPresent(PayrollItemDO::getPayrollId, payrollId));
    }

    default int payrollItemDelByMemberId(Long payrollMemberId){
        return delete(LambdaQueryWrapperX.<PayrollItemDO>lambdaQueryX()
                .eq(PayrollItemDO::getPayrollMemberId, payrollMemberId));
    }

    default int payrollItemDelByMemberId(Long payrollMemberId, List<Long> compensationItemIdList){
        return delete(LambdaQueryWrapperX.<PayrollItemDO>lambdaQueryX()
                .eq(PayrollItemDO::getPayrollMemberId, payrollMemberId)
                .in(PayrollItemDO::getCompensationItemId, compensationItemIdList));
    }

    /**
     * 查询工资条薪酬项
     * @param payrollMemberId 工资单成员ID
     * @param orgId 组织Id
     * @return t
     */
    List<PayrollNoteItem> queryPayrollNoteItemList(@Param("payrollMemberId") Long payrollMemberId, @Param("orgId") String orgId);

}
