package com.mongoso.mgs.module.dailycost.controller.admin.indirectcostamount.vo.spu;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 产品日间接成本 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CostSpuDailyIndirectBaseVO implements Serializable {

    /** 主键ID */
    private Long spuDailyIndirectId;

    /** 费用日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate costDate;

    /** 承担对象ID */
    private String undertakeOrgId;
    private String undertakeOrgName;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核状态 */
    @NotNull(message = "审核状态不能为空")
    private Short dataStatus;

    /** 承担物料id */
    private Long undertakeMaterialId;

    /** 承担物料code */
    private String undertakeMaterialCode;
    /** 承担物料Name */
    private String undertakeMaterialName;

}
