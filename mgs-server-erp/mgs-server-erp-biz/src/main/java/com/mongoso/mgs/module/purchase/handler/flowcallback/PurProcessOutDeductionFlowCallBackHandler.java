package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseFlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutdeduction.PurProcessOutDeductionDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurProcessOutDeductionFlowCallBackHandler extends FlowCallbackHandler<PurProcessOutDeductionDO> {

    protected PurProcessOutDeductionFlowCallBackHandler(FlowApproveHandler<PurProcessOutDeductionDO> approveHandler) {
        super(approveHandler);
    }
}
