package com.mongoso.mgs.module.produce.controller.admin.workpicking.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工单领料单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ItemReqVO implements Serializable {

    private String showId;// 前端多选和回显用的,后端没用到
    private Integer rowNo;// 行号
    private Integer isMaterialAlternative;// 是否是替代物料
    private Long materialId;// 物料ID
    private String materialCode;// 物料编码
    private BigDecimal pickingQty;// 领料数量
    private String warehouseOrgId;// 领料仓库id
    private String remark;// 备注
    private BigDecimal demandQty;// 需求数量
    private BigDecimal lossRate;// 损耗率
    private BigDecimal estimatedQty;// 预估用量

    /** 替代物料id和编码 */
    private Long alternativeMaterialId;
    private String alternativeMaterialCode;

    private Integer level;// 物料层级，0是最高级
    private BigDecimal realLossRate;// 真实损耗率
}
