package com.mongoso.mgs.module.warehouse.service.erptransfer;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 调拨单 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpTransferService {

    /**
     * 创建调拨单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpTransferAdd(@Valid ErpTransferAditReqVO reqVO);

    /**
     * 更新调拨单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpTransferEdit(@Valid ErpTransferAditReqVO reqVO);

    /**
     * 删除调拨单
     *
     * @param transferId 编号
     */
    void erpTransferDel(Long transferId);

    /**
     * 批量删除调拨单
     *
     * @param reqVO 删除信息
     */
    ResultX<BatchResult> erpTransferDelBatch(IdsReqVO reqVO);

    /**
     * 获得调拨单信息
     *
     * @param transferId 编号
     * @return 调拨单信息
     */
    ErpTransferRespVO erpTransferDetail(Long transferId);

    /**
     * 获得调拨单列表
     *
     * @param reqVO 查询条件
     * @return 调拨单列表
     */
    List<ErpTransferRespVO> erpTransferList(@Valid ErpTransferQueryReqVO reqVO);

    /**
     * 获得调拨单分页
     *
     * @param reqVO 查询条件
     * @return 调拨单分页
     */
    PageResult<ErpTransferRespVO> erpTransferPage(@Valid ErpTransferPageReqVO reqVO);

    /**
     * 审核调拨单
     *
     * @param reqVO 查询条件
     * @return 审核结果
     */
    BatchResult erpTransferApprove(FlowApprove reqVO);

    /**
     * 审核调拨单回调
     *
     * @param reqVO 审核信息
     * @return 回调结果
     */
    Object erpTransferFlowCallback(FlowCallback reqVO);

}
