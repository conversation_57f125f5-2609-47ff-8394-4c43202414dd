package com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/12/28 13:48
 * @description:
 */

@Data
public class DispatchProcessOutDemandRespVO implements Serializable {

    /** 生产订单ID */
    private Long prodOrderId;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 生产工单ID */
    private Long prodWorkId;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 工序委外需求id */
    private Long processOutDemandId;

    /** 工序委外需求单号 */
    private String processOutDemandCode;

    /** 工单计划生产数量 */
    private BigDecimal workPlanTotalQty;

    /** 委外数量 */
    private BigDecimal outDemandQty;

    /** 已采购采购数量 */
    private BigDecimal purchasedQty;

    /** 待采购数量汇总 */
    private BigDecimal pendingPurchaseTotalQty;

    /** 可采购数量 */
    private BigDecimal purchaseableQty;

    /** 加工方式 */
    private Long processMethod;

    /** 计件方式 */
    private String pieceworkMethodDictId;
    private String pieceworkMethodDictName;

    /** 待派工数量 */
    private BigDecimal dispatchAbleQty;

    /** 可自制数量 */
    private BigDecimal selfQty;

    /** 可委外数量 */
    private BigDecimal outQty;

    /**工序委外需求清单 */
    private List<DispatchProcessOutDemandRespVO> processOutDemandList;


}
