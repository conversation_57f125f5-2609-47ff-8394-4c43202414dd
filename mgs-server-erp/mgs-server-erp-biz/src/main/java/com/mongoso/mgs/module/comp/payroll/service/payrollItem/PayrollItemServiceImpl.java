package com.mongoso.mgs.module.comp.payroll.service.payrollItem;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payroll.bo.PayrollNoteItem;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo.PayrollItemAditReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo.PayrollItemPageReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo.PayrollItemQueryReqVO;
import com.mongoso.mgs.module.comp.payroll.dal.db.payrollItem.PayrollItemDO;
import com.mongoso.mgs.module.comp.payroll.dal.mysql.payrollItem.PayrollItemMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.comp.enums.ErrorCodeConstants.PAYROLL_ITEM_NOT_EXISTS;


/**
 * 工资单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PayrollItemServiceImpl implements PayrollItemService {

    @Resource
    private PayrollItemMapper payrollItemMapper;

    @Override
    public Long payrollItemAdd(PayrollItemAditReqVO reqVO) {
        // 插入
        PayrollItemDO payrollItem = BeanUtilX.copy(reqVO, PayrollItemDO::new);
        payrollItemMapper.insert(payrollItem);
        // 返回
        return payrollItem.getId();
    }

    @Override
    public void payrollItemBatchEdit(List<PayrollItemDO> reqVOs){
        payrollItemMapper.insertOrUpdateBatch(reqVOs);
    }

    @Override
    public Long payrollItemEdit(PayrollItemAditReqVO reqVO) {
        // 校验存在
        this.payrollItemValidateExists(reqVO.getId());
        // 更新
        PayrollItemDO payrollItem = BeanUtilX.copy(reqVO, PayrollItemDO::new);
        payrollItemMapper.updateById(payrollItem);
        // 返回
        return payrollItem.getId();
    }

    @Override
    public void payrollItemDel(Long id) {
        // 校验存在
        this.payrollItemValidateExists(id);
        // 删除
        payrollItemMapper.deleteById(id);
    }

    @Override
    public void payrollItemDelPayrollId(Long payrollId) {
        payrollItemMapper.payrollMemberDelByPayrollId(payrollId);
    }

    private PayrollItemDO payrollItemValidateExists(Long id) {
        PayrollItemDO payrollItem = payrollItemMapper.selectById(id);
        if (payrollItem == null) {
            throw exception(PAYROLL_ITEM_NOT_EXISTS);
        }
        return payrollItem;
    }


    @Override
    public void payrollItemDelByMemberId(Long payrollMemberId){
        payrollItemMapper.payrollItemDelByMemberId(payrollMemberId);
    }

    @Override
    public void payrollItemDelByMemberId(Long payrollMemberId, List<Long> compensationItemIdList){
        payrollItemMapper.payrollItemDelByMemberId(payrollMemberId, compensationItemIdList);
    }

    @Override
    public PayrollItemDO payrollItemDetail(Long id) {
        return payrollItemMapper.selectById(id);
    }

    @Override
    public PayrollItemDO payrollItemDetail(Long payrollMemberId, Long compensationItemId){
        return payrollItemMapper.selectDetail(payrollMemberId, compensationItemId);
    }

    @Override
    public List<PayrollItemDO> payrollItemList(List<Long> payrollMemberIdList){
        PayrollItemQueryReqVO reqVO = new PayrollItemQueryReqVO();
        reqVO.setPayrollMemberIdList(payrollMemberIdList);
        return payrollItemMapper.selectList(reqVO);
    }

    @Override
    public List<PayrollItemDO> calcItemSumList(List<Long> payrollMemberIdList) {
        PayrollItemQueryReqVO reqVO = new PayrollItemQueryReqVO();
        reqVO.setPayrollMemberIdList(payrollMemberIdList);
        return payrollItemMapper.calcItemSumList(reqVO);
    }

    @Override
    public List<PayrollItemDO> payrollItemList(PayrollItemQueryReqVO reqVO) {
        return payrollItemMapper.selectList(reqVO);
    }

    @Override
    public PageResult<PayrollItemDO> payrollItemPage(PayrollItemPageReqVO reqVO) {
        return payrollItemMapper.selectPage(reqVO);
    }

    @Override
    public List<PayrollNoteItem> queryPayrollNoteItemList(Long payrollMemberId, String orgId){
        return payrollItemMapper.queryPayrollNoteItemList(payrollMemberId, orgId);
    }

}
