package com.mongoso.mgs.module.sale.dal.db.salereturndetail;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 销售退货单明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_sale_return_detail", autoResultMap = true)
//@KeySequence("u_sale_return_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnDetailDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long saleReturnDetailId;

    /** 行号 */
    private Short rowNo;

    /** 销售退货id */
    private Long saleReturnId;

    /** 销售退货单号 */
    private String salesReturnCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 关联行号 */
    private Short relatedRowNo;

    /** 基本单位 */
    private String mainUnitDictId;

    /** 退货数量 */
    private BigDecimal returnQty;

    /** 单价 */
    private BigDecimal exclTaxPrice;

    /** 金额 */
    private BigDecimal exclTaxAmt;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 含税单价 */
    private BigDecimal inclTaxPrice;

    /** 含税金额 */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 已操作数量 */
    private BigDecimal operedQty;

    /** 可操作数量 */
    private BigDecimal operableQty;

    /** 已收货数量 */
    private BigDecimal receiptedQty;

    /** 已入库数量 */
    private BigDecimal inboundedQty;

    /** 物料是否全部已操作 */
    private Integer isMaterialFullOpered;

    /** 币种字典ID */
    private String currencyDictId;

    /** 本币币种字典ID */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;
}
