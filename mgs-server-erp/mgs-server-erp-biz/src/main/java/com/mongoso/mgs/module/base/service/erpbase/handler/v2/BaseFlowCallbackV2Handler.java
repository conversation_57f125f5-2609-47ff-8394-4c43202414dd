//package com.mongoso.mgs.module.base.service.erpbase.handler.v2;
//
//import com.mongoso.mgs.common.util.EntityUtilX;
//import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
//import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
//import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
//import com.mongoso.mgs.component.flow.util.ApproveUtilX;
//import com.mongoso.mgs.framework.common.domain.LoginUser;
//import com.mongoso.mgs.framework.common.domain.batch.FailItem;
//import com.mongoso.mgs.framework.common.exception.BizException;
//import com.mongoso.mgs.framework.common.util.StrUtilX;
//import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
//import com.mongoso.mgs.module.base.dal.mysql.erpbase.ErpBaseMapper;
//import com.mongoso.mgs.module.base.service.erpbase.bo.FlowReqBO;
//import com.mongoso.mgs.module.base.service.erpbase.handler.bo.FlowCallbackBO;
//import com.mongoso.mgs.module.base.service.erpbase.handler.request.ApproveCommonAttrs;
//import com.mongoso.mgs.module.base.service.erpbase.handler.request.BaseApproveRequest;
//import com.mongoso.mgs.module.base.service.erpbase.handler.request.BaseFlowCallbackRequest;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import static com.mongoso.mgs.component.flow.enums.ApproveResultEnum.PASS;
//
///**
// * @author: zhiling
// * @date: 2025/03/06
// * @description: 流程回调基类 v2.0版本
// */
//@Slf4j
//public abstract class BaseFlowCallbackV2Handler<T> {
//
//    protected final BaseApproveV2Handler<T> approveHandler;
//
//    @Resource
//    private ErpBaseMapper erpBaseMapper;
//
//    protected BaseFlowCallbackV2Handler(BaseApproveV2Handler<T> approveHandler) {
//        this.approveHandler = approveHandler;
//    }
//
//    //子类实现公共参数传递
////    protected abstract ApproveCommonAttrs approvalAttributes(T item);
//
//    public final FailItem handleFlowCallback(T item, FlowCallbackBO flowCallbackBO) {
//        FailItem failItem = new FailItem();
//
//        if (item == null){
//            failItem.setReason("当前单据不存在!");
//            return failItem;
//        }
//
//        //回调对象
//        FlowCallback flowCallback = flowCallbackBO.getFlowCallback();
//
//        Integer buttonType = ApproveUtilX.getButtonType(flowCallback.getFlowFunctionCode());
//        Integer dataStatus = getdataStatus(flowCallback);
//        Integer approveResult = flowCallback.getApproveResult();
//        ApproveResultEnum resultEnum = fromKey(approveResult);
//
//        BaseFlowCallbackRequest callBackRequest = new BaseFlowCallbackRequest();
//        callBackRequest.setFlowCallback(flowCallback);
//        callBackRequest.setButtonType(buttonType);
//        callBackRequest.setDataStatus(dataStatus);
//        callBackRequest.setFailItem(failItem);
//
//        try {
//            switch (resultEnum) {
//                case PASS: // 通过
//                    passHandle(item,callBackRequest);
//                    break;
//                case UN_PASS: // 不通过
//                    unPassHandle(item,callBackRequest);
//                    break;
//                default: // 未知
//                    break;
//            }
//        }catch (Exception e) {
//            log.error("审批回调处理处理失败 error:{}",e.getMessage());
//            FlowReqBO flowReqBO = buildFlowReqBO(item,DataButtonEnum.NOT_APPROVE.getKey());
//            erpBaseMapper.updateDataStatus(flowReqBO);
//            throw new BizException(500,e.getMessage());
//        }
//
//        return failItem;
//    }
//
//    protected Integer passHandle(T item, BaseFlowCallbackRequest request) {
//        BaseApproveRequest approveRequest = new BaseApproveRequest();
//        approveRequest.setButtonType(request.getButtonType());
//        approveRequest.setFailItem(request.getFailItem());
//        approveRequest.setDataStatus(request.getDataStatus());
//
//        //进行业务校验
//        Boolean isValid = approveHandler.businessVerify(item, approveRequest);
//
//        //校验结果处理
//        if (!isValid) {
//            log.error("审批回调业务校验不通过！");
//            FlowReqBO flowReqBO = buildFlowReqBO(item,DataButtonEnum.NOT_APPROVE.getKey());
//            erpBaseMapper.updateDataStatus(flowReqBO);
//            return 0;
//        }
//
//        //业务处理
//        approveHandler.handleBusinessData(item, approveRequest);
//
//        return 1;
//    }
//
//    /**
//     * 审批不通过-更新单据状态
//     *
//     * @param request
//     * @return
//     */
//    private Integer unPassHandle(T item,BaseFlowCallbackRequest request){
//        log.info("审批拒绝更新单据状态");
//        FlowReqBO flowReqBO = buildFlowReqBO(item,request.getDataStatus());
//
//        erpBaseMapper.updateDataStatus(flowReqBO);
//
//        return 1;
//    }
//
//    private FlowReqBO buildFlowReqBO(T item,Integer dataStatus) {
//        ApproveCommonAttrs attrs = approveHandler.approvalAttributes(item);
//
//        Long objId = attrs.getObjId();
//        Class<?> clazz = attrs.getClazz();
//
//        String tableName = attrs.getTableName();
//        String pkFieldName = attrs.getCondPkFieldName();
//
//        //获取表名
//        if (StrUtilX.isEmpty(tableName)) {
//            tableName = EntityUtilX.getTableNameByAnnot(clazz);
//        }
//
//        //获取主键字段名称
//        if (StrUtilX.isEmpty(pkFieldName)) {
//            pkFieldName = EntityUtilX.getPkFieldName(clazz);
//        }
//
//        if (StrUtilX.isEmpty(tableName)) {
//            log.error("表名称为空！");
//            throw new IllegalArgumentException("审批回调更新单据状态出现异常！");
//        }
//
//        if (StrUtilX.isEmpty(pkFieldName)) {
//            log.error("主键字段名称不能为空！");
//            throw new IllegalArgumentException("审批回调更新单据状态出现异常！");
//        }
//
//        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//
//        FlowReqBO flowReqBO = new FlowReqBO();
//        flowReqBO.setTableName(tableName);
//        flowReqBO.setCondPkFieldName(pkFieldName);
//        flowReqBO.setId(objId);
//        flowReqBO.setDataStatus(dataStatus);
//        flowReqBO.setFullUserName(loginUser.getFullUserName());
//
//        return flowReqBO;
//    }
//
//
//    /**
//     * 获取单据状态
//     *
//     * @param flowCallback
//     * @return
//     */
//    private Integer getdataStatus(FlowCallback flowCallback) {
//        Integer dataStatus = null;
//        if (flowCallback.getApproveResult() == PASS.getKey()) {
//            // 通过
//            dataStatus = ApproveUtilX.getDataStatusByApproveSuccess(flowCallback.getFlowFunctionCode());
//        } else if (flowCallback.getApproveResult() == ApproveResultEnum.UN_PASS.getKey()) {
//            // 不通过
//            dataStatus = ApproveUtilX.getDataStatusByApproveFail(flowCallback.getFlowFunctionCode());
//        } else {
//            // 撤销
//            dataStatus = ApproveUtilX.getDataStatusByApproveFail(flowCallback.getFlowFunctionCode());
//        }
//        return dataStatus;
//    }
//
//    /**
//     * 获取审批结果枚举
//     *
//     * @param key
//     * @return
//     */
//    private static ApproveResultEnum fromKey(int key) {
//        for (ApproveResultEnum resultEnum : ApproveResultEnum.values()) {
//            if (resultEnum.getKey() == key) {
//                return resultEnum;
//            }
//        }
//        throw new IllegalArgumentException("No matching dataButtonEnum for key: " + key);
//    }
//
//
//}
