package com.mongoso.mgs.module.utility.controller.admin.utilitycost.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 水电气费用 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UtilityCostPageReqVO extends PageParam {

    /** 水电气费用单号 */
    private String utilityCostCode;

    /** 水电气抄表记录id */
    private Long utilityLogId;

    private Long companyId;

    /** 水电气表id */
    private Long utilityConfigId;

    private Long utilityArchivesId;

    private String monthDate;

    /** 昨日抄表数 */
    private BigDecimal yesterdayReadQty;

    /** 当日抄表数 */
    private BigDecimal todayReadQty;

    /** 差额 */
    private BigDecimal readDifference;

    /** 本日使用量 */
    private BigDecimal todayUsage;

    /** 费用单价 */
    private BigDecimal costPrice;

    /** 费用 */
    private BigDecimal utilityAmt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 费用时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startUtilityCostDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endUtilityCostDate;

}
