package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.deduction.PurchaseDeductionDO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： LinShuiQiang
 * @date： 2025/5/30
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurchaseDeductionFlowCallBackHandler extends FlowCallbackHandler<PurchaseDeductionDO> {


    protected PurchaseDeductionFlowCallBackHandler(FlowApproveHandler< PurchaseDeductionDO > approveHandler) {
        super(approveHandler);
    }
}