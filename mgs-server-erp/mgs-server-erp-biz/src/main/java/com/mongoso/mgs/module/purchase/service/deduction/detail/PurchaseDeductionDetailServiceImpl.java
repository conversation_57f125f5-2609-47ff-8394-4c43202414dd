package com.mongoso.mgs.module.purchase.service.deduction.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.deduction.PurchaseDeductionDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.deduction.PurchaseDeductionDetailMapper;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.InvTypeManageQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购扣费单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseDeductionDetailServiceImpl implements PurchaseDeductionDetailService {

    @Resource
    private PurchaseDeductionDetailMapper deductionDetailMapper;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ERPMaterialService erpMaterialService;
    @Resource
    private InvTypeManageMapper invTypeManageMapper;

    @Override
    public List<PurchaseDeductionDetailRespVO> purchaseDeductionDetailList(PurchaseDeductionDetailQueryReqVO reqVO) {
        List<PurchaseDeductionDetailRespVO> deductionDetailRespVOList = deductionDetailMapper.queryList(reqVO);
        this.batchFillVoProperties(deductionDetailRespVOList);
        return deductionDetailRespVOList;
    }

    @Override
    public PageResult<PurchaseDeductionDetailRespVO> purchaseDeductionDetailPage(PurchaseDeductionDetailPageReqVO reqVO) {
        IPage<PurchaseDeductionDetailRespVO> respVOIPage = deductionDetailMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<PurchaseDeductionDetailRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    private void batchFillVoProperties(List<PurchaseDeductionDetailRespVO> deductionDetailRespVOList) {

        if (CollUtilX.isEmpty(deductionDetailRespVOList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.CURRENCY.getDictCode(),
                CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode(),
                CustomerDictEnum.MAIN_UNIT.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for (PurchaseDeductionDetailRespVO detailResp: deductionDetailRespVOList){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
            directorIdList.add(detailResp.getDirectorId());
            directorOrgIdList.add(detailResp.getDirectorOrgId());
            directorOrgIdList.add(detailResp.getCompanyOrgId());
            supplierIdList.add(detailResp.getRelatedSupplierId());
        }

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        //查询关联供应
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for (PurchaseDeductionDetailRespVO detailResp: deductionDetailRespVOList){
            //票据类型
            detailResp.setInvoiceTypeName(invTypeMap.get(detailResp.getInvoiceTypeId()));
            //填充字典值
            detailResp.setMainUnitDictName(dictMap.get(detailResp.getMainUnitDictId()));
            detailResp.setPurchaseTypeDictName(dictMap.get(detailResp.getPurchaseTypeDictId()));
            detailResp.setRefundConditionDictName(dictMap.get(detailResp.getRefundConditionDictId()));
            detailResp.setSettlementMethodDictName(dictMap.get(detailResp.getSettlementMethodDictId()));
            detailResp.setCurrencyDictName(dictMap.get(detailResp.getCurrencyDictId()));


            // 采购单类型
            String purchaseTypeDictId = detailResp.getPurchaseTypeDictId();
            if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
                purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
                detailResp.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            // 币种
            String currencyDictId = detailResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                detailResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = detailResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                detailResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = detailResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                detailResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 退款条件
            String refundConditionDictId = detailResp.getRefundConditionDictId();
            if(StrUtilX.isNotEmpty(refundConditionDictId)){
                refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
                detailResp.setRefundConditionDictName(dictMap.get(refundConditionDictId));
            }

            // 基本单位
            String mainUnitDictId = detailResp.getMainUnitDictId();
            if(StrUtilX.isNotEmpty(mainUnitDictId)) {
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                detailResp.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            // 审核状态
            if(detailResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + detailResp.getDataStatus();
                detailResp.setDataStatusDictName(dictMap.get(dataStatus));;
            }

            //责任人
            detailResp.setDirectorName(directorMap.get(detailResp.getDirectorId()));
            //责任部门属性填充
            detailResp.setDirectorOrgName(directorOrgMap.get(detailResp.getDirectorOrgId()));
            //供应商信息
            detailResp.setRelatedSupplierName(supplierNameMap.get(detailResp.getRelatedSupplierId()));
            //查询公司主体
            detailResp.setCompanyOrgName(directorOrgMap.get(detailResp.getCompanyOrgId()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialDO!=null){
                detailResp.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
                detailResp.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detailResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailResp.setMaterialTypeDictName(erpMaterialDO.getMaterialTypeDictName());
                detailResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detailResp.setSpecModel(erpMaterialDO.getSpecModel());
            }
        }

    }

}
