package com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 收款关系 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PaymentRelationRespVO extends PaymentRelationBaseVO {

    /** 来源单据类型 */
    private Integer sourceFormType;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 单据状态 */
    private Integer formStatus;

    private String formStatusName;

    /** 本币币种 */
    private String localCurrencyDictId;

    /** 本币币种名称 */
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 应*金额 */
    private BigDecimal shouldAmt;

    /** 剩余可*金额 */
    private BigDecimal ableAmt;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
