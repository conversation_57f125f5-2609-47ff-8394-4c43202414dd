package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.purchaseexchange.PurchaseExchangeDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： LinShuiQiang
 * @date： 2025/5/29
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurchaseExchangeFlowCallBackHandler extends FlowCallbackHandler<PurchaseExchangeDO> {


    protected PurchaseExchangeFlowCallBackHandler(FlowApproveHandler<PurchaseExchangeDO> approveHandler) {
        super(approveHandler);
    }
}
