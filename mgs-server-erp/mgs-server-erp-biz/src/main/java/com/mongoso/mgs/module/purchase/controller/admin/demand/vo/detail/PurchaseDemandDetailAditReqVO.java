package com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 采购需求明细 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class PurchaseDemandDetailAditReqVO{
    /** 采购需求明细ID */
    private Long purchaseDemandDetailId;

    /** 采购需求单ID */
    private Long purchaseDemandId;

    /** 采购需求单号 */
    private String purchaseDemandCode;

    /** 行号 */
    private Integer rowNo;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位 */
    private String mainUnitDictId;

    /** 规划需求数量 */
    private BigDecimal planDemandQty;

    /** 备注 */
    private String remark;

}
