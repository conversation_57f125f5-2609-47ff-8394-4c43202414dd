package com.mongoso.mgs.module.produce.service.workpicking;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingRespVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item.WorkPickingItemPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item.WorkPickingItemQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item.WorkPickingItemRespVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.related.RelatedWorkPickingPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.ReturnItemRespVO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工单领料单 Service 接口
 *
 * <AUTHOR>
 */
public interface WorkPickingService {

    /**
     * 创建工单领料单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long workPickingAdd(@Valid WorkPickingAditReqVO reqVO);

    /**
     * 更新工单领料单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long workPickingEdit(@Valid WorkPickingAditReqVO reqVO);

    /**
     * 删除工单领料单
     *
     * @param workPickingId 编号
     */
    void workPickingDel(Long workPickingId);

    ResultX<BatchResult> moldDelBatch(IdReq reqVO);

    /**
     * 获得工单领料单信息
     *
     * @param workPickingId 编号
     * @return 工单领料单信息
     */
    WorkPickingRespVO workPickingDetail(Long workPickingId);

    /**
     * 获得工单领料单被引用信息
     *
     * @param workPickingId 编号
     * @return 工单领料单信息
     */
    WorkPickingRespVO workPickingQuotedDetail(Long workPickingId);

    /**
     * 获得工单领料单列表
     *
     * @param reqVO 查询条件
     * @return 工单领料单列表
     */
    List<WorkPickingRespVO> workPickingList(@Valid WorkPickingQueryReqVO reqVO);

    /**
     * 获得工单领料单分页
     *
     * @param reqVO 查询条件
     * @return 工单领料单分页
     */
    PageResult<WorkPickingRespVO> workPickingPage(@Valid WorkPickingPageReqVO reqVO);


    BatchResult workPickingApprove(FlowApprove reqVO);

    Object workPickingFlowCallback(FlowCallback reqVO);

    PageResult<WorkPickingItemRespVO> workPickingItemPage(WorkPickingItemPageReqVO reqVO);

    List<WorkPickingItemRespVO> workPickingItemQuotedList(WorkPickingItemQueryReqVO reqVO);

    List<ReturnItemRespVO> returnRelatedWorkOrderPicker(RelatedWorkPickingPageReqVO reqVO);


    /**
     * 更新已出库数量
     * @param workPickingDetailId 领料单明细ID
     * @param outboundQty 出库数量
     */
    void updateOutboundedQty(Long workPickingDetailId, BigDecimal outboundQty);

}
