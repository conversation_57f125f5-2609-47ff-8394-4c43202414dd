package com.mongoso.mgs.module.produce.service.mold;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.mold.vo.moldreturn.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 模具归还单 Service 接口
 *
 * <AUTHOR>
 */
public interface MoldReturnService {

    /**
     * 创建模具领用单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long moldReturnAdd(@Valid MoldReturnAditReqVO reqVO);

    /**
     * 更新模具领用单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long moldReturnEdit(@Valid MoldReturnAditReqVO reqVO);

    /**
     * 删除模具领用单
     *
     * @param moldReturnId 编号
     */
    void moldReturnDel(Long moldReturnId);

    /**
     * 获得模具领用单信息
     *
     * @param moldReturnId 编号
     * @return 模具领用单信息
     */
    MoldReturnRespVO moldReturnDetail(Long moldReturnId);

    /**
     * 获得模具领用单列表
     *
     * @param reqVO 查询条件
     * @return 模具领用单列表
     */
    List<MoldReturnRespVO> moldReturnList(@Valid MoldReturnQueryReqVO reqVO);

    /**
     * 获得模具领用单分页
     *
     * @param reqVO 查询条件
     * @return 模具领用单分页
     */
    PageResult<MoldReturnRespVO> moldReturnPage(@Valid MoldReturnPageReqVO reqVO);

    ResultX<BatchResult> moldReturnDelBatch(IdReq reqVO);

    BatchResult moldReturnApprove(FlowApprove reqVO);

    Object moldReturnFlowCallback(FlowCallback reqVO);

    /**
     * 查询模具 加工模数
     *
     * @param reqVO
     * @return
     */
    List<MoldProcessModRespVO> findMoldProcessMod(MoldProcessModReqVO reqVO);
}
