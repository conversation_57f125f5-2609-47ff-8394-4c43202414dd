package com.mongoso.mgs.module.salary.controller.admin.salaryitem.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 薪酬项目归集 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SalaryItemBaseVO implements Serializable {

    /** 主键 */
    private Long salaryItemId;

    /** 创建人ID */
    private Long createdId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 业务类型 */
    private Integer bizType;

    /** 项目 */
    private Long itemId;

    /** 类型 */
    private Integer type;

}
