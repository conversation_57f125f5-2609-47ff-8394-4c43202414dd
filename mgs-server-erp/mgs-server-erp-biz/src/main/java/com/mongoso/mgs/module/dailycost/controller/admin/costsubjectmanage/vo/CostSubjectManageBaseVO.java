package com.mongoso.mgs.module.dailycost.controller.admin.costsubjectmanage.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 成本科目管理 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CostSubjectManageBaseVO implements Serializable {

    /** 成本科目管理ID */
    private Long costSubjectId;

    /** 成本科目编码 */
    private String costSubjectCode;

    /** 成本科目 */
    private String costSubjectName;

    /** 成本用途 */
    private Integer costUsage;

    /** 成本类型 */
    private Integer costType;

    /** 适用公司 */
    private String applyCompany;
    private String applyCompanyName;

    /** 费用名称关键字 */
    private String costNameKeyword;

    /** 费用名称优先级 */
    private Integer costNamePriority;

    /** 使用数量 */
    private Integer useNum;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 版本号 */
    private Integer version;

    public void setCostNameKeyword(String costNameKeyword) {
        if (costNameKeyword != null) {
            costNameKeyword = costNameKeyword.replace('，', ',');
        }
        this.costNameKeyword = costNameKeyword;
    }
}
