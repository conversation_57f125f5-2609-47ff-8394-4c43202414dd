package com.mongoso.mgs.module.produce.service.prodwork;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.material.MaterialEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.produce.DictToEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.ProdWorkRespAI;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.ProdWorkRespStatAI;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.employee.service.personal.bo.UserBaseRespBO;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.vo.DispatchWorkQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocess.vo.FlowProcessRespVO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocessdetail.vo.FlowProcessDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocessdetail.vo.FlowProcessDetailRespVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomRespVO;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkBO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.*;
import com.mongoso.mgs.module.produce.controller.admin.prodworkmaterialbom.vo.ProdWorkMaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodworkmaterialbom.vo.ProdWorkMaterialBomQuotedRespVO;
import com.mongoso.mgs.module.produce.controller.admin.prodworkmaterialbom.vo.ProdWorkMaterialBomRespVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.item.WorkPickingItemRespVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.related.MaterialAlternativeVO;
import com.mongoso.mgs.module.produce.dal.db.dispatchwork.DispatchWorkDO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.produce.dal.db.flowprocessdetail.FlowProcessDetailDO;
import com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.prodworkmaterialbom.ProdWorkMaterialBomDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingMaterialTotalDO;
import com.mongoso.mgs.module.produce.dal.mysql.dispatchwork.DispatchWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.flowprocessdetail.FlowProcessDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialalternative.MaterialAlternativeMapper;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodworkmaterialbom.ProdWorkMaterialBomMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMaterialTotalMapper;
import com.mongoso.mgs.module.produce.handler.approve.ProdWorkApproveHandler;
import com.mongoso.mgs.module.produce.handler.flowCallback.ProdWorkFlowCallBackHandler;
import com.mongoso.mgs.module.produce.service.flowprocess.FlowProcessService;
import com.mongoso.mgs.module.produce.service.materialbom.MaterialBomService;
import com.mongoso.mgs.module.produce.service.materialbom.bo.MaterialBO;
import com.mongoso.mgs.module.produce.service.prodworkmaterialbom.ProdWorkMaterialBomService;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailRespVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.materialstock.ErpMaterialStockDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;

/**
 * 生产工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProdWorkServiceImpl implements ProdWorkService {

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private StockBookService stockBookService;

    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;

    @Resource
    private FlowProcessDetailMapper flowProcessDetailMapper;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private MaterialBomService materialBomService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    @Lazy
    private ProdWorkApproveHandler prodWorkApproveHandler;

    @Resource
    private ProdWorkFlowCallBackHandler prodWorkFlowCallBackHandler;

    @Resource
    private DispatchWorkMapper dispatchWorkMapper;

    @Resource
    private ProdWorkMaterialBomMapper prodWorkMaterialBomMapper;

    @Resource
    private ProdWorkMaterialBomService prodWorkMaterialBomService;

    @Resource
    private FlowProcessService flowProcessService;

    @Resource
    private WorkPickingMaterialTotalMapper workPickingMaterialTotalMapper;

    @Resource
    private MaterialAlternativeMapper materialAlternativeMapper;

    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;
    @Resource
    private ProcessOutDemandMapper processOutDemandMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long prodWorkAdd(ProdWorkAditReqVO reqVO) {

        // 生成单号
        String code = seqService.getGenerateCode(reqVO.getProdWorkCode(), MenuEnum.PRODUCTION_WORK_ORDER_10636.menuId);

        // 插入
        Long prodWorkId = IDUtilX.getId();
        reqVO.setProdWorkId(prodWorkId);
        reqVO.setProdWorkCode(code);
        ProdWorkDO prodWork = BeanUtilX.copy(reqVO, ProdWorkDO::new);

        if (CollUtilX.isNotEmpty(reqVO.getDetailList())) {
            List<FlowProcessDetailDO> flowProcessDetailDOList = getDetailEditList(reqVO);
            flowProcessDetailMapper.insertBatch(flowProcessDetailDOList);
        }

        //新增物料bom清单
        addWorkMaterialBomList(reqVO);

        prodWorkMapper.insert(prodWork);

        // 返回
        return prodWork.getProdWorkId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long prodWorkAditBatch(ProdWorkBatchAditReqVO reqVO) {
        if (CollUtilX.isEmpty(reqVO.getDetailList())) {
            throw new BizException("500", "生产物料不允许为空!");
        }

        Long prodOrderId = reqVO.getProdOrderId();
        String prodOrderCode = reqVO.getProdOrderCode();
        Long directorId = reqVO.getDirectorId();
        String directorOrgId = reqVO.getDirectorOrgId();
        LocalDateTime formDt = reqVO.getFormDt();
        Integer processConfig = reqVO.getProcessConfig();
        Integer outProcessConfigDictId = reqVO.getOutProcessConfigDictId();
        Integer inProcessConfigDictId = reqVO.getInProcessConfigDictId();
        Integer dispatchStrategyConfig = reqVO.getDispatchStrategyConfig();

        //查询生产订单
        ErpProdOrderDO erpProdOrder = erpProdOrderMapper.selectById(prodOrderId);
        if (!(erpProdOrder.getFormStatus().equals(OrderStatusEnum.PENDING.getCode())
                || erpProdOrder.getFormStatus().equals(OrderStatusEnum.IN_PRODUCTION.getCode()))) {
            throw new BizException("500", "生产订单状态需待生产或生产中才可下发生产工单!");
        }

        //批量新增生产工单
        for (ProdWorkBaseVO item : reqVO.getDetailList()) {
            if (item.getMaterialSourceDictId() != null && !item.getMaterialSourceDictId().equals(MaterialEnum.SELF_MADE.key)) {
                continue;
            }

            ProdWorkAditReqVO prodWorkAdd = BeanUtilX.copy(item, ProdWorkAditReqVO::new);
            prodWorkAdd.setProdOrderId(prodOrderId);
            prodWorkAdd.setProdOrderCode(prodOrderCode);
            prodWorkAdd.setDirectorId(directorId);
            prodWorkAdd.setDirectorOrgId(directorOrgId);
            prodWorkAdd.setFormDt(formDt);
            prodWorkAdd.setFormStatus(0);
            prodWorkAdd.setProcessConfig(processConfig);
            prodWorkAdd.setOutProcessConfigDictId(outProcessConfigDictId);
            prodWorkAdd.setInProcessConfigDictId(inProcessConfigDictId);
            prodWorkAdd.setDispatchStrategyConfig(dispatchStrategyConfig);

            //查询工艺路线
            FlowProcessRespVO flowProcessRespVO = flowProcessService.flowProcessByMaterial(item.getMaterialId());
            if (flowProcessRespVO != null) {
                prodWorkAdd.setDetailList(flowProcessRespVO.getDetailList());
            }

            this.prodWorkAdd(prodWorkAdd);
        }
        return 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long prodWorkEdit(ProdWorkAditReqVO reqVO) {
        // 校验存在
//        this.prodWorkValidateExists(reqVO.getProdWorkId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.prodWorkValidateExists(reqVO.getProdWorkId()), reqVO);

        // 更新
        ProdWorkDO prodWork = BeanUtilX.copy(reqVO, ProdWorkDO::new);

        if (CollUtilX.isNotEmpty(reqVO.getDetailList())) {
            List<FlowProcessDetailDO> flowProcessDetailDOList = getDetailEditList(reqVO);
            flowProcessDetailMapper.batchDelete(reqVO.getProdWorkId(), null);
            flowProcessDetailMapper.insertBatch(flowProcessDetailDOList);
        }

        //新增物料bom清单
        prodWorkMaterialBomMapper.batchDelete(reqVO.getProdWorkId());
        addWorkMaterialBomList(reqVO);

        // 更新
        prodWorkMapper.updateById(prodWork);

        // 返回
        return prodWork.getProdWorkId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void prodWorkDel(Long prodWorkId) {
        // 校验存在
        ProdWorkDO currentDO = this.prodWorkValidateExists(prodWorkId);
        if (currentDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()) {
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        prodWorkMapper.deleteById(prodWorkId);
        flowProcessDetailMapper.batchDelete(prodWorkId, null);
        prodWorkMaterialBomMapper.batchDelete(prodWorkId);
    }

    private ProdWorkDO prodWorkValidateExists(Long prodWorkId) {
        ProdWorkDO prodWork = prodWorkMapper.selectById(prodWorkId);
        if (prodWork == null) {
            // throw exception(PROD_WORK_NOT_EXISTS);
            throw new BizException("5001", "生产工单不存在");
        }
        return prodWork;
    }

    @Override
    public ProdWorkRespVO prodWorkDetail(Long prodWorkId) {
        ProdWorkDO data = prodWorkMapper.selectById(prodWorkId);
        ProdWorkRespVO respVO = BeanUtilX.copy(data, ProdWorkRespVO::new);

        //VO属性填充
        fillVoProperties(Collections.singletonList(respVO));

        //查询物料bom清单
        ProdWorkMaterialBomQueryReqVO prodWorkMaterialBomQuery = new ProdWorkMaterialBomQueryReqVO();
        prodWorkMaterialBomQuery.setParentId(prodWorkId);
        List<ProdWorkMaterialBomRespVO> workMaterialBomResps = prodWorkMaterialBomService.prodWorkMaterialBomList(prodWorkMaterialBomQuery);
//        List<MaterialBomRespVO> bomDetailList = BeanUtilX.copy(workMaterialBomResps, MaterialBomRespVO::new);
//        List<MaterialBomRespVO> bomDetailList = materialBomService.findBomDetailByMaterialId(respVO.getMaterialId());
        respVO.setBomDetailList(workMaterialBomResps);

        //工序明细详情处理
        FlowProcessDetailQueryReqVO detailQuery = new FlowProcessDetailQueryReqVO();
        detailQuery.setFlowProcessId(respVO.getProdWorkId());
        List<FlowProcessDetailDO> detailDOList = flowProcessDetailMapper.selectListOld(detailQuery);
        List<FlowProcessDetailRespVO> detailRespVOS = BeanUtilX.copy(detailDOList, FlowProcessDetailRespVO::new);
        detailRespVOS = getDetailRespList(detailRespVOS);
        respVO.setDetailList(detailRespVOS);

        // 计算派工数量 - 通过工艺路线查询派工记录和委外需求清单
        BigDecimal dispatchQty = calculateMinDispatchQty(prodWorkId, detailDOList);
        respVO.setDispatchQty(dispatchQty);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(prodWorkId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    /**
     * 计算最小派工数量
     * 通过工艺路线查询派工记录明细和工序委外需求清单，取最小的派工数量
     *
     * @param prodWorkId   生产工单ID
     * @param detailDOList 工艺路线明细列表
     * @return 最小派工数量
     */
    private BigDecimal calculateMinDispatchQty(Long prodWorkId, List<FlowProcessDetailDO> detailDOList) {
        if (CollUtilX.isEmpty(detailDOList)) {
            return BigDecimal.ZERO;
        }

        // 存储所有工序的派工数量
        List<BigDecimal> processDispatchQtys = new ArrayList<>();

        // 1. 查询派工单记录
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setProdWorkId(prodWorkId);
        List<DispatchWorkDO> dispatchWorkDOList = Optional.ofNullable(dispatchWorkMapper.selectListOld(dispatchWorkQuery))
                .orElse(Collections.emptyList());

        // 按工序ID分组
        Map<Long, List<DispatchWorkDO>> processDispatchMap = dispatchWorkDOList.stream()
                .collect(Collectors.groupingBy(DispatchWorkDO::getProcessId));

        // 2. 查询工序委外需求清单
        ProcessOutDemandQueryReqVO outDemandQuery = new ProcessOutDemandQueryReqVO();
        outDemandQuery.setProdWorkId(prodWorkId);
        List<ProcessOutDemandDO> outDemandList = processOutDemandMapper.selectListOld(outDemandQuery);

        // 按工序ID分组
        Map<Long, List<ProcessOutDemandDO>> processOutDemandMap = outDemandList.stream()
                .collect(Collectors.groupingBy(ProcessOutDemandDO::getProcessId));

        // 3. 对每个工序，根据加工方式计算派工数量
        for (FlowProcessDetailDO processDetail : detailDOList) {
            Long processId = processDetail.getProcessId();
            Long processMethod = processDetail.getProcessMethod();
            BigDecimal processQty = BigDecimal.ZERO;

            // 根据加工方式计算派工数量
            if (processMethod.equals(DictToEnum.OUTSOURCE.key)) {
                // 委外工序，只考虑委外需求清单中的数量
                processQty = processOutDemandMap.getOrDefault(processId, Collections.emptyList()).stream()
                        .map(demand -> Optional.ofNullable(demand.getOutDemandQty()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                // 自制或自制+委外工序，合并派工单和委外需求清单中的数量
                BigDecimal selfMadeQty = processDispatchMap.getOrDefault(processId, Collections.emptyList()).stream()
                        .map(work -> Optional.ofNullable(work.getDispatchQty()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal outsourceQty = processOutDemandMap.getOrDefault(processId, Collections.emptyList()).stream()
                        .map(demand -> Optional.ofNullable(demand.getOutDemandQty()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 合并派工单和委外需求清单中的数量
                processQty = selfMadeQty.add(outsourceQty);
            }

            // 将该工序的派工数量添加到列表
            processDispatchQtys.add(processQty);
        }

        // 4. 找出最小的派工数量
        if (processDispatchQtys.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 返回最小的派工数量
        return processDispatchQtys.stream()
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public ProdWorkQuotedRespVO prodWorkQuotedDetail(Long prodWorkId) {
        ProdWorkDO data = prodWorkValidateExists(prodWorkId);
        ProdWorkQuotedRespVO respVO = BeanUtilX.copy(data, ProdWorkQuotedRespVO::new);

        //详情也是主表, 拼凑结构
        ProdWorkQuotedRespVO detailRespVO = BeanUtilX.copy(data, ProdWorkQuotedRespVO::new);
        //填充明细
        MaterialBO materialBO = erpBaseService.getMaterialByMaterialId(detailRespVO.getMaterialId());
        detailRespVO.setMaterialName(materialBO.getMaterialName());
        detailRespVO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
        detailRespVO.setMaterialCategoryDictName(materialBO.getMaterialCategoryDictName());
        detailRespVO.setMainUnitDictId(materialBO.getMainUnitDictId());
        detailRespVO.setMainUnitDictName(materialBO.getMainUnitDictName());
        detailRespVO.setSpecModel(materialBO.getSpecModel());
        detailRespVO.setSpecAttributeStr(materialBO.getSpecAttributeStr());
        respVO.getDetailList().add(detailRespVO);

        return respVO;
    }

    @Override
    public ProdWorkQuotedRespVO prodWorkDirectReturnQuotedDetail(Long prodWorkId) {
        ProdWorkDO data = prodWorkMapper.selectById(prodWorkId);
        ProdWorkQuotedRespVO respVO = BeanUtilX.copy(data, ProdWorkQuotedRespVO::new);

        List<WorkPickingMaterialTotalDO> list = workPickingMaterialTotalMapper.selectListByRelatedOrderId(prodWorkId);
        if (CollUtilX.isEmpty(list)) {
            return null;
        }

        List<Long> materialIds = list.stream().map(WorkPickingMaterialTotalDO::getMaterialId).collect(Collectors.toList());
        Map<Long, MaterialBO> bomMap = erpBaseService.getDataStatusMaterialByMaterialId(materialIds);

        List<ProdWorkQuotedRespVO> itemList = new ArrayList<>();

        for (WorkPickingMaterialTotalDO item : list) {

            ProdWorkQuotedRespVO copy = new ProdWorkQuotedRespVO();

            copy.setMaterialId(item.getMaterialId());
            copy.setMaterialCode(item.getMaterialCode());
            BigDecimal outboundedQty = item.getOutboundedQty();
            BigDecimal inboundedQty = item.getInboundedQty();

            // 可入库数量：出库数量-已入库数量
            BigDecimal subtract = outboundedQty.subtract(inboundedQty);
            // 可入库数量 <= 0， 不显示
            if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            copy.setInboundableQty(subtract);
            MaterialBO materialBO = bomMap.get(item.getMaterialId());
            if (materialBO != null) {
                copy.setMaterialName(materialBO.getMaterialName());
                copy.setMainUnitDictId(materialBO.getMainUnitDictId());
                copy.setMainUnitDictName(materialBO.getMainUnitDictName());

                copy.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
                copy.setMaterialCategoryDictName(materialBO.getMaterialCategoryDictName());

                copy.setSpecModel(materialBO.getSpecModel());
                copy.setSpecAttributeStr(materialBO.getSpecAttributeStr());
            }
            itemList.add(copy);
        }
        respVO.setDetailList(itemList);
        return respVO;
    }

    @Override
    public ProdWorkQuotedOutboundRespVO prodWorkQuotedDetailByOutbound(Long prodWorkId) {
        ProdWorkDO data = prodWorkMapper.selectById(prodWorkId);
        ProdWorkQuotedOutboundRespVO respVO = BeanUtilX.copy(data, ProdWorkQuotedOutboundRespVO::new);

        ProdWorkMaterialBomQueryReqVO queryBomReqVO = new ProdWorkMaterialBomQueryReqVO();
        queryBomReqVO.setParentId(prodWorkId);
        List<ProdWorkMaterialBomQuotedRespVO> prodWorkMaterialBomQuotedList = prodWorkMaterialBomService.prodWorkMaterialBomQuotedList(queryBomReqVO);
        respVO.getDetailList().addAll(prodWorkMaterialBomQuotedList);

        // 可操作数量处理
        for (ProdWorkMaterialBomQuotedRespVO detailRespVO : respVO.getDetailList()) {

            //计算可用数量
            BigDecimal availableQty = stockBookService.getAvailableQty(respVO.getProdWorkId(), detailRespVO.getMaterialId(),
                    detailRespVO.getWarehouseOrgId(), detailRespVO.getStockQty(), detailRespVO.getLockedQty());
            detailRespVO.setStockableQty(availableQty);

            //可操作数量处理
            if (detailRespVO.getStockableQty() == null) {
                detailRespVO.setOperableQty(BigDecimal.ZERO);
            } else if (detailRespVO.getOutboundableQty().compareTo(detailRespVO.getStockableQty()) <= 0) {
                detailRespVO.setOperableQty(detailRespVO.getOutboundableQty());
            } else {
                detailRespVO.setOperableQty(detailRespVO.getStockableQty());
            }
        }

        //处理可用数量小于等于0的数据
        List<ProdWorkMaterialBomQuotedRespVO> finalList = new ArrayList<>();
        Map<Long, List<ProdWorkMaterialBomQuotedRespVO>> materialIdMap = respVO.getDetailList().stream().collect(Collectors.groupingBy(ProdWorkMaterialBomQuotedRespVO::getMaterialId));
        for (Map.Entry<Long, List<ProdWorkMaterialBomQuotedRespVO>> entry : materialIdMap.entrySet()) {
            List<ProdWorkMaterialBomQuotedRespVO> detailRespVOList = entry.getValue();
            Integer flag = detailRespVOList.size();
            for (ProdWorkMaterialBomQuotedRespVO detailRespVO : detailRespVOList) {
                if (detailRespVO.getStockableQty() != null && detailRespVO.getStockableQty().compareTo(BigDecimal.ZERO) > 0) {
                    finalList.add(detailRespVO);
                } else {
                    flag--;
                }
            }
            if (flag == 0) {
                ProdWorkMaterialBomQuotedRespVO detailRespVO = detailRespVOList.get(0);
                detailRespVO.setWarehouseOrgId(null);
                detailRespVO.setWarehouseOrgName(null);
                detailRespVO.setOperableQty(null);
                detailRespVO.setStockableQty(null);
                detailRespVO.setStockQty(null);
                finalList.add(detailRespVO);
            }
        }

        finalList.sort(Comparator.comparing(ProdWorkMaterialBomQuotedRespVO::getRowNo));
        respVO.setDetailList(finalList);

        return respVO;
    }

    @Override
    public List<ProdWorkRespVO> prodWorkList(ProdWorkQueryReqVO reqVO) {
        List<ProdWorkDO> data = prodWorkMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProdWorkRespVO::new);
    }

    @Override
    public PageResult<ProdWorkRespVO> prodWorkPage(ProdWorkPageReqVO reqVO) {
        //物料编码名称查询
        if (StrUtilX.isNotEmpty(reqVO.getMaterialName())) {
            ERPMaterialQueryReqVO queryReq = new ERPMaterialQueryReqVO();
            queryReq.setMaterialName(reqVO.getMaterialName());
            List<Long> materialIdList = erpMaterialService.findMaterialIdList(queryReq);
            if (CollUtilX.isEmpty(materialIdList)) {
                return PageResult.empty();
            }

            reqVO.setMaterialIdList(materialIdList);
        }

        PageResult<ProdWorkDO> data = prodWorkMapper.selectPage(reqVO);
        PageResult<ProdWorkRespVO> pageResult = BeanUtilX.copy(data, ProdWorkRespVO::new);
        if (CollUtilX.isNotEmpty(pageResult.getList())) {
            //VO属性填充
            fillVoProperties(pageResult.getList());
        }
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> prodWorkDelBatch(IdReq reqVO) {
        // 删除
        String id = EntityUtilX.getPropertyName(ProdWorkDO::getProdWorkId);
        String code = EntityUtilX.getPropertyName(ProdWorkDO::getProdWorkCode);
        //删除生产工单对应的工艺路线
        flowProcessDetailMapper.batchDelete(null, reqVO.getIdList());
        return erpBaseService.batchDelete(reqVO.getIdList(), ProdWorkDO.class, null, id, code);

    }

    @Override
    public BatchResult prodWorkApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ProdWorkDO> list = prodWorkMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ProdWorkDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = prodWorkApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getProdOrderCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()) {
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ProdWorkDO item : list) {
                String reason = reasonMap.get(item.getProdWorkCode());
                if (StrUtilX.isEmpty(reason)) {
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProdWorkId());
                    messageInfoBO.setObjCode(item.getProdWorkCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                } else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProdWorkId());
                    messageInfoBO.setObjCode(item.getProdWorkCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object prodWorkFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        ProdWorkDO item = this.prodWorkValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return prodWorkFlowCallBackHandler.handleFlowCallback(item, flowCallbackBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean prodWorkStatus(ProdWorkPrimaryReqVO reqVO) {

        //查询生产工单
        ProdWorkDO prodWorkDO = prodWorkMapper.selectById(reqVO.getProdWorkId());
        Integer formStatus = prodWorkDO.getFormStatus();
        if (formStatus.equals(reqVO.getOpType())) {
            throw new BizException("500", "工单状态相同,不可重复操作!");
        }

        //已完工
        if (reqVO.getOpType() == OrderStatusEnum.COMPLETED.getCode()) {
            if (prodWorkDO.getWorkActTotalQty().compareTo(prodWorkDO.getWorkPlanTotalQty()) < 0) {
                throw new BizException("500", "实际生产数量小于计划生产数量,不允许进行完工!");
            }
        }

        //强制完工 || 已关闭
        if (reqVO.getOpType() == OrderStatusEnum.FORCE_COMPLETE.getCode() || reqVO.getOpType() == OrderStatusEnum.CLOSED.getCode()) {

            //查询 待生产|生产中 的派工单数据
            List<Integer> formStatusList = new ArrayList<>();
            formStatusList.add(OrderStatusEnum.PENDING.getCode());
            formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

            DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
            dispatchWorkQuery.setProdWorkId(reqVO.getProdWorkId());
            dispatchWorkQuery.setFormStatusList(formStatusList);

            List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
            if (CollUtilX.isNotEmpty(dispatchWorkDOList)) {
                //生产工单关闭,派工单直接关闭
                List<Long> dispatchWorkIdList = dispatchWorkDOList.stream().map(DispatchWorkDO::getDispatchWorkId).collect(Collectors.toList());
                dispatchWorkMapper.updateDispatchWorkStatus(dispatchWorkIdList);
            }
        }

        ProdWorkDO workCodeDO = new ProdWorkDO();

        //更新物料实际使用数量
//        updateBomActUsedQty(reqVO, prodWorkDO, workCodeDO);

        //更新可入库数量
        if (reqVO.getOpType() == OrderStatusEnum.COMPLETED.getCode()
                || reqVO.getOpType() == OrderStatusEnum.FORCE_COMPLETE.getCode()
                || reqVO.getOpType() == OrderStatusEnum.CLOSED.getCode()) {

            if (reqVO.getOpType() != OrderStatusEnum.CLOSED.getCode()) {
                workCodeDO.setActEndDate(LocalDate.now());
            }
        }
        //更新生产工单状态
        workCodeDO.setProdWorkId(reqVO.getProdWorkId());
        workCodeDO.setFormStatus(reqVO.getOpType());
        workCodeDO.setReason(reqVO.getReason());
        prodWorkMapper.updateById(workCodeDO);

        return true;
    }

    /**
     * 更新物流
     *
     * @param reqVO
     * @param prodWorkDO
     * @param workCodeDO
     */
    private void updateBomActUsedQty(ProdWorkPrimaryReqVO reqVO, ProdWorkDO prodWorkDO, ProdWorkDO workCodeDO) {
        if (reqVO.getOpType() != OrderStatusEnum.COMPLETED.getCode()
                && reqVO.getOpType() != OrderStatusEnum.FORCE_COMPLETE.getCode()
                && reqVO.getOpType() != OrderStatusEnum.CLOSED.getCode()) {
            return;
        }

        BigDecimal workActTotalQty = prodWorkDO.getWorkActTotalQty();
        if (workActTotalQty.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        //是否有倒冲领料
        boolean isHasBackflushQty = false;
        //更新BOM子物料实际使用数量
        ProdWorkMaterialBomQueryReqVO bomQueryReqVO = new ProdWorkMaterialBomQueryReqVO();
        bomQueryReqVO.setParentId(prodWorkDO.getProdWorkId());
        List<ProdWorkMaterialBomDO> bomDOList = prodWorkMaterialBomMapper.selectList(bomQueryReqVO);
        for (ProdWorkMaterialBomDO materialBomDO : bomDOList) {
            if (materialBomDO.getPickingMethodDictId() == Integer.valueOf(Math.toIntExact(DictToEnum.BACK_PRESSURE.key))) {
                continue;
            }
            BigDecimal actUsedQty = workActTotalQty.multiply(materialBomDO.getEstimatedQty());
            materialBomDO.setActUsedQty(actUsedQty);
            materialBomDO.setOutboundableQty(actUsedQty);
            materialBomDO.setIsMaterialFullOutbounded(0);
            isHasBackflushQty = true;
        }

        if (isHasBackflushQty) {
            workCodeDO.setIsFullOutbounded(0);
        }

        if (CollUtilX.isNotEmpty(bomDOList)) {
            prodWorkMaterialBomMapper.updateBatch(bomDOList);
        }
    }

    @Override
    public void writeBackProdWork(ProdWorkBO prodWorkBO) {
        ProdWorkDO prodWorkDO = prodWorkValidateExists(prodWorkBO.getProdWorkId());

        //实际生产数量
        BigDecimal okQty = prodWorkBO.getOkQty();
        BigDecimal workActTotalQty = prodWorkDO.getWorkActTotalQty();
        workActTotalQty = workActTotalQty.add(okQty);

        //不良品数量
        BigDecimal ngQty = prodWorkDO.getNgQty();
        ngQty = ngQty.add(prodWorkBO.getNgQty());

        //工单总数量
        BigDecimal prodWorkTotalQty = workActTotalQty.add(ngQty);

        //实际生产数量
        BigDecimal inboundedQty = prodWorkDO.getInboundedQty();
        BigDecimal inboundableQty = workActTotalQty.subtract(inboundedQty);

        //可入库数量
        prodWorkDO.setInboundableQty(inboundableQty);

        //手动入库
        if (prodWorkDO.getIsInbound() == 1 && prodWorkDO.getIsAutoInbound() == 0) {
            if (okQty.compareTo(BigDecimal.ZERO) < 0) {//反审
                prodWorkDO.setIsFullInbounded(1);
            } else if (okQty.compareTo(BigDecimal.ZERO) > 0) {//审核
                prodWorkDO.setIsFullInbounded(0);
            }
        }

        //工单状态
        Integer formStatus = prodWorkDO.getFormStatus();
        boolean isChange = formStatus.equals(OrderStatusEnum.COMPLETED.getCode()) || formStatus.equals(OrderStatusEnum.FORCE_COMPLETE.getCode()) || formStatus.equals(OrderStatusEnum.CLOSED.getCode());

        if (!isChange) {
            prodWorkDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
        }

        //是否有倒冲领料
        boolean isHasBackflushQty = false;
        //更新BOM子物料实际使用数量
        ProdWorkMaterialBomQueryReqVO bomQueryReqVO = new ProdWorkMaterialBomQueryReqVO();
        bomQueryReqVO.setParentId(prodWorkDO.getProdWorkId());
        List<ProdWorkMaterialBomDO> bomDOList = prodWorkMaterialBomMapper.selectList(bomQueryReqVO);
        for (ProdWorkMaterialBomDO materialBomDO : bomDOList) {
            if (materialBomDO.getPickingMethodDictId() != Integer.valueOf(Math.toIntExact(DictToEnum.BACK_PRESSURE.key))) {
                continue;
            }

            BigDecimal actUsedQty = prodWorkTotalQty.multiply(materialBomDO.getEstimatedQty());
            materialBomDO.setActUsedQty(actUsedQty);
            materialBomDO.setOutboundableQty(actUsedQty);
            materialBomDO.setIsMaterialFullOutbounded(0);
            isHasBackflushQty = true;
        }

        if (isHasBackflushQty) {
            prodWorkDO.setIsFullOutbounded(0);
        }

        if (CollUtilX.isNotEmpty(bomDOList)) {
            prodWorkMaterialBomMapper.updateBatch(bomDOList);
        }

        prodWorkDO.setWorkActTotalQty(workActTotalQty);
        prodWorkDO.setNgQty(ngQty);
        prodWorkMapper.updateById(prodWorkDO);
    }

    /**
     * 新增工单bom
     *
     * @param reqVO
     */
    private void addWorkMaterialBomList(ProdWorkAditReqVO reqVO) {
        List<ProdWorkMaterialBomRespVO> reqVOBomDetailList = reqVO.getBomDetailList();

        List<ProdWorkMaterialBomDO> workMaterialBomList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(reqVOBomDetailList)) {
            for (ProdWorkMaterialBomRespVO bomRespVO : reqVOBomDetailList) {
                bomRespVO.setMaterialBomId(null);
                bomRespVO.setParentId(reqVO.getProdWorkId());
                bomRespVO.setProdWorkCode(reqVO.getProdWorkCode());
                ProdWorkMaterialBomDO workMaterialBomDO = BeanUtilX.copy(bomRespVO, ProdWorkMaterialBomDO::new);
                workMaterialBomList.add(workMaterialBomDO);
            }
            prodWorkMaterialBomMapper.insertBatch(workMaterialBomList);
        } else {

            List<MaterialBomRespVO> bomDetailList = materialBomService.findBomDetailByMaterialId(reqVO.getMaterialId());
            if (CollUtilX.isNotEmpty(bomDetailList)) {

                BigDecimal workPlanTotalQty = reqVO.getWorkPlanTotalQty();

                for (MaterialBomRespVO bomRespVO : bomDetailList) {
                    bomRespVO.setMaterialBomId(null);
                    bomRespVO.setParentId(reqVO.getProdWorkId());
                    bomRespVO.setProdWorkCode(reqVO.getProdWorkCode());
                    ProdWorkMaterialBomDO workMaterialBomDO = BeanUtilX.copy(bomRespVO, ProdWorkMaterialBomDO::new);

                    BigDecimal oneDemandQty = bomRespVO.getDemandQty();
                    BigDecimal lossRate = bomRespVO.getLossRate();

                    BigDecimal lossRateRound = BigDecimal.ONE.add(lossRate.divide(BigDecimal.valueOf(100), 12, RoundingMode.HALF_UP));

                    //预估用量
                    BigDecimal estimatedQty = oneDemandQty.multiply(lossRateRound).setScale(6, RoundingMode.HALF_UP);
                    //预估总量
                    BigDecimal estimatedTotalQty = workPlanTotalQty.multiply(oneDemandQty.multiply(lossRateRound)).setScale(3, RoundingMode.HALF_UP);

                    workMaterialBomDO.setEstimatedQty(estimatedQty);
                    workMaterialBomDO.setEstimatedTotalQty(estimatedTotalQty);
                    workMaterialBomList.add(workMaterialBomDO);
                }
                prodWorkMaterialBomMapper.insertBatch(workMaterialBomList);
            }
        }
    }

    /**
     * 明细新增/编辑处理
     *
     * @param reqVO
     * @return
     */
    private List<FlowProcessDetailDO> getDetailEditList(ProdWorkAditReqVO reqVO) {
        List<FlowProcessDetailDO> detailDOS = new ArrayList<>();
        List<FlowProcessDetailRespVO> detailList = reqVO.getDetailList();
        Map<Integer, Long> rowNoMap = new HashMap<>();
        Integer maxRowNo = detailList.size();
        for (int i = 0; i < detailList.size(); i++) {
            FlowProcessDetailRespVO detail = detailList.get(i);

            rowNoMap.put(detail.getRowNo(), detail.getProcessId());

            //设值工艺路线外键
            detail.setFlowProcessId(reqVO.getProdWorkId());
            detail.setRelatedOrderCode(reqVO.getProdWorkCode());
            detail.setFlowProcessType(1);
            detail.setFlowProcessDetailId(null);

            //设置上一道工序
            if (detail.getRowNo() == 1) {
                detail.setPreProcessId(0L);
            } else {
                detail.setPreProcessId(rowNoMap.get(detail.getRowNo() - 1));
            }
            //设置最终工序
            if (maxRowNo.equals(detail.getRowNo())) {
                detail.setFinalProcess(1);
            }

            FlowProcessDetailDO detailDO = BeanUtilX.copy(detail, FlowProcessDetailDO::new);
            detailDOS.add(detailDO);

        }

        return detailDOS;
    }

    /**
     * VO属性填充
     *
     * @param itemList
     */
    private void fillVoProperties(List<ProdWorkRespVO> itemList) {
        if (CollUtilX.isEmpty(itemList)) {
            return;
        }

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        List<Long> prodWorkIdList = new ArrayList<>();
        for (ProdWorkRespVO item : itemList) {
            empIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());
            materialIdList.add(item.getMaterialId());
            prodWorkIdList.add(item.getProdWorkId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.PROD_WORK_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode(), SystemDictEnum.IN_PROCESS_CONFIG.getDictCode(), SystemDictEnum.OUT_PROCESS_CONFIG.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        //查询员工信息
        Map<Long, UserBaseRespBO> empNameMap = erpBaseService.getEmpByIdList(empIdList);

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        // 查询工单领料统计表中的所有数量信息（已领料、已退料、已入库、已出库）
        List<DocumentRespBO> documentList = workPickingMaterialTotalMapper.queryWorkPickingQty(prodWorkIdList);
        Map<Long, DocumentRespBO> documentMap = documentList.stream()
                .collect(Collectors.toMap(
                        DocumentRespBO::getRelatedOrderId,
                        Function.identity(),
                        (existing, replacement) -> {
                            // 如果有重复，累加各个数量
                            existing.setReceivedQty(Optional.ofNullable(existing.getReceivedQty()).orElse(BigDecimal.ZERO)
                                    .add(Optional.ofNullable(replacement.getReceivedQty()).orElse(BigDecimal.ZERO)));
                            existing.setReturnedQty(Optional.ofNullable(existing.getReturnedQty()).orElse(BigDecimal.ZERO)
                                    .add(Optional.ofNullable(replacement.getReturnedQty()).orElse(BigDecimal.ZERO)));
                            existing.setOutboundedQty(Optional.ofNullable(existing.getOutboundedQty()).orElse(BigDecimal.ZERO)
                                    .add(Optional.ofNullable(replacement.getOutboundedQty()).orElse(BigDecimal.ZERO)));
                            existing.setInboundedQty(Optional.ofNullable(existing.getInboundedQty()).orElse(BigDecimal.ZERO)
                                    .add(Optional.ofNullable(replacement.getInboundedQty()).orElse(BigDecimal.ZERO)));
                            return existing;
                        }
                ));

        for (ProdWorkRespVO item : itemList) {
            // 计算已领料数量
            DocumentRespBO document = documentMap.get(item.getProdWorkId());
            item.setReceivedQty(calculateReceivedQty(document));

            // 设置各种字典名称和关联信息
            //工单状态
            if (item.getFormStatus() != null) {
                String formStatus = SystemDictEnum.PROD_WORK_STATUS.getDictCode() + "-" + item.getFormStatus();
                item.setFormStatusDictName(dictMap.get(formStatus));
            }

            //审核状态
            if (item.getDataStatus() != null) {
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //入库流程配置
            if (item.getInProcessConfigDictId() != null) {
                String inProcessConfigDictId = SystemDictEnum.IN_PROCESS_CONFIG.getDictCode() + "-" + item.getInProcessConfigDictId();
                item.setInProcessConfigDictName(dictMap.get(inProcessConfigDictId));
            }

            //出库流程配置
            if (item.getOutProcessConfigDictId() != null) {
                String outProcessConfigDictId = SystemDictEnum.OUT_PROCESS_CONFIG.getDictCode() + "-" + item.getOutProcessConfigDictId();
                item.setOutProcessConfigDictName(dictMap.get(outProcessConfigDictId));
            }

            //查询负责人
            UserBaseRespBO dirEmployee = empNameMap.get(item.getDirectorId());
            if (dirEmployee != null) {
                item.setDirectorName(dirEmployee.getEmployeeName());
            }

            //查询责任部门
            item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(item.getMaterialId());
            if (erpMaterialDO != null) {
                item.setMaterialName(erpMaterialDO.getMaterialName());
                item.setSpecModel(erpMaterialDO.getSpecModel());
                item.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                item.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                item.setMainUnitDictId(erpMaterialDO.getMainUnitDictId());
                item.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                item.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
        }

    }

    /**
     * 计算工单已领料数量
     * 根据工单的出入库流程类型计算数量
     *
     * @param item     工单信息
     * @param document 工单领料统计信息
     * @return 已领料数量
     */
    private BigDecimal calculateReceivedQty( DocumentRespBO document) {
        if (document == null) {
            // 如果没有找到对应的记录，返回0
            return BigDecimal.ZERO;
        }

        // 使用已出库数量 - 已入库数量
        BigDecimal outboundedQty = Optional.ofNullable(document.getOutboundedQty()).orElse(BigDecimal.ZERO);
        BigDecimal inboundedQty = Optional.ofNullable(document.getInboundedQty()).orElse(BigDecimal.ZERO);
        return outboundedQty.subtract(inboundedQty);

    }

    /**
     * 查询工序明细
     *
     * @param detailRespVOS
     * @return
     */
    private List<FlowProcessDetailRespVO> getDetailRespList(List<FlowProcessDetailRespVO> detailRespVOS) {
        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.PIECE_WORK_METHOD.getDictCode(), SystemDictEnum.PIECEWORK_METHOD.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        for (FlowProcessDetailRespVO detail : detailRespVOS) {
            //计件方式
            if (StrUtilX.isNotEmpty(detail.getPieceworkMethodDictId())) {
                String pieceworkMethodDictId = SystemDictEnum.PIECE_WORK_METHOD.getDictCode() + "-" + detail.getPieceworkMethodDictId();
                detail.setPieceworkMethodDictName(dictMap.get(pieceworkMethodDictId));
            }

            //加工方式
            if (detail.getProcessMethod() != null) {
                String processMethod = SystemDictEnum.PIECEWORK_METHOD.getDictCode() + "-" + detail.getProcessMethod();
                detail.setProcessMethodName(dictMap.get(processMethod));
            }
        }

        return detailRespVOS;
    }


    @Override
    public void updateInboundQty(Long prodWorkId, BigDecimal inboundQty) {
        ProdWorkDO prodWorkDO = prodWorkMapper.selectById(prodWorkId);
        if (prodWorkDO == null) {
            return;
        }

        //已入库数量
        prodWorkDO.setInboundedQty(prodWorkDO.getInboundedQty().add(inboundQty));
        //可入库数量
        BigDecimal inboundeableQty = prodWorkDO.getWorkActTotalQty().subtract(prodWorkDO.getInboundedQty());
        prodWorkDO.setInboundableQty(inboundeableQty);
        //是否全部入库, 可入库数量为0,则为全部完成
        if (inboundeableQty.compareTo(BigDecimal.ZERO) == 0) {
            prodWorkDO.setIsFullInbounded(1);
        } else {
            prodWorkDO.setIsFullInbounded(0);
        }
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        prodWorkDO.setUpdatedBy(loginUser.getFullUserName());
        prodWorkDO.setUpdatedDt(LocalDateTime.now());
        prodWorkMapper.updateById(prodWorkDO);
    }


    @Override
    public List<ProdWorkDO> forewarnJob(Integer dataStatus, List<Integer> statusList) {
        return prodWorkMapper.forewarnJob(dataStatus, statusList);
    }

    @Override
    public List<ProdWorkRespAI> prodWorkPageAI() {
        // 获取今天的日期
        LocalDate today = LocalDate.now();

        // 获取本周的开始日期（周一）
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));

        // 获取本周的结束日期（周日）
        LocalDate endOfWeek = today.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY));

        List<ProdWorkDO> list = prodWorkMapper.selectListBy(startOfWeek, endOfWeek, DataStatusEnum.APPROVED.getKey());

        Set<Long> materialIds = list.stream().map(ProdWorkDO::getMaterialId).collect(Collectors.toSet());

        Map<Long, String> nameMap = erpMaterialService.nameMap(materialIds);

        List<ProdWorkRespAI> collect = list.stream().map(item -> {
            ProdWorkRespAI prodWorkRespAI = new ProdWorkRespAI();
            prodWorkRespAI.setProdWorkCode(item.getProdWorkCode());
            prodWorkRespAI.setMaterialCode(item.getMaterialCode());
            prodWorkRespAI.setMaterialName(nameMap.get(item.getMaterialId()));
            prodWorkRespAI.setWorkPlanTotalQty(item.getWorkPlanTotalQty());
            prodWorkRespAI.setWorkActTotalQty(item.getWorkActTotalQty());
            prodWorkRespAI.setPlanStartDate(item.getPlanStartDate());
            return prodWorkRespAI;
        }).collect(Collectors.toList());

        return collect;
    }

    @Override
    public ProdWorkRespStatAI prodWorkStatAI() {

        // 获取今天的日期
        LocalDate today = LocalDate.now();

        // 获取当月的开始日期（本月的第一天）
        LocalDate startOfMonth = today.withDayOfMonth(1);

        // 获取当月的结束日期（本月的最后一天）
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());

        List<ProdWorkDO> list = prodWorkMapper.selectListBy2(startOfMonth, endOfMonth);

        BigDecimal workPlanTotalQty = BigDecimal.ZERO;
        BigDecimal actTotalQty = BigDecimal.ZERO;

        for (ProdWorkDO item : list) {
            workPlanTotalQty = workPlanTotalQty.add(item.getWorkPlanTotalQty());
            actTotalQty = actTotalQty.add(item.getWorkActTotalQty());
        }

        BigDecimal ratio = BigDecimal.ZERO;
        if (workPlanTotalQty.compareTo(BigDecimal.ZERO) != 0) {
            ratio = actTotalQty.divide(workPlanTotalQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        ProdWorkRespStatAI prodWorkRespStatAI = new ProdWorkRespStatAI();
        prodWorkRespStatAI.setWorkPlanTotalQty(workPlanTotalQty);
        prodWorkRespStatAI.setWorkActTotalQty(actTotalQty);
        prodWorkRespStatAI.setRatio(ratio);
        return prodWorkRespStatAI;
    }

    @Override
    public List<ProdWorkMaterialDetailTreeRespVO> prodWorkMaterialDetailTree(ProdWorkMaterialDetailTreeReqVO reqVO) {

        List<ProdWorkMaterialBomDO> list = prodWorkMaterialBomMapper.selectListByRelatedOrderId(reqVO.getProdWorkId());
        if (CollUtilX.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> collect = list.stream().map(ProdWorkMaterialBomDO::getMaterialId).collect(Collectors.toList());
        Map<Long, MaterialBO> map = erpBaseService.getMaterialByMaterialId(collect);
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.MATERIAL_SOURCE.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);
        List<ProdWorkMaterialDetailTreeRespVO> detailRespVOS = new ArrayList<>();
        for (ProdWorkMaterialBomDO item : list) {
            ProdWorkMaterialDetailTreeRespVO bomMaterialBO = new ProdWorkMaterialDetailTreeRespVO();
            bomMaterialBO.setProdWorkId(reqVO.getProdWorkId());
            bomMaterialBO.setMaterialId(item.getMaterialId());
            bomMaterialBO.setMaterialCode(item.getMaterialCode());
            bomMaterialBO.setMainUnitDictId(item.getMainUnitDictId());
            bomMaterialBO.setDemandQty(item.getDemandQty());
            bomMaterialBO.setLossRate(item.getLossRate());
            bomMaterialBO.setEstimatedQty(item.getEstimatedQty());
            bomMaterialBO.setPickingMethodDictId(item.getPickingMethodDictId());
            MaterialBO materialBO = map.get(item.getMaterialId());
            if (materialBO != null) {
                bomMaterialBO.setMaterialName(materialBO.getMaterialName());
                bomMaterialBO.setSpecAttributeStr(materialBO.getSpecAttributeStr());
                bomMaterialBO.setMaterialCategoryDictId(materialBO.getMaterialCategoryDictId());
                bomMaterialBO.setMaterialCategoryDictName(materialBO.getMaterialCategoryDictName());
            }


            // 基本单位
            String mainUnitDictId = item.getMainUnitDictId();
            if (StrUtilX.isNotEmpty(mainUnitDictId)) {
                item.setMainUnitDictId(item.getMainUnitDictId());
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                bomMaterialBO.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }
            // 排除倒冲领料的物料
            if (item.getPickingMethodDictId() != null && item.getPickingMethodDictId()==Integer.valueOf(Math.toIntExact(DictToEnum.FORWARD.key))){
                detailRespVOS.add(bomMaterialBO);
            }
        }

//        List<ProdWorkMaterialDetailTreeRespVO> detailRespVOS2 = new ArrayList<>();
//        for (ProdWorkMaterialDetailTreeRespVO value : detailRespVOS) {
//            if(CollUtilX.isNotEmpty(reqVO.getExclMaterialIdList()) && reqVO.getExclMaterialIdList().contains(value.getMaterialId())){
//                // 排除掉工单
//                continue;
//            }
//
//            if(reqVO.getPickingMethodDictId() != null && value.getPickingMethodDictId()==Integer.valueOf(Math.toIntExact(DictToEnum.BACK_PRESSURE.key))){
//                // 排除倒冲领料的物料
//                continue;
//            }
//            detailRespVOS2.add(value);
//        }

        List<ProdWorkMaterialDetailTreeRespVO> detailTreeRespVOList = this.seletctMaterialStock(detailRespVOS);

        for (ProdWorkMaterialDetailTreeRespVO bomMaterialBO : detailTreeRespVOList) {
            // 查询子物料的替代物料
            List<MaterialAlternativeVO> materialAlternativeVOList = materialAlternativeMapper.selectListByMaterialId(bomMaterialBO.getMaterialId());
            if (CollUtilX.isEmpty(materialAlternativeVOList)) {
                continue;
            }
            List<ProdWorkMaterialDetailTreeRespVO> materialAlternativeList = new ArrayList<>();
            // 计算比率
            Map<Long, BigDecimal> qtyMap = new HashMap<>();
            List<Long> materialIdList = new ArrayList<>();
            for (MaterialAlternativeVO materialAlternativeVO : materialAlternativeVOList) {
                materialIdList.add(materialAlternativeVO.getMaterialId());
                BigDecimal mainQty = materialAlternativeVO.getMainQty();
                BigDecimal alternativeQty = materialAlternativeVO.getAlternativeQty();
                // 计算比率= 替换数量 / 主物料数量
                BigDecimal divide = alternativeQty.divide(mainQty, 2, BigDecimal.ROUND_HALF_UP);

                // 替换物料的数量 = 需求数量*比率
                qtyMap.put(materialAlternativeVO.getMaterialId(), bomMaterialBO.getDemandQty().multiply(divide));
            }
            Map<Long, MaterialBO> materialBOMapO = erpBaseService.getMaterialByMaterialId(materialIdList);
            for (Long materialId : materialIdList) {
                MaterialBO materialBO = materialBOMapO.get(materialId);
                if (materialBO == null) {
                    continue;
                }
                materialBO.setDemandQty(qtyMap.get(materialId));
                materialBO.setLossRate(BigDecimal.ZERO);// 替代物料默认是0
                materialAlternativeList.add(BeanUtilX.copy(materialBO, ProdWorkMaterialDetailTreeRespVO::new));
            }
            List<ProdWorkMaterialDetailTreeRespVO> sonDetailTreeRespVOList = this.seletctMaterialStock(materialAlternativeList);
            bomMaterialBO.setChildren(sonDetailTreeRespVOList);

        }

        //处理可用数量小于等于0的数据
        List<ProdWorkMaterialDetailTreeRespVO> finalList = new ArrayList<>();
        Map<Long, List<ProdWorkMaterialDetailTreeRespVO>> materialIdMap = detailTreeRespVOList.stream().collect(Collectors.groupingBy(ProdWorkMaterialDetailTreeRespVO::getMaterialId));
        for (Map.Entry<Long, List<ProdWorkMaterialDetailTreeRespVO>> entry : materialIdMap.entrySet()) {
            List<ProdWorkMaterialDetailTreeRespVO> detailRespVOList = entry.getValue();
            Integer flag = detailRespVOList.size();
            for (ProdWorkMaterialDetailTreeRespVO detailRespVO : detailRespVOList) {
                if (detailRespVO.getStockableQty() != null && detailRespVO.getStockableQty().compareTo(BigDecimal.ZERO) > 0) {
                    finalList.add(detailRespVO);
                } else {
                    flag--;
                }
            }
            if (flag == 0) {
                ProdWorkMaterialDetailTreeRespVO detailRespVO = detailRespVOList.get(0);
                detailRespVO.setWarehouseOrgId(null);
                detailRespVO.setWarehouseOrgName(null);
                detailRespVO.setStockableQty(null);
                detailRespVO.setStockQty(null);
                finalList.add(detailRespVO);
            }
        }
        return finalList;
    }


    private List<ProdWorkMaterialDetailTreeRespVO> seletctMaterialStock(List<ProdWorkMaterialDetailTreeRespVO> detailRespVOS) {
        List<ProdWorkMaterialDetailTreeRespVO> detailTreeRespVOList = new ArrayList<>();
        List<String> orgIds = new ArrayList<>();
        for (ProdWorkMaterialDetailTreeRespVO detailRespVO : detailRespVOS) {
            ErpMaterialStockQueryReqVO stockQueryReqVO = new ErpMaterialStockQueryReqVO();
            stockQueryReqVO.setMaterialId(detailRespVO.getMaterialId());
            List<ErpMaterialStockDO> materialStockList = erpMaterialStockMapper.selectList(stockQueryReqVO);
            if (CollUtilX.isNotEmpty(materialStockList)) {
                for (ErpMaterialStockDO materialStockDO : materialStockList) {
                    ProdWorkMaterialDetailTreeRespVO treeRespVO = new ProdWorkMaterialDetailTreeRespVO();
                    treeRespVO = BeanUtilX.copy(detailRespVO, ProdWorkMaterialDetailTreeRespVO::new);
                    treeRespVO.setWarehouseOrgId(materialStockDO.getWarehouseOrgId());
                    orgIds.add(materialStockDO.getWarehouseOrgId());
                    treeRespVO.setMaterialIdAndWarehouseOrgId(IDUtilX.getId());
                    treeRespVO.setStockQty(materialStockDO.getStockQty());
                    treeRespVO.setLockedQty(materialStockDO.getLockedQty());
                    treeRespVO.setAvailableQty(materialStockDO.getAvailableQty());
                    //计算可用数量
                    BigDecimal availableQty = stockBookService.getAvailableQty(treeRespVO.getProdWorkId(), treeRespVO.getMaterialId(),
                            treeRespVO.getWarehouseOrgId(), treeRespVO.getStockQty(), treeRespVO.getLockedQty());
                    treeRespVO.setStockableQty(availableQty);
                    if (availableQty.compareTo(BigDecimal.ZERO) > 0){
                        detailTreeRespVOList.add(treeRespVO);
                    }
                }
            } else {
                detailTreeRespVOList.add(detailRespVO);
            }
        }
        if (CollUtilX.isNotEmpty(detailTreeRespVOList)) {
            Map<String, String> orgNameMap = new HashMap<>();
            if (CollUtilX.isNotEmpty(orgIds)) {
                orgNameMap = erpBaseService.getOrgNameByIds(orgIds);
            }
            for (ProdWorkMaterialDetailTreeRespVO treeRespVO : detailTreeRespVOList) {
                if (treeRespVO.getWarehouseOrgId() != null) {
                    treeRespVO.setWarehouseOrgName(orgNameMap.get(treeRespVO.getWarehouseOrgId()));
                }
            }
            return detailTreeRespVOList;
        }
        return detailRespVOS;
    }
}
