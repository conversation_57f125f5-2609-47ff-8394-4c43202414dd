package com.mongoso.mgs.module.base.controller.admin.processconfig.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
import com.alibaba.fastjson.JSONObject;
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;



/**
 * 流程配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProcessConfigPageReqVO extends PageParam {

    /** 流程名称 */
    private String processName;

    /** 模块名称 */
    private String moduleName;

    /** 流程类型 */
    private Integer processType;

    /** 基本信息 */
    private JSONObject baseInfo;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
