package com.mongoso.mgs.module.warehouse.service.inventorysecuritystrategy;

import java.util.*;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import jakarta.validation.*;
import com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategy.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.inventorysecuritystrategy.InventorySecurityStrategyDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 安全库存策略 Service 接口
 *
 * <AUTHOR>
 */
public interface InventorySecurityStrategyService {

    /**
     * 创建安全库存策略
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long inventorySecurityStrategyAdd(@Valid InventorySecurityStrategyAditReqVO reqVO);

    /**
     * 更新安全库存策略
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long inventorySecurityStrategyEdit(@Valid InventorySecurityStrategyAditReqVO reqVO);

    /**
     * 删除安全库存策略
     *
     * @param inventorySecurityStrategyId 编号
     */
    void inventorySecurityStrategyDelete(Long inventorySecurityStrategyId);

    /**
     * 获得安全库存策略信息
     *
     * @param inventorySecurityStrategyId 编号
     * @return 安全库存策略信息
     */
    InventorySecurityStrategyRespVO inventorySecurityStrategyDetail(Long inventorySecurityStrategyId);

    /**
     * 获得安全库存策略列表
     *
     * @param reqVO 查询条件
     * @return 安全库存策略列表
     */
    List<InventorySecurityStrategyRespVO> inventorySecurityStrategyList(@Valid InventorySecurityStrategyQueryReqVO reqVO);

    /**
     * 获得安全库存策略分页
     *
     * @param reqVO 查询条件
     * @return 安全库存策略分页
     */
    PageResult<InventorySecurityStrategyRespVO> inventorySecurityStrategyPage(@Valid InventorySecurityStrategyPageReqVO reqVO);

    ResultX<BatchResult> inventorySecurityStrategyDeleteBatch(IdsReqVO reqVO);

    BatchResult inventorySecurityStrategyApprove(FlowApprove reqVO);

    Object inventorySecurityStrategyFlowCallback(FlowCallback reqVO);

    List<InventorySecurityStrategyReportRespVO> inventorySecurityStrategyReport(InventorySecurityStrategyReportReqVO reqVO);

    List<InventorySecurityStrategyReportRespVO> doforewarnJob();


}
