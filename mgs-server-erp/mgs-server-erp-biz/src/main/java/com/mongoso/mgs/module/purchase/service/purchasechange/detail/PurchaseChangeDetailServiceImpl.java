package com.mongoso.mgs.module.purchase.service.purchasechange.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.purchase.PurchaseBizTypeEnum;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.InvoiceApplyEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplan.vo.InvoicePlanQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.shouldpayment.vo.ShouldPaymentQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplan.InvoicePlanDO;
import com.mongoso.mgs.module.finance.dal.db.settlepool.SettlePoolDO;
import com.mongoso.mgs.module.finance.dal.db.shouldpayment.ShouldPaymentDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplan.InvoicePlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.settlepool.SettlePoolMapper;
import com.mongoso.mgs.module.finance.dal.mysql.shouldpayment.ShouldPaymentMapper;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.dal.db.purchasechange.PurchaseChangeDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchasechange.detail.PurchaseChangeDetailMapper;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.InvTypeManageQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.salechangdetail.vo.ChangUnitPriceCheckReqVO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购订单变更明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseChangeDetailServiceImpl implements PurchaseChangeDetailService {

    @Resource
    private PurchaseChangeDetailMapper changeDetailMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private InvTypeManageMapper invTypeManageMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private SettlePoolMapper poolMapper;

    @Resource
    private ShouldPaymentMapper shouldPaymentMapper;

    @Resource
    private InvoicePlanMapper planMapper;

    @Override
    public Long purchaseChangeDetailAdd(PurchaseChangeDetailAditReqVO reqVO) {
        // 插入
        PurchaseChangeDetailDO changeDetail = BeanUtilX.copy(reqVO, PurchaseChangeDetailDO::new);
        changeDetailMapper.insert(changeDetail);
        // 返回
        return changeDetail.getPurchaseChangeDetailId();
    }

    @Override
    public Long purchaseChangeDetailEdit(PurchaseChangeDetailAditReqVO reqVO) {
        // 校验存在
        this.purchaseChangeDetailValidateExists(reqVO.getPurchaseChangeDetailId());
        // 更新
        PurchaseChangeDetailDO changeDetail = BeanUtilX.copy(reqVO, PurchaseChangeDetailDO::new);
        changeDetailMapper.updateById(changeDetail);
        // 返回
        return changeDetail.getPurchaseChangeDetailId();
    }

    @Override
    public void purchaseChangeDetailDel(Long purchaseChangeDetailId) {
        // 校验存在
        this.purchaseChangeDetailValidateExists(purchaseChangeDetailId);
        // 删除
        changeDetailMapper.deleteById(purchaseChangeDetailId);
    }

    private PurchaseChangeDetailDO purchaseChangeDetailValidateExists(Long purchaseChangeDetailId) {
        PurchaseChangeDetailDO changeDetail = changeDetailMapper.selectById(purchaseChangeDetailId);
        if (changeDetail == null) {
            // throw exception(CHANGE_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "采购订单变更明细不存在");
        }
        return changeDetail;
    }

    @Override
    public PurchaseChangeDetailRespVO purchaseChangeDetailDetail(Long purchaseChangeDetailId) {
        PurchaseChangeDetailDO data = changeDetailMapper.selectById(purchaseChangeDetailId);
        return BeanUtilX.copy(data, PurchaseChangeDetailRespVO::new);
    }

    @Override
    public List<PurchaseChangeDetailRespVO> purchaseChangeDetailList(PurchaseChangeDetailQueryReqVO reqVO) {
        Short bizType = reqVO.getPurchaseOrderBizType();
        List<PurchaseChangeDetailRespVO> detailRespList = null;
        if (bizType == PurchaseBizTypeEnum.GENERAL_PURCHASE.type || bizType == PurchaseBizTypeEnum.OUTSOURCING_PURCHASE.type
                || bizType == PurchaseBizTypeEnum.SALE_PURCHASE.type){
            detailRespList = changeDetailMapper.queryList(reqVO);
        }
        if (bizType == PurchaseBizTypeEnum.DEMAND_PURCHASE.type){
            detailRespList = changeDetailMapper.queryList4Demand(reqVO);
        }
        if (bizType == PurchaseBizTypeEnum.PROD_OUT_PURCHASE.type){
            detailRespList = changeDetailMapper.queryList4Prod(reqVO);
        }
        if (bizType == PurchaseBizTypeEnum.MATERIAL_OUT_PURCHASE.type){
            detailRespList = changeDetailMapper.queryList4Material(reqVO);
        }

        //计算不可变动值
        PurchaseOrderDetailQueryReqVO detailQueryReqVO = new PurchaseOrderDetailQueryReqVO();
        detailQueryReqVO.setPurchaseOrderId(reqVO.getPurchaseOrderId());
//        if (reqVO.getPurchaseReceiptProcessConfig() != null){
//            if (reqVO.getPurchaseReceiptProcessConfig() == 1){//采购收货通知单
//                //以采购订单ID查询采购收货通知单的明细，计算出对应的采购收货通知数map，然后赋值给detailRespList
//                Map<Long, BigDecimal> noticeQtyMap = receiptNoticeDetailService.detailNoticeQtyMap(reqVO.getPurchaseOrderId());
//                for (PurchaseChangeDetailRespVO detailRespVO : detailRespList) {
//                    detailRespVO.setNonChangeValue(detailRespVO.getNonChangeValue()
//                            .add(noticeQtyMap.get(detailRespVO.getMaterialId()) == null?BigDecimal.ZERO:noticeQtyMap.get(detailRespVO.getMaterialId())));
//                }
//            }
//        }
        this.batchFillVoProperties(detailRespList);
        return detailRespList;
    }

    @Override
    public PageResult<PurchaseChangeDetailRespVO> purchaseChangeDetailPage(PurchaseChangeDetailPageReqVO reqVO) {
        IPage<PurchaseChangeDetailRespVO> respVOIPage = changeDetailMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<PurchaseChangeDetailRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoPropertiesForPage(pageResult.getList());
        return pageResult;
    }

    @Override
    public void purChangUnitPriceCheck(ChangUnitPriceCheckReqVO reqVO) {
        Long materialId = reqVO.getMaterialId();
        Short rowNo = reqVO.getRowNo();

        Long originOrderId = reqVO.getPurchaseOrderId();
        Short formType = 2;

        //对账状态
        List<Integer> accountStatusList = Arrays.asList(OrderStatusEnum.ACCOUNTING.getCode(),OrderStatusEnum.ACCOUNTED.getCode());

        //查询结算池
        SettlePoolQueryReqVO settlePoolQuery = new SettlePoolQueryReqVO();
        settlePoolQuery.setOriginOrderId(originOrderId);
        settlePoolQuery.setMaterialId(materialId);
        settlePoolQuery.setSourceLineNumber(rowNo);
        settlePoolQuery.setAccountStatusList(accountStatusList);
        settlePoolQuery.setFormType(formType);
        List<SettlePoolDO> settlePoolList = poolMapper.selectListOld(settlePoolQuery);
        if (CollUtilX.isNotEmpty(settlePoolList)){
            throw new BizException("500","结算池存在已审核的下游数据,不允许修改物料单价和票据类型");
        }

        Short dataStatus = Short.valueOf(String.valueOf(DataStatusEnum.APPROVED.key));

        //对账状态
        List<Integer> exclAccountsPayableStatus = Arrays.asList(OrderStatusEnum.NOT_PAYMENT.getCode(),OrderStatusEnum.NOT_SHOULD.getCode());
        //查询应收帐款
        ShouldPaymentQueryReqVO shouldPaymentQuery = new ShouldPaymentQueryReqVO();
        shouldPaymentQuery.setDataStatus(dataStatus);
        shouldPaymentQuery.setOriginOrderId(originOrderId);
        shouldPaymentQuery.setExclAccountsPayableStatus(exclAccountsPayableStatus);
        List<ShouldPaymentDO> shouldPaymentList = shouldPaymentMapper.selectListOld(shouldPaymentQuery);
        if (CollUtilX.isNotEmpty(shouldPaymentList)){
            throw new BizException("500","应付帐款存在已审核的下游数据,不允许修改物料单价和票据类型");
        }

        //查询开票计划
        InvoicePlanQueryReqVO invoicePlanQuery = new InvoicePlanQueryReqVO();
        invoicePlanQuery.setDataStatus(dataStatus);
        invoicePlanQuery.setOriginOrderId(originOrderId);
        invoicePlanQuery.setInvoiceApplyStatusNe(InvoiceApplyEnum.NOT_INVOICE.type);
        List<InvoicePlanDO> invoicePlanList = planMapper.selectListOld(invoicePlanQuery);
        if (CollUtilX.isNotEmpty(invoicePlanList)){
            throw new BizException("500","收票计划存在已审核的下游数据,不允许修改物料单价和票据类型");
        }
    }

    private void batchFillVoProperties(List<PurchaseChangeDetailRespVO> detailRespList) {
        if (CollUtilX.isEmpty(detailRespList)){
            return;
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());

        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        for (PurchaseChangeDetailRespVO detailResp: detailRespList){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());

            //不可变动值
            BigDecimal noChangeValue = MathUtilX.getMaxQty(detailResp.getNonChangeValue(),detailResp.getReconciledQty(),detailResp.getPlanedQty(),detailResp.getReceivedQty());
            detailResp.setNonChangeValue(noChangeValue);
        }

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }



        for (PurchaseChangeDetailRespVO detailResp: detailRespList){
            detailResp.setInvoiceTypeName(invTypeMap.get(detailResp.getInvoiceTypeId()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialDO!=null){
                detailResp.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
                detailResp.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detailResp.setSpecModel(erpMaterialDO.getSpecModel());
            }
            detailResp.setMainUnitDictName(dictMap.get(detailResp.getMainUnitDictId()));
        }
    }

    private void batchFillVoPropertiesForPage(List<PurchaseChangeDetailRespVO> detailRespList) {
        if (CollUtilX.isEmpty(detailRespList)){
            return;
        }



        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for (PurchaseChangeDetailRespVO detailResp: detailRespList){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
            supplierIdList.add(detailResp.getRelatedSupplierId());
            directorIdList.add(detailResp.getDirectorId());
            directorOrgIdList.add(detailResp.getDirectorOrgId());
            directorOrgIdList.add(detailResp.getCompanyOrgId());
        }

        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeMap = new HashMap<>();
        //查询票据类型Map
        if (CollUtilX.isNotEmpty(invoiceTypeIdList)){
            InvTypeManageQueryReqVO invTypeManageQueryReqVO = new InvTypeManageQueryReqVO();
            invTypeManageQueryReqVO.setInvoiceTypeIdList(invoiceTypeIdList);
            List<InvTypeManageDO> invTypeManageDOS = invTypeManageMapper.selectList(invTypeManageQueryReqVO);
            invTypeMap = invTypeManageDOS.stream()
                    .collect(Collectors.toMap(InvTypeManageDO::getInvoiceTypeId, InvTypeManageDO::getInvoiceName));
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.CURRENCY.getDictCode(),
                CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.PURCHASE_PAYMENT_TERMS.getDictCode(),
                CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for (PurchaseChangeDetailRespVO detailResp: detailRespList){
            //供应商
            detailResp.setRelatedSupplierName(supplierNameMap.get(detailResp.getRelatedSupplierId()));
            //公司主体
            detailResp.setCompanyOrgName(directorOrgMap.get(detailResp.getCompanyOrgId()));
            //责任部门
            detailResp.setDirectorOrgName(directorOrgMap.get(detailResp.getDirectorOrgId()));
            //责任人
            detailResp.setDirectorName(directorMap.get(detailResp.getDirectorId()));
            //票据类型
            detailResp.setInvoiceTypeName(invTypeMap.get(detailResp.getInvoiceTypeId()));
            // 采购单类型
            String purchaseTypeDictId = detailResp.getPurchaseTypeDictId();
            if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
                purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
                detailResp.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            // 币种
            String currencyDictId = detailResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                detailResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = detailResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                detailResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = detailResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                detailResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 收款条件
            String paymentTermsDictId = detailResp.getPaymentTermsDictId();
            if(StrUtilX.isNotEmpty(paymentTermsDictId)){
                paymentTermsDictId = CustomerDictEnum.PURCHASE_PAYMENT_TERMS.getDictCode() + "-" + paymentTermsDictId;
                detailResp.setPaymentTermsDictName(dictMap.get(paymentTermsDictId));
            }

            // 物料类别
            String materialCategoryDictId = detailResp.getMaterialCategoryDictId();
            if(StrUtilX.isNotEmpty(materialCategoryDictId)){
                materialCategoryDictId = CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
                detailResp.setMaterialCategoryDictName(dictMap.get(materialCategoryDictId));
            }

            // 基本单位
            String mainUnitDictId = detailResp.getMainUnitDictId();
            if(StrUtilX.isNotEmpty(mainUnitDictId)) {
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                detailResp.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            // 审核状态
            if(detailResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + detailResp.getDataStatus();
                detailResp.setDataStatusDictName(dictMap.get(dataStatus));;
            }

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialDO!=null){
                detailResp.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
                detailResp.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detailResp.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detailResp.setSpecModel(erpMaterialDO.getSpecModel());
            }
        }
    }

}
