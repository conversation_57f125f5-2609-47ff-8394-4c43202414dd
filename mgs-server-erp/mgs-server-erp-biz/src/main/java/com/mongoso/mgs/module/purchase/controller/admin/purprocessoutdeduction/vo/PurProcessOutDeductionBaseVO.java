package com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeduction.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
 import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 工序委外采购扣费单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PurProcessOutDeductionBaseVO implements Serializable {

    /** 主键ID */
    private Long processOutDeductionId;

    /** 工序委外采购扣费单号 */
    private String processOutDeductionCode;

    /** 工序委外采购订单ID */
    private Long purchaseProcessOutId;

    /** 工序委外采购订单code */
    private String purchaseProcessOutCode;

    /** 关联供应商 */
    private Long relatedSupplierId;
    private String relatedSupplierName;

    /** 联系人名称 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate deliveryDate;

    /** 退货日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate returnDate;

    /** 币种 */
    private String currencyDictId;
    private String currencyDictName;

    /** 主体公司 */
    private String companyOrgId;
    private String companyOrgName;

    /** 退款条件 */
    private String refundConditionDictId;
    private String refundConditionDictName;

    /** 结算方式 */
    private String settlementMethodDictId;
    private String settlementMethodDictName;

    /** 扣费总金额(不含税) */
    private BigDecimal exclTaxTotalAmt;

    /** 扣费总金额(含税) */
    private BigDecimal inclTaxTotalAmt;

    /** 票据类型 */
    private Long invoiceTypeId;
    private String invoiceTypeName;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币订单总金额 */
    private BigDecimal inclTaxLocalCurrencyTotalAmt;

}
