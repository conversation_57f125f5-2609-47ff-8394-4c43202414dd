package com.mongoso.mgs.module.sale.service.erpsaleorderdetail;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.material.MaterialEnum;
import com.mongoso.mgs.common.enums.order.OrderCodeEnum;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.service.materialanalysis.bo.MaterialBomTreeBO;
import com.mongoso.mgs.module.produce.service.materialbom.MaterialBomService;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.detail.PurchaseOrderDetailMapper;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.bo.ErpSaleOrderBO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailAditReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailPageReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.sale.controller.admin.saledeductiondetail.vo.SaleDeductionDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.saleexchangedetail.vo.SaleExchangeDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.salereturndetail.vo.SaleReturnDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo.ShipNoticeDetailQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail.ErpSaleOrderDetailDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.saledeductiondetail.SaleDeductionDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.saleexchangedetail.SaleExchangeDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.salereturndetail.SaleReturnDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.shipnoticedetail.SaleShipNoticeDetailMapper;
import com.mongoso.mgs.module.sale.enums.ConfirmStatusEnum;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.bo.ErpProdOrderDetailBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.common.enums.order.OrderCodeEnum.*;


/**
 * 销售订单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ErpSaleOrderDetailServiceImpl implements ErpSaleOrderDetailService {

    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Resource
    private ErpSaleOrderDetailMapper erpSaleOrderDetailMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private SaleDeductionDetailMapper saleDeductionDetailMapper;

    @Resource
    private SaleReturnDetailMapper saleReturnDetailMapper;

    @Resource
    private SaleShipNoticeDetailMapper saleShipNoticeDetailMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private SaleExchangeDetailMapper saleExchangeDetailMapper;

    @Resource
    private MaterialBomService materialBomService;

    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Override
    public Long erpSaleOrderDetailAdd(ErpSaleOrderDetailAditReqVO reqVO) {
        // 插入
        ErpSaleOrderDetailDO erpSaleOrderDetail = BeanUtilX.copy(reqVO, ErpSaleOrderDetailDO::new);
        erpSaleOrderDetailMapper.insert(erpSaleOrderDetail);
        // 返回
        return erpSaleOrderDetail.getSaleOrderDetailId();
    }

    @Override
    public Long erpSaleOrderDetailEdit(ErpSaleOrderDetailAditReqVO reqVO) {
        // 校验存在
        this.erpSaleOrderDetailValidateExists(reqVO.getSaleOrderId());
        // 更新
        ErpSaleOrderDetailDO erpSaleOrderDetail = BeanUtilX.copy(reqVO, ErpSaleOrderDetailDO::new);
        erpSaleOrderDetailMapper.updateById(erpSaleOrderDetail);
        // 返回
        return erpSaleOrderDetail.getSaleOrderDetailId();
    }

    @Override
    public void erpSaleOrderDetailDel(Long id) {
        // 校验存在
        this.erpSaleOrderDetailValidateExists(id);
        // 删除
        erpSaleOrderDetailMapper.deleteById(id);
    }

    private ErpSaleOrderDetailDO erpSaleOrderDetailValidateExists(Long id) {
        ErpSaleOrderDetailDO erpSaleOrderDetail = erpSaleOrderDetailMapper.selectById(id);
        if (erpSaleOrderDetail == null) {
            // throw exception(ERP_SALE_ORDER_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "销售订单明细不存在");
        }
        return erpSaleOrderDetail;
    }

    @Override
    public ErpSaleOrderDetailDO erpSaleOrderDetailDetail(Long id) {
        return erpSaleOrderDetailMapper.selectById(id);
    }

    @Override
    public List<ErpSaleOrderDetailRespVO> erpSaleOrderDetailList(ErpSaleOrderDetailQueryReqVO reqVO) {
        List<ErpSaleOrderDetailDO> erpSaleOrderDetailDOList = erpSaleOrderDetailMapper.selectList(reqVO);
        List<ErpSaleOrderDetailRespVO> detailRespVOS = new ArrayList<>();

        if (CollUtilX.isEmpty(erpSaleOrderDetailDOList)){
            return detailRespVOS;
        }

        //查询物料信息
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        List<Long> materialIdList = new ArrayList<>();
        for (ErpSaleOrderDetailDO detailDO: erpSaleOrderDetailDOList){
            materialIdList.add(detailDO.getMaterialId());
            ErpSaleOrderDetailRespVO detailRespVO = BeanUtilX.copy(detailDO, ErpSaleOrderDetailRespVO::new);

            // 销售订单总数 = 关联销售订单的物料数量
            BigDecimal qty = detailDO.getQty();
            if (qty == null) {
                qty = BigDecimal.ZERO;
            }
            detailRespVO.setSaleOrderMaterialQty(qty);
            detailRespVOS.add(detailRespVO);

        }

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());


        // 查询销售订单的物料总和
        Long saleOrderId = reqVO.getSaleOrderId();
        // 根据销售订单和物料id，查询所有的物料的生产数量
        List<ErpProdOrderDetailBO> erpProdOrderDetailBOList = erpProdOrderMapper.selectListByRelatedOrderIdAndMaterialIdList(saleOrderId,materialIdList);
        // 转成map
        Map<Long, BigDecimal> qtyMap = erpProdOrderDetailBOList.stream().collect(Collectors.toMap(ErpProdOrderDetailBO::getMaterialId, ErpProdOrderDetailBO::getProdPlanQty));

        //填充物料信息
        for (ErpSaleOrderDetailRespVO deatilResp: detailRespVOS){
            ERPMaterialRespVO erpMaterial = erpMaterialDOMap.get(deatilResp.getMaterialId());
            if (erpMaterial!=null){
                deatilResp.setMaterialCode(erpMaterial.getMaterialCode());
                deatilResp.setMaterialName(erpMaterial.getMaterialName());
                deatilResp.setMaterialCategoryDictId(erpMaterial.getMaterialCategoryDictId());
                deatilResp.setMaterialCategoryDictName(erpMaterial.getMaterialCategoryDictName());
                deatilResp.setSpecModel(erpMaterial.getSpecModel());
                deatilResp.setSpecAttributeStr(erpMaterial.getSpecAttributeStr());
                deatilResp.setMaterialSourceDictId(erpMaterial.getMaterialSourceDictId());
                deatilResp.setMaterialSourceDictName(erpMaterial.getMaterialSourceDictName());
            }

            //基本单位
            if (deatilResp.getMainUnitDictId()!=null){
                deatilResp.setMainUnitDictName(dictMap.get(deatilResp.getMainUnitDictId()));
            }

            BigDecimal issueOrderQty = qtyMap.get(deatilResp.getMaterialId());
            if (issueOrderQty == null) {
                issueOrderQty = BigDecimal.ZERO;
            }
            // 已下发订单总数 = 销售订单关联的生产订单的物料总数
            deatilResp.setIssueOrderQty(issueOrderQty);
        }

        return detailRespVOS;
    }

    @Override
    public PageResult<ErpSaleOrderDetailDO> erpSaleOrderDetailPage(ErpSaleOrderDetailPageReqVO reqVO) {
        return erpSaleOrderDetailMapper.selectPage(reqVO);
    }

    @Override
    public List<ErpSaleOrderDetailRespVO> erpShipNoticelQuoteList(ErpSaleOrderDetailQueryReqVO reqVO) {
        if (reqVO.getSaleOrderId() == null){
            throw new BizException("500","销售订单id不允许为空");
        }

        //下游单据查询
        ShipNoticeDetailQueryReqVO shipNoticeDetailQuery = new ShipNoticeDetailQueryReqVO();
        shipNoticeDetailQuery.setSaleOrderId(reqVO.getSaleOrderId());
        shipNoticeDetailQuery.setExclMaterialIdList(reqVO.getExclMaterialIdList());
        shipNoticeDetailQuery.setConfirmStatus(ConfirmStatusEnum.APPROVED.type);
        List<DocumentRespBO> respBOList = saleShipNoticeDetailMapper.sumQty(shipNoticeDetailQuery);
        Map<Long, BigDecimal> quoteMaterialQtyMap = respBOList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, DocumentRespBO::getSumQty));

        //物料列表值处理
        List<ErpSaleOrderDetailRespVO> detailRespVOS = findErpSaleOrderDetailList(SHIP_NOTICE_CODE, reqVO, quoteMaterialQtyMap);

        return detailRespVOS;
    }

    @Override
    public List<ErpSaleOrderDetailRespVO> erpSaleDeductionQuoteList(ErpSaleOrderDetailQueryReqVO reqVO) {
        if (reqVO.getSaleOrderId() == null){
            throw new BizException("500","销售订单id不允许为空");
        }

        //下游单据物料数量查询
        Map<Long, BigDecimal> quoteMaterialQtyMap = quoteMaterialQtyMap(reqVO);

        //物料列表值处理
        List<ErpSaleOrderDetailRespVO> detailRespVOS = findErpSaleOrderDetailList(DEDUCTION_ORDER_CODE, reqVO, quoteMaterialQtyMap);

        return detailRespVOS;
    }

    @Override
    public List<ErpSaleOrderDetailRespVO> erpSaleReturnQuoteList(ErpSaleOrderDetailQueryReqVO reqVO) {
        if (reqVO.getSaleOrderId() == null){
            throw new BizException("500","销售订单id不允许为空");
        }

        //下游单据物料数量查询
        Map<Long, BigDecimal> quoteMaterialQtyMap = quoteMaterialQtyMap(reqVO);
        //物料列表值处理
        List<ErpSaleOrderDetailRespVO> detailRespVOS = findErpSaleOrderDetailList(SALES_RETURN_CODE, reqVO, quoteMaterialQtyMap);

        return detailRespVOS;
    }

    @Override
    public List<ErpSaleOrderDetailRespVO> erpSaleExchangeQuoteList(ErpSaleOrderDetailQueryReqVO reqVO) {
        if (reqVO.getSaleOrderId() == null){
            throw new BizException("500","销售订单id不允许为空");
        }

        //下游单据物料数量查询
        Map<Long, BigDecimal> quoteMaterialQtyMap = quoteMaterialQtyMap(reqVO);
        //物料列表值处理
        List<ErpSaleOrderDetailRespVO> detailRespVOS = findErpSaleOrderDetailList(EXCHANGE_ORDER_CODE, reqVO, quoteMaterialQtyMap);

        return detailRespVOS;
    }

    private Map<Long, BigDecimal> quoteMaterialQtyMap(ErpSaleOrderDetailQueryReqVO reqVO) {
        List<DocumentRespBO> resultList = new ArrayList<>();

        //下游销售扣费单查询
        SaleDeductionDetailQueryReqVO saleDeductionDetailQuery = new SaleDeductionDetailQueryReqVO();
        saleDeductionDetailQuery.setSaleOrderId(reqVO.getSaleOrderId());
        saleDeductionDetailQuery.setExclMaterialIdList(reqVO.getExclMaterialIdList());
        List<DocumentRespBO> saleDeductionResps = saleDeductionDetailMapper.sumQty(saleDeductionDetailQuery);
        resultList.addAll(saleDeductionResps);


        //下游销售退货单查询
        SaleReturnDetailQueryReqVO saleReturnDetailQuery = new SaleReturnDetailQueryReqVO();
        saleReturnDetailQuery.setSaleOrderId(reqVO.getSaleOrderId());
        saleReturnDetailQuery.setExclMaterialIdList(reqVO.getExclMaterialIdList());
        List<DocumentRespBO> saleReturnResps = saleReturnDetailMapper.sumQty(saleReturnDetailQuery);
        resultList.addAll(saleReturnResps);

        //下游销售换货单查询
        SaleExchangeDetailQueryReqVO exchangeDetailQueryReqVO = new SaleExchangeDetailQueryReqVO();
        exchangeDetailQueryReqVO.setSaleOrderId(reqVO.getSaleOrderId());
        exchangeDetailQueryReqVO.setExclMaterialIdList(reqVO.getExclMaterialIdList());
        List<DocumentRespBO> saleExchangeResps = saleExchangeDetailMapper.sumQty(exchangeDetailQueryReqVO);
        resultList.addAll(saleExchangeResps);

        Map<Long, BigDecimal> quoteMaterialQtyMap = new HashMap<>();
        for (DocumentRespBO respBO : resultList){
            BigDecimal qty = quoteMaterialQtyMap.get(respBO.getFieldId());
            if (qty == null){
                quoteMaterialQtyMap.put(respBO.getFieldId(),respBO.getSumQty());
            }else {
                qty = qty.add(respBO.getSumQty());
                quoteMaterialQtyMap.put(respBO.getFieldId(),qty);
            }
        }

        return quoteMaterialQtyMap;
    }

    /**
     *  物料列表值处理
     *
     * @param codeEnum
     * @param reqVO
     * @param quoteMaterialQtyMap
     * @return
     */
    private List<ErpSaleOrderDetailRespVO> findErpSaleOrderDetailList(OrderCodeEnum codeEnum, ErpSaleOrderDetailQueryReqVO reqVO, Map<Long, BigDecimal> quoteMaterialQtyMap) {

        //查询销售订单产品明细信息
        List<ErpSaleOrderDetailDO> erpSaleOrderDetailDOList = erpSaleOrderDetailMapper.selectList(reqVO);
        List<ErpSaleOrderDetailRespVO> detailRespVOS = BeanUtilX.copy(erpSaleOrderDetailDOList, ErpSaleOrderDetailRespVO::new);

        //查询物料信息
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        List<Long> materialIdList = new ArrayList<>();
        for (ErpSaleOrderDetailRespVO deatilResp: detailRespVOS){
            materialIdList.add(deatilResp.getMaterialId());
        }

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.MAIN_UNIT.getDictCode());;

        Iterator<ErpSaleOrderDetailRespVO> iterator = detailRespVOS.iterator();
        while (iterator.hasNext()){
            ErpSaleOrderDetailRespVO deatilResp = iterator.next();
            //填充物料信息
            ERPMaterialRespVO erpMaterial = erpMaterialDOMap.get(deatilResp.getMaterialId());
            if (erpMaterial!=null){
                deatilResp.setMaterialCode(erpMaterial.getMaterialCode());
                deatilResp.setMaterialName(erpMaterial.getMaterialName());
                deatilResp.setMaterialCategoryDictId(erpMaterial.getMaterialCategoryDictId());
                deatilResp.setMaterialCategoryDictName(erpMaterial.getMaterialCategoryDictName());
                deatilResp.setSpecModel(erpMaterial.getSpecModel());
                deatilResp.setSpecAttributeStr(erpMaterial.getSpecAttributeStr());
            }
            //基本单位
            if (deatilResp.getMainUnitDictId()!=null){
                deatilResp.setMainUnitDictName(dictMap.get(deatilResp.getMainUnitDictId()));
            }

            BigDecimal subtractQty = BigDecimal.ONE;
            BigDecimal quoteMaterialQty = quoteMaterialQtyMap.get(deatilResp.getMaterialId());
            quoteMaterialQty = quoteMaterialQty == null ? BigDecimal.ZERO : quoteMaterialQty;

            boolean check = true;
            if (codeEnum.equals(SHIP_NOTICE_CODE)){
                //可通知数量=对应物料的销售数量-已通知数量
                subtractQty = deatilResp.getQty().subtract(quoteMaterialQty);
                deatilResp.setNoticeQty(quoteMaterialQty);
                deatilResp.setCanNoticeQty(subtractQty);
                check = false;
            }else if (codeEnum.equals(SALES_RETURN_CODE)){
                //可退货数量=对应物料的已出库数量-已退货数量-已扣费数量-已换货数量+已换货入库数量
                subtractQty = deatilResp.getOutQty().subtract(quoteMaterialQty).add(deatilResp.getExchangeInboundQty());
                deatilResp.setReturnQty(quoteMaterialQty.subtract(deatilResp.getExchangeInboundQty()));
                deatilResp.setCanReturnQty(subtractQty);
            }else if (codeEnum.equals(DEDUCTION_ORDER_CODE)){
                //可扣
                // 费数量=对应物料的已出库数量-已退货数量-已扣费数量-已换货数量+已换货入库数量
                subtractQty = deatilResp.getOutQty().subtract(quoteMaterialQty).add(deatilResp.getExchangeInboundQty());
                deatilResp.setDeductionQty(quoteMaterialQty.subtract(deatilResp.getExchangeInboundQty()));
                deatilResp.setCanDeductionQty(subtractQty);
            }else if (codeEnum.equals(EXCHANGE_ORDER_CODE)){
                //可换货数量=对应物料的已出库数量-已退货数量-已扣费数量-已换货数量+已换货入库数量
                subtractQty = deatilResp.getOutQty().subtract(quoteMaterialQty).add(deatilResp.getExchangeInboundQty());
                deatilResp.setExchangeQty(quoteMaterialQty.subtract(deatilResp.getExchangeInboundQty()));
                deatilResp.setCanExchangeQty(subtractQty);
            }
            //移除可操作数量小于等于0的物料
            if (check && subtractQty.compareTo(BigDecimal.ZERO) <=0 ){
                iterator.remove();
            }
        }

        return detailRespVOS;
    }

    /**
     * 更新出库数量(出库审核更新)
     *
     * @param saleOrderDetailId 销售订单明细ID
     * @param outboundQty 入库数量
     */
    public void updateOutboundedQty(Long saleOrderDetailId, BigDecimal outboundQty){
        ErpSaleOrderDetailDO erpSaleOrderDetailDO = erpSaleOrderDetailMapper.selectById(saleOrderDetailId);
        //已出库数量
        erpSaleOrderDetailDO.setOutQty(erpSaleOrderDetailDO.getOutQty().add(outboundQty));
        //可出库数量
//        BigDecimal outboundableQty = erpSaleOrderDetailDO.getQty().subtract(erpSaleOrderDetailDO.getOutQty());
        // 是否全部已出库, 可出库数量为0,则为全部完成
//        if(outboundableQty.compareTo(BigDecimal.ZERO) <= 0){
//            erpSaleOrderDetailDO.setIsMaterialFullOutbounded(1);
//        }else{
//            erpSaleOrderDetailDO.setIsMaterialFullOutbounded(0);
//        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        erpSaleOrderDetailDO.setUpdatedBy(loginUser.getFullUserName());
        erpSaleOrderDetailDO.setUpdatedDt(LocalDateTime.now());
        erpSaleOrderDetailMapper.updateById(erpSaleOrderDetailDO);

        //更新销售单
        ErpSaleOrderDO erpSaleOrderDO = new ErpSaleOrderDO();
        erpSaleOrderDO.setSaleOrderId(erpSaleOrderDetailDO.getSaleOrderId());
        //todo 可无限出库,无限制
//        Long unFullOperedCount = erpSaleOrderDetailMapper.selectUnFullOutbounded(erpSaleOrderDetailDO.getSaleOrderId());
//        if(unFullOperedCount == 0){
//            erpSaleOrderDO.setIsCanIssueOut((short) 1);
//        }else{
//            erpSaleOrderDO.setIsCanIssueOut((short) 0);
//        }
        erpSaleOrderDO.setUpdatedBy(loginUser.getFullUserName());
        erpSaleOrderDO.setUpdatedDt(LocalDateTime.now());

        //可完成数量(销售退货和采购扣费用) = 已出库数量-已退货数量-已扣费数量-已换货数量+已换货入库数量
        BigDecimal completeableQty = erpSaleOrderDetailDO.getOutQty().subtract(erpSaleOrderDetailDO.getReturnedQty())
                .subtract(erpSaleOrderDetailDO.getFeedQty()).subtract(erpSaleOrderDetailDO.getExchangeQty().add(erpSaleOrderDetailDO.getExchangeInboundQty()));
        // 是否全部已完成, 可完成数量为0,则为全部完成
        if(completeableQty.compareTo(BigDecimal.ZERO) == 0){
            erpSaleOrderDO.setIsCanIssueReturn((short) 1);
            erpSaleOrderDO.setIsCanIssueFee((short) 1);
            erpSaleOrderDO.setIsCanIssueExchange((short) 1);
        }else{
            erpSaleOrderDO.setIsCanIssueReturn((short) 0);
            erpSaleOrderDO.setIsCanIssueFee((short) 0);
            erpSaleOrderDO.setIsCanIssueExchange((short) 0);
        }

//        //是否全部出库
//        ErpSaleOrderStatRespVO statRespVO = erpSaleOrderDetailMapper.getSaleOrderStat(erpSaleOrderDetailDO.getSaleOrderId());
//        BigDecimal totalSaleQty = statRespVO.getTotalSaleQty();
//        BigDecimal totalOutboundedQty = statRespVO.getTotalOutboundedQty();
//        //入库状态  ["未出库", "部分出库", "全部出库"]
//        if(totalOutboundedQty.compareTo(BigDecimal.ZERO) == 0){
//            erpSaleOrderDO.setOutboundStatus(0);
//        }else if(totalOutboundedQty.compareTo(totalSaleQty) < 0){
//            erpSaleOrderDO.setOutboundStatus(1);
//        }
        erpSaleOrderMapper.updateById(erpSaleOrderDO);
    }

    public void updateShippedStatus(Long saleOrderId) {
        //判断销售订单每条明细的出库数量是否都大于销售数量
        ErpSaleOrderDetailQueryReqVO saleOrderDetailReqVO = new ErpSaleOrderDetailQueryReqVO();
        saleOrderDetailReqVO.setSaleOrderId(saleOrderId);
        List<ErpSaleOrderDetailDO> saleOrderDetailList = erpSaleOrderDetailMapper.selectList(saleOrderDetailReqVO);
        ErpSaleOrderDO erpSaleOrderDO = new ErpSaleOrderDO();
        erpSaleOrderDO.setSaleOrderId(saleOrderId);
        boolean allOutbound = saleOrderDetailList.stream()
                .allMatch(detail -> detail.getOutQty().compareTo(detail.getQty()) >= 0);
        boolean noOutbound = saleOrderDetailList.stream()
                .allMatch(detail -> detail.getOutQty().compareTo(BigDecimal.ZERO) == 0);
        if (allOutbound) {
            erpSaleOrderDO.setOutboundStatus(2);
        }
        if (noOutbound){
            erpSaleOrderDO.setOutboundStatus(0);
        }
        if (!noOutbound && !allOutbound){
            erpSaleOrderDO.setOutboundStatus(1);
        }
        erpSaleOrderMapper.updateById(erpSaleOrderDO);
    }

    @Override
    public void writeBackSaleOrder(ErpSaleOrderBO erpSaleOrderBO) {

        if (StrUtilX.isNotEmpty(erpSaleOrderBO.getSaleOrderCode())){
            ErpSaleOrderQueryReqVO erpSaleOrderQuery = new ErpSaleOrderQueryReqVO();
            erpSaleOrderQuery.setSaleOrderCode(erpSaleOrderBO.getSaleOrderCode());
            ErpSaleOrderDO erpSaleOrderDO = erpSaleOrderMapper.selectOne(erpSaleOrderQuery);
            if (erpSaleOrderDO != null){
                erpSaleOrderBO.setSaleOrderId(erpSaleOrderDO.getSaleOrderId());
            }
        }
        //查询生产订单明细
        ErpSaleOrderDetailDO saleOrderDetailDO = erpSaleOrderDetailMapper.selectOne(ErpSaleOrderDetailDO::getSaleOrderId,erpSaleOrderBO.getSaleOrderId(),ErpSaleOrderDetailDO::getMaterialId,erpSaleOrderBO.getMaterialId());

        if (saleOrderDetailDO == null){
            log.error("产品明细已删除!");
            throw new BizException("500","产品明细已删除!");
        }
        BigDecimal reconciledQty = saleOrderDetailDO.getReconciledQty();
        BigDecimal planedQty = saleOrderDetailDO.getPlanedQty();
        BigDecimal invoicedQty = saleOrderDetailDO.getInvoicedQty();

        reconciledQty = reconciledQty.add(erpSaleOrderBO.getReconciledQty());
        planedQty = planedQty.add(erpSaleOrderBO.getPlanedQty());
        invoicedQty = invoicedQty.add(erpSaleOrderBO.getInvoicedQty());

        ErpSaleOrderDetailDO erpSaleOrderDetailDO = new ErpSaleOrderDetailDO();
        erpSaleOrderDetailDO.setSaleOrderDetailId(saleOrderDetailDO.getSaleOrderDetailId());
        erpSaleOrderDetailDO.setReconciledQty(reconciledQty);
        erpSaleOrderDetailDO.setPlanedQty(planedQty);
        erpSaleOrderDetailDO.setInvoicedQty(invoicedQty);
        erpSaleOrderDetailMapper.updateById(erpSaleOrderDetailDO);
    }

    @Override
    public List<ErpSaleOrderDetailRespVO> erpSalePurQuoteList(ErpSaleOrderDetailQueryReqVO reqVO) {

        //查询销售订单产品明细信息
        List<ErpSaleOrderDetailRespVO> erpSaleOrderDetailDOList = this.erpSaleOrderDetailList(reqVO);
        if (CollUtilX.isEmpty(erpSaleOrderDetailDOList)){
            return List.of();
        }

        List<ErpSaleOrderDetailRespVO> respVOS = new ArrayList<>();
        Map<Long,ErpSaleOrderDetailRespVO> detailRespMap = new HashMap<>();
        List<Long> materialRootIds = new ArrayList<>();
        List<Long> materialOutIds = new ArrayList<>();
        List<Long> exclMaterialIdList = reqVO.getExclMaterialIdList() == null ? new ArrayList<>() : reqVO.getExclMaterialIdList();

        for (ErpSaleOrderDetailRespVO item : erpSaleOrderDetailDOList){
            if (item.getMaterialSourceDictId() == MaterialEnum.OUTSOURCE.key &&
                    !exclMaterialIdList.contains(item.getMaterialId())){//外购
                respVOS.add(item);
                detailRespMap.put(item.getMaterialId(), item);
                materialOutIds.add(item.getMaterialId());
            }else {
                materialRootIds.add(item.getMaterialId());
            }
        }

        if (CollUtilX.isEmpty(materialRootIds)){
            return respVOS;
        }

        //查询物料bom
        MaterialBomQueryReqVO materialBomQuery = new MaterialBomQueryReqVO();
        materialBomQuery.setMaterialIdList(materialRootIds);
        List<MaterialBomTreeBO> materialBomList = materialBomService.findAllMaterialBomList(materialBomQuery);

        if (CollUtilX.isEmpty(materialBomList)){
            return respVOS;
        }

        List<Long> materialIdList = materialBomList.stream().map(MaterialBomTreeBO::getMaterialId).collect(Collectors.toList());

        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        for (Map.Entry<Long, ERPMaterialRespVO> entry : erpMaterialMap.entrySet()){
            Long materialId = entry.getKey();
            ERPMaterialRespVO material = entry.getValue();

            if (material == null) {
                continue;
            }

            Integer materialSourceDictId = material.getMaterialSourceDictId();
            if (materialSourceDictId !=null && materialSourceDictId.equals(MaterialEnum.OUTSOURCE.key)
                    && !materialOutIds.contains(materialId)
                    && !exclMaterialIdList.contains(materialId)){//外购
                ErpSaleOrderDetailRespVO detailResp = BeanUtilX.copy(material, ErpSaleOrderDetailRespVO::new);
                respVOS.add(detailResp);
            }
        }

        //查询已采购数量
        //先获取所有物料ID
        List<Long> respMaterialIdList = respVOS.stream().map(ErpSaleOrderDetailRespVO::getMaterialId).collect(Collectors.toList());
        //查询物料已采购数量
        List<PurchaseOrderDetailRespVO> purchaseMaterialList = purchaseOrderDetailMapper.queryMaterialPurchaseQty(reqVO.getSaleOrderId(), respMaterialIdList);
        Map<Long, BigDecimal> purchaseMaterialQtyMap = purchaseMaterialList.stream().collect(Collectors.toMap(PurchaseOrderDetailRespVO::getMaterialId,
                PurchaseOrderDetailRespVO::getPurchaseQty));
        //填充已采购数量
        for (ErpSaleOrderDetailRespVO item : respVOS){
            BigDecimal purchaseQty = purchaseMaterialQtyMap.get(item.getMaterialId());
            if (purchaseQty == null) {
                purchaseQty = BigDecimal.ZERO;
            }
            item.setPurchasedQty(purchaseQty);
        }

        return respVOS;
    }

}
