package com.mongoso.mgs.module.salary.controller.admin.salaryaggreobj;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreobj.vo.*;
import com.mongoso.mgs.module.salary.dal.db.salaryaggreobj.SalaryAggreObjDO;
import com.mongoso.mgs.module.salary.service.salaryaggreobj.SalaryAggreObjService;

/**
 * 工资单归集承担对象 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/salary")
@Validated
public class SalaryAggreObjController {

    @Resource
    private SalaryAggreObjService aggreObjService;

    @OperateLog("工资单归集承担对象添加或编辑")
    @PostMapping("/salaryAggreObjAdit")
    @PreAuthorize("@ss.hasPermission('salaryAggreObj:adit')")
    public ResultX<Long> salaryAggreObjAdit(@Valid @RequestBody SalaryAggreObjAditReqVO reqVO) {
        return success(reqVO.getAggreObjId() == null
                            ? aggreObjService.salaryAggreObjAdd(reqVO)
                            : aggreObjService.salaryAggreObjEdit(reqVO));
    }

    @OperateLog("工资单归集承担对象删除")
    @PostMapping("/salaryAggreObjDelete")
    @PreAuthorize("@ss.hasPermission('salaryAggreObj:delete')")
    public ResultX<Boolean> salaryAggreObjDelete(@Valid @RequestBody SalaryAggreObjPrimaryReqVO reqVO) {
        aggreObjService.salaryAggreObjDelete(reqVO.getAggreObjId());
        return success(true);
    }

    @OperateLog("工资单归集承担对象详情")
    @PostMapping("/salaryAggreObjDetail")
    @PreAuthorize("@ss.hasPermission('salaryAggreObj:query')")
    public ResultX<SalaryAggreObjRespVO> salaryAggreObjDetail(@Valid @RequestBody SalaryAggreObjPrimaryReqVO reqVO) {
        return success(aggreObjService.salaryAggreObjDetail(reqVO.getAggreObjId()));
    }

    @OperateLog("工资单归集承担对象列表")
    @PostMapping("/salaryAggreObjList")
    @PreAuthorize("@ss.hasPermission('salaryAggreObj:query')")
    public ResultX<List<SalaryAggreObjRespVO>> salaryAggreObjList(@Valid @RequestBody SalaryAggreObjQueryReqVO reqVO) {
        return success(aggreObjService.salaryAggreObjList(reqVO));
    }

    @OperateLog("工资单归集承担对象分页")
    @PostMapping("/salaryAggreObjPage")
    @PreAuthorize("@ss.hasPermission('salaryAggreObj:query')")
    public ResultX<PageResult<SalaryAggreObjRespVO>> salaryAggreObjPage(@Valid @RequestBody SalaryAggreObjPageReqVO reqVO) {
        return success(aggreObjService.salaryAggreObjPage(reqVO));
    }

}
