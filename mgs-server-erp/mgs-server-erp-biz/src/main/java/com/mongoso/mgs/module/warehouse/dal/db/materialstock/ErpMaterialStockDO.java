package com.mongoso.mgs.module.warehouse.dal.db.materialstock;

import lombok.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 库存 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_material_stock", autoResultMap = true)
//@KeySequence("erp.u_material_stock_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpMaterialStockDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long materialStockId;

    /** 物料ID */
    private Long materialId;

    /** 仓库ID */
    private String warehouseOrgId;

    /** 库存数量 */
    private BigDecimal stockQty;

    /** 已锁定数量 */
    private BigDecimal lockedQty;

    /** 已预订数量 */
    private BigDecimal bookedQty;

    /** 可用数量 */
    private BigDecimal availableQty;


}
