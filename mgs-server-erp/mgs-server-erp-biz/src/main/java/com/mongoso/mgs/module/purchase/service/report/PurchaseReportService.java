package com.mongoso.mgs.module.purchase.service.report;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseFlowApprove;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.PurchaseFlowCallback;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.*;
import com.mongoso.mgs.module.purchase.controller.admin.report.vo.MaterialPurchaseReportPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.report.vo.MaterialPurchaseReportRespVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购报表 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseReportService {

    /**
     * 查询物料采购统计报表标题
     * @param reqVO 请求参数
     * @return 标题数据
     */
    List<String> getTitleList(MaterialPurchaseReportPageReqVO reqVO);

    /**
     * 查询物料采购统计报表
     * @param reqVO 请求参数
     * @return 统计数据
     */
    PageResult<MaterialPurchaseReportRespVO> queryMaterialPurchaseReport(MaterialPurchaseReportPageReqVO reqVO);
}
