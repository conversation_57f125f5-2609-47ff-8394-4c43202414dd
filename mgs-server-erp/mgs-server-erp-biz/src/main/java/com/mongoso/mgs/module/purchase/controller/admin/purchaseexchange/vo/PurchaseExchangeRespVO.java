package com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo;

import com.mongoso.mgs.module.purchase.controller.admin.purchaseexchange.vo.detail.PurchaseExchangeDetailRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 
 


/**
 * 采购换货 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseExchangeRespVO extends PurchaseExchangeBaseVO {

    /** 审核状态 */
    private String dataStatusDictName;

    /** 责任人 */
    private String directorName;

    /** 责任部门(组织名称) */
    private String directorOrgName;

    /** 采购订单类型字典Name */
    private String purchaseTypeDictName;

    /** 关联供应商 */
    private String relatedSupplierName;

    /** 明细列表 */
    private List<PurchaseExchangeDetailRespVO> detailList;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 审批任务id */
    private Long approveTaskId;
}
