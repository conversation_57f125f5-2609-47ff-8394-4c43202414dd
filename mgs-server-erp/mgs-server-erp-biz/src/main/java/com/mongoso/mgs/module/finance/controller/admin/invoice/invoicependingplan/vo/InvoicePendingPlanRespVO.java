package com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.vo;

import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingdetail.vo.InvoicePendingDetailBaseVO;
import lombok.*;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import java.util.List;


/**
 * 待开票计划 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoicePendingPlanRespVO extends InvoicePendingPlanBaseVO {

    /** 开票计划状态 */
    private String invoicePlanStatusDictName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    private String customerName;
    private String directorName;
    /** 责任部门 */
    private String directorOrgName;
    //币种名称
    private String currencyDictName;

    List<InvoicePendingDetailBaseVO> detailList;

}
