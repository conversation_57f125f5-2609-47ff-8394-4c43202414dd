package com.mongoso.mgs.module.finance.service.invoice.invoicedetaillist;

import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicedetaillist.vo.InvoiceDetailListRespVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetaillist.InvoiceDetailListDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicedetaillist.InvoiceDetailListMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.exception.BizException;


/**
 * 实开发票明细集合 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvoiceDetailListServiceImpl implements InvoiceDetailListService {

    @Resource
    private InvoiceDetailListMapper detailListMapper;

    @Override
    public Long invoiceDetailListAdd(InvoiceDetailListAditReqVO reqVO) {
        // 插入
        InvoiceDetailListDO detailList = BeanUtilX.copy(reqVO, InvoiceDetailListDO::new);
        detailListMapper.insert(detailList);
        // 返回
        return detailList.getInvoiceDetailListId();
    }

    @Override
    public Long invoiceDetailListEdit(InvoiceDetailListAditReqVO reqVO) {
        // 校验存在
        this.invoiceDetailListValidateExists(reqVO.getInvoiceDetailListId());
        // 更新
        InvoiceDetailListDO detailList = BeanUtilX.copy(reqVO, InvoiceDetailListDO::new);
        detailListMapper.updateById(detailList);
        // 返回
        return detailList.getInvoiceDetailListId();
    }

    @Override
    public void invoiceDetailListDel(Long invoiceDetailListId) {
        // 校验存在
        this.invoiceDetailListValidateExists(invoiceDetailListId);
        // 删除
        detailListMapper.deleteById(invoiceDetailListId);
    }

    private InvoiceDetailListDO invoiceDetailListValidateExists(Long invoiceDetailListId) {
        InvoiceDetailListDO detailList = detailListMapper.selectById(invoiceDetailListId);
        if (detailList == null) {
            // throw exception(DETAIL_LIST_NOT_EXISTS);
            throw new BizException("5001", "实开发票明细集合不存在");
        }
        return detailList;
    }

    @Override
    public InvoiceDetailListRespVO invoiceDetailListDetail(Long invoiceDetailListId) {
        InvoiceDetailListDO data = detailListMapper.selectById(invoiceDetailListId);
        return BeanUtilX.copy(data, InvoiceDetailListRespVO::new);
    }

    @Override
    public List<InvoiceDetailListRespVO> invoiceDetailListList(InvoiceDetailListQueryReqVO reqVO) {
        List<InvoiceDetailListDO> data = detailListMapper.selectList(reqVO);
        return BeanUtilX.copy(data, InvoiceDetailListRespVO::new);
    }

    @Override
    public PageResult<InvoiceDetailListRespVO> invoiceDetailListPage(InvoiceDetailListPageReqVO reqVO) {
        PageResult<InvoiceDetailListDO> data = detailListMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, InvoiceDetailListRespVO::new);
    }

}
