package com.mongoso.mgs.module.base.controller.admin.spu.vo;

import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * SPU物料 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpuRespVO extends SpuBaseVO {

    /**
     * 物料明细
     */
    List<ERPMaterialRespVO> materialDetailList;

    /** 品牌 */
    private String brandDictName;

    /** 物料来源 */
    private String materialSourceDictName;

    /** 物料类型 */
    private String materialTypeDictName;

    /** 物料类别 */
    private String materialCategoryDictName;

    /** 上架状态 */
    private String publishStatusDictName;

    /** 责任人 */
    private String directorName;

    /** 是否可编辑基本单位 */
    private Integer isChangeMainUnit;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;


    /** 审批任务id */
    private Long approveTaskId;

}
