package com.mongoso.mgs.module.base.controller.admin.hzproduct.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 产品 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class HzProductQueryReqVO {

    /** 产品编码 */
    private String productCode;

    /** 产品名称 */
    private String productName;

    /** 是否bom */
    private Short isBom;

    /** 物料类型 */
    private String dictMaterialTypeId;


    /** 主单位 */
    private String mainUnit;

    /** 辅单位 leftVal:1, leftUnit:"盒", rightVal:5, rightUnit:"瓶" */
    private String auxUnit;

    /** 产品描述 */
    private String productDesc;

    /** 规格设置 */
    private String specSetting;

    /** 工艺流程编码 */
    private String flowCode;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 产品代号 精确查询*/
    private String productCodeEq;

}
