package com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciateconfig.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 折旧方法配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class AssetDepreciateConfigBaseVO implements Serializable {

    /** 主键ID */
    private Long depreciateConfigId;

    /** 方法名称 */
    @NotEmpty(message = "方法名称不能为空")
    private String methodName;

    /** 公式 */
    @NotEmpty(message = "公式不能为空")
    private String formula;

    /** 责任人 */
    private Long directorId;

    private String directorName;

    /** 责任部门 */
    private String directorOrgId;

    private String directorOrgName;

    /** 是否系统自动生成 */
    private Integer isSystemGenerate;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 单据状态 */
    private Short dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;


}
