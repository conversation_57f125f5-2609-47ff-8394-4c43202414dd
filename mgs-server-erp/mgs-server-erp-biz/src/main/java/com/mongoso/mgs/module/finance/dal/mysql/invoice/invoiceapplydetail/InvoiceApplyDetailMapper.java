package com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapplydetail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapplydetail.vo.InvoiceApplyDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapplydetail.vo.InvoiceApplyDetailQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapplydetail.InvoiceApplyDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 开票申请明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoiceApplyDetailMapper extends BaseMapperX<InvoiceApplyDetailDO> {

    default PageResult<InvoiceApplyDetailDO> selectPageOld(InvoiceApplyDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<InvoiceApplyDetailDO>lambdaQueryX()
                .eqIfPresent(InvoiceApplyDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoiceApplyId, reqVO.getInvoiceApplyId())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoicePlanDetailId, reqVO.getInvoicePlanDetailId())
                .likeIfPresent(InvoiceApplyDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(InvoiceApplyDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoiceApplyDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyQty, reqVO.getRemainingApplyQty())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyAmt, reqVO.getRemainingApplyAmt())
                .eqIfPresent(InvoiceApplyDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(InvoiceApplyDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmtInclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtExclTax, reqVO.getPlanInvoiceAmtExclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoiceApplyDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(InvoiceApplyDetailDO::getCreatedDt));
    }



    default PageResult<InvoiceApplyDetailDO> selectPage(InvoiceApplyDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<InvoiceApplyDetailDO>lambdaQueryX()
                .eqIfPresent(InvoiceApplyDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoiceApplyId, reqVO.getInvoiceApplyId())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoicePlanDetailId, reqVO.getInvoicePlanDetailId())
                .likeIfPresent(InvoiceApplyDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(InvoiceApplyDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoiceApplyDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyQty, reqVO.getRemainingApplyQty())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyAmt, reqVO.getRemainingApplyAmt())
                .eqIfPresent(InvoiceApplyDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(InvoiceApplyDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmtInclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtExclTax, reqVO.getPlanInvoiceAmtExclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoiceApplyDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(InvoiceApplyDetailDO::getCreatedDt));
    }

    default List<InvoiceApplyDetailDO> selectListOld(InvoiceApplyDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<InvoiceApplyDetailDO>lambdaQueryX()
                .eqIfPresent(InvoiceApplyDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoiceApplyId, reqVO.getInvoiceApplyId())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoicePlanDetailId, reqVO.getInvoicePlanDetailId())
                .likeIfPresent(InvoiceApplyDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(InvoiceApplyDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoiceApplyDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyQty, reqVO.getRemainingApplyQty())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyAmt, reqVO.getRemainingApplyAmt())
                .eqIfPresent(InvoiceApplyDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(InvoiceApplyDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmtInclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtExclTax, reqVO.getPlanInvoiceAmtExclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoiceApplyDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(InvoiceApplyDetailDO::getCreatedDt));
    }

    default List<InvoiceApplyDetailDO> selectList(InvoiceApplyDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<InvoiceApplyDetailDO>lambdaQueryX()
                .eqIfPresent(InvoiceApplyDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoiceApplyId, reqVO.getInvoiceApplyId())
                .eqIfPresent(InvoiceApplyDetailDO::getInvoicePlanDetailId, reqVO.getInvoicePlanDetailId())
                .likeIfPresent(InvoiceApplyDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(InvoiceApplyDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoiceApplyDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyQty, reqVO.getRemainingApplyQty())
                .eqIfPresent(InvoiceApplyDetailDO::getRemainingApplyAmt, reqVO.getRemainingApplyAmt())
                .eqIfPresent(InvoiceApplyDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .eqIfPresent(InvoiceApplyDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmtInclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getPlanInvoiceAmtExclTax, reqVO.getPlanInvoiceAmtExclTax())
                .eqIfPresent(InvoiceApplyDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoiceApplyDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoiceApplyDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(InvoiceApplyDetailDO::getCreatedDt));
    }

    BigDecimal invoiceApplyQty(@Param("invoiceApplyId") Long invoiceApplyId);

    void deleteByinvoicePlanIds(@Param("invoicePlanIds") List<Long> invoicePlanIds);
}