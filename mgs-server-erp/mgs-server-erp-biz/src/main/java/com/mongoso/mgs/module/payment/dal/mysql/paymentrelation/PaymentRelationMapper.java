package com.mongoso.mgs.module.payment.dal.mysql.paymentrelation;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.PaymentRelationPageReqVO;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.PaymentRelationQueryReqVO;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 收款关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PaymentRelationMapper extends BaseMapperX<PaymentRelationDO> {

    default PageResult<PaymentRelationDO> selectPage(PaymentRelationPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .eqIfPresent(PaymentRelationDO::getSort, reqVO.getSort())
                .eqIfPresent(PaymentRelationDO::getFormType, reqVO.getFormType())
                .eqIfPresent(PaymentRelationDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(PaymentRelationDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(PaymentRelationDO::getAmt, reqVO.getAmt())
                .eqIfPresent(PaymentRelationDO::getCreatedId, reqVO.getCreatedId())
                .betweenIfPresent(PaymentRelationDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(PaymentRelationDO::getId));
    }




    default List<PaymentRelationDO> selectList(PaymentRelationQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .eqIfPresent(PaymentRelationDO::getSort, reqVO.getSort())
                .eqIfPresent(PaymentRelationDO::getFormType, reqVO.getFormType())
                .eqIfPresent(PaymentRelationDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(PaymentRelationDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(PaymentRelationDO::getAmt, reqVO.getAmt())
                .eqIfPresent(PaymentRelationDO::getCreatedId, reqVO.getCreatedId())
                .eqIfPresent(PaymentRelationDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(PaymentRelationDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(PaymentRelationDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(PaymentRelationDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                .orderByDesc(PaymentRelationDO::getId));
    }


    default int deleteByOrderId(Long orderId){
        return delete(LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .eq(PaymentRelationDO::getOrderId, orderId)
        );
    }


    default int deleteByOrderIds(List<Long> orderIds){
        return delete(LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .in(PaymentRelationDO::getOrderId, orderIds)
        );
    }

    default List<PaymentRelationDO> selectListByOrderId(Long orderId, Integer relatedDetailType){
        return selectList(LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .eq(PaymentRelationDO::getOrderId, orderId)
                .eq(PaymentRelationDO::getRelatedDetailType, relatedDetailType)
                .orderByAsc(PaymentRelationDO::getSort)
        );
    }

    default List<PaymentRelationDO> selectListByRelatedOrderId(Long relatedOrderId){
        return selectList(LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .eq(PaymentRelationDO::getRelatedOrderId, relatedOrderId)
        );
    }

    default Long selectCountByRelatedOrderId(Long relatedOrderId){
        return selectCount(LambdaQueryWrapperX.<PaymentRelationDO>lambdaQueryX()
                .eq(PaymentRelationDO::getRelatedOrderId, relatedOrderId)
        );
    }
}