package com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplan.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 开票计划 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class InvoicePlanBaseVO implements Serializable {

    /** 开票计划id */
    private Long invoicePlanId;

    /** 开票方向 */
    private Short billingDirection;

    /** 开票申请状态 */
    private Short invoiceApplyStatus;

    /** 待开票计划id */
    @NotNull(message = "待开票计划不能为空")
    private Long invoicePendingPlanId;

    /** 待开票计划明细id */
    private Long invoicePendingPlanDetailId;

    /** 票据类型 */
    private Long invoiceTypeDictId;
    private String invoiceTypeDictName;

    /** 开票计划单号 */
    private String invoicePlanNo;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 币种id */
    private String currencyDictId;

    /** 客户id */
    private Long customerId;

    /** 计划开票总额（含税） */
    private BigDecimal planInvoiceTotalInclTax;

    /** 计划开票日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planInvoiceDate;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    private BigDecimal planInvoiceTotalExclTax;

    private BigDecimal totalTax;

    private BigDecimal readyQty;

    private BigDecimal readyAmt;

    private BigDecimal canQty;

    private BigDecimal canAmt;

    //开票策略
    private String invoiceStrategy;

    /** 跳过开票申请 */
    private Integer skipInvoiceApply;

    /** 已开票数量 */
    private BigDecimal readyInvoiceQty;

    /** 已开票金额 */
    private BigDecimal readyInvoiceAmt;

    /** 可开票数量 */
    private BigDecimal canInvoiceQty;

    /** 可开票金额 */
    private BigDecimal canInvoiceAmt;

    /** 源头单据id */
    private Long originOrderId;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币计划开票总额（含税） */
    private BigDecimal localCurrencyPlanInvoiceTotalInclTax;

    /** 本币已开票金额 */
    private BigDecimal localCurrencyReadyInvoiceAmt;

    /** 本币可开票金额 */
    private BigDecimal localCurrencyCanInvoiceAmt;
}
