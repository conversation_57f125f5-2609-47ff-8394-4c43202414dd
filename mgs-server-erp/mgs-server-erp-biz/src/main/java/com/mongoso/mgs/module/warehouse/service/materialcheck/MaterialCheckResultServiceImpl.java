package com.mongoso.mgs.module.warehouse.service.materialcheck;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseRespVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckResultAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckResultPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckResultQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckResultRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckStatisticsReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckStatisticsRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.NgReasonStatistics;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheckngdetail.vo.MaterialCheckNgDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheckngdetail.vo.MaterialCheckNgDetailRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.materialcheck.MaterialCheckResultDO;
import com.mongoso.mgs.module.warehouse.dal.db.materialcheckngdetail.MaterialCheckNgDetailDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialcheck.MaterialCheckResultMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialcheckngdetail.MaterialCheckNgDetailMapper;
import com.mongoso.mgs.module.warehouse.handler.approve.MaterialCheckResultApproveHandler;
import com.mongoso.mgs.module.warehouse.handler.flowCallback.MaterialCheckResultFlowCallBackHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.warehouse.enums.ErrorCodeConstants.*;


/**
 * 检验结果录入单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MaterialCheckResultServiceImpl implements MaterialCheckResultService {

    @Resource
    private MaterialCheckResultMapper materialCheckResultMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    private MaterialCheckResultApproveHandler materialCheckResultApproveHandler;

    @Resource
    private MaterialCheckResultFlowCallBackHandler materialCheckResultFlowCallBackHandler;

    @Resource
    private MaterialCheckNgDetailMapper materialCheckNgDetailMapper;

    @Override
    public Long materialCheckResultAdd(MaterialCheckResultAditReqVO reqVO) {

        // 生成检验结果录入单
        reqVO.setCheckResultCode(seqService.getGenerateCode(reqVO.getCheckResultCode(), MenuEnum.INSPECTION_RESULT_ENTRY_ORDER.menuId));

        // 插入
        MaterialCheckResultDO materialCheckResult = BeanUtilX.copy(reqVO, MaterialCheckResultDO::new);
        materialCheckResultMapper.insert(materialCheckResult);

        //插入检验单不良品明细
        List<MaterialCheckNgDetailRespVO> ngDetailList = reqVO.getNgDetailList();
        if(CollUtilX.isNotEmpty(ngDetailList)){
            for(MaterialCheckNgDetailRespVO item : ngDetailList){
                MaterialCheckNgDetailDO detailDO = BeanUtilX.copy(item, MaterialCheckNgDetailDO::new);
                detailDO.setRelatedOrderId(materialCheckResult.getCheckResultId());
                detailDO.setRelatedOrderCode(materialCheckResult.getCheckResultCode());
                materialCheckNgDetailMapper.insert(detailDO);
            }
        }

        // 返回
        return materialCheckResult.getCheckResultId();
    }

    @Override
    public Long materialCheckResultEdit(MaterialCheckResultAditReqVO reqVO) {
        // 校验存在
//        this.materialCheckResultValidateExists(reqVO.getCheckResultId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.materialCheckResultValidateExists(reqVO.getCheckResultId()), reqVO);

        // 更新
        MaterialCheckResultDO materialCheckResult = BeanUtilX.copy(reqVO, MaterialCheckResultDO::new);
        materialCheckResultMapper.updateById(materialCheckResult);

        //删除检验单不良品明细
        materialCheckNgDetailMapper.batchDeleteByCheckId(reqVO.getCheckResultId());
        //插入检验单不良品明细
        List<MaterialCheckNgDetailRespVO> ngDetailList = reqVO.getNgDetailList();
        if(CollUtilX.isNotEmpty(ngDetailList)){
            for(MaterialCheckNgDetailRespVO item : ngDetailList){
                MaterialCheckNgDetailDO detailDO = BeanUtilX.copy(item, MaterialCheckNgDetailDO::new);
                detailDO.setRelatedOrderId(reqVO.getCheckResultId());
                detailDO.setRelatedOrderCode(materialCheckResult.getCheckResultCode());
                materialCheckNgDetailMapper.insert(detailDO);
            }
        }

        // 返回
        return materialCheckResult.getCheckResultId();
    }

    @Override
    public void materialCheckResultDel(Long checkResultId) {
        // 校验存在
        MaterialCheckResultDO materialCheckResultDO = this.materialCheckResultValidateExists(checkResultId);
        if(materialCheckResultDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException("5001", "单据状态不是未审核,不可删除！");
        }

        // 删除
        materialCheckResultMapper.deleteById(checkResultId);
    }

    @Override
    public ResultX<BatchResult> materialCheckResultDelBatch(IdsReqVO reqVO){
        String id = EntityUtilX.getPropertyName(MaterialCheckResultDO::getCheckResultId);
        String code = EntityUtilX.getPropertyName(MaterialCheckResultDO::getCheckResultCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), MaterialCheckResultDO.class, null, id, code);
    }

    private MaterialCheckResultDO materialCheckResultValidateExists(Long checkResultId) {
        MaterialCheckResultDO materialCheckResult = materialCheckResultMapper.selectById(checkResultId);
        if (materialCheckResult == null) {
            // throw exception(MATERIAL_CHECK_RESULT_NOT_EXISTS);
            throw new BizException("5001", "检验结果录入单不存在");
        }
        return materialCheckResult;
    }

    @Override
    public MaterialCheckResultRespVO materialCheckResultDetail(Long checkResultId) {
        //校验存在
        this.materialCheckResultValidateExists(checkResultId);
        //查询详情
        MaterialCheckResultRespVO respVO = materialCheckResultMapper.queryMaterialCheckResultDetail(checkResultId);

        MaterialCheckNgDetailQueryReqVO reqVO = new MaterialCheckNgDetailQueryReqVO();
        reqVO.setRelatedOrderId(checkResultId);
        List<MaterialCheckNgDetailRespVO> data = BeanUtilX.copy(materialCheckNgDetailMapper.selectList(reqVO), MaterialCheckNgDetailRespVO :: new);
        respVO.setNgDetailList(data);

        //属性填充
        fillVoProperties(respVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(checkResultId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<MaterialCheckResultRespVO> materialCheckResultList(MaterialCheckResultQueryReqVO reqVO) {
        List<MaterialCheckResultRespVO> respVOList = BeanUtilX.copyList(materialCheckResultMapper.selectList(reqVO), MaterialCheckResultRespVO::new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<MaterialCheckResultRespVO> materialCheckResultPage(MaterialCheckResultPageReqVO reqVO) {
        IPage<MaterialCheckResultRespVO> respVOIPage = materialCheckResultMapper.queryMaterialCheckResultPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<MaterialCheckResultRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(MaterialCheckResultRespVO respVO) {
        List<MaterialCheckResultRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<MaterialCheckResultRespVO> respVOList) {

        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        for(MaterialCheckResultRespVO respVO : respVOList){
            directorIdList.add(respVO.getDirectorId());
            directorOrgIdList.add(respVO.getDirectorOrgId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.CHECK_TYPE.getDictCode(), CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(),
                CustomerDictEnum.MAIN_UNIT.getDictCode(), SystemDictEnum.CHECK_RESULT.getDictCode(),
                CustomerDictEnum.CHECK_NG_REASON.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        // 属性填充
        for (MaterialCheckResultRespVO item : respVOList) {
            //责任人
            if(item.getDirectorId() != null){
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }

            // 检验单类型
            String checkTypeDictId = item.getCheckTypeDictId();
            if(StrUtilX.isNotEmpty(checkTypeDictId)){
                checkTypeDictId = CustomerDictEnum.CHECK_TYPE.getDictCode() + "-" + checkTypeDictId;
                item.setCheckTypeDictName(dictMap.get(checkTypeDictId));
            }

            // 检验结果
            Integer checkResult = item.getCheckResult();
            if(checkResult != null){
                String checkResultStr = SystemDictEnum.CHECK_RESULT.getDictCode() + "-" + checkResult;
                item.setCheckResultDictName(dictMap.get(checkResultStr));
            }

            // 物料类别
            String materialCategoryDictId = item.getMaterialCategoryDictId();
            if(StrUtilX.isNotEmpty(materialCategoryDictId)){
                item.setMaterialCategoryDictId(materialCategoryDictId);
                materialCategoryDictId = CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
                item.setMaterialCategoryDictName(dictMap.get(materialCategoryDictId));
            }

            // 基本单位
            String mainUnitDictId = item.getMainUnitDictId();
            if(StrUtilX.isNotEmpty(mainUnitDictId)) {
                item.setMainUnitDictId(item.getMainUnitDictId());
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                item.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            List<MaterialCheckNgDetailRespVO> ngDetailList = item.getNgDetailList();
            if(CollUtilX.isNotEmpty(ngDetailList)){
                for(MaterialCheckNgDetailRespVO sItem : ngDetailList){
                    Object ngReasonDictId = sItem.getNgReasonDictId();
                    if(ngReasonDictId != null){
                        String ngReasonDictIdStr =  CustomerDictEnum.CHECK_NG_REASON.getDictCode() + "-" + ngReasonDictId;
                        sItem.setNgReasonDictName(dictMap.get(ngReasonDictIdStr));
                    }
                }
            }
        }
    }

    @Override
    public BatchResult materialCheckResultApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<MaterialCheckResultDO> list = materialCheckResultMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (MaterialCheckResultDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = materialCheckResultApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getCheckResultCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (MaterialCheckResultDO item : list) {
                String reason = reasonMap.get(item.getCheckResultCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getCheckResultId());
                    messageInfoBO.setObjCode(item.getCheckResultCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getCheckResultId());
                    messageInfoBO.setObjCode(item.getCheckResultCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }


    @Override
    public Object materialCheckResultFlowCallback(FlowCallback reqVO){
        String objId = reqVO.getObjId();
        MaterialCheckResultDO currentDO = this.materialCheckResultValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();
        return materialCheckResultFlowCallBackHandler.handleFlowCallback(currentDO, flowCallbackBO);
    }

    @Override
    public PageResult<MaterialCheckStatisticsRespVO> materialCheckStatistics(MaterialCheckStatisticsReqVO reqVO) {
        try {
            log.info("开始执行物料检验统计查询，查询条件: {}", reqVO);

            //因为传递的日期是年月日，所有，结束日期需要加一天才能把结束日期的那天的数据查询出来
            reqVO.setEndFormDt(reqVO.getEndFormDt().plusDays(1));

            // 1. 调用Mapper查询统计数据（分页）
            IPage<MaterialCheckStatisticsRespVO> statisticsPage = materialCheckResultMapper.queryMaterialCheckStatisticsPage(
                PageUtilX.buildParam(reqVO), reqVO);

            PageResult<MaterialCheckStatisticsRespVO> pageResult = PageUtilX.buildResult(statisticsPage);

            if (CollUtilX.isEmpty(pageResult.getList())) {
                log.info("未查询到符合条件的检验统计数据");
                return pageResult;
            }

            // 2. 查询不良品原因统计并计算不良品率
            for (MaterialCheckStatisticsRespVO statistics : pageResult.getList()) {
                List<NgReasonStatistics> ngReasonList =
                    materialCheckResultMapper.queryNgReasonStatistics(
                        reqVO.getStartFormDt(),
                        reqVO.getEndFormDt(),
                        statistics.getMaterialCode());

                // 计算每个不良原因的不良品率 = 当前不良品原因数量 / 检验总数 × 100%
                if (CollUtilX.isNotEmpty(ngReasonList) && statistics.getTotalCheckQty().compareTo(BigDecimal.ZERO) > 0) {
                    for (NgReasonStatistics ngReason : ngReasonList) {
                        BigDecimal rate = ngReason.getNgQty()
                            .divide(statistics.getTotalCheckQty(), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                        ngReason.setNgRate(rate);
                        //去除末尾零
                        ngReason.setNgRate(MathUtilX.stripTrailingZeros(ngReason.getNgRate()));
                        ngReason.setNgQty(MathUtilX.stripTrailingZeros(ngReason.getNgQty()));
                    }
                }

                statistics.setNgReasonList(ngReasonList);
            }

            // 3. 填充字典名称
            fillStatisticsProperties(pageResult.getList());

            log.info("物料检验统计查询完成，返回 {} 条记录，总计 {} 条",
                    pageResult.getList().size(), pageResult.getTotalCount());
            return pageResult;

        } catch (Exception e) {
            log.error("物料检验统计查询异常: {}", e.getMessage(), e);
            throw new BizException("5001", "物料检验统计查询失败: " + e.getMessage());
        }
    }





    /**
     * 填充统计数据的字典属性
     */
    private void fillStatisticsProperties(List<MaterialCheckStatisticsRespVO> statisticsList) {
        if (CollUtilX.isEmpty(statisticsList)) {
            return;
        }

        // 查询字典库信息（物料类别字典和不良品原因字典）
        List<String> dictCodeList = Arrays.asList(
            CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(),
            CustomerDictEnum.CHECK_NG_REASON.getDictCode()
        );
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        // 填充字典名称
        for (MaterialCheckStatisticsRespVO statistics : statisticsList) {
            // 填充物料类别名称
            String materialCategoryDictId = statistics.getMaterialCategoryDictName(); // 这里实际存储的是ID
            if (StrUtilX.isNotEmpty(materialCategoryDictId)) {
                String categoryKey = CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
                statistics.setMaterialCategoryDictName(dictMap.get(categoryKey));
            }

            // 填充不良品原因名称
            List<NgReasonStatistics> ngReasonList = statistics.getNgReasonList();
            if (CollUtilX.isNotEmpty(ngReasonList)) {
                for (NgReasonStatistics ngReason : ngReasonList) {
                    String ngReasonDictId = ngReason.getNgReasonDictId();
                    if (StrUtilX.isNotEmpty(ngReasonDictId)) {
                        String reasonKey = CustomerDictEnum.CHECK_NG_REASON.getDictCode() + "-" + ngReasonDictId;
                        ngReason.setNgReasonDictName(dictMap.get(reasonKey));
                    }
                }
            }

            //去除数量和比例的末尾零
            statistics.setTotalCheckQty(MathUtilX.stripTrailingZeros(statistics.getTotalCheckQty()));
            statistics.setTotalOkQty(MathUtilX.stripTrailingZeros(statistics.getTotalOkQty()));
            statistics.setTotalNgQty(MathUtilX.stripTrailingZeros(statistics.getTotalNgQty()));
            statistics.setOkRate(MathUtilX.stripTrailingZeros(statistics.getOkRate()));
        }
    }

}
