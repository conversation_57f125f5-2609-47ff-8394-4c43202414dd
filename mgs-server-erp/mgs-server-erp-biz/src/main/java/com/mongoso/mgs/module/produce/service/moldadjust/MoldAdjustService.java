package com.mongoso.mgs.module.produce.service.moldadjust;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.produce.controller.admin.moldadjust.vo.*;
import com.mongoso.mgs.module.produce.dal.db.moldadjust.MoldAdjustDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 模具调整记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MoldAdjustService {

    /**
     * 创建模具调整记录
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long moldAdjustAdd(@Valid MoldAdjustAditReqVO reqVO);

    /**
     * 更新模具调整记录
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long moldAdjustEdit(@Valid MoldAdjustAditReqVO reqVO);

    /**
     * 删除模具调整记录
     *
     * @param moldAdjustId 编号
     */
    void moldAdjustDel(Long moldAdjustId);

    /**
     * 获得模具调整记录信息
     *
     * @param moldAdjustId 编号
     * @return 模具调整记录信息
     */
    MoldAdjustRespVO moldAdjustDetail(Long moldAdjustId);

    /**
     * 获得模具调整记录列表
     *
     * @param reqVO 查询条件
     * @return 模具调整记录列表
     */
    List<MoldAdjustRespVO> moldAdjustList(@Valid MoldAdjustQueryReqVO reqVO);

    /**
     * 获得模具调整记录分页
     *
     * @param reqVO 查询条件
     * @return 模具调整记录分页
     */
    PageResult<MoldAdjustRespVO> moldAdjustPage(@Valid MoldAdjustPageReqVO reqVO);

}
