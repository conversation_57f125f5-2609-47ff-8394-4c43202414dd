package com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo;

import com.mongoso.mgs.module.sale.controller.admin.salechangdetail.vo.SaleChangDetailRespVO;
import lombok.*;

import java.util.List;

/**
 * 提成任务 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CommissionTaskAditReqVO extends CommissionTaskBaseVO {

    private List<SaleChangDetailRespVO> detailList;
}
