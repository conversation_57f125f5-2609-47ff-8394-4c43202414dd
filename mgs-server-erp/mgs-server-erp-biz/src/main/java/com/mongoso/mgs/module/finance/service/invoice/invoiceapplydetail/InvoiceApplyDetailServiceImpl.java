package com.mongoso.mgs.module.finance.service.invoice.invoiceapplydetail;

import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapplydetail.vo.InvoiceApplyDetailAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapplydetail.vo.InvoiceApplyDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapplydetail.vo.InvoiceApplyDetailQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapplydetail.vo.InvoiceApplyDetailRespVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapplydetail.InvoiceApplyDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapplydetail.InvoiceApplyDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.invoice.enums.ErrorCodeConstants.*;


/**
 * 开票申请明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvoiceApplyDetailServiceImpl implements InvoiceApplyDetailService {

    @Resource
    private InvoiceApplyDetailMapper applyDetailMapper;

    @Override
    public Long invoiceApplyDetailAdd(InvoiceApplyDetailAditReqVO reqVO) {
        // 插入
        InvoiceApplyDetailDO applyDetail = BeanUtilX.copy(reqVO, InvoiceApplyDetailDO::new);
        applyDetailMapper.insert(applyDetail);
        // 返回
        return applyDetail.getInvoiceApplyDetailId();
    }

    @Override
    public Long invoiceApplyDetailEdit(InvoiceApplyDetailAditReqVO reqVO) {
        // 校验存在
        this.invoiceApplyDetailValidateExists(reqVO.getInvoiceApplyDetailId());
        // 更新
        InvoiceApplyDetailDO applyDetail = BeanUtilX.copy(reqVO, InvoiceApplyDetailDO::new);
        applyDetailMapper.updateById(applyDetail);
        // 返回
        return applyDetail.getInvoiceApplyDetailId();
    }

    @Override
    public void invoiceApplyDetailDel(Long invoiceApplyDetailId) {
        // 校验存在
        this.invoiceApplyDetailValidateExists(invoiceApplyDetailId);
        // 删除
        applyDetailMapper.deleteById(invoiceApplyDetailId);
    }

    private InvoiceApplyDetailDO invoiceApplyDetailValidateExists(Long invoiceApplyDetailId) {
        InvoiceApplyDetailDO applyDetail = applyDetailMapper.selectById(invoiceApplyDetailId);
        if (applyDetail == null) {
            // throw exception(APPLY_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "开票申请明细不存在");
        }
        return applyDetail;
    }

    @Override
    public InvoiceApplyDetailRespVO invoiceApplyDetailDetail(Long invoiceApplyDetailId) {
        InvoiceApplyDetailDO data = applyDetailMapper.selectById(invoiceApplyDetailId);
        return BeanUtilX.copy(data, InvoiceApplyDetailRespVO::new);
    }

    @Override
    public List<InvoiceApplyDetailRespVO> invoiceApplyDetailList(InvoiceApplyDetailQueryReqVO reqVO) {
        List<InvoiceApplyDetailDO> data = applyDetailMapper.selectList(reqVO);
        return BeanUtilX.copy(data, InvoiceApplyDetailRespVO::new);
    }

    @Override
    public PageResult<InvoiceApplyDetailRespVO> invoiceApplyDetailPage(InvoiceApplyDetailPageReqVO reqVO) {
        PageResult<InvoiceApplyDetailDO> data = applyDetailMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, InvoiceApplyDetailRespVO::new);
    }

}
