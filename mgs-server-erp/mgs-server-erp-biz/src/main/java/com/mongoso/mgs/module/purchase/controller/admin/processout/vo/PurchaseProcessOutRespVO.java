package com.mongoso.mgs.module.purchase.controller.admin.processout.vo;

import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 工序委外采购订单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseProcessOutRespVO extends PurchaseProcessOutBaseVO {

    /** 币种 */
    private String currencyDictName;

    /** 结算方式 */
    private String settlementMethodDictName;

    /** 付款条件 */
    private String paymentTermsDictName;

    /** 主体公司 */
    private String companyOrgName;

    /** 票据类型 */
    private String  invoiceTypeName;

    /** 责任人 */
    private String directorName;

    /** 责任部门 */
    private String directorOrgName;

    /** 关联供应商 */
    private String relatedSupplierName;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 明细列表 */
    private List<PurchaseProcessOutDetailRespVO> detailList;

    /** 审批任务id */
    private Long approveTaskId;
    /** 付款计划策略--取子集 */
    private String paymentPlanStrategyDictName;

    /** 付款计划策略--取父集 */
    private String relatedPaymentPlanStrategyDictName;

    /** 收票计划策略 */
    private String receiptPlanStrategyDictName;

    /** 收票结算池策略 */
    private String receiptSettlementStrategyDictName;

    /** 采购退款计划策略 */
    private String purchaseRefundPlanStrategyDictName;
}
