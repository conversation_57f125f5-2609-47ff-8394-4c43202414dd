package com.mongoso.mgs.module.produce.controller.admin.processoperationlog;

import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.processoperationlog.vo.*;
import com.mongoso.mgs.module.produce.service.processoperationlog.ProcessOperationLogService;

/**
 * 工序操作记录 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class ProcessOperationLogController {

    @Resource
    private ProcessOperationLogService processOperationLogService;

    @OperateLog("工序操作记录添加或编辑")
    @PostMapping("/processOperationLogAdit")
    @PreAuthorize("@ss.hasPermission('processOperationLog:adit')")
    public ResultX<Long> processOperationLogAdit(@Valid @RequestBody ProcessOperationLogAditReqVO reqVO) {
        return success(reqVO.getProcessOperationId() == null
                            ? processOperationLogService.processOperationLogAdd(reqVO)
                            : processOperationLogService.processOperationLogEdit(reqVO));
    }

    @OperateLog("工序操作记录删除")
    @PostMapping("/processOperationLogDel")
    @PreAuthorize("@ss.hasPermission('processOperationLog:delete')")
    public ResultX<Boolean> processOperationLogDel(@Valid @RequestBody ProcessOperationLogPrimaryReqVO reqVO) {
        processOperationLogService.processOperationLogDel(reqVO.getProcessOperationId());
        return success(true);
    }

    @OperateLog("工序操作记录详情")
    @PostMapping("/processOperationLogDetail")
    @PreAuthorize("@ss.hasPermission('processOperationLog:query')")
    public ResultX<ProcessOperationLogRespVO> processOperationLogDetail(@Valid @RequestBody ProcessOperationLogPrimaryReqVO reqVO) {
        return success(processOperationLogService.processOperationLogDetail(reqVO.getProcessOperationId()));
    }

    @DataPermission
    @OperateLog("工序操作记录列表")
    @PostMapping("/processOperationLogList")
    @PreAuthorize("@ss.hasPermission('processOperationLog:query')")
    public ResultX<List<ProcessOperationLogRespVO>> processOperationLogList(@Valid @RequestBody ProcessOperationLogQueryReqVO reqVO) {
        return success(processOperationLogService.processOperationLogList(reqVO));
    }

    @OperateLog("工序操作记录分页")
    @PostMapping("/processOperationLogPage")
    @PreAuthorize("@ss.hasPermission('processOperationLog:query')")
    public ResultX<PageResult<ProcessOperationLogRespVO>> processOperationLogPage(@Valid @RequestBody ProcessOperationLogPageReqVO reqVO) {
        return success(processOperationLogService.processOperationLogPage(reqVO));
    }

}
