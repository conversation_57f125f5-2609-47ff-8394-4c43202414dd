package com.mongoso.mgs.module.produce.service.erpprodorderchangedetail;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo.*;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderchangedetail.ErpProdOrderChangeDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 生产物料 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpProdOrderChangeDetailService {

    /**
     * 创建生产物料
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpProdOrderChangeDetailAdd(@Valid ErpProdOrderChangeDetailAditReqVO reqVO);

    /**
     * 更新生产物料
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpProdOrderChangeDetailEdit(@Valid ErpProdOrderChangeDetailAditReqVO reqVO);

    /**
     * 删除生产物料
     *
     * @param prodOrderChangeDetailId 编号
     */
    void erpProdOrderChangeDetailDel(Long prodOrderChangeDetailId);

    /**
     * 获得生产物料信息
     *
     * @param prodOrderChangeDetailId 编号
     * @return 生产物料信息
     */
    ErpProdOrderChangeDetailRespVO erpProdOrderChangeDetailDetail(Long prodOrderChangeDetailId);

    /**
     * 获得生产物料列表
     *
     * @param reqVO 查询条件
     * @return 生产物料列表
     */
    List<ErpProdOrderChangeDetailRespVO> erpProdOrderChangeDetailList(@Valid ErpProdOrderChangeDetailQueryReqVO reqVO);

    /**
     * 获得生产物料分页
     *
     * @param reqVO 查询条件
     * @return 生产物料分页
     */
    PageResult<ErpProdOrderChangeDetailRespVO> erpProdOrderChangeDetailPage(@Valid ErpProdOrderChangeDetailPageReqVO reqVO);

    /**
     * 查询不可移除物料
     *
     * @param reqVO
     * @return
     */
    void findNonRemovableMaterial(ErpProdOrderChangeDetailQueryReqVO reqVO);
}
