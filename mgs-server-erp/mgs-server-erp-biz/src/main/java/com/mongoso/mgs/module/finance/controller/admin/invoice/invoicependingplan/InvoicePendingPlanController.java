package com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.vo.*;
import com.mongoso.mgs.module.finance.service.invoice.invoicependingplan.InvoicePendingPlanService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 待开票计划 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/invoice")
@Validated
public class InvoicePendingPlanController {

    @Resource
    private InvoicePendingPlanService pendingPlanService;

    @OperateLog("待开票计划添加或编辑")
    @PostMapping("/invoicePendingPlanAdit")
    @PreAuthorize("@ss.hasPermission('invoicePendingPlan:adit')")
    public ResultX<Long> invoicePendingPlanAdit(@Valid @RequestBody InvoicePendingPlanAditReqVO reqVO) {
        return success(reqVO.getInvoicePendingPlanId() == null
                            ? pendingPlanService.invoicePendingPlanAdd(reqVO)
                            : pendingPlanService.invoicePendingPlanEdit(reqVO));
    }


    @OperateLog("待开票计划删除")
    @PostMapping("/invoicePendingPlanDel")
    @PreAuthorize("@ss.hasPermission('invoicePendingPlan:delete')")
    public ResultX<Boolean> invoicePendingPlanDel(@Valid @RequestBody InvoicePendingPlanPrimaryReqVO reqVO) {
        pendingPlanService.invoicePendingPlanDel(reqVO.getInvoicePendingPlanId());
        return success(true);
    }

    @OperateLog("待开票计划详情")
    @PostMapping("/invoicePendingPlanDetail")
    @PreAuthorize("@ss.hasPermission('invoicePendingPlan:query')")
    public ResultX<InvoicePendingPlanRespVO> invoicePendingPlanDetail(@Valid @RequestBody InvoicePendingPlanPrimaryReqVO reqVO) {
        return success(pendingPlanService.invoicePendingPlanDetail(reqVO.getInvoicePendingPlanId()));
    }

    @OperateLog("待开票计划列表")
    @PostMapping("/invoicePendingPlanList")
    @PreAuthorize("@ss.hasPermission('invoicePendingPlan:query')")
    @DataPermission
    public ResultX<List<InvoicePendingPlanRespVO>> invoicePendingPlanList(@Valid @RequestBody InvoicePendingPlanQueryReqVO reqVO) {
        return success(pendingPlanService.invoicePendingPlanList(reqVO));
    }

    @OperateLog("待开票计划分页")
    @PostMapping("/invoicePendingPlanPage")
    @PreAuthorize("@ss.hasPermission('invoicePendingPlan:query')")
    @DataPermission
    public ResultX<PageResult<InvoicePendingPlanRespVO>> invoicePendingPlanPage(@Valid @RequestBody InvoicePendingPlanPageReqVO reqVO) {
        return success(pendingPlanService.invoicePendingPlanPage(reqVO));
    }

}
