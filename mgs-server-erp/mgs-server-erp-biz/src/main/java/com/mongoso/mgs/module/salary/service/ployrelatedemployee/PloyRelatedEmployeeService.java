package com.mongoso.mgs.module.salary.service.ployrelatedemployee;

import java.util.*;

import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeAditReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeePageReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeQueryReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeRespVO;
import jakarta.validation.*;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 策略关联人员 Service 接口
 *
 * <AUTHOR>
 */
public interface PloyRelatedEmployeeService {

    /**
     * 创建策略关联人员
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long ployRelatedEmployeeAdd(@Valid PloyRelatedEmployeeAditReqVO reqVO);

    /**
     * 更新策略关联人员
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long ployRelatedEmployeeEdit(@Valid PloyRelatedEmployeeAditReqVO reqVO);

    /**
     * 删除策略关联人员
     *
     * @param ployRelatedEmployeeId 编号
     */
    void ployRelatedEmployeeDelete(Long ployRelatedEmployeeId);

    /**
     * 获得策略关联人员信息
     *
     * @param ployRelatedEmployeeId 编号
     * @return 策略关联人员信息
     */
    PloyRelatedEmployeeRespVO ployRelatedEmployeeDetail(Long ployRelatedEmployeeId);

    /**
     * 获得策略关联人员列表
     *
     * @param reqVO 查询条件
     * @return 策略关联人员列表
     */
    List<PloyRelatedEmployeeRespVO> ployRelatedEmployeeList(@Valid PloyRelatedEmployeeQueryReqVO reqVO);

    /**
     * 获得策略关联人员分页
     *
     * @param reqVO 查询条件
     * @return 策略关联人员分页
     */
    PageResult<PloyRelatedEmployeeRespVO> ployRelatedEmployeePage(@Valid PloyRelatedEmployeePageReqVO reqVO);

}
