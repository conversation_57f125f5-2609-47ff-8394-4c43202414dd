package com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;


/**
 * 库存 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpMaterialStockQuotedPageReqVO extends PageParam {

    /** 仓库ID */
    private String warehouseOrgId;

    /** 物料编码 */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料编码 */
    private String materialName;

    /** 规格型号 */
    private String specModel;

    /** 物料类别字典ID */
    private String materialCategoryDictId;

    /** 物料BOM_ID*/
    private Long materialBomId;

    /** 排除主键IdList */
    private List<Long> exclMaterialStockIdList;

    /** 排除物料IdList */
    private List<Long> exclMaterialIdList;
}
