package com.mongoso.mgs.module.sale.dal.mysql.workbench;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderDetailResp;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderPageReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderRespVO;
import com.mongoso.mgs.module.sale.controller.admin.workbench.bo.SaleLineBO;
import com.mongoso.mgs.module.sale.controller.admin.workbench.bo.SaleRankBO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售工作台 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SaleWorkbenchMapper {

    /***
     * 查询待交付订单数量
     * @return 待交付订单数量
     */
    Integer getPendingOrderCount();

    /***
     * 查询待出库商品数量
     * @return 待出库商品数量
     */
    BigDecimal getPendingOutboundQty();

    /***
     * 查询待收款金额
     * @return 待收款金额
     */
    BigDecimal getPendingPaymentAmt();

    /***
     * 查询待退货商品数量
     * @return 待退货商品数量
     */
    BigDecimal getReturnPendingInboundedQty();

    /***
     * 查询待退款金额
     * @return 待退款金额
     */
    BigDecimal getPendingReturnAmt();

    /***
     * 查询客户数量
     * @return 客户数量
     */
    Integer getCustomerNumber();

    /***
     *  查询活跃客户数量(近3月)
     * @return 活跃客户数量
     */
    Integer getActiveCustomerNumber();

    /***
     * 查询销售折线图数据
     * @return 折线图数据
     */
    List<SaleLineBO> querySaleAmtLineList();

    /***
     * 查询销售金额排行
     * @return 销售金额排行
     */
    List<SaleRankBO> querySaleAmtRank();

    /***
     * 查询销售数量排行
     * @return 销售数量排行
     */
    List<SaleRankBO> querySaleQtyRank();

}