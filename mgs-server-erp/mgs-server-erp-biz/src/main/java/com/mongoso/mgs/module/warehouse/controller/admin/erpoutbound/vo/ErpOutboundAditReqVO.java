package com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo;

import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailAditReqVO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 出库单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpOutboundAditReqVO extends ErpOutboundBaseVO {

    /** 明细列表 **/
    List<ErpOutboundDetailAditReqVO> detailList;

    /** 清单核对明细列表 **/
    List<ErpOutboundDetailAditReqVO> orderCheckDetailList;
}
