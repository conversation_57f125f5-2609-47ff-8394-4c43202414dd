package com.mongoso.mgs.module.base.controller.admin.prodorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 生产订单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProdOrderQueryReqVO {

    /** 生产订单编码 */
    private String prodOrderCode;

    /** 生产订单编码集合 */
    private List<String> productOrderCodes;

    /** 销售订单编号 */
    private String saleOrderNo;

    /** 产品编码 */
    private String productCode;

    /** 产品名称 */
    private String productName;

    //    bom确认状态 ["待确认",“已确认”]
    private Integer bomStatus;

    //    工单价状态 ["待确认",“已确认”]
    private Integer unitPriceStatus;

    //    工艺路线确认状态 ["待确认",“已确认”]
    private Integer flowProcessStatus;

    /** 开始日期 */
//    @JsonFormat(pattern = FORMAT_yyyy_MM_dd)
//    private LocalDate[] startDate;

    /** 销售订单编号集合 */
    private List<String> saleOrderNos;

    /** 完成日期 */
    @JsonFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate[] completionDate;

    /** 订单状态 待计划/计划中/已计划/已完成/已关闭 */
    private Integer orderState;

    // 订单状态
    private List<Integer> orderStateList;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    //是否需要容差
    private Integer isTolerance;

    /** 限制下限 */
    private BigDecimal lowerLimit;

    /** 限制上限 */
    private BigDecimal upperLimit;

    //容差类型
    private Integer toleranceType;

    //负责人
    private String directorUserIds;

    //负责人名称
    private String directorUserNames;

    //执行人
    private String executorIds;

    //执行人名称
    private String executorNames;

    //执行组织
    private String executeOrgs;

    //执行组织名称
    private String executeOrgNames;

    //物料类型[单物料,多物料]
    private Integer materialQtyType;


}
