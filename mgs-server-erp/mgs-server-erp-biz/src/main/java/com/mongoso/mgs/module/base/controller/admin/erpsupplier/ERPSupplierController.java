package com.mongoso.mgs.module.base.controller.admin.erpsupplier;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.controller.admin.erpsupplier.vo.*;
import com.mongoso.mgs.module.base.service.erpsupplier.ERPSupplierService;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 供应商 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/base")
@Validated
public class ERPSupplierController {

    @Resource
    @Lazy
    private ERPSupplierService erpSupplierService;

    @OperateLog("供应商添加或编辑")
    @PostMapping("/erpSupplierAdit")
    @PreAuthorize("@ss.hasPermission('supplier:adit')")
    public ResultX<Long> supplierAdit(@Valid @RequestBody ERPSupplierAditReqVO reqVO) {
        return success(reqVO.getSupplierId() == null
                            ? erpSupplierService.supplierAdd(reqVO)
                            : erpSupplierService.supplierEdit(reqVO));
    }

    @OperateLog("供应商删除")
    @PostMapping("/erpSupplierDel")
    @PreAuthorize("@ss.hasPermission('supplier:delete')")
    public ResultX<Boolean> supplierDel(@Valid @RequestBody ERPSupplierPrimaryReqVO reqVO) {
        erpSupplierService.supplierDel(reqVO.getSupplierId());
        return success(true);
    }

    @OperateLog("供应商删除(批量)")
    @PostMapping("/erpSupplierDelBatch")
    @PreAuthorize("@ss.hasPermission('supplier:delete')")
    public ResultX<BatchResult> supplierDelBatch(@Valid @RequestBody ERPSupplierPrimaryReqVO reqVO) {
        return erpSupplierService.supplierDelBatch(reqVO);
    }

    @OperateLog("供应商详情")
    @PostMapping("/erpSupplierDetail")
    @PreAuthorize("@ss.hasPermission('supplier:query')")
    public ResultX<ERPSupplierRespVO> supplierDetail(@Valid @RequestBody ERPSupplierPrimaryReqVO reqVO) {
        return success(erpSupplierService.supplierDetail(reqVO.getSupplierId()));
    }


    @OperateLog("供应商结算日期")
    @PostMapping("/getSupplierSettleDate")
    @PreAuthorize("@ss.hasPermission('supplier:query')")
    public ResultX<LocalDate> getSettleDate(@Valid @RequestBody ERPSupplierPrimaryReqVO reqVO) {
        return success(erpSupplierService.getSettleDate(reqVO.getSupplierId()));
    }

    @OperateLog("供应商列表")
    @PostMapping("/erpSupplierList")
    @PreAuthorize("@ss.hasPermission('supplier:query')")
    @DataPermission
    public ResultX<List<ERPSupplierRespVO>> supplierList(@Valid @RequestBody ERPSupplierQueryReqVO reqVO) {
        return success(erpSupplierService.supplierList(reqVO));
    }

    @OperateLog("供应商列表")
    @PostMapping("/erpSupplierQuotedList")
    @PreAuthorize("@ss.hasPermission('supplier:query')")
    public ResultX<List<ERPSupplierRespVO>> supplierQuoteList(@Valid @RequestBody ERPSupplierQueryReqVO reqVO) {
        return success(erpSupplierService.supplierList(reqVO));
    }

    @OperateLog("供应商分页")
    @PostMapping("/erpSupplierPage")
    @PreAuthorize("@ss.hasPermission('supplier:query')")
    @DataPermission
    public ResultX<PageResult<ERPSupplierRespVO>> supplierPage(@Valid @RequestBody ERPSupplierPageReqVO reqVO) {
        return success(erpSupplierService.supplierPage(reqVO));
    }

    @OperateLog("供应商分页")
    @PostMapping("/erpSupplierQuotedPage")
    @PreAuthorize("@ss.hasPermission('supplier:query')")
    public ResultX<PageResult<ERPSupplierRespVO>> supplierQuotePage(@Valid @RequestBody ERPSupplierPageReqVO reqVO) {
        return success(erpSupplierService.supplierPage(reqVO));
    }

    @OperateLog("供应商审核")
    @PostMapping("/erpSupplierApprove")
    @PreAuthorize("@ss.hasPermission('container:adit')")
    public ResultX<BatchResult> erpSupplierApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = erpSupplierService.erpSupplierApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("供应商回调接口")
    @PostMapping("/erpSupplierFlowCallback")
    public ResultX<Object> erpSupplierFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(erpSupplierService.erpSupplierFlowCallback(reqVO));
    }



}
