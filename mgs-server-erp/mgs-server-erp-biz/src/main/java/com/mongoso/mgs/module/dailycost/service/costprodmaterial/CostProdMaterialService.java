package com.mongoso.mgs.module.dailycost.service.costprodmaterial;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodmaterial.vo.*;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.AmortiseStatusReqVO;

import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseAditReqVO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产物料成本单 Service 接口
 *
 * <AUTHOR>
 */
public interface CostProdMaterialService {

    /**
     * 创建生产物料成本单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costProdMaterialAdd(@Valid CostProdMaterialAditReqVO reqVO);

    /**
     * 更新生产物料成本单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long costProdMaterialEdit(@Valid CostProdMaterialAditReqVO reqVO);

    /**
     * 生产物料成本单修改承担物料信息
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long undertakeMaterialAdit(@Valid CostProdMaterialAditReqVO reqVO);

    /**
     * 通过报工记录ID更新生产物料成本单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    void reportedWorkIdEdit(@Valid ReportedWorkIdEditReqVO reqVO);

    /**
     * 删除生产物料成本单
     *
     * @param costProdMaterialId 编号
     */
    void costProdMaterialDel(Long costProdMaterialId);

    /**
     *
     * 报工记录ID删除生产物料成本单
     *
     * @param reportedWorkId 编号
     */
    void reportedWorkIdDel(Long reportedWorkId);

    /**
     * 获得生产物料成本单信息
     *
     * @param costProdMaterialId 编号
     * @return 生产物料成本单信息
     */
    CostProdMaterialRespVO costProdMaterialDetail(Long costProdMaterialId);

    /**
     * 获得生产物料成本单信息
     *
     * @param reportedWorkId 编号
     * @return 生产物料成本单信息
     */
    CostProdMaterialRespVO reportedWorkIdDetail(Long reportedWorkId);

    /**
     * 获得生产物料成本单列表
     *
     * @param reqVO 查询条件
     * @return 生产物料成本单列表
     */
    List<CostProdMaterialRespVO> costProdMaterialList(@Valid CostProdMaterialQueryReqVO reqVO);

    /**
     * 获得生产物料成本单分页
     *
     * @param reqVO 查询条件
     * @return 生产物料成本单分页
     */
    PageResult<CostProdMaterialRespVO> costProdMaterialPage(@Valid CostProdMaterialPageReqVO reqVO);

    /**
     * 摊销状态变更
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    BatchResult costProdMaterialAmortise(@Valid AmortiseStatusReqVO reqVO);

}
