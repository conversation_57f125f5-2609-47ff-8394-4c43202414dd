package com.mongoso.mgs.module.produce.handler.approve;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.enums.material.MaterialEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.purchase.PurchaseBizTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO;
import com.mongoso.mgs.module.base.dal.db.processconfig.ProcessConfigDO;
import com.mongoso.mgs.module.base.dal.mysql.erpbase.ErpBaseMapper;
import com.mongoso.mgs.module.base.dal.mysql.erpmaterial.ERPMaterialMapper;
import com.mongoso.mgs.module.base.dal.mysql.processconfig.ProcessConfigMapper;
import com.mongoso.mgs.module.base.service.orderrelation.OrderRelationService;
import com.mongoso.mgs.module.finance.controller.admin.strategyconfig.vo.StrategyConfigRespVO;
import com.mongoso.mgs.module.finance.service.strategyconfig.StrategyConfigService;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderdetail.vo.ErpProdOrderDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocess.vo.FlowProcessRespVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal.bo.MaterialAnalysisTotalBO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomRespVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodworkmaterialbom.vo.ProdWorkMaterialBomRespVO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO;
import com.mongoso.mgs.module.produce.dal.db.materialsupplier.MaterialSupplierDO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderdetail.ErpProdOrderDetailMapper;
import com.mongoso.mgs.module.produce.service.flowprocess.FlowProcessService;
import com.mongoso.mgs.module.produce.service.materialanalysistotal.MaterialAnalysisTotalService;
import com.mongoso.mgs.module.produce.service.materialbom.MaterialBomService;
import com.mongoso.mgs.module.produce.service.prodwork.ProdWorkService;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailAditReqVO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.purchase.service.purchase.PurchaseOrderService;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import com.mongoso.mgs.module.sale.enums.FormStatusEnum;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import static com.mongoso.mgs.module.sale.enums.ErrorCodeConstants.RELATED_SALE_FORCE_CLOSE;

/**
 * @author: zhiling
 * @date: 2024/11/26 18:34
 * @description: 生产订单审批流程处理类
 */

@Component
public class ErpProdOrderApproveHandler extends FlowApproveHandler<ErpProdOrderDO> {

    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;

    @Resource
    private ErpProdOrderDetailMapper prodOrderDetailMapper;

    @Resource
    private ErpBaseMapper erpBaseMapper;

    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Resource
    private MaterialAnalysisTotalService materialAnalysisTotalService;

    @Resource
    private OrderRelationService orderRelationService;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private StockBookService stockBookService;


    @Resource
    private ERPMaterialMapper erpMaterialMapper;
    @Lazy
    @Resource
    private ProdWorkService prodWorkService;

    @Resource
    private ProcessConfigMapper processConfigMapper;

    @Resource
    private MaterialBomService materialBomService;

    @Resource
    private FlowProcessService flowProcessService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(ErpProdOrderDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(ErpProdOrderDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(ErpProdOrderDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getProdOrderId())
                .objCode(item.getProdOrderCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(ErpProdOrderDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            if (item.getProdOrderBizType().equals(1)) {
                //查询销售订单
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getRelatedOrderId());
                if (erpSaleOrder == null) {
                    failItem.setCode(item.getProdOrderCode());
                    failItem.setReason("关联的销售订单不存在！");
                    return false;
                }

                if (erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type) {
                    failItem.setCode(item.getProdOrderCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }

//            //物料分析生产订单 todo 目前去掉限制
//            if (item.getProdOrderBizType().equals(2)){
//
//                //查询生产订单明细
//                List<ErpProdOrderDetailDO> detailDOList = prodOrderDetailMapper.selectMaterialAnalysis(item.getRelatedOrderId(), item.getProdOrderId());
//                if (CollUtilX.isNotEmpty(detailDOList)){
//                    //查询物料分析汇总表
//                    MaterialAnalysisTotalQueryReqVO materialAnalysisTotalQuery = new MaterialAnalysisTotalQueryReqVO();
//                    materialAnalysisTotalQuery.setMaterialAnalysisId(item.getRelatedOrderId());
//                    materialAnalysisTotalQuery.setMaterialSourceDictId(0L);
//                    List<MaterialAnalysisTotalDO> analysisTotalList = materialAnalysisTotalMapper.selectListOld(materialAnalysisTotalQuery);
//
//                    Map<Long, BigDecimal> analysisTotalMap = analysisTotalList.stream().collect(Collectors.toMap(MaterialAnalysisTotalDO::getMaterialId, MaterialAnalysisTotalDO::getNetDemandQty));
//
//                    for (ErpProdOrderDetailDO prodOrderDetailDO : detailDOList){
//                        BigDecimal netDemandQty = analysisTotalMap.get(prodOrderDetailDO.getMaterialId());
//                        if (netDemandQty!=null && prodOrderDetailDO.getProdPlanQty().compareTo(netDemandQty) > 0) {
//                            failItem.setCode(item.getProdOrderCode());
//                            failItem.setReason(PROD_QTY_EXCEEDS_ALLOWED.getMsg());
//                            return false;
//                        }
//                    }
//                }
//            }

        }

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

            if (item.getProdOrderBizType().equals(1)) {
                //查询销售订单
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getRelatedOrderId());
                if (erpSaleOrder == null){
                    failItem.setCode(item.getProdOrderCode());
                    failItem.setReason("关联的销售订单不存在！");
                    return false;
                }
                if (erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type) {
                    failItem.setCode(item.getProdOrderCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }


            //查询下游生产工单
            String tableName = EntityUtilX.getTableNameByAnnot(ProdWorkDO.class);
            String idName = EntityUtilX.getPropertyName(ProdWorkDO::getProdOrderId);
            Integer count = erpBaseMapper.queryDownstreamApproved(tableName, idName, item.getProdOrderId());
            if (count > 0) {
                failItem.setCode(item.getProdOrderCode());
                failItem.setReason("已关联审核的生产工单,不允许进行反审核!");
                return false;
            }

            //查询下游采购订单
            String tableName2 = EntityUtilX.getTableNameByAnnot(PurchaseOrderDO.class);
            String idName2 = EntityUtilX.getPropertyName(PurchaseOrderDO::getRelatedOrderId);
            Integer count2 = erpBaseMapper.queryDownstreamApproved(tableName2, idName2, item.getProdOrderId());
            if (count2 > 0) {
                failItem.setCode(item.getProdOrderCode());
                failItem.setReason("已关联审核的生产委外采购订单,不允许进行反审核!");
                return false;
            }
        }
        return true;
    }

    @Override
    public Integer handleBusinessData(ErpProdOrderDO currentDO, BaseApproveRequest request) {
        //当前对象
        if (currentDO == null) {
            return 1;
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = currentDO.getProdOrderId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        //查询生产订单的明细
        ErpProdOrderDetailQueryReqVO detailQueryReqVO = new ErpProdOrderDetailQueryReqVO();
//        detailQueryReqVO.setMaterialSourceDictId(MaterialEnum.OUT_MADE.key);
        List<Integer> materialSourceDictIds = new ArrayList<>();
        materialSourceDictIds.add(MaterialEnum.OUT_MADE.key);
        materialSourceDictIds.add(MaterialEnum.SELF_MADE.key);
        detailQueryReqVO.setMaterialSourceDictIdList(materialSourceDictIds);
        detailQueryReqVO.setProdOrderId(id);
        List<ErpProdOrderDetailDO> prodOrderDetailDOList = prodOrderDetailMapper.queryList(detailQueryReqVO);
        if (CollUtilX.isEmpty(prodOrderDetailDOList)) {
            currentDO.setIsFullPurchased(1);
        } else {
            currentDO.setIsFullPurchased(0);
        }

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            // 判断是否需要自动下发生产工单
            if (currentDO.getIsIssueProdWork() != null && currentDO.getIsIssueProdWork() == 1) {
                autoIssueProdWork(currentDO);
            }
        }

        //更新物料分析数量汇总表 订单已下发数量
        ErpProdOrderDetailQueryReqVO erpOrderDetailQuery = new ErpProdOrderDetailQueryReqVO();
        erpOrderDetailQuery.setProdOrderId(id);
        List<ErpProdOrderDetailDO> prodOrderDetailList = prodOrderDetailMapper.selectListOld(erpOrderDetailQuery);

        for (ErpProdOrderDetailDO prodOrderDetailDO : prodOrderDetailList) {
            MaterialAnalysisTotalBO analysisTotalBO = new MaterialAnalysisTotalBO();
            analysisTotalBO.setProdOrderId(prodOrderDetailDO.getProdOrderId());
            analysisTotalBO.setMaterialId(prodOrderDetailDO.getMaterialId());

            BigDecimal prodPlanQty = prodOrderDetailDO.getProdPlanQty();
            if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                prodPlanQty = prodPlanQty.negate();
            }
            analysisTotalBO.setProdIssuedQty(prodPlanQty);
            materialAnalysisTotalService.writebackMaterialAnalysisTotal(analysisTotalBO);
        }

        //关联信息处理
        orderRelationService.orderRelationAditOrDel(buttonType, id, currentDO.getProdOrderCode(), currentDO.getRelatedOrderId());

        //处理单据关联的预订任务
        stockBookService.bookReleaseByOrderApprove(buttonType, id);

        if (currentDO.getProdOrderBizType().equals(1)) {
            //销售销售订单状态
            erpSaleOrderService.editChildrenOrderCount(currentDO.getRelatedOrderId(), buttonType);
        }

        //更新状态
        currentDO.setApprovedBy(loginUser.getFullUserName());
        currentDO.setApprovedDt(LocalDateTime.now());
        currentDO.setDataStatus(dataStatus);

        int updateCount = erpProdOrderMapper.updateById(currentDO);
        return updateCount;
    }

    /**
     * 自动下发生产工单
     * 只能下发生产订单中物料来源=【自制】类型的物料
     * 生产订单的多个物料对应多个工单
     *
     * @param currentDO 生产订单
     */
    private void autoIssueProdWork(ErpProdOrderDO currentDO) {
        Long prodOrderId = currentDO.getProdOrderId();

        // 查询生产订单中物料来源=【自制】类型的物料
        ErpProdOrderDetailQueryReqVO selfMadeQuery = new ErpProdOrderDetailQueryReqVO();
        selfMadeQuery.setProdOrderId(prodOrderId);
        selfMadeQuery.setMaterialSourceDictId(MaterialEnum.SELF_MADE.key);
        List<ErpProdOrderDetailDO> selfMadeDetails = prodOrderDetailMapper.selectList(selfMadeQuery);

        if (CollUtilX.isEmpty(selfMadeDetails)) {
            return;
        }

        // 查询流程配置
        Map<String, ProcessConfigDO> configMap = new HashMap<>();
        List<ProcessConfigDO> processConfigs = processConfigMapper.selectList(
                LambdaQueryWrapperX.<ProcessConfigDO>lambdaQueryX()
                        .eq(ProcessConfigDO::getModuleName, "produce")
                        .in(ProcessConfigDO::getProcessType, Arrays.asList(2, 4))
        );

        for (ProcessConfigDO config : processConfigs) {
            configMap.put("produce_" + config.getProcessType(), config);
        }

        // 获取派工流程配置
        Integer dispatchProcessConfig = 0; // 默认不自动派工
        ProcessConfigDO dispatchConfig = configMap.get("produce_2");
        if (dispatchConfig != null && dispatchConfig.getBaseInfo() != null
                && dispatchConfig.getBaseInfo().containsKey("processKey")) {
            dispatchProcessConfig = dispatchConfig.getBaseInfo().getInteger("processKey");
        }

        // 获取出库和入库流程配置
        Integer outProcessConfig = 0; // 默认不自动出库
        Integer inProcessConfig = 0; // 默认不自动入库
        ProcessConfigDO workConfig = configMap.get("produce_4");
        if (workConfig != null && workConfig.getBaseInfo() != null) {
            JSONObject baseInfo = workConfig.getBaseInfo();
            if (baseInfo.containsKey("outProcessConfig")) {
                outProcessConfig = baseInfo.getInteger("outProcessConfig");
            }
            if (baseInfo.containsKey("inProcessConfig")) {
                inProcessConfig = baseInfo.getInteger("inProcessConfig");
            }
        }

        // 为每个自制物料创建一个生产工单
        for (ErpProdOrderDetailDO detail : selfMadeDetails) {
            // 创建一个生产工单
            ProdWorkAditReqVO prodWorkReqVO = new ProdWorkAditReqVO();

            // 设置基本信息
            prodWorkReqVO.setProdOrderId(prodOrderId);
            prodWorkReqVO.setProdOrderCode(currentDO.getProdOrderCode());
            prodWorkReqVO.setFormDt(currentDO.getFormDt());
            prodWorkReqVO.setDirectorId(currentDO.getDirectorId());
            prodWorkReqVO.setDirectorOrgId(currentDO.getDirectorOrgId());
            prodWorkReqVO.setRemark(currentDO.getRemark());
            prodWorkReqVO.setProcessConfig(currentDO.getProcessConfig());
            prodWorkReqVO.setRowNo(detail.getRowNo());

            // 设置物料信息
            prodWorkReqVO.setMaterialId(detail.getMaterialId());
            prodWorkReqVO.setMaterialCode(detail.getMaterialCode());

            // 获取物料信息
            ERPMaterialDO materialDO = erpMaterialMapper.selectById(detail.getMaterialId());
            if (materialDO != null) {
                prodWorkReqVO.setMaterialName(materialDO.getMaterialName());
                prodWorkReqVO.setSpecModel(materialDO.getSpecModel());
                prodWorkReqVO.setMainUnitDictId(materialDO.getMainUnitDictId());
            }

            // 设置计划数量和日期
            prodWorkReqVO.setWorkPlanTotalQty(detail.getProdPlanQty());
            prodWorkReqVO.setPriority(detail.getPriority());
            // 设置是否入库和自动入库，优先级为1的生产工单默认入库和自动入库，其他优先级默认不入库
            prodWorkReqVO.setIsInbound(detail.getPriority() != null && detail.getPriority() == 1 ? 1 : 0);
            prodWorkReqVO.setIsAutoInbound(detail.getPriority() != null && detail.getPriority() == 1 ? 1 : 0);
            prodWorkReqVO.setPlanStartDate(detail.getPlanStartDate() != null ? detail.getPlanStartDate() : currentDO.getPlanStartDate());
            prodWorkReqVO.setPlanEndDate(detail.getPlanEndDate() != null ? detail.getPlanEndDate() : currentDO.getPlanEndDate());

            // 设置流程配置
            prodWorkReqVO.setDispatchStrategyConfig(dispatchProcessConfig);
            prodWorkReqVO.setOutProcessConfigDictId(outProcessConfig);
            prodWorkReqVO.setInProcessConfigDictId(inProcessConfig);

            // 设置状态为待生产
            prodWorkReqVO.setFormStatus(OrderStatusEnum.PENDING.getCode());
            prodWorkReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);

            // 查询物料BOM明细
            MaterialBomQueryReqVO bomQuery = new MaterialBomQueryReqVO();
            bomQuery.setMaterialId(detail.getMaterialId());
            bomQuery.setDataStatus(DataStatusEnum.APPROVED.key);
            MaterialBomRespVO materialBomDetail = materialBomService.findMaterialBomDetail(bomQuery);

            if (materialBomDetail != null && CollUtilX.isNotEmpty(materialBomDetail.getDetailList())) {
                // 使用BOM明细作为工单BOM明细
                List<ProdWorkMaterialBomRespVO> bomDetailList = new ArrayList<>();
                int rowNo = 1;

                for (MaterialBomRespVO bomDetail : materialBomDetail.getDetailList()) {
                    ProdWorkMaterialBomRespVO bomDetailReqVO = new ProdWorkMaterialBomRespVO();
                    bomDetailReqVO.setRowNo(rowNo++);
                    bomDetailReqVO.setMaterialId(bomDetail.getMaterialId());
                    bomDetailReqVO.setMaterialCode(bomDetail.getMaterialCode());
                    bomDetailReqVO.setMaterialName(bomDetail.getMaterialName());
                    bomDetailReqVO.setSpecModel(bomDetail.getSpecModel());
                    bomDetailReqVO.setMainUnitDictId(bomDetail.getMainUnitDictId());
                    bomDetailReqVO.setLossRate(bomDetail.getLossRate());

                    bomDetailReqVO.setDemandQty(bomDetail.getDemandQty());

                    // 计算预估总量 = 计划生产数量 * 需求数量 * (1 + 损耗率/100)
                    BigDecimal lossRate = bomDetail.getLossRate();
                    BigDecimal lossRateFactor = BigDecimal.ONE.add(
                            lossRate.divide(BigDecimal.valueOf(100), 12, RoundingMode.HALF_UP));
                    BigDecimal estimatedTotalQty = detail.getProdPlanQty()
                            .multiply(bomDetail.getDemandQty())
                            .multiply(lossRateFactor)
                            .setScale(6, RoundingMode.HALF_UP);
                    bomDetailReqVO.setEstimatedTotalQty(estimatedTotalQty);

                    // 设置其他属性
                    bomDetailReqVO.setWarehouseOrgId(bomDetail.getWarehouseOrgId());
                    bomDetailReqVO.setPickingMethodDictId(bomDetail.getPickingMethodDictId());
                    bomDetailReqVO.setRemark(bomDetail.getRemark());

                    bomDetailList.add(bomDetailReqVO);
                }

                prodWorkReqVO.setBomDetailList(bomDetailList);
            }

            // 查询工艺路线
            FlowProcessRespVO flowProcessRespVO = flowProcessService.flowProcessByMaterial(detail.getMaterialId());
            if (flowProcessRespVO != null && CollUtilX.isNotEmpty(flowProcessRespVO.getDetailList())) {
                prodWorkReqVO.setDetailList(flowProcessRespVO.getDetailList());
            }

            // 保存生产工单
            prodWorkService.prodWorkAdd(prodWorkReqVO);
        }
    }
}
