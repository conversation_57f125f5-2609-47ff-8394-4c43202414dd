package com.mongoso.mgs.module.produce.dal.db.mold;

import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 模具领用明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_mold_use_detail", autoResultMap = true)
//@KeySequence("u_mold_use_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MoldUseDetailDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 行号 */
    private Short rowNo;

    private Long moldUseId;

    /** 领用单号 */
    private String moldUseCode;

    /** 模具id */
    private Long moldId;
    private String moldCode;

    /** 领用人 */
    private Long usedBy;

    /** 预计归还时间 */
    private LocalDate planReturnDate;

    /** 备注 */
    private String remark;

    private Integer returnedStatus;
    private Long returnedBy;
    private LocalDate returnedDate;



}
