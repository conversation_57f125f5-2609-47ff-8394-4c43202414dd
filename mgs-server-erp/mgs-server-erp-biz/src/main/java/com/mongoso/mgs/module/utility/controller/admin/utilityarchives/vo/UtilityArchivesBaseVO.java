package com.mongoso.mgs.module.utility.controller.admin.utilityarchives.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 水电气档案 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class UtilityArchivesBaseVO implements Serializable {

    /** 水电气档案id */
    private Long utilityArchivesId;

    /** 水电气档案编码 */
    private String utilityArchivesCode;

    /** 档案表名称 */
    @NotEmpty(message = "表名称不能为空")
    private String archivesName;

    /** 水电气表id */
    @NotNull(message = "表类型不能为空")
    private Long utilityConfigId;

    /** 公司id */
    @NotNull(message = "所属公司不能为空")
    private Long companyId;

    /** 转化倍数 */
    @NotNull(message = "转化倍数不能为空")
    private BigDecimal converMult;

    /** 表初始数 */
    @NotNull(message = "表初始值不能为空")
    private BigDecimal utilityInitNum;

    /** 初始时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime utilityInitTime;

    /** 表初始使用量 */
    private BigDecimal utilityUsageNum;

    /** 上次抄表时间 */
    private LocalDateTime lastDt;

    /** 上次抄表数 */
    private BigDecimal lastNum;

    /** 最后抄表记录ID */
    private Long lastLogId;

    /** 是否计入成本 */
    @NotNull(message = "是否记入成本不能为空")
    private Short costEnable;

    /** 审核人 */
    private String approvedBy;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @NotNull(message = "单据时间不能为空")
    private LocalDateTime formDt;

    /** 责任人 */
    @NotNull(message = "责任人不能为空")
    private Long directorId;

    /** 责任部门 */
    @NotEmpty(message = "责任部门不能为空")
    private String directorOrgId;

    /** 版本号 */
    private Integer version;


}
