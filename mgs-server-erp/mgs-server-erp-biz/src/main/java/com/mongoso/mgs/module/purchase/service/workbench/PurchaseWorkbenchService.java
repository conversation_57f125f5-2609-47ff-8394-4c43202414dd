package com.mongoso.mgs.module.purchase.service.workbench;

import com.mongoso.mgs.common.charts.vo.ChartsLineRespVO;
import com.mongoso.mgs.common.charts.vo.ChartsRankRespVO;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.vo.PurchaseOrderDataRespVO;
import com.mongoso.mgs.module.purchase.dal.mysql.workbench.vo.PurchaseSupplierDataRespVO;
import com.mongoso.mgs.module.sale.controller.admin.workbench.vo.SaleCustomerDataRespVO;
import com.mongoso.mgs.module.sale.controller.admin.workbench.vo.SaleOrderDataRespVO;

import java.util.List;

/**
 * 采购工作台 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseWorkbenchService {

    /**
     * 查询采购订单数据
     * @return
     */
    PurchaseOrderDataRespVO purchaseOrderDataStat();

    /**
     * 查询采购供应商数据
     * @return
     */
    PurchaseSupplierDataRespVO purchaseSupplierDataStat();

    /**
     * 查询采购金额趋势
     * @return
     */
     ChartsLineRespVO purchaseAmtLineStat();

    /**
     * 查询采购数量排行
     * @return
     */
    List<ChartsRankRespVO> purchaseQtyRankStat();

    /**
     * 查询采购金额排行
     * @return
     */
    List<ChartsRankRespVO> purchaseAmtRankStat();
}
