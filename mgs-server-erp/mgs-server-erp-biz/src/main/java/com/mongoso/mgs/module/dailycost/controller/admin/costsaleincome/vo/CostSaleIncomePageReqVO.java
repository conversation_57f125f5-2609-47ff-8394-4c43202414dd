package com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 


/**
 * 销售收入单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostSaleIncomePageReqVO extends PageParam {

    /** 摊销状态 */
    private Short amortiseStatus;

    /** 关联上游单号 */
    private String relatedUpFormCode;

    /** 关联上游单ID */
    private Long relatedUpFormId;

    /** 单据类型 */
    private String outboundTypeDictId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 数量 */
    private BigDecimal qty;

    /** 总金额 */
    private BigDecimal totalAmt;

    /** 订单类型 */
    private Short orderType;

    /** 承担对象ID */
    private String undertakeOrgId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 承担物料id */
    private Long undertakeMaterialId;

    /** 承担物料编码 */
    private String undertakeMaterialCode;

    /**物料BOMID路径*/
    private String materialBomIdPath;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
