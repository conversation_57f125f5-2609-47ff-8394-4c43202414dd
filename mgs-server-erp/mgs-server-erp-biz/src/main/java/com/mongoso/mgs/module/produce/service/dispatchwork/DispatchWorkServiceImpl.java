package com.mongoso.mgs.module.produce.service.dispatchwork;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.produce.DictToEnum;
import com.mongoso.mgs.common.enums.produce.ProduceEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.datapermission.core.enums.DataScopeEnum;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.controller.admin.processconfig.vo.ProcessConfigQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.processconfig.ProcessConfigDO;
import com.mongoso.mgs.module.base.dal.mysql.processconfig.ProcessConfigMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.produce.controller.admin.calculatepriceorder.vo.CalculatePriceOrderPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.calculatepriceorder.vo.CalculatePriceOrderQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.calculatepriceorder.vo.CalculatePriceOrderRespVO;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.bo.DispatchDataRespBO;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.bo.DispatchDataVerifyReqBO;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.bo.DispatchWriteBackBO;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.bo.ErpProdOrderBO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocessdetail.vo.FlowProcessDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessRespVO;
import com.mongoso.mgs.module.produce.controller.admin.processoperationlog.vo.ProcessOperationLogAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.DispatchProcessOutDemandRespVO;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandRespVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkBO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.reportedwork.bo.ReportedEditReqBO;
import com.mongoso.mgs.module.produce.controller.admin.workcenter.vo.WorkCenterQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workcenterrel.vo.WorkCenterRelQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.calculatepriceorder.CalculatePriceOrderDO;
import com.mongoso.mgs.module.produce.dal.db.flowprocessdetail.FlowProcessDetailDO;
import com.mongoso.mgs.module.produce.dal.db.process.ProcessDO;
import com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.workcenter.WorkCenterDO;
import com.mongoso.mgs.module.produce.dal.db.workcenterrel.WorkCenterRelDO;
import com.mongoso.mgs.module.produce.dal.mysql.calculatepriceorder.CalculatePriceOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.flowprocessdetail.FlowProcessDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.process.ProcessMapper;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workcenter.WorkCenterMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workcenterrel.WorkCenterRelMapper;
import com.mongoso.mgs.module.produce.service.dispatchwork.bo.DispatchProcessOutDemandReqBO;
import com.mongoso.mgs.module.produce.service.dispatchwork.bo.DispatchQtyRespBO;
import com.mongoso.mgs.module.produce.service.erpprodorder.ErpProdOrderService;
import com.mongoso.mgs.module.produce.service.processoperationlog.ProcessOperationLogService;
import com.mongoso.mgs.module.produce.service.prodwork.ProdWorkService;
import com.mongoso.mgs.module.produce.service.reportedwork.ReportedWorkService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import com.mongoso.mgs.module.produce.controller.admin.dispatchwork.vo.*;
import com.mongoso.mgs.module.produce.dal.db.dispatchwork.DispatchWorkDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.dispatchwork.DispatchWorkMapper;
import com.mongoso.mgs.framework.common.exception.BizException;


/**
 * 派工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DispatchWorkServiceImpl implements DispatchWorkService {

    @Resource
    private DispatchWorkMapper dispatchWorkMapper;

    @Resource
    private ProcessMapper processMapper;

    @Resource
    private FlowProcessDetailMapper flowProcessDetailMapper;

    @Resource
    private SeqService seqService;

    @Resource
    private ProcessOperationLogService processOperationLogService;

    @Resource
    private ProcessOutDemandMapper processOutDemandMapper;

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private WorkCenterMapper workCenterMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private ErpProdOrderService erpProdOrderService;

    @Resource
    private ProdWorkService prodWorkService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ReportedWorkService reportedWorkService;

    @Resource
    private WorkCenterRelMapper workCenterRelMapper;

    @Resource
    private ProcessConfigMapper processConfigMapper;

    @Resource
    private CalculatePriceOrderMapper calculatePriceOrderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long dispatchWorkAdd(DispatchWorkAditReqVO reqVO) {

        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setProdWorkId(reqVO.getProdWorkId());
        checkDataPermission(prodWorkQuery);

        //派工数量校验
        DispatchDataVerifyReqBO dataVerifyReqBO = new DispatchDataVerifyReqBO();
        dataVerifyReqBO.setProdWorkId(reqVO.getProdWorkId());
        dataVerifyReqBO.setMaterialId(reqVO.getMaterialId());
        dataVerifyReqBO.setDispatchMethod(reqVO.getDispatchMethod());
        dataVerifyReqBO.setProcessId(reqVO.getProcessId());
        dataVerifyReqBO.setDispatchQty(reqVO.getOperedQty());
        dataVerifyReqBO.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());
        dispatchQtyVerify(dataVerifyReqBO);

        Integer dispatchMethod = reqVO.getDispatchMethod();
        if (dispatchMethod == 0) {
             workProcessDispatchAdd(reqVO);
        } else {
            //拆单处理方法
             processDispatchSplitAdd(reqVO);
        }

        return 0L;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long dispatchWorkEdit(DispatchWorkAditReqVO reqVO) {

        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setProdWorkId(reqVO.getProdWorkId());
        checkDataPermission(prodWorkQuery);

        Integer dispatchMethod = reqVO.getDispatchMethod();
        Integer processOperationType = reqVO.getProcessOperationType();

        //派工数量校验
        DispatchDataVerifyReqBO dataVerifyReqBO = new DispatchDataVerifyReqBO();
        dataVerifyReqBO.setProdWorkId(reqVO.getProdWorkId());
        dataVerifyReqBO.setMaterialId(reqVO.getMaterialId());
        dataVerifyReqBO.setDispatchMethod(dispatchMethod);
        dataVerifyReqBO.setProcessId(reqVO.getProcessId());
        dataVerifyReqBO.setDispatchQty(reqVO.getOperedQty());
        dataVerifyReqBO.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());

        if (processOperationType.equals(0)) {//新增
            dataVerifyReqBO.setDispatchQty(reqVO.getOperedQty());

            //派工数量校验
            dispatchQtyVerify(dataVerifyReqBO);
        } else if (processOperationType.equals(1)) {//编辑
            dataVerifyReqBO.setDispatchQty(reqVO.getDispatchQty());

            //派工数量校验
            dispatchQtyVerify(dataVerifyReqBO);
        }

        if (dispatchMethod == 0) {
            //查询 待生产|生产中 的派工单数据
            DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
            dispatchWorkQuery.setProdWorkId(reqVO.getProdWorkId());
            dispatchWorkQuery.setMaterialId(reqVO.getMaterialId());
            dispatchWorkQuery.setDispatchMethod(dispatchMethod);
            dispatchWorkQuery.setProcessId(reqVO.getProcessId());
            dispatchWorkQuery.setWorkCenterId(reqVO.getWorkCenterId());
            dispatchWorkQuery.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());

            List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
            if (CollUtilX.isNotEmpty(dispatchWorkDOList)) {
                BigDecimal totalPendingQty = dispatchWorkDOList.stream().map(DispatchWorkDO::getPendingQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalPendingQty.compareTo(reqVO.getOldPendingQty()) != 0) {
                    throw new BizException("500", "待生产数量已被修改,请重新点击查询按钮!");
                }
            }

            //编辑
            workProcessDispatchEdit(reqVO);
        } else {
            //拆单处理方法
            processDispatchEdit(reqVO);
        }

        return 0L;

    }


    /**
     * 工单工序派工
     *
     * @param reqVO
     * @return
     */
    @Transactional
    @Override
    public Long workProcessDispatchAdd(DispatchWorkAditReqVO reqVO) {
        //生成id
        reqVO.setDispatchWorkId(IDUtilX.getId());

        // 生成单号
        LocalDate now = LocalDate.now();
        Long seq = seqService.lockInsertLamb(SeqEnum.PROCESSOPERATIONCODE.getTableName(), now, null);
        String code = CodeGenUtil.generateCode(CodeGenUtil.CodeTypeEnums.DISPATCH_WORK_CODE, seq);

        //设置属性
        reqVO.setDispatchWorkCode(code);
        reqVO.setFormStatus(OrderStatusEnum.DISPATCHED.getCode());

        BigDecimal planDispatchQty = reqVO.getDispatchQty();
        //待生产
        reqVO.setPendingQty(planDispatchQty);

        // 插入
        DispatchWorkDO dispatchWork = BeanUtilX.copy(reqVO, DispatchWorkDO::new);
        dispatchWorkMapper.insert(dispatchWork);

        //更新生产工单单据状态
        ProdWorkDO workCodeDO = new ProdWorkDO();
        workCodeDO.setProdWorkId(dispatchWork.getProdWorkId());
        workCodeDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
        prodWorkMapper.updateById(workCodeDO);

        //新增工序操作记录表
        addProcessOperationLog(dispatchWork, reqVO.getProcessOperationType(), reqVO.getLogSource());

        // 返回
        return dispatchWork.getDispatchWorkId();
    }

    /**
     * 工序派工
     *
     * @param reqVO
     * @return
     */
    private Long processDispatchSplitAdd(DispatchWorkAditReqVO reqVO) {

        Integer dispatchMethod = reqVO.getDispatchMethod();
        Long workCenterId = reqVO.getWorkCenterId();
        Long materialId = reqVO.getMaterialId();
        Long processId = reqVO.getProcessId();
        Long processMethod = reqVO.getProcessMethod();

        BigDecimal finalDispatchQty = reqVO.getDispatchQty();
        BigDecimal dispatchQty = reqVO.getDispatchQty();
        String pieceworkMethodDictId = reqVO.getPieceworkMethodDictId();

        //查询派过工的生产工单
        DispatchDataRespBO dispatchVerifiyRespBO = findDispatchData(workCenterId, materialId, processId, dispatchMethod, processMethod,pieceworkMethodDictId);

        //排除已派完工的工单
        List<Long> exclProdWorkIdList = dispatchVerifiyRespBO.getProdWorkIdList();

        //可派数量
        Map<Long, BigDecimal> dispatchAbleQtyMap = dispatchVerifiyRespBO.getDispatchAbleQtyMap();

        //生产工单
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询工艺路线下的生产工单
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setMaterialId(materialId);
        prodWorkQuery.setProcessConfig(dispatchMethod);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        prodWorkQuery.setExclProdWorkIdList(exclProdWorkIdList);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);

        //派工数量合计
        BigDecimal sumQty = BigDecimal.ZERO;
        // 按照优先级降序,然后按照创建时间升序
        List<ProdWorkDO> prodWorkSortList = prodWorkList.stream().sorted(Comparator.comparingInt(ProdWorkDO::getPriority).reversed().thenComparing(ProdWorkDO::getCreatedDt)).collect(Collectors.toList());
        for (ProdWorkDO prodWorkDO : prodWorkSortList) {

            // 委外自动跳过
            if (processMethod.equals(DictToEnum.OUTSOURCE.key)) {
                continue;
            }

            //根据派工数量进行拆单处理
            BigDecimal qty = BigDecimal.ZERO;

            if (sumQty.compareTo(finalDispatchQty) >= 0) {
                break;
            }

            FlowProcessDetailDO flowProcessDetailDO = flowProcessDetailMapper.selectOne(processId, prodWorkDO.getProdWorkId(), 1);
            if (flowProcessDetailDO == null) {
                continue;
            }

            //判断计件方式是否相同
            if (!flowProcessDetailDO.getPieceworkMethodDictId().equals(pieceworkMethodDictId)) {
                continue;
            }

            //计划派工数量
            BigDecimal workPlanTotalQty = prodWorkDO.getWorkPlanTotalQty();

            //工艺路线中工序加工方式
            Long flowProcessMethod = flowProcessDetailDO.getProcessMethod();

            //可派数量
            BigDecimal dispatchAbleQty = dispatchAbleQtyMap.get(prodWorkDO.getProdWorkId());

            // 自制
            if (processMethod.equals(DictToEnum.SELF_MADE.key)) {

                //加工方式不同不新增
                if (!flowProcessMethod.equals(processMethod)) {
                    continue;
                }

                if (dispatchAbleQty == null) {
                    //本次派工数量
                    dispatchQty = dispatchQty.subtract(workPlanTotalQty);

                    if (dispatchQty.compareTo(BigDecimal.ZERO) >= 0) {
                        qty = workPlanTotalQty;
                    } else {
                        //取剩余数量
                        qty = dispatchQty.add(workPlanTotalQty);
                    }
                }

                if (dispatchAbleQty != null) {
                    //本次派工数量
                    dispatchQty = dispatchQty.subtract(dispatchAbleQty);

                    if (dispatchQty.compareTo(BigDecimal.ZERO) >= 0) {
                        qty = dispatchAbleQty;
                    } else {
                        //取剩余数量
                        qty = dispatchQty.add(dispatchAbleQty);
                    }
                }
            }

            if (qty.compareTo(BigDecimal.ZERO)<=0){
                continue;
            }

            sumQty = sumQty.add(qty);
            reqVO.setProdOrderId(prodWorkDO.getProdOrderId());
            reqVO.setProdOrderCode(prodWorkDO.getProdOrderCode());
            reqVO.setProdWorkId(prodWorkDO.getProdWorkId());
            reqVO.setProdWorkCode(prodWorkDO.getProdWorkCode());
            reqVO.setPlanStartDate(prodWorkDO.getPlanStartDate());
            reqVO.setPlanEndDate(prodWorkDO.getPlanEndDate());
            reqVO.setWorkPlanTotalQty(prodWorkDO.getWorkPlanTotalQty());
            reqVO.setPriority(prodWorkDO.getPriority());
            reqVO.setDispatchQty(qty);
            reqVO.setDispatchMethod(dispatchMethod);
            reqVO.setFinalProcess(flowProcessDetailDO.getFinalProcess());
            reqVO.setProcessMethod(processMethod);
            reqVO.setPieceworkMethodDictId(pieceworkMethodDictId);
            reqVO.setLogSource(ProduceEnum.LOGSOURCE_DISPATCH_PROCESS.code);

            //调用单个新增的方法
            workProcessDispatchAdd(reqVO);
        }

        // 返回
        return 0L;
    }

    /**
     * 工序派工
     *
     * @param reqVO
     * @return
     */
    private Long processDispatchEdit(DispatchWorkAditReqVO reqVO) {

        Integer dispatchMethod = 1;
        Long materialId = reqVO.getMaterialId();
        Long processId = reqVO.getProcessId();
        Long workCenterId = reqVO.getWorkCenterId();
        BigDecimal finalDispatchQty = reqVO.getDispatchQty();
        BigDecimal dispatchQty = reqVO.getDispatchQty();

        //判断是增加数量还是减少数量
        Boolean add = true;
        if (finalDispatchQty.compareTo(BigDecimal.ZERO) < 0) {
            add = false;
        }

        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询工艺路线下的生产工单
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setMaterialId(materialId);
        prodWorkQuery.setProcessConfig(dispatchMethod);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);

        List<Long> prodWorkIdList = new ArrayList<>();
        for (ProdWorkDO prodWorkDO : prodWorkList) {
            prodWorkIdList.add(prodWorkDO.getProdWorkId());
        }

        //查询 所有 的派工单数据
        DispatchWorkQueryReqVO dispatchWorkAllQuery = new DispatchWorkQueryReqVO();
        dispatchWorkAllQuery.setProdWorkIdList(prodWorkIdList);
        dispatchWorkAllQuery.setMaterialId(materialId);
        dispatchWorkAllQuery.setDispatchMethod(dispatchMethod);
        dispatchWorkAllQuery.setProcessId(processId);
        dispatchWorkAllQuery.setWorkCenterId(workCenterId);
        dispatchWorkAllQuery.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());
        List<DispatchWorkDO> dispatchWorkAllList = dispatchWorkMapper.selectListOld(dispatchWorkAllQuery);
        if (CollUtilX.isNotEmpty(dispatchWorkAllList)) {
            BigDecimal totalPendingQty = dispatchWorkAllList.stream().map(DispatchWorkDO::getPendingQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalPendingQty.compareTo(reqVO.getOldPendingQty()) != 0) {
                throw new BizException("500", "待生产数量已被修改,请重新点击查询按钮!");
            }
        }

        //正数 处理派工单派工数量
        if (add) {

            //查询 待生产|生产中 的派工单数据
            DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
            dispatchWorkQuery.setProdWorkIdList(prodWorkIdList);
            dispatchWorkQuery.setMaterialId(materialId);
            dispatchWorkQuery.setDispatchMethod(dispatchMethod);
            dispatchWorkQuery.setProcessId(processId);
            dispatchWorkQuery.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());
            List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.findAbleDispatchWorkList(dispatchWorkQuery);

            //可派数量
            BigDecimal dispatchAbleQty = BigDecimal.ZERO;

            //取最新的一条派工单数据,是刚好派完或者派了一些
            if (CollUtilX.isNotEmpty(dispatchWorkDOList)) {
                DispatchWorkDO dispatchWorkDO = dispatchWorkDOList.get(0);
                reqVO.setDispatchWorkId(dispatchWorkDO.getDispatchWorkId());
                reqVO.setProcessOperationType(1);

                BigDecimal workPlanTotalQty = dispatchWorkDO.getWorkPlanTotalQty();
                BigDecimal oldDispatchQty = dispatchWorkDO.getDispatchQty();
                dispatchAbleQty = workPlanTotalQty.subtract(oldDispatchQty);

                if (dispatchAbleQty.compareTo(dispatchQty) >= 0) {
                    reqVO.setDispatchQty(dispatchQty);
                    dispatchQty = BigDecimal.ZERO;
                } else {
                    reqVO.setDispatchQty(dispatchAbleQty);
                    dispatchQty = dispatchQty.subtract(dispatchAbleQty);
                }

                //调用单个编辑的方法
                workProcessDispatchEdit(reqVO);
            }

            //新增派工单数据
            if (dispatchQty.compareTo(BigDecimal.ZERO) > 0) {
                reqVO.setDispatchQty(dispatchQty);
                processDispatchSplitAdd(reqVO);
            }
        }

        //负数 处理派工单派工数量
        if (!add) {

            //查询 待生产|生产中 的派工单数据
            DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
            dispatchWorkQuery.setProdWorkIdList(prodWorkIdList);
            dispatchWorkQuery.setMaterialId(materialId);
            dispatchWorkQuery.setDispatchMethod(dispatchMethod);
            dispatchWorkQuery.setProcessId(processId);
            dispatchWorkQuery.setFormStatusList(formStatusList);
            dispatchWorkQuery.setWorkCenterId(workCenterId);
            dispatchWorkQuery.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());
            List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);

            //派工数量合计 负数
            BigDecimal negative = new BigDecimal(-1);

            for (DispatchWorkDO dispatchWorkDO : dispatchWorkDOList) {
                if (dispatchQty.compareTo(BigDecimal.ZERO) >= 0) {
                    break;
                }

                BigDecimal qty = BigDecimal.ZERO;

                //待生产数量
                BigDecimal pendingQty = dispatchWorkDO.getPendingQty();

                if (pendingQty.add(dispatchQty).compareTo(BigDecimal.ZERO) >= 0) {
                    qty = dispatchQty;
                } else {
                    qty = pendingQty.multiply(negative);
                }
                dispatchQty = pendingQty.add(dispatchQty);

                reqVO.setDispatchQty(qty);
                reqVO.setDispatchWorkId(dispatchWorkDO.getDispatchWorkId());
                workProcessDispatchEdit(reqVO);
            }
        }

        // 返回
        return 0L;
    }

    /**
     * 工单派工编辑
     *
     * @param reqVO
     * @return
     */
    @Override
    public Long workProcessDispatchEdit(DispatchWorkAditReqVO reqVO) {

        // 校验存在
        DispatchWorkDO oldDispatchWorkDO = this.dispatchWorkValidateExists(reqVO.getDispatchWorkId());

        if (reqVO.getAutoDispatch()){
            oldDispatchWorkDO.setWorkPlanTotalQty(reqVO.getWorkPlanTotalQty());//生成工单变更更新
        }

        //派工方式
        Integer dispatchMethod = reqVO.getDispatchMethod();

        //计划派工数量
        BigDecimal planDispatchQty = reqVO.getDispatchQty();

        //派工数量
        BigDecimal dispatchQty = oldDispatchWorkDO.getDispatchQty();
        dispatchQty = dispatchQty.add(planDispatchQty);
        oldDispatchWorkDO.setDispatchQty(dispatchQty);

        //物料待生产数量
        BigDecimal pendingQty = oldDispatchWorkDO.getPendingQty();
        pendingQty = pendingQty.add(planDispatchQty);
        oldDispatchWorkDO.setPendingQty(pendingQty);

        oldDispatchWorkDO.setUpdatedBy(null);
        oldDispatchWorkDO.setUpdatedDt(null);

        if (dispatchQty.compareTo(BigDecimal.ZERO) <= 0) {
            //记录工序操作记录,记录修改数量到操作日志
            oldDispatchWorkDO.setDispatchQty(planDispatchQty.abs());
            addProcessOperationLog(oldDispatchWorkDO, 2, reqVO.getLogSource());
            dispatchWorkMapper.deleteById(oldDispatchWorkDO);

            //更新生产工单状态
            ProdWorkDO workCodeDO = new ProdWorkDO();
            workCodeDO.setProdWorkId(oldDispatchWorkDO.getProdWorkId());
            //判断该工单有没有派工任务
            Integer count = prodWorkMapper.selectDispatchWorkCount(oldDispatchWorkDO.getProdWorkId());
            if (count == 0) {
                workCodeDO.setFormStatus(OrderStatusEnum.PENDING.getCode());
            }else {
                workCodeDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
            }
            prodWorkMapper.updateById(workCodeDO);

            return 0L;
        }

        if (pendingQty.compareTo(BigDecimal.ZERO) <= 0) {
            oldDispatchWorkDO.setFormStatus(OrderStatusEnum.FINISHED.getCode());
        }else {
            if (oldDispatchWorkDO.getFormStatus() != OrderStatusEnum.DISPATCHED_CLOSED.getCode() && oldDispatchWorkDO.getReportedQty().compareTo(BigDecimal.ZERO) > 0){
                oldDispatchWorkDO.setFormStatus(OrderStatusEnum.DISPATCHED.getCode());
            }
        }

        //更新
        dispatchWorkMapper.updateById(oldDispatchWorkDO);

        //更新生产工单状态
        ProdWorkDO workCodeDO = new ProdWorkDO();
        workCodeDO.setProdWorkId(oldDispatchWorkDO.getProdWorkId());
        workCodeDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
        prodWorkMapper.updateById(workCodeDO);

        //如果有报工记录则更新报工记录里面的派工数量
        ReportedEditReqBO reportedEditReqBO = new ReportedEditReqBO();
        reportedEditReqBO.setDispatchWorkId(oldDispatchWorkDO.getDispatchWorkId());
        reportedEditReqBO.setDispatchQty(oldDispatchWorkDO.getDispatchQty());
        reportedWorkService.upateReportedWork(reportedEditReqBO);

        //记录工序操作记录,记录修改数量到操作日志
        if (dispatchMethod.equals(0)) {
            oldDispatchWorkDO.setDispatchQty(reqVO.getOperedQty());
        } else {
            if (reqVO.getProcessOperationType().equals(0)) {
                oldDispatchWorkDO.setDispatchQty(planDispatchQty);
            }
        }

        //工单变更单不更新派工数量不需要记录日志
        if (reqVO.getAutoDispatch() && planDispatchQty.compareTo(BigDecimal.ZERO) == 0){
            return oldDispatchWorkDO.getDispatchWorkId();
        }

        //新增操作日志
        addProcessOperationLog(oldDispatchWorkDO, reqVO.getProcessOperationType(), reqVO.getLogSource());

        // 返回
        return oldDispatchWorkDO.getDispatchWorkId();
    }

    /**
     * 新增工序操作记录表
     *
     * @param dispatchWorkDO
     */
    private void addProcessOperationLog(DispatchWorkDO dispatchWorkDO, Integer processOperationType, Integer logSource) {
        ProcessOperationLogAditReqVO operationLogAdd = BeanUtilX.copy(dispatchWorkDO, ProcessOperationLogAditReqVO::new);
        operationLogAdd.setDispatchQty(dispatchWorkDO.getDispatchQty());
        operationLogAdd.setLogSource(logSource);
        operationLogAdd.setProcessOperationType(processOperationType);
        operationLogAdd.setSourceOrderCode(dispatchWorkDO.getDispatchWorkCode());
        operationLogAdd.setSourceOrderId(dispatchWorkDO.getDispatchWorkId());
        processOperationLogService.processOperationLogAdd(operationLogAdd);
    }

    /**
     * 查询工作中心下的派工数量
     *
     * @param reqVO
     * @return
     */
    private BigDecimal getWorkCenterDispatchQty(DispatchWorkAditReqVO reqVO) {
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setMaterialId(reqVO.getMaterialId());
        dispatchWorkQuery.setProdWorkId(reqVO.getProdWorkId());
        dispatchWorkQuery.setProcessId(reqVO.getProcessId());
        dispatchWorkQuery.setWorkCenterId(reqVO.getWorkCenterId());
        dispatchWorkQuery.setDispatchMethod(reqVO.getDispatchMethod());
        BigDecimal workCenterDispatchQty = dispatchWorkMapper.selectWorkCenterDispatchQty(dispatchWorkQuery);
        workCenterDispatchQty = workCenterDispatchQty == null ? BigDecimal.ZERO : workCenterDispatchQty;

        return workCenterDispatchQty;
    }

    @Override
    public void dispatchWorkDel(Long dispatchWorkId) {
        // 校验存在
        DispatchWorkDO dispatchWorkDO = this.dispatchWorkValidateExists(dispatchWorkId);
        if (dispatchWorkDO.getReportedQty().compareTo(BigDecimal.ZERO) > 0) {
            dispatchWorkDO.setDispatchQty(dispatchWorkDO.getReportedQty());
            dispatchWorkDO.setPendingQty(BigDecimal.ZERO);
            dispatchWorkDO.setFormStatus(OrderStatusEnum.FINISHED.getCode());
            dispatchWorkDO.setUpdatedDt(null);

            // 更新
            dispatchWorkMapper.updateById(dispatchWorkDO);
            addProcessOperationLog(dispatchWorkDO, 1, 0);
        } else {
            // 删除
            dispatchWorkMapper.deleteById(dispatchWorkId);
            addProcessOperationLog(dispatchWorkDO, 2, 0);
        }

    }

    private DispatchWorkDO dispatchWorkValidateExists(Long dispatchWorkId) {
        DispatchWorkDO dispatchWork = dispatchWorkMapper.selectById(dispatchWorkId);
        if (dispatchWork == null) {
            // throw exception(DISPATCH_WORK_NOT_EXISTS);
            throw new BizException("5001", "派工单不存在");
        }
        return dispatchWork;
    }

    @Override
    public DispatchWorkRespVO dispatchWorkDetail(Long dispatchWorkId) {
        DispatchWorkDO data = dispatchWorkValidateExists(dispatchWorkId);
        return BeanUtilX.copy(data, DispatchWorkRespVO::new);
    }

    @Override
    public List<DispatchWorkRespVO> dispatchWorkList(DispatchWorkQueryReqVO reqVO) {
        List<DispatchWorkRespVO> respVOList = BeanUtilX.copy(dispatchWorkMapper.selectList(reqVO), DispatchWorkRespVO::new);
        //查询字典库
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.DISPATCH_WORK_STATUS.getDictCode());
        for(DispatchWorkRespVO respVO : respVOList){
            //派工单状态
            if(respVO.getFormStatus() != null){
                respVO.setFormStatusDictName(dictMap.get(respVO.getFormStatus().toString()));
            }
        }
        return respVOList;
    }

    @Override
    public PageResult<DispatchWorkRespVO> dispatchWorkPage(DispatchWorkPageReqVO reqVO) {
        PageResult<DispatchWorkRespVO> pageResult = BeanUtilX.copy(dispatchWorkMapper.selectPage(reqVO), DispatchWorkRespVO::new);
        if(CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }

        //查询字典库
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.DISPATCH_WORK_STATUS.getDictCode());
        for(DispatchWorkRespVO respVO : pageResult.getList()){
            //派工单状态
            if(respVO.getFormStatus() != null){
                respVO.setFormStatusDictName(dictMap.get(respVO.getFormStatus().toString()));
            }
        }

        return pageResult;
    }

    @Override
    public DispatchBaseRespVO findDispatchMaterialList(DispatchWorkQueryReqVO reqVO) {

        //前置条件
        DispatchBaseRespVO respVO = new DispatchBaseRespVO();

        //查询工序
        ProcessDO processDO = getProcess(reqVO);

        //派工方式
        Integer dispatchMethod = 0;

        Long processId = processDO.getProcessId();
        Long prodWorkId = reqVO.getProdWorkId();

        //工序对应的工艺路线
        FlowProcessDetailQueryReqVO flowProcessDetailQuery = new FlowProcessDetailQueryReqVO();
        flowProcessDetailQuery.setProcessId(processId);
        flowProcessDetailQuery.setFlowProcessType(1);
        flowProcessDetailQuery.setFlowProcessId(prodWorkId);
        List<FlowProcessDetailDO> flowProcessDetailDOList = flowProcessDetailMapper.selectListOld(flowProcessDetailQuery);
        if (CollUtilX.isEmpty(flowProcessDetailDOList)) {
            throw new BizException("500", "生产工单工艺路线不存在!");
        }

//        //是否包含委外类型的工序加工方式
//        boolean inclProcessOut = false;

        //查询可派工的生产工单
        List<Long> prodWorkIdList = new ArrayList<>();
        //工序加工方式
        Map<Long, FlowProcessDetailDO> flowProcessDetailMap = new HashMap<>();
        for (FlowProcessDetailDO flowProcessDetail : flowProcessDetailDOList) {
            prodWorkIdList.add(flowProcessDetail.getFlowProcessId());
            flowProcessDetailMap.put(flowProcessDetail.getFlowProcessId(), flowProcessDetail);

//            if (flowProcessDetail.getProcessMethod() != DictToEnum.SELF_MADE.key) {
//                inclProcessOut = true;
//            }
        }
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询工艺路线下的生产工单
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setProdWorkIdList(prodWorkIdList);
        prodWorkQuery.setProcessConfig(dispatchMethod);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        prodWorkQuery.setProdWorkId(prodWorkId);

        //数据权限处理
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Map<String, Integer> dataScopeMap = loginUser.getDataScopeMap();
        Integer dataScope = dataScopeMap.get(loginUser.getCurMenuId());
        if(dataScope != null){
            Integer isAdmin = loginUser.getIsAdmin();
            if (isAdmin.equals(BaseEnum.ADMIN.getKey()) || isAdmin.equals(BaseEnum.SUPER_ADMIN.getKey())) {

            }else if (dataScope == DataScopeEnum.CURRENT_ORG.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurOrgIdList();
                prodWorkQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_ORG_CHILD.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurChildOrgIdList();
                prodWorkQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_USER.getKey()) {
                Long directorId = loginUser.getCurUserId();
                prodWorkQuery.setDirectorId(directorId);
            }
        }

        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);

        if (CollUtilX.isEmpty(prodWorkList)) {
            return respVO;
        }

        //只需要待生产|生产中的 生产工单
        prodWorkIdList = prodWorkList.stream().map(ProdWorkDO::getProdWorkId).collect(Collectors.toList());

        //查询生产工单下每个工序的派工任务
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setProdWorkIdList(prodWorkIdList);
        dispatchWorkQuery.setDispatchMethod(dispatchMethod);
        dispatchWorkQuery.setProcessId(processId);
        dispatchWorkQuery.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());

        List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
        Map<Long, BigDecimal> materialDispatchQtyMap = new HashMap<>();
        for (DispatchWorkDO dispatchWorkDO : dispatchWorkDOList) {
            BigDecimal dispatchQty = materialDispatchQtyMap.get(dispatchWorkDO.getProdWorkId());
            if (dispatchQty != null) {
                dispatchQty = dispatchQty.add(dispatchWorkDO.getDispatchQty());
            } else {
                dispatchQty = dispatchWorkDO.getDispatchQty();
            }
            materialDispatchQtyMap.put(dispatchWorkDO.getProdWorkId(), dispatchQty);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        //工单派工 查询工序需求清单
//        if (inclProcessOut) {

        DispatchProcessOutDemandReqBO demandBO = new DispatchProcessOutDemandReqBO();
        demandBO.setDispatchMethod(dispatchMethod);
        demandBO.setProdWorkIdList(prodWorkIdList);
        demandBO.setProcessId(processId);
        demandBO.setMaterialDispatchQtyMap(materialDispatchQtyMap);
        demandBO.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());

        //工单委外需求清单处理
        DispatchProcessOutDemandRespVO demandRespVO = doDemandRespVO(demandBO);

        //移除可采购数量为0的任务
        Iterator<DispatchProcessOutDemandRespVO> iterator = demandRespVO.getProcessOutDemandList().iterator();
        while (iterator.hasNext()) {
            DispatchProcessOutDemandRespVO outDemandRespVO = iterator.next();
            if (outDemandRespVO.getPurchaseableQty().compareTo(BigDecimal.ZERO) == 0) {
                iterator.remove();
            }
        }
        respVO.setDemandRespVO(demandRespVO);


//        BigDecimal pendingPurchaseTotalQty = BigDecimal.ZERO;
//        DispatchProcessOutDemandRespVO demandRespVO = new DispatchProcessOutDemandRespVO();
//        List<DispatchProcessOutDemandRespVO> processOutDemandList = new ArrayList<>();
//
//        //查询工序委外需求清单
//        ProcessOutDemandQueryReqVO processOutDemandQuery = new ProcessOutDemandQueryReqVO();
//        processOutDemandQuery.setProdWorkIdList(prodWorkIdList);
//        processOutDemandQuery.setProcessId(processId);
//        processOutDemandQuery.setDispatchMethod(dispatchMethod);
//        List<ProcessOutDemandDO> outDemandList = processOutDemandMapper.selectListOld(processOutDemandQuery);
//        for (ProcessOutDemandDO outDemandDO : outDemandList) {
//
//            //计件方式
//            if (outDemandDO.getPieceworkMethodDictId() == null){
//                continue;
//            }
//
//            DispatchProcessOutDemandRespVO outDemandRespVO = BeanUtilX.copy(outDemandDO, DispatchProcessOutDemandRespVO::new);
//            outDemandRespVO.setPieceworkMethodDictName(dictMap.get(outDemandDO.getPieceworkMethodDictId()));
//
//            //可采购数量
//            pendingPurchaseTotalQty = pendingPurchaseTotalQty.add(outDemandDO.getPurchaseableQty());
//            if (outDemandDO.getPurchaseableQty().compareTo(BigDecimal.ZERO) > 0) {
//                processOutDemandList.add(outDemandRespVO);
//            }
//
//            //委外派工数量
//            BigDecimal outDemandQty = materialDispatchQtyMap.get(outDemandDO.getProdWorkId());
//            if (outDemandQty != null) {
//                outDemandQty = outDemandQty.add(outDemandDO.getOutDemandQty());
//            } else {
//                outDemandQty = outDemandDO.getOutDemandQty();
//            }
//            materialDispatchQtyMap.put(outDemandDO.getProdWorkId(), outDemandQty);
//        }
//
//        demandRespVO.setPendingPurchaseTotalQty(pendingPurchaseTotalQty);
//        demandRespVO.setProcessOutDemandList(processOutDemandList);

        //填充工序委外需求清单
//        respVO.setDemandRespVO(demandRespVO);
//        }

        //查询物料
        List<Long> materialIdList = new ArrayList<>();
        for (ProdWorkDO item : prodWorkList) {
            materialIdList.add(item.getMaterialId());
        }

        if (CollUtilX.isEmpty(materialIdList)) {
            return respVO;
        }

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        //派工物料
        List<DispatchMaterialRespVO> materialResps = new ArrayList<>();
        for (ProdWorkDO prodWorkDO : prodWorkList) {
            DispatchMaterialRespVO materialkRespVO = BeanUtilX.copy(prodWorkDO, DispatchMaterialRespVO::new);

            BigDecimal dispatchQty = materialDispatchQtyMap.get(materialkRespVO.getProdWorkId());
            if (dispatchQty != null) {
                BigDecimal dispatchAbleQty = prodWorkDO.getWorkPlanTotalQty().subtract(dispatchQty);
                materialkRespVO.setDispatchAbleQty(dispatchAbleQty);
            } else {
                materialkRespVO.setDispatchAbleQty(prodWorkDO.getWorkPlanTotalQty());
            }

            //填充物料
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(prodWorkDO.getMaterialId());
            if (erpMaterialDO != null) {
                materialkRespVO.setMaterialName(erpMaterialDO.getMaterialName());
            }

            FlowProcessDetailDO flowProcessDetailDO = flowProcessDetailMap.get(prodWorkDO.getProdWorkId());
            if (flowProcessDetailDO != null) {
                materialkRespVO.setProcessMethod(flowProcessDetailDO.getProcessMethod());
                materialkRespVO.setProcessId(flowProcessDetailDO.getProcessId());
                materialkRespVO.setProcessCode(flowProcessDetailDO.getProcessCode());
                materialkRespVO.setProcessName(flowProcessDetailDO.getProcessName());
                materialkRespVO.setFinalProcess(flowProcessDetailDO.getFinalProcess());

                //计件方式
                if (flowProcessDetailDO.getPieceworkMethodDictId()!=null){
                    materialkRespVO.setPieceworkMethodDictId(flowProcessDetailDO.getPieceworkMethodDictId());
                    materialkRespVO.setPieceworkMethodDictName(dictMap.get(flowProcessDetailDO.getPieceworkMethodDictId()));
                }

                //加工方式
                Long processMethod = flowProcessDetailDO.getProcessMethod();
                if (processMethod.equals(DictToEnum.OUTSOURCE.key)) {
                    materialkRespVO.setSelfQty(BigDecimal.ZERO);
                    materialkRespVO.setOutQty(materialkRespVO.getDispatchAbleQty());
                }else {
                    materialkRespVO.setSelfQty(materialkRespVO.getDispatchAbleQty());
                    materialkRespVO.setOutQty(materialkRespVO.getDispatchAbleQty());
                }
            }

            materialResps.add(materialkRespVO);
        }

        //填充派工物料
        respVO.setDispatchMaterialList(materialResps);

        return respVO;
    }

    /**
     * 工单委外需求清单处理
     *
     * @param demandBO
     * @return
     */
    private DispatchProcessOutDemandRespVO doDemandRespVO(DispatchProcessOutDemandReqBO demandBO) {
        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        Map<Long, BigDecimal> materialDispatchQtyMap = Optional.ofNullable(demandBO.getMaterialDispatchQtyMap()).orElseGet(HashMap::new);

        BigDecimal pendingPurchaseTotalQty = BigDecimal.ZERO;
        DispatchProcessOutDemandRespVO demandRespVO = new DispatchProcessOutDemandRespVO();
        List<DispatchProcessOutDemandRespVO> processOutDemandList = new ArrayList<>();

        //查询工序委外需求清单
        ProcessOutDemandQueryReqVO processOutDemandQuery = new ProcessOutDemandQueryReqVO();
        processOutDemandQuery.setProdWorkIdList(demandBO.getProdWorkIdList());
        processOutDemandQuery.setProcessId(demandBO.getProcessId());
        processOutDemandQuery.setProcessIdList(demandBO.getProcessIdList());
        processOutDemandQuery.setDispatchMethod(demandBO.getDispatchMethod());
        processOutDemandQuery.setPieceworkMethodDictId(demandBO.getPieceworkMethodDictId());
        List<ProcessOutDemandDO> outDemandList = processOutDemandMapper.selectListOld(processOutDemandQuery);
        for (ProcessOutDemandDO outDemandDO : outDemandList) {

            //计件方式
            if (outDemandDO.getPieceworkMethodDictId() == null){
                continue;
            }

            //计件方式
            DispatchProcessOutDemandRespVO outDemandRespVO = BeanUtilX.copy(outDemandDO, DispatchProcessOutDemandRespVO::new);
            outDemandRespVO.setPieceworkMethodDictName(dictMap.get(outDemandDO.getPieceworkMethodDictId()));

            //可采购数量
            pendingPurchaseTotalQty = pendingPurchaseTotalQty.add(outDemandDO.getPurchaseableQty());
//            if (outDemandDO.getPurchaseableQty().compareTo(BigDecimal.ZERO) > 0) {
//                processOutDemandList.add(outDemandRespVO);
//            }

            processOutDemandList.add(outDemandRespVO);
            //委外派工数量
            BigDecimal outDemandQty = materialDispatchQtyMap.get(outDemandDO.getProdWorkId());
            if (outDemandQty != null) {
                outDemandQty = outDemandQty.add(outDemandDO.getOutDemandQty());
            } else {
                outDemandQty = outDemandDO.getOutDemandQty();
            }
            materialDispatchQtyMap.put(outDemandDO.getProdWorkId(), outDemandQty);
        }

        demandRespVO.setPendingPurchaseTotalQty(pendingPurchaseTotalQty);
        demandRespVO.setProcessOutDemandList(processOutDemandList);

        return demandRespVO;
    }

    private DispatchProcessOutDemandRespVO demandProcessRespVO(DispatchProcessOutDemandReqBO demandBO) {
        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        Map<String, DispatchMaterialRespVO> materialDispatchQtyMap = Optional.ofNullable(demandBO.getMaterialProcessDisQtyMap()).orElseGet(HashMap::new);
        Map<String, BigDecimal> outDispQtyMap = Optional.ofNullable(demandBO.getOutDispQtyMap()).orElseGet(HashMap::new);
        Map<String, BigDecimal> otherDispQtyMap = Optional.ofNullable(demandBO.getOtherDispQtyMap()).orElseGet(HashMap::new);

        BigDecimal pendingPurchaseTotalQty = BigDecimal.ZERO;
        List<Long> prodWorkIdList = demandBO.getProdWorkIdList();
        Long processId = demandBO.getProcessId();
        String pieceworkMethodDictId = demandBO.getPieceworkMethodDictId();
        ProcessDO processDO = demandBO.getProcessDO();

        DispatchProcessOutDemandRespVO demandRespVO = new DispatchProcessOutDemandRespVO();
        List<DispatchProcessOutDemandRespVO> processOutDemandList = new ArrayList<>();

        //查询工序委外需求清单
        List<ProcessOutDemandDO> outDemandList = processOutDemandMapper.selectProcessOutDemandDetailQty(prodWorkIdList, processId,pieceworkMethodDictId);
        for (ProcessOutDemandDO outDemandDO : outDemandList) {

            if (outDemandDO.getPieceworkMethodDictId() == null){
                continue;
            }

            DispatchProcessOutDemandRespVO outDemandRespVO = BeanUtilX.copy(outDemandDO, DispatchProcessOutDemandRespVO::new);
            //待采购数量
            BigDecimal purchaseableQty = outDemandRespVO.getOutDemandQty().subtract(outDemandRespVO.getPurchasedQty());
            outDemandRespVO.setPurchaseableQty(purchaseableQty);

            //工序
            outDemandRespVO.setProcessId(processDO.getProcessId());
            outDemandRespVO.setProcessCode(processDO.getProcessCode());
            outDemandRespVO.setProcessName(processDO.getProcessName());

            //计件方式
            outDemandRespVO.setPieceworkMethodDictName(dictMap.get(outDemandDO.getPieceworkMethodDictId()));

            //委外清单明细
            if (purchaseableQty.compareTo(BigDecimal.ZERO) > 0) {
                processOutDemandList.add(outDemandRespVO);
            }
            pendingPurchaseTotalQty = pendingPurchaseTotalQty.add(purchaseableQty);

            //委外派工数量
            String key = outDemandDO.getProcessId() + "_" + outDemandDO.getMaterialId() + "_" + outDemandDO.getPieceworkMethodDictId();

            DispatchMaterialRespVO dispatch = materialDispatchQtyMap.get(key);
            if (dispatch != null) {
                BigDecimal outDemandQty = dispatch.getDispatchQty().add(outDemandDO.getOutDemandQty());
                dispatch.setDispatchQty(outDemandQty);
            } else {
                DispatchMaterialRespVO dispatchWork = new DispatchMaterialRespVO();
                dispatchWork.setProcessId(processDO.getProcessId());
                dispatchWork.setProcessCode(processDO.getProcessCode());
                dispatchWork.setProcessName(processDO.getProcessName());
                dispatchWork.setMaterialId(outDemandDO.getMaterialId());
                dispatchWork.setMaterialCode(outDemandDO.getMaterialCode());
                dispatchWork.setMaterialName(outDemandDO.getMaterialName());
                dispatchWork.setFinalProcess(outDemandDO.getFinalProcess());
                dispatchWork.setDispatchQty(outDemandDO.getOutDemandQty());
                dispatchWork.setPieceworkMethodDictId(outDemandDO.getPieceworkMethodDictId());
                materialDispatchQtyMap.put(key, dispatchWork);
            }
        }

        //查询委外需求清单明细
        ProcessOutDemandQueryReqVO processOutDemandQuery = new ProcessOutDemandQueryReqVO();
        processOutDemandQuery.setProdWorkIdList(prodWorkIdList);
        processOutDemandQuery.setProcessId(processId);
        processOutDemandQuery.setPieceworkMethodDictId(pieceworkMethodDictId);
        List<ProcessOutDemandDO> processOutDemands = processOutDemandMapper.selectListOld(processOutDemandQuery);

        for (ProcessOutDemandDO demandDO : processOutDemands) {
            String materialKey = demandDO.getProcessId() + "_" + demandDO.getMaterialId() + "_" + demandDO.getPieceworkMethodDictId();

            //查询生产订单工艺路线,判断工序真正加工方式
            FlowProcessDetailDO processDetailDO = flowProcessDetailMapper.selectOne(demandDO.getProcessId(), demandDO.getProdWorkId(), 1);

            if (processDetailDO!= null && processDetailDO.getProcessMethod().equals(DictToEnum.SELF_MADE.key)){
                updateQtyMap(materialKey,demandDO.getOutDemandQty(),otherDispQtyMap);
            }else {
                updateQtyMap(materialKey,demandDO.getOutDemandQty(),outDispQtyMap);
            }
        }

        demandRespVO.setPendingPurchaseTotalQty(pendingPurchaseTotalQty);
        demandRespVO.setProcessOutDemandList(processOutDemandList);

        return demandRespVO;
    }

    @Override
    public DispatchBaseRespVO findProcessDispatchMaterialList(DispatchWorkQueryReqVO reqVO) {
        DispatchBaseRespVO respVO = new DispatchBaseRespVO();

        //查询工序
//        ProcessDO processDO = getProcess(reqVO);
        List<Long> processIdList = reqVO.getProcessIdList() == null ? new ArrayList<>() : reqVO.getProcessIdList();
        if(reqVO.getProcessId()!= null){
            processIdList.add(reqVO.getProcessId());
        }

        if (CollUtilX.isEmpty(processIdList)){
            return respVO;
        }

        List<ProcessDO> processList = processMapper.selectBatchIds(processIdList);
        if (CollUtilX.isEmpty(processList)) {
            return respVO;
        }
        Map<Long, ProcessDO> processMap = processList.stream().collect(Collectors.toMap(ProcessDO::getProcessId, detail -> detail));

        //派工方式
        Integer dispatchMethod = 1;

        //工序对应的工艺路线
//        Long processId = processDO.getProcessId();
        FlowProcessDetailQueryReqVO flowProcessDetailQuery = new FlowProcessDetailQueryReqVO();
        flowProcessDetailQuery.setProcessId(reqVO.getProcessId());
        flowProcessDetailQuery.setProcessIdList(reqVO.getProcessIdList());
        flowProcessDetailQuery.setFlowProcessType(1);
        List<FlowProcessDetailDO> flowProcessDetailDOList = flowProcessDetailMapper.selectListOld(flowProcessDetailQuery);
        if (CollUtilX.isEmpty(flowProcessDetailDOList)) {
            throw new BizException("500", "工艺路线不存在!");
        }

        //查询可派工的生产工单
        Set<Long> prodWorkIdSet = new HashSet<>();

        //单个工序
        Map<Long, FlowProcessDetailDO> flowProcessMap = new HashMap<>();

        //工序加工方式
        for (FlowProcessDetailDO flowProcessDetail : flowProcessDetailDOList) {
            prodWorkIdSet.add(flowProcessDetail.getFlowProcessId());
            flowProcessMap.put(flowProcessDetail.getFlowProcessId(), flowProcessDetail);
        }

        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        List<Long> prodWorkIdList = prodWorkIdSet.stream().collect(Collectors.toList());

        //查询工艺路线下的生产工单
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setProdWorkIdList(prodWorkIdList);
        prodWorkQuery.setProcessConfig(dispatchMethod);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        prodWorkQuery.setMaterialId(reqVO.getMaterialId());

        //数据权限处理
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Map<String, Integer> dataScopeMap = loginUser.getDataScopeMap();
        Integer dataScope = dataScopeMap.get(loginUser.getCurMenuId());
        if(dataScope != null){
            Integer isAdmin = loginUser.getIsAdmin();
            if (isAdmin.equals(BaseEnum.ADMIN.getKey()) || isAdmin.equals(BaseEnum.SUPER_ADMIN.getKey())) {

            }else if (dataScope == DataScopeEnum.CURRENT_ORG.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurOrgIdList();
                prodWorkQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_ORG_CHILD.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurChildOrgIdList();
                prodWorkQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_USER.getKey()) {
                Long directorId = loginUser.getCurUserId();
                prodWorkQuery.setDirectorId(directorId);
            }
        }

        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);
        if (CollUtilX.isEmpty(prodWorkList)) {
            return respVO;
        }

        //只需要待生产|生产中的 生产工单
        prodWorkIdList = prodWorkList.stream().map(ProdWorkDO::getProdWorkId).collect(Collectors.toList());

        //物料 工单对应关系
        List<Long> materialIdList = new ArrayList<>();

        //记录每个加工方式下的物料计划数量
        Map<String, BigDecimal> outWorkQtyMap = new HashMap<>();
        Map<String, BigDecimal> selfWorkQtyMap = new HashMap<>();

        //工单下的工序
        Map<String, FlowProcessDetailDO> flowProcessDetailMap = new HashMap<>();

        for (ProdWorkDO item : prodWorkList) {
            for (FlowProcessDetailDO flowProcessDetailDO : flowProcessDetailDOList) {
                if (item.getProdWorkId().equals(flowProcessDetailDO.getFlowProcessId())) {
                    String key = flowProcessDetailDO.getProcessId() + "_" + item.getMaterialId() + "_" + flowProcessDetailDO.getPieceworkMethodDictId();
                    flowProcessDetailMap.put(key, flowProcessDetailDO);

                    if (flowProcessDetailDO.getProcessMethod().equals(DictToEnum.OUTSOURCE.key)) {
                        updateQtyMap(key,item.getWorkPlanTotalQty(),outWorkQtyMap);
                    }else {
                        updateQtyMap(key,item.getWorkPlanTotalQty(),selfWorkQtyMap);
                    }
                }
            }
            materialIdList.add(item.getMaterialId());
        }

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        //派工数量map
        Map<String, DispatchMaterialRespVO> materialDispatchQtyMap = new HashMap<>();

        //记录每个加工方式下每个工单派工数量
        Map<String, BigDecimal> outDispQtyMap = new HashMap<>();
        Map<String, BigDecimal> selfDispQtyMap = new HashMap<>();
        Map<String, BigDecimal> otherDispQtyMap = new HashMap<>();

        //查询生产工单下每个工序的派工任务
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setProdWorkIdList(prodWorkIdList);
        dispatchWorkQuery.setDispatchMethod(dispatchMethod);
        dispatchWorkQuery.setProcessId(reqVO.getProcessId());
        dispatchWorkQuery.setProcessIdList(reqVO.getProcessIdList());
        dispatchWorkQuery.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());

        List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);

        for (DispatchWorkDO dispatchWorkDO : dispatchWorkDOList) {

            String key = dispatchWorkDO.getProcessId() + "_" + dispatchWorkDO.getMaterialId() + "_" + dispatchWorkDO.getPieceworkMethodDictId();
            DispatchMaterialRespVO dispatch = materialDispatchQtyMap.get(key);

            if (dispatch != null) {
                BigDecimal dispatchQty = dispatch.getDispatchQty().add(dispatchWorkDO.getDispatchQty());
                dispatch.setDispatchQty(dispatchQty);
            } else {
                DispatchMaterialRespVO dispatchWork = new DispatchMaterialRespVO();
                dispatchWork.setProcessId(dispatchWorkDO.getProcessId());
                dispatchWork.setProcessCode(dispatchWorkDO.getProcessCode());
                dispatchWork.setProcessName(dispatchWorkDO.getProcessName());
                dispatchWork.setMaterialId(dispatchWorkDO.getMaterialId());
                dispatchWork.setMaterialCode(dispatchWorkDO.getMaterialCode());
                dispatchWork.setMaterialName(dispatchWorkDO.getMaterialName());
                dispatchWork.setFinalProcess(dispatchWorkDO.getFinalProcess());
                dispatchWork.setDispatchQty(dispatchWorkDO.getDispatchQty());
                dispatchWork.setPieceworkMethodDictId(dispatchWorkDO.getPieceworkMethodDictId());

                materialDispatchQtyMap.put(key, dispatchWork);
            }

            //自制派工数量
            updateQtyMap(key,dispatchWorkDO.getDispatchQty(),selfDispQtyMap);
        }

        List<DispatchProcessOutDemandRespVO> processOutDemandAllList = new ArrayList<>();
        BigDecimal pendingPurchaseTotalQty = BigDecimal.ZERO;

        for (Long processId : processIdList) {
            ProcessDO processDO = processMap.get(processId);
            DispatchProcessOutDemandReqBO demandBO = new DispatchProcessOutDemandReqBO();
            demandBO.setProdWorkIdList(prodWorkIdList);
            demandBO.setProcessId(processId);
            demandBO.setMaterialProcessDisQtyMap(materialDispatchQtyMap);
            demandBO.setPieceworkMethodDictId(reqVO.getPieceworkMethodDictId());
            demandBO.setProcessDO(processDO);
            demandBO.setOutDispQtyMap(outDispQtyMap);
            demandBO.setOtherDispQtyMap(otherDispQtyMap);

            DispatchProcessOutDemandRespVO demandRespVO = demandProcessRespVO(demandBO);

            //待采购数量汇总
            pendingPurchaseTotalQty = pendingPurchaseTotalQty.add(demandRespVO.getPendingPurchaseTotalQty());
            List<DispatchProcessOutDemandRespVO> processOutDemandList = demandRespVO.getProcessOutDemandList();
            processOutDemandAllList.addAll(processOutDemandList);
        }

        DispatchProcessOutDemandRespVO demandRespVO = new DispatchProcessOutDemandRespVO();
        demandRespVO.setProcessOutDemandList(processOutDemandAllList);
        demandRespVO.setPendingPurchaseTotalQty(pendingPurchaseTotalQty);

        //填充工序委外需求清单
        respVO.setDemandRespVO(demandRespVO);

        //派工任务汇总
        List<DispatchMaterialRespVO> materialResps = new ArrayList<>();

        for (Map.Entry<String, DispatchMaterialRespVO> entry : materialDispatchQtyMap.entrySet()){
            String materialkey = entry.getKey();
            DispatchMaterialRespVO dispatchMaterialRespVO = entry.getValue();

            FlowProcessDetailDO flowProcessDetailDO = flowProcessDetailMap.get(materialkey);

            //工单关闭没有对应的计划生产数量
            if (flowProcessDetailDO == null){
                continue;
            }

            BigDecimal workPlanTotalQty = selfWorkQtyMap.getOrDefault(materialkey,BigDecimal.ZERO).add(outWorkQtyMap.getOrDefault(materialkey,BigDecimal.ZERO));
            dispatchMaterialRespVO.setWorkPlanTotalQty(workPlanTotalQty);
            dispatchMaterialRespVO.setDispatchAbleQty(workPlanTotalQty.subtract(dispatchMaterialRespVO.getDispatchQty()));

            //计件方式
            if (flowProcessDetailDO.getPieceworkMethodDictId()!= null){
                dispatchMaterialRespVO.setPieceworkMethodDictName(dictMap.get(flowProcessDetailMap.get(materialkey).getPieceworkMethodDictId()));
            }

            BigDecimal selfWork = selfWorkQtyMap.getOrDefault(materialkey, BigDecimal.ZERO);
            BigDecimal outWork = outWorkQtyMap.getOrDefault(materialkey, BigDecimal.ZERO);

            BigDecimal selfDisp = selfDispQtyMap.getOrDefault(materialkey, BigDecimal.ZERO);
            BigDecimal outDisp = outDispQtyMap.getOrDefault(materialkey, BigDecimal.ZERO);
            BigDecimal otherQty = otherDispQtyMap.getOrDefault(materialkey, BigDecimal.ZERO);

            BigDecimal selfQty = selfWork.subtract(selfDisp).subtract(otherQty);
            BigDecimal outQty = outWork.subtract(outDisp).add(selfQty);

            dispatchMaterialRespVO.setSelfQty(selfQty);
            dispatchMaterialRespVO.setOutQty(outQty);

            materialResps.add(dispatchMaterialRespVO);
        }

        if (reqVO.getProcessId()!= null){
            //相同物料相同加工方式工单计划生产数量汇总
            Map<String, DispatchMaterialRespVO> materialDispatchMap = new HashMap<>();

            materialResps = new ArrayList<>();

            for (ProdWorkDO prodWorkDO : prodWorkList) {

                FlowProcessDetailDO flowProcessDetailDO = flowProcessMap.get(prodWorkDO.getProdWorkId());
                if (flowProcessDetailDO == null || flowProcessDetailDO.getPieceworkMethodDictId() == null){
                    continue;
                }

                String key = flowProcessDetailDO.getProcessId() + "_" + prodWorkDO.getMaterialId() + "_" + flowProcessDetailDO.getPieceworkMethodDictId();

                DispatchMaterialRespVO dispatchMaterial = materialDispatchMap.get(key);
                if (dispatchMaterial == null) {
                    DispatchMaterialRespVO materialRespVO = new DispatchMaterialRespVO();
                    materialRespVO.setMaterialId(prodWorkDO.getMaterialId());
                    materialRespVO.setMaterialCode(prodWorkDO.getMaterialCode());
                    materialRespVO.setWorkPlanTotalQty(prodWorkDO.getWorkPlanTotalQty());

                    //计件方式
                    materialRespVO.setPieceworkMethodDictId(flowProcessDetailDO.getPieceworkMethodDictId());
                    materialRespVO.setPieceworkMethodDictName(dictMap.get(flowProcessDetailDO.getPieceworkMethodDictId()));
                    //设置工序
                    materialRespVO.setProcessId(flowProcessDetailDO.getProcessId());
                    materialRespVO.setProcessCode(flowProcessDetailDO.getProcessCode());
                    materialRespVO.setProcessName(flowProcessDetailDO.getProcessName());
                    materialRespVO.setFinalProcess(flowProcessDetailDO.getFinalProcess());

                    //填充物料基本信息
                    ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(prodWorkDO.getMaterialId());
                    if (erpMaterialDO != null) {
                        materialRespVO.setMaterialName(erpMaterialDO.getMaterialName());
                    }
                    materialDispatchMap.put(key, materialRespVO);

                } else {
                    //生产工单计划数量
                    BigDecimal workPlanTotalQty = dispatchMaterial.getWorkPlanTotalQty();
                    workPlanTotalQty = workPlanTotalQty.add(prodWorkDO.getWorkPlanTotalQty());
                    dispatchMaterial.setWorkPlanTotalQty(workPlanTotalQty);

                    materialDispatchMap.put(key, dispatchMaterial);
                }
            }

            //待派工数量计算
            for (Map.Entry<String, DispatchMaterialRespVO> entry : materialDispatchMap.entrySet()) {
                String key = entry.getKey();
                DispatchMaterialRespVO dispatchMaterial = entry.getValue();

                //派工任务
                DispatchMaterialRespVO materialResp = materialDispatchQtyMap.get(key);

                if (materialResp != null) {
                    dispatchMaterial.setDispatchAbleQty(materialResp.getDispatchAbleQty());
                    dispatchMaterial.setSelfQty(materialResp.getSelfQty());
                    dispatchMaterial.setOutQty(materialResp.getOutQty());
                } else {
                    dispatchMaterial.setDispatchAbleQty(dispatchMaterial.getWorkPlanTotalQty());

                    BigDecimal selfWork = selfWorkQtyMap.getOrDefault(key, BigDecimal.ZERO);
                    BigDecimal outWork = outWorkQtyMap.getOrDefault(key, BigDecimal.ZERO);

                    dispatchMaterial.setSelfQty(selfWork);
                    dispatchMaterial.setOutQty(selfWork.add(outWork));
                }
                materialResps.add(dispatchMaterial);
            }

            //工序列表查询返回
            respVO.setDispatchMaterialList(materialResps);
        }else {

            //视图查询返回
            respVO.setDispatchMaterialList(materialResps);
        }

        return respVO;
    }

    /**
     * 更新map数量
     *
     * @param key
     * @param curQty
     * @param qtyMap
     */
    private void updateQtyMap(String key, BigDecimal curQty,Map<String, BigDecimal> qtyMap) {
        BigDecimal oldQty = qtyMap.get(key);

        if (oldQty != null){
            oldQty = oldQty.add(curQty);
            qtyMap.put(key, oldQty);
        }else {
            qtyMap.put(key, curQty);
        }
    }

    @Override
    public List<DispatchWorkCenterRespVO> findDispatchWorkCenterList(DispatchWorkQueryReqVO reqVO) {

        //查询工序
//        ProcessDO processDO = getProcess(reqVO);

        ProcessQueryReqVO processQuery = new ProcessQueryReqVO();
        processQuery.setProcessCode(reqVO.getProcessCode());
        processQuery.setProcessName(reqVO.getProcessName());
        processQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        processQuery.setProcessId(reqVO.getProcessId());
        processQuery.setProcessIdList(reqVO.getProcessIdList());
        List<ProcessDO> processList = processMapper.selectListOld(processQuery);
        if (CollUtilX.isEmpty(processList)) {
            throw new BizException("500", "工序不存在!");
        }

        //派工方式
        Integer dispatchMethod = 0;

        //是否匹配
        Boolean match = false;

        //查询工序对应的工作中心列表
        List<Long> workCenterIdList = new ArrayList<>();

        for (ProcessDO processDO : processList) {
//            List<JSONObject> workCenterInfo = processDO.getWorkCenterInfo();
            List<WorkCenterRelDO> workCenterRelList = workCenterRelMapper.selectList(processDO.getProcessId());

            for (WorkCenterRelDO workCenter : workCenterRelList) {
//                Long workCenterId = workCenter.getLong("workCenterId");
                Long workCenterId = workCenter.getWorkCenterId();
                if (reqVO.getWorkCenterId() != null) {
                    if (workCenterId.equals(reqVO.getWorkCenterId())) {
                        match = true;
                        workCenterIdList.add(workCenterId);
                        break;
                    }
                } else {
                    match = true;
                    workCenterIdList.add(workCenterId);
                }
            }
        }

        if (match.equals(false) || CollUtilX.isEmpty(workCenterIdList)) {
            return new ArrayList<>();
        }

        //查询工序关联的工作中心信息
        WorkCenterQueryReqVO workCenterQuery = new WorkCenterQueryReqVO();
        workCenterQuery.setWorkCenterIdList(workCenterIdList);
        workCenterQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        workCenterQuery.setWorkCenterCode(reqVO.getWorkCenterCode());

        //数据权限处理
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Map<String, Integer> dataScopeMap = loginUser.getDataScopeMap();
        Integer dataScope = dataScopeMap.get(loginUser.getCurMenuId());
        if(dataScope != null){
            Integer isAdmin = loginUser.getIsAdmin();
            if (isAdmin.equals(BaseEnum.ADMIN.getKey()) || isAdmin.equals(BaseEnum.SUPER_ADMIN.getKey())) {

            }else if (dataScope == DataScopeEnum.CURRENT_ORG.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurOrgIdList();
                workCenterQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_ORG_CHILD.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurChildOrgIdList();
                workCenterQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_USER.getKey()) {
                Long directorId = loginUser.getCurUserId();
                workCenterQuery.setDirectorId(directorId);
            }
        }

        List<WorkCenterDO> workCenterDOList = workCenterMapper.selectListOld(workCenterQuery);
        List<DispatchWorkCenterRespVO> workCenterResps = BeanUtilX.copy(workCenterDOList, DispatchWorkCenterRespVO::new);
        if (CollUtilX.isEmpty(workCenterResps)) {
            return workCenterResps;
        }

        //查询工作中心下的派工物料
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());
        formStatusList.add(OrderStatusEnum.DISPATCHED.getCode());

        DispatchWorkQueryReqVO dispatchMaterialQuery = new DispatchWorkQueryReqVO();
        dispatchMaterialQuery.setProcessId(reqVO.getProcessId());
        dispatchMaterialQuery.setProcessIdList(reqVO.getProcessIdList());
        dispatchMaterialQuery.setWorkCenterIdList(workCenterIdList);
        dispatchMaterialQuery.setDispatchMethod(dispatchMethod);
        dispatchMaterialQuery.setFormStatusList(formStatusList);
        List<DispatchWorkDO> dispatchWorkList = dispatchWorkMapper.selectList(dispatchMaterialQuery);
        if (CollUtilX.isEmpty(dispatchWorkList)) {
            return workCenterResps;
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        //待生产数量
        Map<Long, BigDecimal> dispatchQtyMap = new HashMap<>();
        Map<Long, List<DispatchWorkRespVO>> dispatchMaterialMap = new HashMap<>();
        for (DispatchWorkDO dispatchWorkDO : dispatchWorkList) {

            DispatchWorkRespVO dispatchWorkResp = BeanUtilX.copy(dispatchWorkDO, DispatchWorkRespVO::new);

            dispatchWorkResp.setPieceworkMethodDictName(dictMap.get(dispatchWorkResp.getPieceworkMethodDictId()));

            BigDecimal pendingQty = dispatchQtyMap.get(dispatchWorkDO.getWorkCenterId());
            List<DispatchWorkRespVO> dispatchMaterialList = dispatchMaterialMap.get(dispatchWorkDO.getWorkCenterId());

            if (pendingQty != null) {
                pendingQty = pendingQty.add(dispatchWorkDO.getPendingQty());
                dispatchMaterialList.add(dispatchWorkResp);
            } else {
                dispatchMaterialList = new ArrayList<>();
                pendingQty = dispatchWorkDO.getPendingQty();
                dispatchMaterialList.add(dispatchWorkResp);
            }

            dispatchQtyMap.put(dispatchWorkDO.getWorkCenterId(), pendingQty);
            dispatchMaterialMap.put(dispatchWorkDO.getWorkCenterId(), dispatchMaterialList);

        }

        for (DispatchWorkCenterRespVO workCenterResp : workCenterResps) {
            BigDecimal pendingQty = dispatchQtyMap.get(workCenterResp.getWorkCenterId());
            List<DispatchWorkRespVO> dispatchMaterialList = dispatchMaterialMap.get(workCenterResp.getWorkCenterId());
            workCenterResp.setWorkCenterPendingQty(pendingQty);

            if (CollUtilX.isEmpty(dispatchMaterialList)) {
                continue;
            }

            workCenterResp.setDetailList(dispatchMaterialList);
        }

        return workCenterResps;
    }

    @Override
    public List<DispatchWorkCenterRespVO> findProcessDispatchWorkCenterList(DispatchWorkQueryReqVO reqVO) {
        //查询工序
//        ProcessDO processDO = getProcess(reqVO);

        List<Long> processIdList = reqVO.getProcessIdList() == null ? new ArrayList<>() : reqVO.getProcessIdList();
        if(reqVO.getProcessId()!= null){
            processIdList.add(reqVO.getProcessId());
        }

        //派工方式
        Integer dispatchMethod = 1;

        //是否匹配
        Boolean match = false;

        //查询工序对应的工作中心列表
        List<Long> workCenterIdList = new ArrayList<>();
//        List<JSONObject> workCenterInfo = processDO.getWorkCenterInfo();

        WorkCenterRelQueryReqVO workCenterRelQuery = new WorkCenterRelQueryReqVO();
        workCenterRelQuery.setRelatedOrderIdList(processIdList);
        List<WorkCenterRelDO> workCenterRelList = workCenterRelMapper.selectList(workCenterRelQuery);
        for (WorkCenterRelDO workCenter : workCenterRelList) {
//            Long workCenterId = workCenter.getLong("workCenterId");
            Long workCenterId = workCenter.getWorkCenterId();
            if (reqVO.getWorkCenterId() != null) {
                if (workCenterId.equals(reqVO.getWorkCenterId())) {
                    match = true;
                    workCenterIdList.add(workCenterId);
                    break;
                }
            } else {
                match = true;
                workCenterIdList.add(workCenterId);
            }
        }

        if (match.equals(false) || CollUtilX.isEmpty(workCenterIdList)) {
            return new ArrayList<>();
        }

        //查询工序关联的工作中心列表
        WorkCenterQueryReqVO workCenterQuery = new WorkCenterQueryReqVO();
        workCenterQuery.setWorkCenterIdList(workCenterIdList);
        workCenterQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        workCenterQuery.setWorkCenterCode(reqVO.getWorkCenterCode());

        //数据权限处理
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Map<String, Integer> dataScopeMap = loginUser.getDataScopeMap();
        Integer dataScope = dataScopeMap.get(loginUser.getCurMenuId());
        if(dataScope != null){
            Integer isAdmin = loginUser.getIsAdmin();
            if (isAdmin.equals(BaseEnum.ADMIN.getKey()) || isAdmin.equals(BaseEnum.SUPER_ADMIN.getKey())) {

            }else if (dataScope == DataScopeEnum.CURRENT_ORG.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurOrgIdList();
                workCenterQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_ORG_CHILD.getKey()) {
                List<String> directorOrgIdList = (List<String>) loginUser.getCurChildOrgIdList();
                workCenterQuery.setDirectorOrgIdList(directorOrgIdList);
            } else if (dataScope == DataScopeEnum.CURRENT_USER.getKey()) {
                Long directorId = loginUser.getCurUserId();
                workCenterQuery.setDirectorId(directorId);
            }
        }

        List<WorkCenterDO> workCenterDOList = workCenterMapper.selectListOld(workCenterQuery);
        List<DispatchWorkCenterRespVO> workCenterResps = BeanUtilX.copy(workCenterDOList, DispatchWorkCenterRespVO::new);
        if (CollUtilX.isEmpty(workCenterResps)) {
            return workCenterResps;
        }

        //查询工作中心下的派工物料
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());
        formStatusList.add(OrderStatusEnum.DISPATCHED.getCode());

        DispatchWorkQueryReqVO dispatchMaterialQuery = new DispatchWorkQueryReqVO();
//        dispatchMaterialQuery.setProcessId(processDO.getProcessId());
        dispatchMaterialQuery.setProcessIdList(processIdList);
        dispatchMaterialQuery.setWorkCenterIdList(workCenterIdList);
        dispatchMaterialQuery.setDispatchMethod(dispatchMethod);
        dispatchMaterialQuery.setFormStatusList(formStatusList);
        List<DispatchWorkDO> dispatchWorkList = dispatchWorkMapper.selectList(dispatchMaterialQuery);
        if (CollUtilX.isEmpty(dispatchWorkList)) {
            return workCenterResps;
        }

        //查询物料
        List<Long> materialIdList = new ArrayList<>();
        for (DispatchWorkDO item : dispatchWorkList) {
            materialIdList.add(item.getMaterialId());
        }

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        //相同物料待生产数量汇总
        Map<Long, DispatchWorkDO> workCenterMap = new HashMap<>();
        Map<String, DispatchWorkRespVO> workCenterMaterialMap = new HashMap<>();

        //待生产数量处理
        for (DispatchWorkDO dispatchWorkDO : dispatchWorkList) {
            DispatchWorkRespVO dispatchWorkResp = BeanUtilX.copy(dispatchWorkDO, DispatchWorkRespVO::new);

            dispatchWorkResp.setPieceworkMethodDictName(dictMap.get(dispatchWorkResp.getPieceworkMethodDictId()));

            //统计相同工作中心的待生产数量
            DispatchWorkDO dispatchWork = workCenterMap.get(dispatchWorkResp.getWorkCenterId());
            if (dispatchWork == null) {
                //填充物料
                ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(dispatchWorkResp.getMaterialId());
                if (erpMaterialDO != null) {
                    dispatchWorkResp.setMaterialName(erpMaterialDO.getMaterialName());
                }
                DispatchWorkDO dispatchWorkCenter = new DispatchWorkDO();
                dispatchWorkCenter.setWorkCenterId(dispatchWorkResp.getWorkCenterId());
                dispatchWorkCenter.setWorkCenterCode(dispatchWorkResp.getWorkCenterCode());
                dispatchWorkCenter.setWorkCenterName(dispatchWorkResp.getWorkCenterName());
                dispatchWorkCenter.setPendingQty(dispatchWorkResp.getPendingQty());
                workCenterMap.put(dispatchWorkResp.getWorkCenterId(), dispatchWorkCenter);
            } else {
                BigDecimal pendingQty = dispatchWork.getPendingQty();
                pendingQty = pendingQty.add(dispatchWorkResp.getPendingQty());
                dispatchWork.setPendingQty(pendingQty);

                workCenterMap.put(dispatchWorkResp.getWorkCenterId(), dispatchWork);
            }

            //统计相同物料的待生产数量
            String workCenterMaterialKey = dispatchWorkResp.getProcessId() + "_" +dispatchWorkResp.getWorkCenterId() + "_" + dispatchWorkResp.getMaterialId()+ "_" + dispatchWorkResp.getPieceworkMethodDictId();
            DispatchWorkRespVO workCenterMaterial = workCenterMaterialMap.get(workCenterMaterialKey);
            if (workCenterMaterial == null) {
                DispatchWorkRespVO material = new DispatchWorkRespVO();
                material.setMaterialId(dispatchWorkResp.getMaterialId());
                material.setMaterialCode(dispatchWorkResp.getMaterialCode());
                material.setMaterialName(dispatchWorkResp.getMaterialName());
                material.setPendingQty(dispatchWorkResp.getPendingQty());
                material.setProcessMethod(dispatchWorkResp.getProcessMethod());
                material.setProcessId(dispatchWorkResp.getProcessId());
                material.setProcessCode(dispatchWorkResp.getProcessCode());
                material.setProcessName(dispatchWorkResp.getProcessName());
                material.setPieceworkMethodDictId(dispatchWorkResp.getPieceworkMethodDictId());
                material.setPieceworkMethodDictName(dictMap.get(material.getPieceworkMethodDictId()));
                material.setFinalProcess(dispatchWorkResp.getFinalProcess());
                material.setDispatchQty(dispatchWorkResp.getDispatchQty());

                workCenterMaterialMap.put(workCenterMaterialKey, material);
            } else {
                BigDecimal pendingQty = workCenterMaterial.getPendingQty();
                pendingQty = pendingQty.add(dispatchWorkResp.getPendingQty());

                BigDecimal dispatchQty = workCenterMaterial.getDispatchQty();
                dispatchQty = dispatchQty.add(dispatchWorkResp.getDispatchQty());

                workCenterMaterial.setPendingQty(pendingQty);
                workCenterMaterial.setDispatchQty(dispatchQty);
            }
        }

        for (DispatchWorkCenterRespVO workCenterResp : workCenterResps) {
            DispatchWorkDO dispatchWorkDO = workCenterMap.get(workCenterResp.getWorkCenterId());
            if (dispatchWorkDO == null) {
                continue;
            }
            List<DispatchWorkRespVO> dispatchWorkDOList = new ArrayList<>();
            for (Map.Entry<String, DispatchWorkRespVO> entry : workCenterMaterialMap.entrySet()) {
                String key = entry.getKey();
                DispatchWorkRespVO workDO = entry.getValue();
                if (key.contains(workCenterResp.getWorkCenterId().toString())) {
                    dispatchWorkDOList.add(workDO);
                }
            }

            workCenterResp.setWorkCenterPendingQty(dispatchWorkDO.getPendingQty());
            workCenterResp.setDetailList(dispatchWorkDOList);
        }

        return workCenterResps;
    }

    @Override
    public List<DispatchWorkRespVO> dispatchWorkListByPW(DispatchWorkQueryReqVO reqVO) {

        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询 待生产|生产中 的派工单数据
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setWorkCenterId(reqVO.getWorkCenterId());
        dispatchWorkQuery.setProcessId(reqVO.getProcessId());
        dispatchWorkQuery.setDispatchMethod(0);
        dispatchWorkQuery.setFormStatusList(formStatusList);
        dispatchWorkQuery.setZero(BigDecimal.ZERO);
        dispatchWorkQuery.setProdWorkId(reqVO.getProdWorkId());
        List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
        if (CollUtilX.isEmpty(dispatchWorkDOList)) {
            return new ArrayList<>();
        }

        List<DispatchWorkRespVO> dispatchWorkRespVOS = new ArrayList<>();

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        List<Long> prodWorkIds = new ArrayList<>();
        //查询生产工单判断是否自动入库
        for (DispatchWorkDO dispatchWorkDO : dispatchWorkDOList){
            DispatchWorkRespVO dispatchWorkResp = BeanUtilX.copy(dispatchWorkDO, DispatchWorkRespVO::new);
            dispatchWorkRespVOS.add(dispatchWorkResp);
            prodWorkIds.add(dispatchWorkDO.getProdWorkId());
        }

        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setProdWorkIdList(prodWorkIds);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectList(prodWorkQuery);
        Map<Long, ProdWorkDO> prodWorkDOMap = prodWorkList.stream().collect(Collectors.toMap(ProdWorkDO::getProdWorkId, detail -> detail));

        //查询该工序中工艺路线的良品报工规则设置
        for (DispatchWorkRespVO dispatchWorkRespVO : dispatchWorkRespVOS) {
            FlowProcessDetailDO flowProcessDetailDO = flowProcessDetailMapper.selectOne(dispatchWorkRespVO.getProcessId(), dispatchWorkRespVO.getProdWorkId(), 1);
            if (flowProcessDetailDO == null) {
                throw new BizException("500", "生产工单工艺路线不存在!");
            }
//            List<JSONObject> goodProductPriceRuleInfo = flowProcessDetailDO.getGoodProductPriceRuleInfo();
//            dispatchWorkRespVO.setGoodProductPriceRuleInfo(goodProductPriceRuleInfo);
            dispatchWorkRespVO.setIsMolded(flowProcessDetailDO.getIsMolded());

            //计件方式
            if (flowProcessDetailDO.getPieceworkMethodDictId()!=null){
                dispatchWorkRespVO.setPieceworkMethodDictName(dictMap.get(dispatchWorkRespVO.getPieceworkMethodDictId()));
            }

            ProdWorkDO prodWorkDO = prodWorkDOMap.get(dispatchWorkRespVO.getProdWorkId());
            dispatchWorkRespVO.setIsAutoInbound(prodWorkDO.getIsAutoInbound());
        }

        return dispatchWorkRespVOS;
    }

    @Override
    public List<DispatchMaterialRespVO> dispatchWorkListByProcess(DispatchWorkQueryReqVO reqVO) {
        List<DispatchMaterialRespVO> result = new ArrayList<>();
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询 待生产|生产中 的派工单数据
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setWorkCenterId(reqVO.getWorkCenterId());
        dispatchWorkQuery.setProcessId(reqVO.getProcessId());
        dispatchWorkQuery.setDispatchMethod(1);
        dispatchWorkQuery.setFormStatusList(formStatusList);
        dispatchWorkQuery.setZero(BigDecimal.ZERO);
        dispatchWorkQuery.setMaterialId(reqVO.getMaterialId());
        dispatchWorkQuery.setProdWorkId(reqVO.getProdWorkId());
        List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
        if (CollUtilX.isEmpty(dispatchWorkDOList)) {
            return new ArrayList<>();
        }

        //相同物料待生产数量汇总
        Map<String, DispatchMaterialRespVO> materialDispatchMap = new HashMap<>();
        Map<String, Set<String>> priceRuleMap = new HashMap<>();

        List<Long> prodWorkIds = new ArrayList<>();

        for (DispatchWorkDO dispatchWork : dispatchWorkDOList) {
            prodWorkIds.add(dispatchWork.getProdWorkId());

            String processAndMaterialKey = dispatchWork.getMaterialId() + "_" + dispatchWork.getProcessId()+ "_" + dispatchWork.getPieceworkMethodDictId();

            DispatchMaterialRespVO dispatchMaterial = materialDispatchMap.get(processAndMaterialKey);
            if (dispatchMaterial == null) {
                DispatchMaterialRespVO materialRespVO = new DispatchMaterialRespVO();
                materialRespVO.setMaterialId(dispatchWork.getMaterialId());
                materialRespVO.setMaterialCode(dispatchWork.getMaterialCode());
                materialRespVO.setMaterialName(dispatchWork.getMaterialName());
                materialRespVO.setPendingQty(dispatchWork.getPendingQty());
                materialRespVO.setPieceworkMethodDictId(dispatchWork.getPieceworkMethodDictId());

                materialRespVO.setProcessId(dispatchWork.getProcessId());
                materialRespVO.setProcessCode(dispatchWork.getProcessCode());
                materialRespVO.setProcessName(dispatchWork.getProcessName());
                materialRespVO.setFinalProcess(dispatchWork.getFinalProcess());
                materialRespVO.setIsAutoInbound(0);

                materialDispatchMap.put(processAndMaterialKey, materialRespVO);
            } else {
                BigDecimal pendingQty = dispatchMaterial.getPendingQty();
                pendingQty = pendingQty.add(dispatchWork.getPendingQty());
                dispatchMaterial.setPendingQty(pendingQty);

                materialDispatchMap.put(processAndMaterialKey, dispatchMaterial);
            }

            //查询该工序中工艺路线的良品报工规则设置
            FlowProcessDetailDO flowProcessDetailDO = flowProcessDetailMapper.selectOne(dispatchWork.getProcessId(), dispatchWork.getProdWorkId(), 1);
            if (flowProcessDetailDO == null) {
                throw new BizException("500", "生产工单工艺路线不存在!");
            }
//            List<JSONObject> goodProductPriceRuleInfo = flowProcessDetailDO.getGoodProductPriceRuleInfo();
//            if (CollUtilX.isEmpty(goodProductPriceRuleInfo)) {
//                continue;
//            }

            //合并计价规则
//            Set<String> ruleList = priceRuleMap.get(processAndMaterialKey);
//            for (JSONObject priceRule : goodProductPriceRuleInfo) {
//                if (CollUtilX.isEmpty(ruleList)) {
//                    ruleList = new HashSet<>();
//                    ruleList.add(priceRule.getString("ruleName"));
//                    priceRuleMap.put(processAndMaterialKey, ruleList);
//                } else {
//                    ruleList.add(priceRule.getString("ruleName"));
//                }
//            }
        }

        //查询生产工单判断是否自动入库
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setProdWorkIdList(prodWorkIds);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectList(prodWorkQuery);

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        for (Map.Entry<String, DispatchMaterialRespVO> entry : materialDispatchMap.entrySet()) {
            String processAndMaterialKey = entry.getKey();
            DispatchMaterialRespVO materialRespVO = entry.getValue();

//            List<JSONObject> goodProductPriceRuleInfo = new ArrayList<>();
//            Set<String> ruleList = priceRuleMap.get(processAndMaterialKey);

//            if (CollUtilX.isNotEmpty(ruleList)) {
//                for (String rule : ruleList) {
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("ruleName", rule);
//                    goodProductPriceRuleInfo.add(jsonObject);
//                }
//            }

            for (ProdWorkDO prodWorkDO : prodWorkList){
                if (processAndMaterialKey.contains(prodWorkDO.getMaterialId().toString()) && prodWorkDO.getIsAutoInbound().equals(1)){
                    materialRespVO.setIsAutoInbound(1);
                }
            }

            //计件方式
            if (materialRespVO.getPieceworkMethodDictId()!=null){
                materialRespVO.setPieceworkMethodDictId(materialRespVO.getPieceworkMethodDictId());
                materialRespVO.setPieceworkMethodDictName(dictMap.get(materialRespVO.getPieceworkMethodDictId()));
            }

//            materialRespVO.setGoodProductPriceRuleInfo(goodProductPriceRuleInfo);
            result.add(materialRespVO);
        }

        return result;
    }

    @Override
    public List<DispatchMaterialRespVO> findMaterialListByProcessOut(DispatchWorkQueryReqVO reqVO) {
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(ProduceEnum.PENDING_PURCHASE.getCode());
        formStatusList.add(ProduceEnum.IN_PURCHASING.getCode());

        //查询 待生产|生产中 的工序委外单数据
        ProcessOutDemandQueryReqVO dispatchWorkQuery = new ProcessOutDemandQueryReqVO();
        dispatchWorkQuery.setProcessId(reqVO.getProcessId());
        dispatchWorkQuery.setProcessCode(reqVO.getProcessCode());
        dispatchWorkQuery.setProcessName(reqVO.getProcessName());
        dispatchWorkQuery.setFormStatusList(formStatusList);
        dispatchWorkQuery.setProcessOutDemandId(reqVO.getProcessOutDemandId());
        dispatchWorkQuery.setProcessOutDemandCode(reqVO.getProcessOutDemandCode());
        dispatchWorkQuery.setProdWorkCode(reqVO.getProdWorkCode());
        dispatchWorkQuery.setProdOrderCode(reqVO.getProdOrderCode());
        List<ProcessOutDemandRespVO> processOutDemandResps = processOutDemandMapper.findReportAbleProcessOutDemandList(dispatchWorkQuery);
        if (CollUtilX.isEmpty(processOutDemandResps)) {
            return new ArrayList<>();
        }

        return BeanUtilX.copy(processOutDemandResps, DispatchMaterialRespVO::new);
    }

    @Override
    public List<DispatchMaterialRespVO> findCheckMaterialList(DispatchWorkQueryReqVO reqVO) {

        List<DispatchMaterialRespVO> dispatchWorkResps = new ArrayList<>();

        Long processId = reqVO.getProcessId();
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        if (processId == null) {
            throw new BizException("500", "工序不能为空!");
        }

        FlowProcessDetailQueryReqVO flowProcessDetailQuery = new FlowProcessDetailQueryReqVO();
        flowProcessDetailQuery.setFlowProcessType(1);
        flowProcessDetailQuery.setProcessId(processId);
        List<FlowProcessDetailDO> flowProcessDetailList = flowProcessDetailMapper.selectListOld(flowProcessDetailQuery);

        if (CollUtilX.isEmpty(flowProcessDetailList)) {
            return dispatchWorkResps;
        }

        //查询待生产||生产中的生产工单
        List<Long> prodWorkIdList = flowProcessDetailList.stream().map(FlowProcessDetailDO::getFlowProcessId).collect(Collectors.toList());

        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setFormStatusList(formStatusList);
        prodWorkQuery.setProdWorkIdList(prodWorkIdList);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        List<ProdWorkDO> prodWorkDOList = prodWorkMapper.selectListOld(prodWorkQuery);

        List<Long> flowProcessIdList = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        for (ProdWorkDO prodWorkDO : prodWorkDOList) {
            flowProcessIdList.add(prodWorkDO.getProdWorkId());
            materialIdList.add(prodWorkDO.getMaterialId());
        }

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        //查询生产工单工艺路线信息
        FlowProcessDetailQueryReqVO flowProcessQuery = new FlowProcessDetailQueryReqVO();
        flowProcessQuery.setFlowProcessIdList(flowProcessIdList);
        flowProcessQuery.setFlowProcessType(1);
        flowProcessQuery.setProcessId(processId);

        List<FlowProcessDetailDO> flowProcessDetailDOListList = flowProcessDetailMapper.selectListOld(flowProcessQuery);
        if (CollUtilX.isEmpty(flowProcessDetailDOListList)) {
            throw new BizException("500", "生产工单工艺路线不存在!");
        }

        Map<Long, FlowProcessDetailDO> processDetailDOMap = flowProcessDetailDOListList.stream().collect(Collectors.toMap(FlowProcessDetailDO::getFlowProcessId, detailDO -> detailDO));

        //查询该工序中工艺路线的良品报工规则设置
        for (ProdWorkDO prodWorkDO : prodWorkDOList) {

            DispatchMaterialRespVO respVO = BeanUtilX.copy(prodWorkDO, DispatchMaterialRespVO::new);

            FlowProcessDetailDO processDetailDO = processDetailDOMap.get(prodWorkDO.getProdWorkId());
//            if (processDetailDO != null) {
//                List<JSONObject> checkPriceRuleInfo = processDetailDO.getCheckPriceRuleInfo();
//                respVO.setCheckPriceRuleInfo(checkPriceRuleInfo);
                respVO.setProcessId(processDetailDO.getProcessId());
                respVO.setProcessCode(processDetailDO.getProcessCode());
                respVO.setProcessName(processDetailDO.getProcessName());
//            }

            ERPMaterialRespVO materialRespVO = erpMaterialDOMap.get(prodWorkDO.getMaterialId());
            if (materialRespVO != null) {
                respVO.setMaterialName(materialRespVO.getMaterialName());
            }

            dispatchWorkResps.add(respVO);
        }

        return dispatchWorkResps;
    }

    /**
     * 查询派工数据
     *
     * @param materialId
     * @param processId
     * @param dispatchMethod
     * @param processMethod
     * @return
     */
    @Override
    public DispatchDataRespBO findDispatchData(Long workCenterId, Long materialId, Long processId, Integer dispatchMethod, Long processMethod,String pieceworkMethodDictId) {

        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询工艺路线下的生产工单
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setMaterialId(materialId);
        prodWorkQuery.setProcessConfig(dispatchMethod);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);

        List<Long> prodWorkIdList = new ArrayList<>();
        for (ProdWorkDO prodWorkDO : prodWorkList) {
            prodWorkIdList.add(prodWorkDO.getProdWorkId());
        }

        DispatchDataRespBO dispatchVerifiyRespBO = new DispatchDataRespBO();
        List<Long> exclProdWorkIdList = new ArrayList<>();

        //派工任务汇总
        List<DispatchQtyRespBO> dispatchQtyRespBOAllList = new ArrayList<>();

        //可派工数量
        Map<Long, BigDecimal> dispatchAbleQtyMap = new HashMap<>();

        //已派工数量
//        Map<Long, BigDecimal> dispatchedQtyMap = new HashMap<>();

        //查询生产工单下每个工序的派工任务
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setPieceworkMethodDictId(pieceworkMethodDictId);
        dispatchWorkQuery.setDispatchMethod(dispatchMethod);
        dispatchWorkQuery.setProdWorkIdList(prodWorkIdList);
        dispatchWorkQuery.setProcessId(processId);
        dispatchWorkQuery.setMaterialId(materialId);

        List<DispatchQtyRespBO> dispatchQtyRespBOList = dispatchWorkMapper.sumDispatchQty(dispatchWorkQuery);
        if (CollUtilX.isNotEmpty(dispatchQtyRespBOList)) {
            dispatchQtyRespBOAllList.addAll(dispatchQtyRespBOList);
        }

//        List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
//
//        for (DispatchWorkDO dispatchWorkDO : dispatchWorkDOList) {
//            BigDecimal dispatchAbleQty = dispatchWorkDO.getWorkPlanTotalQty().subtract(dispatchWorkDO.getDispatchQty());
//            if (dispatchAbleQty.compareTo(BigDecimal.ZERO) == 0) {
//                result.add(dispatchWorkDO.getProdWorkId());
//            } else {
//                dispatchAbleQtyMap.put(dispatchWorkDO.getProdWorkId(), dispatchAbleQty);
//                dispatchedQtyMap.put(dispatchWorkDO.getProdWorkId(), dispatchWorkDO.getDispatchQty());
//            }
//        }

        //委外派工数量
        ProcessOutDemandQueryReqVO processOutDemandQuery = new ProcessOutDemandQueryReqVO();
        processOutDemandQuery.setPieceworkMethodDictId(pieceworkMethodDictId);
        processOutDemandQuery.setDispatchMethod(dispatchMethod);
        processOutDemandQuery.setProdWorkIdList(prodWorkIdList);
        processOutDemandQuery.setProcessId(processId);
        processOutDemandQuery.setMaterialId(materialId);

        List<DispatchQtyRespBO> outQtyRespBOList = processOutDemandMapper.sumDispatchQty(dispatchWorkQuery);

//        List<ProcessOutDemandDO> outDemandDOList = processOutDemandMapper.selectListOld(processOutDemandQuery);
//        for (ProcessOutDemandDO dispatchWorkDO : outDemandDOList) {
//            Long workId = dispatchWorkDO.getProdWorkId();
//            BigDecimal outDemandQty = dispatchWorkDO.getOutDemandQty();
//
//            //已派工数量
//            BigDecimal dispatchedQty = dispatchedQtyMap.get(workId);
//            if (dispatchedQty == null) {
//                dispatchedQty = BigDecimal.ZERO;
//            }
//            dispatchedQty = dispatchedQty.add(outDemandQty);
//
//            //可派工数量
//            BigDecimal dispatchAbleQty = dispatchAbleQtyMap.get(workId);
//            if (dispatchAbleQty == null) {
//                dispatchAbleQty = BigDecimal.ZERO;
//            }
//
//            if (dispatchedQty.compareTo(dispatchWorkDO.getWorkPlanTotalQty()) >= 0) {
//                result.add(dispatchWorkDO.getProdWorkId());
//                dispatchAbleQtyMap.remove(workId);
//            } else {
//                dispatchAbleQty = dispatchWorkDO.getWorkPlanTotalQty().subtract(dispatchWorkDO.getOutDemandQty()).subtract(dispatchAbleQty);
//                dispatchAbleQtyMap.put(workId, dispatchAbleQty);
//            }
//        }

        if (CollUtilX.isNotEmpty(outQtyRespBOList)) {
            dispatchQtyRespBOAllList.addAll(outQtyRespBOList);
        }

        if (CollUtilX.isNotEmpty(dispatchQtyRespBOAllList)) {
            for (DispatchQtyRespBO dispatchQtyRespBO : dispatchQtyRespBOAllList) {
                if (dispatchQtyRespBO.getDispatchAbleQty().compareTo(BigDecimal.ZERO) == 0) {
                    exclProdWorkIdList.add(dispatchQtyRespBO.getProdWorkId());
                } else {
                    dispatchAbleQtyMap.put(dispatchQtyRespBO.getProdWorkId(), dispatchQtyRespBO.getDispatchAbleQty());
                }
            }
        }

        dispatchVerifiyRespBO.setProdWorkIdList(exclProdWorkIdList);
        dispatchVerifiyRespBO.setDispatchAbleQtyMap(dispatchAbleQtyMap);

        return dispatchVerifiyRespBO;
    }

    @Override
    public void writeBackDispatch(DispatchWriteBackBO dispatchWriteBackBO) {

        //报工数量
        BigDecimal okQty = dispatchWriteBackBO.getOkQty();

        //派工单
        DispatchWorkDO dispatchWorkDO = dispatchWorkValidateExists(dispatchWriteBackBO.getDispatchWorkId());

        BigDecimal reportedQty = dispatchWorkDO.getReportedQty();
        reportedQty = reportedQty.add(okQty);

        BigDecimal pendingQty = dispatchWorkDO.getPendingQty();
        pendingQty = pendingQty.subtract(okQty);

        Integer formStatus = dispatchWorkDO.getFormStatus();
        boolean isChange =  formStatus.equals(OrderStatusEnum.DISPATCHED_CLOSED.getCode());

        //派工单状态 更新为已完成
        if (pendingQty.compareTo(BigDecimal.ZERO) == 0) {
            dispatchWorkDO.setFormStatus(OrderStatusEnum.FINISHED.getCode());
        }

        if (!isChange && reportedQty.compareTo(BigDecimal.ZERO) == 0) {
            dispatchWorkDO.setFormStatus(OrderStatusEnum.DISPATCHED.getCode());
        }

        if (!isChange && reportedQty.compareTo(BigDecimal.ZERO) > 0 && pendingQty.compareTo(BigDecimal.ZERO) > 0) {
            dispatchWorkDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
        }

        dispatchWorkDO.setPendingQty(pendingQty);
        dispatchWorkDO.setReportedQty(reportedQty);

        //更新派工单
        dispatchWorkMapper.updateById(dispatchWorkDO);

        //回写生产订单和生产工单
        writeBackProdOrderAndProdWork(dispatchWriteBackBO);

    }

    @Override
    public void writeBackProcessOutDeman(DispatchWriteBackBO dispatchWriteBackBO) {

        //报工数量
        BigDecimal okQty = dispatchWriteBackBO.getOkQty();

        //工序委外单
        ProcessOutDemandDO dispatchWorkDO = processOutDemandMapper.selectById(dispatchWriteBackBO.getProcessOutDemandId());

        //报工数量
        BigDecimal reportedQty = dispatchWorkDO.getOkQty();
        reportedQty = reportedQty.add(okQty);

        dispatchWorkDO.setOkQty(reportedQty);

        //更新派工单
        processOutDemandMapper.updateById(dispatchWorkDO);

        //回写生产订单和生产工单
        writeBackProdOrderAndProdWork(dispatchWriteBackBO);

    }

    /**
     * 回写生产订单和生产工单
     *
     * @param dispatchWriteBackBO
     */
    private void writeBackProdOrderAndProdWork(DispatchWriteBackBO dispatchWriteBackBO) {

        //报工数量
        BigDecimal okQty = dispatchWriteBackBO.getOkQty();
        BigDecimal ngQty = dispatchWriteBackBO.getNgQty();
        ngQty = ngQty == null ? BigDecimal.ZERO : ngQty;

        //非成品工序更新工单状态
        if (dispatchWriteBackBO.getFinalProcess().equals(0)) {
            //更新生产工单实际生产数量
            ProdWorkBO prodWorkBO = new ProdWorkBO();
            prodWorkBO.setProdWorkId(dispatchWriteBackBO.getProdWorkId());
            prodWorkBO.setOkQty(BigDecimal.ZERO);
            prodWorkService.writeBackProdWork(prodWorkBO);
        }

        //最终工序 更新实际生产数量
        if (dispatchWriteBackBO.getFinalProcess().equals(1)) {

            //更新生产订单实际生产数量
            ErpProdOrderBO orderBO = new ErpProdOrderBO();
            orderBO.setProdOrderId(dispatchWriteBackBO.getProdOrderId());
            orderBO.setMaterialId(dispatchWriteBackBO.getMaterialId());
            orderBO.setOkQty(okQty);
            orderBO.setNgQty(ngQty);
            erpProdOrderService.writeBackErpProdOrder(orderBO);

            //更新生产工单实际生产数量
            ProdWorkBO prodWorkBO = new ProdWorkBO();
            prodWorkBO.setProdWorkId(dispatchWriteBackBO.getProdWorkId());
            prodWorkBO.setMaterialId(dispatchWriteBackBO.getMaterialId());
            prodWorkBO.setOkQty(okQty);
            prodWorkBO.setNgQty(ngQty);
            prodWorkService.writeBackProdWork(prodWorkBO);

        }
    }

    /**
     * 关闭派工单
     *
     * @param dispatchWorkIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dispatchWorkStatus(List<Long> dispatchWorkIdList) {
        if (CollUtilX.isEmpty(dispatchWorkIdList)) {
            throw new BizException("500", "派工单主键不能为空!");
        }

        dispatchWorkMapper.updateDispatchWorkStatus(dispatchWorkIdList);
    }

    /**
     * 查询派工完成的生产工单
     *
     * @param processId
     * @param materialId
     * @param dispatchMethod
     * @return
     */
    @Override
    public List<Long> getDispatchCompleteList(Long processId, Long materialId, Integer dispatchMethod) {
        List<Long> result = new ArrayList<>();

        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        //查询工艺路线下的生产工单
        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setMaterialId(materialId);
        prodWorkQuery.setProcessConfig(dispatchMethod);
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);

        List<Long> prodWorkIdList = new ArrayList<>();
        for (ProdWorkDO prodWorkDO : prodWorkList) {
            prodWorkIdList.add(prodWorkDO.getProdWorkId());
        }

        //查询生产订单工艺路线
        FlowProcessDetailQueryReqVO flowProcessDetail = new FlowProcessDetailQueryReqVO();
        flowProcessDetail.setFlowProcessIdList(prodWorkIdList);
        flowProcessDetail.setProcessId(processId);
        flowProcessDetail.setFlowProcessType(1);
        List<FlowProcessDetailDO> flowProcessDetailDOList = flowProcessDetailMapper.selectListOld(flowProcessDetail);
        if (flowProcessDetailDOList.size() != prodWorkIdList.size()) {
            throw new BizException("500", "生产工单工艺路线不存在!");
        }

        //查询 待生产|生产中 的派工单数据
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setProdWorkIdList(prodWorkIdList);
        dispatchWorkQuery.setMaterialId(materialId);
        dispatchWorkQuery.setDispatchMethod(dispatchMethod);
        dispatchWorkQuery.setProcessId(processId);
        dispatchWorkQuery.setFormStatusList(formStatusList);
        List<DispatchWorkDO> dispatchWorkDOList = dispatchWorkMapper.selectListOld(dispatchWorkQuery);
        if (CollUtilX.isEmpty(dispatchWorkDOList)) {
            return result;
        }

        for (DispatchWorkDO dispatchWorkDO : dispatchWorkDOList) {
            BigDecimal dispatchAbleQty = dispatchWorkDO.getWorkPlanTotalQty().subtract(dispatchWorkDO.getDispatchQty());
            if (dispatchAbleQty.compareTo(BigDecimal.ZERO) == 0) {
                result.add(dispatchWorkDO.getProdWorkId());
            }
        }

        return result;

    }

    @Override
    public void dispatchQtyVerify(DispatchDataVerifyReqBO dispatchDataVerifyReqBO) {

        //待派工数量
        BigDecimal dispatchAbleQty = BigDecimal.ZERO;
        //派工方式
        Integer dispatchMethod = dispatchDataVerifyReqBO.getDispatchMethod();
        //本次派工数量
        BigDecimal dispatchQty = dispatchDataVerifyReqBO.getDispatchQty();
        String pieceworkMethodDictId = dispatchDataVerifyReqBO.getPieceworkMethodDictId();

        if (dispatchMethod.equals(0)) {
            //查询派工任务列表
            DispatchWorkQueryReqVO dispatchWorkQuery = BeanUtilX.copy(dispatchDataVerifyReqBO, DispatchWorkQueryReqVO::new);
            DispatchBaseRespVO dispatchList = findDispatchMaterialList(dispatchWorkQuery);

            List<DispatchMaterialRespVO> dispatchMaterialList = dispatchList.getDispatchMaterialList();
            if (CollUtilX.isEmpty(dispatchMaterialList)) {
                throw new BizException("500", "物料无待派工数量,请重新点击查询按钮!");
            }

            DispatchMaterialRespVO dispatchMaterial = dispatchMaterialList.get(0);
            dispatchAbleQty = dispatchMaterial.getDispatchAbleQty();
        }

        if (dispatchMethod.equals(1)) {
            //查询派工任务列表
            DispatchWorkQueryReqVO dispatchWorkQuery = BeanUtilX.copy(dispatchDataVerifyReqBO, DispatchWorkQueryReqVO::new);
            DispatchBaseRespVO dispatchList = findProcessDispatchMaterialList(dispatchWorkQuery);

            List<DispatchMaterialRespVO> dispatchMaterialList = dispatchList.getDispatchMaterialList();
            if (CollUtilX.isEmpty(dispatchMaterialList)) {
                throw new BizException("500", "物料无待派工数量,请重新点击查询按钮!");
            }

            for (DispatchMaterialRespVO dispatchMaterial : dispatchMaterialList){
                if (dispatchMaterial.getPieceworkMethodDictId().equals(pieceworkMethodDictId)){
                    dispatchAbleQty = dispatchMaterial.getDispatchAbleQty();
                }
            }
        }

        if (dispatchQty.compareTo(dispatchAbleQty) > 0) {
            throw new BizException("500", "派工数量不允许超过待派工数量,请重新点击查询按钮!");
        }


    }

    @Override
    public void reportedQtyVerify(DispatchDataVerifyReqBO dispatchDataVerifyReqBO) {

        //待生产数量
        BigDecimal pendingQty = BigDecimal.ZERO;
        //报工类型
        Integer reportedWorkType = dispatchDataVerifyReqBO.getReportedWorkType();
        //本次报工数量
        BigDecimal reportedQty = dispatchDataVerifyReqBO.getReportedQty();

        //工单报工
        if (reportedWorkType.equals(0)) {
            //查询派工任务列表
            DispatchWorkQueryReqVO dispatchWorkQuery = BeanUtilX.copy(dispatchDataVerifyReqBO, DispatchWorkQueryReqVO::new);
            List<DispatchWorkRespVO> dispatchWorkList = dispatchWorkListByPW(dispatchWorkQuery);

            if (CollUtilX.isEmpty(dispatchWorkList)) {
                throw new BizException("500", "物料无待生产数量,请重新点击查询按钮!");
            }

            DispatchWorkRespVO dispatchMaterial = dispatchWorkList.get(0);
            pendingQty = dispatchMaterial.getPendingQty();
        }

        //工序报工
        if (reportedWorkType.equals(1)) {
            //查询派工任务列表
            DispatchWorkQueryReqVO dispatchWorkQuery = BeanUtilX.copy(dispatchDataVerifyReqBO, DispatchWorkQueryReqVO::new);
            List<DispatchMaterialRespVO> dispatchList = dispatchWorkListByProcess(dispatchWorkQuery);

            if (CollUtilX.isEmpty(dispatchList)) {
                throw new BizException("500", "物料无待派工数量,请重新点击查询按钮!");
            }

            DispatchMaterialRespVO dispatchMaterial = dispatchList.get(0);
            pendingQty = dispatchMaterial.getPendingQty();
        }

        //委外报工
        if (reportedWorkType.equals(2)) {
            //查询派工任务列表
            DispatchWorkQueryReqVO dispatchWorkQuery = BeanUtilX.copy(dispatchDataVerifyReqBO, DispatchWorkQueryReqVO::new);
            List<DispatchMaterialRespVO> dispatchList = findMaterialListByProcessOut(dispatchWorkQuery);

            if (CollUtilX.isEmpty(dispatchList)) {
                throw new BizException("500", "物料无待派工数量,请重新点击查询按钮!");
            }

            DispatchMaterialRespVO dispatchMaterial = dispatchList.get(0);
            pendingQty = dispatchMaterial.getPendingQty();
        }

        if (reportedQty.compareTo(pendingQty) > 0) {
            throw new BizException("500", "报工数量不允许超过待生产数量,请重新点击查询按钮!");
        }

    }

    @Override
    public List<ProcessRespVO> findDispatchProcessList(DispatchWorkQueryReqVO reqVO) {
        List<ProcessRespVO> respVOS = dispatchWorkMapper.findDispatchProcessList(reqVO);
        return respVOS;
    }

    @Override
    public DispatchViewRespVO viewByProdWork(DispatchWorkQueryReqVO reqVO) {

        DispatchViewRespVO viewRespVO = new DispatchViewRespVO();

//        if (reqVO.getProcessId() == null) {
//            throw new BizException("500", "工序主键不允许为空!");
//        }

        //只需要待生产|生产中的 生产工单
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.PENDING.getCode());
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());

        ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
        prodWorkQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        prodWorkQuery.setFormStatusList(formStatusList);
        List<ProdWorkDO> prodWorkList = prodWorkMapper.selectListOld(prodWorkQuery);

        if (CollUtilX.isEmpty(prodWorkList)) {
            return viewRespVO;
        }

        List<Long> prodWorkIdList = prodWorkList.stream().map(ProdWorkDO::getProdWorkId).collect(Collectors.toList());

        //查询派工工序
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setIsFullDispatch(1);
        dispatchWorkQuery.setDispatchMethod(0);
        List<ProcessRespVO> dispatchProcessList = this.findDispatchProcessList(dispatchWorkQuery);
        if (CollUtilX.isEmpty(dispatchProcessList)) {
            return viewRespVO;
        }

        List<Long> processIdList = dispatchProcessList.stream().map(ProcessRespVO::getProcessId).collect(Collectors.toList());

        //查询派工任务列表
        reqVO.setProcessIdList(processIdList);
//        List<DispatchWorkCenterRespVO> dispatchWorkCenterList = this.findDispatchWorkCenterList(reqVO);
        List<DispatchWorkCenterRespVO> dispatchWorkCenterList = this.findDispatchWorkCenterListView(reqVO);

        //查询工序委外清单
        DispatchProcessOutDemandReqBO outDemandReqBO = new DispatchProcessOutDemandReqBO();
        outDemandReqBO.setProdWorkIdList(prodWorkIdList);
        outDemandReqBO.setDispatchMethod(0);
        outDemandReqBO.setProcessIdList(processIdList);
        DispatchProcessOutDemandRespVO outDemandRespVO = this.doDemandRespVO(outDemandReqBO);

        //工单计划数量
        Map<Long,BigDecimal> prodWorkQtyMap = new HashMap<>();

        //统计派工数量
        Map<String,BigDecimal> dispatchQtyMap = new HashMap<>();

        //统计待派工数量-工作中心维度
        if (CollUtilX.isNotEmpty(dispatchWorkCenterList)) {
            for (DispatchWorkCenterRespVO dispatchWork : dispatchWorkCenterList) {
                BigDecimal pendingQty = BigDecimal.ZERO;

                if (CollUtilX.isEmpty(dispatchWork.getDetailList())) {
                    continue;
                }

                for (DispatchWorkRespVO item : dispatchWork.getDetailList()) {

                    //工单计划数量
                    prodWorkQtyMap.put(item.getProdWorkId(), item.getWorkPlanTotalQty());

                    //派工数量
                    String key = item.getProdWorkId() + "_" + item.getProcessId();
                    BigDecimal dispatchQty = dispatchQtyMap.get(key);
                    if (dispatchQty == null) {
                        dispatchQtyMap.put(key, item.getDispatchQty());
                    }else {
                        dispatchQty = dispatchQty.add(item.getDispatchQty());
                        dispatchQtyMap.put(key, dispatchQty);
                    }

                    //待生产数量
                    pendingQty = pendingQty.add(item.getPendingQty());
                }

                //待生产数量之和
                dispatchWork.setWorkCenterPendingQty(pendingQty);
            }
        }

        //统计待派工数量-工序委外需求清单维度
        if (CollUtilX.isNotEmpty(outDemandRespVO.getProcessOutDemandList())) {
            BigDecimal purchaseableQty = BigDecimal.ZERO;
            for (DispatchProcessOutDemandRespVO item : outDemandRespVO.getProcessOutDemandList() ){

                //工单计划数量
                prodWorkQtyMap.put(item.getProdWorkId(), item.getWorkPlanTotalQty());

                //派工数量
                String key = item.getProdWorkId() + "_" + item.getProcessId();
                BigDecimal dispatchQty = dispatchQtyMap.get(key);
                if (dispatchQty == null) {
                    dispatchQtyMap.put(key, item.getOutDemandQty());
                }else {
                    dispatchQty = dispatchQty.add(item.getOutDemandQty());
                    dispatchQtyMap.put(key, dispatchQty);
                }

                //可采购数量
                purchaseableQty = purchaseableQty.add(item.getPurchaseableQty());
            }

            outDemandRespVO.setPendingPurchaseTotalQty(purchaseableQty);
        }

        boolean isNotNull = false;
        if (CollUtilX.isNotEmpty(dispatchWorkCenterList)) {
            for (DispatchWorkCenterRespVO dispatchWork : dispatchWorkCenterList) {
                if (CollUtilX.isEmpty(dispatchWork.getDetailList())) {
                    continue;
                }

                isNotNull = true;

                //移除待生产数量为0的任务
                Iterator<DispatchWorkRespVO> iterator = dispatchWork.getDetailList().iterator();
                while (iterator.hasNext()) {
                    DispatchWorkRespVO item = iterator.next();
                    if (item.getPendingQty().compareTo(BigDecimal.ZERO) == 0) {
                        iterator.remove();
                    }

                    BigDecimal workPlanTotalQty = prodWorkQtyMap.get(item.getProdWorkId());
                    String key = item.getProdWorkId() + "_" + item.getProcessId();
                    BigDecimal dispatchQty = dispatchQtyMap.getOrDefault(key,BigDecimal.ZERO);
                    item.setDispatchAbleQty(workPlanTotalQty.subtract(dispatchQty));
                    item.setSelfQty(workPlanTotalQty.subtract(dispatchQty));
                    item.setOutQty(workPlanTotalQty.subtract(dispatchQty));
                }
            }
        }

        if (CollUtilX.isNotEmpty(outDemandRespVO.getProcessOutDemandList())) {
            isNotNull = true;

            //移除可采购数量为0的任务
            Iterator<DispatchProcessOutDemandRespVO> iterator = outDemandRespVO.getProcessOutDemandList().iterator();
            while (iterator.hasNext()) {
                DispatchProcessOutDemandRespVO outDemand = iterator.next();
                if (outDemand.getPurchaseableQty().compareTo(BigDecimal.ZERO) == 0) {
                    iterator.remove();
                }

                BigDecimal workPlanTotalQty = prodWorkQtyMap.get(outDemand.getProdWorkId());
                String key = outDemand.getProdWorkId() + "_" + outDemand.getProcessId();
                BigDecimal dispatchQty = dispatchQtyMap.getOrDefault(key,BigDecimal.ZERO);
                outDemand.setDispatchAbleQty(workPlanTotalQty.subtract(dispatchQty));
                outDemand.setSelfQty(workPlanTotalQty.subtract(dispatchQty));
                outDemand.setOutQty(workPlanTotalQty.subtract(dispatchQty));
            }
        }

        if (isNotNull) {
            viewRespVO.setDispatchWorkCenterList(dispatchWorkCenterList);
            viewRespVO.setDemandRespVO(outDemandRespVO);
            return viewRespVO;
        }

        return viewRespVO;
    }

    private List<DispatchWorkCenterRespVO> findDispatchWorkCenterListView(DispatchWorkQueryReqVO reqVO) {
        if (CollUtilX.isEmpty(reqVO.getProcessIdList())) {
            throw new BizException("500", "工序不存在!");
        }

        //派工方式
        Integer dispatchMethod = 0;

        WorkCenterQueryReqVO workCenterQuery = new WorkCenterQueryReqVO();
        workCenterQuery.setDataStatus(DataStatusEnum.APPROVED.key);

        List<WorkCenterDO> workCenterDOList = workCenterMapper.selectListOld(workCenterQuery);
        List<DispatchWorkCenterRespVO> workCenterResps = BeanUtilX.copy(workCenterDOList, DispatchWorkCenterRespVO::new);
        if (CollUtilX.isEmpty(workCenterResps)) {
            return workCenterResps;
        }

        //查询工作中心下的派工物料
//        List<Integer> formStatusList = new ArrayList<>();
//        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());
//        formStatusList.add(OrderStatusEnum.DISPATCHED.getCode());

        DispatchWorkQueryReqVO dispatchMaterialQuery = new DispatchWorkQueryReqVO();
        dispatchMaterialQuery.setProcessId(reqVO.getProcessId());
        dispatchMaterialQuery.setProcessIdList(reqVO.getProcessIdList());
        dispatchMaterialQuery.setDispatchMethod(dispatchMethod);
//        dispatchMaterialQuery.setFormStatusList(formStatusList);
        List<DispatchWorkDO> dispatchWorkList = dispatchWorkMapper.selectList(dispatchMaterialQuery);
        if (CollUtilX.isEmpty(dispatchWorkList)) {
            return workCenterResps;
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        //待生产数量
        Map<Long, BigDecimal> dispatchQtyMap = new HashMap<>();
        Map<Long, List<DispatchWorkRespVO>> dispatchMaterialMap = new HashMap<>();
        for (DispatchWorkDO dispatchWorkDO : dispatchWorkList) {

            DispatchWorkRespVO dispatchWorkResp = BeanUtilX.copy(dispatchWorkDO, DispatchWorkRespVO::new);

            dispatchWorkResp.setPieceworkMethodDictName(dictMap.get(dispatchWorkResp.getPieceworkMethodDictId()));

            BigDecimal pendingQty = dispatchQtyMap.get(dispatchWorkDO.getWorkCenterId());
            List<DispatchWorkRespVO> dispatchMaterialList = dispatchMaterialMap.get(dispatchWorkDO.getWorkCenterId());

            BigDecimal qty = BigDecimal.ZERO;
            if (dispatchWorkDO.getFormStatus().equals(OrderStatusEnum.IN_PRODUCTION.getCode()) || dispatchWorkDO.getFormStatus().equals(OrderStatusEnum.DISPATCHED.getCode())){
                qty = dispatchWorkDO.getPendingQty();
            }else {//已完成|已关闭 待生产数量设置为0
                dispatchWorkResp.setPendingQty(BigDecimal.ZERO);
            }

            if (pendingQty != null) {
                pendingQty = pendingQty.add(qty);
                dispatchMaterialList.add(dispatchWorkResp);
            } else {
                dispatchMaterialList = new ArrayList<>();
                pendingQty = qty;
                dispatchMaterialList.add(dispatchWorkResp);
            }

            dispatchQtyMap.put(dispatchWorkDO.getWorkCenterId(), pendingQty);
            dispatchMaterialMap.put(dispatchWorkDO.getWorkCenterId(), dispatchMaterialList);

        }

        for (DispatchWorkCenterRespVO workCenterResp : workCenterResps) {
            BigDecimal pendingQty = dispatchQtyMap.get(workCenterResp.getWorkCenterId());
            List<DispatchWorkRespVO> dispatchMaterialList = dispatchMaterialMap.get(workCenterResp.getWorkCenterId());
            workCenterResp.setWorkCenterPendingQty(pendingQty);

            if (CollUtilX.isEmpty(dispatchMaterialList)) {
                continue;
            }

            workCenterResp.setDetailList(dispatchMaterialList);
        }

        return workCenterResps;
    }

    @Override
    public DispatchViewRespVO viewByProcess(DispatchWorkQueryReqVO reqVO) {
        DispatchViewRespVO viewRespVO = new DispatchViewRespVO();

        //查询派工工序
        DispatchWorkQueryReqVO dispatchProcessQuery = new DispatchWorkQueryReqVO();
        dispatchProcessQuery.setIsFullDispatch(1);
        dispatchProcessQuery.setDispatchMethod(1);
        List<ProcessRespVO> dispatchProcessList = this.findDispatchProcessList(dispatchProcessQuery);
        if (CollUtilX.isEmpty(dispatchProcessList)) {
            return viewRespVO;
        }

        List<Long> processIdList = dispatchProcessList.stream().map(ProcessRespVO::getProcessId).collect(Collectors.toList());

        //自制数量
        Map<String,BigDecimal> selfQtyMap = new HashMap<>();
        //委外数量
        Map<String,BigDecimal> outQtyMap = new HashMap<>();

        //查询派工物料
        DispatchWorkQueryReqVO dispatchWorkQuery = new DispatchWorkQueryReqVO();
        dispatchWorkQuery.setProcessId(reqVO.getProcessId());
        dispatchWorkQuery.setProcessIdList(processIdList);
        dispatchWorkQuery.setDispatchMethod(1);
        DispatchBaseRespVO dispatchResp = this.findProcessDispatchMaterialList(dispatchWorkQuery);

        if (dispatchResp != null) {
            for (DispatchMaterialRespVO dispatchMaterial : dispatchResp.getDispatchMaterialList()){
                BigDecimal selfQty = dispatchMaterial.getSelfQty();
                BigDecimal outQty = dispatchMaterial.getOutQty();

                String materialKey = dispatchMaterial.getProcessId() +"_" + dispatchMaterial.getMaterialId() +"_"+dispatchMaterial.getPieceworkMethodDictId();
                selfQtyMap.put(materialKey, selfQty);
                outQtyMap.put(materialKey, outQty);
            }

            //委外需求单
            DispatchProcessOutDemandRespVO demandRespVO = dispatchResp.getDemandRespVO();
            if (demandRespVO != null) {
                //待采购数量
                BigDecimal purchaseableQty = BigDecimal.ZERO;
                for (DispatchProcessOutDemandRespVO item : demandRespVO.getProcessOutDemandList()){
                    purchaseableQty = purchaseableQty.add(item.getPurchaseableQty());
                    String materialKey = item.getProcessId() +"_" + item.getMaterialId() +"_"+item.getPieceworkMethodDictId();
                    item.setSelfQty(selfQtyMap.getOrDefault(materialKey,BigDecimal.ZERO));
                    item.setOutQty(outQtyMap.getOrDefault(materialKey,BigDecimal.ZERO));
                }
                demandRespVO.setPendingPurchaseTotalQty(purchaseableQty);
            }

            viewRespVO.setDemandRespVO(demandRespVO);
        }

        //查询派工任务
//        List<DispatchWorkCenterRespVO> dispatchWorkCenterList = this.findProcessDispatchWorkCenterList(dispatchWorkQuery);
        List<DispatchWorkCenterRespVO> dispatchWorkCenterList = this.findProcessDispatchWorkCenterListView(dispatchWorkQuery);
        if (CollUtilX.isNotEmpty(dispatchWorkCenterList)) {
            for (DispatchWorkCenterRespVO dispatchWork : dispatchWorkCenterList) {
                BigDecimal workCenterPendingQty = BigDecimal.ZERO;
                if (CollUtilX.isEmpty(dispatchWork.getDetailList())) {
                    continue;
                }

                for (DispatchWorkRespVO item : dispatchWork.getDetailList()) {
                    String materialKey = item.getProcessId() +"_" + item.getMaterialId() +"_"+item.getPieceworkMethodDictId();
                    workCenterPendingQty = workCenterPendingQty.add(item.getPendingQty());
                    item.setSelfQty(selfQtyMap.getOrDefault(materialKey,BigDecimal.ZERO));
                    item.setOutQty(outQtyMap.getOrDefault(materialKey,BigDecimal.ZERO));
                }
                dispatchWork.setWorkCenterPendingQty(workCenterPendingQty);
            }

            viewRespVO.setDispatchWorkCenterList(dispatchWorkCenterList);
        }
        return viewRespVO;
    }

    private List<DispatchWorkCenterRespVO> findProcessDispatchWorkCenterListView(DispatchWorkQueryReqVO reqVO) {

        if (CollUtilX.isEmpty(reqVO.getProcessIdList())) {
            throw new BizException("500", "工序不存在!");
        }

        //派工方式
        Integer dispatchMethod = 1;

        //查询工序关联的工作中心列表
        WorkCenterQueryReqVO workCenterQuery = new WorkCenterQueryReqVO();
        workCenterQuery.setDataStatus(DataStatusEnum.APPROVED.key);

        List<WorkCenterDO> workCenterDOList = workCenterMapper.selectListOld(workCenterQuery);
        List<DispatchWorkCenterRespVO> workCenterResps = BeanUtilX.copy(workCenterDOList, DispatchWorkCenterRespVO::new);
        if (CollUtilX.isEmpty(workCenterResps)) {
            return workCenterResps;
        }

        //查询工作中心下的派工物料
        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(OrderStatusEnum.IN_PRODUCTION.getCode());
        formStatusList.add(OrderStatusEnum.DISPATCHED.getCode());

        DispatchWorkQueryReqVO dispatchMaterialQuery = new DispatchWorkQueryReqVO();
        dispatchMaterialQuery.setProcessIdList(reqVO.getProcessIdList());
        dispatchMaterialQuery.setDispatchMethod(dispatchMethod);
        dispatchMaterialQuery.setFormStatusList(formStatusList);
        List<DispatchWorkDO> dispatchWorkList = dispatchWorkMapper.selectList(dispatchMaterialQuery);
        if (CollUtilX.isEmpty(dispatchWorkList)) {
            return workCenterResps;
        }

        //查询物料
        List<Long> materialIdList = new ArrayList<>();
        for (DispatchWorkDO item : dispatchWorkList) {
            materialIdList.add(item.getMaterialId());
        }

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PIECE_WORK_METHOD.getDictCode());

        //相同物料待生产数量汇总
        Map<Long, DispatchWorkDO> workCenterMap = new HashMap<>();
        Map<String, DispatchWorkRespVO> workCenterMaterialMap = new HashMap<>();

        //待生产数量处理
        for (DispatchWorkDO dispatchWorkDO : dispatchWorkList) {
            DispatchWorkRespVO dispatchWorkResp = BeanUtilX.copy(dispatchWorkDO, DispatchWorkRespVO::new);

            dispatchWorkResp.setPieceworkMethodDictName(dictMap.get(dispatchWorkResp.getPieceworkMethodDictId()));

            //统计相同工作中心的待生产数量
            DispatchWorkDO dispatchWork = workCenterMap.get(dispatchWorkResp.getWorkCenterId());
            if (dispatchWork == null) {
                //填充物料
                ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(dispatchWorkResp.getMaterialId());
                if (erpMaterialDO != null) {
                    dispatchWorkResp.setMaterialName(erpMaterialDO.getMaterialName());
                }
                DispatchWorkDO dispatchWorkCenter = new DispatchWorkDO();
                dispatchWorkCenter.setWorkCenterId(dispatchWorkResp.getWorkCenterId());
                dispatchWorkCenter.setWorkCenterCode(dispatchWorkResp.getWorkCenterCode());
                dispatchWorkCenter.setWorkCenterName(dispatchWorkResp.getWorkCenterName());
                dispatchWorkCenter.setPendingQty(dispatchWorkResp.getPendingQty());
                workCenterMap.put(dispatchWorkResp.getWorkCenterId(), dispatchWorkCenter);
            } else {
                BigDecimal pendingQty = dispatchWork.getPendingQty();
                pendingQty = pendingQty.add(dispatchWorkResp.getPendingQty());
                dispatchWork.setPendingQty(pendingQty);

                workCenterMap.put(dispatchWorkResp.getWorkCenterId(), dispatchWork);
            }

            //统计相同物料的待生产数量
            String workCenterMaterialKey = dispatchWorkResp.getProcessId() + "_" +dispatchWorkResp.getWorkCenterId() + "_" + dispatchWorkResp.getMaterialId()+ "_" + dispatchWorkResp.getPieceworkMethodDictId();
            DispatchWorkRespVO workCenterMaterial = workCenterMaterialMap.get(workCenterMaterialKey);
            if (workCenterMaterial == null) {
                DispatchWorkRespVO material = new DispatchWorkRespVO();
                material.setMaterialId(dispatchWorkResp.getMaterialId());
                material.setMaterialCode(dispatchWorkResp.getMaterialCode());
                material.setMaterialName(dispatchWorkResp.getMaterialName());
                material.setPendingQty(dispatchWorkResp.getPendingQty());
                material.setProcessMethod(dispatchWorkResp.getProcessMethod());
                material.setProcessId(dispatchWorkResp.getProcessId());
                material.setProcessCode(dispatchWorkResp.getProcessCode());
                material.setProcessName(dispatchWorkResp.getProcessName());
                material.setPieceworkMethodDictId(dispatchWorkResp.getPieceworkMethodDictId());
                material.setPieceworkMethodDictName(dictMap.get(material.getPieceworkMethodDictId()));
                material.setFinalProcess(dispatchWorkResp.getFinalProcess());
                material.setDispatchQty(dispatchWorkResp.getDispatchQty());

                workCenterMaterialMap.put(workCenterMaterialKey, material);
            } else {
                BigDecimal pendingQty = workCenterMaterial.getPendingQty();
                pendingQty = pendingQty.add(dispatchWorkResp.getPendingQty());

                BigDecimal dispatchQty = workCenterMaterial.getDispatchQty();
                dispatchQty = dispatchQty.add(dispatchWorkResp.getDispatchQty());

                workCenterMaterial.setPendingQty(pendingQty);
                workCenterMaterial.setDispatchQty(dispatchQty);
            }
        }

        for (DispatchWorkCenterRespVO workCenterResp : workCenterResps) {
            DispatchWorkDO dispatchWorkDO = workCenterMap.get(workCenterResp.getWorkCenterId());
            if (dispatchWorkDO == null) {
                continue;
            }
            List<DispatchWorkRespVO> dispatchWorkDOList = new ArrayList<>();
            for (Map.Entry<String, DispatchWorkRespVO> entry : workCenterMaterialMap.entrySet()) {
                String key = entry.getKey();
                DispatchWorkRespVO workDO = entry.getValue();
                if (key.contains(workCenterResp.getWorkCenterId().toString())) {
                    dispatchWorkDOList.add(workDO);
                }
            }

            workCenterResp.setWorkCenterPendingQty(dispatchWorkDO.getPendingQty());
            workCenterResp.setDetailList(dispatchWorkDOList);
        }

        return workCenterResps;
    }

    @Override
    public List<CalculatePriceOrderRespVO> reportingDimensionsList(DispatchQueryReqVO reqVO) {
        //查询生产流程配置
        ProcessConfigQueryReqVO processQuery = new ProcessConfigQueryReqVO();
        processQuery.setProcessType(3);
        processQuery.setModuleName("produce");
        List<ProcessConfigDO> processConfigDOS = processConfigMapper.selectListOld(processQuery);
        if (CollUtilX.isEmpty(processConfigDOS)) {
            throw new BizException("500", "流程配置不存在!");
        }
        JSONObject baseInfo = processConfigDOS.get(0).getBaseInfo();
        CalculatePriceOrderQueryReqVO calculatePriceOrderQueryReqVO = new CalculatePriceOrderQueryReqVO();
        calculatePriceOrderQueryReqVO.setMaterialCode(reqVO.getMaterialCode());
        calculatePriceOrderQueryReqVO.setProcessCode(reqVO.getProcessCode());
        if (reqVO.getReportingDimensionsType() == 1){//记件
            Integer calculatePrice = baseInfo.getInteger("calculatePrice");

            if (calculatePrice == 1){
                //查询生产记件定额清单
                calculatePriceOrderQueryReqVO.setBizType(0);

            }else {
                throw new BizException("500", "生产记件配置异常!");
            }
        }else {
            //记时
            Integer calculateTime = baseInfo.getInteger("calculateTime");

            if (calculateTime == 1){
                //查询生产记时定额清单
                calculatePriceOrderQueryReqVO.setBizType(1);
            }else {
                throw new BizException("500", "生产记时配置异常!");
            }
        }
        calculatePriceOrderQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
        List<CalculatePriceOrderDO> calculatePriceOrderDOS = calculatePriceOrderMapper.selectList(calculatePriceOrderQueryReqVO);
        List<CalculatePriceOrderRespVO> list = new ArrayList<>();
        BeanUtilX.copy(calculatePriceOrderDOS, list);
        return list;
    }

    /**
     * 查询工序
     *
     * @param reqVO
     * @return
     */
    private ProcessDO getProcess(DispatchWorkQueryReqVO reqVO) {

        ProcessQueryReqVO processQuery = new ProcessQueryReqVO();
        processQuery.setProcessCode(reqVO.getProcessCode());
        processQuery.setProcessName(reqVO.getProcessName());
        processQuery.setProcessId(reqVO.getProcessId());
        List<ProcessDO> processList = processMapper.selectListOld(processQuery);
        if (CollUtilX.isEmpty(processList)) {
            throw new BizException("500", "工序不存在!");
        }

        return processList.get(0);
    }

    private void checkDataPermission(ProdWorkQueryReqVO prodWorkQuery) {
        //数据权限处理
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Map<String, Integer> dataScopeMap = loginUser.getDataScopeMap();
        if (dataScopeMap != null && dataScopeMap.size() > 0){
            Integer dataScope = dataScopeMap.get(loginUser.getCurMenuId());
            if(dataScope != null){
                Integer isAdmin = loginUser.getIsAdmin();
                if (isAdmin.equals(BaseEnum.ADMIN.getKey()) || isAdmin.equals(BaseEnum.SUPER_ADMIN.getKey())) {

                }else if (dataScope == DataScopeEnum.CURRENT_ORG.getKey()) {
                    List<String> directorOrgIdList = (List<String>) loginUser.getCurOrgIdList();
                    prodWorkQuery.setDirectorOrgIdList(directorOrgIdList);
                } else if (dataScope == DataScopeEnum.CURRENT_ORG_CHILD.getKey()) {
                    List<String> directorOrgIdList = (List<String>) loginUser.getCurChildOrgIdList();
                    prodWorkQuery.setDirectorOrgIdList(directorOrgIdList);
                } else if (dataScope == DataScopeEnum.CURRENT_USER.getKey()) {
                    Long directorId = loginUser.getCurUserId();
                    prodWorkQuery.setDirectorId(directorId);
                }
            }
            Long prodWorkCount = prodWorkMapper.selectCount(prodWorkQuery);
            if(prodWorkCount == 0){
                throw new BizException("500", "修改失败，暂无此任务的操作权限！");
            }
        }
    }

}
