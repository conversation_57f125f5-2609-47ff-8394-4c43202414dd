package com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo;

import lombok.*;

import java.io.Serializable;
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 盘点单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ErpInventoryBaseVO implements Serializable {

    /** 盘点单ID */
    private Long inventoryId;

    /** 盘点单号 */
    private String inventoryCode;

    /** 盘点单主题 */
    private String inventoryName;

    /** 盘点单类型ID */
    private String inventoryTypeDictId;

    /** 盘点仓库 */
    private String warehouseOrgId;

    /** 盘点方式 */
    private Short inventoryMethod;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime planInventoryDt;

    /** 实际盘点时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime actStartDt;

    /** 盘点完成盘点 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime actEndDt;

    /** 盘点状态 */
    private Integer inventoryStatus = 0;

    /** 调整状态 ["无需调整","待调整"，“已调整”]*/
    private Integer adjustStatus;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 单据状态 */
    private Integer dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

    /**
     * 仓库列表
     */
    private List<String> warehouseOrgIdList;
}
