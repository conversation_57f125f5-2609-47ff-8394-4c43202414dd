package com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 销售订单明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_erp_sale_order_detail", autoResultMap = true)
//@KeySequence("u_erp_sale_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpSaleOrderDetailDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long saleOrderDetailId;

    /** 销售订单id */
    private Long saleOrderId;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 行号 */
    private Short rowNo;

    /** 关联行号 */
    private Short relatedRowNo;

    /** 物料id */
    private Long materialId;

    /** 基本单位 */
    private String mainUnitDictId;

    /** 数量 */
    private BigDecimal qty;

    /** 单价 */
    private BigDecimal exclTaxPrice;

    /** 金额 */
    private BigDecimal exclTaxAmt;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 含税单价 */
    private BigDecimal inclTaxPrice;

    /** 含税金额 */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 已出库数量 */
    private BigDecimal outQty;

    /** 已规划需求数量 */
    private BigDecimal plannedDemandQty;

    /** 可规划需求数量 */
    private BigDecimal planAbleDemandQty;

    /** 已通知数量 */
    private BigDecimal notifiedQty;

    /** 已退货数量 */
    private BigDecimal returnedQty;

    /** 已扣费数量 */
    private BigDecimal feedQty;

    /** 已采购数量 */
    private BigDecimal purchasedQty;

    /** 是否通知完成 */
    private Integer isMaterialFulNotified;

    /** 是否退货完成 */
    private Integer isMaterialFulReturned;

    /** 是否采购完成 */
    private Integer isMaterialFulPurchased;

    /** 是否出库完成 */
    private Integer isMaterialFullOutbounded;

    /** 已对账数量 */
    private BigDecimal reconciledQty;

    /** 已计划数量 */
    private BigDecimal planedQty;

    /** 已计划开票数量 */
    private BigDecimal invoicedQty;

    /** 已换货数量 */
    private BigDecimal exchangeQty;

    /** 已换货入库数量 */
    private BigDecimal exchangeInboundQty;

    /** 币种字典ID */
    private String currencyDictId;

    /** 本币币种字典ID */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;

    /**
     * 物料编码
     */
    private String materialCode;
}
