package com.mongoso.mgs.module.warehouse.dal.mysql.materialreturn;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.detail.MaterialReturnDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.detail.MaterialReturnDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.detail.MaterialReturnDetailRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.materialreturn.MaterialReturnDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 归还单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialReturnDetailMapper extends BaseMapperX<MaterialReturnDetailDO> {

    default PageResult<MaterialReturnDetailDO> selectPage(MaterialReturnDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<MaterialReturnDetailDO>lambdaQueryX()
                .eqIfPresent(MaterialReturnDetailDO::getReturnId, reqVO.getReturnId())
                .likeIfPresent(MaterialReturnDetailDO::getReturnCode, reqVO.getReturnCode())
                .eqIfPresent(MaterialReturnDetailDO::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(MaterialReturnDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(MaterialReturnDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                        .orderByDesc(MaterialReturnDetailDO::getCreatedDt));
    }


    default List<MaterialReturnDetailDO> selectList(MaterialReturnDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<MaterialReturnDetailDO>lambdaQueryX()
                .eqIfPresent(MaterialReturnDetailDO::getReturnId, reqVO.getReturnId())
                .likeIfPresent(MaterialReturnDetailDO::getReturnCode, reqVO.getReturnCode())
                .eqIfPresent(MaterialReturnDetailDO::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(MaterialReturnDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(MaterialReturnDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                        .orderByDesc(MaterialReturnDetailDO::getCreatedDt));
    }

    default int deleteByReturnId(Long returnId){
        return delete(LambdaQueryWrapperX.<MaterialReturnDetailDO>lambdaQueryX()
                .eq(MaterialReturnDetailDO:: getReturnId, returnId)
        );
    }

    IPage<MaterialReturnDetailRespVO> queryMaterialReturnDetailPage(Page<MaterialReturnDetailPageReqVO> page,
                                                                    @Param("reqVO") MaterialReturnDetailPageReqVO reqVO);

    List<MaterialReturnDetailRespVO> queryMaterialReturnDetailList(@Param("reqVO") MaterialReturnDetailQueryReqVO reqVO);

    List<DocumentRespBO> returnQtyList(@Param("returnId") Long returnId);

    List<MaterialWarehouseBO> returnMaterialWarehouseList(@Param("returnIdList") List<Long> returnIdList);

}