package com.mongoso.mgs.module.base.controller.admin.hzproductbomlist;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.controller.admin.hzproductbomlist.vo.*;
import com.mongoso.mgs.module.base.dal.db.hzproductbomlist.HzProductBomListDO;
import com.mongoso.mgs.module.base.service.hzproductbomlist.HzProductBomListService;

/**
 * 产品关联BOM清单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/base")
@Validated
public class HzProductBomListController {

    @Resource
    private HzProductBomListService hzProductBomListService;

    @OperateLog("产品关联BOM清单添加或编辑")
    @PostMapping("/hzProductBomListAdit")
    @PreAuthorize("@ss.hasPermission('hzProductBomList:adit')")
    public ResultX<Long> hzProductBomListAdit(@Valid @RequestBody HzProductBomListAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? hzProductBomListService.hzProductBomListAdd(reqVO)
                            : hzProductBomListService.hzProductBomListEdit(reqVO));
    }

    @OperateLog("产品关联BOM清单删除")
    @PostMapping("/hzProductBomListDel")
    @PreAuthorize("@ss.hasPermission('hzProductBomList:del')")
    public ResultX<Boolean> hzProductBomListDel(@Valid @RequestBody HzProductBomListPrimaryReqVO reqVO) {
        hzProductBomListService.hzProductBomListDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("产品关联BOM清单详情")
    @PostMapping("/hzProductBomListDetail")
    @PreAuthorize("@ss.hasPermission('hzProductBomList:query')")
    public ResultX<HzProductBomListRespVO> hzProductBomListDetail(@Valid @RequestBody HzProductBomListPrimaryReqVO reqVO) {
        HzProductBomListDO oldDO = hzProductBomListService.hzProductBomListDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, HzProductBomListRespVO::new));
    }

    @OperateLog("产品关联BOM清单列表")
    @PostMapping("/hzProductBomListList")
    @PreAuthorize("@ss.hasPermission('hzProductBomList:query')")
    public ResultX<List<HzProductBomListRespVO>> hzProductBomListList(@Valid @RequestBody HzProductBomListQueryReqVO reqVO) {
        List<HzProductBomListDO> list = hzProductBomListService.hzProductBomListList(reqVO);
        return success(BeanUtilX.copyList(list, HzProductBomListRespVO::new));
    }

    @OperateLog("产品关联BOM清单分页")
    @PostMapping("/hzProductBomListPage")
    @PreAuthorize("@ss.hasPermission('hzProductBomList:query')")
    public ResultX<PageResult<HzProductBomListRespVO>> hzProductBomListPage(@Valid @RequestBody HzProductBomListPageReqVO reqVO) {
        PageResult<HzProductBomListDO> pageResult = hzProductBomListService.hzProductBomListPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, HzProductBomListRespVO::new));
    }

}
