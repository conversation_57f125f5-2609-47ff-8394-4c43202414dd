package com.mongoso.mgs.common.util.util;

import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;

import java.util.Map;

public class MapImportHandler extends ExcelDataHandlerDefaultImpl<Map<String, Object>> {

    @Override
    public void setMapValue(Map<String, Object> map, String originKey, Object value) {
        map.put(getNewKey(map, originKey), value);
    }

    private String getNewKey(Map<String, Object> map, String originKey) {
        String newKey = originKey;
        if (map.containsKey(originKey)) {
            newKey = originKey + "_1";
            return getNew<PERSON>ey(map, newKey);
        } else {
            return newKey;
        }
    }
}
