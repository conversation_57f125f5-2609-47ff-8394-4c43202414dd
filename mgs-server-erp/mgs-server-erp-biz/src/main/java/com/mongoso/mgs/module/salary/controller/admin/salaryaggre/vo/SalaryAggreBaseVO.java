package com.mongoso.mgs.module.salary.controller.admin.salaryaggre.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreobj.vo.SalaryAggreObjRespVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryitem.vo.SalaryItemRespVO;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工资单归集 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SalaryAggreBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 工资单归集ID */
    private Long payrollAggreId;

    /** 工资单ID */
    @NotNull(message = "工资单ID不能为空")
    private Long payrollId;

    /** 工资单号 */
    private String payrollCode;

    /** 工资单成员ID */
    @NotNull(message = "工资单成员ID不能为空")
    private Long payrollMemberId;

    /** 工资单月份 */
    private String payrollMonth;

    /** 员工档案ID */
    private Long employeeArchivesId;

    /**
     * 工号
     */
    private String employeeNumber;

    /** 上月固定支出 */
    private BigDecimal lastMonthFixedFee;

    /** 本月固定支出 */
    private BigDecimal currentMonthFixedFee;

    /** 上月递延费用 */
    private BigDecimal lastMonthDeferFee;

    /** 本月递延费用 */
    private BigDecimal currentMonthDeferFee;

    /** 策略配置状态 */
    private Integer strategyConfigStatus;

    /** 是否有固定支出 */
    private Integer isFixedExpense;

    /** 归集状态 */
    @NotNull(message = "归集状态不能为空")
    private Integer aggreStatus;

    /** 承担对象 */
    private List<SalaryAggreObjRespVO> undertakeOrg;

    /** 固定支出成本科目id */
    @NotNull(message = "固定支出成本科目不能为空")
    private Long fixedExpendCostSubjectId;
    private String fixedExpendCostSubjectName;

    /** 固定支出成本用途 */
    @NotNull(message = "固定支出成本用途不能为空")
    private Integer fixedExpendCostUsage;

    /** 递延费用成本科目id */
    @NotNull(message = "递延费用成本科目不能为空")
    private Long deferFeeCostSubjectId;
    private String deferFeeCostSubjectName;

    /** 递延费用成本用途 */
    @NotNull(message = "递延费用成本用途不能为空")
    private Integer deferFeeCostUsage;

    /** 薪酬项目归集 */
    private List<SalaryItemRespVO> salaryItemCollection;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

}
