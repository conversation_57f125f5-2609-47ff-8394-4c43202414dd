package com.mongoso.mgs.module.purchase.service.receiptnotice;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticePageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购收货通知单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseReceiptNoticeService {

    /**
     * 创建采购收货通知单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseReceiptNoticeAdd(@Valid PurchaseReceiptNoticeAditReqVO reqVO);

    /**
     * 更新采购收货通知单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseReceiptNoticeEdit(@Valid PurchaseReceiptNoticeAditReqVO reqVO);

    /**
     * 删除采购收货通知单
     *
     * @param receiptNoticeId 编号
     */
    void purchaseReceiptNoticeDel(Long receiptNoticeId);

    /**
     * 获得采购收货通知单信息
     *
     * @param receiptNoticeId 编号
     * @return 采购收货通知单信息
     */
    PurchaseReceiptNoticeRespVO purchaseReceiptNoticeDetail(Long receiptNoticeId);

    /**
     * 获得采购收货通知单引用信息
     *
     * @param receiptNoticeId 编号
     * @return 采购收货通知单信息
     */
    PurchaseReceiptNoticeRespVO purchaseReceiptNoticeQuotedDetail(Long receiptNoticeId);

    /**
     * 获得采购收货通知单列表
     *
     * @param reqVO 查询条件
     * @return 采购收货通知单列表
     */
    List<PurchaseReceiptNoticeRespVO> purchaseReceiptNoticeList(@Valid PurchaseReceiptNoticeQueryReqVO reqVO);

    /**
     * 获得采购收货通知单分页
     *
     * @param reqVO 查询条件
     * @return 采购收货通知单分页
     */
    PageResult<PurchaseReceiptNoticeRespVO> purchaseReceiptNoticePage(@Valid PurchaseReceiptNoticePageReqVO reqVO);

    ResultX<BatchResult> purchaseReceiptNoticeDelBatch(IdReq reqVO);

    BatchResult erpReceiptNoticeApprove(FlowApprove reqVO);

    Object erpReceiptNoticeFlowCallback(FlowCallback reqVO);

}
