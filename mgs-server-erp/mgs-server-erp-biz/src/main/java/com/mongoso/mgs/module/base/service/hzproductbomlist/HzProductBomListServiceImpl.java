package com.mongoso.mgs.module.base.service.hzproductbomlist;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.base.controller.admin.hzproductbomlist.vo.HzProductBomListAditReqVO;
import com.mongoso.mgs.module.base.controller.admin.hzproductbomlist.vo.HzProductBomListPageReqVO;
import com.mongoso.mgs.module.base.controller.admin.hzproductbomlist.vo.HzProductBomListQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.hzproductbomlist.HzProductBomListDO;
import com.mongoso.mgs.module.base.dal.mysql.hzproductbomlist.HzProductBomListMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.HZ_PRODUCT_BOM_LIST_NOT_EXISTS;

/**
 * 产品关联BOM清单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HzProductBomListServiceImpl implements HzProductBomListService {

    @Resource
    private HzProductBomListMapper hzProductBomListMapper;

    @Override
    public Long hzProductBomListAdd(HzProductBomListAditReqVO reqVO) {
        // 插入
        HzProductBomListDO hzProductBomList = BeanUtilX.copy(reqVO, HzProductBomListDO::new);
        hzProductBomListMapper.insert(hzProductBomList);
        // 返回
        return hzProductBomList.getId();
    }

    @Override
    public Long hzProductBomListEdit(HzProductBomListAditReqVO reqVO) {
        // 校验存在
        this.hzProductBomListValidateExists(reqVO.getId());
        // 更新
        HzProductBomListDO hzProductBomList = BeanUtilX.copy(reqVO, HzProductBomListDO::new);
        hzProductBomListMapper.updateById(hzProductBomList);
        // 返回
        return hzProductBomList.getId();
    }

    @Override
    public void hzProductBomListDel(Long id) {
        // 校验存在
        this.hzProductBomListValidateExists(id);
        // 删除
        hzProductBomListMapper.deleteById(id);
    }

    private HzProductBomListDO hzProductBomListValidateExists(Long id) {
        HzProductBomListDO hzProductBomList = hzProductBomListMapper.selectById(id);
        if (hzProductBomList == null) {
            throw exception(HZ_PRODUCT_BOM_LIST_NOT_EXISTS);
        }
        return hzProductBomList;
    }

    @Override
    public HzProductBomListDO hzProductBomListDetail(Long id) {
        return hzProductBomListMapper.selectById(id);
    }

    @Override
    public List<HzProductBomListDO> hzProductBomListList(HzProductBomListQueryReqVO reqVO) {
        return hzProductBomListMapper.selectList(reqVO);
    }

    @Override
    public PageResult<HzProductBomListDO> hzProductBomListPage(HzProductBomListPageReqVO reqVO) {
        return hzProductBomListMapper.selectPage(reqVO);
    }

    @Override
    public Integer deleteByBomId(Long bId) {
        return hzProductBomListMapper.deleteByBomId(bId);
    }


}
