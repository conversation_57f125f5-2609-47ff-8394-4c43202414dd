package com.mongoso.mgs.common.util.util;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

public class ExcelImportUtils {

    public static ExcelImportResult<Map<String, Object>> importExcelMore(InputStream inputstream, Class<?> pojoClass, ImportParams params) throws Exception {
        ExcelImportResult<Map<String, Object>> result = ExcelImportUtil.importExcelMore(inputstream, Map.class, params);
        if (result != null && result.getList() != null && !result.getList().isEmpty()) {
            List<Map<String, Object>> mapList = result.getList();
            for (Map<String, Object> map : mapList) {
                if (map.containsKey("excelRowNum")) {
                    map.remove("excelRowNum");
                }
            }
        }
        return result;
    }
}
