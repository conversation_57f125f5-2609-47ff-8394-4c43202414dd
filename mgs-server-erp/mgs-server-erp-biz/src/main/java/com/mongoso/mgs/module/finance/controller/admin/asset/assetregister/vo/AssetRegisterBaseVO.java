package com.mongoso.mgs.module.finance.controller.admin.asset.assetregister.vo;

import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogRespVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 资产登记 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class AssetRegisterBaseVO implements Serializable {

    /** 主键ID */
    private Long assetRegisterId;

    /** 资产编码 */
    private String assetCode;

    /** 资产名称 */
    @NotEmpty(message = "资产名称不能为空")
    private String assetName;

    /** 设备编码 */
    private String deviceCode;

    /** 设备id */
    private Long deviceId;

    /** 资产分类 */
    @NotNull(message = "资产分类不能为空")
    private String assetTypeDictId;

    private String assetTypeDictName;

    /** 资产型号 */
    @NotEmpty(message = "资产型号不能为空")
    private String assetModel;

    /** 资产状态ID */
    @NotNull(message = "资产状态ID不能为空")
    private Long assetStatusConfigId;

    private String assetStatusConfigName;

    /** 计提周期 */
    private BigDecimal accrualPeriod;

    /** 计提周期字符串 */
    private String accrualPeriodDictName;

    /** 计提单位类型 */
    private Short accrualUnitType;

    /** 折旧方法ID */
    private Long depreciationsConfigId;

    private String depreciationsConfigName;

    /** 资产购价 */
    @NotNull(message = "资产购价不能为空")
    private BigDecimal assetPurchasePrice;

    /** 资产净值 */
    private BigDecimal assetNetValue;

    /** 净残值率 */
    @NotNull(message = "净残值率不能为空")
    private BigDecimal netResidualValueRate;

    /** 净残值 */
    @NotNull(message = "净残值不能为空")
    private BigDecimal netResidualValue;

    /** 以前是否计提 */
    @NotNull(message = "以前是否计提不能为空")
    private Short isPreAccrued;

    /** 以前已提周期 */
    private BigDecimal preAccruedPeriod;

    /** 以前已提金额 */
    private BigDecimal preAccruedAmt;

    /** 购置日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate purchaseDate;

    /** 购置人ID */
    private Long purchaserId;

    private String purchaserName;

    /** 存放地址 */
    private String storageAddress;

    /** 计提开始日期 */
    @NotNull(message = "计提开始日期不能为空")
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate accrualStartDate;

    /** 领用人 */
    @NotNull(message = "领用人不能为空")
    private Long usedId;

    private String usedName;

    /** 领用日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate usedDate;

    /** 剩余周期 */
    private BigDecimal remainingPeriod;

    /** 已提金额 */
    private BigDecimal assetAccruedAmt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    private String directorName;

    /** 责任部门 */
    private String directorOrgId;

    private String directorOrgName;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 单据状态 */
    private Short dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    private List<FileLogRespVO> fileList;

    /** 版本号 */
    private Integer version;

}
