package com.mongoso.mgs.module.produce.controller.admin.dailyampling.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 日常抽检不良明细表 DO
 *
 * <AUTHOR>
 */
@Data
public class DailyAmplingNgDetailReqVO implements Serializable {

    /** 抽检记录编码 */
    private Long defectReasonId;
    private String defectReasonName;

    /** 不良品数量 */
    private BigDecimal ngQty;




}
