package com.mongoso.mgs.module.comp.company.dal.mysql.compensationitem;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.comp.company.controller.admin.compensationitem.vo.CompanyCompensationItemQueryReqVO;
import com.mongoso.mgs.module.comp.company.controller.admin.compensationitem.vo.CompanyCompensationItemRespVO;
import com.mongoso.mgs.module.comp.company.dal.db.compensationItem.CompanyCompensationItemDO;
import com.mongoso.mgs.module.comp.system.dal.db.compensationitem.CompensationItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公司薪酬项配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CompanyCompensationItemMapper extends BaseMapperX<CompanyCompensationItemDO> {

    default List<CompanyCompensationItemDO> selectList(CompanyCompensationItemQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<CompanyCompensationItemDO>lambdaQueryX()
                .eqIfPresent(CompanyCompensationItemDO::getOrgId, reqVO.getOrgId())
                .orderByDesc(CompanyCompensationItemDO::getId));
    }

    default List<CompanyCompensationItemDO> selectList(String orgId) {
        return selectList(LambdaQueryWrapperX.<CompanyCompensationItemDO>lambdaQueryX()
                .eqIfPresent(CompanyCompensationItemDO::getOrgId, orgId));
    }

    /**
     *  查询公司薪酬项
     * @param orgId 公司组织ID
     * @return 公司薪酬项
     */
    List<CompanyCompensationItemRespVO> queryCompanyCompensationItemList(@Param("orgId") String orgId);

    /**
     *  查询公司薪酬项
     * @param orgId 公司组织ID
     * @return 公司薪酬项
     */
    List<CompensationItemDO> queryCompanyCompensationItemWithSort(@Param("orgId") String orgId);

}
