package com.mongoso.mgs.module.finance.service.payment;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.module.finance.dal.db.advancepayment.AdvancePaymentDO;
import com.mongoso.mgs.module.finance.dal.db.cashbank.acceptbill.AcceptBillDO;
import com.mongoso.mgs.module.finance.dal.mysql.advancepayment.AdvancePaymentMapper;
import com.mongoso.mgs.module.finance.dal.mysql.cashbank.acceptbill.AcceptBillMapper;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import com.mongoso.mgs.module.payment.dal.mysql.paymentrelation.PaymentRelationMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收款/付款核销服务实现类
 *
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class PaymentWriteOffServiceImpl implements PaymentWriteOffService {

    @Resource
    private PaymentRelationMapper paymentRelationMapper;

    @Resource
    private AdvancePaymentMapper advancePaymentMapper;

    @Resource
    private AcceptBillMapper acceptBillMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAdvancePaymentWriteOff(Long paymentId, Short formType, List<JSONObject> advancePayList) {
        if (CollectionUtils.isEmpty(advancePayList)) {
            return;
        }

        // 先删除原有的预收款核销关系
        deleteAdvancePaymentWriteOff(paymentId);

        // 获取预收款信息
        List<Long> advanceIds = advancePayList.stream()
                .map(json -> json.getLong("advanceId"))
                .collect(Collectors.toList());
        
        List<AdvancePaymentDO> advancePayments = advancePaymentMapper.selectBatchIds(advanceIds);
        Map<Long, AdvancePaymentDO> advanceMap = advancePayments.stream()
                .collect(Collectors.toMap(AdvancePaymentDO::getAdvanceId, advance -> advance));

        // 创建核销关系
        List<PaymentRelationDO> relations = new ArrayList<>();
        int rowNo = 1;
        for (JSONObject advanceJson : advancePayList) {
            Long advanceId = advanceJson.getLong("advanceId");
            BigDecimal writeOffAmt = advanceJson.getBigDecimal("payWriteAmt");
            
            AdvancePaymentDO advance = advanceMap.get(advanceId);
            if (advance == null) {
                log.warn("预收款不存在: {}", advanceId);
                continue;
            }

            PaymentRelationDO relation = new PaymentRelationDO();
            relation.setId(IDUtilX.getId());
            relation.setSort(rowNo++);
            relation.setRelatedDetailType(1);
            relation.setOrderId(paymentId);
            relation.setRelatedOrderId(advanceId);
            relation.setRelatedOrderCode(advance.getAdvanceCode());
            relation.setAmt(writeOffAmt);
            
            // 根据formType设置单据类型和关联单据类型
            if (formType == 1) { // 收款
                relation.setFormType(OrderTypeEnum.SALE_PAY.getType());
                relation.setRelatedOrderType(OrderTypeEnum.SALE_ADVANCE.getType());
            } else { // 付款
                relation.setFormType(OrderTypeEnum.PURCHASE_PAY.getType());
                relation.setRelatedOrderType(OrderTypeEnum.PURCHASE_ADVANCE.getType());
            }
            
            // 计算本币金额 (这里假设汇率为1，实际应该从payment表获取)
            relation.setExclTaxLocalCurrencyAmt(writeOffAmt);
            
            relations.add(relation);
        }

        // 批量插入
        if (!relations.isEmpty()) {
            paymentRelationMapper.insertBatch(relations);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAcceptBillWriteOff(Long paymentId, Short formType, List<Long> billList, List<BigDecimal> billWriteAmtList) {
        if (CollectionUtils.isEmpty(billList)) {
            return;
        }

        // 先删除原有的承兑汇票核销关系
        deleteAcceptBillWriteOff(paymentId);

        // 获取承兑汇票信息
        List<AcceptBillDO> acceptBills = acceptBillMapper.selectBatchIds(billList);
        Map<Long, AcceptBillDO> billMap = acceptBills.stream()
                .collect(Collectors.toMap(AcceptBillDO::getAcceptBillId, bill -> bill));

        // 创建核销关系
        List<PaymentRelationDO> relations = new ArrayList<>();
        int rowNo = 1;
        for (int i = 0; i < billList.size(); i++) {
            Long billId = billList.get(i);
            AcceptBillDO bill = billMap.get(billId);
            if (bill == null) {
                log.warn("承兑汇票不存在: {}", billId);
                continue;
            }

            // 核销金额：如果提供了具体金额则使用，否则使用汇票面额
            BigDecimal writeOffAmt = (billWriteAmtList != null && i < billWriteAmtList.size()) 
                    ? billWriteAmtList.get(i) 
                    : bill.getBillAmt();

            PaymentRelationDO relation = new PaymentRelationDO();
            relation.setId(IDUtilX.getId());
            relation.setSort(rowNo++);
            relation.setRelatedDetailType(2);
            relation.setOrderId(paymentId);
            relation.setRelatedOrderId(billId);
            relation.setRelatedOrderCode(bill.getTicketBillPackageNo());
            relation.setAmt(writeOffAmt);
            
            // 根据formType设置单据类型和关联单据类型
            if (formType == 1) { // 收款
                relation.setFormType(OrderTypeEnum.SALE_PAY.getType());
                relation.setRelatedOrderType(OrderTypeEnum.ACCEPT_BILL_RECEIPT.getType());
            } else { // 付款
                relation.setFormType(OrderTypeEnum.PURCHASE_PAY.getType());
                relation.setRelatedOrderType(OrderTypeEnum.ACCEPT_BILL_RECEIPT.getType());
            }
            
            // 计算本币金额 (这里假设汇率为1，实际应该从payment表获取)
            relation.setExclTaxLocalCurrencyAmt(writeOffAmt);
            
            relations.add(relation);
        }

        // 批量插入
        if (!relations.isEmpty()) {
            paymentRelationMapper.insertBatch(relations);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePaymentWriteOffRelations(Long paymentId) {
        paymentRelationMapper.delete(new LambdaQueryWrapperX<PaymentRelationDO>()
                .eq(PaymentRelationDO::getOrderId, paymentId)
                .in(PaymentRelationDO::getRelatedOrderType, 
                    OrderTypeEnum.SALE_ADVANCE.getType(),
                    OrderTypeEnum.PURCHASE_ADVANCE.getType(),
                    OrderTypeEnum.ACCEPT_BILL_RECEIPT.getType()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAdvancePaymentWriteOff(Long paymentId) {
        paymentRelationMapper.delete(new LambdaQueryWrapperX<PaymentRelationDO>()
                .eq(PaymentRelationDO::getOrderId, paymentId)
                .in(PaymentRelationDO::getRelatedOrderType,
                    OrderTypeEnum.SALE_ADVANCE.getType(),
                    OrderTypeEnum.PURCHASE_ADVANCE.getType()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAcceptBillWriteOff(Long paymentId) {
        paymentRelationMapper.delete(new LambdaQueryWrapperX<PaymentRelationDO>()
                .eq(PaymentRelationDO::getOrderId, paymentId)
                .in(PaymentRelationDO::getRelatedOrderType,
                        OrderTypeEnum.ACCEPT_BILL_PAYMENT.getType(),
                        OrderTypeEnum.ACCEPT_BILL_RECEIPT.getType()));
    }

    @Override
    public List<PaymentRelationDO> getAdvancePaymentWriteOff(Long paymentId) {
        return paymentRelationMapper.selectList(new LambdaQueryWrapperX<PaymentRelationDO>()
                .eq(PaymentRelationDO::getOrderId, paymentId)
                .in(PaymentRelationDO::getRelatedOrderType, 
                    OrderTypeEnum.SALE_ADVANCE.getType(),
                    OrderTypeEnum.PURCHASE_ADVANCE.getType())
                .orderByAsc(PaymentRelationDO::getSort));
    }

    @Override
    public List<PaymentRelationDO> getAcceptBillWriteOff(Long paymentId) {
        return paymentRelationMapper.selectList(new LambdaQueryWrapperX<PaymentRelationDO>()
                .eq(PaymentRelationDO::getOrderId, paymentId)
                .in(PaymentRelationDO::getRelatedOrderType,
                        OrderTypeEnum.ACCEPT_BILL_PAYMENT.getType(),
                        OrderTypeEnum.ACCEPT_BILL_RECEIPT.getType())
                .orderByAsc(PaymentRelationDO::getSort));
    }

    @Override
    public List<JSONObject> getAdvancePaymentWriteOffAsJson(Long paymentId) {
        List<PaymentRelationDO> relations = getAdvancePaymentWriteOff(paymentId);
        List<JSONObject> result = new ArrayList<>();
        
        for (PaymentRelationDO relation : relations) {
            JSONObject json = new JSONObject();
            json.put("advanceId", relation.getRelatedOrderId());
            json.put("payWriteAmt", relation.getAmt());
            json.put("advanceCode", relation.getRelatedOrderCode());
            result.add(json);
        }
        
        return result;
    }

    @Override
    public List<Long> getAcceptBillWriteOffAsIdList(Long paymentId) {
        List<PaymentRelationDO> relations = getAcceptBillWriteOff(paymentId);
        return relations.stream()
                .map(PaymentRelationDO::getRelatedOrderId)
                .collect(Collectors.toList());
    }

    @Override
    public BigDecimal calculateAdvanceWriteOffTotal(Long paymentId) {
        List<PaymentRelationDO> relations = getAdvancePaymentWriteOff(paymentId);
        return relations.stream()
                .map(PaymentRelationDO::getAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal calculateBillWriteOffTotal(Long paymentId) {
        List<PaymentRelationDO> relations = getAcceptBillWriteOff(paymentId);
        return relations.stream()
                .map(PaymentRelationDO::getAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
