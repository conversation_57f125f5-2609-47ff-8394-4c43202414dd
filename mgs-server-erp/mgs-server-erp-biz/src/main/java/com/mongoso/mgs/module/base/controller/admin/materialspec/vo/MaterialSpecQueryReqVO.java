package com.mongoso.mgs.module.base.controller.admin.materialspec.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 物料规格属性 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialSpecQueryReqVO {

    /** 主键 */
    private Long materialSpecId;

    /** 创建人ID */
    private Long createdId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 关联单ID */
    private Long relatedOrderId;

    /**
     * 关联单据
     */
    private String relatedOrderCode;

    /** 字典分类 */
    private Long dictCategoryId;

    /** 规格名 */
    private String specName;

    /** 规格值 */
    private String specValue;

    /** 行号 */
    private Integer rowNo;

    /**
     * 物料编码
     */
    private String materialCode;
}
