package com.mongoso.mgs.module.sale.dal.db.customerpriceplan;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/11/9 9:45
 * @description: 客户价格方案
 */
@TableName(value = "erp.u_customer_price_plan",autoResultMap = true)
@KeySequence("u_customer_price_plan") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerPricePlanDO extends OperateDO {

    /** 主键ID */
    @TableId
    private Long pricePlanId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核状态 */
    private Integer dataStatus;

//    /** 基本信息 */
//    @TableField(typeHandler = JsonbTypeHandler.class)
//    private JSONObject baseInfo;

//    /** 适用客户  fill = FieldFill.INSERT*/
//    @TableField(typeHandler = JsonbTypeHandler.class)
//    private List<JSONObject> customerInfo;
//
//    /** 商品价格 */
//    @TableField(typeHandler = JsonbTypeHandler.class)
//    private List<JSONObject> materialInfo;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
//    private Integer version;


    /** 方案编码 */
    private String planCode;

    /** 方案名称 */
    private String planName;

    /** 方案类型 */
    private String planTypeDictId;

    /** 重要指数 */
    private BigDecimal importIndex;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 是否生效 */
    private String isEffective;

    /** 开始日期 */
    private LocalDate startDate;

    /** 结束日期 */
    private LocalDate endDate;

}
