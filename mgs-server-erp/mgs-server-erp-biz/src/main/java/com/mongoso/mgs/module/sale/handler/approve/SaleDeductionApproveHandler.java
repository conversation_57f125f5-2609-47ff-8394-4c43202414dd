package com.mongoso.mgs.module.sale.handler.approve;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiongrant.vo.SaleOrderAddGrantReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo.CostSaleIncomeAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.enums.SpuConfigSourceOrderEnum;
import com.mongoso.mgs.module.dailycost.service.commissiongrant.CommissionGrantService;
import com.mongoso.mgs.module.dailycost.service.costsaleincome.CostSaleIncomeService;
import com.mongoso.mgs.module.dailycost.service.spuconfig.CostSpuConfigService;
import com.mongoso.mgs.module.finance.service.common.FinanceConnectService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderRespVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.sale.controller.admin.saledeductiondetail.vo.SaleDeductionDetailQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail.ErpSaleOrderDetailDO;
import com.mongoso.mgs.module.sale.dal.db.salededuction.SaleDeductionDO;
import com.mongoso.mgs.module.sale.dal.db.saledeductiondetail.SaleDeductionDetailDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.salededuction.SaleDeductionMapper;
import com.mongoso.mgs.module.sale.dal.mysql.saledeductiondetail.SaleDeductionDetailMapper;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.ErpSaleOrderDetailService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zhiling
 * @date: 2024/11/26 18:34
 * @description: 销售扣费单审批流程处理类
 */

@Component
public class SaleDeductionApproveHandler extends FlowApproveHandler<SaleDeductionDO> {

    @Resource
    private SaleDeductionDetailMapper saleDeductionDetailMapper;

    @Resource
    private SaleDeductionMapper saleDeductionMapper;

    @Resource
    private  ErpSaleOrderMapper erpSaleOrderMapper;

    @Resource
    private ErpSaleOrderDetailService erpSaleOrderDetailService;

    @Resource
    private ErpSaleOrderDetailMapper erpSaleOrderDetailMapper;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private FinanceConnectService financeConnectService;
    @Resource
    private CostSpuConfigService costSpuConfigService;

    @Resource
    private CostSaleIncomeService costSaleIncomeService;
    @Resource
    private CommissionGrantService commissionGrantService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(SaleDeductionDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(SaleDeductionDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(SaleDeductionDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getDeductionOrderId())
                .objCode(item.getDeductionOrderCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(SaleDeductionDO item,  BaseApproveRequest request)  {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getSaleOrderId());
        if (buttonType == DataButtonEnum.APPROVE.getKey()){
            //查询销售订单
            if (erpSaleOrder!=null && erpSaleOrder.getDataStatus()!= DataStatusEnum.APPROVED.key){
                failItem.setCode(item.getDeductionOrderCode());
                failItem.setReason("销售订单状态不是【已审核】,审核失败!");
                return false;
            }

//            if (erpSaleOrder!=null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type){
//                failItem.setCode(item.getDeductionOrderCode());
//                failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
//                return false;
//            }

            //查询销售扣费明细
            SaleDeductionDetailQueryReqVO saleDeductionDetailQuery = new SaleDeductionDetailQueryReqVO();
            saleDeductionDetailQuery.setDeductionOrderId(item.getDeductionOrderId());
            List<SaleDeductionDetailDO> saleDeductionDOS = saleDeductionDetailMapper.selectListOld(saleDeductionDetailQuery);

            //查询可扣费数量
            ErpSaleOrderDetailQueryReqVO erpSaleOrderDetailQuery = new ErpSaleOrderDetailQueryReqVO();
            erpSaleOrderDetailQuery.setSaleOrderId(item.getSaleOrderId());
            List<ErpSaleOrderDetailRespVO> detailRespVOS = erpSaleOrderDetailService.erpSaleDeductionQuoteList(erpSaleOrderDetailQuery);
            Map<Long, ErpSaleOrderDetailRespVO> erpDetailMap = detailRespVOS.stream().collect(Collectors.toMap(ErpSaleOrderDetailRespVO::getMaterialId, detailResp -> detailResp));

            //数量校验
            if (erpDetailMap.isEmpty()){
                failItem.setCode(item.getDeductionOrderCode());
                failItem.setReason("物料扣费数量已超出可扣费数量,审核失败!");
                return false;
            }

            // 按MaterialId累加DeductionQty
            Map<Long, BigDecimal> materialDeductionQtyMap = new HashMap<>();
            for (SaleDeductionDetailDO detailDO : saleDeductionDOS) {
                Long materialId = detailDO.getMaterialId();
                BigDecimal currentQty = materialDeductionQtyMap.getOrDefault(materialId, BigDecimal.ZERO);
                materialDeductionQtyMap.put(materialId, currentQty.add(detailDO.getDeductionQty()));
            }

            // 校验累加后的扣费数量是否超出可扣费数量
            for (Map.Entry<Long, BigDecimal> entry : materialDeductionQtyMap.entrySet()) {
                Long materialId = entry.getKey();
                BigDecimal totalDeductionQty = entry.getValue();

                ErpSaleOrderDetailRespVO saleOrderDetailRespVO = erpDetailMap.get(materialId);
                if (saleOrderDetailRespVO == null) {
                    failItem.setCode(item.getDeductionOrderCode());
                    failItem.setReason("扣费单明细异常，存在物料在销售订单中不存在,审核失败!");
                    return false;
                }

                if (totalDeductionQty.compareTo(saleOrderDetailRespVO.getCanDeductionQty()) > 0) {
                    failItem.setCode(item.getDeductionOrderCode());
                    failItem.setReason("扣费数量已超出可扣费数量,审核失败!");
                    return false;
                }
            }
        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()){
//            if (erpSaleOrder!=null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type){
//                failItem.setCode(item.getDeductionOrderCode());
//                failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
//                return false;
//            }
            //财务反审校验
            financeConnectService.checkOrder(item.getDeductionOrderId());
        }
        return true;
    }



    @Override
    public Integer handleBusinessData(SaleDeductionDO item,  BaseApproveRequest request) {
        Long id = item.getDeductionOrderId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        SaleDeductionDO saleDeductionDO = saleDeductionMapper.selectById(id);
        if (saleDeductionDO == null){
            return 1;
        }

        //查询销售订单
        ErpSaleOrderDetailQueryReqVO erpSaleOrderDetailQuery = new ErpSaleOrderDetailQueryReqVO();
        erpSaleOrderDetailQuery.setSaleOrderId(saleDeductionDO.getSaleOrderId());
        List<ErpSaleOrderDetailDO> erpSaleOrderBOS = erpSaleOrderDetailMapper.selectList(erpSaleOrderDetailQuery);

        //当前销售退货明细
        SaleDeductionDetailQueryReqVO detailQuery = new SaleDeductionDetailQueryReqVO();
        detailQuery.setDeductionOrderId(id);
        List<SaleDeductionDetailDO> detailDOS = saleDeductionDetailMapper.selectListOld(detailQuery);
        // 按MaterialId累加DeductionQty
        Map<Long, BigDecimal> materialQtyMap = new HashMap<>();
        for (SaleDeductionDetailDO detailDO : detailDOS) {
            Long materialId = detailDO.getMaterialId();
            BigDecimal currentQty = materialQtyMap.getOrDefault(materialId, BigDecimal.ZERO);
            materialQtyMap.put(materialId, currentQty.add(detailDO.getDeductionQty()));
        }

        // 以materialQtyMap作为循环体，确保每个物料的累加数量都能被处理
        for (Map.Entry<Long, BigDecimal> entry : materialQtyMap.entrySet()) {
            Long materialId = entry.getKey();
            BigDecimal totalDeductionQty = entry.getValue();

            // 查找匹配的销售订单明细并直接修改
            for (ErpSaleOrderDetailDO detail : erpSaleOrderBOS) {
                if (detail.getMaterialId().equals(materialId)) {
                    BigDecimal updateQty = BigDecimal.ZERO;
                    BigDecimal oldQty = detail.getFeedQty();

                    if (buttonType == DataButtonEnum.APPROVE.getKey()) {
                        updateQty = oldQty.add(totalDeductionQty);
                        if (updateQty.compareTo(detail.getOutQty()) >= 0){
                            detail.setIsMaterialFulReturned(1);
                        }
                    }

                    if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                        updateQty = oldQty.subtract(totalDeductionQty);
                        detail.setIsMaterialFulReturned(0);
                    }

                    detail.setFeedQty(updateQty);
                    break; // 只更新第一个匹配的记录
                }
            }
        }

        //更新物料完成状态和数量
        if (CollUtilX.isNotEmpty(erpSaleOrderBOS)){
            erpSaleOrderDetailMapper.updateBatch(erpSaleOrderBOS);
        }

        BigDecimal sumQty = BigDecimal.ZERO;
        List<DocumentRespBO> respBOList = erpSaleOrderDetailMapper.completeableQtyList(saleDeductionDO.getSaleOrderId());
        for (DocumentRespBO respBO : respBOList){
            sumQty = sumQty.add(respBO.getSumQty());
        }

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            //判断销售订单是否可下发销售退货单
//            if (count.compareTo(0)>0 && count.compareTo(erpSaleOrderBOS.size()) == 0){
            if (sumQty.compareTo(BigDecimal.ZERO) == 0){
                //更新为 不可下发销售退货单
                ErpSaleOrderDO erpSaleOrderDO = new ErpSaleOrderDO();
                erpSaleOrderDO.setSaleOrderId(saleDeductionDO.getSaleOrderId());
                erpSaleOrderDO.setIsCanIssueReturn((short) 1);
                erpSaleOrderDO.setIsCanIssueFee((short) 1);
                erpSaleOrderDO.setIsCanIssueExchange((short) 1);
                erpSaleOrderMapper.updateById(erpSaleOrderDO);
            }

            //新增日成本销售收入单
            costSaleIncomeAdd(saleDeductionDO,detailDOS);

            //新增日成本提成发放
            saleOrderAddGrant(saleDeductionDO,detailDOS);
        }

        //更新为可下发销售扣费单
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            ErpSaleOrderDO erpSaleOrderDO = new ErpSaleOrderDO();
            erpSaleOrderDO.setSaleOrderId(saleDeductionDO.getSaleOrderId());
            erpSaleOrderDO.setIsCanIssueReturn((short) 0);
            erpSaleOrderDO.setIsCanIssueFee((short) 0);
            erpSaleOrderDO.setIsCanIssueExchange((short) 0);
            erpSaleOrderMapper.updateById(erpSaleOrderDO);

            //删除日成本销售收入单
            costSaleIncomeService.relatedUpFormIdDel(saleDeductionDO.getDeductionOrderId());

            //删除日成本提成发放
            commissionGrantService.sourceOrderIdDel(saleDeductionDO.getDeductionOrderId(),3);
        }

        //日成本初始化
        this.dailyCostInit(buttonType,saleDeductionDO, detailDOS);

        //销售销售订单状态
        erpSaleOrderService.editChildrenOrderCount(saleDeductionDO.getSaleOrderId(), buttonType);

        //更新销售退货单状态
        saleDeductionDO.setApprovedBy(loginUser.getFullUserName());
        saleDeductionDO.setApprovedDt(LocalDateTime.now());
        saleDeductionDO.setDataStatus(dataStatus);

        int updateCount = saleDeductionMapper.updateById(saleDeductionDO);

        //数据进入财务
        financeData(saleDeductionDO, buttonType);

        return updateCount;
    }

    private void dailyCostInit(Integer buttonType,SaleDeductionDO saleDeductionDO, List<SaleDeductionDetailDO> detailDOS) {
        ArrayList<CostSpuConfigAditReqVO> costSpuConfigList = new ArrayList<>();
        for (SaleDeductionDetailDO deductionDetailDO : detailDOS) {
            CostSpuConfigAditReqVO costSpuConfigAditReqVO = new CostSpuConfigAditReqVO();
            costSpuConfigAditReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey());
            costSpuConfigAditReqVO.setRelatedUpOrderId(deductionDetailDO.getDeductionOrderId());
            costSpuConfigAditReqVO.setRelatedUpOrderCode(saleDeductionDO.getDeductionOrderCode());
            costSpuConfigAditReqVO.setRelatedOrderDetailId(deductionDetailDO.getDeductionOrderDetailId());
            costSpuConfigAditReqVO.setMaterialId(deductionDetailDO.getMaterialId());
            costSpuConfigAditReqVO.setMaterialCode(deductionDetailDO.getMaterialCode());
            costSpuConfigAditReqVO.setFormDt(saleDeductionDO.getFormDt());
            costSpuConfigAditReqVO.setOrderType(SpuConfigSourceOrderEnum.SALE_DEDUCTION.code);
            costSpuConfigList.add(costSpuConfigAditReqVO);
        }
        costSpuConfigService.costSpuConfigOperate(buttonType, costSpuConfigList);
    }

    private void financeData(SaleDeductionDO saleReturnDO, Integer buttonType){
        //组装主表
        ErpSaleOrderRespVO erpSaleOrderRespVO = erpSaleOrderService.erpSaleOrderDetail(saleReturnDO.getSaleOrderId());
        erpSaleOrderRespVO.setSourceFormType(OrderTypeEnum.SALE_CHARGE_SLIPS_ORDER.type.shortValue());
        erpSaleOrderRespVO.setTotalAmt(saleReturnDO.getDeductionAmt().negate());
        erpSaleOrderRespVO.setFormDt(saleReturnDO.getFormDt());
        erpSaleOrderRespVO.setCustomerId(saleReturnDO.getCustomerId());
        erpSaleOrderRespVO.setCurrencyDictId(saleReturnDO.getCurrencyDictId());
        erpSaleOrderRespVO.setCurrencyDictName(saleReturnDO.getCurrencyDictName());
        erpSaleOrderRespVO.setRelatedOrderId(saleReturnDO.getSaleOrderId());
        erpSaleOrderRespVO.setRelatedOrderCode(saleReturnDO.getSaleOrderCode());
        erpSaleOrderRespVO.setDirectorId(saleReturnDO.getDirectorId());
        erpSaleOrderRespVO.setDirectorOrgId(saleReturnDO.getDirectorOrgId());
        erpSaleOrderRespVO.setSaleOrderId(saleReturnDO.getDeductionOrderId());
        erpSaleOrderRespVO.setSaleOrderCode(saleReturnDO.getDeductionOrderCode());
        //组装子表
        List<SaleDeductionDetailDO> saleReturnDetailDOS = saleDeductionDetailMapper.selectList(new LambdaQueryWrapper<SaleDeductionDetailDO>()
                .eq(SaleDeductionDetailDO::getDeductionOrderId, saleReturnDO.getDeductionOrderId())
        );
        List<ErpSaleOrderDetailRespVO> detailList = new ArrayList<>();
        for (SaleDeductionDetailDO detailDO : saleReturnDetailDOS){
            ErpSaleOrderDetailRespVO respVO = new ErpSaleOrderDetailRespVO();
            respVO.setRowNo(detailDO.getRowNo());
            respVO.setMaterialCode(detailDO.getMaterialCode());
            respVO.setMaterialId(detailDO.getMaterialId());
            respVO.setMainUnitDictId(detailDO.getMainUnitDictId());
            respVO.setInclTaxAmt(detailDO.getInclTaxAmt().negate());
            respVO.setExclTaxAmt(detailDO.getExclTaxAmt().negate());
            respVO.setQty(detailDO.getDeductionQty().negate());
            respVO.setInclTaxPrice(detailDO.getInclTaxPrice());
            respVO.setExclTaxPrice(detailDO.getExclTaxPrice());
            respVO.setInvoiceTypeId(detailDO.getInvoiceTypeId());
            respVO.setInvoiceTypeName(detailDO.getInvoiceTypeName());
            respVO.setTaxRate(detailDO.getTaxRate());
            respVO.setCalculatType(detailDO.getCalculatType());
            respVO.setSaleOrderDetailId(detailDO.getDeductionOrderDetailId());

            detailList.add(respVO);
        }
        erpSaleOrderRespVO.setDetailList(detailList);

        //源头单据id
        erpSaleOrderRespVO.setOriginOrderId(saleReturnDO.getSaleOrderId());

        financeConnectService.saleInsertFinance(erpSaleOrderRespVO, buttonType);
    }

    //新增日成本销售收入单
    private void costSaleIncomeAdd(SaleDeductionDO saleDeductionDO, List<SaleDeductionDetailDO> detailDOS) {
        for (SaleDeductionDetailDO detailDO : detailDOS){
            CostSaleIncomeAditReqVO reqVO = new CostSaleIncomeAditReqVO();
            reqVO.setRelatedUpFormCode(saleDeductionDO.getDeductionOrderCode());
            reqVO.setRelatedUpFormId(saleDeductionDO.getDeductionOrderId());
            reqVO.setRelatedOrderCode(saleDeductionDO.getSaleOrderCode());
            reqVO.setFormDt(saleDeductionDO.getFormDt());
            reqVO.setOrderType((short) 2);
            reqVO.setUndertakeOrgId(saleDeductionDO.getCompanyOrgId()+"");
            reqVO.setMaterialId(detailDO.getMaterialId());
            reqVO.setMaterialCode(detailDO.getMaterialCode());
            reqVO.setRelatedRowNo((int) detailDO.getRowNo());
            reqVO.setQty(detailDO.getDeductionQty());
            reqVO.setExclTaxUnitPrice(detailDO.getExclTaxPrice());
            reqVO.setTotalAmt(MathUtilX.getAmt(detailDO.getExclTaxPrice(), detailDO.getDeductionQty()));
            costSaleIncomeService.costSaleIncomeAdd(reqVO);
        }
    }

    private void saleOrderAddGrant(SaleDeductionDO saleDeductionDO, List<SaleDeductionDetailDO> detailDOS) {
        SaleOrderAddGrantReqVO reqVO = new SaleOrderAddGrantReqVO();
        reqVO.setSourceOrderId(saleDeductionDO.getDeductionOrderId());
        reqVO.setSourceOrderCode(saleDeductionDO.getDeductionOrderCode());
        reqVO.setSaleOrderId(saleDeductionDO.getSaleOrderId());
        reqVO.setFormType(3);
        reqVO.setFormDt(saleDeductionDO.getFormDt());
        reqVO.setDeductionDetailList(detailDOS);
        commissionGrantService.saleOrderAddGrant(reqVO);
    }

}