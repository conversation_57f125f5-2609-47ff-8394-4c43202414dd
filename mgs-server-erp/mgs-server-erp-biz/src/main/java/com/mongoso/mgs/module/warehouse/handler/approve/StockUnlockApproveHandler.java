package com.mongoso.mgs.module.warehouse.handler.approve;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.warehouse.controller.admin.stockunlock.vo.detail.StockUnlockDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.stocklock.StockLockDO;
import com.mongoso.mgs.module.warehouse.dal.db.stockunlock.StockUnlockDO;
import com.mongoso.mgs.module.warehouse.dal.db.stockunlock.StockUnlockDetailDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stocklock.StockLockDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stocklock.StockLockMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stockunlock.StockUnlockDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stockunlock.StockUnlockMapper;
import com.mongoso.mgs.module.warehouse.service.stocklock.StockLockDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Fashoin.Liu
 * @date: 2024/12/3 18:34
 * @description: 库存解锁单审批流程处理类
 */

@Component
public class StockUnlockApproveHandler extends FlowApproveHandler<StockUnlockDO> {

    @Resource
    private StockUnlockMapper stockUnlockMapper;

    @Resource
    private StockUnlockDetailMapper stockUnlockDetailMapper;

    @Resource
    private StockLockMapper stockLockMapper;

    @Resource
    private StockLockDetailMapper stockLockDetailMapper;

    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;

    @Resource
    private StockLockDetailService stockLockDetailService;

    @Resource
    private ErpBaseService erpBaseService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(StockUnlockDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(StockUnlockDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(StockUnlockDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getUnlockId())
                .objCode(item.getUnlockCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(StockUnlockDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            Long unlockId = item.getUnlockId();
            Long lockId = item.getLockId();

            StockLockDO stockLockDO = stockLockMapper.selectById(lockId);
            if(stockLockDO == null){
                failItem.setCode(item.getUnlockCode());
                failItem.setReason("关联的库存锁定单已删除,不可操作审核");
                return false;
            }
            if(stockLockDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()){
                failItem.setCode(item.getUnlockCode());
                failItem.setReason("关联的库存锁定单未审核,不可操作审核");
                return false;
            }

            //查询库存解锁单(解锁数量)
            List<DocumentRespBO> unlockQtyRespList = stockUnlockDetailMapper.unlockQtyList(unlockId);
            Map<Long, DocumentRespBO> unlockQtyMap = unlockQtyRespList.stream().collect(
                    Collectors.toMap(DocumentRespBO::getFieldId, doc -> doc));

            //查询库存锁定单(可解锁数量)
            List<DocumentRespBO> unlockableRespList = stockLockDetailMapper.unlockableQtyList(lockId);
            Map<Long, BigDecimal> unlockableQtyMap = unlockableRespList.stream().collect(
                    Collectors.toMap(DocumentRespBO::getFieldId, DocumentRespBO::getSumQty));

            //判断解锁数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : unlockQtyMap.entrySet()){
                Long lockDetailId = entry.getKey();
                // 可解锁数量
                BigDecimal unlockableQty = unlockableQtyMap.get(lockDetailId);
                DocumentRespBO documentRespBO = unlockQtyMap.get(lockDetailId);
                if (unlockableQty == null || documentRespBO.getSumQty().compareTo(unlockableQty) > 0){
                    failItem.setCode(item.getUnlockCode());
                    String warehouseOrgName = erpBaseService.getOrgNameById(documentRespBO.getWarehouseOrgId());
                    failItem.setReason("物料编码【"+documentRespBO.getCode()+"】仓库【"+ warehouseOrgName +"】解锁数量不允许超过可解锁数量!");
                    return false;
                }
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            Long unlockId = item.getUnlockId();

            //查询库存解锁单(解锁数量)
            List<DocumentRespBO> unlockQtyRespList = stockUnlockDetailMapper.unlockQtyList(unlockId);
            Map<Long, DocumentRespBO> unlockQtyMap = unlockQtyRespList.stream().collect(
                    Collectors.toMap(DocumentRespBO::getMaterialStockId, doc -> doc));
            List<Long> materialStockIdList = unlockQtyRespList.stream().map(DocumentRespBO::getMaterialStockId).collect(Collectors.toList());

            //查询物料库存
            List<DocumentRespBO> materialStockRespList = erpMaterialStockMapper.queryStockAvailableQtyList(materialStockIdList);
            Map<Long, BigDecimal> materialStockQtyMap = materialStockRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    obj -> {
                        BigDecimal stockQty = obj.getStockQty() == null ? BigDecimal.ZERO : obj.getStockQty();
                        BigDecimal lockedQty = obj.getLockedQty() == null ? BigDecimal.ZERO : obj.getLockedQty();
                        return stockQty.subtract(lockedQty);
                    }));

            //反审判断解锁数量是否超出库存可用数量
            for (Map.Entry<Long, DocumentRespBO> entry : unlockQtyMap.entrySet()){
                Long materialStockId = entry.getKey();
                BigDecimal stockQty = materialStockQtyMap.get(materialStockId);
                DocumentRespBO documentRespBO = unlockQtyMap.get(materialStockId);
                if (documentRespBO.getSumQty().compareTo(stockQty) > 0){
                    failItem.setCode(item.getUnlockCode());
                    String warehouseOrgName = erpBaseService.getOrgNameById(documentRespBO.getWarehouseOrgId());
                    String reason = "物料编码【"+documentRespBO.getCode()+"】仓库【"+ warehouseOrgName +"】解锁数量超过物料库存可用数量, 不可操作反审核";
                    failItem.setReason(reason);
                    return false;
                }
            }

        }

        return true;
    }


    @Override
    public Integer handleBusinessData(StockUnlockDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        Integer buttonType = request.getButtonType();
        Long id = item.getUnlockId();
        Integer dataStatus = request.getDataStatus();

        StockUnlockDO stockUnlockDO = stockUnlockMapper.selectById(id);
        //审核结果处理
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            //查询库存解锁单
            StockUnlockDetailQueryReqVO stockUnlockDetailQueryReqVO = new StockUnlockDetailQueryReqVO();
            stockUnlockDetailQueryReqVO.setUnlockId(id);
            List<StockUnlockDetailDO> stockUnlockRespList = stockUnlockDetailMapper.selectList(stockUnlockDetailQueryReqVO);

            for(StockUnlockDetailDO stockUnlockDetailDO : stockUnlockRespList){
                // 增加物料库存锁定数量
                Long lockDetailId = stockUnlockDetailDO.getLockDetailId();
                BigDecimal unlockQty = stockUnlockDetailDO.getUnlockQty();
                stockLockDetailService.updateUnlockedQty(lockDetailId, unlockQty);
            }
        }

        //反审核结果处理
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //查询库存解锁单
            StockUnlockDetailQueryReqVO stockUnlockDetailQueryReqVO = new StockUnlockDetailQueryReqVO();
            stockUnlockDetailQueryReqVO.setUnlockId(id);
            List<StockUnlockDetailDO> stockUnlockRespList = stockUnlockDetailMapper.selectList(stockUnlockDetailQueryReqVO);

            for(StockUnlockDetailDO stockUnlockDetailDO : stockUnlockRespList){
                // 增加物料库存锁定数量
                Long lockDetailId = stockUnlockDetailDO.getLockDetailId();
                BigDecimal unlockQty = stockUnlockDetailDO.getUnlockQty().negate();
                stockLockDetailService.updateUnlockedQty(lockDetailId, unlockQty);
            }
        }

        //更新业务数据
        stockUnlockDO.setApprovedBy(loginUser.getFullUserName());
        stockUnlockDO.setApprovedDt(LocalDateTime.now());
        stockUnlockDO.setDataStatus(dataStatus);
        Integer updateCount = stockUnlockMapper.updateById(stockUnlockDO);

        return updateCount;
    }

}