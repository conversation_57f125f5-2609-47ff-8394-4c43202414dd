package com.mongoso.mgs.module.produce.controller.admin.report.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * @author: zhiling
 * @date: 2025/2/24 16:20
 * @description: 生产订单跟踪报表 响应参数
 */

@Data
public class ProdWorkReportRespVO implements Serializable {

    /** 生产订单ID */
    private Long prodOrderId;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 生产工单id */
    private Long prodWorkId;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 单据状态 */
    private Integer formStatus;
    private String formStatusDictName;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate planEndDate;

    /** 实际完成日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate actEndDate;

    /** 工单计划总数 */
    private BigDecimal workPlanTotalQty = BigDecimal.ZERO;

    /** 实际生产数量 */
    private BigDecimal workActTotalQty = BigDecimal.ZERO;

    /** 预估用量 */
    private BigDecimal estimatedQty = BigDecimal.ZERO;

    /** 是否入库 */
    private Integer isInbound = 0;

    /** 已入库数量 */
    private BigDecimal inboundedQty = BigDecimal.ZERO;

    /** 入库状态["未入库","部分入库","全部入库","无需入库"] */
    private Integer prodInboundStatus= 0;

    /** 领料数量 */
    private BigDecimal pickQty = BigDecimal.ZERO;

    /** 退料数量 */
    private BigDecimal pickReturnQty = BigDecimal.ZERO;

    /** 已领料数量 */
    private BigDecimal receivedQty = BigDecimal.ZERO;

    /** 待领料数量 */
    private BigDecimal receiveQty = BigDecimal.ZERO;

    /** 物料bom数 */
    private Integer worksBomNum;

    /** 领料状态["未领料","部分领料","全部领料","无需领料"] */
    private Integer pickingFormStatus = 0;

    /** 派工单状态 */
    private Integer dispatchWorkStatus = 0;

    /** 派工数量 */
    private BigDecimal dispatchQty = BigDecimal.ZERO;

    /** 产量 */
    private BigDecimal totalYield = BigDecimal.ZERO;

    /** 工期进度 */
    private String progress;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 物料来源 */
    private Integer materialSourceDictId;
    private String materialSourceDictName;

    /** 主单位(基本单位) */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;


}
