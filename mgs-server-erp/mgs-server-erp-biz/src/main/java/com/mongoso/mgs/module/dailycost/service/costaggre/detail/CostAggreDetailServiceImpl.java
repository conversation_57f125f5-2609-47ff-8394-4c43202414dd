package com.mongoso.mgs.module.dailycost.service.costaggre.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.CostAggreQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.CostAggreRespVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailRespVO;
import com.mongoso.mgs.module.dailycost.dal.db.costaggre.CostAggreDetailDO;
import com.mongoso.mgs.module.dailycost.dal.mysql.costaggre.CostAggreDetailMapper;
import com.mongoso.mgs.module.dailycost.dal.mysql.costaggre.CostAggreMapper;
import com.mongoso.mgs.module.dailycost.service.costaggre.CostAggreService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.cost.enums.ErrorCodeConstants.*;


/**
 * 成本归集明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CostAggreDetailServiceImpl implements CostAggreDetailService {

    @Resource
    private CostAggreDetailMapper aggreDetailMapper;
    @Resource
    private CostAggreMapper costAggreMapper;
    @Resource
    private CostAggreService costAggreService;
    @Resource
    private ErpBaseService erpBaseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long costAggreDetailAdd(CostAggreDetailAditReqVO reqVO) {
        // 插入
        CostAggreDetailDO aggreDetail = BeanUtilX.copy(reqVO, CostAggreDetailDO::new);
        aggreDetailMapper.insert(aggreDetail);
        // 返回
        return aggreDetail.getAggreDetailId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long costAggreDetailEdit(CostAggreDetailAditReqVO reqVO) {
        // 校验存在
        this.costAggreDetailValidateExists(reqVO.getAggreDetailId());
        // 更新
        CostAggreDetailDO aggreDetail = BeanUtilX.copy(reqVO, CostAggreDetailDO::new);
        aggreDetailMapper.updateById(aggreDetail);
        // 返回
        return aggreDetail.getAggreDetailId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void costAggreDetailDel(Long aggreDetailId) {
        // 校验存在
        this.costAggreDetailValidateExists(aggreDetailId);
        // 删除
        aggreDetailMapper.deleteById(aggreDetailId);
    }

    private CostAggreDetailDO costAggreDetailValidateExists(Long aggreDetailId) {
        CostAggreDetailDO aggreDetail = aggreDetailMapper.selectById(aggreDetailId);
        if (aggreDetail == null) {
            // throw exception(AGGRE_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "成本归集明细不存在");
        }
        return aggreDetail;
    }

    @Override
    public CostAggreDetailRespVO costAggreDetailDetail(Long aggreDetailId) {
        CostAggreDetailDO data = aggreDetailMapper.selectById(aggreDetailId);
        return BeanUtilX.copy(data, CostAggreDetailRespVO::new);
    }

    @Override
    public List<CostAggreDetailRespVO> costAggreDetailList(CostAggreDetailQueryReqVO reqVO) {
        List<CostAggreDetailDO> data = aggreDetailMapper.selectList(reqVO);
        return BeanUtilX.copy(data, CostAggreDetailRespVO::new);
    }

    @Override
    public PageResult<CostAggreDetailRespVO> costAggreDetailPage(CostAggreDetailPageReqVO reqVO) {
        IPage<CostAggreDetailRespVO> respVOIPage = aggreDetailMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<CostAggreDetailRespVO> respVOPageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoProperties(reqVO.getAggreType(), respVOPageResult.getList());
        return respVOPageResult;
    }

    private void batchFillVoProperties(Integer aggreType, List<CostAggreDetailRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> aggreIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        for (CostAggreDetailRespVO detailRespVO : respVOList) {
            orgIdList.add(detailRespVO.getUndertakeOrgId());
            aggreIdList.add(detailRespVO.getAggreId());
        }

        aggreIdList = aggreIdList.stream().distinct().collect(Collectors.toList());
        CostAggreQueryReqVO costAggreQueryReqVO = new CostAggreQueryReqVO();
        costAggreQueryReqVO.setAggreIdList(aggreIdList);
        costAggreQueryReqVO.setAggreType(aggreType);
        List<CostAggreRespVO> aggreRespVOList = costAggreService.costAggreList(costAggreQueryReqVO);
        if (CollUtilX.isEmpty(aggreRespVOList)){
            return;
        }

        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(orgIdList);
        Map<Long, CostAggreRespVO> costAggreRespVOMap = aggreRespVOList.stream().collect(Collectors.toMap(CostAggreRespVO::getAggreId, costAggreRespVO -> costAggreRespVO));
        for (CostAggreDetailRespVO detailRespVO : respVOList) {

            //成本归集单
            if (detailRespVO.getAggreId() != null){
                CostAggreRespVO costAggreRespVO = costAggreRespVOMap.get(detailRespVO.getAggreId());
                BeanUtilX.copy(costAggreRespVO, detailRespVO);
            }

            //承担对象
            if (detailRespVO.getUndertakeOrgId() != null){
                detailRespVO.setUndertakeOrgName(orgNameMap.get(detailRespVO.getUndertakeOrgId()));
            }
        }
    }

}
