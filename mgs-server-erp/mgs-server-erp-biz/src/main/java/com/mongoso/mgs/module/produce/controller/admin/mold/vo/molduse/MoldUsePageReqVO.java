package com.mongoso.mgs.module.produce.controller.admin.mold.vo.molduse;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 模具领用单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MoldUsePageReqVO extends PageParam {

    /** 领用单号 */
    private String moldUseCode;
    private Integer returnedStatus;// 归还状态

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核状态 */
    private Integer dataStatus;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;


}
