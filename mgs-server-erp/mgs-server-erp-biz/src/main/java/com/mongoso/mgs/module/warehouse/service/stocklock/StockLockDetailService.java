package com.mongoso.mgs.module.warehouse.service.stocklock;

import java.math.BigDecimal;
import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.detail.StockLockDetailRespVO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 库存锁定单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface StockLockDetailService {

    /**
     * 创建库存锁定单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long stockLockDetailAdd(@Valid StockLockDetailAditReqVO reqVO);

    /**
     * 更新库存锁定单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long stockLockDetailEdit(@Valid StockLockDetailAditReqVO reqVO);

    /**
     * 删除库存锁定单明细
     *
     * @param id 编号
     */
    void stockLockDetailDel(Long id);

    /**
     * 获得库存锁定单明细信息
     *
     * @param id 编号
     * @return 库存锁定单明细信息
     */
    StockLockDetailRespVO stockLockDetailDetail(Long id);

    /**
     * 获得库存锁定单明细列表
     *
     * @param reqVO 查询条件
     * @return 库存锁定单明细列表
     */
    List<StockLockDetailRespVO> stockLockDetailList(@Valid StockLockDetailQueryReqVO reqVO);

    /**
     * 获得库存锁定单明细引用列表
     *
     * @param reqVO 查询条件
     * @return 库存锁定单明细列表
     */
    List<StockLockDetailRespVO> stockLockDetailQuoteList(@Valid StockLockDetailQueryReqVO reqVO);

    /**
     * 获得库存锁定单明细分页
     *
     * @param reqVO 查询条件
     * @return 库存锁定单明细分页
     */
    PageResult<StockLockDetailRespVO> stockLockDetailPage(@Valid StockLockDetailPageReqVO reqVO);

    /**
     * 更新物料库存锁定数量
     *
     * @param lockDetailId 明细ID
     * @param unlockQty 解锁数量
     * @return
     */
    void updateUnlockedQty(Long lockDetailId, BigDecimal unlockQty);

}
