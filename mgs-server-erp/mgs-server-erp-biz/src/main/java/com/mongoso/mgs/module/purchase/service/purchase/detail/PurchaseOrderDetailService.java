package com.mongoso.mgs.module.purchase.service.purchase.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseOrderBO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购订单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseOrderDetailService {

    /**
     * 创建采购订单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseOrderDetailAdd(@Valid PurchaseOrderDetailAditReqVO reqVO);

    /**
     * 更新采购订单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseOrderDetailEdit(@Valid PurchaseOrderDetailAditReqVO reqVO);

    /**
     * 删除采购订单明细
     *
     * @param purchaseOrderDetailId 编号
     */
    void purchaseOrderDetailDel(Long purchaseOrderDetailId);

    /**
     * 获得采购订单明细信息
     *
     * @param purchaseOrderDetailId 编号
     * @return 采购订单明细信息
     */
    PurchaseOrderDetailRespVO purchaseOrderDetailDetail(Long purchaseOrderDetailId);

    /**
     * 获得采购订单明细列表
     *
     * @param reqVO 查询条件
     * @return 采购订单明细列表
     */
    List<PurchaseOrderDetailRespVO> purchaseOrderDetailList(@Valid PurchaseOrderDetailQueryReqVO reqVO);

    /**
     * 获得采购订单被引用明细列表
     *
     * @param reqVO 查询条件
     * @return 采购订单明细列表
     */
    List<PurchaseOrderDetailRespVO> purchaseOrderDetailQuotedList(PurchaseOrderDetailQueryReqVO reqVO);


    List<PurchaseOrderDetailRespVO> detailListForPurchaseEdit(PurchaseOrderDetailQueryReqVO reqVO);


    List<PurchaseOrderDetailRespVO> erpPurchaseOrderDetailListForChange(@Valid PurchaseOrderDetailQueryReqVO reqVO);

    /**
     * 获得采购订单明细分页
     *
     * @param reqVO 查询条件
     * @return 采购订单明细分页
     */
    PageResult<PurchaseOrderDetailRespVO> purchaseOrderDetailPage(@Valid PurchaseOrderDetailPageReqVO reqVO);

    PageResult<PurchaseOrderDetailRespVO> materialHistoryPricePage(PurchaseOrderDetailPageReqVO reqVO);

    /**
     * 更新生产委外采购订单的入库数量
     * @param inboundQty
     * @param purchaseOrderId
     */
    void computeProdOutsourceInboundQty(Long purchaseOrderId, Long materialId, BigDecimal inboundQty);

    /**
     * 更新采购订单已收货数量
     *
     * @param purchaseOrderDetailId 采购订单明细ID
     * @param operQty 操作数量
     * @return
     */
    void updateReceiptedQtyByReceipt(Long purchaseOrderDetailId, BigDecimal operQty);

    /**
     * 更新采购订单已收货数量
     *
     * @param purchaseOrderDetailId 采购订单明细ID
     * @param receiptQty 操作数量
     * @return
     */
    void updateReceiptedQtyByNotice(Long purchaseOrderDetailId, BigDecimal receiptQty);

    /**
     * 更新采购订单已入库数量
     *
     * @param purchaseOrderDetailId 采购订单明细ID
     * @param inboundQty 操作数量
     * @return
     */
    void updateInboundedQty(Long purchaseOrderDetailId, BigDecimal inboundQty);

    /**
     * 更新采购订单已入库数量
     *
     * @param purchaseOrderDetailId 采购订单明细ID
     * @param inboundQty 操作数量
     * @return
     */
    void updateInboundedQtyByInbound(Long purchaseOrderDetailId, BigDecimal inboundQty);

    List<PurchaseOrderDetailRespVO> purchaseOrderDetailListForProd(PurchaseOrderDetailQueryReqVO reqVO);

    /**
     * 回写采购订单
     * @param purchaseOrderBO
     */
    void writeBackPurchaseOrder(PurchaseOrderBO purchaseOrderBO);
}
