package com.mongoso.mgs.module.produce.controller.admin.erpprodorder;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.module.produce.service.materialanalysis.bo.IssuedBO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo.*;
import com.mongoso.mgs.module.produce.service.erpprodorder.ErpProdOrderService;

/**
 * 生产订单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class ErpProdOrderController {

    @Resource
    private ErpProdOrderService erpProdOrderService;

    @OperateLog("生产订单添加或编辑")
    @PostMapping("/erpProdOrderAdit")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:adit')")
    public ResultX<Long> erpProdOrderAdit(@Valid @RequestBody ErpProdOrderAditReqVO reqVO) {
        return success(reqVO.getProdOrderId() == null
                            ? erpProdOrderService.erpProdOrderAdd(reqVO)
                            : erpProdOrderService.erpProdOrderEdit(reqVO));
    }

    @OperateLog("生产订单删除")
    @PostMapping("/erpProdOrderDel")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:delete')")
    public ResultX<Boolean> erpProdOrderDel(@Valid @RequestBody ErpProdOrderPrimaryReqVO reqVO) {
        erpProdOrderService.erpProdOrderDel(reqVO.getProdOrderId());
        return success(true);
    }

    @OperateLog("生产订单详情")
    @PostMapping("/erpProdOrderDetail")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:query')")
    public ResultX<ErpProdOrderRespVO> erpProdOrderDetail(@Valid @RequestBody ErpProdOrderPrimaryReqVO reqVO) {
        return success(erpProdOrderService.erpProdOrderDetail(reqVO.getProdOrderId()));
    }

    @OperateLog("生产订单列表")
    @PostMapping("/erpProdOrderList")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:query')")
    @DataPermission
    public ResultX<List<ErpProdOrderRespVO>> erpProdOrderList(@Valid @RequestBody ErpProdOrderQueryReqVO reqVO) {
        return success(erpProdOrderService.erpProdOrderList(reqVO));
    }

    @OperateLog("生产订单列表")
    @PostMapping("/erpProdOrderQuotedList")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:query')")
    public ResultX<List<ErpProdOrderRespVO>> erpProdOrderQuotedList(@Valid @RequestBody ErpProdOrderQueryReqVO reqVO) {
        return success(erpProdOrderService.erpProdOrderList(reqVO));
    }

    @OperateLog("生产订单分页")
    @PostMapping("/erpProdOrderPage")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:query')")
    @DataPermission
    public ResultX<PageResult<ErpProdOrderRespVO>> erpProdOrderPage(@Valid @RequestBody ErpProdOrderPageReqVO reqVO) {
        return success(erpProdOrderService.erpProdOrderPage(reqVO));
    }

    @OperateLog("生产订单分页")
    @PostMapping("/erpProdOrderQuotedPage")
    @PreAuthorize("@ss.hasPermission('erpProdOrder:query')")
    public ResultX<PageResult<ErpProdOrderRespVO>> erpProdOrderQuotedPage(@Valid @RequestBody ErpProdOrderPageReqVO reqVO) {
        return success(erpProdOrderService.erpProdOrderPage(reqVO));
    }

    @OperateLog("销售变更单查询变更前销售订单详情")
    @PostMapping("/erpProdOrderChangQueryDetail")
    @PreAuthorize("@ss.hasPermission('erpSaleOrder:query')")
    public ResultX<ErpProdOrderRespVO> erpProdOrderChangQueryDetail(@Valid @RequestBody ErpProdOrderPageReqVO reqVO) {
        ErpProdOrderRespVO respVO = erpProdOrderService.erpProdOrderChangQueryDetail(reqVO.getProdOrderChangeId());
        return success(respVO);
    }
    
    @OperateLog("生产订单删除")
    @PostMapping("/erpProdOrderDelBatch")
    @PreAuthorize("@ss.hasPermission('device:del')")
    public ResultX<BatchResult> erpProdOrderDelBatch(@Valid @RequestBody IdReq reqVO) {
        return erpProdOrderService.erpProdOrderDelBatch(reqVO);
    }

    @OperateLog("生产订单审核")
    @PostMapping("/erpProdOrderApprove")
    @PreAuthorize("@ss.hasPermission('device:adit')")
    public ResultX<BatchResult> erpProdOrderApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = erpProdOrderService.erpProdOrderApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("生产订单回调接口")
    @PostMapping("/erpProdOrderFlowCallback")
    public ResultX<Object> erpProdOrderFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(erpProdOrderService.erpProdOrderFlowCallback(reqVO));
    }

    @OperateLog("生产订单状态变更")
    @PostMapping("/erpProdOrderStatus")
    public ResultX<Boolean> erpProdOrderStatus(@Valid @RequestBody ErpProdOrderPrimaryReqVO reqVO) {
        return success(erpProdOrderService.erpProdOrderStatus(reqVO));
    }

    @OperateLog("下发生产订单和生产工单")
    @PostMapping("/erpProdOrderFastAdd")
    public ResultX<Boolean> erpProdOrderFastAdd(@Valid @RequestBody IssuedBO reqVO) {
        return success(erpProdOrderService.erpProdOrderFastAdd(reqVO));
    }

    @OperateLog("主子表生产订单明细分页")
    @PostMapping("/queryErpProdOrderDetailPage")
    @PreAuthorize("@ss.hasPermission('erpSaleOrder:query')")
    @DataPermission
    public ResultX<PageResult<ErpProdOrderResp>> queryErpProdOrderDetailPage(@Valid @RequestBody ErpProdOrderPageReqVO reqVO) {
        PageResult<ErpProdOrderResp> pageResult = erpProdOrderService.queryErpProdOrderDetailPage(reqVO);
        return success(pageResult);
    }
}
