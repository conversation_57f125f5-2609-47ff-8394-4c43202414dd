package com.mongoso.mgs.module.utility.dal.mysql.utilitycost;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo2.BomPageReqVO;
import com.mongoso.mgs.module.utility.controller.admin.utilitycost.vo.UtilityCostPageReqVO;
import com.mongoso.mgs.module.utility.controller.admin.utilitycost.vo.UtilityCostQueryReqVO;
import com.mongoso.mgs.module.utility.controller.admin.utilitycost.vo.UtilityCostRespVO;
import com.mongoso.mgs.module.utility.dal.db.utilitycost.UtilityCostDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 水电气费用 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UtilityCostMapper extends BaseMapperX<UtilityCostDO> {

    default PageResult<UtilityCostDO> selectPageOld(UtilityCostPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<UtilityCostDO>lambdaQueryX()
                .likeIfPresent(UtilityCostDO::getUtilityCostCode, reqVO.getUtilityCostCode())
                .eqIfPresent(UtilityCostDO::getUtilityLogId, reqVO.getUtilityLogId())
                .eqIfPresent(UtilityCostDO::getYesterdayReadQty, reqVO.getYesterdayReadQty())
                .eqIfPresent(UtilityCostDO::getTodayReadQty, reqVO.getTodayReadQty())
                .eqIfPresent(UtilityCostDO::getReadDifference, reqVO.getReadDifference())
                .eqIfPresent(UtilityCostDO::getTodayUsage, reqVO.getTodayUsage())
                .eqIfPresent(UtilityCostDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(UtilityCostDO::getUtilityAmt, reqVO.getUtilityAmt())
                .eqIfPresent(UtilityCostDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(UtilityCostDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(UtilityCostDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(UtilityCostDO::getCreatedDt));
    }



    default PageResult<UtilityCostDO> selectPage(UtilityCostPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<UtilityCostDO>lambdaQueryX()
                .eqIfPresent(UtilityCostDO::getUtilityLogId, reqVO.getUtilityLogId())
                .eqIfPresent(UtilityCostDO::getUtilityConfigId, reqVO.getUtilityConfigId())
                .eqIfPresent(UtilityCostDO::getUtilityArchivesId, reqVO.getUtilityArchivesId())
                .eqIfPresent(UtilityCostDO::getCompanyId, reqVO.getCompanyId())
                .eqIfPresent(UtilityCostDO::getYesterdayReadQty, reqVO.getYesterdayReadQty())
                .eqIfPresent(UtilityCostDO::getTodayReadQty, reqVO.getTodayReadQty())
                .eqIfPresent(UtilityCostDO::getReadDifference, reqVO.getReadDifference())
                .eqIfPresent(UtilityCostDO::getTodayUsage, reqVO.getTodayUsage())
                .eqIfPresent(UtilityCostDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(UtilityCostDO::getUtilityAmt, reqVO.getUtilityAmt())
                .eqIfPresent(UtilityCostDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(UtilityCostDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .likeIfPresent(UtilityCostDO::getUtilityCostCode, reqVO.getUtilityCostCode())
                .betweenIfPresent(UtilityCostDO::getUtilityCostDate, reqVO.getStartUtilityCostDate(), reqVO.getEndUtilityCostDate())
                .betweenIfPresent(UtilityCostDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(UtilityCostDO::getCreatedDt));
    }

    default List<UtilityCostDO> selectListOld(UtilityCostQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<UtilityCostDO>lambdaQueryX()
                .likeIfPresent(UtilityCostDO::getUtilityCostCode, reqVO.getUtilityCostCode())
                .eqIfPresent(UtilityCostDO::getUtilityLogId, reqVO.getUtilityLogId())
                .eqIfPresent(UtilityCostDO::getYesterdayReadQty, reqVO.getYesterdayReadQty())
                .eqIfPresent(UtilityCostDO::getTodayReadQty, reqVO.getTodayReadQty())
                .eqIfPresent(UtilityCostDO::getReadDifference, reqVO.getReadDifference())
                .eqIfPresent(UtilityCostDO::getTodayUsage, reqVO.getTodayUsage())
                .eqIfPresent(UtilityCostDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(UtilityCostDO::getUtilityAmt, reqVO.getUtilityAmt())
                .eqIfPresent(UtilityCostDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(UtilityCostDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(UtilityCostDO::getUtilityCostDate, reqVO.getStartUtilityCostDate(), reqVO.getEndUtilityCostDate())
                .betweenIfPresent(UtilityCostDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(UtilityCostDO::getCreatedDt));
    }

    default List<UtilityCostDO> selectList(UtilityCostQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<UtilityCostDO>lambdaQueryX()
                .likeIfPresent(UtilityCostDO::getUtilityCostCode, reqVO.getUtilityCostCode())
                .eqIfPresent(UtilityCostDO::getUtilityLogId, reqVO.getUtilityLogId())
                .eqIfPresent(UtilityCostDO::getYesterdayReadQty, reqVO.getYesterdayReadQty())
                .eqIfPresent(UtilityCostDO::getTodayReadQty, reqVO.getTodayReadQty())
                .eqIfPresent(UtilityCostDO::getReadDifference, reqVO.getReadDifference())
                .eqIfPresent(UtilityCostDO::getTodayUsage, reqVO.getTodayUsage())
                .eqIfPresent(UtilityCostDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(UtilityCostDO::getUtilityAmt, reqVO.getUtilityAmt())
                .eqIfPresent(UtilityCostDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(UtilityCostDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(UtilityCostDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(UtilityCostDO::getCreatedDt));
    }

    IPage<UtilityCostRespVO> selectMonthPage(Page<BomPageReqVO> objectPage, @Param("reqVO")UtilityCostPageReqVO reqVO);
}