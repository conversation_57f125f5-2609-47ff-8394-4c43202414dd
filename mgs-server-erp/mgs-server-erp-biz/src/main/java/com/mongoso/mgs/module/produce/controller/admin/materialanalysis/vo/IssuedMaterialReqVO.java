package com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo;

import com.mongoso.mgs.module.produce.service.materialanalysis.bo.IssuedMaterialBO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 下发对象
 *
 * <AUTHOR>
 */
@Data
public class IssuedMaterialReqVO implements Serializable {

    private Long materialId;// 物料id
    private BigDecimal issuedQty;//下发数量
    private String remark;// 备注

}
