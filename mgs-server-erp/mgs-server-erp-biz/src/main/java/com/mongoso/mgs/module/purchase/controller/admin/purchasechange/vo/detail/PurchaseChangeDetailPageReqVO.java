package com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购订单变更明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseChangeDetailPageReqVO extends PageParam {

    /** 采购订单变更单号 */
    private String purchaseChangeCode;

    /** 采购订单号 */
    private String purchaseOrderCode;

    /** 采购订单类型 */
    private String purchaseTypeDictId;

    /** 采购订单变更单ID */
    private Long purchaseChangeId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 币种 */
    private String currencyDictId;

    /** 主体公司 */
    private String companyOrgId;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 付款条件 */
    private String paymentTermsDictId;

    /** 物料名称 */
    private String materialName;

    /** 物料类别 */
    private String materialCategoryDictId;

    /** 规格型号 */
    private String specModel;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 行号 */
    private Integer rowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位 */
    private String mainUnitDictId;

    /** 采购数量 */
    private BigDecimal purchaseQty;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 单价(含税) */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    private Long purchaseOrderId;
    private Short purchaseOrderBizType;
    private List<Short> purchaseOrderBizTypeList;

}
