package com.mongoso.mgs.module.warehouse.controller.admin.materialassembly;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.common.vo.QuoteReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.materialassembly.MaterialAssemblyDO;
import com.mongoso.mgs.module.warehouse.service.materialassembly.MaterialAssemblyService;

/**
 * 组装拆卸单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warehouse")
@Validated
public class MaterialAssemblyController {

    @Resource
    private MaterialAssemblyService materialAssemblyService;

    @OperateLog("组装拆卸单添加或编辑")
    @PostMapping("/materialAssemblyAdit")
    @PreAuthorize("@ss.hasPermission('materialAssembly:adit')")
    public ResultX<Long> materialAssemblyAdit(@Valid @RequestBody MaterialAssemblyAditReqVO reqVO) {
        return success(reqVO.getAssemblyId() == null
                            ? materialAssemblyService.materialAssemblyAdd(reqVO)
                            : materialAssemblyService.materialAssemblyEdit(reqVO));
    }

    @OperateLog("组装拆卸单删除")
    @PostMapping("/materialAssemblyDel")
    @PreAuthorize("@ss.hasPermission('materialAssembly:delete')")
    public ResultX<Boolean> materialAssemblyDel(@Valid @RequestBody MaterialAssemblyPrimaryReqVO reqVO) {
        materialAssemblyService.materialAssemblyDel(reqVO.getAssemblyId());
        return success(true);
    }

    @OperateLog("组装拆卸单批量删除")
    @PostMapping("/materialAssemblyDelBatch")
    @PreAuthorize("@ss.hasPermission('materialAssembly:delete')")
    public ResultX<BatchResult> materialAssemblyDelBatch(@Valid @RequestBody IdsReqVO reqVO) {
        return materialAssemblyService.materialAssemblyDelBatch(reqVO);
    }

    @OperateLog("组装拆卸单详情")
    @PostMapping("/materialAssemblyDetail")
    @PreAuthorize("@ss.hasPermission('materialAssembly:query')")
    public ResultX<MaterialAssemblyRespVO> materialAssemblyDetail(@Valid @RequestBody MaterialAssemblyPrimaryReqVO reqVO) {
        return success(materialAssemblyService.materialAssemblyDetail(reqVO.getAssemblyId()));
    }


    @OperateLog("组装拆卸单引用明细列表")
    @PostMapping("/materialAssemblyQuoteDetailPageList")
    @PreAuthorize("@ss.hasPermission('materialAssembly:query')")
    public ResultX<MaterialAssemblyRespVO> materialAssemblyQuoteDetailPageList(@Valid @RequestBody MaterialAssemblyQuoteReqVO reqVO) {
        return materialAssemblyService.materialAssemblyQuoteDetailPageList(reqVO);
    }


    @OperateLog("组装拆卸单列表")
    @PostMapping("/materialAssemblyList")
    @PreAuthorize("@ss.hasPermission('materialAssembly:query')")
    @DataPermission
    public ResultX<List<MaterialAssemblyRespVO>> materialAssemblyList(@Valid @RequestBody MaterialAssemblyQueryReqVO reqVO) {
        return success(materialAssemblyService.materialAssemblyList(reqVO));
    }

    @OperateLog("组装拆卸单分页")
    @PostMapping("/materialAssemblyPage")
    @PreAuthorize("@ss.hasPermission('materialAssembly:query')")
    @DataPermission
    public ResultX<PageResult<MaterialAssemblyRespVO>> materialAssemblyPage(@Valid @RequestBody MaterialAssemblyPageReqVO reqVO) {
        return success(materialAssemblyService.materialAssemblyPage(reqVO));
    }

    @OperateLog("组装拆卸单审核")
    @PostMapping("/materialAssemblyApprove")
    @PreAuthorize("@ss.hasPermission('materialAssembly:adit')")
    public ResultX<BatchResult> erpInboundApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(materialAssemblyService.materialAssemblyApprove(reqVO));
    }

    @OperateLog("组装拆卸单审核回调接口")
    @PostMapping("/materialAssemblyFlowCallback")
    public ResultX<Object> erpInboundFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(materialAssemblyService.materialAssemblyFlowCallback(reqVO));
    }

}
