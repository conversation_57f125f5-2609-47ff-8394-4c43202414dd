package com.mongoso.mgs.module.purchase.service.receiptnotice.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.PurchaseReceiptNoticeMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.detail.PurchaseReceiptNoticeDetailMapper;
import com.mongoso.mgs.module.purchase.service.purchase.detail.PurchaseOrderDetailService;
import com.mongoso.mgs.module.sale.controller.admin.invtypemanage.vo.InvTypeManageQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购收货通知单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReceiptNoticeDetailServiceImpl implements PurchaseReceiptNoticeDetailService {

    @Resource
    private PurchaseReceiptNoticeDetailMapper receiptNoticeDetailMapper;

    @Resource
    private PurchaseReceiptNoticeMapper receiptNoticeMapper;

    @Resource
    private InvTypeManageMapper invTypeManageMapper;

    @Lazy
    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Override
    public Long purchaseReceiptNoticeDetailAdd(PurchaseReceiptNoticeDetailAditReqVO reqVO) {
        // 插入
        PurchaseReceiptNoticeDetailDO receiptNoticeDetail = BeanUtilX.copy(reqVO, PurchaseReceiptNoticeDetailDO::new);
        receiptNoticeDetailMapper.insert(receiptNoticeDetail);
        // 返回
        return receiptNoticeDetail.getReceiptNoticeDetailId();
    }

    @Override
    public Long purchaseReceiptNoticeDetailEdit(PurchaseReceiptNoticeDetailAditReqVO reqVO) {
        // 校验存在
        this.purchaseReceiptNoticeDetailValidateExists(reqVO.getReceiptNoticeDetailId());
        // 更新
        PurchaseReceiptNoticeDetailDO receiptNoticeDetail = BeanUtilX.copy(reqVO, PurchaseReceiptNoticeDetailDO::new);
        receiptNoticeDetailMapper.updateById(receiptNoticeDetail);
        // 返回
        return receiptNoticeDetail.getReceiptNoticeDetailId();
    }

    @Override
    public void purchaseReceiptNoticeDetailDel(Long receiptNoticeDetailId) {
        // 校验存在
        this.purchaseReceiptNoticeDetailValidateExists(receiptNoticeDetailId);
        // 删除
        receiptNoticeDetailMapper.deleteById(receiptNoticeDetailId);
    }

    private PurchaseReceiptNoticeDetailDO purchaseReceiptNoticeDetailValidateExists(Long receiptNoticeDetailId) {
        PurchaseReceiptNoticeDetailDO receiptNoticeDetail = receiptNoticeDetailMapper.selectById(receiptNoticeDetailId);
        if (receiptNoticeDetail == null) {
            // throw exception(RECEIPT_NOTICE_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "采购收货通知单明细不存在");
        }
        return receiptNoticeDetail;
    }

    @Override
    public PurchaseReceiptNoticeDetailRespVO purchaseReceiptNoticeDetailDetail(Long receiptNoticeDetailId) {
        PurchaseReceiptNoticeDetailDO data = receiptNoticeDetailMapper.selectById(receiptNoticeDetailId);
        return BeanUtilX.copy(data, PurchaseReceiptNoticeDetailRespVO::new);
    }

    @Override
    public List<PurchaseReceiptNoticeDetailRespVO> purchaseReceiptNoticeDetailList(PurchaseReceiptNoticeDetailQueryReqVO reqVO) {
        List<PurchaseReceiptNoticeDetailRespVO> noticeDetailRespVOList = receiptNoticeDetailMapper.queryList(reqVO);
        this.batchFillVoProperties(noticeDetailRespVOList);
        return noticeDetailRespVOList;
    }

    @Override
    public List<PurchaseReceiptNoticeDetailRespVO> purchaseReceiptNoticeDetailQuotedList(PurchaseReceiptNoticeDetailQueryReqVO reqVO) {
        List<PurchaseReceiptNoticeDetailRespVO> noticeDetailRespVOList = BeanUtilX.copyList(receiptNoticeDetailMapper.selectList(reqVO),
                PurchaseReceiptNoticeDetailRespVO :: new);
        this.batchFillVoProperties(noticeDetailRespVOList);
        return noticeDetailRespVOList;
    }

    private void batchFillVoProperties(List<PurchaseReceiptNoticeDetailRespVO> noticeDetailRespVOList) {

        if (CollUtilX.isEmpty(noticeDetailRespVOList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(),  CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);


        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for (PurchaseReceiptNoticeDetailRespVO detailResp: noticeDetailRespVOList){
            materialIdList.add(detailResp.getMaterialId());
//            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
            directorIdList.add(detailResp.getDirectorId());
            directorOrgIdList.add(detailResp.getDirectorOrgId());
            supplierIdList.add(detailResp.getRelatedSupplierId());
        }

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        //查询关联供应
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for (PurchaseReceiptNoticeDetailRespVO detailResp: noticeDetailRespVOList){
            // 采购单类型
            String purchaseTypeDictId = detailResp.getPurchaseTypeDictId();
            if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
                purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
                detailResp.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            // 基本单位
            String mainUnitDictId = detailResp.getMainUnitDictId();
            if(StrUtilX.isNotEmpty(mainUnitDictId)) {
                mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                detailResp.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            // 审核状态
            if(detailResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + detailResp.getDataStatus();
                detailResp.setDataStatusDictName(dictMap.get(dataStatus));;
            }

            //责任人
            detailResp.setDirectorName(directorMap.get(detailResp.getDirectorId()));
            //责任部门属性填充
            detailResp.setDirectorOrgName(directorOrgMap.get(detailResp.getDirectorOrgId()));
            //供应商信息
            detailResp.setRelatedSupplierName(supplierNameMap.get(detailResp.getRelatedSupplierId()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialDO!=null){
                detailResp.setMaterialCode(erpMaterialDO.getMaterialCode());
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
                detailResp.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detailResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detailResp.setSpecModel(erpMaterialDO.getSpecModel());
            }
            if(detailResp.getWarehouseOrgId()!=null){
                String warehouseOrgName = erpBaseService.getOrgNameById(detailResp.getWarehouseOrgId());
                detailResp.setWarehouseOrgName(warehouseOrgName);
            }
        }

    }


    @Override
    public PageResult<PurchaseReceiptNoticeDetailRespVO> purchaseReceiptNoticeDetailPage(PurchaseReceiptNoticeDetailPageReqVO reqVO) {
        IPage<PurchaseReceiptNoticeDetailRespVO> respVOIPage = receiptNoticeDetailMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<PurchaseReceiptNoticeDetailRespVO> detailRespVOPageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoProperties(detailRespVOPageResult.getList());
        return detailRespVOPageResult;
    }

    @Override
    public Map<Long, BigDecimal> detailNoticeQtyMap(Long purchaseOrderId) {
        List<PurchaseReceiptNoticeDetailDO> receiptNoticeDetailDOList = receiptNoticeDetailMapper.selectNoticeQtyListByPurchaseId(purchaseOrderId);
        return receiptNoticeDetailDOList.stream().collect(Collectors
                .toMap(PurchaseReceiptNoticeDetailDO::getMaterialId, PurchaseReceiptNoticeDetailDO::getNoticeQty));
    }

    public void updateReceiptedQty(Long receiptNoticeDetailId, BigDecimal receiptQty){

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        PurchaseReceiptNoticeDetailDO receiptNoticeDetailDO = receiptNoticeDetailMapper.selectById(receiptNoticeDetailId);
        if(receiptNoticeDetailDO == null){
            return;
        }
        //已收货数量
        receiptNoticeDetailDO.setReceiptedQty(receiptNoticeDetailDO.getReceiptedQty().add(receiptQty));
        //可收货数量
        BigDecimal receiptableQty = receiptNoticeDetailDO.getNoticeQty().subtract(receiptNoticeDetailDO.getReceiptedQty());
        receiptNoticeDetailDO.setReceiptableQty(receiptableQty);
        // 是否全部已收货, 可入库数量为0,则为全部完成
        if(receiptableQty.compareTo(BigDecimal.ZERO) == 0){
            receiptNoticeDetailDO.setIsMaterialFullReceipted(1);
        }else{
            receiptNoticeDetailDO.setIsMaterialFullReceipted(0);
        }

        receiptNoticeDetailDO.setUpdatedBy(loginUser.getFullUserName());
        receiptNoticeDetailDO.setUpdatedDt(LocalDateTime.now());
        receiptNoticeDetailMapper.updateById(receiptNoticeDetailDO);

        // 更新采购收货通知单
        Long receiptNoticeId = receiptNoticeDetailDO.getReceiptNoticeId();
        PurchaseReceiptNoticeDO receiptNoticeDO = receiptNoticeMapper.selectById(receiptNoticeId);
        Long unFullReceiptedCount = receiptNoticeDetailMapper.selectUnFullReceiptedCount(receiptNoticeId);
        if(unFullReceiptedCount == 0){
            receiptNoticeDO.setIsFullReceipted(1);
        }else{
            receiptNoticeDO.setIsFullReceipted(0);
        }
        receiptNoticeDO.setUpdatedBy(loginUser.getFullUserName());
        receiptNoticeDO.setUpdatedDt(LocalDateTime.now());
        receiptNoticeMapper.updateById(receiptNoticeDO);

        //更新采购订单已收货数量
        purchaseOrderDetailService.updateReceiptedQtyByNotice(receiptNoticeDetailDO.getPurchaseOrderDetailId(), receiptQty);
    }

    public void updateInboundedQty(Long receiptNoticeDetailId, BigDecimal inboundedQty) {

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        PurchaseReceiptNoticeDetailDO receiptNoticeDetailDO = receiptNoticeDetailMapper.selectById(receiptNoticeDetailId);
        if (receiptNoticeDetailDO == null) {
            return;
        }
        //已入库数量
        receiptNoticeDetailDO.setInboundedQty(receiptNoticeDetailDO.getInboundedQty().add(inboundedQty));

        //更新明细
        receiptNoticeDetailDO.setUpdatedBy(loginUser.getFullUserName());
        receiptNoticeDetailDO.setUpdatedDt(LocalDateTime.now());
        receiptNoticeDetailMapper.updateById(receiptNoticeDetailDO);

        //更新采购订单已入库数量
        purchaseOrderDetailService.updateInboundedQty(receiptNoticeDetailDO.getPurchaseOrderDetailId(), inboundedQty);
    }

}
