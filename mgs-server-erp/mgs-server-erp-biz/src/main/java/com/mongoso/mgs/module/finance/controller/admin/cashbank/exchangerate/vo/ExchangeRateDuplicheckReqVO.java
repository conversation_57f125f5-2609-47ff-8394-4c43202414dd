package com.mongoso.mgs.module.finance.controller.admin.cashbank.exchangerate.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 汇率验重 DuplicheckReqVO
 *
 * <AUTHOR>
 */
@Data
public class ExchangeRateDuplicheckReqVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 原币币种字典ID */
    @NotNull(message = "原币币种字典ID不能为空")
    private Long originalCurrencyDictId;

    /** 本币币种字典ID */
    @NotNull(message = "本币币种字典ID不能为空")
    private Long localCurrencyDictId;
}
