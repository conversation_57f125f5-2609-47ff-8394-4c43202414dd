package com.mongoso.mgs.module.warehouse.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDO;
import org.springframework.stereotype.Component;

/**
 * @author: Fashon.Liu
 * @date: 2024/12/9 9:40
 * @description: 入库单回调处理类
 */

@Component
public class ErpInboundFlowCallBackHandler extends FlowCallbackHandler<ErpInboundDO> {

    protected ErpInboundFlowCallBackHandler(FlowApproveHandler<ErpInboundDO> approveHandler) {
        super(approveHandler);
    }

}
