package com.mongoso.mgs.module.dailycost.controller.admin.commissiontask;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.*;
import com.mongoso.mgs.module.dailycost.service.commissiontask.CommissionTaskService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 提成任务 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CommissionTaskController {

    @Resource
    private CommissionTaskService commissionTaskService;

    @OperateLog("提成任务添加或编辑")
    @PostMapping("/commissionTaskAdit")
    @PreAuthorize("@ss.hasPermission('commissionTask:adit')")
    public ResultX<Long> commissionTaskAdit(@Valid @RequestBody CommissionTaskAditReqVO reqVO) {
        return success(reqVO.getCommissionTaskId() == null
                            ? commissionTaskService.commissionTaskAdd(reqVO)
                            : commissionTaskService.commissionTaskEdit(reqVO));
    }

    @OperateLog("提成任务删除")
    @PostMapping("/commissionTaskDel")
    @PreAuthorize("@ss.hasPermission('commissionTask:delete')")
    public ResultX<Boolean> commissionTaskDel(@Valid @RequestBody CommissionTaskPrimaryReqVO reqVO) {
        commissionTaskService.commissionTaskDel(reqVO.getCommissionTaskId());
        return success(true);
    }

    @OperateLog("提成任务详情")
    @PostMapping("/commissionTaskDetail")
    @PreAuthorize("@ss.hasPermission('commissionTask:query')")
    public ResultX<CommissionTaskRespVO> commissionTaskDetail(@Valid @RequestBody CommissionTaskPrimaryReqVO reqVO) {
        return success(commissionTaskService.commissionTaskDetail(reqVO.getCommissionTaskId()));
    }

    @OperateLog("提成任务列表")
    @PostMapping("/commissionTaskList")
    @PreAuthorize("@ss.hasPermission('commissionTask:query')")
    @DataPermission
    public ResultX<List<CommissionTaskRespVO>> commissionTaskList(@Valid @RequestBody CommissionTaskQueryReqVO reqVO) {
        return success(commissionTaskService.commissionTaskList(reqVO));
    }

    @OperateLog("提成任务分页")
    @PostMapping("/commissionTaskPage")
    @PreAuthorize("@ss.hasPermission('commissionTask:query')")
    @DataPermission
    public ResultX<PageResult<CommissionTaskRespVO>> commissionTaskPage(@Valid @RequestBody CommissionTaskPageReqVO reqVO) {
        return success(commissionTaskService.commissionTaskPage(reqVO));
    }

    @OperateLog("提成任务审核")
    @PostMapping("/commissionTaskApprove")
    @PreAuthorize("@ss.hasPermission('commissionTask:adit')")
    public ResultX<BatchResult> commissionTaskApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(commissionTaskService.commissionTaskApprove(reqVO));
    }

    @OperateLog("提成任务批量删除")
    @PostMapping("/commissionTaskDelBatch")
    @PreAuthorize("@ss.hasPermission('commissionTask:del')")
    public ResultX<BatchResult> commissionTaskDelBatch(@Valid @RequestBody IdReq reqVO) {
        return commissionTaskService.commissionTaskDelBatch(reqVO);
    }

    @OperateLog("提成规则回调接口")
    @PostMapping("/commissionTaskFlowCallback")
    public ResultX<Object> commissionTaskFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(commissionTaskService.commissionTaskFlowCallback(reqVO));
    }

}
