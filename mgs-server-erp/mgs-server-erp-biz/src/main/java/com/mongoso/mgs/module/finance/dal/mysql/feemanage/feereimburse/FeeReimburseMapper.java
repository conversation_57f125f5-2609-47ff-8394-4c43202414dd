package com.mongoso.mgs.module.finance.dal.mysql.feemanage.feereimburse;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimburse.vo.FeeReimbursePageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feereimburse.vo.FeeReimburseQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feereimburse.FeeReimburseDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 费用报销 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FeeReimburseMapper extends BaseMapperX<FeeReimburseDO> {

    default PageResult<FeeReimburseDO> selectPageOld(FeeReimbursePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<FeeReimburseDO>lambdaQueryX()
                .eqIfPresent(FeeReimburseDO::getOutBillStatus, reqVO.getOutBillStatus())
                .likeIfPresent(FeeReimburseDO::getReimburseCode, reqVO.getReimburseCode())
                .likeIfPresent(FeeReimburseDO::getReimburseName, reqVO.getReimburseName())
                .eqIfPresent(FeeReimburseDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeReimburseDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(FeeReimburseDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeReimburseDO::getReimburserId, reqVO.getReimburserId())
                .likeIfPresent(FeeReimburseDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(FeeReimburseDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(FeeReimburseDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeReimburseDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeReimburseDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeReimburseDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeReimburseDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeReimburseDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeReimburseDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(FeeReimburseDO::getCreatedDt));
    }



    default PageResult<FeeReimburseDO> selectPage(FeeReimbursePageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<FeeReimburseDO>lambdaQueryX()
                .eqIfPresent(FeeReimburseDO::getOutBillStatus, reqVO.getOutBillStatus())
                .likeIfPresent(FeeReimburseDO::getReimburseCode, reqVO.getReimburseCode())
                .likeIfPresent(FeeReimburseDO::getReimburseName, reqVO.getReimburseName())
                .eqIfPresent(FeeReimburseDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeReimburseDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(FeeReimburseDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeReimburseDO::getReimburserId, reqVO.getReimburserId())
                .likeIfPresent(FeeReimburseDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(FeeReimburseDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(FeeReimburseDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeReimburseDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeReimburseDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeReimburseDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeReimburseDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeReimburseDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeReimburseDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(FeeReimburseDO::getCreatedDt));
    }

    default List<FeeReimburseDO> selectListOld(FeeReimburseQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<FeeReimburseDO>lambdaQueryX()
                .eqIfPresent(FeeReimburseDO::getOutBillStatus, reqVO.getOutBillStatus())
                .likeIfPresent(FeeReimburseDO::getReimburseCode, reqVO.getReimburseCode())
                .likeIfPresent(FeeReimburseDO::getReimburseName, reqVO.getReimburseName())
                .eqIfPresent(FeeReimburseDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeReimburseDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(FeeReimburseDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeReimburseDO::getReimburserId, reqVO.getReimburserId())
                .likeIfPresent(FeeReimburseDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(FeeReimburseDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(FeeReimburseDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeReimburseDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeReimburseDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeReimburseDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeReimburseDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeReimburseDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeReimburseDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(FeeReimburseDO::getCreatedDt));
    }

    default List<FeeReimburseDO> selectList(FeeReimburseQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<FeeReimburseDO>lambdaQueryX()
                .eqIfPresent(FeeReimburseDO::getOutBillStatus, reqVO.getOutBillStatus())
                .likeIfPresent(FeeReimburseDO::getReimburseCode, reqVO.getReimburseCode())
                .likeIfPresent(FeeReimburseDO::getReimburseName, reqVO.getReimburseName())
                .eqIfPresent(FeeReimburseDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeReimburseDO::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(FeeReimburseDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeReimburseDO::getReimburserId, reqVO.getReimburserId())
                .likeIfPresent(FeeReimburseDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(FeeReimburseDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .eqIfPresent(FeeReimburseDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeReimburseDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeReimburseDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeReimburseDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeReimburseDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeReimburseDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeReimburseDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(FeeReimburseDO::getCreatedDt));
    }

}