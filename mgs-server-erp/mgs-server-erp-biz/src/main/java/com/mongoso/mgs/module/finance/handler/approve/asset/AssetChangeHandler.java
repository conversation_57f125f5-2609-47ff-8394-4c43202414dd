package com.mongoso.mgs.module.finance.handler.approve.asset;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.finance.dal.db.asset.assetchange.AssetChangeDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciate.AssetDepreciateDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetregister.AssetRegisterDO;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetchange.AssetChangeMapper;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetregister.AssetRegisterMapper;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;


@Component
public class AssetChangeHandler extends FlowApproveHandler<AssetChangeDO> {
    @Resource
    private AssetChangeMapper assetChangeMapper;

    @Resource
    private AssetRegisterMapper assetRegisterMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(AssetChangeDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(AssetChangeDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(AssetChangeDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getAssetChangeId())
                .objCode(item.getChangeCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(AssetChangeDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            AssetRegisterDO assetRegisterDO = assetRegisterMapper.selectById(item.getAssetId());
            if (assetRegisterDO.getDataStatus() != DataStatusEnum.APPROVED.key.shortValue()){
                failItem.setCode(item.getChangeCode());
                failItem.setReason("关联的资产登记未审核");
                return false;
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

        }

        return true;
    }


    @Override
    public Integer handleBusinessData(AssetChangeDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = item.getAssetChangeId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        AssetChangeDO exist  = assetChangeMapper.selectById(id);
        exist.setApprovedBy(loginUser.getFullUserName());
        exist.setApprovedDt(LocalDateTime.now());
        exist.setDataStatus(dataStatus.shortValue());
        // todo 审核需上游 已审核，反审核 需下游 未审核
        // 审核通过，反审核通过
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            AssetRegisterDO assetRegisterDO = new AssetRegisterDO();
            assetRegisterDO.setAssetRegisterId(exist.getAssetId());
            assetRegisterDO.setAssetStatusConfigId(exist.getChangeStatusId());
            assetRegisterMapper.updateById(assetRegisterDO);
        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){

        }
        //更新业务数据
        Integer updateCount = assetChangeMapper.updateById(exist);

        return updateCount;
    }
}
