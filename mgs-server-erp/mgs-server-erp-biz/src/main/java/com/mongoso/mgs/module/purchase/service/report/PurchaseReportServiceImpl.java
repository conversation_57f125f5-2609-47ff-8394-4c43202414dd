package com.mongoso.mgs.module.purchase.service.report;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.DateUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.purchase.controller.admin.report.bo.MaterialPurchaseReportBO;
import com.mongoso.mgs.module.purchase.controller.admin.report.vo.MaterialPurchaseReportItemRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.report.vo.MaterialPurchaseReportPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.report.vo.MaterialPurchaseReportRespVO;
import com.mongoso.mgs.module.purchase.dal.mysql.report.PurchaseReportMapper;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockChangeStatRespVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 采购报表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReportServiceImpl implements PurchaseReportService {

    @Resource
    private PurchaseReportMapper purchaseReportMapper;

    @Override
    public PageResult<MaterialPurchaseReportRespVO> queryMaterialPurchaseReport(MaterialPurchaseReportPageReqVO reqVO) {

        //获取标题
        List<String> titleList = getTitleList(reqVO);

        IPage<MaterialPurchaseReportRespVO> respVOIPage = purchaseReportMapper.queryMaterialPurchaseReport(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<MaterialPurchaseReportRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }

        List<Long> materialIdList = pageResult.getList().stream().map(MaterialPurchaseReportRespVO::getMaterialId)
                .collect(Collectors.toList());
        //查询明细列表
        List<MaterialPurchaseReportBO> detailList = purchaseReportMapper.queryMaterialPurchaseReportDetailList(reqVO.getReportType(),
                materialIdList, reqVO.getStartFormDt(), reqVO.getEndFormDt());
        //复合键转Map
        Map<String, MaterialPurchaseReportBO> detailMap = detailList.stream().collect(Collectors.toMap(
                record -> record.getMaterialId() + "-" + record.getReportDate(), item -> item));
        //组装数据
        BigDecimal zero = BigDecimal.ZERO;
        for(MaterialPurchaseReportRespVO respVO : pageResult.getList()){

            MaterialPurchaseReportItemRespVO itemRespVO1 = new MaterialPurchaseReportItemRespVO();
            itemRespVO1.setItemName("采购总数量");
            itemRespVO1.getItemDetailList().add(zero);
            respVO.getItemList().add(itemRespVO1);

            MaterialPurchaseReportItemRespVO itemRespVO2 = new MaterialPurchaseReportItemRespVO();
            itemRespVO2.setItemName("采购总金额(含税)");
            itemRespVO2.getItemDetailList().add(zero);
            respVO.getItemList().add(itemRespVO2);

            MaterialPurchaseReportItemRespVO itemRespVO3 = new MaterialPurchaseReportItemRespVO();
            itemRespVO3.setItemName("退货总数量");
            itemRespVO3.getItemDetailList().add(zero);
            respVO.getItemList().add(itemRespVO3);

            MaterialPurchaseReportItemRespVO itemRespVO4 = new MaterialPurchaseReportItemRespVO();
            itemRespVO4.setItemName("退货总金额(含税)");
            itemRespVO4.getItemDetailList().add(zero);
            respVO.getItemList().add(itemRespVO4);

            MaterialPurchaseReportItemRespVO itemRespVO5 = new MaterialPurchaseReportItemRespVO();
            itemRespVO5.setItemName("扣费总数量");
            itemRespVO5.getItemDetailList().add(zero);
            respVO.getItemList().add(itemRespVO5);

            MaterialPurchaseReportItemRespVO itemRespVO6 = new MaterialPurchaseReportItemRespVO();
            itemRespVO6.setItemName("扣费总金额(含税)");
            itemRespVO6.getItemDetailList().add(zero);
            respVO.getItemList().add(itemRespVO6);

            BigDecimal totalPurchaseQty = zero;
            BigDecimal totalPurchaseInclTaxAmt = zero;
            BigDecimal totalReturnQty = zero;
            BigDecimal totalReturnInclTaxAmt = zero;
            BigDecimal totalDeductionQty = zero;
            BigDecimal totalDeductionInclTaxAmt = zero;
            for(String title : titleList){
                String key = respVO.getMaterialId() + "-" + title;
                MaterialPurchaseReportBO reportDetailBO = detailMap.get(key);
                if (reportDetailBO == null){
                    itemRespVO1.getItemDetailList().add(zero);
                    itemRespVO2.getItemDetailList().add(zero);
                    itemRespVO3.getItemDetailList().add(zero);
                    itemRespVO4.getItemDetailList().add(zero);
                    itemRespVO5.getItemDetailList().add(zero);
                    itemRespVO6.getItemDetailList().add(zero);
                } else{
                    itemRespVO1.getItemDetailList().add(reportDetailBO.getPurchaseQty());
                    itemRespVO2.getItemDetailList().add(reportDetailBO.getPurchaseInclTaxAmt());
                    itemRespVO3.getItemDetailList().add(reportDetailBO.getReturnQty());
                    itemRespVO4.getItemDetailList().add(reportDetailBO.getReturnInclTaxAmt());
                    itemRespVO5.getItemDetailList().add(reportDetailBO.getDeductionQty());
                    itemRespVO6.getItemDetailList().add(reportDetailBO.getDeductionInclTaxAmt());
                    totalPurchaseQty = totalPurchaseQty.add(reportDetailBO.getPurchaseQty());
                    totalPurchaseInclTaxAmt = totalPurchaseInclTaxAmt.add(reportDetailBO.getPurchaseInclTaxAmt());
                    totalReturnQty = totalReturnQty.add(reportDetailBO.getReturnQty());
                    totalReturnInclTaxAmt = totalReturnInclTaxAmt.add(reportDetailBO.getReturnInclTaxAmt());
                    totalDeductionQty = totalDeductionQty.add(reportDetailBO.getDeductionQty());
                    totalDeductionInclTaxAmt = totalDeductionInclTaxAmt.add(reportDetailBO.getDeductionInclTaxAmt());
                }
            }
            itemRespVO1.getItemDetailList().set(0, totalPurchaseQty);
            itemRespVO2.getItemDetailList().set(0, totalPurchaseInclTaxAmt);
            itemRespVO3.getItemDetailList().set(0, totalReturnQty);
            itemRespVO4.getItemDetailList().set(0, totalReturnInclTaxAmt);
            itemRespVO5.getItemDetailList().set(0, totalDeductionQty);
            itemRespVO6.getItemDetailList().set(0, totalDeductionInclTaxAmt);
        }

        return pageResult;
    }


    public List<String> getTitleList(MaterialPurchaseReportPageReqVO reqVO) {
        //标题
        List<String> titleList = new ArrayList<>();
        //日
        if(reqVO.getReportType() == 0){
            reqVO.setStartFormDt(DateUtilX.strToLocalDate(reqVO.getStartDate()));
            reqVO.setEndFormDt(DateUtilX.strToLocalDate(reqVO.getEndDate()));
            //获取标题
            LocalDate startFormDt = reqVO.getStartFormDt();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            while(startFormDt.compareTo(reqVO.getEndFormDt()) <=0){
                titleList.add(startFormDt.format(formatter));
                startFormDt = startFormDt.plusDays(1);
            }
        }

        //月
        if(reqVO.getReportType() == 1){
            reqVO.setStartFormDt(DateUtilX.strToLocalDate(reqVO.getStartDate()+"-01"));
            LocalDate endDate = DateUtilX.strToLocalDate(reqVO.getEndDate()+"-01");
            //获取当前月份的最后一天
            LocalDate lastDayOfMonth = endDate.with(TemporalAdjusters.lastDayOfMonth());
            reqVO.setEndFormDt(lastDayOfMonth);
            //获取标题
            LocalDate startFormDt = reqVO.getStartFormDt();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            while(startFormDt.compareTo(reqVO.getEndFormDt()) <=0){
                titleList.add(startFormDt.format(formatter));
                startFormDt = startFormDt.plusMonths(1);
            }
        }

        //年
        if(reqVO.getReportType() == 2){
            reqVO.setStartFormDt(DateUtilX.strToLocalDate(reqVO.getStartDate()+"-01-01"));
            LocalDate endDate = DateUtilX.strToLocalDate(reqVO.getEndDate()+"-01-01");
            // 获取当前月份的最后一天
            LocalDate lastDayOfYear = YearMonth.of(endDate.getYear(), 12).atEndOfMonth();
            reqVO.setEndFormDt(lastDayOfYear);
            //获取标题
            LocalDate startFormDt = reqVO.getStartFormDt();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
            while(startFormDt.compareTo(reqVO.getEndFormDt()) <= 0){
                titleList.add(startFormDt.format(formatter));
                startFormDt = startFormDt.plusYears(1);
            }
        }

        return titleList;
    }
}
