package com.mongoso.mgs.module.purchase.service.purchasechange.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasechange.vo.detail.PurchaseChangeDetailRespVO;

import com.mongoso.mgs.module.sale.controller.admin.salechangdetail.vo.ChangUnitPriceCheckReqVO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购订单变更明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseChangeDetailService {

    /**
     * 创建采购订单变更明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseChangeDetailAdd(@Valid PurchaseChangeDetailAditReqVO reqVO);

    /**
     * 更新采购订单变更明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseChangeDetailEdit(@Valid PurchaseChangeDetailAditReqVO reqVO);

    /**
     * 删除采购订单变更明细
     *
     * @param purchaseChangeDetailId 编号
     */
    void purchaseChangeDetailDel(Long purchaseChangeDetailId);

    /**
     * 获得采购订单变更明细信息
     *
     * @param purchaseChangeDetailId 编号
     * @return 采购订单变更明细信息
     */
    PurchaseChangeDetailRespVO purchaseChangeDetailDetail(Long purchaseChangeDetailId);

    /**
     * 获得采购订单变更明细列表
     *
     * @param reqVO 查询条件
     * @return 采购订单变更明细列表
     */
    List<PurchaseChangeDetailRespVO> purchaseChangeDetailList(@Valid PurchaseChangeDetailQueryReqVO reqVO);

    /**
     * 获得采购订单变更明细分页
     *
     * @param reqVO 查询条件
     * @return 采购订单变更明细分页
     */
    PageResult<PurchaseChangeDetailRespVO> purchaseChangeDetailPage(@Valid PurchaseChangeDetailPageReqVO reqVO);

    void purChangUnitPriceCheck(ChangUnitPriceCheckReqVO changUnitPriceCheckReq);

}
