package com.mongoso.mgs.module.produce.service.devicemoldlog;

import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.*;
import com.mongoso.mgs.module.produce.controller.admin.devicemoldlog.vo.*;
import com.mongoso.mgs.module.produce.dal.db.devicemoldlog.DeviceMoldLogDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.devicemoldlog.DeviceMoldLogMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.produce.enums.ErrorCodeConstants.*;


/**
 * 设备模具关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceMoldLogServiceImpl implements DeviceMoldLogService {

    @Resource
    private DeviceMoldLogMapper deviceMoldLogMapper;

    @Resource
    private SeqService seqService;


    @Override
    public Long deviceMoldLogAdd(DeviceMoldLogAditReqVO reqVO) {

        // 生成单号
        LocalDate now = LocalDate.now();
        Long seq = seqService.lockInsertLamb(SeqEnum.DEVICEMOLDCODE.getTableName(), now, null);
        String code = CodeGenUtil.generateCode(CodeGenUtil.CodeTypeEnums.DEVICEMOLD_CODE, seq);

        reqVO.setDeviceMoldCode(code);

        // 插入
        DeviceMoldLogDO deviceMoldLog = BeanUtilX.copy(reqVO, DeviceMoldLogDO::new);
        deviceMoldLogMapper.insert(deviceMoldLog);
        // 返回
        return deviceMoldLog.getDeviceMoldId();
    }

    @Override
    public Long deviceMoldLogEdit(DeviceMoldLogAditReqVO reqVO) {
        // 校验存在
        this.deviceMoldLogValidateExists(reqVO.getDeviceMoldId());
        // 更新
        DeviceMoldLogDO deviceMoldLog = BeanUtilX.copy(reqVO, DeviceMoldLogDO::new);
        deviceMoldLogMapper.updateById(deviceMoldLog);
        // 返回
        return deviceMoldLog.getDeviceMoldId();
    }

    @Override
    public void deviceMoldLogDel(Long deviceMoldId) {
        // 校验存在
        this.deviceMoldLogValidateExists(deviceMoldId);
        // 删除
        deviceMoldLogMapper.deleteById(deviceMoldId);
    }

    private DeviceMoldLogDO deviceMoldLogValidateExists(Long deviceMoldId) {
        DeviceMoldLogDO deviceMoldLog = deviceMoldLogMapper.selectById(deviceMoldId);
        if (deviceMoldLog == null) {
            // throw exception(DEVICE_MOLD_LOG_NOT_EXISTS);
            throw new BizException("5001", "设备模具关联不存在");
        }
        return deviceMoldLog;
    }

    @Override
    public DeviceMoldLogRespVO deviceMoldLogDetail(Long deviceMoldId) {
        DeviceMoldLogDO data = deviceMoldLogValidateExists(deviceMoldId);
        return BeanUtilX.copy(data, DeviceMoldLogRespVO::new);
    }

    @Override
    public List<DeviceMoldLogRespVO> deviceMoldLogList(DeviceMoldLogQueryReqVO reqVO) {
        List<DeviceMoldLogDO> data = deviceMoldLogMapper.selectList(reqVO);
        return BeanUtilX.copy(data, DeviceMoldLogRespVO::new);
    }

    @Override
    public PageResult<DeviceMoldLogRespVO> deviceMoldLogPage(DeviceMoldLogPageReqVO reqVO) {
        PageResult<DeviceMoldLogDO> data = deviceMoldLogMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, DeviceMoldLogRespVO::new);
    }

}
