package com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetail;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 实开发票明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_invoice_detail", autoResultMap = true)
//@KeySequence("erp.u_invoice_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDetailDO extends OperateDO {

    /** 实开发票明细id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long invoiceDetailId;

    /** 开票方向 */
    private Short billingDirection;

    /** 实开发票id */
    private Long invoiceId;

    /** 开票申请明细id */
    private Long invoiceApplyDetailId;

    /** 开票计划明细id */
    private Long invoicePlanDetailId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 行号 */
    private Long rowNo;

    /** 物料编码 */
    private String materialCode;

    private Long materialId;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 剩余开票数量 */
    private BigDecimal remainingInvoiceQty;

    /** 剩余开票金额 */
    private BigDecimal remainingInvoiceAmt;

    /** 申请开票金额（含税） */
    private BigDecimal applyInvoiceAmtInclTax;

    /** 申请开票金额（不含税） */
    private BigDecimal applyInvoiceAmtExclTax;

    /** 数量 */
    private BigDecimal qty;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    private Long invoiceTypeDictId;
    private String invoiceTypeDictName;

    /** 税率 */
    private BigDecimal taxRate;

    private BigDecimal invoiceQty;

    private BigDecimal invoiceAmtInclTax;

    private BigDecimal invoiceAmtExclTax;

    private BigDecimal taxAmt;
    /** 发票代码 */
    private String invoiceCode;
}
