//package com.mongoso.mgs.module.base.service.erpbase.handler.v2;
//
//import com.mongoso.mgs.common.util.EntityUtilX;
//import com.mongoso.mgs.component.flow.service.approve.ApproveService;
//import com.mongoso.mgs.component.flow.service.approve.bo.ApproveResult;
//import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowBizBO;
//import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
//import com.mongoso.mgs.component.flow.util.ApproveUtilX;
//import com.mongoso.mgs.component.flow.util.ReflectUtilX;
//import com.mongoso.mgs.framework.common.domain.LoginUser;
//import com.mongoso.mgs.framework.common.domain.batch.FailItem;
//import com.mongoso.mgs.framework.common.util.StrUtilX;
//import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
//import com.mongoso.mgs.module.base.dal.mysql.erpbase.ErpBaseMapper;
//import com.mongoso.mgs.module.base.service.erpbase.bo.FlowReqBO;
//import com.mongoso.mgs.module.base.service.erpbase.handler.bo.FlowApproveBO;
//import com.mongoso.mgs.module.base.service.erpbase.handler.request.ApproveCommonAttrs;
//import com.mongoso.mgs.module.base.service.erpbase.handler.request.BaseApproveRequest;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//import java.util.Map;
//
///**
// * @author: zhiling
// * @date: 2025/03/06
// * @description: 审批流程抽象类 v2.0版本
// */
//
//@Slf4j
//public abstract class BaseApproveV2Handler<T> {
//
//    @Autowired
//    protected ApproveService approveService;
//
//    @Resource
//    private ErpBaseMapper erpBaseMapper;
//
//    /**
//     * 审批流程公共参数传输对象
//     *
//     * @param item
//     * @return
//     */
//    protected abstract ApproveCommonAttrs approvalAttributes(T item);
//
//    /**
//     * 流程处理
//     *
//     * @param item
//     * @param flowApproveBO
//     * @return
//     */
//    public final FailItem process(T item, FlowApproveBO flowApproveBO) {
//        FailItem failItem = new FailItem();
//
//        Map<String, Object> conditionMap = flowApproveBO.getConditionMap();
//        String flowFunctionCode = flowApproveBO.getFlowFunctionCode();
//        FlowConfigBO flowConfigBO = flowApproveBO.getFlowConfigBO();
//        Integer buttonType = flowApproveBO.getButtonType();
//
//        BaseApproveRequest approveRequest = new BaseApproveRequest();
//        approveRequest.setButtonType(buttonType);
//        approveRequest.setFailItem(failItem);
//
//        // 1.业务校验 校验是否通过
//        if (!businessVerify(item, approveRequest)) {
//            return failItem;
//        }
//
//        // 2. 数据状态更新
//        Integer dataStatus = 0;
//        if (!flowConfigBO.getToFlow()){
//            //更新数据状态,更新业务数据
//            dataStatus = ApproveUtilX.getDataStatusByApproveSuccess(flowFunctionCode);
//            approveRequest.setDataStatus(dataStatus);
//
//            handleBusinessData(item,approveRequest);
//        }else {
//            //走审批进行审批操作,更新状态为审批中状态
//            dataStatus = ApproveUtilX.getDataStatusByApproveStart(flowFunctionCode);
//
//            //发起审批任务
//            approveRequest.setFlowConfigBO(flowConfigBO);
//            approveRequest.setConditionMap(conditionMap);
//            approveRequest.setDataStatus(dataStatus);
//
//            this.processApproval(item,approveRequest);
//        }
//
//        return failItem;
//    }
//
//    /**
//     * 发起审批
//     *
//     * @param item
//     * @param request
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void processApproval(T item,BaseApproveRequest request) {
//
//        Map<String, Object> conditionMap = request.getConditionMap();
//        FlowConfigBO flowConfigBO = request.getFlowConfigBO();
//        Integer dataStatus = request.getDataStatus();
//        FailItem failItem = request.getFailItem();
//
//        if (conditionMap == null || conditionMap.isEmpty()){
//            conditionMap = ReflectUtilX.toMapByExcluded(item, null);
//        }
//
//        BaseApproveRequest approveRequest = new BaseApproveRequest();
//        approveRequest.setDataStatus(dataStatus);
//        approveRequest.setFailItem(failItem);
//
//        //中间状态业务数据处理
//        Integer count = applyIntermediate(item, approveRequest);
//
//        ApproveCommonAttrs approvalAttributes = approvalAttributes(item);
//
//        FlowBizBO flowBizBO = new FlowBizBO();
//        flowBizBO.setObjId(approvalAttributes.getObjId().toString());
//        flowBizBO.setObjCode(approvalAttributes.getObjCode());
//        flowBizBO.setConditionMap(conditionMap);
//
//        // 调用预申请方法
//        ApproveResult approveResults = approveService.preApproveTaskAdd(flowConfigBO, flowBizBO);
//
//        // 批量发起审批
//        approveService.approveTaskAdd(flowConfigBO, approveResults);
//
//    }
//
//    /**
//     * 审批不通过-更新单据状态
//     *
//     * @param request
//     * @return
//     */
//    private Integer applyIntermediate(T item, BaseApproveRequest request){
//        log.info("发起审批更新单据状态为审批中");
//        FlowReqBO flowReqBO = buildFlowReqBO(item,request.getDataStatus());
//
//        erpBaseMapper.updateDataStatus(flowReqBO);
//
//        return 1;
//    }
//
//    private FlowReqBO buildFlowReqBO(T item,Integer dataStatus) {
//        ApproveCommonAttrs attrs = approvalAttributes(item);
//        Long objId = attrs.getObjId();
//        Class<?> clazz = attrs.getClazz();
//
//        String tableName = attrs.getTableName();
//        String pkFieldName = attrs.getCondPkFieldName();
//
//        //获取表名
//        if (StrUtilX.isEmpty(tableName)) {
//            tableName = EntityUtilX.getTableNameByAnnot(clazz);
//        }
//
//        //获取主键字段名称
//        if (StrUtilX.isEmpty(pkFieldName)) {
//            pkFieldName = EntityUtilX.getPkFieldName(clazz);
//        }
//
//        if (StrUtilX.isEmpty(tableName)) {
//            log.error("表名称为空！");
//            throw new IllegalArgumentException("审批回调更新单据状态出现异常！");
//        }
//
//        if (StrUtilX.isEmpty(pkFieldName)) {
//            log.error("主键字段名称不能为空！");
//            throw new IllegalArgumentException("审批回调更新单据状态出现异常！");
//        }
//
//        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//
//        FlowReqBO flowReqBO = new FlowReqBO();
//        flowReqBO.setTableName(tableName);
//        flowReqBO.setCondPkFieldName(pkFieldName);
//        flowReqBO.setId(objId);
//        flowReqBO.setDataStatus(dataStatus);
//        flowReqBO.setFullUserName(loginUser.getFullUserName());
//
//        return flowReqBO;
//    }
//
//    /**
//     * 业务校验
//     *
//     * @param item
//     * @param request
//     * @return
//     */
//    protected abstract Boolean businessVerify(T item, BaseApproveRequest request);
//
//    /**
//     * 中间状态业务数据处理
//     *
//     * @param item
//     * @param request
//     * @return
//     */
////    public abstract Integer applyIntermediate(T item,BaseApproveRequest request);
//
//
//    /**
//     * 更新业务数据
//     *
//     * @param item
//     * @param request
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public abstract Integer handleBusinessData(T item,BaseApproveRequest request);
//}