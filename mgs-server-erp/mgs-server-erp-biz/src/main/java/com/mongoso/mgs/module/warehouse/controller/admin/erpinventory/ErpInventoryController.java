package com.mongoso.mgs.module.warehouse.controller.admin.erpinventory;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import com.mongoso.mgs.module.warehouse.service.erpinventory.ErpInventoryService;

/**
 * 盘点单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warehouse")
@Validated
public class ErpInventoryController {

    @Resource
    private ErpInventoryService erpInventoryService;

    @OperateLog("盘点单添加或编辑")
    @PostMapping("/erpInventoryAdit")
    @PreAuthorize("@ss.hasPermission('erpInventory:adit')")
    public ResultX<Long> erpInventoryAdit(@Valid @RequestBody ErpInventoryAditReqVO reqVO) {
        return success(reqVO.getInventoryId() == null
                            ? erpInventoryService.erpInventoryAdd(reqVO)
                            : erpInventoryService.erpInventoryEdit(reqVO));
    }

    @OperateLog("盘点单状态编辑")
    @PostMapping("/erpInventoryStatusEdit")
    @PreAuthorize("@ss.hasPermission('erpInventory:adit')")
    public ResultX<Long> erpInventoryStatusEdit(@Valid @RequestBody ErpInventoryAditReqVO reqVO) {
        return success(erpInventoryService.erpInventoryStatusEdit(reqVO));
    }

    @OperateLog("盘点单调整状态编辑")
    @PostMapping("/erpInventoryAdjustStatusEdit")
    @PreAuthorize("@ss.hasPermission('erpInventory:adit')")
    public ResultX<Long> erpInventoryAdjustStatusEdit(@Valid @RequestBody ErpInventoryAditReqVO reqVO) {
        return success(erpInventoryService.erpInventoryAdjustStatusEdit(reqVO));
    }

    @OperateLog("盘点单删除")
    @PostMapping("/erpInventoryDel")
    @PreAuthorize("@ss.hasPermission('erpInventory:delete')")
    public ResultX<Boolean> erpInventoryDel(@Valid @RequestBody ErpInventoryPrimaryReqVO reqVO) {
        erpInventoryService.erpInventoryDel(reqVO.getInventoryId());
        return success(true);
    }

    @OperateLog("盘点单批量删除")
    @PostMapping("/erpInventoryDelBatch")
    @PreAuthorize("@ss.hasPermission('erpInventory:delete')")
    public ResultX<BatchResult> erpInventoryDelBatch(@Valid @RequestBody IdsReqVO reqVO) {
        return erpInventoryService.erpInventoryDelBatch(reqVO);
    }

    @OperateLog("盘点单详情")
    @PostMapping("/erpInventoryDetail")
    @PreAuthorize("@ss.hasPermission('erpInventory:query')")
    public ResultX<ErpInventoryRespVO> erpInventoryDetail(@Valid @RequestBody ErpInventoryPrimaryReqVO reqVO) {
        return success(erpInventoryService.erpInventoryDetail(reqVO));
    }

    @OperateLog("盘点单列表")
    @PostMapping("/erpInventoryList")
    @PreAuthorize("@ss.hasPermission('erpInventory:query')")
    @DataPermission
    public ResultX<List<ErpInventoryRespVO>> erpInventoryList(@Valid @RequestBody ErpInventoryQueryReqVO reqVO) {
        return success(erpInventoryService.erpInventoryList(reqVO));
    }

    @OperateLog("盘点单列表")
    @PostMapping("/erpInventoryQuotedList")
    @PreAuthorize("@ss.hasPermission('erpInventory:query')")
    public ResultX<List<ErpInventoryRespVO>> erpInventoryQuotedList(@Valid @RequestBody ErpInventoryQueryReqVO reqVO) {
        return success(erpInventoryService.erpInventoryList(reqVO));
    }


    @OperateLog("盘点单分页")
    @PostMapping("/erpInventoryPage")
    @PreAuthorize("@ss.hasPermission('erpInventory:query')")
    @DataPermission
    public ResultX<PageResult<ErpInventoryRespVO>> erpInventoryPage(@Valid @RequestBody ErpInventoryPageReqVO reqVO) {
        return success(erpInventoryService.erpInventoryPage(reqVO));
    }

    @OperateLog("盘点单分页")
    @PostMapping("/erpInventoryQuotedPage")
    @PreAuthorize("@ss.hasPermission('erpInventory:query')")
    public ResultX<PageResult<ErpInventoryRespVO>> erpInventoryQuotedPage(@Valid @RequestBody ErpInventoryPageReqVO reqVO) {
        return success(erpInventoryService.erpInventoryPage(reqVO));
    }

    @OperateLog("盘点单审核")
    @PostMapping("/erpInventoryApprove")
    @PreAuthorize("@ss.hasPermission('erpInventory:adit')")
    public ResultX<BatchResult> erpInventoryApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(erpInventoryService.erpInventoryApprove(reqVO));
    }

    @OperateLog("盘点单审核回调接口")
    @PostMapping("/erpInventoryFlowCallback")
    public ResultX<Object> erpInventoryFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(erpInventoryService.erpInventoryFlowCallback(reqVO));
    }

    @OperateLog("盘点单生成")
    @PostMapping("/erpInventoryPlanGenTask")
    public ResultX erpInventoryPlanGenTask() {
        erpInventoryService.erpInventoryPlanGenTask();
        return success();
    }



}
