package com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.mongoso.mgs.framework.common.util.tree.TreeItem;
import com.mongoso.mgs.framework.mybatis.core.handler.JsonbTypeHandler;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisTotalRespVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 物料分析结果对象
 *
 * <AUTHOR>
 */
@Data
public class MaterialAnalysisResultRespVO implements Serializable {


    /** 主键ID */
    private Long materialAnalysisId;
    /** 物料分析编码 */
    private String materialAnalysisCode;

    /** 物料分析类型 */
    private String materialAnalysisTypeDictId;
    private String materialAnalysisTypeDictName;

    /** 关联单据id */
    private Long relatedOrderId;
    /** 关联单据号 */
    private String relatedOrderCode;

    /** 分析范围 */
    private List<String> analysisScopeList;
    private String analysisScope;

    /** 审核状态 */
    private Integer dataStatus;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    private List<ResultTree>  itemResultTree;

    private List<MaterialAnalysisTotalRespVO>  itemTotalList;

    /** 需求日期 */
    private LocalDate demandDate;

    /** 分析参数 */
    private String analyzeParam;

    /** 审批任务id */
    private Long approveTaskId;

    /** 是否可用 */
    private Integer useEnable;

    @Data
    public static  class ResultTree extends TreeItem<ResultTree>  implements Serializable {

        private Integer rowNo;
        /** 物料id */
        private Long materialId;
        /** 物料编码 */
        private String materialCode;
        private String materialName;// 物料名称
        private String materialCategoryDictId;// 物料类别id
        private String materialCategoryDictName;// 物料类别id
        private String mainUnitDictId;// 基本单位
        private String mainUnitDictName;// 基本单位
        private String specModel;// 规格型号
        private String specAttributeStr;// 规格属性

        private BigDecimal demandQty;// 需求数量
        private BigDecimal oneDemandQty;// 单个需求数量
        private BigDecimal lossRate;// 损耗率
        private BigDecimal estimatedQty;// 预估用量
        private BigDecimal estimatedTotalQty;// 预估总量
        private BigDecimal netDemandQty;// 净需求数量，自制/委外/应外
        private BigDecimal reqSelfMadeQty;// 应自制数量
        private BigDecimal reqOutSourceQty;// 应委外数量
        private BigDecimal reqPurchaseQty;// 应外购数量
        private Integer materialSourceDictId;//物料来源// 物料来源id，自制/委外/应外
        private String materialSourceDictName;// 物料来源id，自制/委外/应外

        private BigDecimal stockableQty; //可用库存数量
        private BigDecimal purchaseInQty; //采购在制
        private BigDecimal workOrderInQty; //工单在制
        private BigDecimal availableStockQty;//实际可用数量
        private BigDecimal stockQty; //预估可用数量
        private List<JSONObject> purDetailList;//采购数量明细
        private List<JSONObject> workDetailList;//工单数量明细


        /** 是否使用可替代物料 (0-否, 1-是) */
        private Integer useSubstituteMaterial = 0;

        /** 可替代物料的信息 */
//        @TableField(typeHandler = JsonbTypeHandler.class)
        private JSONObject substituteMaterialInfo;

    }

    @Data
    public static  class Total implements Serializable {

        private Integer rowNo;
        private Long materialId;
        private String materialCode;
        private String materialName;// 物料名称
        private String materialCategoryDictId;// 物料类别id
        private String materialCategoryDictName;// 物料类别id
        private String mainUnitDictId;// 基本单位
        private String mainUnitDictName;// 基本单位
        private String specModel;// 规格型号
        private String specAttributeStr;// 规格属性
        private BigDecimal netDemandQty;// 净需求数量，自制/委外/应外
        private BigDecimal issuedQty;// 已下发数量
        private Integer materialSourceDictId;// 物料来源id，自制/委外/应外

    }
}
