package com.mongoso.mgs.module.produce.controller.admin.processoperationlog.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import com.alibaba.fastjson.JSONObject;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 工序操作记录 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProcessOperationLogQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long processOperationId;

    /** 操作流水编码 */
    private String processOperationCode;

    /** 操作日志 操作类型['新增','更新','删除'] */
    private Integer processOperationType;

    /** 日志来源['工单工序派工','工序派工','工单报工','工序报工','委外报工','返工报工','质检报工'] */
    private Integer logSource;

    /** 来源单ID */
    private Long sourceOrderId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 生产工单ID */
    private Long prodWorkId;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 生产订单ID */
    private Long prodOrderId;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 加工方式 */
    private Long processMethod;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanStartDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanEndDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanEndDate;

    /** 工作中心ID */
    private Long workCenterId;

    /** 工作中心编码 */
    private String workCenterCode;

    /** 工作中心名称 */
    private String workCenterName;

    /** 报工记录id */
    private Long reportedWorkId;

    /** 报工记录编码 */
    private String reportedWorkCode;

    /** 报工类型 ['工单报工','工序报工','委外','返工','质检'] */
    private Integer reportedWorkType;

    /** 派工数量 */
    private BigDecimal dispatchQty;

    /** 良品数量 */
    private BigDecimal okQty;

    /** 不良品信息 */
    private List<JSONObject> ngDetailList;

    /** 报工人 */
    private String reportedId;
    private String reportedName;

    /** 报工时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startReportedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endReportedDt;

    /** 流转卡编码 */
    private String flowCardCode;

    /** 流转卡类型['工单工序报工','工序报工'] */
    private Integer flowCardType;

    /** 设备主键ID */
    private Long deviceId;

    /** 设备编码 */
    private String deviceCode;

    /** 设备名称 */
    private String deviceName;

    /** 模具主键ID */
    private Long moldId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 派工方式['工单工序派工','工序派工'] */
    private Integer dispatchMethod;

    /** 用户id */
    private Long userId;

}
