package com.mongoso.mgs.module.finance.handler.flowCallback.payment;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.finance.dal.db.advancepaymentrefund.AdvancePaymentRefundDO;
import org.springframework.stereotype.Component;

/**
 * @author: daiji<PERSON><PERSON><PERSON>
 * @date: 2024/12/19
 * @description: 预收款退款回调处理类
 */

@Component
public class AdvancePaymentRefundFlowCallBackHandler extends FlowCallbackHandler<AdvancePaymentRefundDO> {

    protected AdvancePaymentRefundFlowCallBackHandler(FlowApproveHandler<AdvancePaymentRefundDO> approveHandler) {
        super(approveHandler);
    }
}
