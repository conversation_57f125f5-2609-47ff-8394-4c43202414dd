package com.mongoso.mgs.module.dailycost.dal.mysql.costprodmaterialdetail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodmaterialdetail.vo.CostProdMaterialDetailPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodmaterialdetail.vo.CostProdMaterialDetailQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spupricestatis.vo.CostSpuPriceStatisRespVO;
import com.mongoso.mgs.module.dailycost.dal.db.costprodmaterialdetail.CostProdMaterialDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 生产物料成本明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CostProdMaterialDetailMapper extends BaseMapperX<CostProdMaterialDetailDO> {

    default PageResult<CostProdMaterialDetailDO> selectPageOld(CostProdMaterialDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<CostProdMaterialDetailDO>lambdaQueryX()
                .likeIfPresent(CostProdMaterialDetailDO::getCostProdMaterialCode, reqVO.getCostProdMaterialCode())
                .eqIfPresent(CostProdMaterialDetailDO::getCostProdMaterialId, reqVO.getCostProdMaterialId())
                .eqIfPresent(CostProdMaterialDetailDO::getCostUsage, reqVO.getCostUsage())
                .eqIfPresent(CostProdMaterialDetailDO::getCostSubjectId, reqVO.getCostSubjectId())
                .eqIfPresent(CostProdMaterialDetailDO::getUndertakeOrgId, reqVO.getUndertakeOrgId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(CostProdMaterialDetailDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .betweenIfPresent(CostProdMaterialDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(CostProdMaterialDetailDO::getCreatedDt));
    }



    default PageResult<CostProdMaterialDetailDO> selectPage(CostProdMaterialDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<CostProdMaterialDetailDO>lambdaQueryX()
                .likeIfPresent(CostProdMaterialDetailDO::getCostProdMaterialCode, reqVO.getCostProdMaterialCode())
                .inIfPresent(CostProdMaterialDetailDO::getMaterialId, reqVO.getMaterialIdList())
                .inIfPresent(CostProdMaterialDetailDO::getCostProdMaterialId, reqVO.getCostProdMaterialIdList())
                .eqIfPresent(CostProdMaterialDetailDO::getCostProdMaterialId, reqVO.getCostProdMaterialId())
                .eqIfPresent(CostProdMaterialDetailDO::getProcessId, reqVO.getProcessId())
                .eqIfPresent(CostProdMaterialDetailDO::getCostUsage, reqVO.getCostUsage())
                .eqIfPresent(CostProdMaterialDetailDO::getCostSubjectId, reqVO.getCostSubjectId())
                .eqIfPresent(CostProdMaterialDetailDO::getUndertakeOrgId, reqVO.getUndertakeOrgId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(CostProdMaterialDetailDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .betweenIfPresent(CostProdMaterialDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(CostProdMaterialDetailDO::getCreatedDt));
    }

    default List<CostProdMaterialDetailDO> selectListOld(CostProdMaterialDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<CostProdMaterialDetailDO>lambdaQueryX()
                .likeIfPresent(CostProdMaterialDetailDO::getCostProdMaterialCode, reqVO.getCostProdMaterialCode())
                .eqIfPresent(CostProdMaterialDetailDO::getCostProdMaterialId, reqVO.getCostProdMaterialId())
                .eqIfPresent(CostProdMaterialDetailDO::getCostUsage, reqVO.getCostUsage())
                .eqIfPresent(CostProdMaterialDetailDO::getCostSubjectId, reqVO.getCostSubjectId())
                .eqIfPresent(CostProdMaterialDetailDO::getUndertakeOrgId, reqVO.getUndertakeOrgId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(CostProdMaterialDetailDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .betweenIfPresent(CostProdMaterialDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(CostProdMaterialDetailDO::getCreatedDt));
    }

    default List<CostProdMaterialDetailDO> selectList(CostProdMaterialDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<CostProdMaterialDetailDO>lambdaQueryX()
                .likeIfPresent(CostProdMaterialDetailDO::getCostProdMaterialCode, reqVO.getCostProdMaterialCode())
                .eqIfPresent(CostProdMaterialDetailDO::getCostProdMaterialId, reqVO.getCostProdMaterialId())
                .eqIfPresent(CostProdMaterialDetailDO::getCostUsage, reqVO.getCostUsage())
                .eqIfPresent(CostProdMaterialDetailDO::getCostSubjectId, reqVO.getCostSubjectId())
                .eqIfPresent(CostProdMaterialDetailDO::getUndertakeOrgId, reqVO.getUndertakeOrgId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(CostProdMaterialDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(CostProdMaterialDetailDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .betweenIfPresent(CostProdMaterialDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(CostProdMaterialDetailDO::getCreatedDt));
    }

    default CostProdMaterialDetailDO selectCostProdMaterialId(Long costProdMaterialId) {
        return selectOne(LambdaQueryWrapperX.<CostProdMaterialDetailDO>lambdaQueryX()
                .eq(CostProdMaterialDetailDO::getCostProdMaterialId, costProdMaterialId));
    }

    void deleteByCostProdMaterialId(@Param("costProdMaterialId") Long costProdMaterialId);

    List<CostSpuPriceStatisRespVO> getPrice(@Param("reqVO") CostSpuPriceStatisQueryReqVO reqVO);
    List<CostSpuPriceStatisRespVO> getReportedQty(@Param("reqVO") CostSpuPriceStatisQueryReqVO reqVO);
}