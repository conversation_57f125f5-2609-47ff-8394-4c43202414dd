package com.mongoso.mgs.module.sale.controller.admin.cutomerrel.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 客户中间 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class CutomerRelQueryReqVO {

    /** 主键 */
    private Long customerRelId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 创建人ID */
    private Long createdId;

    /** 客户id */
    private Long customerId;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /** 行号 */
    private Integer rowNo;

}
