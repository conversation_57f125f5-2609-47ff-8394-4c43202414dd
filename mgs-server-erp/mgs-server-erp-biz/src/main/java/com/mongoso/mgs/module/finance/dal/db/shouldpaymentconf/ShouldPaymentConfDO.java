package com.mongoso.mgs.module.finance.dal.db.shouldpaymentconf;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;

/**
 * 应收账龄配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_should_payment_conf", autoResultMap = true)
//@KeySequence("u_should_payment_conf_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShouldPaymentConfDO extends OperateDO {

    /** 配置主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long confId;

    /** 单据类型 */
    private Short formType;

    /** 标题 */
    private String title;

    /** 距离天数 */
    private Integer distanceDays;
    @TableField(exist = false)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDay;
    @TableField(exist = false)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDay;

    /** 排序 */
    private Integer sort;


}
