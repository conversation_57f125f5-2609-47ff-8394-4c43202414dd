package com.mongoso.mgs.module.finance.service.asset.assetchange;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.AssetChangeAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.AssetChangePageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.AssetChangeQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.AssetChangeRespVO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetchange.AssetChangeDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciate.AssetDepreciateDO;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetchange.AssetChangeMapper;
import com.mongoso.mgs.module.finance.handler.approve.asset.AssetChangeHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.asset.AssetChangeFlowCallBackHandler;
import com.mongoso.mgs.module.finance.service.asset.assetregister.AssetRegisterService;
import com.mongoso.mgs.module.finance.service.asset.assetstatusconfig.AssetStatusConfigService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.finance.enums.ErrorCodeConstants.*;


/**
 * 资产变动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssetChangeServiceImpl implements AssetChangeService {

    @Resource
    private AssetChangeMapper assetChangeMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    private AssetStatusConfigService assetStatusService;

    @Resource
    private AssetRegisterService assetRegisterService;

    @Resource
    @Lazy
    private AssetChangeHandler assetChangeHandler;

    @Resource
    private AssetChangeFlowCallBackHandler assetChangeFlowCallBackHandler;

    @Override
    public Long assetChangeAdd(AssetChangeAditReqVO reqVO) {
        // 插入
        AssetChangeDO assetChange = BeanUtilX.copy(reqVO, AssetChangeDO::new);
        //变动单号
        String code = seqService.getGenerateCode(reqVO.getChangeCode(), MenuEnum.ASSET_CHANGE.menuId);
        assetChange.setChangeCode(code);
        assetChangeMapper.insert(assetChange);
        // 返回
        return assetChange.getAssetChangeId();
    }

    @Override
    public Long assetChangeEdit(AssetChangeAditReqVO reqVO) {
        // 校验存在
//        this.assetChangeValidateExists(reqVO.getAssetChangeId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.assetChangeValidateExists(reqVO.getAssetChangeId()), reqVO);

        // 更新
        AssetChangeDO assetChange = BeanUtilX.copy(reqVO, AssetChangeDO::new);
        assetChangeMapper.updateById(assetChange);
        // 返回
        return assetChange.getAssetChangeId();
    }

    @Override
    public void assetChangeDel(Long assetChangeId) {
        // 校验存在
        AssetChangeDO returnDo = this.assetChangeValidateExists(assetChangeId);
        if(returnDo.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey().shortValue()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        assetChangeMapper.deleteById(assetChangeId);
    }

    private AssetChangeDO assetChangeValidateExists(Long assetChangeId) {
        AssetChangeDO assetChange = assetChangeMapper.selectById(assetChangeId);
        if (assetChange == null) {
            // throw exception(ASSET_CHANGE_NOT_EXISTS);
            throw new BizException("5001", "资产变动不存在");
        }
        return assetChange;
    }

    @Override
    public AssetChangeRespVO assetChangeDetail(Long assetChangeId) {
        AssetChangeDO data = assetChangeMapper.selectById(assetChangeId);
        AssetChangeRespVO respVO = BeanUtilX.copy(data, AssetChangeRespVO::new);
        //VO属性填充
        fillVoProperties(respVO);
        Optional.ofNullable(approveService.detailByObjId(assetChangeId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));
        return respVO;
    }

    @Override
    public List<AssetChangeRespVO> assetChangeList(AssetChangeQueryReqVO reqVO) {
        List<AssetChangeRespVO> respVOList = BeanUtilX.copy(assetChangeMapper.selectList(reqVO), AssetChangeRespVO :: new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<AssetChangeRespVO> assetChangePage(AssetChangePageReqVO reqVO) {
        PageResult<AssetChangeRespVO> pageResult = BeanUtilX.copy(assetChangeMapper.selectPage(reqVO), AssetChangeRespVO :: new);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    @Override
    public ResultX<BatchResult> assetChangeDelBatch(IdReq reqVO) {
        //获取对象属性名
        String id = EntityUtilX.getPropertyName(AssetChangeDO:: getAssetChangeId);
        String code = EntityUtilX.getPropertyName(AssetChangeDO::getChangeCode);

        return erpBaseService.batchDelete(reqVO.getIdList(), AssetChangeDO.class, null, id, code);
    }


    @Override
    public BatchResult assetChangeApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<AssetChangeDO> list = assetChangeMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (AssetChangeDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus().intValue());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = assetChangeHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getChangeCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (AssetChangeDO item : list) {
                String reason = reasonMap.get(item.getChangeCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getAssetChangeId());
                    messageInfoBO.setObjCode(item.getChangeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getAssetChangeId());
                    messageInfoBO.setObjCode(item.getChangeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object assetChangeFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        AssetChangeDO item = this.assetChangeValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return assetChangeFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }


    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(AssetChangeRespVO respVO) {
        List<AssetChangeRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<AssetChangeRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> assetStatusIdList = new ArrayList<>();
        List<Long> changStatusIdList = new ArrayList<>();
        List<Long> assetIdList = new ArrayList<>();
        for(AssetChangeRespVO respVO : respVOList){
            directorIdList.add(respVO.getDirectorId());
            directorOrgIdList.add(respVO.getDirectorOrgId());
            assetStatusIdList.add(respVO.getAssetStatusId());
            changStatusIdList.add(respVO.getChangeStatusId());
            assetIdList.add(respVO.getAssetId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        //查询资产状态
        Map<Long, String> assetStatusMap = assetStatusService.assetStatusNameMap(assetStatusIdList);

        //查询变动状态
        Map<Long, String> changStatusMap = assetStatusService.assetStatusNameMap(changStatusIdList);

        //查询资产名称
        Map<Long, String> assetMap = assetRegisterService.assetNameMap(assetIdList);

        // 属性填充
        for (AssetChangeRespVO item : respVOList) {

            // 资产名称
            if(item.getAssetId() != null){
                item.setAssetName(assetMap.get(item.getAssetId()));
            }

            // 资产状态
            if(item.getAssetStatusId() != null){
                item.setAssetStatusName(assetStatusMap.get(item.getAssetStatusId()));
            }

            // 变动状态
            if(item.getChangeStatusId() != null){
                item.setChangeStatusName(changStatusMap.get(item.getChangeStatusId()));
            }

            //责任人
            if(item.getDirectorId() != null){
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

        }
    }

}
