package com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 生产采购成本单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostProdPurchasePageReqVO extends PageParam {

    /** 订单类型 */
    private Short orderType;

    /** 摊销状态 */
    private Short amortiseStatus;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 关联据单ID */
    private Long relatedOrderId;

    /** 关联单据明细ID */
    private Long relatedOrderDetailId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 关联供应商ID */
    private Long relatedSupplierId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 数量 */
    private BigDecimal qty;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 主体公司ID */
    private String companyOrgId;

    /** 归集状态 */
    private Short aggreStatus;

    /** 单据来源类型['采购订单','需求采购订单'] */
    private Integer sourceFormType;

    /** 承担物料id */
    private Long undertakeMaterialId;

    /** 承担物料编码 */
    private String undertakeMaterialCode;

    /** 物料BOMID路径 */
    private String materialBomIdPath;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

}
