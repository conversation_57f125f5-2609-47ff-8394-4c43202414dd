package com.mongoso.mgs.module.produce.service.erpprodorderdetail;

import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.materialbom.MaterialBomDO;
import com.mongoso.mgs.module.produce.dal.mysql.materialbom.MaterialBomMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import java.util.*;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderdetail.vo.*;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderdetail.ErpProdOrderDetailMapper;
import com.mongoso.mgs.framework.common.exception.BizException;


/**
 * 生产物料 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpProdOrderDetailServiceImpl implements ErpProdOrderDetailService {

    @Resource
    private ErpProdOrderDetailMapper erpProdOrderDetailMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private MaterialBomMapper materialBomMapper;

    @Override
    public Long erpProdOrderDetailAdd(ErpProdOrderDetailAditReqVO reqVO) {
        // 插入
        ErpProdOrderDetailDO erpProdOrderDetail = BeanUtilX.copy(reqVO, ErpProdOrderDetailDO::new);
        erpProdOrderDetailMapper.insert(erpProdOrderDetail);
        // 返回
        return erpProdOrderDetail.getProdOrderDetailId();
    }

    @Override
    public Long erpProdOrderDetailEdit(ErpProdOrderDetailAditReqVO reqVO) {
        // 校验存在
        this.erpProdOrderDetailValidateExists(reqVO.getProdOrderDetailId());
        // 更新
        ErpProdOrderDetailDO erpProdOrderDetail = BeanUtilX.copy(reqVO, ErpProdOrderDetailDO::new);
        erpProdOrderDetailMapper.updateById(erpProdOrderDetail);
        // 返回
        return erpProdOrderDetail.getProdOrderDetailId();
    }

    @Override
    public boolean erpProdOrderDetailEdit(List<ErpProdOrderDetailAditReqVO> reqVOList) {

        // 更新
        List<ErpProdOrderDetailDO> erpProdOrderDetailList = BeanUtilX.copy(reqVOList, ErpProdOrderDetailDO::new);
        return erpProdOrderDetailMapper.updateBatch(erpProdOrderDetailList);

    }

    @Override
    public void erpProdOrderDetailDel(Long prodOrderDetailId) {
        // 校验存在
        this.erpProdOrderDetailValidateExists(prodOrderDetailId);
        // 删除
        erpProdOrderDetailMapper.deleteById(prodOrderDetailId);
    }

    private ErpProdOrderDetailDO erpProdOrderDetailValidateExists(Long prodOrderDetailId) {
        ErpProdOrderDetailDO erpProdOrderDetail = erpProdOrderDetailMapper.selectById(prodOrderDetailId);
        if (erpProdOrderDetail == null) {
            // throw exception(ERP_PROD_ORDER_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "生产物料不存在");
        }
        return erpProdOrderDetail;
    }

    @Override
    public ErpProdOrderDetailRespVO erpProdOrderDetailDetail(Long prodOrderDetailId) {
        ErpProdOrderDetailDO data = erpProdOrderDetailValidateExists(prodOrderDetailId);
        return BeanUtilX.copy(data, ErpProdOrderDetailRespVO::new);
    }

    @Override
    public ErpProdOrderDetailRespVO erpProdOrderDetail(Long prodOrderId,Long materialId) {
        ErpProdOrderDetailDO data = erpProdOrderDetailMapper.selectOne(ErpProdOrderDetailDO::getProdOrderId,prodOrderId,ErpProdOrderDetailDO::getMaterialId,materialId);
        return BeanUtilX.copy(data, ErpProdOrderDetailRespVO::new);
    }

    @Override
    public List<ErpProdOrderDetailRespVO> erpProdOrderDetailList(ErpProdOrderDetailQueryReqVO reqVO) {
        List<ErpProdOrderDetailDO> detailDOList = null;
        if (reqVO.getMaterialSourceDictId() != null){
            detailDOList = erpProdOrderDetailMapper.queryList(reqVO);
        }else {
            detailDOList = erpProdOrderDetailMapper.selectList(reqVO);
        }
        //VO属性填充
        List<ErpProdOrderDetailRespVO> resuslt = getDetailRespList(detailDOList);
        return resuslt;
    }

    @Override
    public PageResult<ErpProdOrderDetailRespVO> erpProdOrderDetailPage(ErpProdOrderDetailPageReqVO reqVO) {
        PageResult<ErpProdOrderDetailDO> data = erpProdOrderDetailMapper.selectPage(reqVO);
        PageResult<ErpProdOrderDetailRespVO> pageResult = BeanUtilX.copy(data, ErpProdOrderDetailRespVO::new);

        //VO属性填充
        if (CollUtilX.isNotEmpty(data.getList())){
            List<ErpProdOrderDetailRespVO> resuslt = getDetailRespList(data.getList());
            pageResult.setList(resuslt);
        }

        return pageResult;
    }

    @Override
    public Long queryMaterialBomId(Long materialId) {
        if (materialId == null){
            throw new BizException("5001", "物料ID不能为空");
        }
        //查询物料Bom信息
        MaterialBomQueryReqVO materialBom = new MaterialBomQueryReqVO();
        materialBom.setMaterialId(materialId);
        materialBom.setDataStatus(DataStatusEnum.APPROVED.getKey());
        materialBom.setIsParent(1);
        List<MaterialBomDO> materialBomList = materialBomMapper.selectList(materialBom);

        if(CollUtilX.isNotEmpty(materialBomList)){
            return materialBomList.get(0).getMaterialBomId();
        }

        return 0L;
    }

    /**
     * VO属性填充
     *
     * @param detailDOList
     * @return
     */
    private List<ErpProdOrderDetailRespVO> getDetailRespList(List<ErpProdOrderDetailDO> detailDOList) {
        List<ErpProdOrderDetailRespVO> detailRespVOS = BeanUtilX.copy(detailDOList,ErpProdOrderDetailRespVO::new);
        if (CollUtilX.isEmpty(detailDOList)){
            return detailRespVOS;
        }

        //查询物料信息
        List<Long> materialIdList = new ArrayList<>();
        for (ErpProdOrderDetailRespVO detailRespVO : detailRespVOS){
            materialIdList.add(detailRespVO.getMaterialId());
        }

        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        for (ErpProdOrderDetailRespVO detail : detailRespVOS){
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detail.getMaterialId());
            if (erpMaterialDO!=null){
                detail.setMaterialName(erpMaterialDO.getMaterialName());
                detail.setSpecModel(erpMaterialDO.getSpecModel());
                detail.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detail.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detail.setMainUnitDictId(erpMaterialDO.getMainUnitDictId());
                detail.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detail.setMaterialSourceDictId(erpMaterialDO.getMaterialSourceDictId());
                detail.setMaterialSourceDictName(erpMaterialDO.getMaterialSourceDictName());
                detail.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detail.setProcessingFee(erpMaterialDO.getProcessingFee());
                detail.setPurchaseStandardPrice(erpMaterialDO.getPurchaseStandardPrice());
            }
        }

        return detailRespVOS;
    }

}
