package com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo;

import lombok.*;

  
 import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 销售发货通知明细单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShipNoticeDetailRespVO extends ShipNoticeDetailBaseVO {

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** ------出库单引用字段------ **/

    /** 物料库存ID */
    private Long materialStockId;

    /** 仓库ID */
    private String warehouseOrgId;

    /** 仓库名称 */
    private String warehouseOrgName;

    /** 库存数量 **/
    private BigDecimal stockQty;

    /** 可用库存数量  **/
    private BigDecimal stockableQty;

    /** 锁定数量 */
    private BigDecimal lockedQty;

    /** 可操作数量 */
    private BigDecimal operableQty;
    /** ------出库单引用字段------ **/

    /** 已发货数量 */
    private BigDecimal deliveredQty;

}
