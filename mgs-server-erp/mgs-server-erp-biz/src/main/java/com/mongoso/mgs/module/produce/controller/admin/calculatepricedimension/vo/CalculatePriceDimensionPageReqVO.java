package com.mongoso.mgs.module.produce.controller.admin.calculatepricedimension.vo;

import jakarta.validation.constraints.NotNull;
import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 计价维度 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CalculatePriceDimensionPageReqVO extends PageParam {

    /** 维度名称 */
    private String calculatePriceDimensionName;
    private String calculatePriceDimensionValue;

    /** 维度编码 */
    private String calculatePriceDimensionCode;

    /** 业务类型，0：计价，1：计时 */
    @NotNull(message = "bizType 不能为空")
    private Integer bizType;
    private Integer type;

    /** 父级id */
    private Long parentId;

    /** 是否启用 */
    private Integer isEnable;

    /** 创建人ID */
    private Long createdId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
