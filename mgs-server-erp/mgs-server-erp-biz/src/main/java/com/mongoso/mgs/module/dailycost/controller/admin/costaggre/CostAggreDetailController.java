package com.mongoso.mgs.module.dailycost.controller.admin.costaggre;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.*;
import com.mongoso.mgs.module.dailycost.service.costaggre.detail.CostAggreDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 成本归集明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostAggreDetailController {

    @Resource
    private CostAggreDetailService aggreDetailService;

    @OperateLog("成本归集明细添加或编辑")
    @PostMapping("/aggreDetailAdit")
    @PreAuthorize("@ss.hasPermission('costAggreDetail:adit')")
    public ResultX<Long> costAggreDetailAdit(@Valid @RequestBody CostAggreDetailAditReqVO reqVO) {
        return success(reqVO.getAggreDetailId() == null
                            ? aggreDetailService.costAggreDetailAdd(reqVO)
                            : aggreDetailService.costAggreDetailEdit(reqVO));
    }

    @OperateLog("成本归集明细删除")
    @PostMapping("/aggreDetailDel")
    @PreAuthorize("@ss.hasPermission('costAggreDetail:delete')")
    public ResultX<Boolean> costAggreDetailDel(@Valid @RequestBody CostAggreDetailPrimaryReqVO reqVO) {
        aggreDetailService.costAggreDetailDel(reqVO.getAggreDetailId());
        return success(true);
    }

    @OperateLog("成本归集明细详情")
    @PostMapping("/aggreDetailDetail")
    @PreAuthorize("@ss.hasPermission('costAggreDetail:query')")
    public ResultX<CostAggreDetailRespVO> costAggreDetailDetail(@Valid @RequestBody CostAggreDetailPrimaryReqVO reqVO) {
        return success(aggreDetailService.costAggreDetailDetail(reqVO.getAggreDetailId()));
    }

    @OperateLog("成本归集明细列表")
    @PostMapping("/aggreDetailList")
    @PreAuthorize("@ss.hasPermission('costAggreDetail:query')")
    @DataPermission
    public ResultX<List<CostAggreDetailRespVO>> costAggreDetailList(@Valid @RequestBody CostAggreDetailQueryReqVO reqVO) {
        return success(aggreDetailService.costAggreDetailList(reqVO));
    }

    @OperateLog("成本归集明细分页")
    @PostMapping("/aggreDetailPage")
    @PreAuthorize("@ss.hasPermission('costAggreDetail:query')")
    @DataPermission
    public ResultX<PageResult<CostAggreDetailRespVO>> costAggreDetailPage(@Valid @RequestBody CostAggreDetailPageReqVO reqVO) {
        return success(aggreDetailService.costAggreDetailPage(reqVO));
    }

}
