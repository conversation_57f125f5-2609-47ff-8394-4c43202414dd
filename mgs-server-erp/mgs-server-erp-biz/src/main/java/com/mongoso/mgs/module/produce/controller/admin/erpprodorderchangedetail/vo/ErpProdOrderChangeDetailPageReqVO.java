package com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 生产物料 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpProdOrderChangeDetailPageReqVO extends PageParam {

    /** 主键ID */
    private Long prodOrderChangeDetailId;

    /** 生产订单变更单id */
    private Long prodOrderChangeId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 行号 */
    private Integer rowNo;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 计划开始日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanStartDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanStartDate;

    /** 计划完成日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startPlanEndDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endPlanEndDate;

    /** 生产计划数量 */
    private BigDecimal prodPlanQty;

    /** 工单计划数量 */
    private BigDecimal workPlanQty;

    /** 委外采购数量 */
    private BigDecimal outsourceQty;

    /** 生产数量 */
    private BigDecimal producedQty;

    /** 良品数量 */
    private BigDecimal okQty;

    /** 不良品数量 */
    private BigDecimal ngQty;

    /** 委外采购入库数量 */
    private BigDecimal outsourceInboundQty;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
