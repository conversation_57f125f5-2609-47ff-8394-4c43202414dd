package com.mongoso.mgs.module.salary.dal.db.ployrelatedemployee;

import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 策略关联人员 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_ploy_related_employee", autoResultMap = true)
//@KeySequence("u_ploy_related_employee_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PloyRelatedEmployeeDO extends OperateDO {

    /** 策略关联人员主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long ployRelatedEmployeeId;

    /** 工资单归集策略ID */
    private Long payrollAggreStrategyId;

    /** 员工档案ID */
    private Long employeeArchivesId;

    /** 策略编码 */
    private String strategyCode;

    /** 创建人ID */
    private Long createdId;

    /** 行号 */
    private Integer rowNo;

    /** 版本号 */
    private Integer version;


}
