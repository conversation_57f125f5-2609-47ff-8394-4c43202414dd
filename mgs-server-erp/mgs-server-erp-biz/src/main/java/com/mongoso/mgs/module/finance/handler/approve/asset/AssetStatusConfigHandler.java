package com.mongoso.mgs.module.finance.handler.approve.asset;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.finance.dal.db.asset.assetregister.AssetRegisterDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetstatusconfig.AssetStatusConfigDO;
import com.mongoso.mgs.module.finance.dal.mysql.asset.assetstatusconfig.AssetStatusConfigMapper;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class AssetStatusConfigHandler extends FlowApproveHandler<AssetStatusConfigDO> {
    @Resource
    private AssetStatusConfigMapper assetStatusConfigMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(AssetStatusConfigDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(AssetStatusConfigDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(AssetStatusConfigDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getAssetStatusConfigId())
                .objCode(item.getStatusName())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(AssetStatusConfigDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {

        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            
        }

        return true;
    }


    @Override
    public Integer handleBusinessData(AssetStatusConfigDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = item.getAssetStatusConfigId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        AssetStatusConfigDO exist  = assetStatusConfigMapper.selectById(id);
        exist.setApprovedBy(loginUser.getFullUserName());
        exist.setApprovedDt(LocalDateTime.now());
        exist.setDataStatus(dataStatus.shortValue());
        // todo 审核需上游 已审核，反审核 需下游 未审核
        // 审核通过，反审核通过
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {

        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){

        }
        //更新业务数据
        Integer updateCount = assetStatusConfigMapper.updateById(exist);

        return updateCount;
    }
}
