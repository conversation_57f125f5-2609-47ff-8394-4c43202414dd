package com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工单领料单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ReturnItemReqVO implements Serializable {

    private Integer rowNo;// 行号
    private Long materialId;// 物料BomID
    private String materialCode;// 物料编码
    private String warehouseOrgId;// 仓库id
    private BigDecimal qty;// 当前数量
    private String remark;// 备注

}
