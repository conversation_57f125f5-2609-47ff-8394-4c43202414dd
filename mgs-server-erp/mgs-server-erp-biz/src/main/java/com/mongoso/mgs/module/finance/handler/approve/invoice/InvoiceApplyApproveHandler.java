package com.mongoso.mgs.module.finance.handler.approve.invoice;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.InvoiceApplyEnum;
import com.mongoso.mgs.module.finance.controller.admin.strategyconfig.vo.StrategyConfigRespVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapply.InvoiceApplyDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapplydetail.InvoiceApplyDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceissue.InvoiceIssueDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicependingdetail.InvoicePendingDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplan.InvoicePlanDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail.InvoicePlanDetailDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapply.InvoiceApplyMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapplydetail.InvoiceApplyDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceissue.InvoiceIssueMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplan.InvoicePlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplandetail.InvoicePlanDetailMapper;
import com.mongoso.mgs.module.finance.service.strategyconfig.StrategyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InvoiceApplyApproveHandler extends FlowApproveHandler<InvoiceApplyDO> {

    @Resource
    private InvoiceApplyMapper invoiceApplyMapper;

    @Resource
    private InvoiceApplyDetailMapper invoiceApplyDetailMapper;

    @Resource
    private InvoicePlanMapper invoicePlanMapper;

    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;

    @Resource
    private InvoiceIssueMapper invoiceIssueMapper;
    @Override
    protected ApproveCommonAttrs approvalAttributes(InvoiceApplyDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(InvoiceApplyDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(InvoiceApplyDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getInvoiceApplyId())
                .objCode(item.getInvoiceApplyNo())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(InvoiceApplyDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
            //判断上游是否审核通过了
            InvoicePlanDO invoicePlanDO = invoicePlanMapper.selectById(item.getInvoicePlanId());
            if (Objects.isNull(invoicePlanDO)){
                failItem.setCode(item.getInvoiceApplyNo());
                failItem.setReason("上游不存在");
                return false;
            }
            if (invoicePlanDO.getDataStatus() != DataStatusEnum.APPROVED.getKey().shortValue()){
                log.info("上游未审核");
                failItem.setCode(item.getInvoiceApplyNo());
                failItem.setReason("上游未审核");
                return false;
            }
            //只比对明细的金额，不比对总金额
            List<InvoicePlanDetailDO> invoicePlanDetailDOS = invoicePlanDetailMapper.selectList(new LambdaQueryWrapper<InvoicePlanDetailDO>()
                    .eq(InvoicePlanDetailDO::getInvoicePlanId, item.getInvoicePlanId())
            );
            if (CollectionUtils.isEmpty(invoicePlanDetailDOS)){
                failItem.setCode(item.getInvoiceApplyNo());
                failItem.setReason("上游明细不存在");
                return false;
            }
            Map<Long, InvoicePlanDetailDO> invoicePlanDetailMap = invoicePlanDetailDOS.stream().collect(Collectors.toMap(
                    InvoicePlanDetailDO::getInvoicePlanDetailId, Function.identity(), (s1, s2) -> s1)
            );
            List<InvoiceApplyDetailDO> invoiceApplyDetailDOS = invoiceApplyDetailMapper.selectList(new LambdaQueryWrapper<InvoiceApplyDetailDO>()
                    .eq(InvoiceApplyDetailDO::getInvoiceApplyId, item.getInvoiceApplyId())
            );
            for (InvoiceApplyDetailDO detailDO : invoiceApplyDetailDOS){
                InvoicePlanDetailDO invoicePlanDetailDO = invoicePlanDetailMap.get(detailDO.getInvoicePlanDetailId());
                if (detailDO.getPlanInvoiceAmtInclTax().abs().compareTo(invoicePlanDetailDO.getCanAmt().abs()) > 0){
                    failItem.setCode(item.getInvoiceApplyNo());
                    failItem.setReason("金额超过可申请金额");
                    return false;
                }
            }
//            //判断金额
//            if (item.getApplyInvoiceTotalInclTax().abs().compareTo(invoicePlanDO.getCanAmt().abs()) > 0){
//                log.info("金额超过可申请金额");
//                failItem.setCode(item.getInvoiceApplyNo());
//                failItem.setReason("金额超过可申请金额");
//                return false;
//            }

        }else if (buttonType == DataButtonEnum.NOT_APPROVE.key.intValue()){
            //判断下游是否存在已审核的
            List<InvoiceIssueDO> invoiceIssueDOS = invoiceIssueMapper.selectList(new LambdaQueryWrapper<InvoiceIssueDO>()
                    .eq(InvoiceIssueDO::getInvoiceApplyId, item.getInvoiceApplyId())
                    .eq(InvoiceIssueDO::getDataStatus, DataStatusEnum.APPROVED.getKey().shortValue())
            );
            if (CollectionUtils.isNotEmpty(invoiceIssueDOS)){
                log.info("下游存在为反审核的数据");
                failItem.setCode(item.getInvoiceApplyNo());
                failItem.setReason("下游存在为已审核的数据");
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer handleBusinessData(InvoiceApplyDO currentDO, BaseApproveRequest request) {
        //当前对象
        if (currentDO == null){
            return 1;
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = currentDO.getInvoiceApplyId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        FailItem failItem = request.getFailItem();

        InvoiceApplyDO invoiceApplyDO = currentDO;
        try {
            if (buttonType == DataButtonEnum.APPROVE.getKey().intValue() || buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()){
                //查询发票申请数据
                //InvoiceApplyDO invoiceApplyDO = invoiceApplyMapper.selectById(id);
    //            invoiceApplyDO.setApprovedBy(loginUser.getFullUserName());
    //            invoiceApplyDO.setApprovedDt(LocalDateTime.now());
    //            invoiceApplyDO.setDataStatus(dataStatus.shortValue());
    //            invoiceApplyMapper.updateById(invoiceApplyDO);

                //查询上游开票计划
                InvoicePlanDO invoicePlanDO = invoicePlanMapper.selectById(invoiceApplyDO.getInvoicePlanId());
                if (Objects.isNull(invoicePlanDO)){
                    failItem.setCode(invoiceApplyDO.getInvoiceApplyNo());
                    failItem.setReason("上游数据不存在");
                    throw new BizException("5001", "上游数据不存在!");
                }
                BigDecimal amt = BigDecimal.ZERO;

                //已处理数量
                BigDecimal readyQty = BigDecimal.ZERO;
                BigDecimal readyAmt = BigDecimal.ZERO;

                //可处理数量
                BigDecimal canQty = BigDecimal.ZERO;
                BigDecimal canAmt = BigDecimal.ZERO;
                if (buttonType == DataButtonEnum.APPROVE.key.intValue()){
                    //审核，剩余可计划金额-
                    amt = invoicePlanDO.getRemainPlanTotalInclTax().subtract(invoiceApplyDO.getApplyInvoiceTotalInclTax());

                    readyAmt = invoicePlanDO.getReadyAmt().add(invoiceApplyDO.getApplyInvoiceTotalInclTax());
                    readyQty = invoicePlanDO.getReadyQty().add(invoiceApplyDO.getApplyInvoiceQty());

                    canAmt = invoicePlanDO.getCanAmt().subtract(invoiceApplyDO.getApplyInvoiceTotalInclTax());
                    canQty = invoicePlanDO.getCanQty().subtract(invoiceApplyDO.getApplyInvoiceQty());

                }else {
                    //反审核，剩余可计划金额+
                    amt = invoicePlanDO.getRemainPlanTotalInclTax().add(invoiceApplyDO.getApplyInvoiceTotalInclTax());

                    readyAmt = invoicePlanDO.getReadyAmt().subtract(invoiceApplyDO.getApplyInvoiceTotalInclTax());
                    readyQty = invoicePlanDO.getReadyQty().subtract(invoiceApplyDO.getApplyInvoiceQty());

                    canAmt = invoicePlanDO.getCanAmt().add(invoiceApplyDO.getApplyInvoiceTotalInclTax());
                    canQty = invoicePlanDO.getCanQty().add(invoiceApplyDO.getApplyInvoiceQty());
                }
                //设置剩余可计划金额
                invoicePlanDO.setRemainPlanTotalInclTax(amt);

                invoicePlanDO.setReadyAmt(readyAmt);
                invoicePlanDO.setReadyQty(readyQty);
                invoicePlanDO.setCanAmt(canAmt);
                invoicePlanDO.setCanQty(canQty);
                //设置申请状态
                if (invoicePlanDO.getRemainPlanTotalInclTax().compareTo(BigDecimal.ZERO) == 0){
                    invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.ISSUE_APPLY.type.shortValue());
                }else if (invoicePlanDO.getRemainPlanTotalInclTax().compareTo(invoicePlanDO.getPlanInvoiceTotalInclTax()) == 0){
                    invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.NOT_APPLY.type.shortValue());
                }else {
                    invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.PART_APPLY.type.shortValue());
                }
                //明细更新
                List<InvoiceApplyDetailDO> invoiceApplyDetailDOS = invoiceApplyDetailMapper.selectList(new LambdaQueryWrapper<InvoiceApplyDetailDO>().eq(InvoiceApplyDetailDO::getInvoiceApplyId, id));

                //获取开票计划明细id
                List<Long> planDetailIds = new ArrayList<>();
                invoiceApplyDetailDOS.forEach(applyDetail->{
                    planDetailIds.add(applyDetail.getInvoicePlanDetailId());
                });

                List<InvoicePlanDetailDO> planDetailList = invoicePlanDetailMapper.selectBatchIds(planDetailIds);
                Map<Long, InvoicePlanDetailDO> planDetailMap = planDetailList.stream().collect(Collectors.toMap(InvoicePlanDetailDO::getInvoicePlanDetailId, Function.identity(), (s1, s2) -> s1));
                List<InvoicePlanDetailDO>  planDetailUpdateList = new ArrayList<>();
                for (InvoiceApplyDetailDO applyDetail : invoiceApplyDetailDOS){
                    InvoicePlanDetailDO planDetailDO = planDetailMap.get(applyDetail.getInvoicePlanDetailId());
                    //判断上游数据
                    if (Objects.isNull(planDetailDO)){
                        failItem.setCode(invoiceApplyDO.getInvoiceApplyNo());
                        failItem.setReason("上游明细的数据异常");
                        throw new BizException("5001", "上游明细的数据异常!");
                    }

                    BigDecimal detailAmt = BigDecimal.ZERO;
                    BigDecimal detailQty = BigDecimal.ZERO;

                    //已处理数量
                    BigDecimal readyDetailQty = BigDecimal.ZERO;
                    BigDecimal readyDetailAmt = BigDecimal.ZERO;

                    //可处理数量
                    BigDecimal canDetailQty = BigDecimal.ZERO;
                    BigDecimal canDetailAmt = BigDecimal.ZERO;
                    if (buttonType == DataButtonEnum.APPROVE.key.intValue()){
                        //审核，则剩余数量和金额-
                       detailAmt = planDetailDO.getPlanInvoiceAmtInclTax().subtract(applyDetail.getApplyInvoiceAmtInclTax());
                       detailQty = planDetailDO.getPlanInvoiceQty().subtract(applyDetail.getApplyInvoiceQty());

                        readyDetailAmt = planDetailDO.getReadyAmt().add(applyDetail.getApplyInvoiceAmtInclTax());
                        readyDetailQty = planDetailDO.getReadyQty().add(applyDetail.getApplyInvoiceQty());

                        canDetailAmt = planDetailDO.getCanAmt().subtract(applyDetail.getApplyInvoiceAmtInclTax());
                        canDetailQty = planDetailDO.getCanQty().subtract(applyDetail.getApplyInvoiceQty());

                    }else {
                        //反审核，则剩余数量和金额+
                        detailAmt = planDetailDO.getPlanInvoiceAmtInclTax().add(applyDetail.getApplyInvoiceAmtInclTax());
                        detailQty = planDetailDO.getPlanInvoiceQty().add(applyDetail.getApplyInvoiceQty());

                        readyDetailAmt = planDetailDO.getReadyAmt().subtract(applyDetail.getApplyInvoiceAmtInclTax());
                        readyDetailQty = planDetailDO.getReadyQty().subtract(applyDetail.getApplyInvoiceQty());

                        canDetailAmt = planDetailDO.getCanAmt().add(applyDetail.getApplyInvoiceAmtInclTax());
                        canDetailQty = planDetailDO.getCanQty().add(applyDetail.getApplyInvoiceQty());
                    }
                    planDetailDO.setPlanInvoiceAmtInclTax(detailAmt);
                    planDetailDO.setPlanInvoiceQty(detailQty);

                    planDetailDO.setCanAmt(canDetailAmt);
                    planDetailDO.setCanQty(canDetailQty);
                    planDetailDO.setReadyQty(readyDetailQty);
                    planDetailDO.setReadyAmt(readyDetailAmt);
                    planDetailDO.setRemainingPlanQty(canDetailQty);
                    planDetailDO.setRemainingPlanAmt(canDetailAmt);

                    planDetailUpdateList.add(planDetailDO);

                    //修改剩余开票数量和金额
                    invoiceApplyMapper.updateRemainApplyAmtAndQty(planDetailDO.getInvoicePlanDetailId(), canDetailAmt, canQty);

                    //修改申请数量的剩余
                    applyDetail.setRemainingApplyQty(applyDetail.getApplyInvoiceQty());
                    applyDetail.setRemainingApplyAmt(applyDetail.getApplyInvoiceAmtInclTax());
                    applyDetail.setQty(applyDetail.getApplyInvoiceQty());



                }

                invoicePlanDetailMapper.updateBatch(planDetailUpdateList);
                invoiceApplyDetailMapper.updateBatch(invoiceApplyDetailDOS);

                invoicePlanMapper.updateById(invoicePlanDO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            failItem.setCode(invoiceApplyDO.getInvoiceApplyNo());
            failItem.setReason("审核申请开票异常");
            throw e;

        }

        //查询发票申请数据

        invoiceApplyDO.setApprovedBy(loginUser.getFullUserName());
        invoiceApplyDO.setApprovedDt(LocalDateTime.now());
        invoiceApplyDO.setDataStatus(dataStatus.shortValue());
        invoiceApplyMapper.updateById(invoiceApplyDO);

        return 1;
    }
}
