package com.mongoso.mgs.module.produce.dal.mysql.erpprodorderchangedetail;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderchangedetail.ErpProdOrderChangeDetailDO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo.*;

/**
 * 生产物料 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpProdOrderChangeDetailMapper extends BaseMapperX<ErpProdOrderChangeDetailDO> {

    default PageResult<ErpProdOrderChangeDetailDO> selectPageOld(ErpProdOrderChangeDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ErpProdOrderChangeDetailDO>lambdaQueryX()
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeDetailId, reqVO.getProdOrderChangeDetailId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeId, reqVO.getProdOrderChangeId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(ErpProdOrderChangeDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRowNo, reqVO.getRowNo())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdPlanQty, reqVO.getProdPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getWorkPlanQty, reqVO.getWorkPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceQty, reqVO.getOutsourceQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProducedQty, reqVO.getProducedQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOkQty, reqVO.getOkQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getNgQty, reqVO.getNgQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceInboundQty, reqVO.getOutsourceInboundQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ErpProdOrderChangeDetailDO::getCreatedDt));
    }



    default PageResult<ErpProdOrderChangeDetailDO> selectPage(ErpProdOrderChangeDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ErpProdOrderChangeDetailDO>lambdaQueryX()
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeDetailId, reqVO.getProdOrderChangeDetailId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeId, reqVO.getProdOrderChangeId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(ErpProdOrderChangeDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRowNo, reqVO.getRowNo())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdPlanQty, reqVO.getProdPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getWorkPlanQty, reqVO.getWorkPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceQty, reqVO.getOutsourceQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProducedQty, reqVO.getProducedQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOkQty, reqVO.getOkQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getNgQty, reqVO.getNgQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceInboundQty, reqVO.getOutsourceInboundQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(ErpProdOrderChangeDetailDO::getCreatedDt));
    }

    default List<ErpProdOrderChangeDetailDO> selectListOld(ErpProdOrderChangeDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ErpProdOrderChangeDetailDO>lambdaQueryX()
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeDetailId, reqVO.getProdOrderChangeDetailId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeId, reqVO.getProdOrderChangeId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(ErpProdOrderChangeDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRowNo, reqVO.getRowNo())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdPlanQty, reqVO.getProdPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getWorkPlanQty, reqVO.getWorkPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceQty, reqVO.getOutsourceQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProducedQty, reqVO.getProducedQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOkQty, reqVO.getOkQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getNgQty, reqVO.getNgQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceInboundQty, reqVO.getOutsourceInboundQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByAsc(ErpProdOrderChangeDetailDO::getRowNo));
    }

    default List<ErpProdOrderChangeDetailDO> selectList(ErpProdOrderChangeDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ErpProdOrderChangeDetailDO>lambdaQueryX()
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeDetailId, reqVO.getProdOrderChangeDetailId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdOrderChangeId, reqVO.getProdOrderChangeId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(ErpProdOrderChangeDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRowNo, reqVO.getRowNo())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanStartDate, reqVO.getStartPlanStartDate(), reqVO.getEndPlanStartDate())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getPlanEndDate, reqVO.getStartPlanEndDate(), reqVO.getEndPlanEndDate())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProdPlanQty, reqVO.getProdPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getWorkPlanQty, reqVO.getWorkPlanQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceQty, reqVO.getOutsourceQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getProducedQty, reqVO.getProducedQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOkQty, reqVO.getOkQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getNgQty, reqVO.getNgQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getOutsourceInboundQty, reqVO.getOutsourceInboundQty())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ErpProdOrderChangeDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ErpProdOrderChangeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByAsc(ErpProdOrderChangeDetailDO::getRowNo));
    }

    default int batchDelete(Long prodOrderChangeId){
        return delete(LambdaQueryWrapperX.<ErpProdOrderChangeDetailDO>lambdaQueryX()
                .eq(ErpProdOrderChangeDetailDO::getProdOrderChangeId,prodOrderChangeId)
        );
    }

}