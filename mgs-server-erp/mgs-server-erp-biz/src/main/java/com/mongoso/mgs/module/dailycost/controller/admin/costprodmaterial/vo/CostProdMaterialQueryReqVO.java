package com.mongoso.mgs.module.dailycost.controller.admin.costprodmaterial.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 生产物料成本单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class CostProdMaterialQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long costProdMaterialId;

    /** 主键IDList */
    private List<Long> costProdMaterialIdList;

    /** 报工记录id */
    private Long reportedWorkId;

    /** 摊销状态 */
    private Short amortiseStatus;

    /** 报工记录编码 */
    private String reportedWorkCode;

    /** 工序id */
    private Long processId;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 报工人ID */
    private String reportedId;

    /** 报工时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime reportedDt;

    /** 报工数量 */
    private BigDecimal reportedQty;

    /** 原料编码 */
    private String rawMaterialCode;

    /** 原料ID */
    private Long rawMaterialId;

    /** 预估用量 */
    private BigDecimal estimatedQty;

    /** 单价 */
    private BigDecimal price;

    /** 总金额 */
    private BigDecimal totalAmt;

    /** 承担部门 */
    private String undertakeOrgId;

    /** 承担物料id */
    private Long undertakeMaterialId;

    /** 承担物料编码 */
    private String undertakeMaterialCode;

    /**物料BOMID路径*/
    private String materialBomIdPath;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 报工时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startReportedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endReportedDt;

}
