package com.mongoso.mgs.module.sale.service.materialprice;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.sale.controller.admin.materialprice.vo.*;
import com.mongoso.mgs.module.sale.dal.db.materialprice.MaterialPriceDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.sale.dal.mysql.materialprice.MaterialPriceMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.sale.enums.ErrorCodeConstants.*;


/**
 * 商品价格 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialPriceServiceImpl implements MaterialPriceService {

    @Resource
    private MaterialPriceMapper materialPriceMapper;

    @Override
    public Long materialPriceAdd(MaterialPriceAditReqVO reqVO) {
        // 插入
        MaterialPriceDO materialPrice = BeanUtilX.copy(reqVO, MaterialPriceDO::new);
        materialPriceMapper.insert(materialPrice);
        // 返回
        return materialPrice.getMaterialPriceId();
    }

    @Override
    public Long materialPriceEdit(MaterialPriceAditReqVO reqVO) {
        // 校验存在
        this.materialPriceValidateExists(reqVO.getMaterialPriceId());
        // 更新
        MaterialPriceDO materialPrice = BeanUtilX.copy(reqVO, MaterialPriceDO::new);
        materialPriceMapper.updateById(materialPrice);
        // 返回
        return materialPrice.getMaterialPriceId();
    }

    @Override
    public void materialPriceDelete(Long materialPriceId) {
        // 校验存在
        this.materialPriceValidateExists(materialPriceId);
        // 删除
        materialPriceMapper.deleteById(materialPriceId);
    }

    private MaterialPriceDO materialPriceValidateExists(Long materialPriceId) {
        MaterialPriceDO materialPrice = materialPriceMapper.selectById(materialPriceId);
        if (materialPrice == null) {
            // throw exception(MATERIAL_PRICE_NOT_EXISTS);
            throw new BizException("5001", "商品价格不存在");
        }
        return materialPrice;
    }

    @Override
    public MaterialPriceRespVO materialPriceDetail(Long materialPriceId) {
        MaterialPriceDO data = materialPriceMapper.selectById(materialPriceId);
        return BeanUtilX.copy(data, MaterialPriceRespVO::new);
    }

    @Override
    public List<MaterialPriceRespVO> materialPriceList(MaterialPriceQueryReqVO reqVO) {
        List<MaterialPriceDO> data = materialPriceMapper.selectList(reqVO);
        return BeanUtilX.copy(data, MaterialPriceRespVO::new);
    }

    @Override
    public PageResult<MaterialPriceRespVO> materialPricePage(MaterialPricePageReqVO reqVO) {
        PageResult<MaterialPriceDO> data = materialPriceMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, MaterialPriceRespVO::new);
    }

}
