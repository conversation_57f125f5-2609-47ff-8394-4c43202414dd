package com.mongoso.mgs.module.base.controller.admin.hzmaterialbomlist;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.base.controller.admin.hzmaterialbomlist.vo.*;
import com.mongoso.mgs.module.base.dal.db.hzmaterialbomlist.HzMaterialBomListDO;
import com.mongoso.mgs.module.base.service.hzmaterialbomlist.HzMaterialBomListService;

/**
 * 物料关联BOM清单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/base")
@Validated
public class HzMaterialBomListController {

    @Resource
    private HzMaterialBomListService hzMaterialBomListService;

    @OperateLog("物料关联BOM清单添加或编辑")
    @PostMapping("/hzMaterialBomListAdit")
    @PreAuthorize("@ss.hasPermission('hzMaterialBomList:adit')")
    public ResultX<Long> hzMaterialBomListAdit(@Valid @RequestBody HzMaterialBomListAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? hzMaterialBomListService.hzMaterialBomListAdd(reqVO)
                            : hzMaterialBomListService.hzMaterialBomListEdit(reqVO));
    }

    @OperateLog("物料关联BOM清单删除")
    @PostMapping("/hzMaterialBomListDel")
    @PreAuthorize("@ss.hasPermission('hzMaterialBomList:del')")
    public ResultX<Boolean> hzMaterialBomListDel(@Valid @RequestBody HzMaterialBomListPrimaryReqVO reqVO) {
        hzMaterialBomListService.hzMaterialBomListDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("物料关联BOM清单详情")
    @PostMapping("/hzMaterialBomListDetail")
    @PreAuthorize("@ss.hasPermission('hzMaterialBomList:query')")
    public ResultX<HzMaterialBomListRespVO> hzMaterialBomListDetail(@Valid @RequestBody HzMaterialBomListPrimaryReqVO reqVO) {
        HzMaterialBomListDO oldDO = hzMaterialBomListService.hzMaterialBomListDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, HzMaterialBomListRespVO::new));
    }

    @OperateLog("物料关联BOM清单列表")
    @PostMapping("/hzMaterialBomListList")
    @PreAuthorize("@ss.hasPermission('hzMaterialBomList:query')")
    public ResultX<List<HzMaterialBomListRespVO>> hzMaterialBomListList(@Valid @RequestBody HzMaterialBomListQueryReqVO reqVO) {
        List<HzMaterialBomListDO> list = hzMaterialBomListService.hzMaterialBomListList(reqVO);
        return success(BeanUtilX.copyList(list, HzMaterialBomListRespVO::new));
    }

    @OperateLog("物料关联BOM清单分页")
    @PostMapping("/hzMaterialBomListPage")
    @PreAuthorize("@ss.hasPermission('hzMaterialBomList:query')")
    public ResultX<PageResult<HzMaterialBomListRespVO>> hzMaterialBomListPage(@Valid @RequestBody HzMaterialBomListPageReqVO reqVO) {
        PageResult<HzMaterialBomListDO> pageResult = hzMaterialBomListService.hzMaterialBomListPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, HzMaterialBomListRespVO::new));
    }

}
