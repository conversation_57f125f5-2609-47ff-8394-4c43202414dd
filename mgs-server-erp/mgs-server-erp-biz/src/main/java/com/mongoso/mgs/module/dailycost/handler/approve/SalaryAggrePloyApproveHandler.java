package com.mongoso.mgs.module.dailycost.handler.approve;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feeapply.FeeApplyDO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.vo.SalaryAggrePloyAditReqVO;
import com.mongoso.mgs.module.salary.dal.db.salaryaggreploy.SalaryAggrePloyDO;
import com.mongoso.mgs.module.salary.dal.mysql.salaryaggreploy.SalaryAggrePloyMapper;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24
 * @description
 */
@Component
public class SalaryAggrePloyApproveHandler extends FlowApproveHandler<SalaryAggrePloyDO> {

    @Resource
    private SalaryAggrePloyMapper salaryAggrePloyMapper;
    @Resource
    private SalaryAggrePloyMapper aggrePloyMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(SalaryAggrePloyDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(SalaryAggrePloyDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(SalaryAggrePloyDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getPayrollAggreStrategyId())
                .objCode(item.getStrategyCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(SalaryAggrePloyDO item, BaseApproveRequest request) {
        return true;
    }

    @Override
    public Integer handleBusinessData(SalaryAggrePloyDO item, BaseApproveRequest request) {
        Long id = item.getPayrollAggreStrategyId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        FailItem failItem = request.getFailItem();

        SalaryAggrePloyDO aggreDO = salaryAggrePloyMapper.selectById(id);

        if (buttonType == DataButtonEnum.APPROVE.getKey()){
            //审核
            SalaryAggrePloyAditReqVO req = new SalaryAggrePloyAditReqVO();
            req.setPayrollAggreStrategyId(aggreDO.getPayrollAggreStrategyId());
            req.setApplyPersonIds(aggreDO.getApplyPersonIds());
            List<SalaryAggrePloyDO> exsitList = aggrePloyMapper.selectByApplyPersonIds(req);
            if (ObjUtilX.isNotEmpty(exsitList)) {
                //throw new BizException("5001", "您选择的适用人员已存在其他工资单归集策略，请重新选择");
                failItem.setReason("您选择的适用人员已存在其他工资单归集策略，请重新选择");
                failItem.setCode(aggreDO.getStrategyCode());
                this.updateDataStatus(aggreDO,buttonType);
                return 0;
            }
        }
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
            //反审核

        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        aggreDO.setApprovedBy(loginUser.getFullUserName());
        aggreDO.setApprovedDt(LocalDateTime.now());
        aggreDO.setDataStatus(dataStatus);
        return salaryAggrePloyMapper.updateById(aggreDO);
    }


    public void updateDataStatus(SalaryAggrePloyDO aggreDO,Integer buttonType){
        if (buttonType == DataButtonEnum.APPROVE.getKey()){
            aggreDO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
            aggreDO.setDataStatus(DataStatusEnum.APPROVED.key);
        }else if (buttonType == DataButtonEnum.CANCEL.getKey()){
            aggreDO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        }else if (buttonType == DataButtonEnum.NOT_CANCEL.getKey()){
            aggreDO.setDataStatus(DataStatusEnum.CANCEL.key);
        }
        aggreDO.setUpdatedBy(null);
        aggreDO.setUpdatedDt(null);
        salaryAggrePloyMapper.updateById(aggreDO);
    }
}
