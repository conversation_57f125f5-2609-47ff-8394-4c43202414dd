package com.mongoso.mgs.module.warehouse.controller.admin.stocklock;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.ApproveResult;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.warehouse.controller.admin.stocklock.vo.*;
import com.mongoso.mgs.module.warehouse.service.stocklock.StockLockService;

/**
 * 库存锁定单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warehouse")
@Validated
public class StockLockController {

    @Resource
    private StockLockService stockLockService;

    @OperateLog("库存锁定单添加或编辑")
    @PostMapping("/stockLockAdit")
    @PreAuthorize("@ss.hasPermission('stockLock:adit')")
    public ResultX<Long> stockLockAdit(@Valid @RequestBody StockLockAditReqVO reqVO) {
        return success(reqVO.getLockId() == null
                            ? stockLockService.stockLockAdd(reqVO)
                            : stockLockService.stockLockEdit(reqVO));
    }

    @OperateLog("库存锁定单删除")
    @PostMapping("/stockLockDel")
    @PreAuthorize("@ss.hasPermission('stockLock:del')")
    public ResultX<Boolean> stockLockDel(@Valid @RequestBody StockLockPrimaryReqVO reqVO) {
        stockLockService.stockLockDel(reqVO.getLockId());
        return success(true);
    }

    @OperateLog("库存锁定单批量删除")
    @PostMapping("/stockLockDelBatch")
    @PreAuthorize("@ss.hasPermission('stockLock:del')")
    public ResultX<BatchResult>  stockLockDelBatch(@Valid @RequestBody IdsReqVO reqVO) {
        return stockLockService.stockLockDelBatch(reqVO);
    }

    @OperateLog("库存锁定单详情")
    @PostMapping("/stockLockDetail")
    @PreAuthorize("@ss.hasPermission('stockLock:query')")
    public ResultX<StockLockRespVO> stockLockDetail(@Valid @RequestBody StockLockPrimaryReqVO reqVO) {
        return success(stockLockService.stockLockDetail(reqVO.getLockId()));
    }

    @OperateLog("库存锁定单引用详情")
    @PostMapping("/stockLockQuoteDetail")
    @PreAuthorize("@ss.hasPermission('stockLock:query')")
    public ResultX<StockLockRespVO> stockLockQuoteDetail(@Valid @RequestBody StockLockPrimaryReqVO reqVO) {
        return success(stockLockService.stockLockQuoteDetail(reqVO.getLockId()));
    }


    @OperateLog("库存锁定单列表")
    @PostMapping("/stockLockList")
    @PreAuthorize("@ss.hasPermission('stockLock:query')")
    @DataPermission
    public ResultX<List<StockLockRespVO>> stockLockList(@Valid @RequestBody StockLockQueryReqVO reqVO) {
        return success(stockLockService.stockLockList(reqVO));
    }

    @OperateLog("库存锁定单被引用列表")
    @PostMapping("/stockLockQuotedList")
    @PreAuthorize("@ss.hasPermission('stockLock:query')")
    public ResultX<List<StockLockRespVO>> stockLockQuotedList(@Valid @RequestBody StockLockQueryReqVO reqVO) {
        return success(stockLockService.stockLockList(reqVO));
    }

    @OperateLog("库存锁定单分页")
    @PostMapping("/stockLockPage")
    @PreAuthorize("@ss.hasPermission('stockLock:query')")
    @DataPermission
    public ResultX<PageResult<StockLockRespVO>> stockLockPage(@Valid @RequestBody StockLockPageReqVO reqVO) {
        return success(stockLockService.stockLockPage(reqVO));
    }

    @OperateLog("库存锁定单分页")
    @PostMapping("/stockLockQuotedPage")
    @PreAuthorize("@ss.hasPermission('stockLock:query')")
    public ResultX<PageResult<StockLockRespVO>> stockLockQuotedPage(@Valid @RequestBody StockLockPageReqVO reqVO) {
        return success(stockLockService.stockLockPage(reqVO));
    }

    @OperateLog("库存锁定单审核")
    @PostMapping("/stockLockApprove")
    @PreAuthorize("@ss.hasPermission('stockLock:adit')")
    public ResultX<BatchResult> customerApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(stockLockService.stockLockApprove(reqVO));
    }

    @OperateLog("库存锁定单回调接口")
    @PostMapping("/stockLockFlowCallback")
    public ResultX<Object> stockLockFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(stockLockService.stockLockFlowCallback(reqVO));
    }
}
