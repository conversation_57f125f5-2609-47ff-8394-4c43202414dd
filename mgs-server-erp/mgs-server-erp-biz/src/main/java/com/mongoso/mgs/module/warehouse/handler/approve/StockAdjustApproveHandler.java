package com.mongoso.mgs.module.warehouse.handler.approve;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants;
import com.mongoso.mgs.module.warehouse.controller.admin.stockadjust.vo.detail.StockAdjustDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import com.mongoso.mgs.module.warehouse.dal.db.materialstock.ErpMaterialStockDO;
import com.mongoso.mgs.module.warehouse.dal.db.stockadjust.StockAdjustDO;
import com.mongoso.mgs.module.warehouse.dal.db.stockadjust.StockAdjustDetailDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory.ErpInventoryMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stockadjust.StockAdjustDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stockadjust.StockAdjustMapper;
import com.mongoso.mgs.module.warehouse.enums.ErpStcokSFTEnum;
import com.mongoso.mgs.module.warehouse.enums.InventoryAdjustStatusEnum;
import com.mongoso.mgs.module.warehouse.enums.InventoryStatusEnum;
import com.mongoso.mgs.module.warehouse.enums.StockAdjustBizTypeEnum;
import com.mongoso.mgs.module.warehouse.service.erpmaterialstock.ErpMaterialStockService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： ZhouYangqing
 * @date： 2025/4/15
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class StockAdjustApproveHandler extends FlowApproveHandler<StockAdjustDO> {

    @Resource
    private ErpInventoryMapper erpInventoryMapper;

    @Resource
    private StockAdjustMapper stockAdjustMapper;

    @Resource
    private ErpMaterialStockService erpMaterialStockService;

    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;

    @Resource
    private StockAdjustDetailMapper stockAdjustDetailMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(StockAdjustDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(StockAdjustDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(StockAdjustDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getStockAdjustId())
                .objCode(item.getStockAdjustCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(StockAdjustDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //当调整单明细为增加时，对应的库存数量是否小于库存数量，如果小于，则不允许反审核
        StockAdjustDetailQueryReqVO detailQueryReqVO = new StockAdjustDetailQueryReqVO();
        detailQueryReqVO.setStockAdjustId(item.getStockAdjustId());
        List<StockAdjustDetailDO> adjustDetailDOList = stockAdjustDetailMapper.selectList(detailQueryReqVO);

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            //为盘点单库存调整
            if (item.getBizType() == StockAdjustBizTypeEnum.INVENTORY_ADJUST.type) {
                ErpInventoryDO erpInventoryDO = erpInventoryMapper.selectById(item.getRelatedOrderId());

                //盘点单存在判断
                if (ObjUtilX.isEmpty(erpInventoryDO)) {
                    failItem.setCode(item.getStockAdjustCode());
                    failItem.setReason(INVENTORY_NOT_EXIST.getMsg());
                    return false;
                }

                //盘点单审核判断
                if (!Objects.equals(erpInventoryDO.getDataStatus(), DataStatusEnum.APPROVED.getKey())) {
                    failItem.setCode(item.getStockAdjustCode());
                    failItem.setReason(RELATED_ORDER_NOT_APPROVED.getMsg());
                    return false;
                }

                //已完成
                if (!Objects.equals(erpInventoryDO.getInventoryStatus(), InventoryStatusEnum.INVENTORIED.status)) {
                    failItem.setCode(item.getStockAdjustCode());
                    failItem.setReason(ADJUST_NO_FINISHED.getMsg());
                    return false;
                }

                //调整状态-已调整
                if (!Objects.equals(erpInventoryDO.getAdjustStatus(), InventoryAdjustStatusEnum.NEED_ADJUST.status)) {
                    failItem.setCode(item.getStockAdjustCode());
                    failItem.setReason(ADJUST_NO_NEED_ADJUST.getMsg());
                    return false;
                }
            }

            //库存调整单中减少方式的物料数量是否小于对应仓库下库存数量-已锁定数量，是则审核失败
            for (StockAdjustDetailDO detailDO : adjustDetailDOList) {
                if (detailDO.getAdjustMethod() == 0) {//减少
                    BigDecimal adjustQty = detailDO.getAdjustQty();
                    ErpMaterialStockDO erpMaterialStockDO = erpMaterialStockMapper.selectOne(detailDO.getMaterialId(), detailDO.getWarehouseOrgId());

                    if (erpMaterialStockDO.getStockQty() == null){
                        erpMaterialStockDO.setStockQty(BigDecimal.ZERO);
                    }

                    if (erpMaterialStockDO.getLockedQty() == null){
                        erpMaterialStockDO.setLockedQty(BigDecimal.ZERO);
                    }

                    BigDecimal adjustableQty = erpMaterialStockDO.getStockQty().subtract(erpMaterialStockDO.getLockedQty());
                    if (adjustQty != null && adjustableQty.compareTo(adjustQty) < 0) {
                        failItem.setCode(item.getStockAdjustCode());
                        failItem.setReason(ADJUST_DETAIL_REDUCE_GT_STOCK.getMsg());
                        return false;
                    }
                }
            }
        }

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //当调整单明细为增加时，对应的库存数量是否小于库存数量，如果小于，则不允许反审核
            for (StockAdjustDetailDO detailDO : adjustDetailDOList) {
                if (detailDO.getAdjustMethod() == 1) {//增加
                    BigDecimal adjustQty = detailDO.getAdjustQty();
                    ErpMaterialStockDO erpMaterialStockDO = erpMaterialStockMapper.selectOne(detailDO.getMaterialId(), detailDO.getWarehouseOrgId());

                    if (erpMaterialStockDO.getStockQty() == null){
                        erpMaterialStockDO.setStockQty(BigDecimal.ZERO);
                    }

                    if (erpMaterialStockDO.getLockedQty() == null){
                        erpMaterialStockDO.setLockedQty(BigDecimal.ZERO);
                    }

                    BigDecimal adjustableQty = erpMaterialStockDO.getStockQty().subtract(erpMaterialStockDO.getLockedQty());
                    if (adjustQty != null && adjustableQty.compareTo(adjustQty) < 0) {
                        failItem.setCode(item.getStockAdjustCode());
                        failItem.setReason(ErrorCodeConstants.ADJUST_DETAIL_ADD_GT_STOCK.getMsg());
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Integer handleBusinessData(StockAdjustDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        Long id = item.getStockAdjustId();
        Integer dataStatus = request.getDataStatus();

        StockAdjustDO stockAdjustDO = stockAdjustMapper.selectById(id);

        StockAdjustDetailQueryReqVO stockAdjustDetailQueryReqVO = new StockAdjustDetailQueryReqVO();
        stockAdjustDetailQueryReqVO.setStockAdjustId(stockAdjustDO.getStockAdjustId());
        List<StockAdjustDetailDO> adjustDetailDOList = stockAdjustDetailMapper.selectList(stockAdjustDetailQueryReqVO);

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {

            //修改明细对应的库存
            for (StockAdjustDetailDO detailDO : adjustDetailDOList) {
                BigDecimal adjustQty = detailDO.getAdjustQty();
                if (detailDO.getAdjustMethod() != 1) {//减少
                    adjustQty = adjustQty.negate();
                }

                //更新库存数量
                erpMaterialStockService.updateMaterialStockQty(detailDO.getMaterialId(),
                        detailDO.getWarehouseOrgId(), adjustQty,
                        ErpStcokSFTEnum.ADJUST.getType(), id,
                        stockAdjustDO.getCreatedBy(), stockAdjustDO.getFormDt());
            }

            //对应盘点单的调整状态改为已调整
            if (stockAdjustDO.getBizType() == StockAdjustBizTypeEnum.INVENTORY_ADJUST.type) {
                ErpInventoryDO erpInventoryDO = new ErpInventoryDO();
                erpInventoryDO.setInventoryId(stockAdjustDO.getRelatedOrderId());
                erpInventoryDO.setAdjustStatus(InventoryAdjustStatusEnum.ADJUSTED.status);
                erpInventoryMapper.updateById(erpInventoryDO);

            }
        }

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //修改明细对应的库存
            for (StockAdjustDetailDO detailDO : adjustDetailDOList) {
                BigDecimal adjustQty = detailDO.getAdjustQty();
                if (detailDO.getAdjustMethod() == 1) {//减少
                    adjustQty = adjustQty.negate();
                }

                //更新库存数量
                erpMaterialStockService.updateMaterialStockQty(detailDO.getMaterialId(),
                        detailDO.getWarehouseOrgId(), adjustQty,
                        ErpStcokSFTEnum.ADJUST.getType(), id,
                        stockAdjustDO.getCreatedBy(), stockAdjustDO.getFormDt());

                //为盘点单库存调整
                if (stockAdjustDO.getBizType() == StockAdjustBizTypeEnum.INVENTORY_ADJUST.type) {
                    ErpInventoryDO erpInventoryDO = new ErpInventoryDO();
                    erpInventoryDO.setInventoryId(stockAdjustDO.getRelatedOrderId());
                    erpInventoryDO.setAdjustStatus(InventoryAdjustStatusEnum.NEED_ADJUST.status);
                    erpInventoryMapper.updateById(erpInventoryDO);

                }
            }
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        stockAdjustDO.setApprovedBy(loginUser.getFullUserName());
        stockAdjustDO.setApprovedDt(LocalDateTime.now());
        stockAdjustDO.setDataStatus(dataStatus);

        int count = stockAdjustMapper.updateById(stockAdjustDO);
        return count;
    }
}
