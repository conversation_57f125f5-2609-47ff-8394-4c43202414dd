package com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo;

import lombok.*;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 物料分析 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialAnalysisAditReqVO extends MaterialAnalysisBaseVO {


    private List<MaterialAnalysisDetailReqVO> itemList;

    @NotNull(message = "业务类型 不能为空")
    private Long bizType;


}
