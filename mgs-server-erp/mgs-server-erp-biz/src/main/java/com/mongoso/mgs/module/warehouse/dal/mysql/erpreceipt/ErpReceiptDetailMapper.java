package com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDetailDO;
import com.mongoso.mgs.module.warehouse.enums.MaterialCheckResultEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收货单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpReceiptDetailMapper extends BaseMapperX<ErpReceiptDetailDO> {

    default PageResult<ErpReceiptDetailDO> selectPage(ErpReceiptDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eqIfPresent(ErpReceiptDetailDO::getReceiptId, reqVO.getReceiptId())
                .likeIfPresent(ErpReceiptDetailDO::getReceiptCode, reqVO.getReceiptCode())
                .likeIfPresent(ErpReceiptDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpReceiptDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(ErpReceiptDetailDO::getIsMaterialFullInbounded, reqVO.getIsMaterialFullInbounded())
                .orderByDesc(ErpReceiptDetailDO::getCreatedDt));
    }

    default List<ErpReceiptDetailDO> selectList(ErpReceiptDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eqIfPresent(ErpReceiptDetailDO::getReceiptId, reqVO.getReceiptId())
                .likeIfPresent(ErpReceiptDetailDO::getReceiptCode, reqVO.getReceiptCode())
                .likeIfPresent(ErpReceiptDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpReceiptDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(ErpReceiptDetailDO::getIsMaterialFullInbounded, reqVO.getIsMaterialFullInbounded())
                .orderByDesc(ErpReceiptDetailDO::getCreatedDt));
    }

    default Long selectUnCheckCount(Long receiptId){
        return selectCount(LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eq(ErpReceiptDetailDO:: getReceiptId, receiptId)
                .eq(ErpReceiptDetailDO:: getCheckResult, 0)
        );
    }

    default Long selectUnFullInbounded(Long receiptId){
        return selectCount(LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eq(ErpReceiptDetailDO:: getReceiptId, receiptId)
                .eq(ErpReceiptDetailDO:: getIsMaterialFullInbounded, 0)
        );
    }

    default Long selectUnFullCheck(Long receiptId){
        return selectCount(LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eq(ErpReceiptDetailDO:: getReceiptId, receiptId)
                .eq(ErpReceiptDetailDO:: getIsMaterialFullCheck, 0)
        );
    }


    default BigDecimal selectInboundableQty(Long receiptDetailId) {
        ErpReceiptDetailDO detailDO =  selectOne(LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .select(ErpReceiptDetailDO :: getInboundableQty)
                .eq(ErpReceiptDetailDO::getReceiptDetailId, receiptDetailId));
        if(detailDO == null){
            return BigDecimal.ZERO;
        }
        return detailDO.getInboundableQty();
    }

    IPage<ErpReceiptDetailRespVO> queryReceiptDetailPage(Page<ErpReceiptDetailPageReqVO> page,
                                                         @Param("reqVO") ErpReceiptDetailPageReqVO reqVO);

    List<ErpReceiptDetailRespVO> queryReceiptDetailList(@Param("reqVO") ErpReceiptDetailQueryReqVO reqVO);


    default int deleteByReceiptId(Long receiptId){
        return delete(LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eq(ErpReceiptDetailDO:: getReceiptId, receiptId)
        );
    }

    List<DocumentRespBO> receiptQtyList(@Param("receiptId") Long receiptId);

    List<DocumentRespBO> inboundableQtyList(@Param("receiptId") Long receiptId);

    ProcessOutDemandDO selectReceiptQty(@Param("receiptId") Long receiptId);

    default Long selectUnQualifiedCount(Long receiptId){
        return selectCount(LambdaQueryWrapperX.<ErpReceiptDetailDO>lambdaQueryX()
                .eq(ErpReceiptDetailDO:: getReceiptId, receiptId)
                .ne(ErpReceiptDetailDO:: getCheckResult, MaterialCheckResultEnum.QUALIFIED.getType()));
    }

    List<ErpReceiptDetailDO> selectListBySourceOrderCode(@Param("sourceOrderCode") String sourceOrderCode);
}