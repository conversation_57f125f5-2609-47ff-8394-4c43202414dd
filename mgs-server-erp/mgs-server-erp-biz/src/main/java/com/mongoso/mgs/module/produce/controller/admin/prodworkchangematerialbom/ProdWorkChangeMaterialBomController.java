package com.mongoso.mgs.module.produce.controller.admin.prodworkchangematerialbom;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.prodworkchangematerialbom.vo.*;
import com.mongoso.mgs.module.produce.dal.db.prodworkchangematerialbom.ProdWorkChangeMaterialBomDO;
import com.mongoso.mgs.module.produce.service.prodworkchangematerialbom.ProdWorkChangeMaterialBomService;

/**
 * 生产工单变更单bom Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class ProdWorkChangeMaterialBomController {

    @Resource
    private ProdWorkChangeMaterialBomService prodWorkChangeMaterialBomService;

    @OperateLog("生产工单变更单bom添加或编辑")
    @PostMapping("/prodWorkChangeMaterialBomAdit")
    @PreAuthorize("@ss.hasPermission('prodWorkChangeMaterialBom:adit')")
    public ResultX<Long> prodWorkChangeMaterialBomAdit(@Valid @RequestBody ProdWorkChangeMaterialBomAditReqVO reqVO) {
        return success(reqVO.getMaterialBomId() == null
                            ? prodWorkChangeMaterialBomService.prodWorkChangeMaterialBomAdd(reqVO)
                            : prodWorkChangeMaterialBomService.prodWorkChangeMaterialBomEdit(reqVO));
    }

    @OperateLog("生产工单变更单bom删除")
    @PostMapping("/prodWorkChangeMaterialBomDelete")
    @PreAuthorize("@ss.hasPermission('prodWorkChangeMaterialBom:delete')")
    public ResultX<Boolean> prodWorkChangeMaterialBomDelete(@Valid @RequestBody ProdWorkChangeMaterialBomPrimaryReqVO reqVO) {
        prodWorkChangeMaterialBomService.prodWorkChangeMaterialBomDelete(reqVO.getMaterialBomId());
        return success(true);
    }

    @OperateLog("生产工单变更单bom详情")
    @PostMapping("/prodWorkChangeMaterialBomDetail")
    @PreAuthorize("@ss.hasPermission('prodWorkChangeMaterialBom:query')")
    public ResultX<ProdWorkChangeMaterialBomRespVO> prodWorkChangeMaterialBomDetail(@Valid @RequestBody ProdWorkChangeMaterialBomPrimaryReqVO reqVO) {
        return success(prodWorkChangeMaterialBomService.prodWorkChangeMaterialBomDetail(reqVO.getMaterialBomId()));
    }

    @OperateLog("生产工单变更单bom列表")
    @PostMapping("/prodWorkChangeMaterialBomList")
    @PreAuthorize("@ss.hasPermission('prodWorkChangeMaterialBom:query')")
    public ResultX<List<ProdWorkChangeMaterialBomRespVO>> prodWorkChangeMaterialBomList(@Valid @RequestBody ProdWorkChangeMaterialBomQueryReqVO reqVO) {
        return success(prodWorkChangeMaterialBomService.prodWorkChangeMaterialBomList(reqVO));
    }

    @OperateLog("生产工单变更单bom分页")
    @PostMapping("/prodWorkChangeMaterialBomPage")
    @PreAuthorize("@ss.hasPermission('prodWorkChangeMaterialBom:query')")
    public ResultX<PageResult<ProdWorkChangeMaterialBomRespVO>> prodWorkChangeMaterialBomPage(@Valid @RequestBody ProdWorkChangeMaterialBomPageReqVO reqVO) {
        return success(prodWorkChangeMaterialBomService.prodWorkChangeMaterialBomPage(reqVO));
    }

}
