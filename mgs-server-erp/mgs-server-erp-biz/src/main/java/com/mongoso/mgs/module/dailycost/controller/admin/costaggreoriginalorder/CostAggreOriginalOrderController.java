package com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.*;
import com.mongoso.mgs.module.dailycost.service.costaggreoriginalorder.CostAggreOriginalOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 成本归集原始数据单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostAggreOriginalOrderController {

    @Resource
    private CostAggreOriginalOrderService aggreOriginalOrderService;

    @OperateLog("成本归集原始数据单添加或编辑")
    @PostMapping("/aggreOriginalOrderAdit")
    @PreAuthorize("@ss.hasPermission('costAggreOrder:adit')")
    public ResultX<Long> costAggreOriginalOrderAdit(@Valid @RequestBody CostAggreOriginalOrderAditReqVO reqVO) {
        return success(reqVO.getAggreOriginalOrderId() == null
                            ? aggreOriginalOrderService.costAggreOrderAdd(reqVO)
                            : aggreOriginalOrderService.costAggreOrderEdit(reqVO));
    }

    @OperateLog("成本归集原始数据单无需归集/重置归集")
    @PostMapping("/aggreOriginalOrderAggreOperate")
    @PreAuthorize("@ss.hasPermission('costAggreOrder:adit')")
    public ResultX<ResultX<BatchResult>> aggreOriginalOrderAggreOperate(@Valid @RequestBody CostAggreOriginalOrderAditReqVO reqVO) {
        return success(aggreOriginalOrderService.aggreOriginalOrderAggreOperate(reqVO));
    }

    @OperateLog("成本归集原始数据单删除")
    @PostMapping("/aggreOriginalOrderDel")
    @PreAuthorize("@ss.hasPermission('costAggreOriginalOrder:delete')")
    public ResultX<Boolean> costAggreOrderDel(@Valid @RequestBody CostAggreOriginalOrderPrimaryReqVO reqVO) {
        aggreOriginalOrderService.costAggreOrderDel(reqVO.getAggreOriginalOrderId());
        return success(true);
    }

    @OperateLog("成本归集原始数据单详情")
    @PostMapping("/aggreOriginalOrderDetail")
    @PreAuthorize("@ss.hasPermission('costAggreOriginalOrder:query')")
    public ResultX<CostAggreOriginalOrderRespVO> costAggreOrderDetail(@Valid @RequestBody CostAggreOriginalOrderPrimaryReqVO reqVO) {
        return success(aggreOriginalOrderService.costAggreOrderDetail(reqVO.getAggreOriginalOrderId()));
    }

    @OperateLog("成本归集原始数据单列表")
    @PostMapping("/aggreOriginalOrderList")
    @PreAuthorize("@ss.hasPermission('costAggreOriginalOrder:query')")
    @DataPermission
    public ResultX<List<CostAggreOriginalOrderRespVO>> costAggreOrderList(@Valid @RequestBody CostAggreOriginalOrderQueryReqVO reqVO) {
        return success(aggreOriginalOrderService.costAggreOrderList(reqVO));
    }

    @OperateLog("成本归集原始数据单分页")
    @PostMapping("/aggreOriginalOrderPage")
    @PreAuthorize("@ss.hasPermission('costAggreOriginalOrder:query')")
    @DataPermission
    public ResultX<PageResult<CostAggreOriginalOrderRespVO>> costAggreOrderPage(@Valid @RequestBody CostAggreOriginalOrderPageReqVO reqVO) {
        return success(aggreOriginalOrderService.costAggreOrderPage(reqVO));
    }

    @OperateLog("查询归集单统计数据")
    @PostMapping("/getAggreOriginalStatData")
    @PreAuthorize("@ss.hasPermission('costAggreOriginalOrder:query')")
    public ResultX<CostAggreOriginalStatRespVO> getAggreOriginalStatData(@Valid @RequestBody CostAggreOriginalOrderQueryReqVO reqVO) {
        return success(aggreOriginalOrderService.getAggreOriginalStatData());
    }

}
