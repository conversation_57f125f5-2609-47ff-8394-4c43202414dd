package com.mongoso.mgs.module.sale.controller.admin.shipnoticedetail.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 销售发货通知明细单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShipNoticeDetailPageReqVO extends PageParam {

    /** 销售发货通知id */
    private Long shipNoticeId;

    /** 行号 */
    private Short rowNo;

    /** 物料id */
    private Long materialId;

    /** 基本单位 */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 通知数量 */
    private BigDecimal noticeQty;

    /** 是否出库完成 */
    private Short isMaterialFullOutbounded;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
