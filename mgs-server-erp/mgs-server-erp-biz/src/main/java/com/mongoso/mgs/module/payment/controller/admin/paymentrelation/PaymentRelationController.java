package com.mongoso.mgs.module.payment.controller.admin.paymentrelation;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.*;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import com.mongoso.mgs.module.payment.service.paymentrelation.PaymentRelationService;

/**
 * 收款关系 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment")
@Validated
public class PaymentRelationController {

    @Resource
    private PaymentRelationService relationService;

    @OperateLog("收款关系添加或编辑")
    @PostMapping("/paymentRelationAdit")
    @PreAuthorize("@ss.hasPermission('paymentRelation:adit')")
    public ResultX<Long> paymentRelationAdit(@Valid @RequestBody PaymentRelationAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? relationService.paymentRelationAdd(reqVO)
                            : relationService.paymentRelationEdit(reqVO));
    }

    @OperateLog("收款关系删除")
    @PostMapping("/paymentRelationDelete")
    @PreAuthorize("@ss.hasPermission('paymentRelation:delete')")
    public ResultX<Boolean> paymentRelationDelete(@Valid @RequestBody PaymentRelationPrimaryReqVO reqVO) {
        relationService.paymentRelationDelete(reqVO.getId());
        return success(true);
    }

    @OperateLog("收款关系详情")
    @PostMapping("/paymentRelationDetail")
    @PreAuthorize("@ss.hasPermission('paymentRelation:query')")
    public ResultX<PaymentRelationRespVO> paymentRelationDetail(@Valid @RequestBody PaymentRelationPrimaryReqVO reqVO) {
        return success(relationService.paymentRelationDetail(reqVO.getId()));
    }

    @OperateLog("收款关系列表")
    @PostMapping("/paymentRelationList")
    @PreAuthorize("@ss.hasPermission('paymentRelation:query')")
    public ResultX<List<PaymentRelationRespVO>> paymentRelationList(@Valid @RequestBody PaymentRelationQueryReqVO reqVO) {
        return success(relationService.paymentRelationList(reqVO));
    }

    @OperateLog("收款关系分页")
    @PostMapping("/paymentRelationPage")
    @PreAuthorize("@ss.hasPermission('paymentRelation:query')")
    public ResultX<PageResult<PaymentRelationRespVO>> paymentRelationPage(@Valid @RequestBody PaymentRelationPageReqVO reqVO) {
        return success(relationService.paymentRelationPage(reqVO));
    }

}
