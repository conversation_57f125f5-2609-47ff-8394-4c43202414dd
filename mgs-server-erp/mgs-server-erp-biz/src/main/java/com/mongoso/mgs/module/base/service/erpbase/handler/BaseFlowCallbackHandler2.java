package com.mongoso.mgs.module.base.service.erpbase.handler;

import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.component.flow.service.handler.request.BaseFlowCallbackRequest;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import org.springframework.transaction.annotation.Transactional;
import static com.mongoso.mgs.component.flow.enums.ApproveResultEnum.PASS;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:24
 * @description: 流程回调基类
 */
public abstract class BaseFlowCallbackHandler2<T> {

    protected final BaseApproveHandler2<T> approveHandler;

    protected BaseFlowCallbackHandler2(BaseApproveHandler2<T> approveHandler) {
        this.approveHandler = approveHandler;
    }

    public final FailItem handleFlowCallback(T item, FlowCallbackBO flowCallbackBO) {
        FailItem failItem = new FailItem();

        if (item == null){
            failItem.setReason("当前单据不存在!");
            return failItem;
        }

        //回调对象
        FlowCallback flowCallback = flowCallbackBO.getFlowCallback();

        Integer buttonType = ApproveUtilX.getButtonType(flowCallback.getFlowFunctionCode());
        Integer dataStatus = getdataStatus(flowCallback);
        Integer approveResult = flowCallback.getApproveResult();
        ApproveResultEnum resultEnum = fromKey(approveResult);

        BaseFlowCallbackRequest callBackRequest = new BaseFlowCallbackRequest();
        callBackRequest.setFlowCallback(flowCallback);
        callBackRequest.setButtonType(buttonType);
        callBackRequest.setDataStatus(dataStatus);
        callBackRequest.setFailItem(failItem);

        switch (resultEnum) {
            case PASS: // 通过
                passHandle(item,callBackRequest);
                break;
            case UN_PASS: // 不通过
                unPassHandle(item,callBackRequest);
                break;
            default: // 未知
                break;
        }

        return failItem;
    }

    protected Integer passHandle(T item, BaseFlowCallbackRequest callBackRequest) {
        Integer buttonType = callBackRequest.getButtonType();
        FailItem failItem = callBackRequest.getFailItem();
        Integer dataStatus = callBackRequest.getDataStatus();


        BaseApproveRequest approveRequest = new BaseApproveRequest();
        approveRequest.setButtonType(buttonType);
        approveRequest.setFailItem(callBackRequest.getFailItem());
        approveRequest.setDataStatus(callBackRequest.getDataStatus());

        //进行业务验证
        Boolean isValid = approveHandler.businessVerify(buttonType, item, failItem);

        //回调处理
        if (isValid) {
            ApproveCommonAttrs approveCommonAttrs = approveHandler.approvalAttributes(item);
            approveHandler.handleBusinessData(buttonType, approveCommonAttrs.getObjId(), dataStatus, failItem);
        }

        return isValid ? 1 : 0;
    }

    /**
     * 审批不通过-业务处理
     *
     * @param callBackRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public abstract Integer unPassHandle(T item,BaseFlowCallbackRequest callBackRequest);

    /**
     * 获取单据状态
     *
     * @param flowCallback
     * @return
     */
    private Integer getdataStatus(FlowCallback flowCallback) {
        Integer dataStatus = null;
        if (flowCallback.getApproveResult() == PASS.getKey()) {
            // 通过
            dataStatus = ApproveUtilX.getDataStatusByApproveSuccess(flowCallback.getFlowFunctionCode());
        } else if (flowCallback.getApproveResult() == ApproveResultEnum.UN_PASS.getKey()) {
            // 不通过
            dataStatus = ApproveUtilX.getDataStatusByApproveFail(flowCallback.getFlowFunctionCode());
        } else {
            // 撤销
            dataStatus = ApproveUtilX.getDataStatusByApproveFail(flowCallback.getFlowFunctionCode());
        }
        return dataStatus;
    }

    /**
     * 获取审批结果枚举
     *
     * @param key
     * @return
     */
    private static ApproveResultEnum fromKey(int key) {
        for (ApproveResultEnum resultEnum : ApproveResultEnum.values()) {
            if (resultEnum.getKey() == key) {
                return resultEnum;
            }
        }
        throw new IllegalArgumentException("No matching dataButtonEnum for key: " + key);
    }
}
