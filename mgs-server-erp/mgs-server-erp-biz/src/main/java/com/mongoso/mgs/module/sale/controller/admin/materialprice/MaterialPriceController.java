package com.mongoso.mgs.module.sale.controller.admin.materialprice;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.sale.controller.admin.materialprice.vo.*;
import com.mongoso.mgs.module.sale.dal.db.materialprice.MaterialPriceDO;
import com.mongoso.mgs.module.sale.service.materialprice.MaterialPriceService;

/**
 * 商品价格 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sale1")
@Validated
public class MaterialPriceController {

    @Resource
    private MaterialPriceService materialPriceService;

    @OperateLog("商品价格添加或编辑")
    @PostMapping("/materialPriceAdit")
    @PreAuthorize("@ss.hasPermission('materialPrice:adit')")
    public ResultX<Long> materialPriceAdit(@Valid @RequestBody MaterialPriceAditReqVO reqVO) {
        return success(reqVO.getMaterialPriceId() == null
                            ? materialPriceService.materialPriceAdd(reqVO)
                            : materialPriceService.materialPriceEdit(reqVO));
    }

    @OperateLog("商品价格删除")
    @PostMapping("/materialPriceDelete")
    @PreAuthorize("@ss.hasPermission('materialPrice:delete')")
    public ResultX<Boolean> materialPriceDelete(@Valid @RequestBody MaterialPricePrimaryReqVO reqVO) {
        materialPriceService.materialPriceDelete(reqVO.getMaterialPriceId());
        return success(true);
    }

    @OperateLog("商品价格详情")
    @PostMapping("/materialPriceDetail")
    @PreAuthorize("@ss.hasPermission('materialPrice:query')")
    public ResultX<MaterialPriceRespVO> materialPriceDetail(@Valid @RequestBody MaterialPricePrimaryReqVO reqVO) {
        return success(materialPriceService.materialPriceDetail(reqVO.getMaterialPriceId()));
    }

    @OperateLog("商品价格列表")
    @PostMapping("/materialPriceList")
    @PreAuthorize("@ss.hasPermission('materialPrice:query')")
    public ResultX<List<MaterialPriceRespVO>> materialPriceList(@Valid @RequestBody MaterialPriceQueryReqVO reqVO) {
        return success(materialPriceService.materialPriceList(reqVO));
    }

    @OperateLog("商品价格分页")
    @PostMapping("/materialPricePage")
    @PreAuthorize("@ss.hasPermission('materialPrice:query')")
    public ResultX<PageResult<MaterialPriceRespVO>> materialPricePage(@Valid @RequestBody MaterialPricePageReqVO reqVO) {
        return success(materialPriceService.materialPricePage(reqVO));
    }

}
