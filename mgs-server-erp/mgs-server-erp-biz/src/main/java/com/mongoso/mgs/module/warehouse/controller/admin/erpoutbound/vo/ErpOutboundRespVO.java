package com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo;

import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailRespVO;
import lombok.*;

  
 import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.*;


/**
 * 出库单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpOutboundRespVO extends ErpOutboundBaseVO {

    /** 收货对象 */
    private String receiptObjName;

    /** 发货方式 */
    private String deliveryMethodDictName;

    /** 入库单类型名称 */
    private String outboundTypeDictName;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 责任人 */
    private String directorName;

    /** 责任部门(组织名称) */
    private String directorOrgName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 明细列表 */
    List<ErpOutboundDetailRespVO> detailList;

    /** 清单核对明细列表 **/
    List<ErpOutboundDetailRespVO> orderCheckDetailList;

    /** 审批任务id */
    private Long approveTaskId;

}
