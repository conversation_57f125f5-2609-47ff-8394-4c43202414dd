package com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 物料库存报表分页查询请求VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpMaterialStockReportPageReqVO extends PageParam {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料类别字典ID
     */
    private String materialCategoryDictId;

    /**
     * 规格型号
     */
    private String specModel;

}
