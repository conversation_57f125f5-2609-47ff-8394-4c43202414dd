package com.mongoso.mgs.module.finance.dal.db.accountorderdetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 对账单明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_account_order_detail", autoResultMap = true)
//@KeySequence("u_account_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountOrderDetailDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long accountDetailId;

    /** 行号 */
    private Integer rowNo;

    /** 单据类型 1：销售，2：采购 */
    private Short formType;

    /** 对账单单id */
    private Long accountId;

    /** 结算单id */
    private Long settleId;

    /** 结算事务号 */
    private String settleTransactionCode;

    /** 对账单号 */
    private String reconciliationNumber;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 来源单id */
    private Long sourceOrderId;

    /** 物料id */
    private Long materialId;
    /** 物料编码 */
    private String materialCode;

    /** 主单位ID */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 来源单据数量 */
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    private Long invoiceTypeId;
    private String invoiceTypeName;
    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 可对账数量 */
    private BigDecimal reconcilableQty;

    /** 已对账数量 */
    private BigDecimal reconciledQty;

    /** 本次对账数量 */
    private BigDecimal currentReconciliationQty;

    /** 本次对账金额(不含税) */
    private BigDecimal exclCurrentReconciliationAmt;

    /** 本次对账金额(含税) */
    private BigDecimal inclCurrentReconciliationAmt;

    /** 已计划金额 */
    private BigDecimal planAmt;

    /** 可计划金额 */
    private BigDecimal plannableAmt;

    /** 可计划数量 */
    private BigDecimal plannableQty;

    /** 已计划数量 */
    private BigDecimal planQty;

    /** 已开票数量 */
    private BigDecimal invoicedQty;

    /** 可开票数量 */
    private BigDecimal invoiceableQty;

    /** 已开票金额 */
    private BigDecimal invoicedAmt;

    /** 可开票金额 */
    private BigDecimal invoiceableAmt;


    /** 收款计划策略 */
    private String collectionPlanStrategy;

    /** 开票计划策略 */
    private String invoicingPlanStrategy;

    /** 销售退款计划策略 */
    private String saleRefundPlanStrategy;

    /** 开票结算池策略 */
    private String invoicingSettlementStrategy;

    /** 收款计划策略--取父集 */
    private String relatedCollectionPlanStrategy;

    /** 本币行金额（不含税） */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 本币行金额（含税） */
    private BigDecimal inclTaxLocalCurrencyAmt;
}
