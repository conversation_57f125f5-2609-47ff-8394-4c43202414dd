package com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetaillist;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 实开发票明细集合 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_invoice_detail_list", autoResultMap = true)
//@KeySequence("u_invoice_detail_list_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDetailListDO extends OperateDO {

    /** 实开发票明细集合主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long invoiceDetailListId;

    /** 实开发票明细ID */
    private Long invoiceDetailId;

    /** 实开发票id */
    private Long invoiceId;

    /** 开票申请明细id */
    private Long invoiceApplyDetailId;

    /** 开票计划明细ID */
    private Long invoicePlanDetailId;

    /** 开票方向 */
    private Short billingDirection;

    /** 行号 */
    private Long rowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 数量 */
    private BigDecimal qty;

    /** 票据类型ID */
    private Long invoiceTypeDictId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 本次开票金额（不含税） */
    private BigDecimal invoiceAmtExclTax;

    /** 税额 */
    private BigDecimal taxAmt;

    /** 本次开票金额（含税） */
    private BigDecimal invoiceAmtInclTax;

    /** 创建人ID */
    private Long createdId;




}
