package com.mongoso.mgs.module.purchase.controller.admin.report.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 库存变动统计 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class MaterialPurchaseReportRespVO implements Serializable {

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料编码
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 物料类别字典名称
     */
    private String materialCategoryDictName;

    /**
     * 规格属性
     */
    private String specAttributeStr;

    /**
     * 项目列表
     */
    List<MaterialPurchaseReportItemRespVO> itemList = new ArrayList<>();
}

