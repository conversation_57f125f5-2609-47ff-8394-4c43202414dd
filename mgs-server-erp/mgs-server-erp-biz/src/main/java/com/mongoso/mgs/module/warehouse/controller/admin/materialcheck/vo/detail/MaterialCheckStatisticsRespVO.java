package com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 物料检验统计响应VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class MaterialCheckStatisticsRespVO implements Serializable {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料类别
     */
    private String materialCategoryDictName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 规格属性
     */
    private String specAttributeStr;

    /**
     * 检验总数
     */
    private BigDecimal totalCheckQty;

    /**
     * 良品总数
     */
    private BigDecimal totalOkQty;

    /**
     * 不良品总数
     */
    private BigDecimal totalNgQty;

    /**
     * 良品率（良品总数/检验总数）
     */
    private BigDecimal okRate;


    /**
     * 不良品原因列表
     */
    private List<NgReasonStatistics> ngReasonList;

}
