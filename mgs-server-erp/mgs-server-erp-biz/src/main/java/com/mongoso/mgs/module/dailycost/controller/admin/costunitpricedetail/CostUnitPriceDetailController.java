package com.mongoso.mgs.module.dailycost.controller.admin.costunitpricedetail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.costunitpricedetail.vo.*;
import com.mongoso.mgs.module.dailycost.service.costunitpricedetail.CostUnitPriceDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 单价明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostUnitPriceDetailController {

    @Resource
    private CostUnitPriceDetailService costUnitPriceDetailService;


    @OperateLog("单价明细列表")
    @PostMapping("/costUnitPriceDetailList")
    @PreAuthorize("@ss.hasPermission('costUnitPriceDetail:query')")
    @DataPermission
    public ResultX<List<CostUnitPriceDetailRespVO>> costUnitPriceDetailList(@Valid @RequestBody CostUnitPriceDetailQueryReqVO reqVO) {
        return success(costUnitPriceDetailService.costUnitPriceDetailList(reqVO));
    }

    @OperateLog("单价明细分页")
    @PostMapping("/costUnitPriceDetailPage")
    @PreAuthorize("@ss.hasPermission('costUnitPriceDetail:query')")
    @DataPermission
    public ResultX<PageResult<CostUnitPriceDetailRespVO>> costUnitPriceDetailPage(@Valid @RequestBody CostUnitPriceDetailPageReqVO reqVO) {
        return success(costUnitPriceDetailService.costUnitPriceDetailPage(reqVO));
    }

}
