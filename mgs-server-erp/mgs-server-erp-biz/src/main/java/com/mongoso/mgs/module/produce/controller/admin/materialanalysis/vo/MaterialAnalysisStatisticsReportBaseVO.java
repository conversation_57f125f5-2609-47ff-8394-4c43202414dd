package com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo;

import lombok.*;

import java.io.Serializable;

  
import java.math.BigDecimal;
 
/**
 * 物料分析下发统计 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MaterialAnalysisStatisticsReportBaseVO implements Serializable {

    /** 主键 */
    private Long id;

    /** 创建人ID */
    private Long createdId;

    /** 来源单id */
    private Long sourceOrderId;

    /** 业务类型 0物料分析 1销售订单物料分析 2生产订单物料分析 */
    private Integer bizType;

    /** 单据类型  0生产订单 1生产工单 2委外采购订单 3采购需求单*/
    private Short formType;

    /** 单据编码*/
    private String formCode;

    /** 下发数量 */
    private BigDecimal issuedQty;

    /** 来源单号 */
    private String sourceOrderCode;

}
