package com.mongoso.mgs.module.finance.dal.mysql.payment;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.payment.vo.PaymentPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.payment.vo.PaymentQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.payment.PaymentOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收款单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PaymentOrderMapper extends BaseMapperX<PaymentOrderDO> {

    default PageResult<PaymentOrderDO> selectPageOld(PaymentPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<PaymentOrderDO>lambdaQueryX()
                .eqIfPresent(PaymentOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(PaymentOrderDO::getPayCode, reqVO.getPayCode())
                .likeIfPresent(PaymentOrderDO::getApplyCode, reqVO.getApplyCode())
                .eqIfPresent(PaymentOrderDO::getReciveApplyId, reqVO.getReciveApplyId())
                .eqIfPresent(PaymentOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(PaymentOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(PaymentOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(PaymentOrderDO::getPayTotalAmt, reqVO.getPayTotalAmt())
                .eqIfPresent(PaymentOrderDO::getRemainingAmt, reqVO.getRemainingAmt())
                .betweenIfPresent(PaymentOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(PaymentOrderDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(PaymentOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(PaymentOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(PaymentOrderDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(PaymentOrderDO::getInBillAmt, reqVO.getInBillAmt())
                .likeIfPresent(PaymentOrderDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(PaymentOrderDO::getDiscountAmt, reqVO.getDiscountAmt())
                .eqIfPresent(PaymentOrderDO::getPayWriteAmt, reqVO.getPayWriteAmt())
                .eqIfPresent(PaymentOrderDO::getBillWriteAmt, reqVO.getBillWriteAmt())
                .eqIfPresent(PaymentOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PaymentOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PaymentOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(PaymentOrderDO::getCreatedDt));
    }



    default PageResult<PaymentOrderDO> selectPage(PaymentPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<PaymentOrderDO>lambdaQueryX()
                .eqIfPresent(PaymentOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(PaymentOrderDO::getPayCode, reqVO.getPayCode())
                .likeIfPresent(PaymentOrderDO::getApplyCode, reqVO.getApplyCode())
                .eqIfPresent(PaymentOrderDO::getReciveApplyId, reqVO.getReciveApplyId())
                .eqIfPresent(PaymentOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(PaymentOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(PaymentOrderDO::getCustomerId, reqVO.getCustomerId())
                .inIfPresent(PaymentOrderDO::getCustomerId, reqVO.getCustomerIdList())
                .eqIfPresent(PaymentOrderDO::getPayTotalAmt, reqVO.getPayTotalAmt())
                .eqIfPresent(PaymentOrderDO::getRemainingAmt, reqVO.getRemainingAmt())
                .betweenIfPresent(PaymentOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(PaymentOrderDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(PaymentOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(PaymentOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(PaymentOrderDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(PaymentOrderDO::getInBillAmt, reqVO.getInBillAmt())
                .likeIfPresent(PaymentOrderDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(PaymentOrderDO::getDiscountAmt, reqVO.getDiscountAmt())
                .eqIfPresent(PaymentOrderDO::getPayWriteAmt, reqVO.getPayWriteAmt())
                .eqIfPresent(PaymentOrderDO::getBillWriteAmt, reqVO.getBillWriteAmt())
                .eqIfPresent(PaymentOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PaymentOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PaymentOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(PaymentOrderDO::getCreatedDt));
    }

    default List<PaymentOrderDO> selectListOld(PaymentQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<PaymentOrderDO>lambdaQueryX()
                .eqIfPresent(PaymentOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(PaymentOrderDO::getPayCode, reqVO.getPayCode())
                .likeIfPresent(PaymentOrderDO::getApplyCode, reqVO.getApplyCode())
                .eqIfPresent(PaymentOrderDO::getReciveApplyId, reqVO.getReciveApplyId())
                .eqIfPresent(PaymentOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(PaymentOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(PaymentOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(PaymentOrderDO::getPayTotalAmt, reqVO.getPayTotalAmt())
                .eqIfPresent(PaymentOrderDO::getRemainingAmt, reqVO.getRemainingAmt())
                .betweenIfPresent(PaymentOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(PaymentOrderDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(PaymentOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(PaymentOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(PaymentOrderDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(PaymentOrderDO::getInBillAmt, reqVO.getInBillAmt())
                .likeIfPresent(PaymentOrderDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(PaymentOrderDO::getDiscountAmt, reqVO.getDiscountAmt())
                .eqIfPresent(PaymentOrderDO::getPayWriteAmt, reqVO.getPayWriteAmt())
                .eqIfPresent(PaymentOrderDO::getBillWriteAmt, reqVO.getBillWriteAmt())
                .eqIfPresent(PaymentOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PaymentOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PaymentOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(PaymentOrderDO::getCreatedDt));
    }

    default List<PaymentOrderDO> selectList(PaymentQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<PaymentOrderDO>lambdaQueryX()
                .eqIfPresent(PaymentOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(PaymentOrderDO::getPayCode, reqVO.getPayCode())
                .likeIfPresent(PaymentOrderDO::getApplyCode, reqVO.getApplyCode())
                .eqIfPresent(PaymentOrderDO::getReciveApplyId, reqVO.getReciveApplyId())
                .eqIfPresent(PaymentOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(PaymentOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(PaymentOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(PaymentOrderDO::getPayTotalAmt, reqVO.getPayTotalAmt())
                .eqIfPresent(PaymentOrderDO::getRemainingAmt, reqVO.getRemainingAmt())
                .betweenIfPresent(PaymentOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(PaymentOrderDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(PaymentOrderDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(PaymentOrderDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(PaymentOrderDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(PaymentOrderDO::getInBillAmt, reqVO.getInBillAmt())
                .likeIfPresent(PaymentOrderDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(PaymentOrderDO::getDiscountAmt, reqVO.getDiscountAmt())
                .eqIfPresent(PaymentOrderDO::getPayWriteAmt, reqVO.getPayWriteAmt())
                .eqIfPresent(PaymentOrderDO::getBillWriteAmt, reqVO.getBillWriteAmt())
                .eqIfPresent(PaymentOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PaymentOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PaymentOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(PaymentOrderDO::getCreatedDt));
    }

    void deleteByPaymentId(@Param("paymentId") Long paymentId);
}