package com.mongoso.mgs.module.finance.service.feemanage.feeloanpayment;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeloanpayment.vo.FeeLoanPaymentAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeloanpayment.vo.FeeLoanPaymentPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeloanpayment.vo.FeeLoanPaymentQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeloanpayment.vo.FeeLoanPaymentRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 费用借款付款 Service 接口
 *
 * <AUTHOR>
 */
public interface FeeLoanPaymentService {

    /**
     * 创建费用借款付款
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long feeLoanPaymentAdd(@Valid FeeLoanPaymentAditReqVO reqVO);

    /**
     * 更新费用借款付款
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long feeLoanPaymentEdit(@Valid FeeLoanPaymentAditReqVO reqVO);

    /**
     * 删除费用借款付款
     *
     * @param feeLoanPaymentId 编号
     */
    void feeLoanPaymentDel(Long feeLoanPaymentId);

    /**
     * 获得费用借款付款信息
     *
     * @param feeLoanPaymentId 编号
     * @return 费用借款付款信息
     */
    FeeLoanPaymentRespVO feeLoanPaymentDetail(Long feeLoanPaymentId);

    /**
     * 获得费用借款付款列表
     *
     * @param reqVO 查询条件
     * @return 费用借款付款列表
     */
    List<FeeLoanPaymentRespVO> feeLoanPaymentList(@Valid FeeLoanPaymentQueryReqVO reqVO);

    /**
     * 获得费用借款付款分页
     *
     * @param reqVO 查询条件
     * @return 费用借款付款分页
     */
    PageResult<FeeLoanPaymentRespVO> feeLoanPaymentPage(@Valid FeeLoanPaymentPageReqVO reqVO);

    /**
     * 批量删除费用借款
     *
     * @param id 编号
     */
    ResultX<BatchResult> feeLoanPaymentDelBatch(IdReq reqVO);

    /**
     * 状态变更
     *
     * @param reqVO 编号
     */
    BatchResult feeLoanPaymentApprove(FlowApprove reqVO);

    Object feeLoanPaymentFlowCallback(FlowCallback reqVO);

}
