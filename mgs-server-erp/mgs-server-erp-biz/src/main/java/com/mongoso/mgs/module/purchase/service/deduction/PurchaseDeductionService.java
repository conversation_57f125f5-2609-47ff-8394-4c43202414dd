package com.mongoso.mgs.module.purchase.service.deduction;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购扣费单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseDeductionService {

    /**
     * 创建采购扣费单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseDeductionAdd(@Valid PurchaseDeductionAditReqVO reqVO);

    /**
     * 更新采购扣费单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseDeductionEdit(@Valid PurchaseDeductionAditReqVO reqVO);

    /**
     * 删除采购扣费单
     *
     * @param purchaseDeductionId 编号
     */
    void purchaseDeductionDel(Long purchaseDeductionId);

    /**
     * 获得采购扣费单信息
     *
     * @param purchaseDeductionId 编号
     * @return 采购扣费单信息
     */
    PurchaseDeductionRespVO purchaseDeductionDetail(Long purchaseDeductionId);

    /**
     * 获得采购扣费单列表
     *
     * @param reqVO 查询条件
     * @return 采购扣费单列表
     */
    List<PurchaseDeductionRespVO> purchaseDeductionList(@Valid PurchaseDeductionQueryReqVO reqVO);

    /**
     * 获得采购扣费单分页
     *
     * @param reqVO 查询条件
     * @return 采购扣费单分页
     */
    PageResult<PurchaseDeductionRespVO> purchaseDeductionPage(@Valid PurchaseDeductionPageReqVO reqVO);

    ResultX<BatchResult> purchaseDeductionDelBatch(IdReq reqVO);

    BatchResult purchaseDeductionApprove(FlowApprove reqVO);

    Object purchaseDeductionCallback(FlowCallback reqVO);
}
