package com.mongoso.mgs.module.base.dal.mysql.erpcustomer;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.controller.admin.customer.vo.CustomerQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerPageReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerPrimaryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpcustomer.vo.ERPCustomerRespVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialPageReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.dal.db.erpcustomer.ERPCustomerDO;
import com.mongoso.mgs.module.base.dal.db.spu.SpuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ERPCustomerMapper extends BaseMapperX<ERPCustomerDO> {

    default PageResult<ERPCustomerDO> selectPageOld(ERPCustomerPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ERPCustomerDO>lambdaQueryX()
//                .eqIfPresent(ERPCustomerDO::getBaseInfo, reqVO.getBaseInfo())
//                .eqIfPresent(ERPCustomerDO::getFinanceInfo, reqVO.getFinanceInfo())
//                .eqIfPresent(ERPCustomerDO::getContactList, reqVO.getContactList())
                .eqIfPresent(ERPCustomerDO::getCustomerId, reqVO.getCustomerId())
                .notInIfPresent(ERPCustomerDO::getCustomerId, reqVO.getExclCustomerIdList())
                .likeIfPresent(ERPCustomerDO::getCustomerCode, reqVO.getCustomerCode())
                .likeIfPresent(ERPCustomerDO::getCustomerName, reqVO.getCustomerName())
                .likeIfPresent(ERPCustomerDO::getCustomerShortName, reqVO.getCustomerShortName())
                .eqIfPresent(ERPCustomerDO::getCustomerTypeDictId, reqVO.getCustomerTypeDictId())
                .eqIfPresent(ERPCustomerDO::getCountryName, reqVO.getCountryName())
                .eqIfPresent(ERPCustomerDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
                .eqIfPresent(ERPCustomerDO::getDataStatus,reqVO.getDataStatus())
                .betweenIfPresent(ERPCustomerDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ERPCustomerDO::getCustomerId));
    }


    default PageResult<ERPCustomerDO> selectPage(ERPCustomerPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ERPCustomerDO>lambdaQueryX()
//                .eqIfPresent(ERPCustomerDO::getBaseInfo, reqVO.getBaseInfo())
//                .eqIfPresent(ERPCustomerDO::getFinanceInfo, reqVO.getFinanceInfo())
//                .eqIfPresent(ERPCustomerDO::getContactList, reqVO.getContactList())
                .eqIfPresent(ERPCustomerDO::getCustomerId, reqVO.getCustomerId())
                .notInIfPresent(ERPCustomerDO::getCustomerId, reqVO.getExclCustomerIdList())
                .likeIfPresent(ERPCustomerDO::getCustomerCode, reqVO.getCustomerCode())
                .likeIfPresent(ERPCustomerDO::getCustomerName, reqVO.getCustomerName())
                .likeIfPresent(ERPCustomerDO::getCustomerShortName, reqVO.getCustomerShortName())
                .eqIfPresent(ERPCustomerDO::getCustomerTypeDictId, reqVO.getCustomerTypeDictId())
                .eqIfPresent(ERPCustomerDO::getCountryName, reqVO.getCountryName())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
                .eqIfPresent(ERPCustomerDO::getDataStatus,reqVO.getDataStatus())
                .eqIfPresent(ERPCustomerDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ERPCustomerDO::getCustomerId));
    }

//    IPage<ERPCustomerRespVO> selectPage2(Page<ERPCustomerRespVO> page, @Param("reqVO") ERPCustomerPageReqVO reqVO);

    default List<ERPCustomerDO> selectListOld(ERPCustomerQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ERPCustomerDO>lambdaQueryX()
//                .eqIfPresent(ERPCustomerDO::getBaseInfo, reqVO.getBaseInfo())
                .eqIfPresent(ERPCustomerDO::getCustomerId, reqVO.getCustomerId())
                .inIfPresent(ERPCustomerDO::getCustomerId, reqVO.getCustomerIdList())
//                .eqIfPresent(ERPCustomerDO::getBaseInfo, reqVO.getBaseInfo())
//                .eqIfPresent(ERPCustomerDO::getFinanceInfo, reqVO.getFinanceInfo())
//                .eqIfPresent(ERPCustomerDO::getContactList, reqVO.getContactList())
                .eqIfPresent(ERPCustomerDO::getCustomerId, reqVO.getCustomerId())
                .notInIfPresent(ERPCustomerDO::getCustomerId, reqVO.getExclCustomerIdList())
                .likeIfPresent(ERPCustomerDO::getCustomerCode, reqVO.getCustomerCode())
                .likeIfPresent(ERPCustomerDO::getCustomerName, reqVO.getCustomerName())
                .likeIfPresent(ERPCustomerDO::getCustomerShortName, reqVO.getCustomerShortName())
                .eqIfPresent(ERPCustomerDO::getCustomerTypeDictId, reqVO.getCustomerTypeDictId())
                .eqIfPresent(ERPCustomerDO::getCountryName, reqVO.getCountryName())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
                .eqIfPresent(ERPCustomerDO::getDataStatus,reqVO.getDataStatus())
                .betweenIfPresent(ERPCustomerDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ERPCustomerDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ERPCustomerDO::getCustomerId));
    }

    default List<ERPCustomerDO> selectList(ERPCustomerQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ERPCustomerDO>lambdaQueryX()
                .eqIfPresent(ERPCustomerDO::getCustomerId, reqVO.getCustomerId())
                .notInIfPresent(ERPCustomerDO::getCustomerId, reqVO.getExclCustomerIdList())
                .likeIfPresent(ERPCustomerDO::getCustomerCode, reqVO.getCustomerCode())
                .likeIfPresent(ERPCustomerDO::getCustomerName, reqVO.getCustomerName())
                .likeIfPresent(ERPCustomerDO::getCustomerShortName, reqVO.getCustomerShortName())
                .eqIfPresent(ERPCustomerDO::getCustomerTypeDictId, reqVO.getCustomerTypeDictId())
                .eqIfPresent(ERPCustomerDO::getCountryName, reqVO.getCountryName())
                .eqIfPresent(ERPCustomerDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
                .eqIfPresent(ERPCustomerDO::getDataStatus,reqVO.getDataStatus())
                .betweenIfPresent(ERPCustomerDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ERPCustomerDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(ERPCustomerDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ERPCustomerDO::getCustomerId));
    }

//    List<ERPCustomerRespVO> selectList2(@Param("reqVO") ERPCustomerQueryReqVO reqVO);

    List<ERPCustomerRespVO> selectUnableDelete(@Param("list") List<Long> customerIdList);

}