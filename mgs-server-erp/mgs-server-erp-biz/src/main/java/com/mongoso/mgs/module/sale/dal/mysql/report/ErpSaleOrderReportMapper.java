package com.mongoso.mgs.module.sale.dal.mysql.report;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkRespVO;
import com.mongoso.mgs.module.sale.controller.admin.report.vo.ErpSaleOrderReportPageReqVO;
import com.mongoso.mgs.module.sale.controller.admin.report.vo.ErpSaleOrderReportRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: zhiling
 * @date: 2025/2/24 16:49
 * @description:
 */

@Mapper
public interface ErpSaleOrderReportMapper {


    IPage<ErpSaleOrderReportRespVO> findErpSaleOrderDetail(Page<Object> page,@Param("reqVO") ErpSaleOrderReportPageReqVO reqVO);

    List<String> queryPurchaseOrderIdList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryInboundIdByProdOutPurchaseList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryInboundIdByDemandList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryInboundIdByPurchaseList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryInboundIdByProdList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryAllInboundIdByProdList(@Param("saleOrderId")Long saleOrderId);

    List<ProdWorkRespVO > queryProdWorkIdList(@Param("saleOrderId")Long saleOrderId);

    List<ProdWorkRespVO > queryAllProdWorkIdList(@Param("saleOrderId")Long saleOrderId);

    List<String > queryOutboundIdList(@Param("prodWorkIdList")List<Long> prodWorkIdList);

    List<String > queryInboundIdList(@Param("prodWorkIdList")List<Long> prodWorkIdList);

    List<String> queryPurchaseIdByOutsourceList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryAllPurchaseIdByOutsourceList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryInboundIdByOutsourceList(@Param("saleOrderId")Long saleOrderId);

    List<String> queryAllInboundIdByOutsourceList(@Param("saleOrderId")Long saleOrderId);
}
