package com.mongoso.mgs.module.purchase.handler.approve;

import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.enums.SpuConfigSourceOrderEnum;
import com.mongoso.mgs.module.dailycost.service.costprodpurchase.CostProdPurchaseService;
import com.mongoso.mgs.module.dailycost.service.spuconfig.CostSpuConfigService;
import com.mongoso.mgs.module.finance.service.common.FinanceConnectService;
import com.mongoso.mgs.module.produce.controller.admin.processoutdemand.vo.ProcessOutDemandQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.PurchaseProcessOutRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDetailDO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutMapper;
import com.mongoso.mgs.module.purchase.service.processout.PurchaseProcessOutService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_NOT_OPERATION_NOT_APPROVED;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.PURCHASE_DETAIL_NUM_GREATER_DEMAND;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： ZhouYangqing
 * @date： 2024/12/31
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class purchaseProcessOutApproveHandler extends FlowApproveHandler<PurchaseProcessOutDO> {

    @Resource
    private PurchaseProcessOutMapper processOutMapper;

    @Resource
    private PurchaseProcessOutDetailMapper processOutDetailMapper;

    @Resource
    private ProcessOutDemandMapper processOutDemandMapper;

    @Resource
    private CostSpuConfigService costSpuConfigService;

    @Resource
    @Lazy
    private PurchaseProcessOutService purchaseProcessOutService;

    @Resource
    private CostProdPurchaseService costProdPurchaseService;

    @Resource
    private FinanceConnectService financeConnectService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(PurchaseProcessOutDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(PurchaseProcessOutDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(PurchaseProcessOutDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getPurchaseProcessOutId())
                .objCode(item.getPurchaseProcessOutCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }
    @Override
    protected Boolean businessVerify( PurchaseProcessOutDO item,  BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        if(buttonType == DataButtonEnum.APPROVE.getKey()) {
//            if (!DataStatusEnum.NOT_APPROVE.getKey().equals(item.getDataStatus())){
//                failItem.setCode(item.getPurchaseProcessOutCode());
//                failItem.setReason(ORDER_NOT_OPERATION_NOT_APPROVED.getMsg());
//                return false;
//            }
            //采购单的每个工序采购数量不可大于工序委外需求清单中的可采购数量
            Integer result = processOutDetailMapper.getPurchaseIsApproveAble(item);
            if (result == 0){
                failItem.setCode(item.getPurchaseProcessOutCode());
                failItem.setReason(PURCHASE_DETAIL_NUM_GREATER_DEMAND.getMsg());
                return false;
            }
        }

        if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            if (item.getFormStatus() != 0){
                failItem.setCode(item.getPurchaseProcessOutCode());
                failItem.setReason("工序委外采购订单单据状态为未开始,才能进行反审核操作!");
                return false;
            }

            PurchaseProcessOutDetailQueryReqVO detailQueryReqVO = new PurchaseProcessOutDetailQueryReqVO();
            detailQueryReqVO.setPurchaseProcessOutId(item.getPurchaseProcessOutId());
            List<PurchaseProcessOutDetailDO> processOutDetailList = processOutDetailMapper.selectList(detailQueryReqVO);
            //是否存在下游单据
            boolean existReceipt= false;
            boolean existOpered = false;
            for (PurchaseProcessOutDetailDO processOutDetailDO : processOutDetailList){
                BigDecimal receiptedQty = processOutDetailDO.getReceiptedQty();
                BigDecimal operedQty = processOutDetailDO.getOperedQty();

                if (receiptedQty.compareTo(BigDecimal.ZERO)>0){
                    existReceipt = true;
                    break;
                }
                if (operedQty.compareTo(BigDecimal.ZERO)>0) {
                    existOpered = true;
                    break;
                }
            }

            if (existReceipt){
                failItem.setCode(item.getPurchaseProcessOutCode());
                failItem.setReason("存在下游已审核的工序委外收货单,不允许进行反审核操作!");
                return false;
            }

            if (existOpered){
                failItem.setCode(item.getPurchaseProcessOutCode());
                failItem.setReason("存在下游已审核的工序委外采购退货单或工序委外采购扣费单,不允许进行反审核操作!");
                return false;
            }

            //财务反审校验
            financeConnectService.checkOrder(item.getPurchaseProcessOutId());

        }
        return true;
    }


    @Override
    public Integer handleBusinessData(PurchaseProcessOutDO item,  BaseApproveRequest request) {
        Long id = item.getPurchaseProcessOutId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        //查询工序委外采购订单
        PurchaseProcessOutDO purchaseProcessOutDO = processOutMapper.selectById(id);

        //查询工序委外采购订单明细
        PurchaseProcessOutDetailQueryReqVO detailQueryReqVO = new PurchaseProcessOutDetailQueryReqVO();
        detailQueryReqVO.setPurchaseProcessOutId(id);
        List<PurchaseProcessOutDetailDO> purchaseDetailDOList = processOutDetailMapper.selectList(detailQueryReqVO);

        Map<Long, BigDecimal> purchaseQtyMap = purchaseDetailDOList.stream().collect(Collectors
                .toMap(PurchaseProcessOutDetailDO::getProcessOutDemandId, PurchaseProcessOutDetailDO::getPurchaseQty));
        List<Long> processOutDemandIdList = purchaseDetailDOList.stream().map(PurchaseProcessOutDetailDO::getProcessOutDemandId).collect(Collectors.toList());


        //查询工序委外需求清单
        //处理工序委外需求单的purchaseable_qty（可采购数量）和purchased_qty（已采购数量）
        ProcessOutDemandQueryReqVO demandQueryReqVO = new ProcessOutDemandQueryReqVO();
        demandQueryReqVO.setProcessOutDemandIdList(processOutDemandIdList);
        List<ProcessOutDemandDO> processOutDemandDOList = processOutDemandMapper.selectList(demandQueryReqVO);

        if (buttonType == DataButtonEnum.APPROVE.getKey() || buttonType == DataButtonEnum.NOT_APPROVE.getKey()){

            if (buttonType == DataButtonEnum.APPROVE.getKey()) {
                for (ProcessOutDemandDO detailRespVO : processOutDemandDOList) {
                    Long processOutDemandId = detailRespVO.getProcessOutDemandId();
                    if (purchaseQtyMap.get(processOutDemandId) != null){
                        detailRespVO.setPurchaseableQty(detailRespVO.getPurchaseableQty().subtract(purchaseQtyMap.get(processOutDemandId)));
                        detailRespVO.setPurchasedQty(detailRespVO.getPurchasedQty().add(purchaseQtyMap.get(processOutDemandId)));
                    }
                    if (detailRespVO.getPurchasedQty().compareTo(detailRespVO.getOutDemandQty()) == 0){
                        detailRespVO.setFormStatus(2);
                    }else if (detailRespVO.getPurchasedQty() == BigDecimal.ZERO){
                        detailRespVO.setFormStatus(0);
                    }else {
                        detailRespVO.setFormStatus(1);
                    }
                }
            }

            if(buttonType == DataButtonEnum.APPROVE.getKey()) {

                //数据进入日成本
                PurchaseProcessOutRespVO purchaseOrderRespVO = purchaseProcessOutService.purchaseProcessOutDetail(id);
                for (PurchaseProcessOutDetailRespVO purchaseProcessOutDetailRespVO : purchaseOrderRespVO.getDetailList()){
                    CostProdPurchaseAditReqVO aditReqVO = new CostProdPurchaseAditReqVO();
                    aditReqVO.setOrderType((short) 2);
                    aditReqVO.setRelatedOrderCode(purchaseOrderRespVO.getPurchaseProcessOutCode());
                    aditReqVO.setRelatedOrderId(id);
                    aditReqVO.setRelatedOrderDetailId(purchaseProcessOutDetailRespVO.getProcessOutDetailId());
                    aditReqVO.setRelatedRowNo(purchaseProcessOutDetailRespVO.getRowNo());
                    aditReqVO.setFormDt(purchaseOrderRespVO.getFormDt());
                    aditReqVO.setRelatedSupplierId(purchaseOrderRespVO.getRelatedSupplierId());
                    aditReqVO.setMaterialId(purchaseProcessOutDetailRespVO.getMaterialId());
                    aditReqVO.setMaterialCode(purchaseProcessOutDetailRespVO.getMaterialCode());
                    aditReqVO.setProcessId(purchaseProcessOutDetailRespVO.getProcessId());
                    aditReqVO.setProcessCode(purchaseProcessOutDetailRespVO.getProcessCode());
                    aditReqVO.setQty(purchaseProcessOutDetailRespVO.getPurchaseQty());
                    aditReqVO.setExclTaxUnitPrice(purchaseProcessOutDetailRespVO.getExclTaxUnitPrice());
                    aditReqVO.setExclTaxAmt(purchaseProcessOutDetailRespVO.getExclTaxAmt());
                    aditReqVO.setCompanyOrgId(purchaseOrderRespVO.getCompanyOrgId());
                    costProdPurchaseService.costProdPurchaseAdd(aditReqVO);
                }
            }else if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

                for (ProcessOutDemandDO detailRespVO : processOutDemandDOList) {
                    Long processOutDemandId = detailRespVO.getProcessOutDemandId();
                    if (purchaseQtyMap.get(processOutDemandId) != null){
                        detailRespVO.setPurchaseableQty(detailRespVO.getPurchaseableQty().add(purchaseQtyMap.get(processOutDemandId)));
                        detailRespVO.setPurchasedQty(detailRespVO.getPurchasedQty().subtract(purchaseQtyMap.get(processOutDemandId)));
                    }
                    if (detailRespVO.getPurchasedQty().compareTo(detailRespVO.getOutDemandQty()) == 0){
                        detailRespVO.setFormStatus(2);
                    }else if (detailRespVO.getPurchasedQty() == BigDecimal.ZERO){
                        detailRespVO.setFormStatus(0);
                    }else {
                        detailRespVO.setFormStatus(1);
                    }
                }

                //删除日成本数据
                costProdPurchaseService.costRelatedOrderIdDel(id);
            }

            //更新工序委外需求单
            processOutDemandMapper.updateBatch(processOutDemandDOList);
        }

        //日成本初始化
        this.dailyCostInit(buttonType, purchaseProcessOutDO, purchaseDetailDOList);


        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        purchaseProcessOutDO.setApprovedBy(loginUser.getFullUserName());
        purchaseProcessOutDO.setApprovedDt(LocalDateTime.now());
        purchaseProcessOutDO.setDataStatus(dataStatus);
        processOutMapper.updateById(purchaseProcessOutDO);

        //数据进入财务
        processFinancialData(id,buttonType);

        return 1;
    }

    /**
     * 数据进入财务
     *
     * @param id
     * @param buttonType
     */
    private void processFinancialData(Long id, Integer buttonType) {

        PurchaseProcessOutRespVO processOutRespVO = purchaseProcessOutService.purchaseProcessOutDetail(id);
        List<PurchaseProcessOutDetailRespVO> outDetailList = processOutRespVO.getDetailList();

        PurchaseOrderRespVO purchaseOrderResp = BeanUtilX.copy(processOutRespVO, PurchaseOrderRespVO::new);
        List<PurchaseOrderDetailRespVO> purchaseOrderDetailList = new ArrayList<>();

        for (PurchaseProcessOutDetailRespVO outDetailResp : outDetailList){
            PurchaseOrderDetailRespVO purchaseOrderDetailResp = BeanUtilX.copy(outDetailResp, PurchaseOrderDetailRespVO::new);
            purchaseOrderDetailResp.setPurchaseOrderDetailId(outDetailResp.getProcessOutDetailId());//来源单据id
            purchaseOrderDetailList.add(purchaseOrderDetailResp);
        }
        purchaseOrderResp.setDetailList(purchaseOrderDetailList);

        purchaseOrderResp.setSourceFormType(OrderTypeEnum.PURCHASE_ORDER.type.shortValue());
        purchaseOrderResp.setRelatedOrderId(processOutRespVO.getPurchaseProcessOutId());
        purchaseOrderResp.setRelatedOrderCode(processOutRespVO.getPurchaseProcessOutCode());
        purchaseOrderResp.setPurchaseOrderId(processOutRespVO.getPurchaseProcessOutId());//来源单据id
        purchaseOrderResp.setPurchaseOrderCode(processOutRespVO.getPurchaseProcessOutCode());//来源单据code

        //源头单据id
        purchaseOrderResp.setOriginOrderId(id);

        financeConnectService.purchaseInsertFinance(purchaseOrderResp, buttonType);
    }

    private void dailyCostInit(Integer buttonType,PurchaseProcessOutDO purchaseProcessOutDO, List<PurchaseProcessOutDetailDO> purchaseDetailDOList) {
        ArrayList<CostSpuConfigAditReqVO> costSpuConfigList = new ArrayList<>();
        for (PurchaseProcessOutDetailDO purchaseProcessOutDetailDO : purchaseDetailDOList) {
            CostSpuConfigAditReqVO costSpuConfigAditReqVO = new CostSpuConfigAditReqVO();
            costSpuConfigAditReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey());
            costSpuConfigAditReqVO.setRelatedUpOrderId(purchaseProcessOutDetailDO.getPurchaseProcessOutId());
            costSpuConfigAditReqVO.setRelatedUpOrderCode(purchaseProcessOutDetailDO.getPurchaseProcessOutCode());
            costSpuConfigAditReqVO.setRelatedOrderDetailId(purchaseProcessOutDetailDO.getProcessOutDetailId());
            costSpuConfigAditReqVO.setMaterialId(purchaseProcessOutDetailDO.getMaterialId());
            costSpuConfigAditReqVO.setMaterialCode(purchaseProcessOutDetailDO.getMaterialCode());
            costSpuConfigAditReqVO.setFormDt(purchaseProcessOutDO.getFormDt());
            costSpuConfigAditReqVO.setOrderType(SpuConfigSourceOrderEnum.OUT_PROCESS_PURCHASE.code);
            costSpuConfigList.add(costSpuConfigAditReqVO);
        }
        costSpuConfigService.costSpuConfigOperate(buttonType, costSpuConfigList);
    }

    public static void handleAmount(PurchaseOrderRespVO respVO) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        BigDecimal exclTotalAmt = BigDecimal.ZERO;
        List<PurchaseOrderDetailDO> purchaseOrderDetailList = new ArrayList<>();
        List<PurchaseOrderDetailRespVO> detailList = respVO.getDetailList();
        for (PurchaseOrderDetailRespVO detail : detailList){
            BigDecimal unitPrice = BigDecimal.ZERO;
            BigDecimal amt = BigDecimal.ZERO;
            BigDecimal inclTaxPrice = BigDecimal.ZERO;
            BigDecimal inclTaxAmt = BigDecimal.ZERO;

            //以含税价价算未税单价,未税金额
            if (detail.getIncludingTax() != null && detail.getIncludingTax().equals(1)){
                //单价不含税
                unitPrice = MathUtilX.getUnitPrice(detail.getInclTaxUnitPrice(),detail.getTaxRate(), detail.getCalculatType());
                //不含税金额
                amt = MathUtilX.getAmt(unitPrice, detail.getPurchaseQty());
                //含税单价
                inclTaxPrice = MathUtilX.stripTrailingZeros(detail.getInclTaxUnitPrice());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getPurchaseQty());
            }else {
                //单价不含税
                unitPrice = MathUtilX.stripTrailingZeros(detail.getExclTaxUnitPrice());
                //不含税金额
                amt = MathUtilX.getAmt(detail.getExclTaxUnitPrice(), detail.getPurchaseQty());
                //含税单价
                inclTaxPrice = MathUtilX.getInclTaxPrice(detail.getExclTaxUnitPrice(), detail.getTaxRate(), detail.getCalculatType());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getPurchaseQty());
            }

            detail.setExclTaxUnitPrice(unitPrice);
            detail.setExclTaxAmt(amt);
            detail.setInclTaxUnitPrice(inclTaxPrice);
            detail.setInclTaxAmt(inclTaxAmt);
            //初始化已操作数量、可操作
            detail.setOperableQty(detail.getPurchaseQty());
            detail.setOperedQty(BigDecimal.ZERO);
            //订单含税总金额
            totalAmt = totalAmt.add(inclTaxAmt);
            exclTotalAmt = exclTotalAmt.add(amt);

            PurchaseOrderDetailDO purchaseOrderDetailDO = BeanUtilX.copy(detail, PurchaseOrderDetailDO::new);
            purchaseOrderDetailList.add(purchaseOrderDetailDO);
        }

        //重算含税金额
        respVO.setInclTaxTotalAmt(totalAmt);
        respVO.setExclTaxTotalAmt(exclTotalAmt);
    }
}
