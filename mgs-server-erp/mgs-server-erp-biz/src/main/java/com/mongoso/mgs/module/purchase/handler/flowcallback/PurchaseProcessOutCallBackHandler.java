package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： LinShuiQiang
 * @date： 2025/5/29
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurchaseProcessOutCallBackHandler extends FlowCallbackHandler<PurchaseProcessOutDO> {


    protected PurchaseProcessOutCallBackHandler(FlowApproveHandler<PurchaseProcessOutDO> approveHandler) {
        super(approveHandler);
    }
}