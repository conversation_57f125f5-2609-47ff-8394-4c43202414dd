package com.mongoso.mgs.module.comp.payroll.controller.admin.payroll.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 工资单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PayrollBaseVO implements Serializable {

    /** 工资单ID */
    private Long payrollId;

    /** 工资单主题 */
    private String payrollName;

    /** 工资单号 */
    private String payrollCode;

    /** 工资单月份 */
    @NotEmpty(message = "计薪月份不能为空")
    private String payrollMonth;

    /** 组织ID */
    @NotEmpty(message = "公司组织ID不能为空")
    private String companyOrgId;

    /** 计薪人数 */
    private Integer numberOfEmployee = 0;

    /** 应发总额 */
    private BigDecimal totalPayableAmt = BigDecimal.ZERO;

    /** 实发总额 */
    private BigDecimal totalPaidAmt = BigDecimal.ZERO;

    /** 个税总额 */
    private BigDecimal totalTaxAmt = BigDecimal.ZERO;

    /** 状态 ["未发放", "已发放"] */
    private Integer payrollStatus;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;
    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

}
