package com.mongoso.mgs.module.finance.controller.admin.feemanage.feerelatedloan.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 费用报销关联借款单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class FeeRelatedLoanQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long feeRelatedLoanId;

    /** 借款单号 */
    private String loanCode;

    /** 借款id */
    private Long feeLoanId;

    /** 费用报销付款id */
    private Long feeReimbursePaymentId;

    /** 费用报销付款code */
    private String reimbursePaymentCode;

    /** 费用报销id */
    private Long feeReimburseId;

    /** 费用报销code */
    private String reimburseCode;

    /** 抵扣金额 */
    private BigDecimal deductionAmt;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 单据状态 */
    private Short dataStatus;

    /** 行号 */
    private Short rowNo;

}
