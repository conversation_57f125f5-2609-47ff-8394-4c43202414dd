package com.mongoso.mgs.module.finance.controller.admin.cashbank.interaccounttran.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 账间转账 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class InterAccountTranBaseVO implements Serializable {

    /** 主键ID */
    private Long interAccountTranId;

    /** 转账单号 */
    private String transferCode;

    /** 转账标题 */
    @NotEmpty(message = "转账标题不能为空")
    private String transferName;

    /** 转账日期 */
//    @NotNull(message = "转账日期不能为空")
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate transferDate;

    /** 备注 */
    private String remark;

    /** 转出账户ID */
    @NotNull(message = "转出账户ID不能为空")
    private Long outAccountId;

    private String outAccountName;

    /** 转入账户ID */
    @NotNull(message = "转入账户ID不能为空")
    private Long inAccountId;

    private String inAccountName;

    /** 转出账户币种 */
    @NotEmpty(message = "转出账户币种不能为空")
    private String outCurrencyDictName;

    /** 转入账户币种 */
    @NotEmpty(message = "转入账户币种不能为空")
    private String inCurrencyDictName;

    /** 转出账户余额 */
    private BigDecimal outAccountBalance;

    /** 转入账户余额 */
    private BigDecimal inAccountBalance;

    /** 转出金额 */
    @NotNull(message = "转出金额不能为空")
    private BigDecimal outAmt;

    /** 转入金额 */
    @NotNull(message = "转入金额不能为空")
    private BigDecimal inAmt;

    /** 转出汇率 */
    @NotNull(message = "转出汇率不能为空")
    private BigDecimal outExchangeRate;

    /** 转入汇率 */
    @NotNull(message = "转入汇率不能为空")
    private BigDecimal inExchangeRate;

    /** 转出手续费 */
    @NotNull(message = "转出手续费不能为空")
    private BigDecimal outServiceFee;

    /** 转入手续费 */
    @NotNull(message = "转入手续费不能为空")
    private BigDecimal inServiceFee;

    /** 汇兑差额 */
    private BigDecimal exchangeDiffAmt;

    /** 责任部门 */
    private String directorOrgId;

    private String directorOrgName;

    /** 责任人 */
    private Long directorId;

    private String directorName;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核人 */
    private String approvedBy;

}
