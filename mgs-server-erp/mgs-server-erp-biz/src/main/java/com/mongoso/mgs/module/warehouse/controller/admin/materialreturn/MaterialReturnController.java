package com.mongoso.mgs.module.warehouse.controller.admin.materialreturn;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.ApproveResult;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.warehouse.controller.admin.materialreturn.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.materialreturn.MaterialReturnDO;
import com.mongoso.mgs.module.warehouse.service.materialreturn.MaterialReturnService;

/**
 * 归还单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warehouse")
@Validated
public class MaterialReturnController {

    @Resource
    private MaterialReturnService materialReturnService;

    @OperateLog("归还单添加或编辑")
    @PostMapping("/materialReturnAdit")
    @PreAuthorize("@ss.hasPermission('materialReturn:adit')")
    public ResultX<Long> materialReturnAdit(@Valid @RequestBody MaterialReturnAditReqVO reqVO) {
        return success(reqVO.getReturnId() == null
                            ? materialReturnService.materialReturnAdd(reqVO)
                            : materialReturnService.materialReturnEdit(reqVO));
    }

    @OperateLog("归还单删除")
    @PostMapping("/materialReturnDel")
    @PreAuthorize("@ss.hasPermission('materialReturn:delete')")
    public ResultX<Boolean> materialReturnDel(@Valid @RequestBody MaterialReturnPrimaryReqVO reqVO) {
        materialReturnService.materialReturnDel(reqVO.getReturnId());
        return success(true);
    }

    @OperateLog("归还单批量删除")
    @PostMapping("/materialReturnDelBatch")
    @PreAuthorize("@ss.hasPermission('materialReturn:delete')")
    public ResultX<BatchResult> materialReturnDelBatch(@Valid @RequestBody IdsReqVO reqVO) {
        return materialReturnService.materialReturnDelBatch(reqVO);
    }

    @OperateLog("归还单详情")
    @PostMapping("/materialReturnDetail")
    @PreAuthorize("@ss.hasPermission('materialReturn:query')")
    public ResultX<MaterialReturnRespVO> materialReturnDetail(@Valid @RequestBody MaterialReturnPrimaryReqVO reqVO) {
        return success(materialReturnService.materialReturnDetail(reqVO.getReturnId()));
    }

    @OperateLog("归还单列表")
    @PostMapping("/materialReturnList")
    @PreAuthorize("@ss.hasPermission('materialReturn:query')")
    @DataPermission
    public ResultX<List<MaterialReturnRespVO>> materialReturnList(@Valid @RequestBody MaterialReturnQueryReqVO reqVO) {
        return success(materialReturnService.materialReturnList(reqVO));
    }

    @OperateLog("归还单分页")
    @PostMapping("/materialReturnPage")
    @PreAuthorize("@ss.hasPermission('materialReturn:query')")
    @DataPermission
    public ResultX<PageResult<MaterialReturnRespVO>> materialReturnPage(@Valid @RequestBody MaterialReturnPageReqVO reqVO) {
        return success(materialReturnService.materialReturnPage(reqVO));
    }

    @OperateLog("归还单审核")
    @PostMapping("/materialReturnApprove")
    @PreAuthorize("@ss.hasPermission('materialReturn:adit')")
    public ResultX<BatchResult> materialReturnApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(materialReturnService.materialReturnApprove(reqVO));
    }

    @OperateLog("归还单审核回调接口")
    @PostMapping("/materialReturnFlowCallback")
    public ResultX<Object> materialLoanFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(materialReturnService.materialReturnFlowCallback(reqVO));
    }
}
