package com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
 import org.springframework.format.annotation.DateTimeFormat;

import java.time.*;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import java.time.LocalDateTime;
import java.util.List;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 出库单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpOutboundPageReqVO extends PageParam {

    /** 出库单号 */
    private String outboundCode;

    /** 出库单类型 */
    private String outboundTypeDictId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 收货对象ID */
    private Long receiptObjId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 是否完成发货*/
    private Integer isFullDelivered;

    /** 业务类型 */
    private Integer bizType;

    /** 排除业务类型 **/
    private List<Integer> exclBizType;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 审核状态 */
    private Integer dataStatus;

    /** 报工记录ID */
    private Long reportedWorkId;
}
