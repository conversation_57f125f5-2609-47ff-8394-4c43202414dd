package com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchasedetail.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY;
import static com.mongoso.mgs.framework.common.util.DateUtilX_backup.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 生产采购成本报表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostReportReqVO extends PageParam {

    /**
     * 0,1,2 日，周，月
     */

    @NotNull(message = "日期类型不能为空")
    private Integer dateType;

    private Long materialId;
    private String materialName;

    private List<Long> materialIdList;

    /** 公司组织ID */
    private String companyOrgId;
    /** 公司IdList*/
    private List<String> orgIdList;

    private String materialCode;

    /** 数量 */
    private BigDecimal purchaseQty;

    /** 总金额 */
    private BigDecimal purchaseAmt;

    private BigDecimal diffAmt;

    /** 数量 */
    private BigDecimal incomeQty;

    /** 总金额 */
    private BigDecimal incomeAmt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime endDate;


}
