package com.mongoso.mgs.module.finance.controller.admin.advancepaymentrefund;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.advancepaymentrefund.vo.*;
import com.mongoso.mgs.module.finance.service.advancepaymentrefund.AdvancePaymentRefundService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 退预收款 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/advance")
@Validated
public class AdvancePaymentRefundController {

    @Resource
    private AdvancePaymentRefundService paymentRefundService;

    @OperateLog("退预收款添加或编辑")
    @PostMapping("/advancePaymentRefundAdit")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:adit')")
    public ResultX<Long> advancePaymentRefundAdit(@Valid @RequestBody AdvancePaymentRefundAditReqVO reqVO) {
        return success(reqVO.getRefundAdvanceId() == null
                            ? paymentRefundService.advancePaymentRefundAdd(reqVO)
                            : paymentRefundService.advancePaymentRefundEdit(reqVO));
    }

    @OperateLog("退预收款删除")
    @PostMapping("/advancePaymentRefundDel")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:delete')")
    public ResultX<Boolean> advancePaymentRefundDel(@Valid @RequestBody AdvancePaymentRefundPrimaryReqVO reqVO) {
        paymentRefundService.advancePaymentRefundDel(reqVO.getRefundAdvanceId());
        return success(true);
    }

    @OperateLog("退预收款详情")
    @PostMapping("/advancePaymentRefundDetail")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:query')")
    public ResultX<AdvancePaymentRefundRespVO> advancePaymentRefundDetail(@Valid @RequestBody AdvancePaymentRefundPrimaryReqVO reqVO) {
        return success(paymentRefundService.advancePaymentRefundDetail(reqVO.getRefundAdvanceId()));
    }

    @OperateLog("退预收款列表")
    @PostMapping("/advancePaymentRefundList")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:query')")
    @DataPermission
    public ResultX<List<AdvancePaymentRefundRespVO>> advancePaymentRefundList(@Valid @RequestBody AdvancePaymentRefundQueryReqVO reqVO) {
        return success(paymentRefundService.advancePaymentRefundList(reqVO));
    }

    @OperateLog("退预收款分页")
    @PostMapping("/advancePaymentRefundPage")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:query')")
    @DataPermission
    public ResultX<PageResult<AdvancePaymentRefundRespVO>> advancePaymentRefundPage(@Valid @RequestBody AdvancePaymentRefundPageReqVO reqVO) {
        return success(paymentRefundService.advancePaymentRefundPage(reqVO));
    }

    @OperateLog("退预收款列表")
    @PostMapping("/advancePaymentRefundQuoteList")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:query')")
    public ResultX<List<AdvancePaymentRefundRespVO>> advancePaymentRefundQuoteList(@Valid @RequestBody AdvancePaymentRefundQueryReqVO reqVO) {
        return success(paymentRefundService.advancePaymentRefundList(reqVO));
    }

    @OperateLog("退预收款分页")
    @PostMapping("/advancePaymentRefundQuotePage")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:query')")
    public ResultX<PageResult<AdvancePaymentRefundRespVO>> advancePaymentRefundQuotePage(@Valid @RequestBody AdvancePaymentRefundPageReqVO reqVO) {
        return success(paymentRefundService.advancePaymentRefundPage(reqVO));
    }

    @OperateLog("退预收款批量删除")
    @PostMapping("/advancePaymentRefundDelBatch")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:delete')")
    public ResultX<BatchResult> advancePaymentRefundDelBatch(@Valid @RequestBody IdReq reqVO) {
        return paymentRefundService.advancePaymentRefundDelBatch(reqVO);
    }

    @OperateLog("退预收款审核")
    @PostMapping("/advancePaymentRefundApprove")
    @PreAuthorize("@ss.hasPermission('advancePaymentRefund:adit')")
    public ResultX<BatchResult> advancePaymentRefundApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = paymentRefundService.advancePaymentRefundApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("退预收款回调接口")
    @PostMapping("/advancePaymentRefundFlowCallback")
    public ResultX<Object> advancePaymentRefundFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(paymentRefundService.advancePaymentRefundFlowCallback(reqVO));
    }
}
