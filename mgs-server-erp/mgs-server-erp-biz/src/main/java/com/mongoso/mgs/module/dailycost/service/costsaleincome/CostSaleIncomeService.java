package com.mongoso.mgs.module.dailycost.service.costsaleincome;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.AmortiseStatusReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo.CostSaleIncomeAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo.CostSaleIncomePageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo.CostSaleIncomeQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo.CostSaleIncomeRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 销售收入单 Service 接口
 *
 * <AUTHOR>
 */
public interface CostSaleIncomeService {

    /**
     * 创建销售收入单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costSaleIncomeAdd(@Valid CostSaleIncomeAditReqVO reqVO);

    /**
     * 更新销售收入单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long costSaleIncomeEdit(@Valid CostSaleIncomeAditReqVO reqVO);

    /**
     * 销售收入单修改承担物料信息
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long undertakeMaterialAdit(@Valid CostSaleIncomeAditReqVO reqVO);

    /**
     * 删除销售收入单
     *
     * @param saleIncomeId 编号
     */
    void costSaleIncomeDel(Long saleIncomeId);

    /**
     * 删除销售收入单
     *
     * @param relatedUpFormId 编号
     */
    void relatedUpFormIdDel(Long relatedUpFormId);

    /**
     * 获得销售收入单信息
     *
     * @param saleIncomeId 编号
     * @return 销售收入单信息
     */
    CostSaleIncomeRespVO costSaleIncomeDetail(Long saleIncomeId);

    /**
     * 获得销售收入单列表
     *
     * @param reqVO 查询条件
     * @return 销售收入单列表
     */
    List<CostSaleIncomeRespVO> costSaleIncomeList(@Valid CostSaleIncomeQueryReqVO reqVO);

    /**
     * 获得销售收入单分页
     *
     * @param reqVO 查询条件
     * @return 销售收入单分页
     */
    PageResult<CostSaleIncomeRespVO> costSaleIncomePage(@Valid CostSaleIncomePageReqVO reqVO);

    /**
     * 摊销状态变更
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    BatchResult costSaleIncomeAmortise(@Valid AmortiseStatusReqVO reqVO);

}
