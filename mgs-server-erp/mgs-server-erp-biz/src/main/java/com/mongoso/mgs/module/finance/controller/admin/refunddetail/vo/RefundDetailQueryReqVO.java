package com.mongoso.mgs.module.finance.controller.admin.refunddetail.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 退款明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class RefundDetailQueryReqVO extends CommonParam{

    /** 退款详情主键ID */
    private Long refundDetailId;

    /** 退款主键ID */
    private Long refundId;

    /** 单据类型 */
    private Short formType;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 来源单行号 */
    private Long sourceLineNumber;

    /** 来源单据数量 */
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 主单位ID */
    private String mainUnitDictId;

    /** 票据类型ID */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 主单位名称 */
    private String mainUnitDictName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 剩余应退数量 */
    private BigDecimal remainingRefundQty;

    /** 本次退款数量 */
    private BigDecimal currentRefundQty;

    /** 剩余退款金额(含税) */
    private BigDecimal refundableAmt;

    /** 本次退款金额(含税) */
    private BigDecimal inclCurrentRefundAmt;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
