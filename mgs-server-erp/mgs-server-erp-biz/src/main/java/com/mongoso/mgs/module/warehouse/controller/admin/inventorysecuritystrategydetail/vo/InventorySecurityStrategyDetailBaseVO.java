package com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategydetail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 安全库存策略明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class InventorySecurityStrategyDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 安全库存策略id */
    private String inventorySecurityStrategyId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 最小值 */
    private String minValue;

    /** 最大值 */
    private String maxValue;

    /** 行号 */
    private String rowNo;

    /** 备注 */
    private String remark;

    /** 责任人 */
    @NotNull(message = "责任人不能为空")
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人ID */
    private Long createdId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核状态 */
    @NotNull(message = "审核状态不能为空")
    private Short dataStatus;

}
