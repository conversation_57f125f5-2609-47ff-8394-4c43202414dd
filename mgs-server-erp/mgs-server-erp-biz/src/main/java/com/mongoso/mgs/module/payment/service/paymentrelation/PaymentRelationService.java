package com.mongoso.mgs.module.payment.service.paymentrelation;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.payment.controller.admin.paymentrelation.vo.*;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 收款关系 Service 接口
 *
 * <AUTHOR>
 */
public interface PaymentRelationService {

    /**
     * 创建收款关系
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long paymentRelationAdd(@Valid PaymentRelationAditReqVO reqVO);

    /**
     * 更新收款关系
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long paymentRelationEdit(@Valid PaymentRelationAditReqVO reqVO);

    /**
     * 删除收款关系
     *
     * @param id 编号
     */
    void paymentRelationDelete(Long id);

    /**
     * 获得收款关系信息
     *
     * @param id 编号
     * @return 收款关系信息
     */
    PaymentRelationRespVO paymentRelationDetail(Long id);

    /**
     * 获得收款关系列表
     *
     * @param reqVO 查询条件
     * @return 收款关系列表
     */
    List<PaymentRelationRespVO> paymentRelationList(@Valid PaymentRelationQueryReqVO reqVO);

    /**
     * 获得收款关系分页
     *
     * @param reqVO 查询条件
     * @return 收款关系分页
     */
    PageResult<PaymentRelationRespVO> paymentRelationPage(@Valid PaymentRelationPageReqVO reqVO);

}
