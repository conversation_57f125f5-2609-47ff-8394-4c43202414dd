package com.mongoso.mgs.module.base.controller.admin.saleorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mongoso.mgs.module.base.controller.admin.saleordermaterial.vo.SaleorderMaterialBaseVO;
import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 销售订单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SaleOrderBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 销售订单编码【系统生成】 */
    private String saleOrderCode;

    /** 销售订单编号 */
    @NotBlank(message = "对方销售编号不允许为空")
    @Size(max = 50, message = "对方销售编号最大可输入字符数50")
    private String saleOrderNo;

    /** 生产订单主键id */
    private Long prodOrderId;

    /** 生产订单编码 */
    private String prodOrderCode;

    /** 生产订单编码拼接所有的销售订单编号 */
    private String prodSaleOrderCode;

    @NotNull(message = "客户主键id不允许为空")
    private Long customerId;

    /** 客户编码 */
    @NotBlank(message = "客户编码不允许为空")
    private String customerCode;

    /** 客户名称 */
    @NotBlank(message = "客户名称不允许为空")
    private String customerName;

    @NotNull(message = "产品主键id不允许为空")
    private Long productId;

    /** 产品编码 */
    @NotBlank(message = "产品编码不允许为空")
    private String productCode;

    /** 币种 */
    @NotBlank(message = "币种不允许为空")
    private String currency;

    /** 产品名称 */
    @NotBlank(message = "产品名称不允许为空")
    private String productName;

    /** 限制下限 */
//    @NotNull(message = "限制下限不允许为空")
    private Integer lowerLimit;

    /** 限制上限 */
//    @NotNull(message = "限制上限不允许为空")
    private Integer upperLimit;

    /** 下单日期 */
    @JsonFormat(pattern = FORMAT_yyyy_MM_dd)
    @NotNull(message = "下单日期不允许为空")
    private LocalDate orderedDt;

    /** 完成日期 */
    @JsonFormat(pattern = FORMAT_yyyy_MM_dd)
    @NotNull(message = "完成日期不允许为空")
    private LocalDate completionDate;

    /** 订单状态 待开始/进行中/已完成/已关闭  */
    private Integer orderState;

    /** 备注 */
    @Size(max = 250, message = "备注最大可输入字符250")
    private String remark;

    /** 订单总数量 */
    private Integer orderQty;

    /** 订单总金额 */
    private BigDecimal totalAmt;

    /** 生产订单总数量 */
    private Integer prodTotalCount;

    /** 已生产总数量 */
    private Integer actProdTotalCount;

    /** 已出库总数量 */
    private Integer outboundTotalQty;

    /** 销售物料 */
    @Valid
    @NotEmpty(message = "产品信息不能为空,请输入有效的产品")
    private List<SaleorderMaterialBaseVO> saleMaterialList;

    /** 操作类型 确认完成 */
    private Integer type;

    //关闭原因
    private String closeReason;

    /**
     * 多租户编号
     */
    private Long tenantId;

    // bom类型 ["自定义bom",“产品bom”,"物料bom"]
    private Integer bomType;

    /** 是否需要容差['是','否'] */
    private Integer isTolerance;

    /** 容差类型 ['按订单总数','按单物料总数']*/
    private Integer tolerance;

    /** 负责人主键ID */
    private String employeeId;

    /** 负责人姓名 */
    private String employeeName;

    /** 面料 */
    private String productExt01;

    /** 面料成份 */
    private String productExt02;

    /** 袋布 */
    private String productExt03;

    /** 洗水 */
    private String productExt04;
}
