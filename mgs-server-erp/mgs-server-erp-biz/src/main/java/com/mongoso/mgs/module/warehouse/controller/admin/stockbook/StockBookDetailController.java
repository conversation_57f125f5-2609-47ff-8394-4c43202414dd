package com.mongoso.mgs.module.warehouse.controller.admin.stockbook;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.*;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 库存预订明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warehouse")
@Validated
public class StockBookDetailController {

    @Resource
    private StockBookDetailService bookDetailService;

    @OperateLog("库存预订明细添加或编辑")
    @PostMapping("/stockBookDetailAdit")
    @PreAuthorize("@ss.hasPermission('stockBookDetail:adit')")
    public ResultX<Long> stockBookDetailAdit(@Valid @RequestBody StockBookDetailAditReqVO reqVO) {
        return success(reqVO.getStockBookDetailId() == null
                            ? bookDetailService.stockBookDetailAdd(reqVO)
                            : bookDetailService.stockBookDetailEdit(reqVO));
    }

    @OperateLog("库存预订明细删除")
    @PostMapping("/stockBookDetailDel")
    @PreAuthorize("@ss.hasPermission('stockBookDetail:delete')")
    public ResultX<Boolean> stockBookDetailDel(@Valid @RequestBody StockBookDetailPrimaryReqVO reqVO) {
        bookDetailService.stockBookDetailDel(reqVO.getStockBookDetailId());
        return success(true);
    }

    @OperateLog("库存预订明细详情")
    @PostMapping("/stockBookDetailDetail")
    @PreAuthorize("@ss.hasPermission('stockBookDetail:query')")
    public ResultX<StockBookDetailRespVO> stockBookDetailDetail(@Valid @RequestBody StockBookDetailPrimaryReqVO reqVO) {
        return success(bookDetailService.stockBookDetailDetail(reqVO.getStockBookDetailId()));
    }

    @OperateLog("库存预订明细列表")
    @PostMapping("/stockBookDetailList")
    @PreAuthorize("@ss.hasPermission('stockBookDetail:query')")
    public ResultX<List<StockBookDetailRespVO>> stockBookDetailList(@Valid @RequestBody StockBookDetailQueryReqVO reqVO) {
        return success(bookDetailService.stockBookDetailList(reqVO));
    }

    @OperateLog("库存预订明细分页")
    @PostMapping("/stockBookDetailPage")
    @PreAuthorize("@ss.hasPermission('stockBookDetail:query')")
    public ResultX<PageResult<StockBookDetailRespVO>> stockBookDetailPage(@Valid @RequestBody StockBookDetailPageReqVO reqVO) {
        return success(bookDetailService.stockBookDetailPage(reqVO));
    }

}
