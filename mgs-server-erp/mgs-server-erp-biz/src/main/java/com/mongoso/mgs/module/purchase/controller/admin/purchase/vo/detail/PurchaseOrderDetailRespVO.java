package com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 采购订单明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseOrderDetailRespVO extends PurchaseOrderDetailBaseVO {

    /** 不可变动值 */
    private BigDecimal nonChangeValue;
    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 采购订单类型 */
    private String purchaseTypeDictId;
    private String purchaseTypeDictName;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 关联供应商 */
    private Long relatedSupplierId;
    private String relatedSupplierName;

    /** 交货日期 */
//    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate deliveryDate;

    /** 明细交货日期 */
    private LocalDate detailDeliveryDate;

    /** 采购交货日期 */
    private LocalDate purchaseDeliveryDate;

    /** 币种字典名称 */
    private String currencyDictId;
    private String currencyDictName;

    /** 主体公司 */
    private String companyOrgId;
    private String companyOrgName;

    /** 结算方式 */
    private String settlementMethodDictId;
    private String settlementMethodDictName;

    /** 付款条件 */
    private String paymentTermsDictId;
    private String paymentTermsDictName;

    /** 订单总金额（含税） */
    private BigDecimal inclTaxTotalAmt;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 规格设置 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 基本单位 */
    private String mainUnitDictName;

    /** 票据类型ID */
    private Long invoiceTypeId;
    private String invoiceTypeName;

    /** 可采购数量 */
    private BigDecimal purchaseAbleQty;

    /** 已收货数量 */
    private BigDecimal receiptedQty;

    /** 已入库数量 */
    private BigDecimal inboundedQty;

    /** 已退货数量 */
    private BigDecimal returnedQty;

    /** 已扣费数量 */
    private BigDecimal deductedQty;

    /** 已操作数量 */
    private BigDecimal operedQty;

    /** 可操作数量 */
    private BigDecimal operableQty;

    /** 物料是否全部已操作 */
    private Integer isMaterialFullOpered;

    /** 审核状态 */
    private Integer dataStatus;
    private String dataStatusDictName;

    /** 是否带料 */
    private Short isTakeMaterial;

    /** 是否带模具 */
    private Short isTakeMold;

    /** 可采购数量 */
    private BigDecimal purchaseableQty;

    /** 入库状态["未入库","部分入库","已完成"] */
    private String inboundStatusDictName;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 采购标准价 */
    private BigDecimal purchaseStandardPrice;

    /** 加工费 */
    private BigDecimal processingFee;

}
