package com.mongoso.mgs.module.sale.controller.admin.salechang.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDate;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;
import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 销售订单变更单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SaleChangPageReqVO extends PageParam {

    /** 销售订单变更单号 */
    private String salesOrderChangeCode;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 销售订单id */
    private Long saleOrderId;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 销售订单类型 */
    private String salesOrderTypeDictId;

    /** 销售订单类型名称 */
    private String salesOrderTypeDictName;

    /** 关联单据id */
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 关联客户 */
    private Long customerId;

    /** 公司主体 */
    private String companyOrgId;

    /** 联系人 */
    private String contactPersonName;

    /** 联系人电话 */
    private String contactPhone;

    /** 收货人 */
    private String recipientName;

    /** 收货人电话 */
    private String recipientPhone;

    /** 收货地址 */
    private String receiptAddress;

    /** 币种id */
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startDeliveryDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endDeliveryDate;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 收款条件 */
    private String paymentTermsDictId;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 订单总金额 */
    private BigDecimal totalAmt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 产品明细id */
    private Long saleChangDetailId;

    /** 物料id */
    private Long materialId;
    private List<Long> materialIdList;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;

    /** 规格设置 */
    private String specModel;

}
