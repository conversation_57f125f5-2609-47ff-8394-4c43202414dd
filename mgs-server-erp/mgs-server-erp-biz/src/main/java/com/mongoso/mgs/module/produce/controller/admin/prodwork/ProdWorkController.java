package com.mongoso.mgs.module.produce.controller.admin.prodwork;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.*;
import com.mongoso.mgs.module.produce.service.prodwork.ProdWorkService;

/**
 * 生产工单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class ProdWorkController {

    @Resource
    private ProdWorkService prodWorkService;

    @OperateLog("生产工单添加或编辑")
    @PostMapping("/prodWorkAdit")
    @PreAuthorize("@ss.hasPermission('prodWork:adit')")
    public ResultX<Long> prodWorkAdit(@Valid @RequestBody ProdWorkAditReqVO reqVO) {
        return success(reqVO.getProdWorkId() == null
                            ? prodWorkService.prodWorkAdd(reqVO)
                            : prodWorkService.prodWorkEdit(reqVO));
    }

    @OperateLog("批量新增生产工单")
    @PostMapping("/prodWorkAditBatch")
    @PreAuthorize("@ss.hasPermission('prodWork:adit')")
    public ResultX<Long> prodWorkAditBatch(@Valid @RequestBody ProdWorkBatchAditReqVO reqVO) {
        return success(prodWorkService.prodWorkAditBatch(reqVO));
    }

    @OperateLog("生产工单删除")
    @PostMapping("/prodWorkDel")
    @PreAuthorize("@ss.hasPermission('prodWork:delete')")
    public ResultX<Boolean> prodWorkDel(@Valid @RequestBody ProdWorkPrimaryReqVO reqVO) {
        prodWorkService.prodWorkDel(reqVO.getProdWorkId());
        return success(true);
    }

    @OperateLog("生产工单详情")
    @PostMapping("/prodWorkDetail")
    @PreAuthorize("@ss.hasPermission('prodWork:query')")
    public ResultX<ProdWorkRespVO> prodWorkDetail(@Valid @RequestBody ProdWorkPrimaryReqVO reqVO) {
        return success(prodWorkService.prodWorkDetail(reqVO.getProdWorkId()));
    }

    @OperateLog("生产工单详情")
    @PostMapping("/prodWorkQuotedDetail")
    @PreAuthorize("@ss.hasPermission('prodWork:query')")
    public ResultX<ProdWorkQuotedRespVO> prodWorkQuotedDetail(@Valid @RequestBody ProdWorkPrimaryReqVO reqVO) {
        return success(prodWorkService.prodWorkQuotedDetail(reqVO.getProdWorkId()));
    }

    @OperateLog("生产工单列表")
    @PostMapping("/prodWorkList")
    @PreAuthorize("@ss.hasPermission('prodWork:query')")
    @DataPermission
    public ResultX<List<ProdWorkRespVO>> prodWorkList(@Valid @RequestBody ProdWorkQueryReqVO reqVO) {
        return success(prodWorkService.prodWorkList(reqVO));
    }

    @OperateLog("生产工单列表")
    @PostMapping("/prodWorkQuotedList")
    @PreAuthorize("@ss.hasPermission('prodWork:query')")
    public ResultX<List<ProdWorkRespVO>> prodWorkQuotedList(@Valid @RequestBody ProdWorkQueryReqVO reqVO) {
        return success(prodWorkService.prodWorkList(reqVO));
    }

    @OperateLog("生产工单分页")
    @PostMapping("/prodWorkPage")
    @PreAuthorize("@ss.hasPermission('prodWork:query')")
    @DataPermission
    public ResultX<PageResult<ProdWorkRespVO>> prodWorkPage(@Valid @RequestBody ProdWorkPageReqVO reqVO) {
        return success(prodWorkService.prodWorkPage(reqVO));
    }

    @OperateLog("生产工单分页")
    @PostMapping("/prodWorkQuotedPage")
    @PreAuthorize("@ss.hasPermission('prodWork:query')")
    public ResultX<PageResult<ProdWorkRespVO>> prodWorkQuotedPage(@Valid @RequestBody ProdWorkPageReqVO reqVO) {
        return success(prodWorkService.prodWorkPage(reqVO));
    }


    @OperateLog("生产工单删除")
    @PostMapping("/prodWorkDelBatch")
    @PreAuthorize("@ss.hasPermission('device:del')")
    public ResultX<BatchResult> prodWorkDelBatch(@Valid @RequestBody IdReq reqVO) {
        return prodWorkService.prodWorkDelBatch(reqVO);
    }

    @OperateLog("生产工单审核")
    @PostMapping("/prodWorkApprove")
    @PreAuthorize("@ss.hasPermission('device:adit')")
    public ResultX<BatchResult> prodWorkApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = prodWorkService.prodWorkApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("生产工单回调接口")
    @PostMapping("/prodWorkFlowCallback")
    public ResultX<Object> prodWorkFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(prodWorkService.prodWorkFlowCallback(reqVO));
    }

    @OperateLog("生产工单状态变更")
    @PostMapping("/prodWorkStatus")
    public ResultX<Boolean> prodWorkStatus(@Valid @RequestBody ProdWorkPrimaryReqVO reqVO) {
        return success(prodWorkService.prodWorkStatus(reqVO));
    }

    @OperateLog("直接领料出库引用生产工单明细物料")
    @PostMapping("/prodWorkMaterialDetailTree")
    public ResultX<List<ProdWorkMaterialDetailTreeRespVO>> prodWorkMaterialDetailTree(@Valid @RequestBody ProdWorkMaterialDetailTreeReqVO reqVO) {
        return success(prodWorkService.prodWorkMaterialDetailTree(reqVO));
    }


}
