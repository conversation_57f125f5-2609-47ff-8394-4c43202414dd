package com.mongoso.mgs.module.system.controller.admin.systemstatus;

import com.mongoso.mgs.common.job.forewarn.ForewarnServiceJob;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.system.controller.admin.systemstatus.vo.*;
import com.mongoso.mgs.module.system.service.systemstatus.SystemStatusService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 系统状态 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system")
@Validated
public class SystemStatusController {

    @Resource
    private SystemStatusService statusService;

    @OperateLog("系统状态添加或编辑")
    @PostMapping("/systemStatusAdit")
    @PreAuthorize("@ss.hasPermission('systemStatus:adit')")
    public ResultX<Short> systemStatusAdit(@Valid @RequestBody SystemStatusAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? statusService.systemStatusAdd(reqVO)
                            : statusService.systemStatusEdit(reqVO));
    }

    @OperateLog("系统状态删除")
    @PostMapping("/systemStatusDel")
    @PreAuthorize("@ss.hasPermission('systemStatus:delete')")
    public ResultX<Boolean> systemStatusDel(@Valid @RequestBody SystemStatusPrimaryReqVO reqVO) {
        statusService.systemStatusDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("系统状态详情")
    @PostMapping("/systemStatusDetail")
    @PreAuthorize("@ss.hasPermission('systemStatus:query')")
    public ResultX<SystemStatusRespVO> systemStatusDetail() {
        return success(statusService.systemStatusDetail());
    }

    @OperateLog("系统状态列表")
    @PostMapping("/systemStatusList")
    @PreAuthorize("@ss.hasPermission('systemStatus:query')")
    public ResultX<List<SystemStatusRespVO>> systemStatusList(@Valid @RequestBody SystemStatusQueryReqVO reqVO) {
        return success(statusService.systemStatusList(reqVO));
    }

    @OperateLog("系统状态分页")
    @PostMapping("/systemStatusPage")
    @PreAuthorize("@ss.hasPermission('systemStatus:query')")
    public ResultX<PageResult<SystemStatusRespVO>> systemStatusPage(@Valid @RequestBody SystemStatusPageReqVO reqVO) {
        return success(statusService.systemStatusPage(reqVO));
    }

    @Resource
    ForewarnServiceJob forewarnServiceJob;

    @OperateLog("测试预警任务")
    @PostMapping("/demo1")
    public ResultX<String> demo1(@RequestBody SystemStatusPageReqVO reqVO) {
        String s = forewarnServiceJob.forewarnJob();
        return success(s);
    }

}
