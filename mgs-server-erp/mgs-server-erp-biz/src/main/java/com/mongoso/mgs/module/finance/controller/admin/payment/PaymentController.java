package com.mongoso.mgs.module.finance.controller.admin.payment;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.payment.vo.*;
import com.mongoso.mgs.module.finance.service.payment.PaymentService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 收款单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment")
@Validated
public class PaymentController {

    @Resource
    private PaymentService paymentService;

    @OperateLog("收款单添加或编辑")
    @PostMapping("/paymentAdit")
    @PreAuthorize("@ss.hasPermission('payment:adit')")
    public ResultX<Long> paymentAdit(@Valid @RequestBody PaymentAditReqVO reqVO) {
        return success(reqVO.getPayId() == null
                            ? paymentService.paymentAddNew(reqVO)
                            : paymentService.paymentEditNew(reqVO));
    }

    @OperateLog("收款单删除")
    @PostMapping("/paymentDel")
    @PreAuthorize("@ss.hasPermission('payment:delete')")
    public ResultX<Boolean> paymentDel(@Valid @RequestBody PaymentPrimaryReqVO reqVO) {
        paymentService.paymentDel(reqVO.getPayId());
        return success(true);
    }

    @OperateLog("收款批量删除")
    @PostMapping("/paymentDelBatch")
    @PreAuthorize("@ss.hasPermission('payment:delete')")
    public ResultX<BatchResult> paymentDelBatch(@Valid @RequestBody IdReq reqVO) {
        return paymentService.paymentDelBatch(reqVO);
    }

    @OperateLog("收款单详情")
    @PostMapping("/paymentDetail")
    @PreAuthorize("@ss.hasPermission('payment:query')")
    public ResultX<PaymentRespVO> paymentDetail(@Valid @RequestBody PaymentPrimaryReqVO reqVO) {
        return success(paymentService.paymentDetailNew(reqVO.getPayId()));
    }

    @OperateLog("收款单列表")
    @PostMapping("/paymentList")
    @PreAuthorize("@ss.hasPermission('payment:query')")
    @DataPermission
    public ResultX<List<PaymentRespVO>> paymentList(@Valid @RequestBody PaymentQueryReqVO reqVO) {
        return success(paymentService.paymentList(reqVO));
    }

    @OperateLog("收款单分页")
    @PostMapping("/paymentPage")
    @PreAuthorize("@ss.hasPermission('payment:query')")
    @DataPermission
    public ResultX<PageResult<PaymentRespVO>> paymentPage(@Valid @RequestBody PaymentPageReqVO reqVO) {
        return success(paymentService.paymentPage(reqVO));
    }

    @OperateLog("收款单列表")
    @PostMapping("/paymentQuoteList")
    @PreAuthorize("@ss.hasPermission('payment:query')")
    public ResultX<List<PaymentRespVO>> paymentQuoteList(@Valid @RequestBody PaymentQueryReqVO reqVO) {
        return success(paymentService.paymentList(reqVO));
    }

    @OperateLog("收款单分页")
    @PostMapping("/paymentQuotePage")
    @PreAuthorize("@ss.hasPermission('payment:query')")
    public ResultX<PageResult<PaymentRespVO>> paymentQuotePage(@Valid @RequestBody PaymentPageReqVO reqVO) {
        return success(paymentService.paymentPage(reqVO));
    }

    @OperateLog("收款审核")
    @PostMapping("/paymentApprove")
    @PreAuthorize("@ss.hasPermission('payment:adit')")
    public ResultX<BatchResult> paymentApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = paymentService.paymentApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("收款回调接口")
    @PostMapping("/paymentFlowCallback")
    public ResultX<Object> paymentFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(paymentService.paymentFlowCallback(reqVO));
    }
}
