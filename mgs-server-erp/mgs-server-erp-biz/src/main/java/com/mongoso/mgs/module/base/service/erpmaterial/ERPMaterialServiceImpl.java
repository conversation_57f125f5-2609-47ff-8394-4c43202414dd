package com.mongoso.mgs.module.base.service.erpmaterial;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.FileTableEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.material.CostTypeEnum;
import com.mongoso.mgs.common.enums.material.PublishEnum;
import com.mongoso.mgs.common.enums.purchase.PurchaseBizTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.FormulaValidationResult;
import com.mongoso.mgs.common.util.FormulaValidatorUtil;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.customercontact.vo.CustomerContactBaseVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.*;
import com.mongoso.mgs.module.base.controller.admin.materialsamplingstrategy.vo.MaterialSamplingStrategyBaseVO;
import com.mongoso.mgs.module.base.controller.admin.materialsamplingstrategy.vo.MaterialSamplingStrategyQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.materialspec.vo.MaterialSpecQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.materialspec.vo.MaterialSpecRespVO;
import com.mongoso.mgs.module.base.controller.admin.materialunit.vo.MaterialUnitQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.materialunit.vo.MaterialUnitRespVO;
import com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO;
import com.mongoso.mgs.module.base.dal.db.hzmaterial.HzMaterialDO;
import com.mongoso.mgs.module.base.dal.db.materialsamplingstrategy.MaterialSamplingStrategyDO;
import com.mongoso.mgs.module.base.dal.db.materialspec.MaterialSpecDO;
import com.mongoso.mgs.module.base.dal.db.materialunit.MaterialUnitDO;
import com.mongoso.mgs.module.base.dal.db.spu.SpuDO;
import com.mongoso.mgs.module.base.dal.mysql.erpmaterial.ERPMaterialMapper;
import com.mongoso.mgs.module.base.dal.mysql.hzmaterial.HzMaterialMapper;
import com.mongoso.mgs.module.base.dal.mysql.materialsamplingstrategy.MaterialSamplingStrategyMapper;
import com.mongoso.mgs.module.base.dal.mysql.materialspec.MaterialSpecMapper;
import com.mongoso.mgs.module.base.dal.mysql.materialunit.MaterialUnitMapper;
import com.mongoso.mgs.module.base.dal.mysql.spu.SpuMapper;
import com.mongoso.mgs.module.base.handler.approve.ErpMaterialApproveHandler;
import com.mongoso.mgs.module.base.handler.flowCallback.ErpMaterialFlowCallbackHandler;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.bo.CostBO;
import com.mongoso.mgs.module.base.service.erpmaterial.bo.MaterialSupplierBO;
import com.mongoso.mgs.module.base.service.erpsupplier.bo.ERPSupplierBO;
import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogReqVO;
import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogRespVO;
import com.mongoso.mgs.module.infra.service.file.FileService;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderdetail.vo.ErpProdOrderDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal.vo.MaterialAnalysisTotalQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomChainRespVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.MaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialbom.vo.UpdateCostVO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.MaterialAnalysisTotalDO;
import com.mongoso.mgs.module.produce.dal.db.materialbom.MaterialBomDO;
import com.mongoso.mgs.module.produce.dal.db.materialsupplier.MaterialSupplierDO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderdetail.ErpProdOrderDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysistotal.MaterialAnalysisTotalMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialbom.MaterialBomMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialsupplier.MaterialSupplierMapper;
import com.mongoso.mgs.module.produce.service.materialbom.MaterialBomService;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.common.enums.FileTableEnum.MATERIAL_PICTURE;
import static com.mongoso.mgs.framework.common.exception.enums.GlobalErrorCodeConstants.SQL_UPDATE;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.*;


/**
 * 物料 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ERPMaterialServiceImpl implements ERPMaterialService {

    @Resource
    private ERPMaterialMapper erpMaterialMapper;
    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;
    @Resource
    private HzMaterialMapper hzMaterialMapper;
    @Resource
    private SeqService seqService;
    @Resource
    private FileService fileService;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    @Lazy
    private SpuMapper spuMapper;
    @Resource
    @Lazy
    private ErpMaterialApproveHandler erpMaterialApproveHandler;
    @Resource
    private MaterialAnalysisTotalMapper materialAnalysisTotalMapper;
    @Resource
    private ErpProdOrderDetailMapper erpProdOrderDetailMapper;

    @Resource
    private MaterialSupplierMapper materialSupplierMapper;

    @Resource
    private MaterialSamplingStrategyMapper materialSamplingStrategyMapper;

    @Resource
    @Lazy
    private MaterialBomService materialBomService;

    @Resource
    private ErpMaterialFlowCallbackHandler erpMaterialFlowCallbackHandler;

    @Resource
    private MaterialSpecMapper materialSpecMapper;

    @Resource
    private MaterialUnitMapper materialUnitMapper;

    @Resource
    private MaterialBomMapper materialBomMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long materialAdd(ERPMaterialAditReqVO reqVO) {
        // 校验计算公式
        if (CostTypeEnum.CALCULATED_VALUE.getType().equals(reqVO.getCostType())) {
            validateCalcFormula(reqVO.getCalcFormula());
            //设置计算值
//            reqVO.setCostValue(calculateFormula(reqVO.getCalcFormula(), BigDecimal.ONE));
        }
        // 生成物料编码
        String materialCode = seqService.getGenerateCode(reqVO.getMaterialCode(), MenuEnum.PRODUCT_MATERIAL_MANAGEMENT_11063.menuId);
        Long materialId = IDUtilX.getId();
        // 插入
        ERPMaterialDO material = BeanUtilX.copy(reqVO, ERPMaterialDO::new);
        material.setMaterialCode(materialCode);
        material.setMaterialId(materialId);

        if (CollUtilX.isNotEmpty(reqVO.getSpecAttributeList())) {
            material.setSpecAttributeStr(jsonObjectListToStr(reqVO.getSpecAttributeList()));
        }

        material.setDataStatus(0);
        material.setPublishStatus(0);
        material.setSpuId(null);
        material.setSpuIdOld(null);
        erpMaterialMapper.insert(material);

        HzMaterialDO hzMaterialDO = BeanUtilX.copy(material, HzMaterialDO::new);
        hzMaterialDO.setId(material.getMaterialId());
        hzMaterialDO.setSpecSetting(material.getSpecModel());
        hzMaterialDO.setDictMaterialTypeId(1L);  //暂时默认为1, 待生产重构
        hzMaterialDO.setDictMaterialTypeName(reqVO.getMaterialCategoryDictName());
        hzMaterialDO.setMainUnit(reqVO.getMainUnitDictName());
        hzMaterialDO.setMattr((short) 0);
        hzMaterialDO.setIsBom((short) 0);
        hzMaterialDO.setIsEnable((short) 1);
        hzMaterialMapper.insert(hzMaterialDO);

        // 保存物料抽样策略
        if (CollUtilX.isNotEmpty(reqVO.getSamplingStrategy())) {
            List<MaterialSamplingStrategyDO> samplingStrategyList = new ArrayList<>();
            for (MaterialSamplingStrategyBaseVO samplingStrategy : reqVO.getSamplingStrategy()) {
                samplingStrategy.setMaterialFkId(material.getMaterialId());
                samplingStrategy.setStrategyId(null);
                samplingStrategyList.add(BeanUtilX.copy(samplingStrategy, MaterialSamplingStrategyDO::new));
            }
            materialSamplingStrategyMapper.insertBatch(samplingStrategyList);
        }

        //保存附件
        if (CollUtilX.isNotEmpty(reqVO.getMaterialPictureList())) {
            List<String> fileIdList = reqVO.getMaterialPictureList().stream().map(FileLogRespVO::getFileId).collect(Collectors.toList());
            String objId = material.getMaterialId().toString();
            fileService.bind(fileIdList, objId, FileTableEnum.MATERIAL_PICTURE.getTableName(), FileTableEnum.MATERIAL_PICTURE.getFieldName());
        }

        // 物料供应商中间表
        materialSupplierMapper.deleteByMaterialId(reqVO.getMaterialId());
        if (CollUtilX.isNotEmpty(reqVO.getMaterialSupplierList())) {
            Set<Long> set = new HashSet<>();
            List<MaterialSupplierDO> materialSupplierDOList = new ArrayList<>();
            for (MaterialSupplierBO item : reqVO.getMaterialSupplierList()) {
                if (set.add(item.getSupplierId())) {
                    MaterialSupplierDO materialSupplierDO = new MaterialSupplierDO();
                    materialSupplierDO.setMaterialId(material.getMaterialId());
                    materialSupplierDO.setMaterialCode(material.getMaterialCode());
                    materialSupplierDO.setSupplierId(item.getSupplierId());
                    materialSupplierDO.setSupplierCode(item.getSupplierCode());
                    materialSupplierDO.setRowNo(item.getRowNo());
                    materialSupplierDOList.add(materialSupplierDO);
                }
            }
            materialSupplierMapper.insertBatch(materialSupplierDOList);
        }

        //保存规格属性
        if (CollUtilX.isNotEmpty(reqVO.getSpecAttributeList())) {
            List<MaterialSpecDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getSpecAttributeList().size(); i++) {
                MaterialSpecRespVO item = reqVO.getSpecAttributeList().get(i);
                item.setRowNo(i+1);
                item.setRelatedOrderId(material.getMaterialId());
                item.setRelatedOrderCode(material.getMaterialCode());
                item.setMaterialCode(material.getMaterialCode());
                item.setMaterialSpecId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialSpecDO::new));
            }
            materialSpecMapper.insertBatch(materialSpec);
        }

        //保存物料单位
        if (CollUtilX.isNotEmpty(reqVO.getUnitList())) {
            List<MaterialUnitDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getUnitList().size(); i++) {
                MaterialUnitRespVO item = reqVO.getUnitList().get(i);
                item.setRowNo(i+1);
                item.setRelatedOrderId(material.getMaterialId());
                item.setRelatedOrderCode(material.getMaterialCode());
                item.setMaterialUnitId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialUnitDO::new));
            }
            materialUnitMapper.insertBatch(materialSpec);
        }

        // 返回
        return material.getMaterialId();
    }

    public static List<String> getFileIdList(List<FileLogReqVO> fileLogReqVOS) {
        return fileLogReqVOS.stream().map(FileLogReqVO::getFileId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long materialEdit(ERPMaterialAditReqVO reqVO) {
        // 校验计算公式
        if (CostTypeEnum.CALCULATED_VALUE.getType().equals(reqVO.getCostType())) {
            validateCalcFormula(reqVO.getCalcFormula());
            //设置计算值
            String costValue = calculateFormulaByMaterialCode(reqVO.getCalcFormula(), reqVO.getMaterialCode());
            reqVO.setCostValue(new BigDecimal(costValue));
        }else {
            reqVO.setCalcFormula(null);
        }
        // 校验存在
        ERPMaterialDO oldDO = this.materialValidateExists(reqVO.getMaterialId());
//        this.materialValidateExists(reqVO.getMaterialId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.materialValidateExists(reqVO.getMaterialId()), reqVO);

        // 更新
        ERPMaterialDO material = BeanUtilX.copy(reqVO, ERPMaterialDO::new);
        if (reqVO.getSpecAttributeList() != null || reqVO.getSpecAttributeList().isEmpty()) {
            material.setSpecAttributeStr(jsonObjectListToStr(reqVO.getSpecAttributeList()));
        }
        erpMaterialMapper.updateById(material);
        HzMaterialDO hzMaterialDO = BeanUtilX.copy(material, HzMaterialDO::new);
        hzMaterialDO.setId(material.getMaterialId());
        hzMaterialDO.setSpecSetting(material.getSpecModel());
        hzMaterialDO.setDictMaterialTypeId(1L); //暂时默认为1, 待生产重构
        hzMaterialDO.setDictMaterialTypeName(reqVO.getMaterialCategoryDictName());
        hzMaterialDO.setMainUnit(reqVO.getMainUnitDictName());
        hzMaterialMapper.updateById(hzMaterialDO);

        // 保存物料抽样策略
        materialSamplingStrategyMapper.batchDelete(material.getMaterialId());
        if (CollUtilX.isNotEmpty(reqVO.getSamplingStrategy())) {
            List<MaterialSamplingStrategyDO> samplingStrategyList = new ArrayList<>();
            for (MaterialSamplingStrategyBaseVO samplingStrategy : reqVO.getSamplingStrategy()) {
                samplingStrategy.setMaterialFkId(material.getMaterialId());
                samplingStrategy.setStrategyId(null);
                samplingStrategyList.add(BeanUtilX.copy(samplingStrategy, MaterialSamplingStrategyDO::new));
            }
            materialSamplingStrategyMapper.insertBatch(samplingStrategyList);
        }

        //保存附件
        if (CollUtilX.isNotEmpty(reqVO.getMaterialPictureList())) {
            List<String> fileIdList = reqVO.getMaterialPictureList().stream().map(FileLogRespVO::getFileId).collect(Collectors.toList());
            String objId = material.getMaterialId().toString();
            fileService.bind(fileIdList, objId, FileTableEnum.MATERIAL_PICTURE.getTableName(), FileTableEnum.MATERIAL_PICTURE.getFieldName());
        }

        materialSupplierMapper.deleteByMaterialId(reqVO.getMaterialId());
        // 物料供应商中间表
        if (CollUtilX.isNotEmpty(reqVO.getMaterialSupplierList())) {
            Set<Long> set = new HashSet<>();
            List<MaterialSupplierDO> materialSupplierDOList = new ArrayList<>();
            for (MaterialSupplierBO item : reqVO.getMaterialSupplierList()) {
                if (set.add(item.getSupplierId())) {
                    MaterialSupplierDO materialSupplierDO = new MaterialSupplierDO();
                    materialSupplierDO.setMaterialId(oldDO.getMaterialId());
                    materialSupplierDO.setMaterialCode(oldDO.getMaterialCode());
                    materialSupplierDO.setSupplierId(item.getSupplierId());
                    materialSupplierDO.setSupplierCode(item.getSupplierCode());
                    materialSupplierDO.setRowNo(item.getRowNo());
                    materialSupplierDOList.add(materialSupplierDO);
                }
            }
            materialSupplierMapper.insertBatch(materialSupplierDOList);
        }

        //保存规格属性
        if (CollUtilX.isNotEmpty(reqVO.getSpecAttributeList())) {
            materialSpecMapper.batchDelete(material.getMaterialId());
            List<MaterialSpecDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getSpecAttributeList().size(); i++) {
                MaterialSpecRespVO item = reqVO.getSpecAttributeList().get(i);
                item.setRowNo(i+1);
                item.setRelatedOrderId(material.getMaterialId());
                item.setRelatedOrderCode(material.getMaterialCode());
                item.setMaterialCode(material.getMaterialCode());
                item.setMaterialSpecId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialSpecDO::new));
            }
            materialSpecMapper.insertBatch(materialSpec);
        }

        //保存物料单位
        if (CollUtilX.isNotEmpty(reqVO.getUnitList())) {
            materialUnitMapper.batchDelete(material.getMaterialId());
            List<MaterialUnitDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getUnitList().size(); i++) {
                MaterialUnitRespVO item = reqVO.getUnitList().get(i);
                item.setRowNo(i+1);
                item.setRelatedOrderId(material.getMaterialId());
                item.setRelatedOrderCode(material.getMaterialCode());
                item.setMaterialUnitId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialUnitDO::new));
            }
            materialUnitMapper.insertBatch(materialSpec);
        }

        // 返回
        return material.getMaterialId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialDel(Long materialId) {
        // 校验存在
        ERPMaterialDO erpMaterialDO = this.materialValidateExists(materialId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(erpMaterialDO.getDataStatus())) {
            throw exception(NOT_DELETE_NO_APPROVAL);
        }
        //查询物料是否已存在库存
        BigDecimal materialStockQty = erpMaterialStockMapper.queryMaterialStockQty(materialId);
        if (materialStockQty != null && materialStockQty.compareTo(BigDecimal.ZERO) > 0) {
            throw exception(NOT_DELETE_HAVE_STOCK);
        }
        erpMaterialMapper.deleteById(materialId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> materialDelBatch(IdReq reqVO) {
        if (CollUtilX.isEmpty(reqVO.getIdList())) {
            throw new BizException("500", "主键ID不允许为空");
        }
        //查询物料是否已存在库存
        ArrayList<Long> haveStockMaterialIdList = new ArrayList<>();
        for (int i = 0; i < reqVO.getIdList().size(); i++) {
            Long materialId = reqVO.getIdList().get(i);
            BigDecimal materialStockQty = erpMaterialStockMapper.queryMaterialStockQty(materialId);
            if (materialStockQty != null && materialStockQty.compareTo(BigDecimal.ZERO) > 0) {
                haveStockMaterialIdList.add(materialId);
                reqVO.getIdList().remove(i);
            }
        }
        ERPMaterialQueryReqVO materialQueryReqVO = new ERPMaterialQueryReqVO();
        materialQueryReqVO.setMaterialIdList(haveStockMaterialIdList);
        List<ERPMaterialDO> materialDOList = erpMaterialMapper.selectList(materialQueryReqVO);
        Map<Long, String> materialCodeMap = materialDOList.stream().collect(Collectors.toMap(ERPMaterialDO::getMaterialId, ERPMaterialDO::getMaterialCode));
        //获取对象属性名
        String materialId = EntityUtilX.getPropertyName(ERPMaterialDO::getMaterialId);
        String materialCode = EntityUtilX.getPropertyName(ERPMaterialDO::getMaterialCode);
        BatchResult batchResult = new BatchResult();
        ResultX<BatchResult> resultResultX = new ResultX<>();
        List<FailItem> failItemList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(reqVO.getIdList())) {
            resultResultX = erpBaseService.batchDelete(reqVO.getIdList(), ERPMaterialDO.class, null, materialId, materialCode);
        } else {
            batchResult.setTotalCount(0);
            batchResult.setFailCount(0);
            batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());
            batchResult.setFailItem(failItemList);
            resultResultX = ResultX.success(batchResult);
        }
        BatchResult data = resultResultX.getData();
        data.setFailCount(data.getFailCount() + haveStockMaterialIdList.size());
        data.setTotalCount(data.getTotalCount() + haveStockMaterialIdList.size());
        List<FailItem> failItem = data.getFailItem();
        for (Long id : haveStockMaterialIdList) {
            FailItem item = new FailItem();
            item.setCode(materialCodeMap.get(id));
            item.setReason(NOT_DELETE_HAVE_STOCK.getMsg());
            failItem.add(item);
        }
        return resultResultX;
    }

    private ERPMaterialDO materialValidateExists(Long materialId) {
        ERPMaterialDO material = erpMaterialMapper.selectById(materialId);
        if (material == null) {
            // throw exception(MATERIAL_NOT_EXISTS);
            throw new BizException("5001", "物料不存在");
        }
        return material;
    }

    @Override
    public ERPMaterialRespVO materialDetail(Long materialId) {
        ERPMaterialDO material = erpMaterialMapper.selectById(materialId);
        ERPMaterialRespVO materialRespVO = BeanUtilX.copy(material, ERPMaterialRespVO::new);
        if (material == null) {
            return materialRespVO;
        }
        BigDecimal materialStockQty = erpMaterialStockMapper.queryMaterialStockQty(materialId);
        if (materialStockQty != null && materialStockQty.compareTo(BigDecimal.ZERO) > 0) {
            materialRespVO.setIsChangeMainUnit(0);
        } else {
            materialRespVO.setIsChangeMainUnit(1);
        }
        this.batchFillVoProperties(Collections.singletonList(materialRespVO));

        // 查询关联的供应商
        List<MaterialSupplierDO> materialSupplierDOList = materialSupplierMapper.selectListByMaterialId(materialId);
        if (CollUtilX.isNotEmpty(materialSupplierDOList)) {
            List<Long> supplierIds = materialSupplierDOList.stream().map(MaterialSupplierDO::getSupplierId).collect(Collectors.toList());
            List<ERPSupplierBO> supplierList = erpBaseService.getERPSupplierByIdList(supplierIds);

            // 创建供应商ID到行号的映射，处理可能为null的行号
            Map<Long, Integer> supplierRowNoMap = materialSupplierDOList.stream()
                    .collect(Collectors.toMap(
                        MaterialSupplierDO::getSupplierId,
                        supplier -> supplier.getRowNo() != null ? supplier.getRowNo() : 0,
                        (v1, v2) -> v1));

            // 设置行号
            for (ERPSupplierBO supplier : supplierList) {
                Integer rowNo = supplierRowNoMap.get(supplier.getSupplierId());
                supplier.setRowNo(rowNo != null ? rowNo : 0);
            }

            // 根据行号排序
            supplierList.sort(Comparator.comparing(ERPSupplierBO::getRowNo, Comparator.nullsLast(Comparator.naturalOrder())));
            materialRespVO.setMaterialSupplierList(supplierList);
        } else {
            materialRespVO.setMaterialSupplierList(Collections.EMPTY_LIST);
        }

        //查询抽检策略
        MaterialSamplingStrategyQueryReqVO samplingStrategyQueryReqVO = new MaterialSamplingStrategyQueryReqVO();
        samplingStrategyQueryReqVO.setMaterialFkId(materialId);
        List<MaterialSamplingStrategyDO> strategyList = materialSamplingStrategyMapper.selectList(samplingStrategyQueryReqVO);
        // 根据行号排序
        strategyList.sort(Comparator.comparing(MaterialSamplingStrategyDO::getRowNo, Comparator.nullsLast(Comparator.naturalOrder())));
        materialRespVO.setSamplingStrategy(BeanUtilX.copy(strategyList, MaterialSamplingStrategyBaseVO::new));

        //查询物料规格
        MaterialSpecQueryReqVO materialSpecQuery = new MaterialSpecQueryReqVO();
        materialSpecQuery.setRelatedOrderId(materialId);
        List<MaterialSpecDO> materialSpecList = materialSpecMapper.selectList(materialSpecQuery);
        materialRespVO.setSpecAttributeList(BeanUtilX.copy(materialSpecList, MaterialSpecRespVO::new));

        //查询物料单位
        MaterialUnitQueryReqVO materialUnitQuery = new MaterialUnitQueryReqVO();
        materialUnitQuery.setRelatedOrderId(materialId);
        List<MaterialUnitDO> materialUnitList = materialUnitMapper.selectList(materialUnitQuery);
        materialRespVO.setUnitList(BeanUtilX.copy(materialUnitList, MaterialUnitRespVO::new));

        //获取附件
        materialRespVO.setMaterialPictureList(fileService.listByObjId(materialId.toString(), FileTableEnum.MATERIAL_PICTURE.getTableName(), FileTableEnum.MATERIAL_PICTURE.getFieldName()));
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(materialId.toString())).ifPresent(approveTask -> materialRespVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return materialRespVO;
    }

    @Override
    public ERPMaterialRespVO materialBaseInfo(Long materialId) {
        ERPMaterialDO materialDO = materialValidateExists(materialId);
        if (!materialDO.getDataStatus().equals(DataStatusEnum.APPROVED.key)) {
            return new ERPMaterialRespVO();
        }

        ERPMaterialRespVO materialResp = BeanUtilX.copy(materialDO, ERPMaterialRespVO::new);
        //查询字典数据
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MATERIAL_TYPE.getDictCode(),
                CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(), SystemDictEnum.MATERIAL_SOURCE.getDictCode(),
                CustomerDictEnum.BRAND.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.SKU_PUBLISH_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        // 物料信息填充
        materiaFillVoProperties(materialResp, dictMap);

        return materialResp;
    }

    @Override
    public List<ERPMaterialRespVO> materialList(ERPMaterialQueryReqVO reqVO) {
        //销售模块查询
        if (StrUtilX.isEmpty(reqVO.getModule()) || reqVO.getModule().equals("sale")) {
            reqVO.setIsProduct(1);
        } else if (reqVO.getModule().equals("produce")) {
            //生产模块查询
            reqVO.setIsProduct(null);
        }
        List<ERPMaterialDO> materialDOS = erpMaterialMapper.selectList(reqVO);
        List<ERPMaterialRespVO> materialRespVOS = BeanUtilX.copy(materialDOS, ERPMaterialRespVO::new);
        this.batchFillVoProperties(materialRespVOS);
        return materialRespVOS;
    }

    @Override
    public List<Long> findMaterialIdList(ERPMaterialQueryReqVO reqVO) {
        reqVO.setDataStatus(DataStatusEnum.APPROVED.key);
        List<ERPMaterialDO> erpMaterialDOList = erpMaterialMapper.selectList(reqVO);
        return erpMaterialDOList.stream().map(ERPMaterialDO::getMaterialId).collect(Collectors.toList());
    }

    @Override
    public PageResult<ERPMaterialRespVO> materialPage(ERPMaterialPageReqVO reqVO) {
        //销售模块查询
        if (reqVO.getModule() != null) {
            if (reqVO.getModule().equals("sale")) {
                reqVO.setIsProduct(1);
            } else if (reqVO.getModule().equals("produce")) {
                //生产模块查询
                reqVO.setIsProduct(null);
            }
        }

        PageResult<ERPMaterialDO> selectPage = erpMaterialMapper.selectPage(reqVO);
        PageResult<ERPMaterialRespVO> pageResult = BeanUtilX.copy(selectPage, ERPMaterialRespVO::new);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    @Override
    public PageResult<ERPMaterialRespVO> supplierErpMaterialQuotedPage(ERPMaterialPageReqVO reqVO) {
        if (reqVO.getSupplierId() != null) {
            // 如果查供应商的物料
            List<MaterialSupplierDO> materialSupplierDOList = materialSupplierMapper.selectListBySupplierId(reqVO.getSupplierId());
            if (CollUtilX.isNotEmpty(materialSupplierDOList)) {
                List<Long> materialIdList = materialSupplierDOList.stream().map(MaterialSupplierDO::getMaterialId).collect(Collectors.toList());
                reqVO.setMaterialIdList(materialIdList);
            }
            //如果查询供应商物料为空，则直接返回空
            if (CollUtilX.isEmpty(reqVO.getMaterialIdList())) {
                PageResult<ERPMaterialRespVO> pageResult = new PageResult<>();
                pageResult.setList(new ArrayList<>());
                return pageResult;
            }
        }
        PageResult<ERPMaterialDO> selectPage = erpMaterialMapper.selectPage(reqVO);
        PageResult<ERPMaterialRespVO> pageResult = BeanUtilX.copy(selectPage, ERPMaterialRespVO::new);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    @Override
    public PageResult<ErpMaterialQuotedRespVO> materialQuotedPage(ErpMaterialQuotedPageReqVO reqVO) {
        IPage<ErpMaterialQuotedRespVO> respVOIPage = erpMaterialMapper.queryMaterialQuotedPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<ErpMaterialQuotedRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        if (CollUtilX.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        // 属性填充
        List<ErpMaterialQuotedRespVO> respVOList = pageResult.getList();
        for (ErpMaterialQuotedRespVO respVO : respVOList) {
            fillVoProperties(respVO);
        }

        return pageResult;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishStatusChange(ERPMaterialQueryReqVO reqVO) {
        Short operationType = reqVO.getOpType();
        switch (operationType) {
            case 1://上架（前提：已审核）-》已上架
                reqVO.setPublishStatus(1);
                break;
            case 0://下架（前提：已审核）-》未上架
                reqVO.setPublishStatus(0);
                break;
        }
        //校验物料状态
        ArrayList<ERPMaterialBaseVO> list = new ArrayList<>();
        for (ERPMaterialBaseVO materialBaseVO : reqVO.getMaterialList()) {
            materialBaseVO.setPublishStatus(reqVO.getPublishStatus());
            ERPMaterialDO materialDO = this.materialValidateExists(materialBaseVO.getMaterialId());
            if (!Objects.equals(materialDO.getDataStatus(), DataStatusEnum.APPROVED.key)) {
                throw exception(MATERIAL_NOT_APPROVAL);
            }
            if (operationType == 1 && Objects.equals(materialDO.getPublishStatus(), PublishEnum.NOT_PUBLISH.key)) {
                list.add(materialBaseVO);
            }
            if (operationType == 0 && Objects.equals(materialDO.getPublishStatus(), PublishEnum.PUBLISH.key)) {
                list.add(materialBaseVO);
            }
        }
        if (list.isEmpty()) {
            return null;
        }
        reqVO.setMaterialList(list);

        erpMaterialMapper.materialPublishChangeBatch(BeanUtilX.copy(reqVO.getMaterialList(), ERPMaterialDO::new));
        List<SpuDO> publishStatusList = erpMaterialMapper.getSpuListPublishStatus(reqVO.getMaterialList());
        //批量更新spu状态
        if (!publishStatusList.isEmpty()) {
            spuMapper.updateBatch(publishStatusList);
//            spuMapper.updateBatchSpuPublishStatus(publishStatusList);
        }
        return 200L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishStatusChange2(ERPMaterialQueryReqVO reqVO) {
        Short operationType = reqVO.getOpType();
        switch (operationType) {
            case 1://上架（前提：已审核）-》已上架
                reqVO.setPublishStatus(1);
                break;
            case 0://下架（前提：已审核）-》未上架
                reqVO.setPublishStatus(0);
                break;
        }
        //校验物料状态
        ArrayList<ERPMaterialBaseVO> list = new ArrayList<>();
        for (ERPMaterialBaseVO materialBaseVO : reqVO.getMaterialList()) {
            materialBaseVO.setPublishStatus(reqVO.getPublishStatus());
            ERPMaterialDO materialDO = erpMaterialMapper.selectById(materialBaseVO.getMaterialId());
            if (materialDO == null) continue;
            if (operationType == 1 && Objects.equals(materialDO.getPublishStatus(), PublishEnum.NOT_PUBLISH.key)) {
                list.add(materialBaseVO);
            }
            if (operationType == 0 && Objects.equals(materialDO.getPublishStatus(), PublishEnum.PUBLISH.key)) {
                list.add(materialBaseVO);
            }
            list.add(materialBaseVO);
        }
        if (list.isEmpty()) {
            return null;
        }
        reqVO.setMaterialList(list);

//        erpMaterialMapper.materialStatusChange(BeanUtilX.copy(reqVO.getMaterialList(), ERPMaterialDO::new));
//        List<SpuDO> publishStatusList = erpMaterialMapper.getSpuPublishStatus(reqVO.getMaterialList());
        //批量更新spu状态
//        if (!publishStatusList.isEmpty()){
//            spuMapper.updateBatch(publishStatusList);
////            spuMapper.updateBatchSpuPublishStatus(publishStatusList);
//        }
        return 200L;
    }

    @Override
    public Map<Long, ERPMaterialRespVO> selectERPMaterialMap(ERPMaterialQueryReqVO reqVO) {
        List<ERPMaterialDO> erpMaterialDOList = erpMaterialMapper.selectList(reqVO);

        List<ERPMaterialRespVO> erpMaterialRespVOS = BeanUtilX.copyList(erpMaterialDOList, ERPMaterialRespVO::new);

        //查询字典数据
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MATERIAL_TYPE.getDictCode(),
                CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(), SystemDictEnum.MATERIAL_SOURCE.getDictCode(),
                SystemDictEnum.SPU_PUBLISH_STATUS.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode(),
                CustomerDictEnum.BRAND.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询字典库信息
        for (ERPMaterialRespVO erpMaterialRespVO : erpMaterialRespVOS) {
            // 物料信息属性填充
            materiaFillVoProperties(erpMaterialRespVO, dictMap);
        }
        return erpMaterialRespVOS.stream().collect(Collectors.toMap(ERPMaterialRespVO::getMaterialId, material -> material));
    }

//    @Override
//    public List<ApproveResult> materialApprove(FlowApprove reqVO) {
//        //改为ids查询出列表然后遍历
//        LocalDateTime now = LocalDateTime.now();
//        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//        String flowFunctionCode = reqVO.getFlowFunctionCode();
//
//        // 第一步，业务前缀处理
//        // 1.1 查询业务数据
//        List<ERPMaterialDO> list = erpMaterialMapper.selectBatchIds(reqVO.getIdList());
//        if (CollUtilX.isEmpty(list)) {
//            return Collections.emptyList();
//        }
//
//        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
//        for (ERPMaterialDO item : list) {
//
//            // 1.2 通用校验
//            ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
//
//            // 1.3 业务校验
//        }
//
//        // 第二步，流程查询
//        // 2.1 查询流程模版
//        FlowConfigBO flowConfigBO = null;
//        FlowTemplateDO flowTemplateDO = approveService.getFlowTemplate(flowFunctionCode);
//        if (flowTemplateDO != null) {
//            // 2.2 查询流程配置
//            flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);
//        }
//
//        // 第三步，业务处理
//        // 3.1 处理主表
//        // 3.2 处理子表
//        List<ERPMaterialDO> updateList = new ArrayList<>();
//        for (ERPMaterialDO item : list) {
//            item.setMaterialId(item.getMaterialId());
//            item.setReviewer(loginUser.getFullUserName());
//            item.setReviewDt(now);
//            if (flowTemplateDO == null) {
//                // 不走流程，直接结束状态
//                Integer dataStatus = ApproveUtilX.getDataStatusByApproveSuccess(flowFunctionCode);
//                item.setDataStatus(dataStatus);
//                if (Objects.equals(buttonType, DataButtonEnum.APPROVE.key)){
//                    item.setPublishStatus(PublishEnum.PUBLISH.getKey());
//                }
//                if (Objects.equals(buttonType, DataButtonEnum.NOT_APPROVE.key)){
//                    item.setPublishStatus(PublishEnum.NOT_PUBLISH.getKey());
//                }
//
//            }else {
//                // 走流程，中间状态
//                Integer dataStatus = ApproveUtilX.getDataStatusByApproveStart(flowFunctionCode);
//                item.setDataStatus(dataStatus);
//            }
//
//            updateList.add(item);
//        }
//
//        ERPMaterialQueryReqVO queryReqVO = new ERPMaterialQueryReqVO();
//        if (Objects.equals(buttonType, DataButtonEnum.APPROVE.key)){
//            queryReqVO.setOpType((short)1);
//            queryReqVO.setMaterialList(BeanUtilX.copy(list, ERPMaterialBaseVO::new));
//            this.publishStatusChange2(queryReqVO);
//        }
//        if (Objects.equals(buttonType, DataButtonEnum.NOT_APPROVE.key)){
//            queryReqVO.setOpType((short)0);
//            queryReqVO.setMaterialList(BeanUtilX.copy(list, ERPMaterialBaseVO::new));
//            this.publishStatusChange2(queryReqVO);
//        }
//
//        erpMaterialMapper.updateBatch(updateList);
//
//        List<ApproveResult> approveResults = new ArrayList<>();
//        // 第四步，发起审批任务
//        if (flowTemplateDO != null) {
//            // 4.1 封装条件对象
//            List<FlowBizBO> flowBizBOS = new ArrayList<>();
//            for (ERPMaterialDO item : list) {
//                Map<String, Object> conditionMap = ReflectUtilX.toMapByExcluded(item, null);

    /// /                JSONObject baseInfo = item.getBaseInfo();
    /// /                JSONObject financeInfo = item.getFinanceInfo();
    /// /                baseInfo.putAll(financeInfo);
    /// /                makeConditionMap(conditionMap, baseInfo);
    /// /                List<JSONObject> contactsInfo = item.getContactsInfo();
//                FlowBizBO flowBizBO = new FlowBizBO();
//                flowBizBO.setObjId(item.getMaterialId() + "");
//                flowBizBO.setConditionMap(conditionMap);
//                flowBizBOS.add(flowBizBO);
//            }
//            // 4.2 调用预申请方法
//            approveResults = approveService.preApproveTaskAdd(flowConfigBO, flowBizBOS);
//
//            // 4.3 批量发起审批
//            int i = approveService.approveTaskAdd(flowConfigBO, approveResults);
//        }
//
//        return ApproveUtilX.result(approveResults);
//    }
    @Override
    public BatchResult erpMaterialApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ERPMaterialDO> list = erpMaterialMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ERPMaterialDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();


                //流程处理
                FailItem failItem = erpMaterialApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getMaterialCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }
        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()) {
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ERPMaterialDO item : list) {
                String reason = reasonMap.get(item.getMaterialCode());
                if (StrUtilX.isEmpty(reason)) {
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getMaterialId());
                    messageInfoBO.setObjCode(item.getMaterialCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                } else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getMaterialId());
                    messageInfoBO.setObjCode(item.getMaterialCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(), item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object materialCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        ERPMaterialDO item = this.materialValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return erpMaterialFlowCallbackHandler.handleFlowCallback(item,flowCallbackBO);


    }

    @Override
    public PageResult<ERPMaterialRespVO> erpMaterialPage4PurchaseChange(ERPMaterialPageReqVO reqVO) {
        HashMap<Long, BigDecimal> purchaseAbleMap = new HashMap<>();
        if (reqVO.getBizType() == PurchaseBizTypeEnum.PROD_OUT_PURCHASE.getType().intValue()) {
            ErpProdOrderDetailQueryReqVO queryReqVO = BeanUtilX.copy(reqVO, ErpProdOrderDetailQueryReqVO::new);
            queryReqVO.setProdOrderId(reqVO.getRelatedOrderId());
            List<ErpProdOrderDetailDO> prodOrderDetailDOList = erpProdOrderDetailMapper.queryList(queryReqVO);
//            List<Long> materialIdList = prodOrderDetailDOList.stream().map(ErpProdOrderDetailDO::getMaterialId).collect(Collectors.toList());
            List<Long> materialIdList = new ArrayList<>();
            for (ErpProdOrderDetailDO prodOrderDetailDO : prodOrderDetailDOList) {
                materialIdList.add(prodOrderDetailDO.getMaterialId());
                purchaseAbleMap.put(prodOrderDetailDO.getMaterialId(), prodOrderDetailDO.getPurchaseableQty().add(prodOrderDetailDO.getPurchasedQty()));
            }
            if (CollUtilX.isEmpty(materialIdList)) {
                materialIdList.add(IDUtilX.getId());
            }
            reqVO.setMaterialIdList(materialIdList);
        } else if (reqVO.getBizType() == PurchaseBizTypeEnum.MATERIAL_OUT_PURCHASE.getType().intValue()) {
            MaterialAnalysisTotalQueryReqVO queryReqVO = BeanUtilX.copy(reqVO, MaterialAnalysisTotalQueryReqVO::new);
            queryReqVO.setMaterialAnalysisId(reqVO.getRelatedOrderId());
            List<MaterialAnalysisTotalDO> totalDOS = materialAnalysisTotalMapper.selectByMaterialAnalysisIdAndType(queryReqVO);
            List<Long> materialIdList = new ArrayList<>();
            for (MaterialAnalysisTotalDO analysisTotalDO : totalDOS) {
                materialIdList.add(analysisTotalDO.getMaterialId());
                purchaseAbleMap.put(analysisTotalDO.getMaterialId(), analysisTotalDO.getPurchaseableQty().add(analysisTotalDO.getPurchasedQty()));
            }
            if (CollUtilX.isEmpty(materialIdList)) {
                materialIdList.add(IDUtilX.getId());
            }
            reqVO.setMaterialIdList(materialIdList);
        }
        PageResult<ERPMaterialRespVO> respVOPageResult = this.materialPage(reqVO);
        List<ERPMaterialRespVO> resultList = respVOPageResult.getList();
        for (ERPMaterialRespVO materialRespVO : resultList) {
            materialRespVO.setPurchaseableQty(purchaseAbleMap.get(materialRespVO.getMaterialId()));
        }
        return respVOPageResult;
    }

    @Override
    public Map<Long, String> nameMap(Set<Long> materialIds) {
        List<ERPMaterialDO> erpMaterialDOS = erpMaterialMapper.selectBatchIds(materialIds);
        return erpMaterialDOS.stream().collect(Collectors.toMap(ERPMaterialDO::getMaterialId, ERPMaterialDO::getMaterialName));
    }

    @Override
    public ERPMaterialDO detailDO(Long materialId) {
        return erpMaterialMapper.selectById(materialId);
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private ERPMaterialRespVO fillVoProperties(ERPMaterialRespVO respVO) {
        List<ERPMaterialRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
        return respVOList.get(0);
    }

    private void batchFillVoProperties(List<ERPMaterialRespVO> materialList) {
        //查询字典数据
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MATERIAL_TYPE.getDictCode(),
                CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(), SystemDictEnum.MATERIAL_SOURCE.getDictCode(),
                CustomerDictEnum.BRAND.getDictCode(), CustomerDictEnum.MAIN_UNIT.getDictCode(),
                SystemDictEnum.SKU_PUBLISH_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();

        if (CollUtilX.isEmpty(materialList)) {
            return;
        }

        for (ERPMaterialRespVO item : materialList) {
            empIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());
        }
        //查询负责人
        Map<Long, String> empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        for (ERPMaterialRespVO material : materialList) {
            // 物料信息填充
            materiaFillVoProperties(material, dictMap);

            material.setDirectorName(empNameMap.get(material.getDirectorId()));
            material.setDirectorOrgName(orgNameMap.get(material.getDirectorOrgId()));

            if (CollUtilX.isNotEmpty(material.getUnitList())) {
                for (MaterialUnitRespVO jsonObject : material.getUnitList()) {
//                    Object leftUnit = jsonObject.get("leftUnit");
//                    Object rightUnit = jsonObject.get("rightUnit");

//                    if (leftUnit != null) {
//                        String leftUnitStr = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + leftUnit;
//                        jsonObject.put("leftUnitDictName", dictMap.get(leftUnitStr));
//                    }
//                    if (rightUnit != null) {
//                        String rightUnitStr = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + rightUnit;
//                        jsonObject.put("rightUnitDictName", dictMap.get(rightUnitStr));
//                    }

                    String leftUnit = jsonObject.getLeftUnit();
                    if (StrUtilX.isNotEmpty(leftUnit)) {
                        String leftUnitStr = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + leftUnit;
                        jsonObject.setLeftUnitDictName(dictMap.get(leftUnitStr));
                    }
                }
            }
        }
    }

    /**
     * 物料信息属性填充
     *
     * @param material
     * @param dictMap
     */
    private static void materiaFillVoProperties(ERPMaterialRespVO material, Map<String, String> dictMap) {
        // 物料类型
        String materialTypeDictId = material.getMaterialTypeDictId();
        if (StrUtilX.isNotEmpty(materialTypeDictId)) {
            materialTypeDictId = CustomerDictEnum.MATERIAL_TYPE.getDictCode() + "-" + materialTypeDictId;
            material.setMaterialTypeDictName(dictMap.get(materialTypeDictId));
        }

        // 物料类别
        String materialCategoryDictId = material.getMaterialCategoryDictId();
        if (StrUtilX.isNotEmpty(materialCategoryDictId)) {
            materialCategoryDictId = CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
            material.setMaterialCategoryDictName(dictMap.get(materialCategoryDictId));
        }

        // 物料来源
        Integer materialSourceDictId = material.getMaterialSourceDictId();
        if (materialSourceDictId != null) {
            String materialSourceDictIdStr = SystemDictEnum.MATERIAL_SOURCE.getDictCode() + "-" + materialSourceDictId;
            material.setMaterialSourceDictName(dictMap.get(materialSourceDictIdStr));
        }

        // 基本单位
        String mainUnitDictId = material.getMainUnitDictId();
        if (StrUtilX.isNotEmpty(mainUnitDictId)) {
            mainUnitDictId = CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
            material.setMainUnitDictName(dictMap.get(mainUnitDictId));
        }

        // 品牌
        String brandDictId = material.getBrandDictId();
        if (StrUtilX.isNotEmpty(brandDictId)) {
            brandDictId = CustomerDictEnum.BRAND.getDictCode() + "-" + brandDictId;
            material.setBrandDictName(dictMap.get(brandDictId));
        }

        // 上架状态
        Integer publishStatus = material.getPublishStatus();
        if (publishStatus != null) {
            String publishStatusStr = SystemDictEnum.SKU_PUBLISH_STATUS.getDictCode() + "-" + publishStatus;
            material.setPublishStatusDictName(dictMap.get(publishStatusStr));
        }

        //审核时间
        if (material.getDataStatus() != null) {
            String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + material.getDataStatus();
            material.setDataStatusDictName(dictMap.get(dataStatus));
        }
    }

    public static List<JSONObject> strToJSONObjectList(String str, Map<String, String> specMap) {
        String[] pairs = str.split(";");
        List<JSONObject> jsonObjectList = new ArrayList<>();

        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("specName", keyValue[0]);
            jsonObject.put("specValue", keyValue[1]);
            jsonObject.put("dictCategoryId", specMap.get(keyValue[0]));
            jsonObjectList.add(jsonObject);
        }

        return jsonObjectList;
    }

    public static String jsonObjectListToStr(List<MaterialSpecRespVO> jsonObjectList) {
        StringBuilder result = new StringBuilder();

        if (CollUtilX.isEmpty(jsonObjectList)) {
            return "";
        }

        for (int i = 0; i < jsonObjectList.size(); i++) {
            MaterialSpecRespVO jsonObject = jsonObjectList.get(i);
            jsonObject.setRowNo(i+1);
            String specValue = jsonObject.getSpecValue();
            result.append(specValue).append(";");
        }

        if (result.length() > 0) {
            result.setLength(result.length() - 1);
        }

        return result.toString();
    }

    /**
     * 校验计算公式
     *
     * @param calcFormula 计算公式
     */
    private void validateCalcFormula(String calcFormula) {
        if (StrUtilX.isEmpty(calcFormula)) {
            return;
        }

        FormulaValidationResult result = FormulaValidatorUtil.validateFormula(calcFormula);
        if (!result.isValid()) {
            throw new BizException("5001", "计算公式校验失败：" + result.getErrorMessage());
        }
    }

    /**
     * 计算公式结果
     *
     * @param calcFormula 计算公式（如：x + 1, x * 2, (x + 1) * x 等）
     * @param xValue      x变量的值
     * @return 计算结果
     */
    @Override
    public BigDecimal calculateFormula(String calcFormula, BigDecimal xValue, int type) {
        try {
            if (xValue == null) {
                xValue = BigDecimal.ZERO;
            }
            //校验计算公式如果是数字的话，则直接返回
//            if (StrUtilX.isBigDecimal(calcFormula,12,6,true)) {
//                return new BigDecimal(calcFormula).setScale(6);
//            }
            BigDecimal value = FormulaValidatorUtil.calculateFormula(calcFormula, xValue);

            // 直接去除末尾0，不进行四舍五入
            value = MathUtilX.stripTrailingZeros(value);

            if (type == 0) {//只有前端调用的时候才对这个处理
                if(BigDecimal.ZERO.compareTo(value) > 0){
                    throw new BizException("5001", "成本单价必须大于0");
                }
                //值不能大于12位正数和六位小数
                if (value.scale() > 6) {
                    throw new BizException("5001", "【成本单价计算值超出计算范围(12位整数，6位小数)，请修改公式后重新计算！】");
                }
                if (value.precision() - value.scale() > 12) {
                    throw new BizException("5001", "【成本单价计算值超出计算范围(12位整数，6位小数)，请修改公式后重新计算！】");
                }
            }

            return value;
        } catch (IllegalArgumentException e) {
            throw new BizException("5001", e.getMessage());
        }
    }

    @Override
    public String calculateFormulaByMaterialCode(String calcFormula, String materialCode) {
        MaterialBomDO materialBomDO = materialBomMapper.selectOne(new LambdaQueryWrapper<MaterialBomDO>()
                .eq(MaterialBomDO::getMaterialCode, materialCode)
                .eq(MaterialBomDO::getDataStatus, DataStatusEnum.APPROVED.key)
                .last("limit 1")
        );
        BigDecimal subTotalCost = BigDecimal.ZERO;
        if (Objects.nonNull(materialBomDO)) {
            subTotalCost = materialBomDO.getSubTotalCost();
        }

        return calculateFormula(calcFormula, subTotalCost, 0).toEngineeringString();
    }

    @Override
    public void updateMaterialCostValue(Long materialId, BigDecimal changeCostValue) {
        //获取当前节点的所有上级链表
        List<MaterialBomChainRespVO> materialBomChainRespVOS = materialBomService.materialBomChains(materialId);
        //获取list里面的所有物料编码，根据物料编码获取物料信息
        List<Long> materialIdList = materialBomChainRespVOS.stream().flatMap(o -> o.getNodes().stream()).map(MaterialBomChainRespVO.MaterialBomChainNodeVO::getMaterialId).distinct().collect(Collectors.toList());
        //获取所有物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        List<ERPMaterialDO> erpMaterialDOS = erpMaterialMapper.selectList(erpMaterialQuery);
        //根据物料id，为key，ERPMaterialDO为值，转换成map
        Map<Long, ERPMaterialDO> erpMaterialDOMap = erpMaterialDOS.stream().collect(Collectors.toMap(ERPMaterialDO::getMaterialId, obj -> obj));
        List<UpdateCostVO> list = new ArrayList<>();
        List<CostBO> listCostValue = new ArrayList<>();
        if (CollUtilX.isNotEmpty(materialBomChainRespVOS)) {
            //遍历所有链表
            Map<Long, CostBO> costValueMap = new HashMap<>();
            for (MaterialBomChainRespVO materialBomChainRespVO : materialBomChainRespVOS) {
                List<MaterialBomChainRespVO.MaterialBomChainNodeVO> nodes = materialBomChainRespVO.getNodes();
                if (CollUtilX.isEmpty(nodes)) {
                    continue;
                }

                BigDecimal demandQty = nodes.stream().filter(o -> materialId.equals(o.getMaterialId())).toList().get(0).getDemandQty();
                for (MaterialBomChainRespVO.MaterialBomChainNodeVO node : nodes) {
                    CostBO costBO = costValueMap.get(node.getMaterialBomId());
                    if (Objects.isNull(costBO)) {
                        costBO = new CostBO();
                    }
                    costBO.setMaterialId(node.getMaterialId());
//                    costBO.setMaterialBomId(node.getMaterialBomId());
                    if (node.getMaterialId().equals(materialId)) {
                        costBO.setCostValue(changeCostValue);
                        listCostValue.add(costBO);
                    }else {
                        costBO.setCostValue(node.getCostValue());
                    }
                    costBO.setDemandQty(node.getDemandQty());
                    costBO.setSubTotalCost(node.getSubTotalCost());
                    costBO.setCostTotalValue(demandQty.multiply(changeCostValue));

                    costValueMap.put(node.getMaterialBomId(), costBO);
                }
            }
            //把costValueMap组装批量修改的数据
            for (Map.Entry<Long, CostBO> entry : costValueMap.entrySet()) {
                UpdateCostVO item = new UpdateCostVO();

                item.setMaterialBomId(entry.getKey());
                item.setCostValue(entry.getValue().getCostValue());
                if (entry.getValue().getMaterialId().equals(materialId)) {
                    //当前物料自己本身
                    item.setSubTotalCost(BigDecimal.ZERO);
                    item.setCostTotalValue(entry.getValue().getCostTotalValue());
                }else {
                    //获取物料id
                    Long parentMaterialId = entry.getValue().getMaterialId();
                    //根据物料id获取物料信息
                    ERPMaterialDO erpMaterialDO = erpMaterialDOMap.get(parentMaterialId);
                    if (Objects.isNull(erpMaterialDO)) {
                        continue;
                    }
                    //父级处理
                    item.setSubTotalCost(entry.getValue().getCostTotalValue().add(entry.getValue().getSubTotalCost()));
                    if (CostTypeEnum.CALCULATED_VALUE.getType().equals(erpMaterialDO.getCostType())) {
                        //计算新单价
                        BigDecimal newCostValue = calculateFormula(erpMaterialDO.getCalcFormula(), item.getSubTotalCost(), 0);
                        //计算单价变动值
                        item.setCostValue(newCostValue.subtract(entry.getValue().getCostValue()));
                        item.setCostTotalValue(item.getCostValue().multiply(entry.getValue().getDemandQty()));
                        item.setSubTotalCost(entry.getValue().getCostTotalValue());
                    }else {
                        //不是计算公式，不处理，只增加下级总和
                        item.setCostValue(BigDecimal.ZERO);
                        item.setCostTotalValue(BigDecimal.ZERO);
                        //item.setSubTotalCost(entry.getValue().getCostTotalValue());

                    }
                }

                list.add(item);
            }
        }

        //执行修改
        if (CollUtilX.isNotEmpty(list)) {
            materialBomMapper.updateMaterialCostValue(list);
        }
        if (CollUtilX.isNotEmpty(listCostValue)) {
            erpMaterialMapper.updateMaterialCostValue(listCostValue);
        }
    }

    /**
     * 更新物料成本值 - 新实现
     * 当物料单价发生变更时，级联更新所有上级BOM的成本信息
     *
     * @param materialId 物料ID
     * @param changeCostValue 单价变更值（可以是正数或负数）
     */
    @Override
    public void updateMaterialCostValueNew(Long materialId, BigDecimal changeCostValue,  Long materialBomId) {
        //获取当前节点的所有上级链表
        List<MaterialBomChainRespVO> materialBomChainRespVOS = materialBomService.materialBomChains(materialId);
        //获取list里面的所有物料编码，根据物料编码获取物料信息
        List<Long> materialIdList = materialBomChainRespVOS.stream().flatMap(o -> o.getNodes().stream()).map(MaterialBomChainRespVO.MaterialBomChainNodeVO::getMaterialId).distinct().collect(Collectors.toList());
        //获取所有物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        List<ERPMaterialDO> erpMaterialDOS = erpMaterialMapper.selectList(erpMaterialQuery);
        //根据物料id，为key，ERPMaterialDO为值，转换成map
        Map<Long, ERPMaterialDO> erpMaterialDOMap = erpMaterialDOS.stream().collect(Collectors.toMap(ERPMaterialDO::getMaterialId, obj -> obj));
        List<UpdateCostVO> list = new ArrayList<>();
        List<CostBO> listCostValue = new ArrayList<>();
        if (CollUtilX.isNotEmpty(materialBomChainRespVOS)) {
            //遍历所有链表
            Map<Long, CostBO> costValueMap = new HashMap<>();
            Map<String, CostBO> nodeValueMap = new HashMap<>();
            for (MaterialBomChainRespVO materialBomChainRespVO : materialBomChainRespVOS) {
                List<MaterialBomChainRespVO.MaterialBomChainNodeVO> nodes = materialBomChainRespVO.getNodes();
                if (CollUtilX.isEmpty(nodes)) {
                    continue;
                }

                // BigDecimal demandQty = nodes.stream().filter(o -> materialId.equals(o.getMaterialId())).toList().get(0).getDemandQty();
                BigDecimal subTotalCost = BigDecimal.ZERO;//定义下级总和变更值
                //当前节点在最后面，所有需要倒叙处理
                //定义父级的前节点物料id
                Long sonMaterialBomId = null;
                for (int i = nodes.size(); i > 0; i --) {
                    MaterialBomChainRespVO.MaterialBomChainNodeVO node = nodes.get(i-1);

                    //特殊处理，当materialBomId不为空的时候只处理当前materialBomId的树，因为其他树已经处理过了
                    if (materialBomId != null) {
                        //只校验第一个节点
                        if (i == nodes.size()) {
                            //如果当前第一个节点的bomId和传递的bomId不匹配，则跳过当前这个链路
                            if (!materialBomId.equals(node.getMaterialBomId())){
                                changeCostValue = changeCostValue.subtract(node.getCostValue());
                            }
                        }
                    }

                    CostBO costBO = new CostBO();

                    costBO.setMaterialId(node.getMaterialId());
                    costBO.setMaterialBomId(node.getMaterialBomId());
                    costBO.setMaterialCode(node.getMaterialCode());
                    if (node.getMaterialId().equals(materialId)) {
                        //当前节点是物料本身，下级总和变更值为单价变更值*需求数量
                        subTotalCost = changeCostValue.multiply(node.getDemandQty()).setScale(6, RoundingMode.HALF_UP);
                        sonMaterialBomId = node.getMaterialBomId();
                        //判断当前节点是否已经处理过了，如果已经处理过了就不需要再次处理了，只有子节点才需要过滤，父节点不需要
                        if (costValueMap.containsKey(node.getMaterialBomId())) {
                            continue;
                        }
                        costBO.setCostValue(changeCostValue);
                        costBO.setSubTotalCost(BigDecimal.ZERO);
                        costBO.setCostTotalValue(subTotalCost);
                        listCostValue.add(costBO);
                        costValueMap.put(node.getMaterialBomId(), costBO);
                        //sonMaterialBomId = node.getMaterialBomId();
                    }else {
                        //判断是否是计算公式
                        ERPMaterialDO erpMaterialDO = erpMaterialDOMap.get(node.getMaterialId());
                        //父节点 - 检查是否已经处理过，避免重复计算
                        CostBO sonCostBO = nodeValueMap.get(sonMaterialBomId + "-" + node.getMaterialBomId());
                        if (Objects.nonNull(sonCostBO)) {
                            sonMaterialBomId = node.getMaterialBomId();
                            //判断当前节点是否是计算公式，如果不是，则上面的层级就不需要处理了
                            if (!CostTypeEnum.CALCULATED_VALUE.getType().equals(erpMaterialDO.getCostType())) {
                                break;
                            }
                            subTotalCost = sonCostBO.getCostTotalValue();
                            continue;
                        }


                        if (Objects.isNull(erpMaterialDO)) {
                            continue;
                        }
                         if (CostTypeEnum.CALCULATED_VALUE.getType().equals(erpMaterialDO.getCostType())) {
                            //计算新单价
                            BigDecimal nodeSubTotalCost = node.getSubTotalCost().add(subTotalCost);
                            BigDecimal newCostValue = calculateFormula(erpMaterialDO.getCalcFormula(), nodeSubTotalCost, 1);
                            //计算单价变动值
                            costBO.setCostValue(newCostValue.subtract(node.getCostValue()));
                            costBO.setCostTotalValue(costBO.getCostValue().multiply(node.getDemandQty()).setScale(6, RoundingMode.HALF_UP));
                            costBO.setSubTotalCost(subTotalCost);
                            //变更下级总和为当前节点的价值总和
                            subTotalCost = costBO.getCostTotalValue();
                            // 记录已处理的父级节点
                            nodeValueMap.put(sonMaterialBomId + "-" + node.getMaterialBomId(), costBO);
                            listCostValue.add(costBO);
                            sonMaterialBomId = node.getMaterialBomId();
                        }else {
                            //不是计算公式，不处理，只增加下级总和，以备后续变更为计算公式时使用
                            costBO.setCostValue(BigDecimal.ZERO);
                            costBO.setCostTotalValue(BigDecimal.ZERO);
                            costBO.setSubTotalCost(subTotalCost);
                            // 记录已处理的父级节点
                            nodeValueMap.put(sonMaterialBomId + "-" + node.getMaterialBomId(), costBO);
                            listCostValue.add(costBO);
                            sonMaterialBomId = node.getMaterialBomId();
                            //因为此时不是计算公式，所有下级总和不再往上传递
                            break;
                        }
                    }
                }
            }
            //将costValueMap转换成list
            list = listCostValue.stream().map(obj -> {
                UpdateCostVO item = new UpdateCostVO();
                item.setMaterialBomId(obj.getMaterialBomId());
                item.setCostValue(obj.getCostValue());
                item.setSubTotalCost(obj.getSubTotalCost());
                item.setCostTotalValue(obj.getCostTotalValue());
                return item;
            }).collect(Collectors.toList());
        }

        //执行修改
        if (CollUtilX.isNotEmpty(list)) {
            materialBomMapper.updateMaterialCostValue(list);

            //从list取出物料物料id，在根据物料bomid查询物料id和单价
            List<Long> materialBomIds = list.stream().map(UpdateCostVO::getMaterialBomId).collect(Collectors.toList());
            MaterialBomQueryReqVO materialBomQuery = new MaterialBomQueryReqVO();
            materialBomQuery.setMaterialBomIdList(materialBomIds);
            List<MaterialBomDO> materialBomDOS = materialBomMapper.selectListOld(materialBomQuery);
            if (CollUtilX.isNotEmpty(materialBomDOS)) {
                List<ERPMaterialDO> erpMaterialDOList = new ArrayList<>();
                for (MaterialBomDO bomDO : materialBomDOS){
                    if (bomDO.getMaterialId().equals(materialId)) {
                        continue;
                    }
                    ERPMaterialDO item = new ERPMaterialDO();
                    item.setMaterialId(bomDO.getMaterialId());
                    item.setCostValue(bomDO.getCostValue());
                    erpMaterialDOList.add(item);
                }
                if (CollUtilX.isNotEmpty(erpMaterialDOList)) {
                    erpMaterialMapper.updateBatch(erpMaterialDOList);
                }
            }

        }

    }

}
