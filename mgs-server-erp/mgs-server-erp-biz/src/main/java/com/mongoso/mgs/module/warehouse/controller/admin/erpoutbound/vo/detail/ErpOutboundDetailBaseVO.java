package com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 
/**
 * 出库单明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ErpOutboundDetailBaseVO implements Serializable {

    /** 入库单明细ID */
    private Long outboundDetailId;

    /** 出库单ID */
    private Long outboundId;

    /** 出库单号 */
    private String outboundCode;

    /** 行号 */
    private Integer rowNo;

    /** 关联单据明细ID */
    private Long relatedOrderDetailId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料库存ID */
    private Long materialStockId;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 出库数量 */
    private BigDecimal outboundQty;

    /** 已出库数量 */
    private BigDecimal outboundedQty;

    /** 已发货数量 */
    private BigDecimal deliveredQty;

    /** 可发货数量 */
    private BigDecimal deliverableQty;

    /** 是否完全发货 */
    private Integer isMaterialFullDelivered;

    /** 出库仓库ID */
    private String warehouseOrgId;

    /** 备注 */
    private String remark;

    /** 明细类型['物料明细','核对物料明细'] */
    private String detailTypeDictId;

}
