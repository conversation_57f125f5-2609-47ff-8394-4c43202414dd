package com.mongoso.mgs.module.sale.dal.db.erpsaleorder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售订单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_erp_sale_order", autoResultMap = true)
//@KeySequence("u_erp_sale_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpSaleOrderDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long saleOrderId;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 关联单据id */
    private Long relatedOrderId;
    /** 关联单据号 */
    private String relatedOrderCode;

    /** 关联客户 */
    private Long customerId;

    /** 销售订单类型 */
    private String salesOrderTypeDictId;

    /** 联系人 */
    private String contactPersonName;

    /** 联系人电话 */
    private String contactPhone;

    /** 收货人 */
    private String recipientName;

    /** 收货人电话 */
    private String recipientPhone;

    /** 收货地址 */
    private String receiptAddress;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 收款条件 */
    private String paymentTermsDictId;

    /** 币种id */
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 交货日期 */
    private LocalDate deliveryDate;

    /** 订单总金额 */
    private BigDecimal totalAmt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 备注 */
    private String remark;

    /** 是否可下发销售发货通知单 */
    private Short isCanIssueNotice;

    /** 是否可下发销售退货单 */
    private Short isCanIssueReturn;

    /** 是否可下发销售采购需求单 */
    private Short isCanIssuePur;

    /** 是否可下发销售出库单 */
    private Short isCanIssueOut;

    /** 是否可下发销售扣费单 */
    private Short isCanIssueFee;

    /** 是否可下发销售生产订单 */
    private Short isCanIssuePro;

    /** 是否可下发销售物料分析单 */
    private Short isCanIssueMater;

    /** 出库流程配置 */
    private Short shippingProcessConfig;

    /** 下发销售发货通知单 */
    private Short isIssueNotice;

    /** 下发采购需求单 */
    private Short isIssueDemand;

    /** 下发物料分析单 */
    private Short isIssueMaterialAnalysis;

    /** 下发销售采购需求单 */
    private Short isIssuePurchaseReq;

    /** 下发销售出库单 */
    private Short isIssueSaleOut;

    /** 销售订单业务类型 */
    private Short salesOrderBizType;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 公司主体 */
    private String companyOrgId;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    /** 销售订单变更单id */
    private Long saleChangId;

    /**
     * 开票计划策略
     */
    private String invoiceStrategy;

    /** 收款计划策略--取父集 */
    private String relatedCollectionPlanStrategy;

    /**
     * 收款计划策略--取子集
     */
    private String collectionPlanStrategy;

    /**
     * 退款策略
     */
    private String saleRefundPlanStrategy;

    /** 出库状态 */
    private Integer outboundStatus;

    /** 强制关闭 */
    private Integer isForceClose;

    /** 是否可下发销售换货单 */
    private Short isCanIssueExchange;

    /** 单据状态 */
    private Integer formStatus;

    /** 关联下游单据数量 */
    private Integer childrenOrderCount;

    /** 版本号 */
//    private Integer version;

    /** 是否下发销售采购需求单 */
    private Short isCanIssueSalePur;

    /** 本币币种 */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币订单总金额 */
    private BigDecimal localCurrencyTotalAmt;

    /** 是否有自制或委外类型的明细 */
    private Short isSelfMadeOutsource;

    /** 开票结算池策略 */
    private String invoicingSettlementStrategy;

}
