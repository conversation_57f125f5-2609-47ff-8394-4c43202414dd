package com.mongoso.mgs.module.produce.controller.admin.pieceratepayroll.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import java.math.BigDecimal;
 import java.math.BigDecimal;
  import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 计件工资 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class PieceRatePayrollQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long payrollId;

    /** 员工id */
    private Long employeeId;

    /** 员工工号 */
    private String employeeName;

    /** 员工姓名 */
    private String employeeNumber;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 报工记录id */
    private Long reportedWorkId;

    /** 报工记录编码 */
    private String reportedWorkCode;

    /** 报工类型 */
    private Integer reportedWorkType;

    /** 报工时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startReportedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endReportedDt;

    /** 报工数量 */
    private BigDecimal reportedQty;

    /** 单价 */
    private BigDecimal unitPrice;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private Long directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 生产工单ID */
    private Long prodWorkId;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 工作中心ID */
    private Long workCenterId;

    /** 工作中心编码 */
    private String workCenterCode;

    /** 工作中心名称 */
    private String workCenterName;

    /** 用户ID */
    private Long userId;

    private Integer bizType;

}
