package com.mongoso.mgs.module.produce.dal.db.workpicking;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 生产工单领料统计表 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_work_picking_material_total", autoResultMap = true)
//@KeySequence("erp.u_material_analysis_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkPickingMaterialTotalDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long workPickingMaterialTotalId;

    /** 关联单id */
    private Long relatedOrderId;
    private String relatedOrderCode;


    /** 物料id */
    private Long materialId;
    private String materialCode;
    private String materialCategoryDictId;// 物料类别id
    private Integer materialSourceDictId;// 物料来源id
    private String mainUnitDictId;// 基本单位

    /** 需求数量 */
    private BigDecimal demandQty;
    private BigDecimal lossRate;// 损耗率
    private BigDecimal estimatedQty;// 预估用量

    private String warehouseOrgId;// 仓库

    private BigDecimal receivedQty;// 已领料数量
    private BigDecimal returnedQty;// 已退料数量

    private BigDecimal inboundedQty;// 已入库数量
    private BigDecimal outboundedQty;// 已出库数量

}
