package com.mongoso.mgs.module.sale.controller.admin.saledeductiondetail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 
/**
 * 销售扣费明细单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SaleDeductionDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long deductionOrderDetailId;

    /** 销售扣费id */
    private Long deductionOrderId;

    /** 销售扣费单号 */
    private String deductionOrderCode;

    /** 行号 */
    private Short rowNo;

    /** 关联行号 */
    private Short relatedRowNo;

    /** 物料id */
    private Long materialId;

    /** 基本单位 */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 扣费数量 */
    private BigDecimal deductionQty;

    /** 单价 */
    private BigDecimal exclTaxPrice;

    /** 金额 */
    private BigDecimal exclTaxAmt;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 含税单价 */
    private BigDecimal inclTaxPrice;

    /** 含税金额 */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 规格设置 */
    private String specModel;

    /** 是否填报税价 **/
    private Integer includingTax = 0;

    /** 规格属性 */
    private String specAttributeStr;

    /** 币种字典ID */
    private String currencyDictId;
    private String currencyDictName;

    /** 本币币种字典ID */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;

}
