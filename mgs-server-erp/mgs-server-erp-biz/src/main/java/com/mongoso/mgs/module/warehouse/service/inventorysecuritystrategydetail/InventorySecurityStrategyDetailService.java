package com.mongoso.mgs.module.warehouse.service.inventorysecuritystrategydetail;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.warehouse.controller.admin.inventorysecuritystrategydetail.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.inventorysecuritystrategydetail.InventorySecurityStrategyDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 安全库存策略明细 Service 接口
 *
 * <AUTHOR>
 */
public interface InventorySecurityStrategyDetailService {

    /**
     * 获得安全库存策略明细分页
     *
     * @param reqVO 查询条件
     * @return 安全库存策略明细分页
     */
    PageResult<InventorySecurityStrategyDetailRespVO> inventorySecurityStrategyDetailPage(@Valid InventorySecurityStrategyDetailPageReqVO reqVO);

}
