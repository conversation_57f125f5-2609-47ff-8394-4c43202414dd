package com.mongoso.mgs.module.produce.service.workcenter;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.workcenter.vo.*;
import com.mongoso.mgs.module.produce.dal.db.workcenter.WorkCenterDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 工作中心 Service 接口
 *
 * <AUTHOR>
 */
public interface WorkCenterService {

    /**
     * 创建工作中心
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long workCenterAdd(@Valid WorkCenterAditReqVO reqVO);

    /**
     * 更新工作中心
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long workCenterEdit(@Valid WorkCenterAditReqVO reqVO);

    /**
     * 删除工作中心
     *
     * @param workCenterId 编号
     */
    void workCenterDel(Long workCenterId);

    /**
     * 获得工作中心信息
     *
     * @param workCenterId 编号
     * @return 工作中心信息
     */
    WorkCenterRespVO workCenterDetail(Long workCenterId);

    /**
     * 获得工作中心列表
     *
     * @param reqVO 查询条件
     * @return 工作中心列表
     */
    List<WorkCenterRespVO> workCenterList(@Valid WorkCenterQueryReqVO reqVO);

    /**
     * 获得工作中心分页
     *
     * @param reqVO 查询条件
     * @return 工作中心分页
     */
    PageResult<WorkCenterRespVO> workCenterPage(@Valid WorkCenterPageReqVO reqVO);

    /**
     * 批量删除
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> workCenterDelBatch(IdReq reqVO);

    BatchResult workCenterApprove(FlowApprove reqVO);

    Object workCenterFlowCallback(FlowCallback reqVO);

    List<WorkCenterRespVO> selectListByUserId(Long userId);
}
