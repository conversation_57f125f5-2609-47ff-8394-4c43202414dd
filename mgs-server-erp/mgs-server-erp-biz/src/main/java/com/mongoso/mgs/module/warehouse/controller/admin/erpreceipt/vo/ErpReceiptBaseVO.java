package com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo;

import lombok.*;

import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 收货单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ErpReceiptBaseVO implements Serializable {

    /** 收货单ID */
    private Long receiptId;

    /** 收货单号 */
    private String receiptCode;

    /** 收货单类型ID */
    private String receiptTypeDictId;

    /** 关联单据ID */
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 采购订单 */
    private Long purchaseOrderId;

    /** 是否完成检验 */
    private Integer isFullCheck;

    /** 是否全部已入库 */
    private Integer isFullInbounded;

    /** 是否下发入库单 */
    private Integer isIssueInbound;

    /** 备注 */
    private String remark;

    /** 检验结果 */
    private Integer checkResult;

    /** 检验是否自动入库 */
    private Integer checkIsAutoInbound;

    /** 业务类型 */
    private Integer bizType;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 单据状态 */
    private Integer dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

}
