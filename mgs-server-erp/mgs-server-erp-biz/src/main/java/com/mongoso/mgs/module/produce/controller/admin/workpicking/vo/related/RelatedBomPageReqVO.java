package com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.related;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


/**
 * 工单领料单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RelatedBomPageReqVO implements Serializable {

    private String materialCode;
    private String materialName;

    /** 关联单据id */
    private Long materialId;
    private Integer pickingMethodDictId;// 领料方式
    private List<Long> exclMaterialIdList;


}
