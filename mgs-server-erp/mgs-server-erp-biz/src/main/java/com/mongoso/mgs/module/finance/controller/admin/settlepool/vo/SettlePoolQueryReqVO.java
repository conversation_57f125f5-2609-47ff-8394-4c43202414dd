package com.mongoso.mgs.module.finance.controller.admin.settlepool.vo;

import com.mongoso.mgs.framework.common.domain.CommonParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 结算池 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SettlePoolQueryReqVO extends CommonParam{

    /** 单据类型 1：销售，2：采购 */
    @NotNull(message = "单据类型不能为空")
    @Min(value = 1, message = "单据类型值1：销售，2：采购")
    @Max(value = 2, message = "单据类型值1：销售，2：采购")
    private Short formType;
    private List<Long> settleIdList;
    /** 对账状态 */
    private Short accountStatus;
    private List<Integer> accountStatusList;

    /** 结算事务号 */
    private String settleTransactionCode;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单号 */
    private String sourceOrderNumber;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 客户id */
    private Long customerId;

    /** 币种id */
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 物料ID */
    private Long materialId;
    private List<Long> materialIdList;

    /** 主单位ID */
    private String mainUnitDictId;

    /** 来源单据数量 */
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型ID */
    private Long invoiceTypeId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 已对账数量 */
    private BigDecimal reconciledQty;

    /** 可对账数量 */
    private BigDecimal reconcilableQty;

    /** 已对账金额 */
    private BigDecimal reconciledAmt;

    /** 可对账金额 */
    private BigDecimal reconcilableAmt;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 源头单据id */
    private Long originOrderId;

}
