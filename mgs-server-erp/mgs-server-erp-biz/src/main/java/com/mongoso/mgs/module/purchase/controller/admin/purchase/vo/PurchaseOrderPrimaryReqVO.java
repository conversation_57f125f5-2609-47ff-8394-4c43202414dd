package com.mongoso.mgs.module.purchase.controller.admin.purchase.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 采购订单 PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class PurchaseOrderPrimaryReqVO {

    @NotNull(message = "采购订单ID不能为空")
    private Long purchaseOrderId;

    //操作类型['取消强制关闭','强制关闭']
    private Integer opType;

    //单据状态
    private Integer formStatus;

    //采购订单号
    private String purchaseOrderCode;
}
