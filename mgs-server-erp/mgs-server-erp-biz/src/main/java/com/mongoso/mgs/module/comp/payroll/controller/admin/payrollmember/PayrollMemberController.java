package com.mongoso.mgs.module.comp.payroll.controller.admin.payrollmember;

import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo.PayrollNoteDetailRespVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollmember.vo.*;
import com.mongoso.mgs.module.comp.payroll.dal.db.payrollmember.PayrollMemberDO;
import com.mongoso.mgs.module.comp.payroll.service.payrollmember.PayrollMemberService;
import com.mongoso.mgs.module.employee.controller.admin.employee.vo.EmployeeArchivesRespVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 工资单成员 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/compensation")
@Validated
public class PayrollMemberController {

    @Resource
    private PayrollMemberService payrollMemberService;

    @OperateLog("工资单成员添加或编辑")
    @PostMapping("/payrollMemberAdit")
    @PreAuthorize("@ss.hasPermission('payrollMember:adit')")
    public ResultX<Long> payrollMemberAdit(@Valid @RequestBody PayrollMemberAditReqVO reqVO) {
        return success(reqVO.getPayrollMemberId() == null
                            ? payrollMemberService.payrollMemberAdd(reqVO)
                            : payrollMemberService.payrollMemberEdit(reqVO));
    }

    @OperateLog("工资单成员删除")
    @PostMapping("/payrollMemberDelete")
    @PreAuthorize("@ss.hasPermission('payrollMember:del')")
    public ResultX<Boolean> payrollMemberDel(@Valid @RequestBody PayrollMemberPrimaryReqVO reqVO) {
        payrollMemberService.payrollMemberDel(reqVO.getPayrollMemberId());
        return success(true);
    }

    @OperateLog("工资单成员详情")
    @PostMapping("/payrollMemberDetail")
    @PreAuthorize("@ss.hasPermission('payrollMember:query')")
    public ResultX<PayrollMemberRespVO> payrollMemberDetail(@Valid @RequestBody PayrollMemberPrimaryReqVO reqVO) {
        PayrollMemberDO oldDO = payrollMemberService.payrollMemberDetail(reqVO.getPayrollMemberId());
        return success(BeanUtilX.copy(oldDO, PayrollMemberRespVO::new));
    }

    @OperateLog("工资单成员列表")
    @PostMapping("/payrollMemberList")
    @PreAuthorize("@ss.hasPermission('payrollMember:query')")
    public ResultX<List<PayrollMemberRespVO>> payrollMemberList(@Valid @RequestBody PayrollMemberQueryReqVO reqVO) {
        List<PayrollMemberDO> list = payrollMemberService.payrollMemberList(reqVO);
        return success(BeanUtilX.copyList(list, PayrollMemberRespVO::new));
    }

    @OperateLog("工资单未计薪成员")
    @PostMapping("/unPayrollMemberPage")
    @PreAuthorize("@ss.hasPermission('payrollMember:query')")
    public ResultX<PageResult<EmployeeArchivesRespVO>> unPayrollMemberPage(@Valid @RequestBody UnPayrollMemberReqVO reqVO) {
        return success(payrollMemberService.unPayrollMemberPage(reqVO));
    }

    @OperateLog("工资单成员分页")
    @PostMapping("/payrollMemberPage")
    @PreAuthorize("@ss.hasPermission('payrollMember:query')")
    public ResultX<PageResult<PayrollMemberItemRespVO>> payrollMemberPage(@Valid @RequestBody PayrollMemberPageReqVO reqVO) {
        return success(payrollMemberService.payrollMemberPage(reqVO));
    }

    @OperateLog("工资条详情")
    @PostMapping("/payrollNoteDetail")
    @PreAuthorize("@ss.hasPermission('payroll:query')")
    @DataPermission(enable = false)
    public ResultX<PayrollNoteDetailRespVO> payrollNoteDetail(@Valid @RequestBody PayrollMemberQueryReqVO reqVO) {
        return success(payrollMemberService.payrollNoteDetail(reqVO));
    }
}
