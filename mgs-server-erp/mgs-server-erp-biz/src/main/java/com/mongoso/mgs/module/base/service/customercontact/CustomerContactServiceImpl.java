package com.mongoso.mgs.module.base.service.customercontact;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.base.controller.admin.customercontact.vo.*;
import com.mongoso.mgs.module.base.dal.db.customercontact.CustomerContactDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.base.dal.mysql.customercontact.CustomerContactMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.*;


/**
 * 客户联系人 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustomerContactServiceImpl implements CustomerContactService {

    @Resource
    private CustomerContactMapper customerContactMapper;

    @Override
    public Long customerContactAdd(CustomerContactAditReqVO reqVO) {
        // 插入
        CustomerContactDO customerContact = BeanUtilX.copy(reqVO, CustomerContactDO::new);
        customerContactMapper.insert(customerContact);
        // 返回
        return customerContact.getCustomerContactId();
    }

    @Override
    public Long customerContactEdit(CustomerContactAditReqVO reqVO) {
        // 校验存在
        this.customerContactValidateExists(reqVO.getCustomerContactId());
        // 更新
        CustomerContactDO customerContact = BeanUtilX.copy(reqVO, CustomerContactDO::new);
        customerContactMapper.updateById(customerContact);
        // 返回
        return customerContact.getCustomerContactId();
    }

    @Override
    public void customerContactDelete(Long customerContactId) {
        // 校验存在
        this.customerContactValidateExists(customerContactId);
        // 删除
        customerContactMapper.deleteById(customerContactId);
    }

    private CustomerContactDO customerContactValidateExists(Long customerContactId) {
        CustomerContactDO customerContact = customerContactMapper.selectById(customerContactId);
        if (customerContact == null) {
            // throw exception(CUSTOMER_CONTACT_NOT_EXISTS);
            throw new BizException("5001", "客户联系人不存在");
        }
        return customerContact;
    }

    @Override
    public CustomerContactRespVO customerContactDetail(Long customerContactId) {
        CustomerContactDO data = customerContactMapper.selectById(customerContactId);
        return BeanUtilX.copy(data, CustomerContactRespVO::new);
    }

    @Override
    public List<CustomerContactRespVO> customerContactList(CustomerContactQueryReqVO reqVO) {
        List<CustomerContactDO> data = customerContactMapper.selectList(reqVO);
        return BeanUtilX.copy(data, CustomerContactRespVO::new);
    }

    @Override
    public PageResult<CustomerContactRespVO> customerContactPage(CustomerContactPageReqVO reqVO) {
        PageResult<CustomerContactDO> data = customerContactMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, CustomerContactRespVO::new);
    }

}
