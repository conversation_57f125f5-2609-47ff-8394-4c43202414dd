package com.mongoso.mgs.module.sale.controller.admin.salechangdetail.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import java.util.List;

/**
 * 销售订单变更明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SaleChangDetailQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long saleChangDetailId;

    /** 销售订单id */
    private Long saleOrderId;

    /** 行号 */
    private Short rowNo;

    /** 物料id */
    private Long materialId;

    /** 物料ID */
    private List<Long> materialIdList;

    /** 销售订单变更单id */
    private Long saleChangId;

    /** 基本单位 */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 数量 */
    private BigDecimal qty;

    /** 单价 */
    private BigDecimal exclTaxPrice;

    /** 金额 */
    private BigDecimal exclTaxAmt;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 含税单价 */
    private BigDecimal inclTaxPrice;

    /** 含税金额 */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
