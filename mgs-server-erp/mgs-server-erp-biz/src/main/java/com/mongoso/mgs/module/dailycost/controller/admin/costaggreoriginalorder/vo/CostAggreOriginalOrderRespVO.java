package com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 成本归集原始数据单 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostAggreOriginalOrderRespVO extends CostAggreOriginalOrderBaseVO {

    /** 采购订单类型 */
    private Integer purchaseOrderBizType;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 提成发放ID */
    private Long commissionGrantId;

    /** 提成发放单号 */
    private String commissionGrantCode;

    /** 提成任务ID */
    private Long commissionTaskId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单行号 */
    private Integer sourceOrderRowNo;

    /** 单据类型 */
    private Integer formType;

    /** 数量 */
    private BigDecimal qty;

    /** 行金额 (不含税) */
    private BigDecimal exclTaxAmt;

    /** 提成比例 */
    private BigDecimal commissionRatio;

    /** 提成金额 */
    private BigDecimal commissionAmt;

    /** 销售订单id */
    private Long saleOrderId;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 关联客户 */
    private Long customerId;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 客户名称 */
    private String customerName;

    /** 物料名称 */
    private String materialName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 主单位(基本单位) */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 水电气表id */
    private Long utilityConfigId;
    /** 表类型 */
    private String utilityName;

    /** 水电气档案id */
    private Long utilityArchivesId;
    /** 表名称 */
    private String archivesName;

    /** 转化倍数 */
    private BigDecimal converMult;

    /** 当日抄表数 */
    private BigDecimal todayReadQty;

    /** 差额 */
    private BigDecimal readDifference;

    /** 本日使用量 */
    private BigDecimal todayUsage;

    /** 费用单价 */
    private BigDecimal costPrice;

    /** 费用 */
    private BigDecimal utilityAmt;
}
