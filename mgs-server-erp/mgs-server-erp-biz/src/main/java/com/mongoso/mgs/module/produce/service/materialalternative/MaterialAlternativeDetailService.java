package com.mongoso.mgs.module.produce.service.materialalternative;

import java.util.*;

import com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.detail.MaterialAlternativeDetailAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.detail.MaterialAlternativeDetailPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.detail.MaterialAlternativeDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.detail.MaterialAlternativeDetailRespVO;
import jakarta.validation.*;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 替代物料明细 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialAlternativeDetailService {

    /**
     * 创建替代物料明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long materialAlternativeDetailAdd(@Valid MaterialAlternativeDetailAditReqVO reqVO);

    /**
     * 更新替代物料明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long materialAlternativeDetailEdit(@Valid MaterialAlternativeDetailAditReqVO reqVO);

    /**
     * 删除替代物料明细
     *
     * @param id 编号
     */
    void materialAlternativeDetailDelete(Long id);

    /**
     * 获得替代物料明细信息
     *
     * @param id 编号
     * @return 替代物料明细信息
     */
    MaterialAlternativeDetailRespVO materialAlternativeDetailDetail(Long id);

    /**
     * 获得替代物料明细列表
     *
     * @param reqVO 查询条件
     * @return 替代物料明细列表
     */
    List<MaterialAlternativeDetailRespVO> materialAlternativeDetailList(@Valid MaterialAlternativeDetailQueryReqVO reqVO);

    /**
     * 获得替代物料明细分页
     *
     * @param reqVO 查询条件
     * @return 替代物料明细分页
     */
    PageResult<MaterialAlternativeDetailRespVO> materialAlternativeDetailPage(@Valid MaterialAlternativeDetailPageReqVO reqVO);

}
