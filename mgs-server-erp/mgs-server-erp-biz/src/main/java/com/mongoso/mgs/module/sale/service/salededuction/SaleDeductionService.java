package com.mongoso.mgs.module.sale.service.salededuction;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.sale.controller.admin.salededuction.vo.*;
import com.mongoso.mgs.module.sale.dal.db.salededuction.SaleDeductionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 销售扣费单 Service 接口
 *
 * <AUTHOR>
 */
public interface SaleDeductionService {

    /**
     * 创建销售扣费单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long saleDeductionAdd(@Valid SaleDeductionAditReqVO reqVO);

    /**
     * 更新销售扣费单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long saleDeductionEdit(@Valid SaleDeductionAditReqVO reqVO);

    /**
     * 删除销售扣费单
     *
     * @param id 编号
     */
    void saleDeductionDel(Long id);

    /**
     * 删除销售扣费单
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> saleDeductionDelBatch(IdReq reqVO);

    /**
     * 获得销售扣费单信息
     *
     * @param deductionOrderId 编号
     * @return 销售扣费单信息
     */
    SaleDeductionRespVO saleDeductionDetail(Long deductionOrderId);

    /**
     * 获得销售扣费单列表
     *
     * @param reqVO 查询条件
     * @return 销售扣费单列表
     */
    List<SaleDeductionDO> saleDeductionList(@Valid SaleDeductionQueryReqVO reqVO);

    /**
     * 获得销售扣费单分页
     *
     * @param reqVO 查询条件
     * @return 销售扣费单分页
     */
    PageResult<SaleDeductionDetailResp> saleDeductionPage(@Valid SaleDeductionPageReqVO reqVO);

    PageResult<SaleDeductionDetailResp> querySaleDeductionDetailPage(SaleDeductionPageReqVO reqVO);

    BatchResult saleDeductionApprove(FlowApprove reqVO);

    Object saleDeductionFlowCallback(FlowCallback reqVO);
}
