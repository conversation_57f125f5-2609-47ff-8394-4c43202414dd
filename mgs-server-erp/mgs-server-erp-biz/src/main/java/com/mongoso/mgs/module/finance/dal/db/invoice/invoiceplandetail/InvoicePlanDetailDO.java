package com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 开票计划明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_invoice_plan_detail", autoResultMap = true)
//@KeySequence("erp.u_invoice_plan_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePlanDetailDO extends OperateDO {

    /** 开票计划明细id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long invoicePlanDetailId;

    /** 开票方向 */
    private Short billingDirection;

    /** 开票计划id */
    private Long invoicePlanId;

    /** 开票计划单号 */
    private String invoicePlanNo;

    /** 待开票计划明细id */
    private Long invoicePendingPlanDetailId;

    /** 行号 */
    private Long rowNo;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 物料编码 */
    private String materialCode;

    private Long materialId;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 计划开票数量 */
    private BigDecimal planInvoiceQty;

    /** 计划开票金额 */
    private BigDecimal planInvoiceAmtInclTax;
    private BigDecimal planInvoiceAmtExclTax;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 数量 */
    private BigDecimal qty;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;


    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    private Long invoiceTypeDictId;

    private String invoiceTypeDictName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 剩余可计划数量 */
    private BigDecimal remainingPlanQty;

    /** 剩余可计划金额 */
    private BigDecimal remainingPlanAmt;

    /** 已申请数量 */
    private BigDecimal readyQty;

    /** 已申请金额 */
    private BigDecimal readyAmt;

    /** 可申请数量 */
    private BigDecimal canQty;

    /** 可申请金额 */
    private BigDecimal canAmt;

    /** 剩余可计划金额 */
    private BigDecimal taxAmt;

    /** 已开票数量 */
    private BigDecimal readyInvoiceQty;

    /** 已开票金额 */
    private BigDecimal readyInvoiceAmt;

    /** 可开票数量 */
    private BigDecimal canInvoiceQty;

    /** 可开票金额 */
    private BigDecimal canInvoiceAmt;
}
