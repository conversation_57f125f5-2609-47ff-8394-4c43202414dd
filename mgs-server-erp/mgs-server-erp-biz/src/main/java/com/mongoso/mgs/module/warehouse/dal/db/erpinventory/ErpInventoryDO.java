package com.mongoso.mgs.module.warehouse.dal.db.erpinventory;

import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import org.springframework.format.annotation.DateTimeFormat;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 盘点单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_inventory", autoResultMap = true)
//@KeySequence("erp.u_inventory_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpInventoryDO extends OperateDO {

    /** 盘点单ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long inventoryId;

    /** 盘点单号 */
    private String inventoryCode;

    /** 盘点单主题 */
    private String inventoryName;

    /** 盘点单类型ID */
    private String inventoryTypeDictId;

    /** 盘点仓库 */
    private String warehouseOrgId;

    /** 盘点方式 */
    private Short inventoryMethod;

    /** 计划盘点时间 */
    private LocalDateTime planInventoryDt;

    /** 实际盘点时间 */
    private LocalDateTime actStartDt;

    /** 盘点完成盘点 */
    private LocalDateTime actEndDt;

    /** 盘点状态 */
    private Integer inventoryStatus;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 单据状态 */
    private Integer dataStatus;

    /** 调整状态 ["无需调整"，"待调整"，“已调整”] */
    private Integer adjustStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;


}
