package com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 物料分析数量汇总 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialAnalysisTotalQueryReqVO extends CommonParam{

    private Long bizType;

    /** 主键ID */
    private Long materialAnalysisTotalId;

    /** 物料分析ID */
    private Long materialAnalysisId;

    private Integer materialSourceDictId;//物料来源

    /** 物料id */
    private Long materialId;
    private List<Long> materialIdList;
    private List<Long> exclMaterialIdList;

    /** 行号 */
    private Integer rowNo;

    /** 净需求数量 */
    private BigDecimal demandQty;

    /** 已下发数量 */
    private BigDecimal issuedQty;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;


    /** 物料是否全部已采购 */
    private Integer isMaterialFullPurchased;

    /** 物料是否全部已规划 */
    private Integer isMaterialFullPlaned;

}
