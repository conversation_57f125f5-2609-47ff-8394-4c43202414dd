package com.mongoso.mgs.module.comp.payroll.service.payrollpayment;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollpayment.vo.PayrollPaymentAditReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollpayment.vo.PayrollPaymentPageReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollpayment.vo.PayrollPaymentQueryReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payrollpayment.vo.PayrollPaymentRespVO;

/**
 * 工资付款单 Service 接口
 *
 * <AUTHOR>
 */
public interface PayrollPaymentService {

    /**
     * 创建工资付款单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long payrollPaymentAdd(@Valid PayrollPaymentAditReqVO reqVO);

    /**
     * 更新工资付款单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long payrollPaymentEdit(@Valid PayrollPaymentAditReqVO reqVO);

    /**
     * 删除工资付款单
     *
     * @param payrollPaymentId 编号
     */
    void payrollPaymentDel(Long payrollPaymentId);

    /**
     * 批量删除工资付款单
     *
     * @param reqVO 请求对象
     */
    ResultX<BatchResult> payrollPaymentDelBatch(IdReq reqVO);

    /**
     * 获得工资付款单信息
     *
     * @param payrollPaymentId 编号
     * @return 工资付款单信息
     */
    PayrollPaymentRespVO payrollPaymentDetail(Long payrollPaymentId);

    /**
     * 获得工资付款单列表
     *
     * @param reqVO 查询条件
     * @return 工资付款单列表
     */
    List<PayrollPaymentRespVO> payrollPaymentList(@Valid PayrollPaymentQueryReqVO reqVO);

    /**
     * 获得工资付款单分页
     *
     * @param reqVO 查询条件
     * @return 工资付款单分页
     */
    PageResult<PayrollPaymentRespVO> payrollPaymentPage(@Valid PayrollPaymentPageReqVO reqVO);

    /**
     * 审核工资付款单
     *
     * @param reqVO 查询条件
     * @return 审核结果
     */
    BatchResult payrollPaymentApprove(FlowApprove reqVO);

    /**
     * 审核工资付款单回调
     *
     * @param reqVO 审核信息
     * @return 回调结果
     */
    Object payrollPaymentFlowCallback(FlowCallback reqVO);

}
