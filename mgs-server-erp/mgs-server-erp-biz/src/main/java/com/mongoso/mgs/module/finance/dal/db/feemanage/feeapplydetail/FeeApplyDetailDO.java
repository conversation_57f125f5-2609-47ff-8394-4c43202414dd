package com.mongoso.mgs.module.finance.dal.db.feemanage.feeapplydetail;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 费用申请明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_fee_apply_detail", autoResultMap = true)
//@KeySequence("u_fee_apply_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeeApplyDetailDO extends OperateDO {

    /** 主键 */
        @TableId(type = IdType.ASSIGN_ID)
    private Long feeApplyDetailId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 金额 */
    private BigDecimal amt;

    /** 备注 */
    private String remark;

    /** 出发日期 */
    private LocalDate setOutDt;

    /** 出发地点 */
    private String setOutLocation;

    /** 到达日期 */
    private LocalDate arrivalDt;

    /** 到达地点 */
    private String arrivalLocation;

    /** 里程 */
    private String mileage;

    /** 交通工具 */
    private String vehicle;

    /** 返回日期 */
    private LocalDate returnDt;

    /** 购买日期 */
    private LocalDate purchasingDt;

    /** 购买地点 */
    private String purchasingLocation;

    /** 住宿日期 */
    private LocalDate stayDt;

    /** 住宿地点 */
    private String stayLocation;

    /** 离开日期 */
    private LocalDate leaveDt;

    /** 隔间夜 */
    private String overnight;

    /** 发票号 */
    private String invoiceNo;

    /** 单据类型 */
    private Integer formType;

    /** 行号 */
    private Integer rowNo;

    /** 创建人ID */
    private Long createdId;

    /** 版本号 */
//    private Integer version;


}
