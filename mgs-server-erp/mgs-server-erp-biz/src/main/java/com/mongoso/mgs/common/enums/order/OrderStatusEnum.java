package com.mongoso.mgs.common.enums.order;


/**
 * 销售订单状态枚举
 *
 */
public enum OrderStatusEnum {

    // 结算池状态
    NOT_ACCOUNT(0, "未对账"),
    ACCOUNTING(1, "部分对账"),
    ACCOUNTED(2, "已对账"),

    // 待计划状态
    NOT_PLAN(0, "未生成"),
    PLANING(1, "部分生成"),
    PLANED(2, "已生成"),

    // 应收账款状态
    NOT_SHOULD(0, "未申请"),
    SHOULDING(1, "部分申请"),
    SHOULDED(2, "已申请"),
    NOT_RETURN(3, "未退款"),
    RETURNING(4, "部分退款"),
    RETURNED(5, "已退款"),
    NOT_PAYMENT(6, "未付款"),
    PAYMENTING(7, "部分付款"),
    PAYMENTED(8, "已付款"),


    // 申请状态
    NOT_APPLY(0, "未收款"),
    APPLYING(1, "部分收款"),
    APPLYED(2, "已收款"),

    // 付款状态
    NOT_PAY(0, "未生成"),
    PAYING(1, "部分生成"),
    PAYED(2, "已生成"),

    NOT_SETTLED(0,"未核销"),
    SETTLED(1,"已核销"),

    // 预收款状态
    NOT_USE(0, "未使用"),
    USEING(1, "部分使用"),
    USEED(2, "已使用"),

    //生产订单状态
    TOBEGIN(0, "待开始"),
    PROGRESS(1, "进行中"),
    COMPLETED(2, "已完工"),
    CANCELLED(3, "已关闭"),

    //生产订单状态
    PENDING(0, "待生产"),
    IN_PRODUCTION(1, "生产中"),
    FORCE_COMPLETE(3, "强制完工"),
    CLOSED(4, "已关闭"),

    //派工单
    DISPATCHED(0, "已派工"),
    FINISHED(2, "已完成"),
    DISPATCHED_CLOSED(3, "已关闭"),

    NOT_SHIPPED(0,"未出库"),
    PARTIALLY_SHIPPED(1,"部分出库"),
    SHIPPED(2,"已出库"),



    ;

    /**
     * 类型
     */
    private Integer code;

    /**
     * 名称
     */
    private String name;

    OrderStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getKeyByValue(Integer code) {
        String key = null;
        for(OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if(orderStatusEnum.code.equals(code)) {
                key = orderStatusEnum.name;
                break;
            }
        }
        return key;
    }

    public static Integer getValueByKey(String name) {
        Integer value = 0;
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (orderStatusEnum.name.equals(name)) {
                value = orderStatusEnum.code;
                break;
            }
        }
        return value;
    }

}
