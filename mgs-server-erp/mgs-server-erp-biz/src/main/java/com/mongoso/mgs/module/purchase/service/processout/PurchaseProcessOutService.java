package com.mongoso.mgs.module.purchase.service.processout;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.*;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO;

import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDetailDO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderPrimaryReqVO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 工序委外采购订单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseProcessOutService {

    /**
     * 创建工序委外采购订单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseProcessOutAdd(@Valid PurchaseProcessOutAditReqVO reqVO);

    /**
     * 更新工序委外采购订单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseProcessOutEdit(@Valid PurchaseProcessOutAditReqVO reqVO);

    /**
     * 删除工序委外采购订单
     *
     * @param purchaseProcessOutId 编号
     */
    void purchaseProcessOutDel(Long purchaseProcessOutId);

    /**
     * 批量删除采购订单
     * @param reqVO
     * @return
     */
    ResultX<BatchResult> purchaseProcessOutDelBatch(IdReq reqVO);

    /**
     * 获得工序委外采购订单信息
     *
     * @param purchaseProcessOutId 编号
     * @return 工序委外采购订单信息
     */
    PurchaseProcessOutRespVO purchaseProcessOutDetail(Long purchaseProcessOutId);

    PurchaseProcessOutRespVO purchaseProcessOutDetailByReceipt(Long relatedOrderId);

    /**
     * 获得工序委外采购订单列表
     *
     * @param reqVO 查询条件
     * @return 工序委外采购订单列表
     */
    List<PurchaseProcessOutRespVO> purchaseProcessOutList(@Valid PurchaseProcessOutQueryReqVO reqVO);

    /**
     * 获得工序委外采购订单分页
     *
     * @param reqVO 查询条件
     * @return 工序委外采购订单分页
     */
    PageResult<PurchaseProcessOutRespVO> purchaseProcessOutPage(@Valid PurchaseProcessOutPageReqVO reqVO);

    BatchResult purchaseProcessOutApprove(FlowApprove reqVO);

    Object purchaseProcessOutFlowCallback(FlowCallback reqVO);

    void erpPurchaseProcessOutForceClose(PurchaseProcessOutPrimaryReqVO reqVO);

    List<PurchaseProcessOutDO> forewarnJob(Integer dataStatus, List<Integer> formStatusList, Integer isFullReceipted);

    void editChildrenOrderCount (Long purchaseProcessOutId, Integer dataStatus);
    /**
     * 工序委外采购订单订单修改单据状态
     *
     * @param reqVO
     * @return
     */
    void erpPurchaseProcessOutEditFormStatus(PurchaseProcessOutPrimaryReqVO reqVO);


    Map<Long, List<PurchaseProcessOutDetailDO>> forewarnJobByIdList(List<Long> idList);

}
