package com.mongoso.mgs.module.warehouse.service.erpinventory;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.plan.*;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 盘点计划 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpInventoryPlanService {

    /**
     * 创建盘点计划
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpInventoryPlanAdd(@Valid ErpInventoryPlanAditReqVO reqVO);

    /**
     * 更新盘点计划
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpInventoryPlanEdit(@Valid ErpInventoryPlanAditReqVO reqVO);

    /**
     * 更新盘点计划启用/禁用状态
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    BatchResult erpInventoryPlanEnableEdit(@Valid ErpInventoryPlanEnableAditReqVO reqVO);

    /**
     * 删除盘点计划
     *
     * @param inventoryPlanId 编号
     */
    void erpInventoryPlanDel(Long inventoryPlanId);

    /**
     * 批量删除盘点计划
     *
     * @param reqVO 删除信息
     */
    ResultX<BatchResult> erpInventoryPlanDelBatch(IdsReqVO reqVO);

    /**
     * 获得盘点计划信息
     *
     * @param inventoryPlanId 编号
     * @return 盘点计划信息
     */
    ErpInventoryPlanRespVO erpInventoryPlanDetail(Long inventoryPlanId);

    /**
     * 获得盘点计划列表
     *
     * @param reqVO 查询条件
     * @return 盘点计划列表
     */
    List<ErpInventoryPlanRespVO> erpInventoryPlanList(@Valid ErpInventoryPlanQueryReqVO reqVO);

    /**
     * 获得盘点计划分页
     *
     * @param reqVO 查询条件
     * @return 盘点计划分页
     */
    PageResult<ErpInventoryPlanRespVO> erpInventoryPlanPage(@Valid ErpInventoryPlanPageReqVO reqVO);

    /**
     * 审核盘点计划
     *
     * @param reqVO 查询条件
     * @return 审核结果
     */
    BatchResult erpInventoryPlanApprove(FlowApprove reqVO);

    /**
     * 审核盘点计划回调
     *
     * @param reqVO 审核信息
     * @return 回调结果
     */
    Object erpInventoryPlanFlowCallback(FlowCallback reqVO);

}
