package com.mongoso.mgs.module.produce.service.prodwork;

import java.math.BigDecimal;
import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.ProdWorkRespAI;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.ProdWorkRespStatAI;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkBO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.*;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 生产工单 Service 接口
 *
 * <AUTHOR>
 */
public interface ProdWorkService {

    /**
     * 创建生产工单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long prodWorkAdd(@Valid ProdWorkAditReqVO reqVO);

    Long prodWorkAditBatch(ProdWorkBatchAditReqVO reqVO);

    /**
     * 更新生产工单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long prodWorkEdit(@Valid ProdWorkAditReqVO reqVO);

    /**
     * 删除生产工单
     *
     * @param prodWorkId 编号
     */
    void prodWorkDel(Long prodWorkId);

    /**
     * 获得生产工单信息
     *
     * @param prodWorkId 编号
     * @return 生产工单信息
     */
    ProdWorkRespVO prodWorkDetail(Long prodWorkId);

    /**
     * 获得生产工单被引用信息
     *
     * @param prodWorkId 编号
     * @return 生产工单信息
     */
    ProdWorkQuotedRespVO prodWorkQuotedDetail(Long prodWorkId);

    /**
     * 工单直接退料入库单-获得生产工单被引用信息
     *
     * @param prodWorkId 编号
     * @return 生产工单信息
     */
    ProdWorkQuotedRespVO prodWorkDirectReturnQuotedDetail(Long prodWorkId);

    /**
     * 获得生产工单被引用信息
     *
     * @param prodWorkId 编号
     * @return 生产工单信息
     */
    ProdWorkQuotedOutboundRespVO prodWorkQuotedDetailByOutbound(Long prodWorkId);

    /**
     * 获得生产工单列表
     *
     * @param reqVO 查询条件
     * @return 生产工单列表
     */
    List<ProdWorkRespVO> prodWorkList(@Valid ProdWorkQueryReqVO reqVO);

    /**
     * 获得生产工单分页
     *
     * @param reqVO 查询条件
     * @return 生产工单分页
     */
    PageResult<ProdWorkRespVO> prodWorkPage(@Valid ProdWorkPageReqVO reqVO);

    /**
     * 批量删除
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> prodWorkDelBatch(IdReq reqVO);

    BatchResult prodWorkApprove(FlowApprove reqVO);

    Object prodWorkFlowCallback(FlowCallback reqVO);

    /**
     * 生产工单状态变更
     *
     * @param reqVO
     * @return
     */
    Boolean prodWorkStatus(ProdWorkPrimaryReqVO reqVO);

    /**
     * 生产工单数据回写
     *
     * @param prodWorkBO
     */
    void writeBackProdWork(ProdWorkBO prodWorkBO);

    /**
     * 更新已入库数量
     * @param prodWorkId 生产工单ID
     * @param inboundQty 入库数量
     */
    void updateInboundQty(Long prodWorkId, BigDecimal inboundQty);


    List<ProdWorkDO> forewarnJob(Integer dataStatus, List<Integer> statusList);

    List<ProdWorkRespAI> prodWorkPageAI();


    ProdWorkRespStatAI prodWorkStatAI();

    /**
     * 直接领料出库引用生产工单明细物料
     *
     * @param reqVO
     * @return
     */
    List<ProdWorkMaterialDetailTreeRespVO> prodWorkMaterialDetailTree(ProdWorkMaterialDetailTreeReqVO reqVO);


}
