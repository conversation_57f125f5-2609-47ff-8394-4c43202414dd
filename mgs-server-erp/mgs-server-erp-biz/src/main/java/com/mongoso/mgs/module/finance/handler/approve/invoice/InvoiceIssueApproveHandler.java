package com.mongoso.mgs.module.finance.handler.approve.invoice;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.enums.strategy.StrategyEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.vo.StrategyModelVO;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.strategy.StrategyModelService;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapply.enums.InvoiceApplyStatusEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceissue.enums.InvoiceIssueStatusEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.BillingDirectionEnum;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoicependingplan.enums.InvoiceApplyEnum;
import com.mongoso.mgs.module.finance.controller.admin.pendingpaymentplan.vo.PendingPaymentPlanAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.pendingpaymentplandetail.vo.PendingPaymentPlanDetailAditReqVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapply.InvoiceApplyDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceapplydetail.InvoiceApplyDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoicedetail.InvoiceDetailDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceissue.InvoiceIssueDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplan.InvoicePlanDO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail.InvoicePlanDetailDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapply.InvoiceApplyMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceapplydetail.InvoiceApplyDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoicedetail.InvoiceDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceissue.InvoiceIssueMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplan.InvoicePlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplandetail.InvoicePlanDetailMapper;
import com.mongoso.mgs.module.finance.service.shouldpayment.ShouldPaymentService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class InvoiceIssueApproveHandler extends FlowApproveHandler<InvoiceIssueDO> {

    @Resource
    private InvoiceIssueMapper issueMapper;

    @Resource
    private InvoiceApplyMapper invoiceApplyMapper;

    @Resource
    private InvoiceApplyDetailMapper invoiceApplyDetailMapper;

    @Resource
    private InvoiceDetailMapper invoiceDetailMapper;
    @Resource
    private StrategyModelService strategyModelService;

    @Resource
    private ShouldPaymentService shouldPaymentService;

    @Resource
    private InvoicePlanMapper invoicePlanMapper;

    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;


    @Override
    protected ApproveCommonAttrs approvalAttributes(InvoiceIssueDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(InvoiceIssueDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(InvoiceIssueDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getInvoiceId())
                .objCode(item.getInvoiceCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }
    @Override
    protected Boolean businessVerify(InvoiceIssueDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
            //审核
            //判断是否是红冲数据
            if (item.getRedInvoiceId() != null){
                //判断上游是否审核通过了
                InvoiceIssueDO invoiceIssueDO = issueMapper.selectById(item.getRedInvoiceId());
                if (Objects.isNull(invoiceIssueDO)){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游不存在");
                    return false;
                }
                if (invoiceIssueDO.getDataStatus() != DataStatusEnum.APPROVED.getKey().shortValue()){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游未审核");
                    return false;
                }
                //判断审核金额有没有大于上游总金额
                List<InvoiceDetailDO> upDetailList = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>()
                        .eq(InvoiceDetailDO::getInvoiceId, item.getRedInvoiceId())
                );
                if (CollectionUtils.isEmpty(upDetailList)){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游明细不存在");
                    return false;
                }
                Map<Long, InvoiceDetailDO> upDetailMap = upDetailList.stream().collect(Collectors.toMap(
                        InvoiceDetailDO::getInvoiceDetailId, Function.identity(), (s1, s2) -> s1)
                );
                List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>()
                        .eq(InvoiceDetailDO::getInvoiceId, item.getInvoiceId())
                );
                for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                    InvoiceDetailDO invoiceDetailDO = upDetailMap.get(detailDO.getInvoiceApplyDetailId());
                    if (detailDO.getApplyInvoiceAmtInclTax().abs().compareTo(invoiceDetailDO.getRemainingInvoiceAmt().abs()) > 0){
                        failItem.setCode(item.getInvoiceCode());
                        failItem.setReason("审核金额大于剩余可红冲金额");
                        return false;
                    }
                }
//                if (item.getInvoiceTotalTax().abs().compareTo(invoiceIssueDO.getInvoiceTotalTax().abs()) > 0){
//                    failItem.setCode(item.getInvoiceCode());
//                    failItem.setReason("审核金额大于可申请金额");
//                    return false;
//                }
                return true;
            }
            //判断策略配置跳过开票申请是否为是
            if (item.getInvoiceApplyId() != null) {
                //判断上游是否审核通过了
                InvoiceApplyDO invoiceApplyDO = invoiceApplyMapper.selectById(item.getInvoiceApplyId());
                if (Objects.isNull(invoiceApplyDO)){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游不存在");
                    return false;
                }
                if (invoiceApplyDO.getDataStatus() != DataStatusEnum.APPROVED.getKey().shortValue()){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游未审核");
                    return false;
                }
                ///判断明细
                List<InvoiceApplyDetailDO> upDetailList = invoiceApplyDetailMapper.selectList(new LambdaQueryWrapper<InvoiceApplyDetailDO>()
                        .eq(InvoiceApplyDetailDO::getInvoiceApplyId, item.getInvoiceApplyId())
                );
                if (CollectionUtils.isEmpty(upDetailList)){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游明细不存在");
                    return false;
                }
                Map<Long, InvoiceApplyDetailDO> upDetailMap = upDetailList.stream().collect(Collectors.toMap(
                        InvoiceApplyDetailDO::getInvoiceApplyDetailId, Function.identity(), (s1, s2) -> s1)
                );
                List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>()
                        .eq(InvoiceDetailDO::getInvoiceId, item.getInvoiceId())
                );
                for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                    InvoiceApplyDetailDO invoiceDetailDO = upDetailMap.get(detailDO.getInvoiceApplyDetailId());
                    if (detailDO.getApplyInvoiceAmtInclTax().abs().compareTo(invoiceDetailDO.getCanAmt().abs()) > 0){
                        failItem.setCode(item.getInvoiceCode());
                        failItem.setReason("审核金额已超额");
                        return false;
                    }
                }
            }else {
                //判断上游是否审核通过了
                InvoicePlanDO invoicePlanDO = invoicePlanMapper.selectById(item.getInvoicePlanId());
                if (Objects.isNull(invoicePlanDO)){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游不存在");
                    return false;
                }
                if (invoicePlanDO.getDataStatus() != DataStatusEnum.APPROVED.getKey().shortValue()){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游未审核");
                    return false;
                }
                ///判断明细
                List<InvoicePlanDetailDO> upDetailList = invoicePlanDetailMapper.selectList(new LambdaQueryWrapper<InvoicePlanDetailDO>()
                        .eq(InvoicePlanDetailDO::getInvoicePlanId, item.getInvoicePlanId())
                );
                if (CollectionUtils.isEmpty(upDetailList)){
                    failItem.setCode(item.getInvoiceCode());
                    failItem.setReason("上游明细不存在");
                    return false;
                }
                Map<Long, InvoicePlanDetailDO> upDetailMap = upDetailList.stream().collect(Collectors.toMap(
                        InvoicePlanDetailDO::getInvoicePlanDetailId, Function.identity(), (s1, s2) -> s1)
                );
                List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>()
                        .eq(InvoiceDetailDO::getInvoiceId, item.getInvoiceId())
                );
                for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                    InvoicePlanDetailDO invoiceDetailDO = upDetailMap.get(detailDO.getInvoicePlanDetailId());
                    if (detailDO.getApplyInvoiceAmtInclTax().abs().compareTo(invoiceDetailDO.getCanInvoiceAmt().abs()) > 0){
                        failItem.setCode(item.getInvoiceCode());
                        failItem.setReason("审核金额已超额");
                        return false;
                    }
                }
            }
        }else {
            //反审核
            //反审校验收付款是否反审了
            shouldPaymentService.checkOrder(item.getInvoiceId());
            //如果是红冲数据，则不需要判断下游数据
            if (item.getRedInvoiceId() != null){
                return true;
            }

            //因为当前为最下游，所以只需要判断是否绑定红冲数据，如果没有，则不需要做处理
            List<InvoiceIssueDO> invoiceIssueDOS = issueMapper.selectList(new LambdaQueryWrapper<InvoiceIssueDO>()
                    .eq(InvoiceIssueDO::getRedInvoiceId, item.getInvoiceId())
                    .ne(InvoiceIssueDO::getDataStatus, DataStatusEnum.NOT_APPROVE.getKey().shortValue())
            );
            if (CollectionUtils.isNotEmpty(invoiceIssueDOS)){
                failItem.setCode(item.getInvoiceCode());
                failItem.setReason("下游存在已审核的数据");
                return false;
            }


            //return !CollectionUtils.isNotEmpty(invoiceIssueDOS);

        }
        return true;
    }

    @Override
    public Integer handleBusinessData(InvoiceIssueDO currentDO, BaseApproveRequest request) {
        //当前对象
        if (currentDO == null){
            return 1;
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = currentDO.getInvoiceId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        FailItem failItem = request.getFailItem();

        InvoiceIssueDO issue = currentDO;
        try {
            if (buttonType == DataButtonEnum.APPROVE.getKey().intValue() || buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()){
                if (issue.getRedInvoiceId() == null){
                    //只有不是红冲发票才需要做处理
                    InvoiceApplyDO invoiceApplyDO = new InvoiceApplyDO();
                    InvoicePlanDO invoicePlanDO = new InvoicePlanDO();
                    List<Long> invoiceApplyDetailId = new ArrayList<>();
                    List<Long> invoicePlanDetailId = new ArrayList<>();
                    List<InvoiceApplyDetailDO> applyDetailList = new ArrayList<>();
                    Map<Long, InvoiceApplyDetailDO> applyDetailMap = new HashMap<>();
                    List<InvoicePlanDetailDO> planDetailList = new ArrayList<>();
                    Map<Long, InvoicePlanDetailDO> planDetailMap = new HashMap<>();
                    List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>().eq(InvoiceDetailDO::getInvoiceId, issue.getInvoiceId()));
                    if (issue.getInvoiceApplyId() != null){
                        invoiceApplyDO = invoiceApplyMapper.selectById(issue.getInvoiceApplyId());
                        invoiceDetailDOS.forEach(invoiceDetailDO -> {
                            invoiceApplyDetailId.add(invoiceDetailDO.getInvoiceApplyDetailId());
                        });
                        applyDetailList = invoiceApplyDetailMapper.selectBatchIds(invoiceApplyDetailId);
                        applyDetailMap = applyDetailList.stream().collect(Collectors.toMap(InvoiceApplyDetailDO::getInvoiceApplyDetailId, Function.identity(), (s1, s2) -> s1));
                    }else {
                        invoicePlanDO = invoicePlanMapper.selectById(issue.getInvoicePlanId());
                        invoiceDetailDOS.forEach(invoiceDetailDO -> {
                            invoicePlanDetailId.add(invoiceDetailDO.getInvoicePlanDetailId());
                        });
                        planDetailList = invoicePlanDetailMapper.selectBatchIds(invoicePlanDetailId);
                        planDetailMap = planDetailList.stream().collect(Collectors.toMap(InvoicePlanDetailDO::getInvoicePlanDetailId, Function.identity(), (s1, s2) -> s1));
                    }

                    //已处理数量
                    BigDecimal readyQty = BigDecimal.ZERO;
                    BigDecimal readyAmt = BigDecimal.ZERO;
                    //可处理数量
                    BigDecimal canQty = BigDecimal.ZERO;
                    BigDecimal canAmt = BigDecimal.ZERO;
                    //已开票的数量
                    BigDecimal readyInvoiceQty = BigDecimal.ZERO;
                    BigDecimal readyInvoiceAmt = BigDecimal.ZERO;
                    BigDecimal canInvoiceQty = BigDecimal.ZERO;
                    BigDecimal canInvoiceAmt = BigDecimal.ZERO;

                    if (issue.getInvoiceApplyId() != null){
                        List<InvoiceApplyDetailDO> list = new ArrayList<>();
                        List<Long> applyDetailIdList = new ArrayList<>();
                        for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                            InvoiceApplyDetailDO apply = applyDetailMap.get(detailDO.getInvoiceApplyDetailId());
                            if (Objects.isNull(apply)){
                                failItem.setCode(issue.getInvoiceCode());
                                failItem.setReason("上游明细的数据异常");
                                throw new BizException("5001", "上游明细的数据异常!");
                            }
                            BigDecimal amt = BigDecimal.ZERO;
                            BigDecimal qty = BigDecimal.ZERO;

                            //已处理数量
                            BigDecimal readyDetailQty = BigDecimal.ZERO;
                            BigDecimal readyDetailAmt = BigDecimal.ZERO;

                            //可处理数量
                            BigDecimal canDetailQty = BigDecimal.ZERO;
                            BigDecimal canDetailAmt = BigDecimal.ZERO;
                            if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
                                //审核，则 -
                                amt = apply.getRemainingApplyAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                qty = apply.getRemainingApplyQty().subtract(detailDO.getInvoiceQty());

                                readyDetailQty = apply.getReadyQty().add(detailDO.getInvoiceQty());
                                readyDetailAmt = apply.getReadyAmt().add(detailDO.getInvoiceAmtInclTax());

                                canDetailAmt = apply.getCanAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                canDetailQty = apply.getCanQty().subtract(detailDO.getInvoiceQty());
                                //审核通过后为已开票
                                issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.ISSUE_INVOICE.type.shortValue());

                                if (apply.getCanAmt().abs().subtract(detailDO.getInvoiceAmtInclTax().abs()).compareTo(BigDecimal.ZERO) < 0){
                                    failItem.setCode(issue.getInvoiceCode());
                                    failItem.setReason("审核金额大于可申请金额");
                                    throw new BizException("5001", "审核金额大于可申请金额!");
                                }
                            }else {
                                //反审核，则 +
                                amt = apply.getRemainingApplyAmt().add(detailDO.getInvoiceAmtInclTax());
                                qty = apply.getRemainingApplyQty().add(detailDO.getInvoiceQty());
                                readyDetailQty = apply.getReadyQty().subtract(detailDO.getInvoiceQty());
                                readyDetailAmt = apply.getReadyAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                canDetailAmt = apply.getCanAmt().add(detailDO.getInvoiceAmtInclTax());
                                canDetailQty = apply.getCanQty().add(detailDO.getInvoiceQty());
                                //反审核通过后为未开票
                                issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.NOT_INVOICE.type.shortValue());
                            }
                            apply.setReadyQty(readyDetailQty);
                            apply.setReadyAmt(readyDetailAmt);
                            apply.setCanQty(canDetailQty);
                            apply.setCanAmt(canDetailAmt);
                            apply.setRemainingApplyAmt(amt);
                            apply.setRemainingApplyQty(qty);
                            readyAmt = readyAmt.add(readyDetailAmt);
                            readyQty = readyQty.add(readyDetailQty);
                            canAmt = canAmt.add(canDetailAmt);
                            canQty = canQty.add(canDetailQty);
                            list.add(apply);
                            applyDetailIdList.add(detailDO.getInvoiceApplyDetailId());
                            issueMapper.updateRemainIssueAmtAndQty(apply.getInvoiceApplyDetailId(),null, canDetailAmt, canDetailQty);

                            //剩余可开生效
                            detailDO.setRemainingInvoiceQty(detailDO.getInvoiceQty());
                            detailDO.setRemainingInvoiceAmt(detailDO.getInvoiceAmtInclTax());
                            detailDO.setQty(detailDO.getInvoiceQty());
                        }
                        //回写开票申请详情
                        invoiceApplyDetailMapper.updateBatch(list);
                        List<InvoiceApplyDetailDO> invoiceApplyDetailList = invoiceApplyDetailMapper.selectBatchIds(applyDetailIdList);
                        for (InvoiceApplyDetailDO item : invoiceApplyDetailList){
                            invoicePlanDetailId.add(item.getInvoicePlanDetailId());
                        }
                        planDetailList = invoicePlanDetailMapper.selectBatchIds(invoicePlanDetailId);
                        planDetailMap = planDetailList.stream().collect(Collectors.toMap(InvoicePlanDetailDO::getInvoicePlanDetailId, Function.identity(), (s1, s2) -> s1));
                        List<InvoicePlanDetailDO> planDetailDOList = new ArrayList<>();
                        for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                            InvoicePlanDetailDO plan = planDetailMap.get(detailDO.getInvoicePlanDetailId());
                            if (Objects.isNull(plan)){
                                failItem.setCode(issue.getInvoiceCode());
                                failItem.setReason("上游明细的数据异常");
                                throw new BizException("5001", "上游明细的数据异常!");
                            }
                            BigDecimal amt = BigDecimal.ZERO;
                            BigDecimal qty = BigDecimal.ZERO;

                            //已处理数量
                            BigDecimal readyDetailQty = BigDecimal.ZERO;
                            BigDecimal readyDetailAmt = BigDecimal.ZERO;

                            //可处理数量
                            BigDecimal canDetailQty = BigDecimal.ZERO;
                            BigDecimal canDetailAmt = BigDecimal.ZERO;
                            if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
                                //审核，则 -
//                                amt = plan.getRemainingPlanAmt().subtract(detailDO.getInvoiceAmtInclTax());
//                                qty = plan.getRemainingPlanQty().subtract(detailDO.getInvoiceQty());
                                readyDetailQty = plan.getReadyInvoiceQty().add(detailDO.getInvoiceQty());
                                readyDetailAmt = plan.getReadyInvoiceAmt().add(detailDO.getInvoiceAmtInclTax());
                                canDetailAmt = plan.getCanInvoiceAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                canDetailQty = plan.getCanInvoiceQty().subtract(detailDO.getInvoiceQty());
                            }else {
                                //反审核，则 +
//                                amt = plan.getRemainingPlanAmt().add(detailDO.getInvoiceAmtInclTax());
//                                qty = plan.getRemainingPlanQty().add(detailDO.getInvoiceQty());
                                readyDetailQty = plan.getReadyInvoiceQty().subtract(detailDO.getInvoiceQty());
                                readyDetailAmt = plan.getReadyInvoiceAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                canDetailAmt = plan.getCanInvoiceAmt().add(detailDO.getInvoiceAmtInclTax());
                                canDetailQty = plan.getCanInvoiceQty().add(detailDO.getInvoiceQty());
                            }
                            plan.setReadyInvoiceQty(readyDetailQty);
                            plan.setReadyInvoiceAmt(readyDetailAmt);
                            plan.setCanInvoiceQty(canDetailQty);
                            plan.setCanInvoiceAmt(canDetailAmt);
//                            plan.setRemainingPlanAmt(amt);
//                            plan.setRemainingPlanQty(qty);
                            readyInvoiceQty = readyInvoiceQty.add(readyDetailQty);
                            readyInvoiceAmt = readyInvoiceAmt.add(readyDetailAmt);
                            canInvoiceQty = canInvoiceQty.add(canDetailQty);
                            canInvoiceAmt = canInvoiceAmt.add(canDetailAmt);
                            planDetailDOList.add(plan);
                        }
                        //回写开票计划详情
                        invoicePlanDetailMapper.updateBatch(planDetailDOList);
                    }else {
                        List<InvoicePlanDetailDO> list = new ArrayList<>();

                        for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                            InvoicePlanDetailDO plan = planDetailMap.get(detailDO.getInvoicePlanDetailId());

                            if (Objects.isNull(plan)){
                                failItem.setCode(issue.getInvoiceCode());
                                failItem.setReason("上游明细的数据异常");
                                throw new BizException("5001", "上游明细的数据异常!");
                            }
                            BigDecimal amt = BigDecimal.ZERO;
                            BigDecimal qty = BigDecimal.ZERO;

                            //已处理数量
                            BigDecimal readyDetailQty = BigDecimal.ZERO;
                            BigDecimal readyDetailAmt = BigDecimal.ZERO;

                            //可处理数量
                            BigDecimal canDetailQty = BigDecimal.ZERO;
                            BigDecimal canDetailAmt = BigDecimal.ZERO;
                            if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
                                //审核，则 -
                                amt = plan.getRemainingPlanAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                qty = plan.getPlanInvoiceQty().subtract(detailDO.getInvoiceQty());

                                readyDetailQty = plan.getReadyInvoiceQty().add(detailDO.getInvoiceQty());
                                readyDetailAmt = plan.getReadyInvoiceAmt().add(detailDO.getInvoiceAmtInclTax());

                                canDetailAmt = plan.getCanInvoiceAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                canDetailQty = plan.getCanInvoiceQty().subtract(detailDO.getInvoiceQty());
                                //审核通过后为已开票
                                issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.ISSUE_INVOICE.type.shortValue());

                                if (plan.getCanInvoiceAmt().abs().subtract(detailDO.getInvoiceAmtInclTax().abs()).compareTo(BigDecimal.ZERO) < 0){
                                    failItem.setCode(issue.getInvoiceCode());
                                    failItem.setReason("审核金额大于可申请金额");
                                    throw new BizException("5001", "审核金额大于可申请金额!");
                                }
                            }else {
                                //反审核，则 +
                                amt = plan.getRemainingPlanAmt().add(detailDO.getInvoiceAmtInclTax());
                                qty = plan.getPlanInvoiceQty().add(detailDO.getInvoiceQty());
                                readyDetailQty = plan.getReadyInvoiceQty().subtract(detailDO.getInvoiceQty());
                                readyDetailAmt = plan.getReadyInvoiceAmt().subtract(detailDO.getInvoiceAmtInclTax());
                                canDetailAmt = plan.getCanInvoiceAmt().add(detailDO.getInvoiceAmtInclTax());
                                canDetailQty = plan.getCanInvoiceQty().add(detailDO.getInvoiceQty());
                                //反审核通过后为未开票
                                issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.NOT_INVOICE.type.shortValue());
                            }
                            plan.setReadyInvoiceQty(readyDetailQty);
                            plan.setReadyInvoiceAmt(readyDetailAmt);
                            plan.setCanInvoiceQty(canDetailQty);
                            plan.setCanInvoiceAmt(canDetailAmt);

                            plan.setRemainingPlanAmt(amt);
                            plan.setRemainingPlanQty(qty);
                            readyInvoiceQty = readyInvoiceQty.add(readyDetailQty);
                            readyInvoiceAmt = readyInvoiceAmt.add(readyDetailAmt);
                            canInvoiceQty = canInvoiceQty.add(canDetailQty);
                            canInvoiceAmt = canInvoiceAmt.add(canDetailAmt);
                            list.add(plan);
                            issueMapper.updateRemainIssueAmtAndQty(null,plan.getInvoicePlanDetailId(), canDetailAmt, canDetailQty);

                            //剩余可开生效
                            detailDO.setRemainingInvoiceQty(detailDO.getInvoiceQty());
                            detailDO.setRemainingInvoiceAmt(detailDO.getInvoiceAmtInclTax());
                            detailDO.setQty(detailDO.getInvoiceQty());
                        }
                        //回写开票计划详情
                        invoicePlanDetailMapper.updateBatch(list);
                    }
                    invoiceDetailMapper.updateBatch(invoiceDetailDOS);
                    if (issue.getInvoiceApplyId() != null){
                        BigDecimal bigDecimal = invoiceApplyDetailMapper.invoiceApplyQty(issue.getInvoiceApplyId());
                        if (bigDecimal.compareTo(BigDecimal.ZERO) == 0){
                            //已开票
                            invoiceApplyDO.setInvoiceStatus(InvoiceApplyStatusEnum.ISSUE_INVOICE.type.shortValue());
                        }else if (bigDecimal.compareTo(invoiceApplyDO.getApplyInvoiceTotalInclTax()) == 0){
                            invoiceApplyDO.setInvoiceStatus(InvoiceApplyStatusEnum.NOT_INVOICE.type.shortValue());
                        }else {
                            invoiceApplyDO.setInvoiceStatus(InvoiceApplyStatusEnum.PART_INVOICE.type.shortValue());
                        }

                        invoiceApplyDO.setReadyAmt(readyAmt);
                        invoiceApplyDO.setReadyQty(readyQty);
                        invoiceApplyDO.setCanAmt(canAmt);
                        invoiceApplyDO.setCanQty(canQty);
                        invoiceApplyMapper.updateById(invoiceApplyDO);

                        invoicePlanDO.setInvoicePlanId(invoiceApplyDO.getInvoicePlanId());
                        invoicePlanDO.setReadyInvoiceAmt(readyInvoiceAmt);
                        invoicePlanDO.setReadyInvoiceQty(readyInvoiceQty);
                        invoicePlanDO.setCanInvoiceQty(canInvoiceQty);
                        invoicePlanDO.setCanInvoiceAmt(canInvoiceAmt);
                        //更新本币币种及汇率
                        if(invoiceApplyDO.getExchangeRate() != null) {
                            invoicePlanDO.setLocalCurrencyReadyInvoiceAmt(readyInvoiceAmt.multiply(invoiceApplyDO.getExchangeRate()).
                                    setScale(2, RoundingMode.HALF_UP));
                            invoicePlanDO.setLocalCurrencyCanInvoiceAmt(canInvoiceAmt.multiply(invoiceApplyDO.getExchangeRate()).
                                    setScale(2, RoundingMode.HALF_UP));
                        }
                        invoicePlanMapper.updateById(invoicePlanDO);

                    }else {
                        BigDecimal bigDecimal = invoicePlanDetailMapper.invoicePlanQty(issue.getInvoicePlanId());
                        if (bigDecimal.compareTo(BigDecimal.ZERO) == 0){
                            //已开票
                            invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.ISSUE_INVOICE.type.shortValue());
                        }else if (bigDecimal.compareTo(invoicePlanDO.getPlanInvoiceTotalInclTax()) == 0){
                            invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.NOT_INVOICE.type.shortValue());
                        }else {
                            invoicePlanDO.setInvoiceApplyStatus(InvoiceApplyEnum.PART_INVOICE.type.shortValue());
                        }

                        invoicePlanDO.setReadyAmt(readyAmt);
                        invoicePlanDO.setReadyQty(readyQty);
                        invoicePlanDO.setCanAmt(canAmt);
                        invoicePlanDO.setCanQty(canQty);
                        invoicePlanDO.setReadyInvoiceAmt(readyInvoiceAmt);
                        invoicePlanDO.setReadyInvoiceQty(readyInvoiceQty);
                        invoicePlanDO.setCanInvoiceQty(canInvoiceQty);
                        invoicePlanDO.setCanInvoiceAmt(canInvoiceAmt);
                        //更新本币币种及汇率
                        if(invoicePlanDO.getExchangeRate() != null) {
                            invoicePlanDO.setLocalCurrencyReadyInvoiceAmt(readyInvoiceAmt.multiply(invoicePlanDO.getExchangeRate()).
                                    setScale(2, RoundingMode.HALF_UP));
                            invoicePlanDO.setLocalCurrencyCanInvoiceAmt(canInvoiceAmt.multiply(invoicePlanDO.getExchangeRate()).
                                    setScale(2, RoundingMode.HALF_UP));
                        }
                        invoicePlanMapper.updateById(invoicePlanDO);
                    }


                    //进入应收/应付账款
                    genInvoicePlan(issue, invoiceDetailDOS, buttonType);
                    //issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.ISSUE_INVOICE.type.shortValue());
                }else {
                    //红冲发票
                    //红冲金额,一定是负数
                    BigDecimal bigDecimal = issueMapper.redInvoiceAmt(issue.getRedInvoiceId());
                    if (bigDecimal == null){
                        bigDecimal = BigDecimal.ZERO;
                    }
                    InvoiceIssueDO invoiceIssueDO = issueMapper.selectById(issue.getRedInvoiceId());

                    //获取上级蓝字明细，需要修改剩余可红冲数量
                    List<InvoiceDetailDO> invoiceDetailList = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>()
                            .eq(InvoiceDetailDO::getInvoiceId, invoiceIssueDO.getInvoiceId())
                    );
                    Map<Long, InvoiceDetailDO> mapDetail = invoiceDetailList.stream().collect(Collectors.toMap(InvoiceDetailDO::getInvoiceDetailId, Function.identity(), (s1, s2) -> s1));
                    //查询红冲的明细
                    List<InvoiceDetailDO> invoiceDetailDOS = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>().eq(InvoiceDetailDO::getInvoiceId, issue.getInvoiceId()));
                    List<InvoiceDetailDO> listDetail = new ArrayList<>();
                    for (InvoiceDetailDO detailDO : invoiceDetailDOS){
                        InvoiceDetailDO upDetail = mapDetail.get(detailDO.getInvoiceApplyDetailId());
                        if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
                            upDetail.setRemainingInvoiceAmt(upDetail.getRemainingInvoiceAmt().subtract(detailDO.getInvoiceAmtInclTax()));
                            upDetail.setRemainingInvoiceQty(upDetail.getRemainingInvoiceQty().subtract(detailDO.getInvoiceQty()));
                            listDetail.add(upDetail);
                        }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()){
                            upDetail.setRemainingInvoiceAmt(upDetail.getRemainingInvoiceAmt().add(detailDO.getInvoiceAmtInclTax()));
                            upDetail.setRemainingInvoiceQty(upDetail.getRemainingInvoiceQty().add(detailDO.getInvoiceQty()));
                            listDetail.add(upDetail);
                        }
                    }
                    //修改明细
                    if (CollectionUtils.isNotEmpty(listDetail)){
                        invoiceDetailMapper.updateBatch(listDetail);
                    }
                    if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()){
                        //审核
                        if (invoiceIssueDO.getInvoiceTotalTax().add(bigDecimal.add(issue.getInvoiceTotalTax())).compareTo(BigDecimal.ZERO) == 0){
                            //已红冲
                            invoiceIssueDO.setInvoiceIssueStatus(InvoiceIssueStatusEnum.ALL_RED.type.shortValue());
                        }else{
                            //部分红冲
                            invoiceIssueDO.setInvoiceIssueStatus(InvoiceIssueStatusEnum.PART_RED.type.shortValue());
                        }
                        //红冲发票为已开票
                        issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.ISSUE_INVOICE.type.shortValue());
                        issueMapper.updateById(invoiceIssueDO);
//                        issue.setApprovedBy(loginUser.getFullUserName());
//                        issue.setDataStatus(dataStatus.shortValue());
//                        issue.setApprovedDt(LocalDateTime.now());
//                        issueMapper.updateById(issue);
                    }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()){
                        //如果红冲金额等于当前开票金额则为已开票
                        if (bigDecimal.compareTo(issue.getInvoiceTotalTax()) == 0){
                            invoiceIssueDO.setInvoiceIssueStatus(InvoiceIssueStatusEnum.ISSUE_INVOICE.type.shortValue());
                        }else {
                            //部分红冲
                            invoiceIssueDO.setInvoiceIssueStatus(InvoiceIssueStatusEnum.PART_RED.type.shortValue());
                        }

                        //反审核修改状态
                        //issueMapper.deleteById(id);
                        //invoiceDetailMapper.delete(new LambdaQueryWrapper<InvoiceDetailDO>().eq(InvoiceDetailDO::getInvoiceId, id));
                        issueMapper.updateById(invoiceIssueDO);
                        issue.setInvoiceIssueStatus(InvoiceIssueStatusEnum.NOT_INVOICE.type.shortValue());

                       // return 1;
                    }

                    //进入应收/应付账款
                    List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(new LambdaQueryWrapper<InvoiceDetailDO>().eq(InvoiceDetailDO::getInvoiceId, issue.getInvoiceId()));
                    genInvoicePlan(issue, detailDOList, buttonType);
                }

            }
        } catch (Exception e) {
            failItem.setCode(issue.getInvoiceCode());
            failItem.setReason("审核开票异常");
            throw e;
        }
        issue.setApprovedBy(loginUser.getFullUserName());
        issue.setDataStatus(dataStatus.shortValue());
        issue.setApprovedDt(LocalDateTime.now());
        return issueMapper.updateById(issue);
    }

    /**
     * todo 审核生成，反审删除 待开票计划
     * @param exist
     * @param detailDOS
     */
    private void genInvoicePlan(InvoiceIssueDO exist, List<InvoiceDetailDO> detailDOS, Integer buttonType) {
        if(exist.getInvoiceAmt().compareTo(BigDecimal.ZERO) == 0){
            return;
        }
        if (StringUtils.isEmpty(exist.getCollectionPlanStrategy())){
            return;
        }
        StrategyModelVO modelVO = new StrategyModelVO();
        // 开票金额正：收款，否则：退款
        if(exist.getInvoiceAmt().compareTo(BigDecimal.ZERO) > 0) {
            modelVO.setStrategyType(StrategyEnum.RECEIVE_PAYMENT.type);
        }else{
            modelVO.setStrategyType(StrategyEnum.REFUND_PAYMENT.type);
        }
        // 审核 做新增，反审 做删除
        modelVO.setButtonType(buttonType);

        PendingPaymentPlanAditReqVO aditReqVO = new PendingPaymentPlanAditReqVO();
        aditReqVO.setDetails(new ArrayList<>());

        aditReqVO.setSourceOrderId(exist.getInvoiceId());
        aditReqVO.setSourceOrderCode(exist.getInvoiceCode());
        if (exist.getBillingDirection() == 0) {// 开票
            //aditReqVO.setInvoicingPlanStrategy(StrategyConfigEnum.TYPE_004.getKey());
            // todo 新增单据枚举，前端也要补充 INVOICE_ISSUE1
            aditReqVO.setSourceFormType(OrderTypeEnum.INVOICE_ISSUE1.getType().shortValue());
            aditReqVO.setFormType((short) 1);

            modelVO.setSourceFormType(OrderTypeEnum.INVOICE_ISSUE1.getType().shortValue());
            //modelVO.setStrategy(exist.getCollectionPlanStrategy());
        } else {// 收票
            //aditReqVO.setInvoicingPlanStrategy(StrategyConfigEnum.TYPE_404.getKey());
            aditReqVO.setSourceFormType(OrderTypeEnum.INVOICE_ISSUE2.getType().shortValue());
            aditReqVO.setFormType((short) 2);

            modelVO.setSourceFormType(OrderTypeEnum.INVOICE_ISSUE2.getType().shortValue());

        }
        modelVO.setStrategy(exist.getCollectionPlanStrategy());
        //aditReqVO.setFormDt(LocalDateTime.now());
        aditReqVO.setCustomerId(exist.getCustomerId());//todo 没有客户/供应商id
        aditReqVO.setCurrencyDictId(exist.getCurrencyDictId());
        aditReqVO.setTotalAmt(exist.getInvoiceAmt());
        aditReqVO.setPlanAmt(BigDecimal.ZERO);
        List<PendingPaymentPlanDetailAditReqVO> detailList = new ArrayList<>();
        for (InvoiceDetailDO detailDO : detailDOS) {
            PendingPaymentPlanDetailAditReqVO paymentDetail = new PendingPaymentPlanDetailAditReqVO();
            if (exist.getBillingDirection() == BillingDirectionEnum.ISSUE_INVOICE.type.shortValue()) {
                paymentDetail.setFormType((short) 1);
                paymentDetail.setSourceFormType(OrderTypeEnum.INVOICE_ISSUE1.type.shortValue());
            }else {
                paymentDetail.setFormType((short) 2);
                paymentDetail.setSourceFormType(OrderTypeEnum.INVOICE_ISSUE2.type.shortValue());
            }

            paymentDetail.setSourceOrderCode(exist.getInvoiceCode());
            paymentDetail.setSourceLineNumber(detailDO.getRowNo().shortValue());
            paymentDetail.setSourceOrderId(exist.getInvoiceId());
            paymentDetail.setMaterialId(detailDO.getMaterialId());
            paymentDetail.setMaterialCode(detailDO.getMaterialCode());
            paymentDetail.setMainUnitDictId(detailDO.getMainUnitDictId());
            //paymentDetail.setMainUnitDictName(detailDO.getMainUnitDictName());

            if (exist.getRedInvoiceId() != null) {
                paymentDetail.setSourceDocumentQty(detailDO.getInvoiceQty().negate());
                paymentDetail.setInclTaxAmt(detailDO.getInvoiceQty().multiply(detailDO.getInclTaxUnitPrice()).negate().setScale(2, RoundingMode.HALF_UP));
                paymentDetail.setExclTaxAmt(detailDO.getInvoiceQty().multiply(detailDO.getExclTaxUnitPrice()).negate().setScale(2, RoundingMode.HALF_UP));
            }else {
                paymentDetail.setSourceDocumentQty(detailDO.getInvoiceQty());
                paymentDetail.setInclTaxAmt(detailDO.getInvoiceQty().multiply(detailDO.getInclTaxUnitPrice()).setScale(2, RoundingMode.HALF_UP));
                paymentDetail.setExclTaxAmt(detailDO.getInvoiceQty().multiply(detailDO.getExclTaxUnitPrice()).setScale(2, RoundingMode.HALF_UP));
            }
            paymentDetail.setInclTaxUnitPrice(detailDO.getInclTaxUnitPrice());
            paymentDetail.setExclTaxUnitPrice(detailDO.getExclTaxUnitPrice());
            paymentDetail.setInvoiceTypeId(detailDO.getInvoiceTypeDictId());
            paymentDetail.setInvoiceTypeName(detailDO.getInvoiceTypeDictName());
            paymentDetail.setTaxRate(detailDO.getTaxRate());

            if (exist.getRedInvoiceId() != null) {
                paymentDetail.setTotalAmt(paymentDetail.getInclTaxAmt().negate());
            }else {
                paymentDetail.setTotalAmt(paymentDetail.getInclTaxAmt());
            }

            detailList.add(paymentDetail);
        }
        aditReqVO.setDetails(detailList);
        aditReqVO.setDirectorOrgId(exist.getDirectorOrgId());
        aditReqVO.setDirectorId(exist.getDirectorId());
        //更新本币币种及汇率
        aditReqVO.setLocalCurrencyDictId(exist.getLocalCurrencyDictId());
        aditReqVO.setExchangeRate(exist.getExchangeRate());

        modelVO.setPaymentVO(aditReqVO);
        strategyModelService.insertModelData(modelVO);
    }
}
