package com.mongoso.mgs.module.sale.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.sale.dal.db.customerpriceplan.CustomerPricePlanDO;
import org.springframework.stereotype.Component;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:40
 * @description: 客户价格方案回调处理类
 */

@Component
public class CustomerPricePlanFlowCallBackHandler extends FlowCallbackHandler<CustomerPricePlanDO> {

    protected CustomerPricePlanFlowCallBackHandler(FlowApproveHandler<CustomerPricePlanDO> approveHandler) {
        super(approveHandler);
    }

}
