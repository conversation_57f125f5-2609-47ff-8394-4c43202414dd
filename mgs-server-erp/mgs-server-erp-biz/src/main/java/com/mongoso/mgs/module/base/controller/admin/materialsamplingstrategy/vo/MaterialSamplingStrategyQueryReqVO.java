package com.mongoso.mgs.module.base.controller.admin.materialsamplingstrategy.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 

/**
 * 抽检策略 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialSamplingStrategyQueryReqVO {

    /** 主键 */
    private Long strategyId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 创建人ID */
    private Long createdId;

    /** 排序 */
    private Short sort;

    /** 抽检区间 */
    private BigDecimal checkInterval;

    /** 抽检数量 */
    private BigDecimal checkQty;

    /** 允许缺陷数 */
    private BigDecimal allowDefectQty;

    /** 抽检比例 */
    private BigDecimal checkRatio;

    /** 允许缺陷数比例 */
    private BigDecimal allowDefectRatio;

    /** 物料外键 */
    private Long materialFkId;

    /** 行号 */
    private Integer rowNo;

}
