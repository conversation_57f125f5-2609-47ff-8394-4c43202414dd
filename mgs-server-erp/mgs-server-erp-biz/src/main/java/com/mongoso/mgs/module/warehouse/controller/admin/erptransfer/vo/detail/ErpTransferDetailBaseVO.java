package com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.detail;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 
/**
 * 调拨单明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ErpTransferDetailBaseVO implements Serializable {

    /** 调拨单明细ID */
    private Long transferDetailId;

    /** 行号 */
    private Long rowNo;

    /** 调拨单ID */
    private Long transferId;

    /** 调拨单号 */
    private String transferCode;

    /** 物料库存ID */
    private Long materialStockId;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 调拨数量 */
    private BigDecimal transferQty;

    /** 已调出数量 */
    private BigDecimal transferredOutQty;

    /** 已调入数量 */
    private BigDecimal transferredInQty;

    /** 备注 */
    private String remark;

}
