package com.mongoso.mgs.module.sale.dal.db.salededuction;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 销售扣费单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_sale_deduction", autoResultMap = true)
//@KeySequence("u_sale_deduction_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaleDeductionDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long deductionOrderId;

    /** 销售扣费单号 */
    private String deductionOrderCode;

    /** 销售订单号 */
    private String saleOrderCode;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 销售订单id */
    private Long saleOrderId;

    /** 销售订单类型 */
    private String salesOrderTypeDictId;

    /** 关联客户 */
    private Long customerId;

    /** 交货日期 */
    private LocalDate deliveryDate;

    /** 币种id */
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 扣费金额 */
    private BigDecimal deductionAmt;

    /** 票据类型id */
    private Long invoiceTypeId;

    /** 票据类型名称 */
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 审核状态 */
    private Integer dataStatus;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 退款条件 */
    private String refunConditionDictId;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
     private String directorOrgId;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    /** 公司主体 */
    private String companyOrgId;

    /** 版本号 */
//    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币扣费总金额 */
    private BigDecimal localCurrencyDeductionAmt;
}
