package com.mongoso.mgs.module.salary.service.ployrelatedemployee;

import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeAditReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeePageReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeQueryReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeRespVO;
import com.mongoso.mgs.module.salary.dal.db.ployrelatedemployee.PloyRelatedEmployeeDO;
import com.mongoso.mgs.module.salary.dal.mysql.ployrelatedemployee.PloyRelatedEmployeeMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.ploy.enums.ErrorCodeConstants.*;


/**
 * 策略关联人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PloyRelatedEmployeeServiceImpl implements PloyRelatedEmployeeService {

    @Resource
    private PloyRelatedEmployeeMapper relatedEmployeeMapper;

    @Override
    public Long ployRelatedEmployeeAdd(PloyRelatedEmployeeAditReqVO reqVO) {
        // 插入
        PloyRelatedEmployeeDO relatedEmployee = BeanUtilX.copy(reqVO, PloyRelatedEmployeeDO::new);
        relatedEmployeeMapper.insert(relatedEmployee);
        // 返回
        return relatedEmployee.getPloyRelatedEmployeeId();
    }

    @Override
    public Long ployRelatedEmployeeEdit(PloyRelatedEmployeeAditReqVO reqVO) {
        // 校验存在
        this.ployRelatedEmployeeValidateExists(reqVO.getPloyRelatedEmployeeId());
        // 更新
        PloyRelatedEmployeeDO relatedEmployee = BeanUtilX.copy(reqVO, PloyRelatedEmployeeDO::new);
        relatedEmployeeMapper.updateById(relatedEmployee);
        // 返回
        return relatedEmployee.getPloyRelatedEmployeeId();
    }

    @Override
    public void ployRelatedEmployeeDelete(Long ployRelatedEmployeeId) {
        // 校验存在
        this.ployRelatedEmployeeValidateExists(ployRelatedEmployeeId);
        // 删除
        relatedEmployeeMapper.deleteById(ployRelatedEmployeeId);
    }

    private PloyRelatedEmployeeDO ployRelatedEmployeeValidateExists(Long ployRelatedEmployeeId) {
        PloyRelatedEmployeeDO relatedEmployee = relatedEmployeeMapper.selectById(ployRelatedEmployeeId);
        if (relatedEmployee == null) {
            // throw exception(RELATED_EMPLOYEE_NOT_EXISTS);
            throw new BizException("5001", "策略关联人员不存在");
        }
        return relatedEmployee;
    }

    @Override
    public PloyRelatedEmployeeRespVO ployRelatedEmployeeDetail(Long ployRelatedEmployeeId) {
        PloyRelatedEmployeeDO data = relatedEmployeeMapper.selectById(ployRelatedEmployeeId);
        return BeanUtilX.copy(data, PloyRelatedEmployeeRespVO::new);
    }

    @Override
    public List<PloyRelatedEmployeeRespVO> ployRelatedEmployeeList(PloyRelatedEmployeeQueryReqVO reqVO) {
        List<PloyRelatedEmployeeDO> data = relatedEmployeeMapper.selectList(reqVO);
        return BeanUtilX.copy(data, PloyRelatedEmployeeRespVO::new);
    }

    @Override
    public PageResult<PloyRelatedEmployeeRespVO> ployRelatedEmployeePage(PloyRelatedEmployeePageReqVO reqVO) {
        PageResult<PloyRelatedEmployeeDO> data = relatedEmployeeMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, PloyRelatedEmployeeRespVO::new);
    }

}
