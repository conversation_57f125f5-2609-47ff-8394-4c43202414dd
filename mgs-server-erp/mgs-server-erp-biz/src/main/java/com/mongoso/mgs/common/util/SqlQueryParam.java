package com.mongoso.mgs.common.util;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/11/23 15:24
 * @description: SQL参数查询
 */

@Data
@Builder
public class SqlQueryParam<T, R> {

    private Class<T> clazz;                       // 主实体类
    private Class<R> detailClazz;                 // 详情实体类

    private SFunction<T, Object> joinFieldName1;      // 连接字段1
    private SFunction<R, Object> joinFieldName2;      // 连接字段2

    private SFunction<R, Object> qryFieldIdName;      // 查询字段ID
    private SFunction<R, Object> qryAggFieldName;     // 聚合字段

    private SFunction<T, Object> condFieldIdName;     // 引用单据id字段名称
    private SFunction<R, Object> condExcludeFieldName; // 排除的ID集合的字段名称

    private Long relatedOrderId;                  // 引用单据id值
    private List<Long> excludeIdList;             // 排除的ID集合值

    // Validate method to ensure parameters are valid
    public void validate() {
        if (clazz == null || detailClazz == null) {
            throw new IllegalArgumentException("Classes cannot be null");
        }
    }
}
