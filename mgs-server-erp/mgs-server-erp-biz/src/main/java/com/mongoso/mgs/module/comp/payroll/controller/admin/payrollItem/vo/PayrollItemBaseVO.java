package com.mongoso.mgs.module.comp.payroll.controller.admin.payrollItem.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工资单明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PayrollItemBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工资单ID */
    @NotNull(message = "工资单ID不能为空")
    private Long payrollId;

    /** 工资单成员主键ID */
    @NotNull(message = "工资单成员主键ID不能为空")
    private Long payrollMemberPkId;

    /** 工资单成员ID */
    @NotNull(message = "工资单成员ID不能为空")
    private Long payrollMemberId;

    /** 薪酬项目ID */
    @NotNull(message = "薪酬项目ID不能为空")
    private Long emolumentItemId;

    /** 薪酬项目主键ID */
    @NotNull(message = "薪酬项目ID不能为空")
    private Long compensationItemId;

    /** 金额 */
    @NotNull(message = "金额不能为空")
    private BigDecimal amt;

}
