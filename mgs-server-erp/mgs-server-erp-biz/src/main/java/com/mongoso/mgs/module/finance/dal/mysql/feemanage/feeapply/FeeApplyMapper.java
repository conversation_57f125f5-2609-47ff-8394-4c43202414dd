package com.mongoso.mgs.module.finance.dal.mysql.feemanage.feeapply;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeapply.vo.FeeApplyPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.feemanage.feeapply.vo.FeeApplyQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.feemanage.feeapply.FeeApplyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 费用申请 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FeeApplyMapper extends BaseMapperX<FeeApplyDO> {

    default PageResult<FeeApplyDO> selectPageOld(FeeApplyPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<FeeApplyDO>lambdaQueryX()
                .likeIfPresent(FeeApplyDO::getFeeApplyCode, reqVO.getFeeApplyCode())
                .likeIfPresent(FeeApplyDO::getApplyName, reqVO.getApplyName())
                .eqIfPresent(FeeApplyDO::getApplyTotalAmt, reqVO.getApplyTotalAmt())
                .eqIfPresent(FeeApplyDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeApplyDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeApplyDO::getApplierId, reqVO.getApplierId())
                .eqIfPresent(FeeApplyDO::getIsIssueLoan, reqVO.getIsIssueLoan())
                .eqIfPresent(FeeApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(FeeApplyDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeApplyDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeApplyDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeApplyDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeApplyDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeApplyDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeApplyDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(FeeApplyDO::getCreatedDt));
    }



    default PageResult<FeeApplyDO> selectPage(FeeApplyPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<FeeApplyDO>lambdaQueryX()
                .likeIfPresent(FeeApplyDO::getFeeApplyCode, reqVO.getFeeApplyCode())
                .likeIfPresent(FeeApplyDO::getApplyName, reqVO.getApplyName())
                .eqIfPresent(FeeApplyDO::getApplyTotalAmt, reqVO.getApplyTotalAmt())
                .eqIfPresent(FeeApplyDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeApplyDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeApplyDO::getApplierId, reqVO.getApplierId())
                .eqIfPresent(FeeApplyDO::getIsIssueLoan, reqVO.getIsIssueLoan())
                .eqIfPresent(FeeApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(FeeApplyDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeApplyDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeApplyDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeApplyDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeApplyDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeApplyDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeApplyDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(FeeApplyDO::getCreatedDt));
    }

    default List<FeeApplyDO> selectListOld(FeeApplyQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<FeeApplyDO>lambdaQueryX()
                .likeIfPresent(FeeApplyDO::getFeeApplyCode, reqVO.getFeeApplyCode())
                .likeIfPresent(FeeApplyDO::getApplyName, reqVO.getApplyName())
                .eqIfPresent(FeeApplyDO::getApplyTotalAmt, reqVO.getApplyTotalAmt())
                .eqIfPresent(FeeApplyDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeApplyDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeApplyDO::getApplierId, reqVO.getApplierId())
                .eqIfPresent(FeeApplyDO::getIsIssueLoan, reqVO.getIsIssueLoan())
                .eqIfPresent(FeeApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(FeeApplyDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeApplyDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeApplyDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeApplyDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeApplyDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeApplyDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeApplyDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(FeeApplyDO::getCreatedDt));
    }

    default List<FeeApplyDO> selectList(FeeApplyQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<FeeApplyDO>lambdaQueryX()
                .likeIfPresent(FeeApplyDO::getFeeApplyCode, reqVO.getFeeApplyCode())
                .likeIfPresent(FeeApplyDO::getApplyName, reqVO.getApplyName())
                .eqIfPresent(FeeApplyDO::getApplyTotalAmt, reqVO.getApplyTotalAmt())
                .eqIfPresent(FeeApplyDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(FeeApplyDO::getFeeType, reqVO.getFeeType())
                .eqIfPresent(FeeApplyDO::getApplierId, reqVO.getApplierId())
                .eqIfPresent(FeeApplyDO::getIsIssueLoan, reqVO.getIsIssueLoan())
                .eqIfPresent(FeeApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(FeeApplyDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(FeeApplyDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(FeeApplyDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(FeeApplyDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(FeeApplyDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(FeeApplyDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(FeeApplyDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(FeeApplyDO::getCreatedDt));
    }

}