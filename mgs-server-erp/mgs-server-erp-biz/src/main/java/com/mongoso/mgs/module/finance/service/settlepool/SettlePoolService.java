package com.mongoso.mgs.module.finance.service.settlepool;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.settlepool.vo.SettlePoolRespVO;
import com.mongoso.mgs.module.finance.dal.db.settlepool.SettlePoolDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 结算池 Service 接口
 *
 * <AUTHOR>
 */
public interface SettlePoolService {

    /**
     * 创建结算池
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long settlePoolAdd(@Valid SettlePoolAditReqVO reqVO);
    SettlePoolDO addPool(SettlePoolAditReqVO reqVO);
    /**
     * 批量创建结算池
     * @param reqList
     * @return
     */
    Boolean settlePoolSave(@Valid List<SettlePoolAditReqVO> reqList);

    /**
     * 更新结算池
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long settlePoolEdit(@Valid SettlePoolAditReqVO reqVO);

    /**
     * 删除结算池
     *
     * @param id 编号
     */
    void settlePoolDel(Long id);

    void settlePoolDelBySourceOrderId(Long sourceOrderId);

    boolean checkOrder(Long sourceOrderId);

    /**
     * 获得结算池信息
     *
     * @param id 编号
     * @return 结算池信息
     */
    SettlePoolRespVO settlePoolDetail(Long id);

    /**
     * 获得结算池列表
     *
     * @param reqVO 查询条件
     * @return 结算池列表
     */
    List<SettlePoolRespVO> settlePoolList(@Valid SettlePoolQueryReqVO reqVO);

    /**
     * 获得结算池分页
     *
     * @param reqVO 查询条件
     * @return 结算池分页
     */
    PageResult<SettlePoolRespVO> settlePoolPage(@Valid SettlePoolPageReqVO reqVO);

}
