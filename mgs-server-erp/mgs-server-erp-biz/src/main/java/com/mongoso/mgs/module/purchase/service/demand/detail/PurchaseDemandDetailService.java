package com.mongoso.mgs.module.purchase.service.demand.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购需求明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseDemandDetailService {

    /**
     * 创建采购需求明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseDemandDetailAdd(@Valid PurchaseDemandDetailAditReqVO reqVO);

    /**
     * 更新采购需求明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseDemandDetailEdit(@Valid PurchaseDemandDetailAditReqVO reqVO);

    /**
     * 删除采购需求明细
     *
     * @param purchaseDemandDetailId 编号
     */
    void purchaseDemandDetailDel(Long purchaseDemandDetailId);

    /**
     * 获得采购需求明细信息
     *
     * @param purchaseDemandDetailId 编号
     * @return 采购需求明细信息
     */
    PurchaseDemandDetailRespVO purchaseDemandDetailDetail(Long purchaseDemandDetailId);

    /**
     * 获得采购需求明细列表
     *
     * @param reqVO 查询条件
     * @return 采购需求明细列表
     */
    List<PurchaseDemandDetailRespVO> purchaseDemandDetailList(@Valid PurchaseDemandDetailQueryReqVO reqVO);

    /**
     * 销售采购需求关联销售单明细列表
     *
     * @param reqVO 编号
     * @return 采购需求信息
     */
    List<PurchaseDemandDetailRespVO> demandDetailListForPurchase(PurchaseDemandDetailQueryReqVO reqVO);

    /**
     * 获得采购需求明细分页
     *
     * @param reqVO 查询条件
     * @return 采购需求明细分页
     */
    PageResult<PurchaseDemandDetailRespVO> purchaseDemandDetailPage(@Valid PurchaseDemandDetailPageReqVO reqVO);

}
