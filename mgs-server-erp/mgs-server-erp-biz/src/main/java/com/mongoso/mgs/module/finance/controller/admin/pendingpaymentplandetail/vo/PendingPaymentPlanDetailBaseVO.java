package com.mongoso.mgs.module.finance.controller.admin.pendingpaymentplandetail.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 待收款计划明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PendingPaymentPlanDetailBaseVO implements Serializable {

    /** 计划详情主键ID */
    private Long planDetailId;
    /** 行号 */
    private Integer rowNo;
    /** 单据类型 */
    @NotNull(message = "单据类型不能为空")
    private Short formType;

    /** 计划单id */
//    @NotNull(message = "计划单id不能为空")
    private Long planId;

    /** 对账单明细主键ID，如果来源对账单 */
    private Long accountDetailId;

    /** 来源单据类型 */
    @NotNull(message = "来源单据类型不能为空")
    private Short sourceFormType;

    /** 来源单号 */
    @NotEmpty(message = "来源单号不能为空")
    private String sourceOrderCode;

    /** 来源单行号 */
    @NotNull(message = "来源单行号不能为空")
    private Short sourceLineNumber;

    /** 来源单id */
    @NotNull(message = "来源单id不能为空")
    private Long sourceOrderId;

    /** 物料id */
    @NotNull(message = "物料id不能为空")
    private Long materialId;

    /** 物料编码 */
    @NotEmpty(message = "物料编码不能为空")
    private String materialCode;

    /** 主单位ID */
    @NotNull(message = "主单位ID不能为空")
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 来源单据数量 */
    @NotNull(message = "来源单据数量不能为空")
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    @NotNull(message = "单价(不含税）不能为空")
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    @NotNull(message = "票据类型不能为空")
    private Long invoiceTypeId;
    @NotEmpty(message = "票据类型不能为空")
    private String invoiceTypeName;

    /** 税率 */
    @NotNull(message = "税率不能为空")
    private BigDecimal taxRate;

    /** 单价(含税） */
    @NotNull(message = "单价(含税）不能为空")
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    @NotNull(message = "行金额(不含税)不能为空")
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    @NotNull(message = "行金额(含税)不能为空")
    private BigDecimal inclTaxAmt;

    /** 订单总金额 */
    @NotNull(message = "订单总金额不能为空")
    private BigDecimal totalAmt;
    /** 剩余应收数量 */
    //@NotNull(message = "剩余应收数量不能为空")
    private BigDecimal receiveableQty;

    /**
     * 剩余应收金额
     */
//    private BigDecimal amountPayable;
            //@NotNull(message = "剩余应收金额不能为空")
    private BigDecimal receiveableAmt;
    /**
     * 已收数量
     */
    private BigDecimal receivedQty;
    /**
     * 已收金额
     */
    private BigDecimal receivedAmt;


}
