package com.mongoso.mgs.module.warehouse.service.erpinventory;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.*;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 盘点单 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpInventoryService {

    /**
     * 创建盘点单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpInventoryAdd(@Valid ErpInventoryAditReqVO reqVO);

    /**
     * 更新盘点单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpInventoryEdit(@Valid ErpInventoryAditReqVO reqVO);

    /**
     * 更新盘点单状态
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpInventoryStatusEdit(ErpInventoryAditReqVO reqVO);

    /**
     * 更新盘点单调整状态
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpInventoryAdjustStatusEdit(ErpInventoryAditReqVO reqVO);

    /**
     * 删除盘点单
     *
     * @param inventoryId 编号
     */
    void erpInventoryDel(Long inventoryId);

    /**
     * 批量删除盘点单
     *
     * @param reqVO 删除信息
     */
    ResultX<BatchResult> erpInventoryDelBatch(IdsReqVO reqVO);

    /**
     * 获得盘点单信息
     *
     * @param inventoryId 编号
     * @return 盘点单信息
     */
    ErpInventoryRespVO erpInventoryDetail(ErpInventoryPrimaryReqVO inventoryId);

    /**
     * 获得盘点单列表
     *
     * @param reqVO 查询条件
     * @return 盘点单列表
     */
    List<ErpInventoryRespVO> erpInventoryList(@Valid ErpInventoryQueryReqVO reqVO);

    /**
     * 获得盘点单分页
     *
     * @param reqVO 查询条件
     * @return 盘点单分页
     */
    PageResult<ErpInventoryRespVO> erpInventoryPage(@Valid ErpInventoryPageReqVO reqVO);

    /**
     * 审核盘点单
     *
     * @param reqVO 查询条件
     * @return 审核结果
     */
    BatchResult erpInventoryApprove(FlowApprove reqVO);

    /**
     * 审核盘点单回调
     *
     * @param reqVO 审核信息
     * @return 回调结果
     */
    Object erpInventoryFlowCallback(FlowCallback reqVO);

    /**
     * 生成盘点单
     */
    void erpInventoryPlanGenTask();

    List<ErpInventoryDO> forewarnJob(Integer dataStatus, List<Integer> statusList);
}
