package com.mongoso.mgs.module.purchase.service.processout.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.processout.bo.PurchaseProcessOutBO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 工序委外采购订单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseProcessOutDetailService {

    /**
     * 创建工序委外采购订单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseProcessOutDetailAdd(@Valid PurchaseProcessOutDetailAditReqVO reqVO);

    /**
     * 更新工序委外采购订单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseProcessOutDetailEdit(@Valid PurchaseProcessOutDetailAditReqVO reqVO);

    /**
     * 删除工序委外采购订单明细
     *
     * @param processOutDetailId 编号
     */
    void purchaseProcessOutDetailDel(Long processOutDetailId);

    /**
     * 获得工序委外采购订单明细信息
     *
     * @param processOutDetailId 编号
     * @return 工序委外采购订单明细信息
     */
    PurchaseProcessOutDetailRespVO purchaseProcessOutDetailDetail(Long processOutDetailId);

    /**
     * 获得工序委外采购订单明细列表
     *
     * @param reqVO 查询条件
     * @return 工序委外采购订单明细列表
     */
    List<PurchaseProcessOutDetailRespVO> purchaseProcessOutDetailList(@Valid PurchaseProcessOutDetailQueryReqVO reqVO);

    /**
     * 获得工序委外采购订单明细分页
     *
     * @param reqVO 查询条件
     * @return 工序委外采购订单明细分页
     */
    PageResult<PurchaseProcessOutDetailRespVO> purchaseProcessOutDetailPage(@Valid PurchaseProcessOutDetailPageReqVO reqVO);

    /**
     * 更新工序委外采购订单
     *
     * @param purchaseProcessOutBO
     */
    void updateReceiptedByReceipt(PurchaseProcessOutBO purchaseProcessOutBO);
}
