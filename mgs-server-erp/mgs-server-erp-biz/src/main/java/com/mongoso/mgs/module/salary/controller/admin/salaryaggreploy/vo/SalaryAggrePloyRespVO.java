package com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.vo;

import com.mongoso.mgs.module.salary.controller.admin.salaryaggreploy.employee.PloyRelatedEmployeeRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
 


/**
 * 工资单归集策略 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SalaryAggrePloyRespVO extends SalaryAggrePloyBaseVO {

    /** 审核状态 */
    private String dataStatusDictName;

    /** 责任人姓名 */
    private String directorName;

    /** 责任部门名称 */
    private String directorOrgName;

//    private List<PloyRelatedEmployeeRespVO> employeeList;
    private List<Map<String, Object>> confList;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 审核人 */
    private String approvedBy;

    /** 审批任务id */
    private Long approveTaskId;
}
