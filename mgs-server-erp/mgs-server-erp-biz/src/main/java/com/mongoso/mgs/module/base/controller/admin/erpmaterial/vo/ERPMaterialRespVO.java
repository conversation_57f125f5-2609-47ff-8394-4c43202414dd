package com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.module.base.service.erpsupplier.bo.ERPSupplierBO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 物料 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ERPMaterialRespVO extends ERPMaterialBaseVO {

    /** 品牌 */
    private String brandDictName;

    /** 不可变动值 */
    private BigDecimal nonChangeValue;

    /** 可采购数量 */
    private BigDecimal purchaseableQty;

    /** 物料来源 */
    private String materialSourceDictName;

    /** 是否可编辑基本单位 */
    private Integer isChangeMainUnit;

    /** 物料类型 */
    private String materialTypeDictName;

    /** 上架状态 */
    private String publishStatusDictName;

    /** 审核状态 */
    private String dataStatusDictName;

    /** 责任人 */
    private String directorName;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    private List<ERPSupplierBO> materialSupplierList;

    /** 客户物料编码 */
    private String customerMaterialCode;

    /** 客户物料名称 */
    private String customerMaterialName;

    /** 审批任务id */
    private Long approveTaskId;

    /** 行号 */
    private Integer rowNo;
}
