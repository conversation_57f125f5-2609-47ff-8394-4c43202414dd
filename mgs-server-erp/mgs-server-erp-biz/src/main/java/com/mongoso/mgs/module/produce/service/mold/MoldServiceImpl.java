package com.mongoso.mgs.module.produce.service.mold;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.produce.DictToEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.dal.db.approve.ApproveTaskDO;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.CustAndSupplierReq;
import com.mongoso.mgs.module.base.service.erpbase.bo.CustAndSupplierResp;
import com.mongoso.mgs.module.produce.controller.admin.mold.vo.mold.MoldAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.mold.vo.mold.MoldPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.mold.vo.mold.MoldQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.mold.vo.mold.MoldRespVO;
import com.mongoso.mgs.module.produce.handler.approve.MoldApproveHandler;
import com.mongoso.mgs.module.produce.handler.flowCallback.MoldFlowCallBackHandler;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.mongoso.mgs.module.produce.dal.db.mold.MoldDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.mold.MoldMapper;

import com.mongoso.mgs.framework.common.exception.BizException;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;


/**
 * 模具台账 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MoldServiceImpl implements MoldService {

    @Resource
    private MoldMapper moldMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    @Lazy
    private MoldApproveHandler moldApproveHandler;

    @Resource
    private MoldFlowCallBackHandler moldFlowCallBackHandler;


    @Override
    public Long moldAdd(MoldAditReqVO reqVO) {

        // 验重
        Long count = moldMapper.selectCountByCode(reqVO.getMoldCode());
        if (count > 0) {
            throw exceptionMsg("模具编码重复");
        }

        // 插入
        MoldDO mold = BeanUtilX.copy(reqVO, MoldDO::new);
        mold.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey());
        if (reqVO.getStockStatus() == null){
            mold.setStockStatus(DictToEnum.IN.getKey());
        }
        if (reqVO.getMoldStatus() == null){
            mold.setMoldStatus(DictToEnum.NORMAL.getKey());
        }

        if (reqVO.getProcessMod() == null){
            mold.setProcessMod(BigDecimal.ZERO);
        }
        moldMapper.insert(mold);
        // 返回
        return mold.getMoldId();
    }

    @Override
    public Long moldEdit(MoldAditReqVO reqVO) {
        // 校验存在
        MoldDO moldDO = this.moldValidateExists(reqVO.getMoldId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(moldDO, reqVO);

        // 编辑验重
        if (StrUtilX.isNotEmpty(reqVO.getMoldCode()) && !reqVO.getMoldCode().equals(moldDO.getMoldCode())) {
            Long count = moldMapper.selectCountByCode(reqVO.getMoldCode());
            if (count > 0) {
                throw exceptionMsg("模具编码重复");
            }
        }

        // 更新
        MoldDO mold = BeanUtilX.copy(reqVO, MoldDO::new);
        if (reqVO.getProcessMod() == null){
            mold.setProcessMod(BigDecimal.ZERO);
        }
        mold.setDataStatus(null);
        moldMapper.updateById(mold);
        // 返回
        return mold.getMoldId();
    }

    @Override
    public void moldDel(Long moldId) {
        // 校验存在
        MoldDO currentDO = this.moldValidateExists(moldId);

        if(currentDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        moldMapper.deleteById(moldId);
    }

    private MoldDO moldValidateExists(Long moldId) {
        MoldDO mold = moldMapper.selectById(moldId);
        if (mold == null) {
            // throw exception(MOLD_NOT_EXISTS);
            throw new BizException("5001", "模具台账不存在");
        }
        return mold;
    }

    @Override
    public MoldRespVO moldDetail(Long moldId) {
        MoldDO data = moldValidateExists(moldId);
        if (data == null){
            return null;
        }
        MoldRespVO respVO = BeanUtilX.copy(data, MoldRespVO::new);
        ApproveTaskDO approveTaskDO = approveService.detailByObjId(moldId + "");
        if (approveTaskDO != null) {
            respVO.setApproveTaskId(approveTaskDO.getApproveTaskId());
        }
        //VO属性填充
        fillVoProperties(respVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(moldId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<MoldRespVO> moldList(MoldQueryReqVO reqVO) {
        List<MoldDO> data = moldMapper.selectList(reqVO);
        List<MoldRespVO> respVOS = BeanUtilX.copy(data, MoldRespVO::new);

        //属性填充
        batchFillVoProperties(respVOS);

        return respVOS;
    }

    @Override
    public PageResult<MoldRespVO> moldPage(MoldPageReqVO reqVO) {

        PageResult<MoldDO> data = moldMapper.selectPage(reqVO);
        PageResult<MoldRespVO> pageResult = BeanUtilX.copy(data, MoldRespVO::new);
        if (CollUtilX.isNotEmpty(pageResult.getList())) {
            //VO属性填充
            batchFillVoProperties(pageResult.getList());
        }
        return pageResult;
    }

    @Override
    public Long moldDuplicheck(MoldQueryReqVO reqVO) {
        if (null == reqVO.getMoldId()) {
            return moldMapper.selectCountByCode(reqVO.getMoldCode());
        } else {
            MoldDO oldDO = moldValidateExists(reqVO.getMoldId());
            if (!oldDO.getMoldCode().equals(reqVO.getMoldCode())) {
                return moldMapper.selectCountByCode(reqVO.getMoldCode());
            }
        }
        return 0L;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private MoldRespVO fillVoProperties(MoldRespVO respVO) {
        if (respVO == null){
            return respVO;
        }
        List<MoldRespVO> erpRespVOList = batchFillVoProperties(Arrays.asList(respVO));
        return erpRespVOList.get(0);
    }

    private List<MoldRespVO> batchFillVoProperties(List<MoldRespVO> list) {
        if (CollUtilX.isEmpty(list)) {
            return list;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MOLD_TYPE.getDictCode(), CustomerDictEnum.MOLD_SOURCE.getDictCode(),
                SystemDictEnum.PROD_STOCK_STATUS.getDictCode(), SystemDictEnum.MOLD_STATUS.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();
        List<Long> customerIdList = new ArrayList<>();

        List<String> orgIdList = new ArrayList<>();
        List<Long> otherIdList = new ArrayList<>();

        for (MoldRespVO item : list) {
            empIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());
            customerIdList.add(item.getCustomerId());

            Integer locationType = item.getLocationType();

            // 外借组织
            if (locationType!=null && item.getLocationId()!=null && locationType.equals(0)){
                orgIdList.add(item.getLocationId().toString());
            }

            //客户/供应商
            if (locationType!=null && item.getLocationId()!=null && locationType.equals(1)){
                otherIdList.add(item.getLocationId());
                customerIdList.add(item.getLocationId());
            }
        }

        //查询所属客户
        Map<Long, String> customerNameMap = erpBaseService.getERPCustomerNameByIdList(customerIdList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        //查询负责人
        Map<Long, String> empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        // 查询供应商信息
        Map<Long, String> supplierMap = erpBaseService.getERPSupplierNameByIdList(otherIdList);

        //查询外借组织
        Map<String, String> loanOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        for (MoldRespVO item : list) {

            // 模具类型
            String moldTypeDictId = item.getMoldTypeDictId();
            if(StrUtilX.isNotEmpty(moldTypeDictId)){
                moldTypeDictId = CustomerDictEnum.MOLD_TYPE.getDictCode() + "-" + moldTypeDictId;
                item.setMoldTypeDictName(dictMap.get(moldTypeDictId));
            }

            // 模具来源
            String moldSourceDictId = item.getMoldSourceDictId();
            if(StrUtilX.isNotEmpty(moldSourceDictId)){
                moldSourceDictId = CustomerDictEnum.MOLD_SOURCE.getDictCode() + "-" + moldSourceDictId;
                item.setMoldSourceDictName(dictMap.get(moldSourceDictId));
            }

            // 模具状态
            if(item.getMoldStatus() != null){
                String moldStatus = SystemDictEnum.MOLD_STATUS.getDictCode() + "-" + item.getMoldStatus();
                item.setMoldStatusDictName(dictMap.get(moldStatus));
            }

            // 库存状态
            if(item.getStockStatus() != null){
                String stockStatus = SystemDictEnum.PROD_STOCK_STATUS.getDictCode() + "-" + item.getStockStatus();
                item.setStockStatusDictName(dictMap.get(stockStatus));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //查询所属客户
            item.setCustomerName(customerNameMap.get(item.getCustomerId()));

            //查询负责人
            item.setDirectorName(empNameMap.get(item.getDirectorId()));

            //查询责任部门
            item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));

            Integer locationType = item.getLocationType();

            // 外借组织
            if (locationType!=null && item.getLocationId()!=null && locationType.equals(0)){
                item.setLocationName(loanOrgMap.get(item.getLocationId().toString()));
            }

            //客户/供应商
            if (locationType!=null && item.getLocationId()!=null && locationType.equals(1)){
                String locationName = customerNameMap.get(item.getLocationId());
                if (StrUtilX.isNotEmpty(locationName)){
                    item.setLocationName(locationName);
                }else {
                    locationName = supplierMap.get(item.getLocationId());
                    item.setLocationName(locationName);
                }
            }
        }

        return list;
    }

    @Override
    public ResultX<BatchResult> moldDelBatch(IdReq reqVO) {
        // 删除
        String id = EntityUtilX.getPropertyName(MoldDO::getMoldId);
        String code = EntityUtilX.getPropertyName(MoldDO::getMoldCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), MoldDO.class, null, id, code);
    }

    @Override
    public BatchResult moldApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<MoldDO> list = moldMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (MoldDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = moldApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getMoldCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (MoldDO item : list) {
                String reason = reasonMap.get(item.getMoldCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getMoldId());
                    messageInfoBO.setObjCode(item.getMoldCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getMoldId());
                    messageInfoBO.setObjCode(item.getMoldCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object moldFlowCallback(FlowCallback reqVO) {

        String objId = reqVO.getObjId();
        MoldDO item = this.moldValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return moldFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public Map<Long, MoldDO> mapDO(List<Long> moldIdList) {
        List<MoldDO> moldDOS = moldMapper.selectBatchIds(moldIdList);
        if (CollUtilX.isEmpty(moldDOS)){
            return new HashMap<>();
        }
        Map<Long, MoldDO> collect = moldDOS.stream().collect(Collectors.toMap(MoldDO::getMoldId, Function.identity()));
        return collect;
    }

    @Override
    public List<MoldDO> listDO(List<Long> moldIdList) {
        return moldMapper.selectBatchIds(moldIdList);
    }

    @Override
    public Map<Long, MoldRespVO> getMoldInfo(List<Long> moldIdList,Long stockStatus) {
        if (CollUtilX.isEmpty(moldIdList)){
            return new HashMap<>();
        }
        MoldQueryReqVO moldQuery = new MoldQueryReqVO();
        moldQuery.setDataStatus(DataStatusEnum.APPROVED.key);
        moldQuery.setMoldIdList(moldIdList);
        moldQuery.setStockStatus(stockStatus);
        List<MoldDO> moldDOList = moldMapper.selectList(moldQuery);
        List<MoldRespVO> respVOList = BeanUtilX.copy(moldDOList, MoldRespVO::new);

        if (CollUtilX.isNotEmpty(respVOList)){
            //VO属性填充
            batchFillVoProperties(respVOList);
        }
        return respVOList.stream().collect(Collectors.toMap(MoldRespVO::getMoldId, detail -> detail));
    }

    @Override
    public List<CustAndSupplierResp> findCustAndSupplierList(CustAndSupplierReq reqVO) {
        return erpBaseService.getCustAndSupplierList(reqVO);
    }

}
