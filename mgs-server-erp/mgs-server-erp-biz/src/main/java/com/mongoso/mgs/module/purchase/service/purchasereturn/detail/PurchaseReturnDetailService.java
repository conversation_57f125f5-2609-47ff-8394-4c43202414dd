package com.mongoso.mgs.module.purchase.service.purchasereturn.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购退货单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseReturnDetailService {

    /**
     * 创建采购退货单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseReturnDetailAdd(@Valid PurchaseReturnDetailAditReqVO reqVO);

    /**
     * 更新采购退货单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseReturnDetailEdit(@Valid PurchaseReturnDetailAditReqVO reqVO);

    /**
     * 删除采购退货单明细
     *
     * @param purchaseReturnDetailId 编号
     */
    void purchaseReturnDetailDel(Long purchaseReturnDetailId);

    /**
     * 获得采购退货单明细信息
     *
     * @param purchaseReturnDetailId 编号
     * @return 采购退货单明细信息
     */
    PurchaseReturnDetailRespVO purchaseReturnDetailDetail(Long purchaseReturnDetailId);

    /**
     * 获得采购退货单明细列表
     *
     * @param reqVO 查询条件
     * @return 采购退货单明细列表
     */
    List<PurchaseReturnDetailRespVO> purchaseReturnDetailList(@Valid PurchaseReturnDetailQueryReqVO reqVO);

    /**
     * 获得采购退货单被引用明细列表
     *
     * @param reqVO 查询条件
     * @return 采购退货单明细列表
     */
    List<PurchaseReturnDetailRespVO> purchaseReturnDetailQuotedList(@Valid PurchaseReturnDetailQueryReqVO reqVO);

    /**
     * 获得采购退货单明细分页
     *
     * @param reqVO 查询条件
     * @return 采购退货单明细分页
     */
    PageResult<PurchaseReturnDetailRespVO> purchaseReturnDetailPage(@Valid PurchaseReturnDetailPageReqVO reqVO);

    /**
     * 更新出库数量(出库审核更新)
     *
     * @param purchaseReturnDetailId 采购退货单明细ID
     * @param outboundQty 出库数量
     */
    void updateOutboundedQty(Long purchaseReturnDetailId, BigDecimal outboundQty);

}
