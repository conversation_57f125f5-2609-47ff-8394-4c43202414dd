package com.mongoso.mgs.module.purchase.service.receiptnotice.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 采购收货通知单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseReceiptNoticeDetailService {

    /**
     * 创建采购收货通知单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long purchaseReceiptNoticeDetailAdd(@Valid PurchaseReceiptNoticeDetailAditReqVO reqVO);

    /**
     * 更新采购收货通知单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long purchaseReceiptNoticeDetailEdit(@Valid PurchaseReceiptNoticeDetailAditReqVO reqVO);

    /**
     * 删除采购收货通知单明细
     *
     * @param receiptNoticeDetailId 编号
     */
    void purchaseReceiptNoticeDetailDel(Long receiptNoticeDetailId);

    /**
     * 获得采购收货通知单明细信息
     *
     * @param receiptNoticeDetailId 编号
     * @return 采购收货通知单明细信息
     */
    PurchaseReceiptNoticeDetailRespVO purchaseReceiptNoticeDetailDetail(Long receiptNoticeDetailId);

    /**
     * 获得采购收货通知单明细列表
     *
     * @param reqVO 查询条件
     * @return 采购收货通知单明细列表
     */
    List<PurchaseReceiptNoticeDetailRespVO> purchaseReceiptNoticeDetailList(@Valid PurchaseReceiptNoticeDetailQueryReqVO reqVO);

    /**
     * 获得采购收货通知单被引用明细列表
     *
     * @param reqVO 查询条件
     * @return 采购收货通知单明细列表
     */
    List<PurchaseReceiptNoticeDetailRespVO> purchaseReceiptNoticeDetailQuotedList(PurchaseReceiptNoticeDetailQueryReqVO reqVO);

    /**
     * 获得采购收货通知单明细分页
     *
     * @param reqVO 查询条件
     * @return 采购收货通知单明细分页
     */
    PageResult<PurchaseReceiptNoticeDetailRespVO> purchaseReceiptNoticeDetailPage(@Valid PurchaseReceiptNoticeDetailPageReqVO reqVO);


    Map<Long, BigDecimal> detailNoticeQtyMap(Long purchaseOrderId);

    /**
     * 更新采购收货通知单已收货数量
     *
     * @param receiptNoticeDetailId 采购收货通知单明细ID
     * @param receiptQty 收货数量
     * @return
     */
    void updateReceiptedQty(Long receiptNoticeDetailId, BigDecimal receiptQty);

    /**
     * 更新采购收货通知单已入库数量
     *
     * @param receiptNoticeDetailId 采购收货通知单明细ID
     * @param inboundedQty 入库数量
     * @return
     */
    void updateInboundedQty(Long receiptNoticeDetailId, BigDecimal inboundedQty);

}
