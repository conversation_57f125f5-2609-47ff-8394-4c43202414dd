package com.mongoso.mgs.module.purchase.service.demand;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.purchase.PurchaseBizTypeEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.*;
import com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseFlowCallbackHandler;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisStatisticsReportAditReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal.vo.MaterialAnalysisTotalQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.MaterialAnalysisTotalDO;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysistotal.MaterialAnalysisTotalMapper;
import com.mongoso.mgs.module.produce.service.materialanalysis.MaterialAnalysisStatisticsReportService;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.*;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailBaseVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.demand.vo.detail.PurchaseDemandDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.demand.PurchaseDemandMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.demand.detail.PurchaseDemandDetailMapper;
import com.mongoso.mgs.module.purchase.handler.approve.DemandGeneralApproveHandler;
import com.mongoso.mgs.module.purchase.handler.flowcallback.DemandGeneralFlowCallBackHandler;
import com.mongoso.mgs.module.purchase.service.demand.detail.PurchaseDemandDetailService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailQueryReqVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail.ErpSaleOrderDetailDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_DELETE_NOT_APPROVED;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购需求 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseDemandServiceImpl implements PurchaseDemandService {

    @Resource
    private PurchaseDemandMapper demandMapper;
    @Resource
    private PurchaseDemandDetailMapper purchaseDemandDetailMapper;
    @Resource
    private SeqService seqService;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private PurchaseDemandDetailService purchaseDemandDetailService;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private ErpSaleOrderDetailMapper erpSaleOrderDetailMapper;
    @Resource
    private DemandGeneralApproveHandler demandGeneralApproveHandler;
    @Resource
    private MaterialAnalysisTotalMapper materialAnalysisTotalMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private DemandGeneralFlowCallBackHandler demandGeneralFlowCallBackHandler;

    @Resource
    private MaterialAnalysisStatisticsReportService materialAnalysisStatisticsReportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseDemandAdd(PurchaseDemandAditReqVO reqVO) {
        // 插入
        PurchaseDemandDO demand = BeanUtilX.copy(reqVO, PurchaseDemandDO::new);
        // 编号
        String demandCode = null;
        if (reqVO.getPurchaseDemandBizType() == 0) {
            demandCode = seqService.getGenerateCode(reqVO.getPurchaseDemandCode(), MenuEnum.PURCHASE_REQUIREMENT_ORDER.menuId);
        }else if (reqVO.getPurchaseDemandBizType() == 1) {
            demandCode = seqService.getGenerateCode(reqVO.getPurchaseDemandCode(), MenuEnum.SALES_PURCHASE_REQUIREMENT_ORDER.menuId);
        }else if (reqVO.getPurchaseDemandBizType() == 2){
            demandCode = seqService.getGenerateCode(reqVO.getPurchaseDemandCode(), MenuEnum.MATERIAL_ANALYSIS_PURCHASE_REQUIREMENT_ORDER.menuId);
        }
        demand.setPurchaseDemandCode(demandCode);
        demand.setPurchaseDemandId(IDUtilX.getId());
        demand.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        demandMapper.insert(demand);

        //新增明细
        List<PurchaseDemandDetailBaseVO> purchaseDemandDetailBaseVOS = this.fillDetailList(reqVO, demand);
        purchaseDemandDetailMapper.insertBatch(BeanUtilX.copy(purchaseDemandDetailBaseVOS, PurchaseDemandDetailDO::new));

        //累加明细里面的可采购数量
        BigDecimal totalDemandQty = purchaseDemandDetailBaseVOS
                .stream()
                .map(PurchaseDemandDetailBaseVO::getPlanDemandQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        if (reqVO.getPurchaseDemandBizType() == 2
                && reqVO.getBizType() != null
                && reqVO.getRelatedOrderId() != null) {
            //新增物料下发统计单
            MaterialAnalysisStatisticsReportAditReqVO aditReqVO = new MaterialAnalysisStatisticsReportAditReqVO();
            aditReqVO.setSourceOrderId(reqVO.getRelatedOrderId());
            aditReqVO.setBizType(reqVO.getBizType());
            aditReqVO.setFormType((short)3);//3采购需求单
            aditReqVO.setFormCode(demandCode);
            aditReqVO.setIssuedQty(totalDemandQty);
            materialAnalysisStatisticsReportService.materialAnalysisStatisticsReportAdd(aditReqVO);

        }

        // 返回
        return demand.getPurchaseDemandId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseDemandEdit(PurchaseDemandAditReqVO reqVO) {
        // 校验存在
//        this.purchaseDemandValidateExists(reqVO.getPurchaseDemandId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.purchaseDemandValidateExists(reqVO.getPurchaseDemandId()), reqVO);

        // 更新
        PurchaseDemandDO demand = BeanUtilX.copy(reqVO, PurchaseDemandDO::new);
        demandMapper.updateById(demand);

        //明细更新，先删后增
        purchaseDemandDetailMapper.deleteByDemandId(reqVO.getPurchaseDemandId());
        List<PurchaseDemandDetailBaseVO> purchaseDemandDetailBaseVOS = this.fillDetailList(reqVO, demand);
        purchaseDemandDetailMapper.insertBatch(BeanUtilX.copy(purchaseDemandDetailBaseVOS, PurchaseDemandDetailDO::new));
        // 返回
        return demand.getPurchaseDemandId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseDemandDel(Long purchaseDemandId) {
        // 校验存在
        PurchaseDemandDO purchaseDemandDO = this.purchaseDemandValidateExists(purchaseDemandId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(purchaseDemandDO.getDataStatus())){
            throw exception(ORDER_DELETE_NOT_APPROVED);
        }
        // 删除
        demandMapper.deleteById(purchaseDemandId);
        purchaseDemandDetailMapper.deleteByDemandId(purchaseDemandId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> purchaseDemandDelBatch(IdReq reqVO) {
        //获取对象属性名
        String purchaseDemandId = EntityUtilX.getPropertyName(PurchaseDemandDO::getPurchaseDemandId);
        String purchaseDemandCode = EntityUtilX.getPropertyName(PurchaseDemandDO::getPurchaseDemandCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), PurchaseDemandDO.class, PurchaseDemandDetailDO.class, purchaseDemandId, purchaseDemandCode);
    }

    private PurchaseDemandDO purchaseDemandValidateExists(Long purchaseDemandId) {
        PurchaseDemandDO demand = demandMapper.selectById(purchaseDemandId);
        if (demand == null) {
            // throw exception(DEMAND_NOT_EXISTS);
            throw new BizException("5001", "采购需求不存在");
        }
        return demand;
    }

    @Override
    public PurchaseDemandRespVO purchaseDemandDetail(Long purchaseDemandId) {
        //查询需求单
        PurchaseDemandDO data = this.purchaseDemandValidateExists(purchaseDemandId);
        PurchaseDemandRespVO demandRespVO = BeanUtilX.copy(data, PurchaseDemandRespVO::new);
        //VO属性填充
        this.fillVoProperties(demandRespVO);
        //查询需求明细
        PurchaseDemandDetailQueryReqVO detailQueryReqVO = new PurchaseDemandDetailQueryReqVO();
        detailQueryReqVO.setPurchaseDemandId(purchaseDemandId);
        List<PurchaseDemandDetailRespVO> detailRespVOS = purchaseDemandDetailService.purchaseDemandDetailList(detailQueryReqVO);
        //添加可规划需求数量，用于编辑页面
        Short bizType = data.getPurchaseDemandBizType();
        if (bizType == PurchaseBizTypeEnum.SALE_DEMAND.type || bizType == PurchaseBizTypeEnum.MATERIAL_DEMAND.type){
            List<Long> materialIdList = detailRespVOS.stream().map(PurchaseDemandDetailRespVO::getMaterialId).collect(Collectors.toList());
            Map<Long, BigDecimal> planAbleQtyMap = new HashMap<>();
            if (bizType == PurchaseBizTypeEnum.SALE_DEMAND.type){
                ErpSaleOrderDetailQueryReqVO queryReqVO = new ErpSaleOrderDetailQueryReqVO();
                queryReqVO.setMaterialIdList(materialIdList);
                queryReqVO.setSaleOrderId(data.getRelatedOrderId());
                List<ErpSaleOrderDetailDO> saleOrderDetailDOS = erpSaleOrderDetailMapper.selectList(queryReqVO);
                if (CollUtilX.isNotEmpty(saleOrderDetailDOS)){
                    planAbleQtyMap = saleOrderDetailDOS.stream().collect(Collectors
                            .toMap(ErpSaleOrderDetailDO::getMaterialId, ErpSaleOrderDetailDO::getPlanAbleDemandQty));
                }
            }
            if (bizType == PurchaseBizTypeEnum.MATERIAL_DEMAND.type){
                MaterialAnalysisTotalQueryReqVO analysisTotalQueryReqVO = new MaterialAnalysisTotalQueryReqVO();
                analysisTotalQueryReqVO.setMaterialIdList(materialIdList);
                analysisTotalQueryReqVO.setMaterialAnalysisId(data.getRelatedOrderId());
                List<MaterialAnalysisTotalDO> analysisTotalDOList = materialAnalysisTotalMapper.selectList(analysisTotalQueryReqVO);
                if (CollUtilX.isNotEmpty(analysisTotalDOList)){
                    planAbleQtyMap = analysisTotalDOList.stream().collect(Collectors
                            .toMap(MaterialAnalysisTotalDO::getMaterialId, MaterialAnalysisTotalDO::getPlanableDemandQty));
                }
            }
            for (PurchaseDemandDetailRespVO detailRespVO : detailRespVOS) {
                detailRespVO.setPlanAbleDemandQty(planAbleQtyMap.get(detailRespVO.getMaterialId()));
            }
        }

        demandRespVO.setDetailList(detailRespVOS);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(purchaseDemandId.toString())).ifPresent(approveTask -> demandRespVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return demandRespVO;
    }

    @Override
    public List<PurchaseDemandRespVO> purchaseDemandList(PurchaseDemandQueryReqVO reqVO) {
        List<PurchaseDemandDO> data = demandMapper.selectList(reqVO);
        List<PurchaseDemandRespVO> demandRespVOS = BeanUtilX.copy(data, PurchaseDemandRespVO::new);
        this.batchFillVoProperties(demandRespVOS);
        return demandRespVOS;
    }

    @Override
    public PageResult<PurchaseDemandRespVO> purchaseDemandPage(PurchaseDemandPageReqVO reqVO) {
        PageResult<PurchaseDemandDO> data = demandMapper.selectPage(reqVO);
        //获取可下发需求采购单的单据
        PageResult<PurchaseDemandRespVO> pageResult = BeanUtilX.copy(data, PurchaseDemandRespVO::new);
        List<PurchaseDemandRespVO> purchaseAbleList = demandMapper.demandForPurchaseList(null);
        Map<Long, Long> purchaseAbleMap = purchaseAbleList.stream().collect(Collectors.toMap(PurchaseDemandRespVO::getPurchaseDemandId, PurchaseDemandRespVO::getPurchaseDemandId));
        this.batchFillVoProperties(pageResult.getList(), purchaseAbleMap);
        return pageResult;
    }

    @Override
    public BatchResult erpPurchaseDemandApprove(PurchaseFlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PurchaseDemandDO> list = demandMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PurchaseDemandDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = demandGeneralApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getPurchaseDemandCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (PurchaseDemandDO item : list) {
                String reason = reasonMap.get(item.getPurchaseDemandCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPurchaseDemandId());
                    messageInfoBO.setObjCode(item.getPurchaseDemandCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPurchaseDemandId());
                    messageInfoBO.setObjCode(item.getPurchaseDemandCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object erpPurchaseDemandFlowCallback(PurchaseFlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PurchaseDemandDO item = this.purchaseDemandValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return demandGeneralFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public PageResult<PurchaseDemandRespVO> demandForPurchasePage(PurchaseDemandPageReqVO reqVO) {
        IPage<PurchaseDemandRespVO> respVOIPage = demandMapper.demandForPurchasePage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<PurchaseDemandRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    @Override
    public List<PurchaseDemandRespVO> demandForPurchaseList(PurchaseDemandQueryReqVO reqVO) {
        List<PurchaseDemandRespVO> respList = demandMapper.demandForPurchaseList(reqVO);
        this.batchFillVoProperties(respList);
        return respList;
    }

    @Override
    public PurchaseDemandRespAI purchaseDemandPageAI() {
        List<PurchaseDemandBOAI> list = demandMapper.selectListBy();
        Set<Long> ids = new HashSet<>();
        Map<String, BigDecimal> map = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        for (PurchaseDemandBOAI item : list) {
            ids.add(item.getPurchaseDemandId());
            BigDecimal qty = map.get(item.getMaterialCode());
            if (qty == null){
                qty = BigDecimal.ZERO;
            }
            BigDecimal add = qty.add(item.getQty());
            map.put(item.getMaterialCode(),add);
            map1.put(item.getMaterialCode(),item.getMaterialName());
        }

        List<PurchaseDemandItemRespAI> itemList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            String materialCode = entry.getKey();
            BigDecimal qty = entry.getValue();

            String materialName = map1.get(materialCode);

            PurchaseDemandItemRespAI purchaseDemandItemRespAI = new PurchaseDemandItemRespAI();

            purchaseDemandItemRespAI.setMaterialCode(materialCode);
            purchaseDemandItemRespAI.setMaterialName(materialName);
            purchaseDemandItemRespAI.setDemandQty(qty);

            itemList.add(purchaseDemandItemRespAI);
        }

        PurchaseDemandRespAI purchaseDemandRespAI = new PurchaseDemandRespAI();

        purchaseDemandRespAI.setPurchaseDemandQty(ids.size());
        purchaseDemandRespAI.setItemList(itemList);

        return purchaseDemandRespAI;
    }

    @Override
    public purchaseDemandRespStatAI purchaseDemandStatAI() {

        // 获取今天的日期
        LocalDate today = LocalDate.now();

        // 获取当月的开始日期（本月的第一天）
        LocalDate startOfMonth = today.withDayOfMonth(1);

        LocalDateTime startDt = startOfMonth.atTime(0, 0, 0);

        // 获取当月的结束日期（本月的最后一天）
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());

        LocalDateTime endDt = endOfMonth.atTime(23, 59, 59);

        List<AccountBalanceVOAI5> list = demandMapper.selectListBy2(startDt,endDt);
        Long materialId = null;
        Long max = 0L;
        Map<Long, Long> map = new HashMap<>();
        Set<Long> ids = new HashSet<>();
        for (AccountBalanceVOAI5 item : list) {
//            if (isInCurrentMonth(item.getFormDt().toLocalDate(),startOfMonth,endOfMonth)){
                ids.add(item.getPurchaseDemandId());
                Long object = map.get(item.getMaterialId());
                if (object == null){
                    object =  0L;
                }
                object = object + item.getQty();
                map.put(item.getMaterialId(),object);
                if (object >= max){
                    max = object;
                    materialId = item.getMaterialId();
                }
//            }
        }

        ERPMaterialDO erpMaterialRespVO = erpMaterialService.detailDO(materialId);

        int size = ids.size();
        purchaseDemandRespStatAI rest = new purchaseDemandRespStatAI();
        Long materialCount = map.get(materialId);
        BigDecimal ratio = BigDecimal.ZERO;
        if (size != 0) {

            BigDecimal bigDecimal = new BigDecimal(materialCount);
            BigDecimal sizbigDecimal = new BigDecimal(size);
            ratio = bigDecimal.divide(sizbigDecimal, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }

        rest.setCurrentMonthTotalQty(size);
        rest.setMaterialCurrentMonthTotalQty(materialCount);
        rest.setMaterialId(materialId);

        if (erpMaterialRespVO != null){
            rest.setMaterialCode(erpMaterialRespVO.getMaterialCode());
            rest.setMaterialName(erpMaterialRespVO.getMaterialName());
        }

        rest.setRatio(ratio);
        return rest;
    }


    private boolean isInCurrentMonth(LocalDate localDate, LocalDate startOfMonth, LocalDate endOfMonth) {
        return  !localDate.isBefore(startOfMonth) && !localDate.isAfter(endOfMonth);
    }


    private List<PurchaseDemandDetailBaseVO> fillDetailList(PurchaseDemandAditReqVO reqVO, PurchaseDemandDO demand) {
        List<PurchaseDemandDetailBaseVO> detailList = BeanUtilX.copy(reqVO.getDetailList(), PurchaseDemandDetailBaseVO::new);
        for (PurchaseDemandDetailBaseVO detail : detailList) {
            detail.setPurchaseDemandId(demand.getPurchaseDemandId());
            detail.setPurchaseDemandCode(demand.getPurchaseDemandCode());
            if (ObjUtilX.isEmpty(detail.getPlanDemandQty())){
                detail.setPlanDemandQty(BigDecimal.ZERO);
            }
            detail.setPurchaseAbleQty(detail.getPlanDemandQty());
            detail.setPurchasedQty(detail.getPlanDemandQty().subtract(detail.getPurchaseAbleQty()));
        }
        return detailList;
    }

    private void fillVoProperties(PurchaseDemandRespVO respVO) {
        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.DEMAND_TYPE.getDictCode());

        //字典库属性填充
        respVO.setDemandTypeDictName(dictMap.get(respVO.getDemandTypeDictId()));

        //查询负责人
        String directorName = erpBaseService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);

        //查询责任部门
        String directorOrgName = erpBaseService.getOrgNameById(respVO.getDirectorOrgId().toString());
        respVO.setDirectorOrgName(directorOrgName);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param demandRespList
     */
    private void batchFillVoProperties(List<PurchaseDemandRespVO> demandRespList) {
        if (CollUtilX.isEmpty(demandRespList)) {
            return;
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.DEMAND_TYPE.getDictCode());

        //查询责任部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameMap();

        Map<Long, String> empNameMap = new HashMap<>();

        List<Long> empIdList = demandRespList.stream().map(PurchaseDemandRespVO::getDirectorId).collect(Collectors.toList());

        //查询负责人
        empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        for (PurchaseDemandRespVO demandResp : demandRespList) {

            if (demandResp.getPurchaseDemandBizType() == 1){

            }

            //字典库属性填充
            demandResp.setDemandTypeDictName(dictMap.get(demandResp.getDemandTypeDictId()));

            //责任人属性填充
            demandResp.setDirectorName(empNameMap.get(demandResp.getDirectorId()));

            //责任部门属性填充
            demandResp.setDirectorOrgName(orgNameMap.get(demandResp.getDirectorOrgId()));
        }
    }

    /**
     * VO属性填充-批量处理
     *
     * @param demandRespList
     */
    private void batchFillVoProperties(List<PurchaseDemandRespVO> demandRespList, Map<Long, Long> purchaseAbleMap) {
        if (CollUtilX.isEmpty(demandRespList)) {
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.DEMAND_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询责任部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameMap();

        Map<Long, String> empNameMap = new HashMap<>();

        List<Long> empIdList = demandRespList.stream().map(PurchaseDemandRespVO::getDirectorId).collect(Collectors.toList());

        //查询负责人
        empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        for (PurchaseDemandRespVO demandResp : demandRespList) {

            if (purchaseAbleMap.get(demandResp.getPurchaseDemandId()) == null){
                demandResp.setIsCanIssuePurchase((short)0);
            }else {
                demandResp.setIsCanIssuePurchase((short)1);
            }

            // 采购需求单类型
            if(StrUtilX.isNotEmpty(demandResp.getDemandTypeDictId())){
                String demandTypeDictId = CustomerDictEnum.DEMAND_TYPE.getDictCode() + "-" + demandResp.getDemandTypeDictId();
                demandResp.setDemandTypeDictName(dictMap.get(demandTypeDictId));
            }

            // 审核状态
            if(demandResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + demandResp.getDataStatus();
                demandResp.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人属性填充
            demandResp.setDirectorName(empNameMap.get(demandResp.getDirectorId()));

            //责任部门属性填充
            demandResp.setDirectorOrgName(orgNameMap.get(demandResp.getDirectorOrgId()));
        }
    }

}
