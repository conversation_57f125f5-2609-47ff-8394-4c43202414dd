package com.mongoso.mgs.module.finance.controller.admin.cashbank.acceptbillreceipt.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
import java.math.BigDecimal;

import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 承兑汇票收款单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AcceptBillReceiptPageReqVO extends PageParam {

    /** 承兑汇票收款单号 */
    private String receiptCode;

    /** 承兑汇票ID */
    private Long acceptBillId;

    /** 入账金额 */
    private BigDecimal inBillAmt;

    /** 入账账户 */
    private Long inBillAccountId;

    /** 贴现金额 */
    private BigDecimal discountAmt;

    /** 贴现利息 */
    private BigDecimal discountInterest;

    /** 贴现率 */
    private Double discountRate;

    /** 贴现银行 */
    private String discountBankName;

    /** 子票区间下限 */
    private Long subTicketRangeLower;

    /** 子票区间上限 */
    private Long subTicketRangeUpper;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 备注 */
    private String remark;

    private String ticketBillPackageNo;

}
