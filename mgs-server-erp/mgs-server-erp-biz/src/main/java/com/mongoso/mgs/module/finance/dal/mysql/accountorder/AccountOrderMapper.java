package com.mongoso.mgs.module.finance.dal.mysql.accountorder;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.finance.dal.db.accountorder.AccountOrderDO;
import com.mongoso.mgs.module.finance.controller.admin.accountorder.vo.AccountOrderPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.accountorder.vo.AccountOrderQueryReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 对账单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountOrderMapper extends BaseMapperX<AccountOrderDO> {

    default PageResult<AccountOrderDO> selectPageOld(AccountOrderPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<AccountOrderDO>lambdaQueryX()
                .likeIfPresent(AccountOrderDO::getReconciliationNumber, reqVO.getReconciliationNumber())
                .eqIfPresent(AccountOrderDO::getAccountStatus, reqVO.getAccountStatus())
                .eq(AccountOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(AccountOrderDO::getTitle, reqVO.getTitle())
                .likeIfPresent(AccountOrderDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(AccountOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(AccountOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AccountOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AccountOrderDO::getTotalReconciliationAmt, reqVO.getTotalReconciliationAmt())
                .betweenIfPresent(AccountOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AccountOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(AccountOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AccountOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AccountOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(AccountOrderDO::getCreatedDt));
    }



    default PageResult<AccountOrderDO> selectPage(AccountOrderPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<AccountOrderDO>lambdaQueryX()
                .likeIfPresent(AccountOrderDO::getReconciliationNumber, reqVO.getReconciliationNumber())
                .eqIfPresent(AccountOrderDO::getAccountStatus, reqVO.getAccountStatus())
                .eq(AccountOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(AccountOrderDO::getTitle, reqVO.getTitle())
                .likeIfPresent(AccountOrderDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(AccountOrderDO::getCustomerId, reqVO.getCustomerId())
                .inIfPresent(AccountOrderDO::getCustomerId, reqVO.getCustomerIdList())
                .eqIfPresent(AccountOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AccountOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AccountOrderDO::getTotalReconciliationAmt, reqVO.getTotalReconciliationAmt())
                .betweenIfPresent(AccountOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AccountOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(AccountOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AccountOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AccountOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(AccountOrderDO::getCreatedDt));
    }

    default List<AccountOrderDO> selectListOld(AccountOrderQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<AccountOrderDO>lambdaQueryX()
                .likeIfPresent(AccountOrderDO::getReconciliationNumber, reqVO.getReconciliationNumber())
                .eqIfPresent(AccountOrderDO::getAccountStatus, reqVO.getAccountStatus())
                .eq(AccountOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(AccountOrderDO::getTitle, reqVO.getTitle())
                .likeIfPresent(AccountOrderDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(AccountOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(AccountOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AccountOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AccountOrderDO::getTotalReconciliationAmt, reqVO.getTotalReconciliationAmt())
                .betweenIfPresent(AccountOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AccountOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(AccountOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AccountOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AccountOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(AccountOrderDO::getCreatedDt));
    }

    default List<AccountOrderDO> selectList(AccountOrderQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<AccountOrderDO>lambdaQueryX()
                .likeIfPresent(AccountOrderDO::getReconciliationNumber, reqVO.getReconciliationNumber())
                .eqIfPresent(AccountOrderDO::getAccountStatus, reqVO.getAccountStatus())
                .eq(AccountOrderDO::getFormType, reqVO.getFormType())
                .likeIfPresent(AccountOrderDO::getTitle, reqVO.getTitle())
                .likeIfPresent(AccountOrderDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(AccountOrderDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(AccountOrderDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AccountOrderDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AccountOrderDO::getTotalReconciliationAmt, reqVO.getTotalReconciliationAmt())
                .betweenIfPresent(AccountOrderDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AccountOrderDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(AccountOrderDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AccountOrderDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AccountOrderDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(AccountOrderDO::getCreatedDt));
    }

    void deleteBySettIds(@Param("settlePoolIds") List<Long> settlePoolIds);
}