package com.mongoso.mgs.module.produce.handler.approve;

import com.mongoso.mgs.common.constants.FlowFunCodeConstants;
import com.mongoso.mgs.common.enums.produce.ProduceEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.produce.controller.admin.reportedwork.vo.ReportedWorkAditReqVO;
import com.mongoso.mgs.module.produce.dal.db.processoutdemand.ProcessOutDemandDO;
import com.mongoso.mgs.module.produce.dal.db.reportedwork.ReportedWorkDO;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.produce.dal.mysql.reportedwork.ReportedWorkMapper;
import com.mongoso.mgs.module.produce.service.reportedwork.ReportedWorkService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;


/**
 * @author: zhiling
 * @date: 2024/11/26 18:34
 * @description: 生产工单审批流程处理类
 */

@Component
public class ReportedWorkApproveHandler extends FlowApproveHandler<ReportedWorkDO> {

    @Resource
    private ReportedWorkMapper reportedWorkMapper;

    @Resource
    @Lazy
    private ReportedWorkService reportedWorkService;

    @Resource
    @Lazy
    private ProcessOutDemandMapper processOutDemandMapper;


    @Override
    protected ApproveCommonAttrs approvalAttributes(ReportedWorkDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(ReportedWorkDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(ReportedWorkDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getReportedWorkId())
                .objCode(item.getReportedWorkCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(ReportedWorkDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        if (buttonType.equals(DataButtonEnum.APPROVE.getKey())){

            boolean isMatch = item.getReportedWorkType().equals(ProduceEnum.REPORTED_PROD_WORK.code)
                    || item.getReportedWorkType().equals(ProduceEnum.REPORTED_PROCESS.code)
                    || item.getReportedWorkType().equals(ProduceEnum.REPORTED_OUTSOURCE.code);

            if (isMatch){
                //良品数
                BigDecimal operedQty = EntityUtilX.getBigDecDefault(item.getOkQty());
                //待派工数量
                BigDecimal dispatchAbleQty = BigDecimal.ZERO;

                //已审核报工数量
                BigDecimal sumOkQty = reportedWorkMapper.selectSumOkQty(item.getDispatchWorkId());
                sumOkQty = sumOkQty == null? BigDecimal.ZERO : sumOkQty;

                if (item.getReportedWorkType() == ProduceEnum.REPORTED_OUTSOURCE.code){
                    //查询委外采购需求清单
                    ProcessOutDemandDO demandDO = processOutDemandMapper.selectById(item.getDispatchWorkId());
                    if (demandDO != null){
                        BigDecimal dispatchQty = demandDO.getReceiptedQty();
                        dispatchAbleQty = dispatchQty.subtract(sumOkQty);
                    }

                }else {
                    BigDecimal dispatchQty = item.getDispatchQty();
                    dispatchAbleQty = dispatchQty.subtract(sumOkQty);
                }

                if (operedQty.compareTo(dispatchAbleQty)>0){
                    failItem.setCode(item.getReportedWorkCode());
                    failItem.setReason("您提交的报工数量为 ["+MathUtilX.stripTrailingZeros(operedQty)+"],待提交数量为 ["+MathUtilX.stripTrailingZeros(dispatchAbleQty)+"]。请调整后再次提交!");
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    public Integer handleBusinessData(ReportedWorkDO currentDO, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        //当前对象
        if (currentDO == null){
            return 1;
        }

        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();

        //报工类型
        Integer reportedWorkType = currentDO.getReportedWorkType();
        ReportedWorkAditReqVO aditReqVO = BeanUtilX.copy(currentDO, ReportedWorkAditReqVO::new);

        //工单报工，工序报工
        if (reportedWorkType.equals(ProduceEnum.REPORTED_PROD_WORK.code) || reportedWorkType.equals(ProduceEnum.REPORTED_PROCESS.code)){

            if(buttonType.equals(DataButtonEnum.APPROVE.getKey())) {
                // 需要处理
                reportedWorkService.prodApprovePostTask(aditReqVO);
            }

            if(buttonType.equals(DataButtonEnum.NOT_APPROVE.getKey())) {
                aditReqVO.setOkQty(aditReqVO.getOkQty().negate());
                aditReqVO.setNgQty(aditReqVO.getNgQty().negate());
                reportedWorkService.prodNotApprovePostTask(aditReqVO);
            }
        }

        //委外报工
        if (reportedWorkType.equals(ProduceEnum.REPORTED_OUTSOURCE.code)){

            if(buttonType.equals(DataButtonEnum.APPROVE.getKey())) {
                aditReqVO.setOkQty(aditReqVO.getOkQty());
                aditReqVO.setNgQty(aditReqVO.getNgQty());
                aditReqVO.setProcessOutDemandId(currentDO.getDispatchWorkId());
                reportedWorkService.outApprovePostTask(aditReqVO);
            }

            if(buttonType.equals(DataButtonEnum.NOT_APPROVE.getKey())) {
                aditReqVO.setOkQty(aditReqVO.getOkQty().negate());
                aditReqVO.setNgQty(aditReqVO.getNgQty().negate());
                aditReqVO.setProcessOutDemandId(currentDO.getDispatchWorkId());
                reportedWorkService.outNotApprovePostTask(aditReqVO);
            }
        }

        //返工 报工
        if (reportedWorkType.equals(ProduceEnum.REPORTED_REWORK.code)){

            if(buttonType.equals(DataButtonEnum.APPROVE.getKey())) {
                reportedWorkService.reworkAndCheckApprovePostTask(aditReqVO);
            }

            if(buttonType.equals(DataButtonEnum.NOT_APPROVE.getKey())) {
                reportedWorkService.reworkAndCheckNotApprovePostTask(aditReqVO);
            }
        }

        //自动审批
        WebFrameworkUtilX.getLoginUser().setCurMenuId(FlowFunCodeConstants.reportedWorkMenuId);

        //更新状态
        currentDO.setApprovedBy(loginUser.getFullUserName());
        currentDO.setApprovedDt(LocalDateTime.now());
        currentDO.setDataStatus(dataStatus);

        int updateCount = reportedWorkMapper.updateById(currentDO);
        return updateCount;
    }

}