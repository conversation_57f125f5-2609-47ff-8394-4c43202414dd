package com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysistotal.vo.*;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.MaterialAnalysisTotalDO;
import com.mongoso.mgs.module.produce.service.materialanalysistotal.MaterialAnalysisTotalService;

/**
 * 物料分析数量汇总 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class MaterialAnalysisTotalController {



}
