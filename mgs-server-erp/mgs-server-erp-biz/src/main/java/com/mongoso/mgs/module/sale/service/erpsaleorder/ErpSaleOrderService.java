package com.mongoso.mgs.module.sale.service.erpsaleorder;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.ai.controller.admin.finance.vo.ErpSaleOrderStatAI;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.*;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 销售订单 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpSaleOrderService {

    /**
     * 创建销售订单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpSaleOrderAdd(@Valid ErpSaleOrderAditReqVO reqVO);

    /**
     * 更新销售订单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpSaleOrderEdit(@Valid ErpSaleOrderAditReqVO reqVO);

    /**
     * 删除销售订单
     *
     * @param id 编号
     */
    void erpSaleOrderDel(Long id);

    /**
     * 删除销售订单
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> erpSaleOrderDelBatch(IdReq reqVO);

    /**
     * 获得销售订单信息
     *
     * @param id 编号
     * @return 销售订单信息
     */
    ErpSaleOrderRespVO erpSaleOrderDetail(Long id);

    /**
     * 获得销售订单信息
     *
     * @param id 编号
     * @return 销售订单信息
     */
    ErpSaleOrderRespVO erpSaleOrderQuotedDetail(Long id);

    /**
     * 获得销售订单列表
     *
     * @param reqVO 查询条件
     * @return 销售订单列表
     */
    List<ErpSaleOrderRespVO> erpSaleOrderList(@Valid ErpSaleOrderQueryReqVO reqVO);

    /**
     * 获得销售订单分页
     *
     * @param reqVO 查询条件
     * @return 销售订单分页
     */
    PageResult<ErpSaleOrderDetailResp> erpSaleOrderPage(@Valid ErpSaleOrderPageReqVO reqVO);

    PageResult<ErpSaleOrderDetailResp> queryErpSaleOrderDetailPage(ErpSaleOrderPageReqVO reqVO);

    BatchResult erpSaleOrderApprove(FlowApprove reqVO);

    Object erpSaleOrderFlowCallback(FlowCallback reqVO);

    ErpSaleOrderRespVO erpSaleChangQueryDetail(Long saleChangId);

    ErpSaleOrderRespVO erpSaleOrderDetailAI(String saleOrderCode);
    PageResult<ErpSaleOrderDetailResp> erpSaleForDemandPage(ErpSaleOrderPageReqVO reqVO);

    List<ErpSaleOrderDetailResp> erpSaleForDemandList(ErpSaleOrderQueryReqVO reqVO);

    /**
     * 销售订单强制关闭
     *
     * @param reqVO
     * @return
     */
    void erpSaleOrderForceClose(ErpSaleOrderPrimaryReqVO reqVO);

    /**
     * 销售订单修改单据状态
     *
     * @param reqVO
     * @return
     */
    void erpSaleOrderEditFormStatus(ErpSaleOrderPrimaryReqVO reqVO);

    /**
     * 销售订单修改交货日期
     *
     * @param reqVO
     * @return
     */
    void erpSaleOrderEditDate(ErpSaleOrderPrimaryReqVO reqVO);

    List<ErpSaleOrderDO> forewarnJob(Integer dataStatus, List<Integer> formStatusList, List<Integer> outboundStatusList);

    PageResult<ErpSaleOrderDetailResp> erpSaleOrderPageAI(ErpSaleOrderPageAIReqVO reqVO);

    ErpSaleOrderStatAI erpSaleOrderStatAI();

    void editChildrenOrderCount (Long saleOrderId, Integer dataStatus);
}
