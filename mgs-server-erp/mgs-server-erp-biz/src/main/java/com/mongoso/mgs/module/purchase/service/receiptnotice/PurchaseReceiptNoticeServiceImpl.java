package com.mongoso.mgs.module.purchase.service.receiptnotice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticePageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.PurchaseReceiptNoticeMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.detail.PurchaseReceiptNoticeDetailMapper;
import com.mongoso.mgs.module.purchase.handler.approve.ReceiptNoticeApproveHandler;
import com.mongoso.mgs.module.purchase.handler.flowcallback.PurchaseReceiptNoticeFlowCallBackHandler;
import com.mongoso.mgs.module.purchase.service.receiptnotice.detail.PurchaseReceiptNoticeDetailService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_DELETE_NOT_APPROVED;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购收货通知单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReceiptNoticeServiceImpl implements PurchaseReceiptNoticeService {

    @Resource
    private PurchaseReceiptNoticeMapper receiptNoticeMapper;
    @Resource
    private PurchaseReceiptNoticeDetailMapper receiptNoticeDetailMapper;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private PurchaseReceiptNoticeDetailService receiptNoticeDetailService;
    @Resource
    private SeqService seqService;
    @Resource
    private ERPSupplierMapper erpSupplierMapper;
    @Resource
    private ReceiptNoticeApproveHandler receiptNoticeApproveHandler;

    @Resource
    private PurchaseReceiptNoticeFlowCallBackHandler purchaseReceiptNoticeFlowCallBackHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseReceiptNoticeAdd(PurchaseReceiptNoticeAditReqVO reqVO) {
        //编号
        String code = seqService.getGenerateCode(reqVO.getReceiptNoticeCode(), MenuEnum.PURCHASE_RECEIVING_NOTICE.menuId);
        // 插入
        PurchaseReceiptNoticeDO receiptNotice = BeanUtilX.copy(reqVO, PurchaseReceiptNoticeDO::new);
        receiptNotice.setReceiptNoticeCode(code);
        receiptNotice.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        receiptNotice.setApprovedBy(null);
        receiptNotice.setApprovedDt(null);
        receiptNoticeMapper.insert(receiptNotice);
        // 返回
        //新增明细
        List<PurchaseReceiptNoticeDetailDO> detailDOList = BeanUtilX.copy(reqVO.getDetailList(), PurchaseReceiptNoticeDetailDO::new);
        this.fillDetailList(detailDOList, receiptNotice);
        receiptNoticeDetailMapper.insertBatch(detailDOList);
        return receiptNotice.getReceiptNoticeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseReceiptNoticeEdit(PurchaseReceiptNoticeAditReqVO reqVO) {
        // 校验存在
        PurchaseReceiptNoticeDO receiptNoticeDO = this.purchaseReceiptNoticeValidateExists(reqVO.getReceiptNoticeId());
        //校验是否存在和版本号
        EntityUtilX.checkVersion(receiptNoticeDO, reqVO);

        // 更新
        PurchaseReceiptNoticeDO receiptNotice = BeanUtilX.copy(reqVO, PurchaseReceiptNoticeDO::new);
        receiptNoticeMapper.updateById(receiptNotice);
        //明细更新，先删后增
        receiptNoticeDetailMapper.deleteByPurchaseId(reqVO.getReceiptNoticeId());
        List<PurchaseReceiptNoticeDetailDO> detailDOList = BeanUtilX.copy(reqVO.getDetailList(), PurchaseReceiptNoticeDetailDO::new);
        this.fillDetailList(detailDOList, receiptNotice);
        receiptNoticeDetailMapper.insertBatch(detailDOList);
        // 返回
        return receiptNotice.getReceiptNoticeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseReceiptNoticeDel(Long receiptNoticeId) {
        // 校验存在
        PurchaseReceiptNoticeDO receiptNoticeDO = this.purchaseReceiptNoticeValidateExists(receiptNoticeId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(receiptNoticeDO.getDataStatus())){
            throw  exception(ORDER_DELETE_NOT_APPROVED);
        }
        // 删除
        receiptNoticeMapper.deleteById(receiptNoticeId);
        receiptNoticeDetailMapper.deleteByPurchaseId(receiptNoticeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> purchaseReceiptNoticeDelBatch(IdReq reqVO) {
        //获取对象属性名
        String receiptNoticeId = EntityUtilX.getPropertyName(PurchaseReceiptNoticeDO::getReceiptNoticeId);
        String receiptNoticeCode = EntityUtilX.getPropertyName(PurchaseReceiptNoticeDO::getReceiptNoticeCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), PurchaseReceiptNoticeDO.class, PurchaseReceiptNoticeDetailDO.class
                , receiptNoticeId, receiptNoticeCode);
    }

    @Override
    public BatchResult erpReceiptNoticeApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PurchaseReceiptNoticeDO> list = receiptNoticeMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PurchaseReceiptNoticeDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();



                //流程处理
                FailItem failItem = receiptNoticeApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getReceiptNoticeCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }
        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (PurchaseReceiptNoticeDO item : list) {
                String reason = reasonMap.get(item.getReceiptNoticeCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getReceiptNoticeId());
                    messageInfoBO.setObjCode(item.getReceiptNoticeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getReceiptNoticeId());
                    messageInfoBO.setObjCode(item.getReceiptNoticeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    private PurchaseReceiptNoticeDO purchaseReceiptNoticeValidateExists(Long receiptNoticeId) {
        PurchaseReceiptNoticeDO receiptNotice = receiptNoticeMapper.selectById(receiptNoticeId);
        if (receiptNotice == null) {
            // throw exception(RECEIPT_NOTICE_NOT_EXISTS);
            throw new BizException("5001", "采购收货通知单不存在");
        }
        return receiptNotice;
    }

    @Override
    public Object erpReceiptNoticeFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PurchaseReceiptNoticeDO item = this.purchaseReceiptNoticeValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return purchaseReceiptNoticeFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public PurchaseReceiptNoticeRespVO purchaseReceiptNoticeDetail(Long receiptNoticeId) {
        //校验存在
        PurchaseReceiptNoticeDO purchaseReceiptNoticeDO = this.purchaseReceiptNoticeValidateExists(receiptNoticeId);
        //对象转换
        PurchaseReceiptNoticeRespVO respVO = BeanUtilX.copy(purchaseReceiptNoticeDO, PurchaseReceiptNoticeRespVO::new);
        this.fillVoProperties(respVO);
        //查询明细列表
        PurchaseReceiptNoticeDetailQueryReqVO detailQueryReqVO = new PurchaseReceiptNoticeDetailQueryReqVO();
        detailQueryReqVO.setReceiptNoticeId(receiptNoticeId);
        List<PurchaseReceiptNoticeDetailRespVO> detailList = receiptNoticeDetailService.purchaseReceiptNoticeDetailList(detailQueryReqVO);
        respVO.setDetailList(detailList);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(receiptNoticeId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));


        return respVO;
    }

    @Override
    public PurchaseReceiptNoticeRespVO purchaseReceiptNoticeQuotedDetail(Long receiptNoticeId) {
        //校验存在
        PurchaseReceiptNoticeDO purchaseReceiptNoticeDO = this.purchaseReceiptNoticeValidateExists(receiptNoticeId);
        //对象转换
        PurchaseReceiptNoticeRespVO respVO = BeanUtilX.copy(purchaseReceiptNoticeDO, PurchaseReceiptNoticeRespVO::new);
        //查询明细列表
        PurchaseReceiptNoticeDetailQueryReqVO detailQueryReqVO = new PurchaseReceiptNoticeDetailQueryReqVO();
        detailQueryReqVO.setReceiptNoticeId(receiptNoticeId);
        detailQueryReqVO.setIsMaterialFullReceipted(0);
        List<PurchaseReceiptNoticeDetailRespVO> detailList = receiptNoticeDetailService.purchaseReceiptNoticeDetailQuotedList(detailQueryReqVO);
        respVO.setDetailList(detailList);
        return respVO;
    }


    @Override
    public List<PurchaseReceiptNoticeRespVO> purchaseReceiptNoticeList(PurchaseReceiptNoticeQueryReqVO reqVO) {
        List<PurchaseReceiptNoticeDO> data = receiptNoticeMapper.selectList(reqVO);
        List<PurchaseReceiptNoticeRespVO> resultList = BeanUtilX.copy(data, PurchaseReceiptNoticeRespVO::new);
        this.batchFillVoProperties(resultList);
        return resultList;
    }

    @Override
    public PageResult<PurchaseReceiptNoticeRespVO> purchaseReceiptNoticePage(PurchaseReceiptNoticePageReqVO reqVO) {
        IPage<PurchaseReceiptNoticeRespVO> respVOIPage = receiptNoticeMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<PurchaseReceiptNoticeRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    private void fillDetailList(List<PurchaseReceiptNoticeDetailDO> detailDOList, PurchaseReceiptNoticeDO purchase) {
        for (PurchaseReceiptNoticeDetailDO detailDO : detailDOList) {
            detailDO.setReceiptNoticeId(purchase.getReceiptNoticeId());
            detailDO.setReceiptNoticeCode(purchase.getReceiptNoticeCode());
            detailDO.setReceiptableQty(detailDO.getNoticeQty());
        }
    }

    private void fillVoProperties(PurchaseReceiptNoticeRespVO respVO) {
        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.PURCHASE_TYPE.getDictCode());

        //字典库属性填充
        respVO.setPurchaseTypeDictName(dictMap.get(respVO.getPurchaseTypeDictId()));

        //查询负责人
        String directorName = erpBaseService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);

        //查询责任部门
        String directorOrgName = erpBaseService.getOrgNameById(respVO.getDirectorOrgId());
        respVO.setDirectorOrgName(directorOrgName);

        //查询供应商名称
        ERPSupplierDO erpSupplierDO = erpSupplierMapper.selectById(respVO.getRelatedSupplierId());
        if(erpSupplierDO != null){
            respVO.setRelatedSupplierName(erpSupplierDO.getSupplierName());
        }

        if (respVO.getWarehouseOrgId() != null) {
            String warehouseOrgName = erpBaseService.getOrgNameById(respVO.getWarehouseOrgId());
            respVO.setWarehouseOrgName(warehouseOrgName);
        }
    }

    private void batchFillVoProperties(List<PurchaseReceiptNoticeRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)){
            return;
        }
        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for(PurchaseReceiptNoticeRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            supplierIdList.add(item.getRelatedSupplierId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for (PurchaseReceiptNoticeRespVO demandResp: respVOList){
            //供应商
            demandResp.setRelatedSupplierName(supplierNameMap.get(demandResp.getRelatedSupplierId()));
            //责任部门
            demandResp.setDirectorOrgName(directorOrgMap.get(demandResp.getDirectorOrgId()));
            //责任人
            demandResp.setDirectorName(directorMap.get(demandResp.getDirectorId()));
            // 采购订单类型
            if(StrUtilX.isNotEmpty(demandResp.getPurchaseTypeDictId())){
                String purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + demandResp.getPurchaseTypeDictId();
                demandResp.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            //审核状态
            if(demandResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + demandResp.getDataStatus();
                demandResp.setDataStatusDictName(dictMap.get(dataStatus));
            }

            if (demandResp.getWarehouseOrgId() != null) {
                String warehouseOrgName = erpBaseService.getOrgNameById(demandResp.getWarehouseOrgId());
                demandResp.setWarehouseOrgName(warehouseOrgName);
            }
        }
    }

}
