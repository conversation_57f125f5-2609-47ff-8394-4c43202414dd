package com.mongoso.mgs.module.warehouse.handler.approve;

import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.MaterialCheckQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.materialcheck.vo.detail.MaterialCheckResultQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDO;
import com.mongoso.mgs.module.warehouse.dal.db.materialcheck.MaterialCheckDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialcheck.MaterialCheckMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialcheck.MaterialCheckResultMapper;
import com.mongoso.mgs.module.warehouse.enums.MaterialCheckBizTypeEnum;
import com.mongoso.mgs.module.warehouse.enums.MaterialCheckResultEnum;
import com.mongoso.mgs.module.warehouse.service.erpreceipt.ErpReceiptDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: Fashoin.Liu
 * @date: 2024/12/3 18:34
 * @description: 检验单审批流程处理类
 */

@Component
public class MaterialCheckApproveHandler extends FlowApproveHandler<MaterialCheckDO> {

    @Resource
    private MaterialCheckMapper materialCheckMapper;

    @Resource
    private MaterialCheckResultMapper materialCheckResultMapper;

    @Resource
    private ErpReceiptMapper erpReceiptMapper;

    @Resource
    private ErpReceiptDetailService erpReceiptDetailService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(MaterialCheckDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(MaterialCheckDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(MaterialCheckDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getCheckId())
                .objCode(item.getCheckCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(MaterialCheckDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            if(item.getBizType() == MaterialCheckBizTypeEnum.RECEIPT_CHECK.getType()){
                Long relatedOrderId = item.getRelatedOrderId();
                ErpReceiptDO erpReceiptDO = erpReceiptMapper.selectById(relatedOrderId);
                if(erpReceiptDO == null){
                    failItem.setCode(item.getCheckCode());
                    failItem.setReason("关联的收货单已删除,不可操作审核");
                    return false;
                }
                if(erpReceiptDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()){
                    failItem.setCode(item.getCheckCode());
                    failItem.setReason("关联的收货单未审核,不可操作审核");
                    return false;
                }

                MaterialCheckQueryReqVO checkQueryReqVO = new MaterialCheckQueryReqVO();
                checkQueryReqVO.setRelatedOrderDetailId(item.getRelatedOrderDetailId());
                checkQueryReqVO.setDataStatus(DataButtonEnum.APPROVE.getKey());
                Long checkCount = materialCheckMapper.selectCount(checkQueryReqVO);
                if (checkCount > 0){
                    failItem.setCode(item.getCheckCode());
                    failItem.setReason("检验单物料已关联已审核的收货检验单,不可操作审核!");
                    return false;
                }
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            Long checkId = item.getCheckId();
            MaterialCheckResultQueryReqVO reqVO = new MaterialCheckResultQueryReqVO();
            reqVO.setCheckId(checkId);
            reqVO.setDataStatus(DataButtonEnum.APPROVE.key);
            Long checkResultCount = materialCheckResultMapper.selectCount(reqVO);
            if (checkResultCount > 0){
                failItem.setCode(item.getCheckCode());
                failItem.setReason("已关联已审核的检验结果录入单,不允许进行反审核操作!");
                return false;
            }
        }

        return true;
    }

    @Override
    public Integer handleBusinessData(MaterialCheckDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        Integer buttonType = request.getButtonType();
        Long id = item.getCheckId();
        Integer dataStatus = request.getDataStatus();

        MaterialCheckDO materialCheckDO = materialCheckMapper.selectById(id);

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            // 收货检验单
            if(materialCheckDO.getBizType() == MaterialCheckBizTypeEnum.RECEIPT_CHECK.getType()){
                erpReceiptDetailService.updateIsFullCheck(materialCheckDO.getRelatedOrderDetailId(), BaseEnum.YES.getKey(),
                        materialCheckDO.getCheckQty(), materialCheckDO.getAllowDefectsQty());
            }

            // 如果为便捷检验单, 直接更新检验结果
            if(materialCheckDO.getBizType() == MaterialCheckBizTypeEnum.CONVENIENT_CHECK.getType()){
                BigDecimal requiredQty = materialCheckDO.getCheckQty().subtract(materialCheckDO.getAllowDefectsQty());
                if(materialCheckDO.getOkQty().compareTo(requiredQty) >= 0){
                    //合格
                    materialCheckDO.setCheckResult(MaterialCheckResultEnum.QUALIFIED.getType());
                }else {
                    //不合格
                    materialCheckDO.setCheckResult(MaterialCheckResultEnum.UNQUALIFIED.getType());
                }
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

            // 收货检验单
            if(materialCheckDO.getBizType() == MaterialCheckBizTypeEnum.RECEIPT_CHECK.getType()){
                erpReceiptDetailService.updateIsFullCheck(materialCheckDO.getRelatedOrderDetailId(),
                        BaseEnum.NO.getKey(), null, null);
            }

            // 如果为便捷检验单, 直接更新检验结果为未检验
            if(materialCheckDO.getBizType() == MaterialCheckBizTypeEnum.CONVENIENT_CHECK.getType()){
                materialCheckDO.setCheckResult(MaterialCheckResultEnum.UNCHECK.getType());
            }
        }

        //更新业务数据
        materialCheckDO.setApprovedBy(loginUser.getFullUserName());
        materialCheckDO.setApprovedDt(LocalDateTime.now());
        materialCheckDO.setDataStatus(dataStatus);
        Integer updateCount = materialCheckMapper.updateById(materialCheckDO);

        return updateCount;
    }

}