package com.mongoso.mgs.module.purchase.dal.mysql.purprocessoutdeductiondetail;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutdeductiondetail.PurProcessOutDeductionDetailDO;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutreturndetail.PurProcessOutReturnDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail.vo.*;

/**
 * 工序委外采购扣费明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurProcessOutDeductionDetailMapper extends BaseMapperX<PurProcessOutDeductionDetailDO> {

    default PageResult<PurProcessOutDeductionDetailDO> selectPageOld(PurProcessOutDeductionDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<PurProcessOutDeductionDetailDO>lambdaQueryX()
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessCode, reqVO.getProcessCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDeductionQty, reqVO.getDeductionQty())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessId, reqVO.getProcessId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionId, reqVO.getProcessOutDeductionId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionCode, reqVO.getProcessOutDeductionCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PurProcessOutDeductionDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessName, reqVO.getProcessName())
                .orderByAsc(PurProcessOutDeductionDetailDO::getRowNo));
    }



    default PageResult<PurProcessOutDeductionDetailDO> selectPage(PurProcessOutDeductionDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<PurProcessOutDeductionDetailDO>lambdaQueryX()
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessCode, reqVO.getProcessCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDeductionQty, reqVO.getDeductionQty())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessId, reqVO.getProcessId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionId, reqVO.getProcessOutDeductionId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionCode, reqVO.getProcessOutDeductionCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PurProcessOutDeductionDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessName, reqVO.getProcessName())
                .orderByAsc(PurProcessOutDeductionDetailDO::getRowNo));
    }

    default List<PurProcessOutDeductionDetailDO> selectListOld(PurProcessOutDeductionDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<PurProcessOutDeductionDetailDO>lambdaQueryX()
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessCode, reqVO.getProcessCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDeductionQty, reqVO.getDeductionQty())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessId, reqVO.getProcessId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionId, reqVO.getProcessOutDeductionId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionCode, reqVO.getProcessOutDeductionCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PurProcessOutDeductionDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessName, reqVO.getProcessName())
                    .orderByAsc(PurProcessOutDeductionDetailDO::getRowNo));
    }

    default List<PurProcessOutDeductionDetailDO> selectList(PurProcessOutDeductionDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<PurProcessOutDeductionDetailDO>lambdaQueryX()
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessCode, reqVO.getProcessCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxUnitPrice, reqVO.getExclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxUnitPrice, reqVO.getInclTaxUnitPrice())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDeductionQty, reqVO.getDeductionQty())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessId, reqVO.getProcessId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionId, reqVO.getProcessOutDeductionId())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessOutDeductionCode, reqVO.getProcessOutDeductionCode())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurProcessOutDeductionDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PurProcessOutDeductionDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .likeIfPresent(PurProcessOutDeductionDetailDO::getProcessName, reqVO.getProcessName())
                .orderByAsc(PurProcessOutDeductionDetailDO::getRowNo));
    }


    default int deleteByPurchaseId(Long processOutDeductionId){
        return delete(LambdaQueryWrapperX.<PurProcessOutDeductionDetailDO>lambdaQueryX()
                .eq(PurProcessOutDeductionDetailDO::getProcessOutDeductionId, processOutDeductionId));
    }
}