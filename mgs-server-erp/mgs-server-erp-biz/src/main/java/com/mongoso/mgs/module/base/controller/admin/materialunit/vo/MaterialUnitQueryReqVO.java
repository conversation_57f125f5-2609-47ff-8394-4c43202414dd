package com.mongoso.mgs.module.base.controller.admin.materialunit.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 物料单位 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialUnitQueryReqVO {

    /** 主键 */
    private Long materialUnitId;

    /** 创建人ID */
    private Long createdId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 关联单ID */
    private Long relatedOrderId;

    /**
     * 关联单据Code
     */
    private String relatedOrderCode;

    /** 左单位编码 */
    private String leftUnit;

    /** 左单位名称 */
    private String leftUnitDictName;

    /** 右单位值 */
    private String rightVal;


    /** 行号 */
    private Integer rowNo;
}
