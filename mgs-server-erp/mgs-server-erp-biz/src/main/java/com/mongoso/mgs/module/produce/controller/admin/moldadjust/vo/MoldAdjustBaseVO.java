package com.mongoso.mgs.module.produce.controller.admin.moldadjust.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 模具调整记录 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MoldAdjustBaseVO implements Serializable {

    /** 主键ID */
    private Long moldAdjustId;

    /** 模具id */
    @NotNull(message = "模具主键不能为空")
    private Long moldId;

    /** 原模具状态 */
    private Long oldMoldStatus;

    /** 当前模具状态 */
    private Long moldStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人ID */
    private Long createdId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核状态 */
    private Short dataStatus;

}
