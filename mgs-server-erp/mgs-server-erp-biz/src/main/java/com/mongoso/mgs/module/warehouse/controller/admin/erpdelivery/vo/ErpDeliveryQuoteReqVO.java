package com.mongoso.mgs.module.warehouse.controller.admin.erpdelivery.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 单据引用 ReqVO
 *
 * <AUTHOR>
 */
@Data
public class ErpDeliveryQuoteReqVO {

    /** 业务类型 **/
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    /** 排除业务类型 **/
    private List<Integer> exclBizType;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 业务单号 **/
    /** 出库单号 **/
    private String outboundCode;
}
