package com.mongoso.mgs.common.enums.messagetemplate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *  预警消息模板编码
 *
 */
@AllArgsConstructor
@Getter
public enum ForewarnMessageTemplateEnum implements Serializable {


    L1("T00001", "销售订单逾期提醒"),
    L2("T00002", "销售订单提前提醒"),

    L3("T00003", "外借单逾期提醒"),
    L4("T00004", "外借单提前提醒"),

    L5("T00005", "盘点单逾期提醒"),
    L6("T00006", "盘点单提前提醒"),

    L7("T00007", "采购订单逾期提醒"),
    L8("T00008", "采购订单提前提醒"),

    L9("T00009", "工序委外采购订单逾期提醒"),
    L10("T00010", "工序委外采购订单提前"),


    L11("T00011", "应收账款逾期提醒"),
    L12("T00012", "应收账款提前提醒"),
    L13("T00013", "应付账款逾期提醒"),
    L14("T00014", "应付账款提前提醒"),


    L15("T00015", "开票计划逾期提醒"),
    L16("T00016", "开票计划提前提醒"),
    L21("T00021", "收票计划逾期提醒"),
    L22("T00022", "收票计划提前提醒"),


    L17("T00017", "生产订单逾期提醒"),
    L18("T00018", "生产订单提前提醒"),

    L19("T00019", "生产工单逾期提醒"),
    L20("T00020", "生产工单提前提醒"),

    ;

    public final String key;
    public final String value;


    public static ForewarnMessageTemplateEnum getValueByKey(Integer key) {
        for (ForewarnMessageTemplateEnum scope : ForewarnMessageTemplateEnum.values()) {
            if (scope.getKey().equals(key)) {
                return scope;
            }
        }
        return null; // 如果没有找到，返回 null 或者抛出异常
    }

}
