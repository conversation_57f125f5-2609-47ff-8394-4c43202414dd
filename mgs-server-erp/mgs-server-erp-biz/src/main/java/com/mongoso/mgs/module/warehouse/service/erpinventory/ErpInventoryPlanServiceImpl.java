package com.mongoso.mgs.module.warehouse.service.erpinventory;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinventory.vo.plan.*;
import com.mongoso.mgs.module.warehouse.dal.db.erpinventory.ErpInventoryPlanDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinventory.ErpInventoryPlanMapper;
import com.mongoso.mgs.module.warehouse.handler.approve.ErpInventoryPlanApproveHandler;
import com.mongoso.mgs.module.warehouse.handler.flowCallback.ErpInventoryPlanFlowCallBackHandler;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.warehouse.enums.ErrorCodeConstants.*;


/**
 * 盘点计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpInventoryPlanServiceImpl implements ErpInventoryPlanService {

    @Resource
    private ErpInventoryPlanMapper erpInventoryPlanMapper;

    @Resource
    private ErpInventoryService erpInventoryService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    @Lazy
    private ErpInventoryPlanApproveHandler erpInventoryPlanApproveHandler;

    @Resource
    private ErpInventoryPlanFlowCallBackHandler erpInventoryPlanFlowCallBackHandler;

    @Override
    public Long erpInventoryPlanAdd(ErpInventoryPlanAditReqVO reqVO) {
        // 生成盘点计划单号
        reqVO.setInventoryPlanCode(seqService.getGenerateCode(reqVO.getInventoryPlanCode(), MenuEnum.INVENTORY_COUNT_PLAN.menuId));

        // 插入
        ErpInventoryPlanDO erpInventoryPlan = BeanUtilX.copy(reqVO, ErpInventoryPlanDO::new);
        String warehouseOrgId = reqVO.getWarehouseOrgIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
        erpInventoryPlan.setWarehouseOrgId(warehouseOrgId);
        erpInventoryPlanMapper.insert(erpInventoryPlan);
        // 返回
        return erpInventoryPlan.getInventoryPlanId();
    }

    @Override
    public Long erpInventoryPlanEdit(ErpInventoryPlanAditReqVO reqVO) {
        // 校验存在
//        this.erpInventoryPlanValidateExists(reqVO.getInventoryPlanId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.erpInventoryPlanValidateExists(reqVO.getInventoryPlanId()), reqVO);
        // 更新
        ErpInventoryPlanDO erpInventoryPlan = BeanUtilX.copy(reqVO, ErpInventoryPlanDO::new);
        String warehouseOrgId = reqVO.getWarehouseOrgIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
        erpInventoryPlan.setWarehouseOrgId(warehouseOrgId);
        erpInventoryPlanMapper.updateById(erpInventoryPlan);
        // 返回
        return erpInventoryPlan.getInventoryPlanId();
    }

    public BatchResult erpInventoryPlanEnableEdit(@Valid ErpInventoryPlanEnableAditReqVO reqVO) {
        BatchResult result = new BatchResult();
        List<FailItem> failItemList = new ArrayList<>();

        ErpInventoryPlanQueryReqVO queryReqVO = new ErpInventoryPlanQueryReqVO();
        queryReqVO.setInventoryPlanIdList(reqVO.getIdList());
        List<ErpInventoryPlanDO> planDOList = erpInventoryPlanMapper.selectList(queryReqVO);
        for (ErpInventoryPlanDO planDO : planDOList) {
            if (reqVO.getIsEnable() == 0) {
                if (planDO.getDataStatus() != 1) {
                    FailItem failItem = new FailItem();
                    failItem.setCode(planDO.getInventoryPlanCode());
                    failItem.setReason("审核状态不等于已审核, 不可操作禁用!");
                    failItemList.add(failItem);
                } else {
                    LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
                    planDO.setIsEnable(reqVO.getIsEnable());
                    planDO.setUpdatedBy(loginUser.getFullUserName());
                    planDO.setUpdatedDt(LocalDateTime.now());
                    erpInventoryPlanMapper.updateById(planDO);
                }
            } else {
                if (planDO.getDataStatus() != 1) {
                    FailItem failItem = new FailItem();
                    failItem.setCode(planDO.getInventoryPlanCode());
                    failItem.setReason("审核状态不等于已审核, 不可操作启用!");
                    failItemList.add(failItem);
                } else {
                    LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
                    planDO.setIsEnable(reqVO.getIsEnable());
                    planDO.setUpdatedBy(loginUser.getFullUserName());
                    planDO.setUpdatedDt(LocalDateTime.now());
                    erpInventoryPlanMapper.updateById(planDO);
                }
            }
        }

        result.setFailItem(failItemList);
        result.setTotalCount(reqVO.getIdList().size());
        result.setFailCount(failItemList.size());
        result.setSuccessCount(result.getTotalCount() - result.getFailCount());
        return result;
    }

    @Override
    public void erpInventoryPlanDel(Long inventoryPlanId) {
        // 校验存在
        this.erpInventoryPlanValidateExists(inventoryPlanId);
        // 删除
        erpInventoryPlanMapper.deleteById(inventoryPlanId);
    }

    @Override
    public ResultX<BatchResult> erpInventoryPlanDelBatch(IdsReqVO reqVO) {
        String id = EntityUtilX.getPropertyName(ErpInventoryPlanDO::getInventoryPlanId);
        String code = EntityUtilX.getPropertyName(ErpInventoryPlanDO::getInventoryPlanCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), ErpInventoryPlanDO.class, null, id, code);
    }

    private ErpInventoryPlanDO erpInventoryPlanValidateExists(Long inventoryPlanId) {
        ErpInventoryPlanDO erpInventoryPlan = erpInventoryPlanMapper.selectById(inventoryPlanId);
        if (erpInventoryPlan == null) {
            // throw exception(ERP_INVENTORY_PLAN_NOT_EXISTS);
            throw new BizException("5001", "盘点计划不存在");
        }
        return erpInventoryPlan;
    }

    @Override
    public ErpInventoryPlanRespVO erpInventoryPlanDetail(Long inventoryPlanId) {
        ErpInventoryPlanRespVO respVO = BeanUtilX.copy(erpInventoryPlanMapper.selectById(inventoryPlanId), ErpInventoryPlanRespVO::new);
        //属性填充
        fillVoProperties(respVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(inventoryPlanId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<ErpInventoryPlanRespVO> erpInventoryPlanList(ErpInventoryPlanQueryReqVO reqVO) {
        List<ErpInventoryPlanRespVO> respVOList = BeanUtilX.copy(erpInventoryPlanMapper.selectList(reqVO), ErpInventoryPlanRespVO::new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<ErpInventoryPlanRespVO> erpInventoryPlanPage(ErpInventoryPlanPageReqVO reqVO) {
        PageResult<ErpInventoryPlanRespVO> pageResult = BeanUtilX.copy(erpInventoryPlanMapper.selectPage(reqVO), ErpInventoryPlanRespVO::new);
        if (CollUtilX.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private ErpInventoryPlanRespVO fillVoProperties(ErpInventoryPlanRespVO respVO) {
        List<ErpInventoryPlanRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        List<ErpInventoryPlanRespVO> erpRespVOList = batchFillVoProperties(respVOList);
        return erpRespVOList.get(0);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private List<ErpInventoryPlanRespVO> batchFillVoProperties(List<ErpInventoryPlanRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return respVOList;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        for (ErpInventoryPlanRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            if(item.getWarehouseOrgId()!=null){
                orgIdList.addAll(Arrays.asList(item.getWarehouseOrgId().split(",")));
            }
        }

        //查询字典库信息
        List<String > dictCodeList = Arrays.asList(CustomerDictEnum.INVENTORY_TYPE.getDictCode(), SystemDictEnum.INVENTORY_METHOD.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        for(ErpInventoryPlanRespVO item : respVOList) {

            // 盘点计划单类型
            if(StrUtilX.isNotEmpty(item.getInventoryPlanTypeDictId())){
                String inventoryPlanTypeDictId =  CustomerDictEnum.INVENTORY_TYPE.getDictCode() + "-" + item.getInventoryPlanTypeDictId();
                item.setInventoryPlanTypeDictName(dictMap.get(inventoryPlanTypeDictId));
            }

            // 盘点方式
            if(item.getInventoryMethod() != null ){
                String inventoryMethodStr =  SystemDictEnum.INVENTORY_METHOD.getDictCode() + "-" + item.getInventoryMethod();
                item.setInventoryMethodDictName(dictMap.get(inventoryMethodStr));
            }

            // 盘点仓库
            if(item.getWarehouseOrgId() != null){
                item.setWarehouseOrgIdList(Arrays.asList(item.getWarehouseOrgId().split(",")));
                item.setWarehouseOrgName(Arrays.stream(item.getWarehouseOrgId().split(","))
                        .map(directorOrgMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining("、")));
            }

            // 审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if (item.getDirectorId() != null) {
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
        return respVOList;
    }


    @Override
    public BatchResult erpInventoryPlanApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ErpInventoryPlanDO> list = erpInventoryPlanMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ErpInventoryPlanDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = erpInventoryPlanApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                exception.printStackTrace();
                FailItem failItem = new FailItem();
                failItem.setCode(item.getInventoryPlanCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //生成盘点单
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            erpInventoryService.erpInventoryPlanGenTask();
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ErpInventoryPlanDO item : list) {
                String reason = reasonMap.get(item.getInventoryPlanCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInventoryPlanId());
                    messageInfoBO.setObjCode(item.getInventoryPlanCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInventoryPlanId());
                    messageInfoBO.setObjCode(item.getInventoryPlanCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object erpInventoryPlanFlowCallback(FlowCallback reqVO){
        String objId = reqVO.getObjId();
        ErpInventoryPlanDO item = this.erpInventoryPlanValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();
        return erpInventoryPlanFlowCallBackHandler.handleFlowCallback(item, flowCallbackBO);
    }

}
