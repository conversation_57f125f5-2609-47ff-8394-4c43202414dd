package com.mongoso.mgs.module.finance.service.cashbank.interaccounttran;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.bankconfig.vo.BankConfigPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.bankconfig.vo.BankConfigRespVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.interaccounttran.vo.InterAccountTranAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.interaccounttran.vo.InterAccountTranPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.interaccounttran.vo.InterAccountTranQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.cashbank.interaccounttran.vo.InterAccountTranRespVO;
import com.mongoso.mgs.module.finance.dal.db.cashbank.interaccounttran.InterAccountTranDO;
import com.mongoso.mgs.module.finance.dal.mysql.cashbank.interaccounttran.InterAccountTranMapper;
import com.mongoso.mgs.module.finance.handler.approve.cashbank.InterAccountTranHandler;
import com.mongoso.mgs.module.finance.handler.flowCallback.cashbank.InterAccountFlowCallBackHandler;
import com.mongoso.mgs.module.finance.service.cashbank.bankconfig.BankConfigService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.finance.enums.ErrorCodeConstants.*;


/**
 * 账间转账 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InterAccountTranServiceImpl implements InterAccountTranService {

    @Resource
    private InterAccountTranMapper interAccountTranMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    private BankConfigService bankConfigService;

    @Resource
    @Lazy
    private InterAccountTranHandler interAccountTranHandler;

    @Resource
    private InterAccountFlowCallBackHandler interAccountFlowCallBackHandler;

    @Override
    public Long interAccountTranAdd(InterAccountTranAditReqVO reqVO) {
        // 插入
//        if (reqVO.getOutAccountBalance().compareTo(reqVO.getOutAmt().add(reqVO.getOutServiceFee())) < 0){
//            throw new BizException("500", "转出手续费+转出金额必须小于转出账户的可用余额!");
//        }
//        if (reqVO.getInAccountBalance().compareTo(reqVO.getInServiceFee()) < 0){
//            throw new BizException("500", "转入手续费必须小于转入账户的可用余额!");
//        }
        InterAccountTranDO interAccountTran = BeanUtilX.copy(reqVO, InterAccountTranDO::new);
        //生成转账单号
        String code = seqService.getGenerateCode(reqVO.getTransferCode(), MenuEnum.INTER_ACCOUNT_TRANSFER.menuId);
        interAccountTran.setTransferCode(code);
        interAccountTranMapper.insert(interAccountTran);
        // 返回
        return interAccountTran.getInterAccountTranId();
    }

    @Override
    public Long interAccountTranEdit(InterAccountTranAditReqVO reqVO) {
        // 校验存在
//        if (reqVO.getOutAccountBalance().compareTo(reqVO.getOutAmt().add(reqVO.getOutServiceFee())) < 0){
//            throw new BizException("500", "转出手续费+转出金额必须小于转出账户的可用余额!");
//        }
//        if (reqVO.getInAccountBalance().compareTo(reqVO.getInServiceFee()) < 0){
//            throw new BizException("500", "转入手续费必须小于转入账户的可用余额!");
//        }
//        this.interAccountTranValidateExists(reqVO.getInterAccountTranId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.interAccountTranValidateExists(reqVO.getInterAccountTranId()), reqVO);

        // 更新
        InterAccountTranDO interAccountTran = BeanUtilX.copy(reqVO, InterAccountTranDO::new);
        interAccountTranMapper.updateById(interAccountTran);
        // 返回
        return interAccountTran.getInterAccountTranId();
    }

    @Override
    public void interAccountTranDel(Long id) {
        // 校验存在
        InterAccountTranDO interAccountTranDO = this.interAccountTranValidateExists(id);
        if(interAccountTranDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey().shortValue()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        interAccountTranMapper.deleteById(id);
    }

    private InterAccountTranDO interAccountTranValidateExists(Long id) {
        InterAccountTranDO interAccountTran = interAccountTranMapper.selectById(id);
        if (interAccountTran == null) {
            // throw exception(INTER_ACCOUNT_TRAN_NOT_EXISTS);
            throw new BizException("5001", "账间转账不存在");
        }
        return interAccountTran;
    }

    @Override
    public InterAccountTranRespVO interAccountTranDetail(Long id) {
        InterAccountTranDO interAccountTranDO = interAccountTranMapper.selectById(id);
        InterAccountTranRespVO interAccountTranRespVO = BeanUtilX.copy(interAccountTranDO, InterAccountTranRespVO::new);
        //VO属性填充
        fillVoProperties(interAccountTranRespVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(id.toString())).ifPresent(approveTask -> interAccountTranRespVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return interAccountTranRespVO;
    }

    @Override
    public List<InterAccountTranRespVO> interAccountTranList(InterAccountTranQueryReqVO reqVO) {
        List<InterAccountTranRespVO> respVOList = BeanUtilX.copyList(interAccountTranMapper.selectList(reqVO), InterAccountTranRespVO :: new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<InterAccountTranRespVO> interAccountTranPage(InterAccountTranPageReqVO reqVO) {
        PageResult<InterAccountTranRespVO> pageResult = new PageResult<>();
        BankConfigPageReqVO bankReqVO = new BankConfigPageReqVO();
        List<Long> inAccountIdList = new ArrayList<>();
        List<Long> outAccountIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(reqVO.getOutAccountName())) {
            bankReqVO.setAccountName(reqVO.getOutAccountName());
            PageResult<BankConfigRespVO> outBankResult = bankConfigService.bankConfigPage(bankReqVO);
            for (BankConfigRespVO item : outBankResult.getList()){
                outAccountIdList.add(item.getBankConfigId());
            }
        }else if (StringUtils.isNotEmpty(reqVO.getInAccountName())){
            bankReqVO.setAccountName(reqVO.getInAccountName());
            PageResult<BankConfigRespVO> inBankResult = bankConfigService.bankConfigPage(bankReqVO);
            for (BankConfigRespVO item : inBankResult.getList()){
                inAccountIdList.add(item.getBankConfigId());
            }
        }
        if ((StringUtils.isNotEmpty(reqVO.getInAccountName()) || StringUtils.isNotEmpty(reqVO.getOutAccountName())) && CollUtilX.isEmpty(inAccountIdList)  && CollUtilX.isEmpty(outAccountIdList)) {
            return pageResult;
        }
        reqVO.setOutAccountIdList(outAccountIdList);
        reqVO.setInAccountIdList(inAccountIdList);
        pageResult = BeanUtilX.copyPage(interAccountTranMapper.selectPage(reqVO), InterAccountTranRespVO :: new);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(InterAccountTranRespVO respVO) {
        List<InterAccountTranRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<InterAccountTranRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        List<Long> outAccountIdList = new ArrayList<>();
        List<Long> inAccountIdList = new ArrayList<>();
        for(InterAccountTranRespVO respVO : respVOList){
            directorIdList.add(respVO.getDirectorId());
            directorOrgIdList.add(respVO.getDirectorOrgId());
            outAccountIdList.add(respVO.getOutAccountId());
            inAccountIdList.add(respVO.getInAccountId());
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.APPROVED_STATUS.getDictCode());

        //出账账户
        Map<Long, String> outAccountMap = erpBaseService.getBankAccountByIdList(outAccountIdList);

        //入账账户
        Map<Long, String> inAccountMap = erpBaseService.getBankAccountByIdList(inAccountIdList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        // 属性填充
        for (InterAccountTranRespVO item : respVOList) {
            //出账账户
            if(item.getOutAccountId() != null){
                item.setOutAccountName(outAccountMap.get(item.getOutAccountId()));
            }

            //入账账户
            if(item.getInAccountId() != null){
                item.setInAccountName(inAccountMap.get(item.getInAccountId()));
            }

            // 审核状态
            if(item.getDataStatus() != null){
                item.setDataStatusDictName(dictMap.get(item.getDataStatus().toString()));
            }

            //责任人
            if(item.getDirectorId() != null){
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
    }

    @Override
    public ResultX<BatchResult> interAccountTranDelBatch(IdReq reqVO) {

        //获取对象属性名
        String id = EntityUtilX.getPropertyName(InterAccountTranDO:: getInterAccountTranId);
        String code = EntityUtilX.getPropertyName(InterAccountTranDO::getTransferCode);

        return erpBaseService.batchDelete(reqVO.getIdList(), InterAccountTranDO.class, null, id, code);
    }

    @Override
    public BatchResult interAccountTranApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<InterAccountTranDO> list = interAccountTranMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (InterAccountTranDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理（使用FlowApproveHandler的方法签名）
                FailItem failItem = interAccountTranHandler.process(item, flowApproveBO);
                //流程处理
                //FailItem failItem = interAccountTranHandler.process(item, null, flowFunctionCode, flowConfigBO, buttonType);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getTransferCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (InterAccountTranDO item : list) {
                String reason = reasonMap.get(item.getTransferCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInterAccountTranId());
                    messageInfoBO.setObjCode(item.getTransferCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInterAccountTranId());
                    messageInfoBO.setObjCode(item.getTransferCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object interAccountFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        InterAccountTranDO item = this.interAccountTranValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return interAccountFlowCallBackHandler.handleFlowCallback(item, flowCallbackBO);
    }


}
