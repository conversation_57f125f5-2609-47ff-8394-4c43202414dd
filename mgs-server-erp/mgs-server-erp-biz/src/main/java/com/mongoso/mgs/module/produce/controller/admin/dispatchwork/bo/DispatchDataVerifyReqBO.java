package com.mongoso.mgs.module.produce.controller.admin.dispatchwork.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: zhiling
 * @date: 2024/12/31 19:12
 * @description: 查询数量校验
 */

@Data
public class DispatchDataVerifyReqBO implements Serializable {

    /** 工作中心ID */
    private Long workCenterId;

    //生产工单id
    private Long prodWorkId;

    /** 物料id */
    private Long materialId;

    /** 派工方式['工单工序派工','工序派工'] */
    private Integer dispatchMethod;

    /** 报工类型 ['工单报工','工序报工','委外报工','返工报工','质检报工'] */
    private Integer reportedWorkType;

    //工序id
    private Long processId;

    /** 派工数量 */
    private BigDecimal dispatchQty;

    /** 报工数量 */
    private BigDecimal reportedQty;

    //工序委外单id
    private Long processOutDemandId;

    /** 计件方式 */
    private String pieceworkMethodDictId;


}