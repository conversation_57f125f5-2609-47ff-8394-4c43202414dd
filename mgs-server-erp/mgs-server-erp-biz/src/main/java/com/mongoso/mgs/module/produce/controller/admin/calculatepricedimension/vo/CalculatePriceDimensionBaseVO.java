package com.mongoso.mgs.module.produce.controller.admin.calculatepricedimension.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;

import jakarta.validation.constraints.*;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 计价维度 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CalculatePriceDimensionBaseVO implements Serializable {

    /** 计价维度id */
    private Long calculatePriceDimensionId;

    private Long parentId;

    /** 维度名称 */
    private String calculatePriceDimensionName;

    /** 维度编码 */
    private String calculatePriceDimensionCode;

    /** 维度值 */
    private String calculatePriceDimensionValue;

    /** 是否启用 */
    private Integer isEnable;

    /** 业务类型，0：计价，1：计时 */
    @NotNull(message = "bizType 不能为空")
    private Integer bizType;

    private Integer type;

    private LocalDateTime createdDt;
    private LocalDateTime updatedDt;
    private String createdBy;
    private String updatedBy;

}
