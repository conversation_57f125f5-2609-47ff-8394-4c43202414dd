package com.mongoso.mgs.module.sale.controller.admin.salechangdetail;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.sale.controller.admin.salechangdetail.vo.*;
import com.mongoso.mgs.module.sale.dal.db.salechangdetail.SaleChangDetailDO;
import com.mongoso.mgs.module.sale.service.salechangdetail.SaleChangDetailService;

/**
 * 销售订单变更明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sale")
@Validated
public class SaleChangDetailController {

    @Resource
    private SaleChangDetailService changDetailService;

    @OperateLog("销售订单变更明细添加或编辑")
    @PostMapping("/saleChangDetailAdit")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:adit')")
    public ResultX<Long> saleChangDetailAdit(@Valid @RequestBody SaleChangDetailAditReqVO reqVO) {
        return success(reqVO.getSaleChangDetailId() == null
                            ? changDetailService.saleChangDetailAdd(reqVO)
                            : changDetailService.saleChangDetailEdit(reqVO));
    }

    @OperateLog("销售订单变更明细删除")
    @PostMapping("/saleChangDetailDel")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:del')")
    public ResultX<Boolean> saleChangDetailDel(@Valid @RequestBody SaleChangDetailPrimaryReqVO reqVO) {
        changDetailService.saleChangDetailDel(reqVO.getSaleChangDetailId());
        return success(true);
    }

    @OperateLog("销售订单变更明细详情")
    @PostMapping("/saleChangDetailDetail")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:query')")
    public ResultX<SaleChangDetailRespVO> saleChangDetailDetail(@Valid @RequestBody SaleChangDetailPrimaryReqVO reqVO) {
        SaleChangDetailDO oldDO = changDetailService.saleChangDetailDetail(reqVO.getSaleChangDetailId());
        return success(BeanUtilX.copy(oldDO, SaleChangDetailRespVO::new));
    }

    @OperateLog("销售订单变更明细列表")
    @PostMapping("/saleChangDetailList")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:query')")
    public ResultX<List<SaleChangDetailRespVO>> saleChangDetailList(@Valid @RequestBody SaleChangDetailQueryReqVO reqVO) {
        List<SaleChangDetailDO> list = changDetailService.saleChangDetailList(reqVO);
        return success(BeanUtilX.copyList(list, SaleChangDetailRespVO::new));
    }

    @OperateLog("销售订单变更明细分页")
    @PostMapping("/saleChangDetailPage")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:query')")
    public ResultX<PageResult<SaleChangDetailRespVO>> saleChangDetailPage(@Valid @RequestBody SaleChangDetailPageReqVO reqVO) {
        PageResult<SaleChangDetailDO> pageResult = changDetailService.saleChangDetailPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, SaleChangDetailRespVO::new));
    }

    @OperateLog("销售订单变更查询不可变动值")
    @PostMapping("/saleChangFindNonChangeValue")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:query')")
    public ResultX<List<SaleChangDetailRespVO>> saleChangFindNonChangeValue(@Valid @RequestBody SaleChangDetailQueryReqVO reqVO) {
        List<SaleChangDetailRespVO> list = changDetailService.saleChangFindNonChangeValue(reqVO);
        return success(list);
    }

    @OperateLog("销售订单变更校验下游数据")
    @PostMapping("/saleChangUnitPriceCheck")
    @PreAuthorize("@ss.hasPermission('saleChangDetail:query')")
    public ResultX<Boolean> saleChangUnitPriceCheck(@Valid @RequestBody ChangUnitPriceCheckReqVO reqVO) {
        try {
            changDetailService.saleChangUnitPriceCheck(reqVO);
        }catch (Exception e) {
            return success(false);
        }
        return success(true);
    }


}
