package com.mongoso.mgs.module.salary.dal.mysql.salaryaggre;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggre.vo.SalaryAggrePageReqVO;
import com.mongoso.mgs.module.salary.controller.admin.salaryaggre.vo.SalaryAggreQueryReqVO;
import com.mongoso.mgs.module.salary.dal.db.salaryaggre.SalaryAggreDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工资单归集 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SalaryAggreMapper extends BaseMapperX<SalaryAggreDO> {

    default PageResult<SalaryAggreDO> selectPage(SalaryAggrePageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<SalaryAggreDO>lambdaQueryX()
                .eqIfPresent(SalaryAggreDO::getPayrollId, reqVO.getPayrollId())
                .eqIfPresent(SalaryAggreDO::getPayrollMemberId, reqVO.getPayrollMemberId())
                .eqIfPresent(SalaryAggreDO::getPayrollMonth, reqVO.getPayrollMonth())
                .eqIfPresent(SalaryAggreDO::getLastMonthFixedFee, reqVO.getLastMonthFixedFee())
                .eqIfPresent(SalaryAggreDO::getCurrentMonthFixedFee, reqVO.getCurrentMonthFixedFee())
                .eqIfPresent(SalaryAggreDO::getLastMonthDeferFee, reqVO.getLastMonthDeferFee())
                .eqIfPresent(SalaryAggreDO::getCurrentMonthDeferFee, reqVO.getCurrentMonthDeferFee())
                .eqIfPresent(SalaryAggreDO::getStrategyConfigStatus, reqVO.getStrategyConfigStatus())
                .eqIfPresent(SalaryAggreDO::getIsFixedExpense, reqVO.getIsFixedExpense())
                .eqIfPresent(SalaryAggreDO::getAggreStatus, reqVO.getAggreStatus())
                .eqIfPresent(SalaryAggreDO::getFixedExpendCostSubjectId, reqVO.getFixedExpendCostSubjectId())
                .eqIfPresent(SalaryAggreDO::getFixedExpendCostUsage, reqVO.getFixedExpendCostUsage())
                .eqIfPresent(SalaryAggreDO::getDeferFeeCostSubjectId, reqVO.getDeferFeeCostSubjectId())
                .eqIfPresent(SalaryAggreDO::getDeferFeeCostUsage, reqVO.getDeferFeeCostUsage())
                .eqIfPresent(SalaryAggreDO::getEmployeeArchivesId, reqVO.getEmployeeArchivesId())
                .eqIfPresent(SalaryAggreDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(SalaryAggreDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .likeIfPresent(SalaryAggreDO::getPayrollCode, reqVO.getPayrollCode())
                .likeIfPresent(SalaryAggreDO::getEmployeeNumber, reqVO.getEmployeeNumber())
                .betweenIfPresent(SalaryAggreDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(SalaryAggreDO::getPayrollAggreId));
    }




    default List<SalaryAggreDO> selectList(SalaryAggreQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<SalaryAggreDO>lambdaQueryX()
                .eqIfPresent(SalaryAggreDO::getPayrollId, reqVO.getPayrollId())
                .eqIfPresent(SalaryAggreDO::getPayrollMemberId, reqVO.getPayrollMemberId())
                .eqIfPresent(SalaryAggreDO::getPayrollMonth, reqVO.getPayrollMonth())
                .eqIfPresent(SalaryAggreDO::getLastMonthFixedFee, reqVO.getLastMonthFixedFee())
                .eqIfPresent(SalaryAggreDO::getCurrentMonthFixedFee, reqVO.getCurrentMonthFixedFee())
                .eqIfPresent(SalaryAggreDO::getLastMonthDeferFee, reqVO.getLastMonthDeferFee())
                .eqIfPresent(SalaryAggreDO::getCurrentMonthDeferFee, reqVO.getCurrentMonthDeferFee())
                .eqIfPresent(SalaryAggreDO::getStrategyConfigStatus, reqVO.getStrategyConfigStatus())
                .eqIfPresent(SalaryAggreDO::getIsFixedExpense, reqVO.getIsFixedExpense())
                .eqIfPresent(SalaryAggreDO::getAggreStatus, reqVO.getAggreStatus())
                .eqIfPresent(SalaryAggreDO::getFixedExpendCostSubjectId, reqVO.getFixedExpendCostSubjectId())
                .eqIfPresent(SalaryAggreDO::getFixedExpendCostUsage, reqVO.getFixedExpendCostUsage())
                .eqIfPresent(SalaryAggreDO::getDeferFeeCostSubjectId, reqVO.getDeferFeeCostSubjectId())
                .eqIfPresent(SalaryAggreDO::getDeferFeeCostUsage, reqVO.getDeferFeeCostUsage())
                .eqIfPresent(SalaryAggreDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(SalaryAggreDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .likeIfPresent(SalaryAggreDO::getPayrollCode, reqVO.getPayrollCode())
                .likeIfPresent(SalaryAggreDO::getEmployeeNumber, reqVO.getEmployeeNumber())
                .betweenIfPresent(SalaryAggreDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(SalaryAggreDO::getPayrollAggreId));
    }


}