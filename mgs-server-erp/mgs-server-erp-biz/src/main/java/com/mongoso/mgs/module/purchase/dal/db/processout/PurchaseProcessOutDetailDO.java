package com.mongoso.mgs.module.purchase.dal.db.processout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 工序委外采购订单明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_purchase_process_out_detail", autoResultMap = true)
//@KeySequence("erp.u_purchase_process_out_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseProcessOutDetailDO extends OperateDO {

    /** 工序委外采购订单明细ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long processOutDetailId;

    /** 工序委外采购订单ID */
    private Long purchaseProcessOutId;

    /** 工序委外需求ID */
    private Long processOutDemandId;

    /** 工序委外采购订单号 */
    private String purchaseProcessOutCode;

    /** 行号 */
    private Integer rowNo;

    /** 工序委外需求单号 */
    private String processOutDemandCode;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 工序id */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 采购数量 */
    private BigDecimal purchaseQty;

    /** 是否填报税价 */
    private Integer includingTax;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 单价(含税) */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 已收货数量 */
    private BigDecimal receiptedQty;

    /** 已操作数量 */
    private BigDecimal operedQty;

    /** 已退货数量 */
    private BigDecimal returnedQty;

    /** 已扣费数量 */
    private BigDecimal feedQty;

    /** 物料是否全部收货 */
    private Integer isMaterialFullReceipted;

    /** 物料是否全部已操作 */
    private Integer isMaterialFullOpered;

    /** 已对账数量 */
    private BigDecimal reconciledQty;

    /** 已计划数量 */
    private BigDecimal planedQty;

    /** 已计划收票数量 */
    private BigDecimal receivedQty;

    /** 交货日期 */
    private LocalDate deliveryDate;


    /** 币种字典ID */
    private String currencyDictId;

    /** 本币币种字典ID */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;
}
