package com.mongoso.mgs.module.purchase.controller.admin.report.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 库存变动统计 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class MaterialPurchaseReportItemRespVO implements Serializable {

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目明细列表
     */
    private List<BigDecimal> itemDetailList = new ArrayList<>();
}

