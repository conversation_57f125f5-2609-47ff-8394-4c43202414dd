package com.mongoso.mgs.module.warehouse.service.stockbook;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 库存预订明细 Service 接口
 *
 * <AUTHOR>
 */
public interface StockBookDetailService {

    /**
     * 创建库存预订明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long stockBookDetailAdd(@Valid StockBookDetailAditReqVO reqVO);

    /**
     * 更新库存预订明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long stockBookDetailEdit(@Valid StockBookDetailAditReqVO reqVO);

    /**
     * 删除库存预订明细
     *
     * @param stockBookDetailId 编号
     */
    void stockBookDetailDel(Long stockBookDetailId);

    /**
     * 获得库存预订明细信息
     *
     * @param stockBookDetailId 编号
     * @return 库存预订明细信息
     */
    StockBookDetailRespVO stockBookDetailDetail(Long stockBookDetailId);

    /**
     * 获得库存预订明细列表
     *
     * @param reqVO 查询条件
     * @return 库存预订明细列表
     */
    List<StockBookDetailRespVO> stockBookDetailList(@Valid StockBookDetailQueryReqVO reqVO);

    /**
     * 获得库存预订明细分页
     *
     * @param reqVO 查询条件
     * @return 库存预订明细分页
     */
    PageResult<StockBookDetailRespVO> stockBookDetailPage(@Valid StockBookDetailPageReqVO reqVO);

}
