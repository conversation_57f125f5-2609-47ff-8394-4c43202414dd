package com.mongoso.mgs.module.dailycost.controller.admin.undertakeobj.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 
/**
 * 承担对象 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class UndertakeObjBaseVO implements Serializable {

    /** 主键 */
    private Long undertakeObjId;

    /** 创建人ID */
    private Long createdId;

    /** 承担对象ID */
    private String undertakeOrgId;

    /** 承担对象名称 */
    private String undertakeOrgName;

    /** 承担比例 */
    private BigDecimal undertakeRatio;

    /** 关联单据id */
    private Long relatedOrderId;

}
