package com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.related;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * 工单领料单 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class RelatedWorkPickingPageRespVO implements Serializable {

    private Long materialId;
    private String materialCode;// 物料编码
    private String materialName;// 物料名称
    private String materialCategoryDictId;// 物料类别id
    private String materialCategoryDictName;// 物料类别id
    private Integer materialSourceDictId;//物料来源
    private String materialSourceDictName;//物料来源
    private String mainUnitDictId;// 基本单位
    private String mainUnitDictName;// 基本单位
    private String specModel;// 规格型号
    private String specAttributeStr;// 规格属性
    private String remark;// 物料编码


}
