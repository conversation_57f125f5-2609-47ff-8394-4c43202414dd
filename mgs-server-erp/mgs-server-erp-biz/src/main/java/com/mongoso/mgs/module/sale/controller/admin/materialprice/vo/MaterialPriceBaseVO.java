package com.mongoso.mgs.module.sale.controller.admin.materialprice.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 
/**
 * 商品价格 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MaterialPriceBaseVO implements Serializable {

    /** 主键 */
    private Long materialPriceId;

    /** 创建人ID */
    private Long createdId;

    /** 备注 */
    private String remark;

    /** 客户订货价 */
    private BigDecimal customerOrderPrice;

    /** 物料ID */
    private Long materialId;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 物料来源 */
    private Integer materialSourceDictId;
    private String materialSourceDictName;

    /** 主单位(基本单位) */
    private String mainUnitDictId;
    private String mainUnitDictName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

}
