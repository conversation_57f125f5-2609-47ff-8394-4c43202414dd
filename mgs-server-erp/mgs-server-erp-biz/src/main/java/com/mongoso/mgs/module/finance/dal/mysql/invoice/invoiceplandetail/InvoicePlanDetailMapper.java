package com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplandetail;

import java.math.BigDecimal;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo.InvoicePlanDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo.InvoicePlanDetailQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail.InvoicePlanDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 开票计划明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoicePlanDetailMapper extends BaseMapperX<InvoicePlanDetailDO> {

    default PageResult<InvoicePlanDetailDO> selectPageOld(InvoicePlanDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<InvoicePlanDetailDO>lambdaQueryX()
                .eqIfPresent(InvoicePlanDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePlanId, reqVO.getInvoicePlanId())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePendingPlanDetailId, reqVO.getInvoicePendingPlanDetailId())
                .eqIfPresent(InvoicePlanDetailDO::getRowNo, reqVO.getRowNo())
                .likeIfPresent(InvoicePlanDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoicePlanDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .likeIfPresent(InvoicePlanDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(InvoicePlanDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceQty, reqVO.getPlanInvoiceQty())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmt())
                .eqIfPresent(InvoicePlanDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoicePlanDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(InvoicePlanDetailDO::getCreatedDt));
    }



    default PageResult<InvoicePlanDetailDO> selectPage(InvoicePlanDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<InvoicePlanDetailDO>lambdaQueryX()
                .eqIfPresent(InvoicePlanDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePlanId, reqVO.getInvoicePlanId())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePendingPlanDetailId, reqVO.getInvoicePendingPlanDetailId())
                .eqIfPresent(InvoicePlanDetailDO::getRowNo, reqVO.getRowNo())
                .likeIfPresent(InvoicePlanDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoicePlanDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .likeIfPresent(InvoicePlanDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(InvoicePlanDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceQty, reqVO.getPlanInvoiceQty())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmt())
                .eqIfPresent(InvoicePlanDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoicePlanDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(InvoicePlanDetailDO::getCreatedDt));
    }

    default List<InvoicePlanDetailDO> selectListOld(InvoicePlanDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<InvoicePlanDetailDO>lambdaQueryX()
                .eqIfPresent(InvoicePlanDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePlanId, reqVO.getInvoicePlanId())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePendingPlanDetailId, reqVO.getInvoicePendingPlanDetailId())
                .eqIfPresent(InvoicePlanDetailDO::getRowNo, reqVO.getRowNo())
                .likeIfPresent(InvoicePlanDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoicePlanDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .likeIfPresent(InvoicePlanDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(InvoicePlanDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceQty, reqVO.getPlanInvoiceQty())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmt())
                .eqIfPresent(InvoicePlanDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoicePlanDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(InvoicePlanDetailDO::getCreatedDt));
    }

    default List<InvoicePlanDetailDO> selectList(InvoicePlanDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<InvoicePlanDetailDO>lambdaQueryX()
                .eqIfPresent(InvoicePlanDetailDO::getBillingDirection, reqVO.getBillingDirection())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePlanId, reqVO.getInvoicePlanId())
                .eqIfPresent(InvoicePlanDetailDO::getInvoicePendingPlanDetailId, reqVO.getInvoicePendingPlanDetailId())
                .eqIfPresent(InvoicePlanDetailDO::getRowNo, reqVO.getRowNo())
                .likeIfPresent(InvoicePlanDetailDO::getSourceOrderCode, reqVO.getSourceOrderCode())
                .eqIfPresent(InvoicePlanDetailDO::getSourceLineNumber, reqVO.getSourceLineNumber())
                .likeIfPresent(InvoicePlanDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(InvoicePlanDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceQty, reqVO.getPlanInvoiceQty())
                .eqIfPresent(InvoicePlanDetailDO::getPlanInvoiceAmtInclTax, reqVO.getPlanInvoiceAmt())
                .eqIfPresent(InvoicePlanDetailDO::getInclTaxAmt, reqVO.getInclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getExclTaxAmt, reqVO.getExclTaxAmt())
                .eqIfPresent(InvoicePlanDetailDO::getQty, reqVO.getQty())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(InvoicePlanDetailDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(InvoicePlanDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(InvoicePlanDetailDO::getCreatedDt));
    }

    void updateRemainPlanAmtAndQty(@Param("pendingDetailId") Long pendingDetailId,
                               @Param("amt") BigDecimal amt,
                               @Param("qty") BigDecimal qty
                               );

    BigDecimal invoicePlanQty(@Param("invoicePlanId") Long invoicePlanId);

    void deleteByinvoicePlanIds(@Param("invoicePlanIds") List<Long> invoicePlanIds);
}