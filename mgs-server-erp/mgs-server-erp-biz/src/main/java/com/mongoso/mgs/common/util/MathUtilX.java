package com.mongoso.mgs.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @author: zhiling
 * @date: 2024/11/12 18:27
 * @description: 计算工具类
 */
public class MathUtilX {

    /**
     * 去掉末尾的零
     *
     * @param decimal
     * @return
     */
    public static BigDecimal stripTrailingZeros(BigDecimal decimal){
        return decimal == null ? decimal :  new BigDecimal(decimal.stripTrailingZeros().toPlainString());
    }

    /**
     * 去掉末尾的零
     *
     * @param decimal
     * @return
     */
    public static BigDecimal reserveDecimal2(BigDecimal decimal){
        return decimal == null ? decimal :  decimal.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算不含税单价
     *
     * @param inclTaxPrice
     * @param rate
     * @return
     */
    public static BigDecimal getUnitPrice(BigDecimal inclTaxPrice, BigDecimal rate, Integer calculatType){
        BigDecimal unitPrice = BigDecimal.ZERO;
        if (inclTaxPrice == null || rate==null){
            return unitPrice;
        }
        rate = rate.divide(BigDecimal.valueOf(100));
        BigDecimal taxRate = calculatType == 1 ? BigDecimal.ONE.subtract(rate): BigDecimal.ONE.add(rate);

        //四舍五入,保留六位
        unitPrice = calculatType == 1 ? inclTaxPrice.multiply(taxRate) : inclTaxPrice.divide(taxRate, 6, RoundingMode.HALF_UP);

        return stripTrailingZeros(unitPrice);
    }

    /**
     * 计算含税单价
     *
     * @param unitPrice
     * @param rate
     * @return
     */
    public static BigDecimal getInclTaxPrice(BigDecimal unitPrice, BigDecimal rate, Integer calculatType){
        BigDecimal inclTaxPrice = BigDecimal.ZERO;
        if (unitPrice == null || rate==null){
            return inclTaxPrice;
        }
        rate = rate.divide(BigDecimal.valueOf(100));

        BigDecimal taxRate = calculatType == 1 ? BigDecimal.ONE.subtract(rate) : BigDecimal.ONE.add(rate);

        inclTaxPrice = calculatType == 1 ? unitPrice.divide(taxRate,6, RoundingMode.HALF_UP) : unitPrice.multiply(taxRate);

        return stripTrailingZeros(inclTaxPrice);
    }

    /**
     * 计算金额
     *
     * @param price
     * @param qty
     * @return
     */
    public static BigDecimal getAmt(BigDecimal price, BigDecimal qty){
        BigDecimal inclTaxAmt = BigDecimal.ZERO;
        if (price == null || qty==null){
            return inclTaxAmt;
        }

        inclTaxAmt = price.multiply(qty);
        // 四舍五入（例如保留两位小数）
        inclTaxAmt = inclTaxAmt.setScale(2, RoundingMode.HALF_UP);

        return inclTaxAmt;
    }

    /**
     * 等于0的时候返回0，正数的时候判断小于0就返回1，负数的时候判断大于0就返回1，否则就返回-1
     * @param ableAmt
     */
    public static int checkAmt0(BigDecimal ableAmt) {
        if (ableAmt.compareTo(BigDecimal.ZERO) == 0) {
            return 0; // ableAmt 等于 0，返回 0
        } else if (ableAmt.compareTo(BigDecimal.ZERO) > 0) {
            return 1; // 正数情况
        } else {
            return -1; // 负数情况
        }
    }

    public static int checkDiffAmt0(BigDecimal a, BigDecimal b) {
        BigDecimal ableAmt = a.subtract(b);
        return checkAmt0(ableAmt);
    }

    /**
     * 获取最大值
     *
     * @param qtys
     * @return
     */
    public static BigDecimal getMaxQty(BigDecimal... qtys) {
        BigDecimal maxQty = BigDecimal.ZERO;
        for (BigDecimal quantity : qtys) {
            if (quantity.compareTo(maxQty) > 0) {
                maxQty = quantity;
            }
        }
        return maxQty;
    }

    /**
     * 判断状态
     *
     * @param min
     * @param max
     * @return
     */
    public static Integer getStatus(BigDecimal min, BigDecimal max) {
        if (min.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        if (min.compareTo(BigDecimal.ZERO) > 0 && min.compareTo(max) < 0) {
            return 1;
        }
        if (min.compareTo(max) >= 0) {
            return 2;
        }
        return -1;
    }


}
