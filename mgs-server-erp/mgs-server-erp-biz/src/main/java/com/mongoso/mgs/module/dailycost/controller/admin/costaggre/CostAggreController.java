package com.mongoso.mgs.module.dailycost.controller.admin.costaggre;

import com.mongoso.mgs.common.job.dailycost.CostAggreJob;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.*;
import com.mongoso.mgs.module.dailycost.service.costaggre.CostAggreService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 成本归集单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cost")
@Validated
public class CostAggreController {

    @Resource
    private CostAggreService aggreService;
    @Resource
    private CostAggreJob costAggreJob;

    @OperateLog("成本归集单添加或编辑")
    @PostMapping("/aggreAdit")
    @PreAuthorize("@ss.hasPermission('costAggre:adit')")
    public ResultX<Long> costAggreAdit(@Valid @RequestBody CostAggreAditReqVO reqVO) {
        return success(reqVO.getAggreId() == null
                            ? aggreService.costAggreAdd(reqVO)
                            : aggreService.costAggreEdit(reqVO));
    }

    @OperateLog("成本归集单删除")
    @PostMapping("/aggreDel")
    @PreAuthorize("@ss.hasPermission('costAggre:delete')")
    public ResultX<Boolean> costAggreDel(@Valid @RequestBody CostAggrePrimaryReqVO reqVO) {
        aggreService.costAggreDel(reqVO.getAggreId());
        return success(true);
    }

    @OperateLog("成本科目管理删除(批量)")
    @PostMapping("/aggreDelBatch")
    @PreAuthorize("@ss.hasPermission('castAggre:delete')")
    public ResultX<BatchResult> aggreDelBatch(@Valid @RequestBody IdReq reqVO) {
        return aggreService.costAggreDelBatch(reqVO);
    }

    @OperateLog("成本归集单详情")
    @PostMapping("/aggreDetail")
    @PreAuthorize("@ss.hasPermission('costAggre:query')")
    public ResultX<CostAggreRespVO> costAggreDetail(@Valid @RequestBody CostAggrePrimaryReqVO reqVO) {
        return success(aggreService.costAggreDetail(reqVO.getAggreId()));
    }

    @OperateLog("成本归集单列表")
    @PostMapping("/aggreList")
    @PreAuthorize("@ss.hasPermission('costAggre:query')")
    @DataPermission
    public ResultX<List<CostAggreRespVO>> costAggreList(@Valid @RequestBody CostAggreQueryReqVO reqVO) {
        return success(aggreService.costAggreList(reqVO));
    }

    @OperateLog("成本归集单分页")
    @PostMapping("/aggrePage")
    @PreAuthorize("@ss.hasPermission('costAggre:query')")
    @DataPermission
    public ResultX<PageResult<CostAggreRespVO>> costAggrePage(@Valid @RequestBody CostAggrePageReqVO reqVO) {
        return success(aggreService.costAggrePage(reqVO));
    }

    @OperateLog("成本归集审核")
    @PostMapping("/aggreApprove")
    @PreAuthorize("@ss.hasPermission('aggre:adit')")
    public ResultX<BatchResult> costAggreApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = aggreService.costAggreApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("成本归集审核")
    @PostMapping("/scheduleTaskTest")
    @PreAuthorize("@ss.hasPermission('aggre:adit')")
    public ResultX<Boolean> scheduleTaskTest() {
        costAggreJob.dailyCostTask();
        return success(true);
    }

    @OperateLog("成本归集回调接口")
    @PostMapping("/aggreFlowCallback")
    public ResultX<Object> aggreFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(aggreService.aggreFlowCallback(reqVO));
    }


}
