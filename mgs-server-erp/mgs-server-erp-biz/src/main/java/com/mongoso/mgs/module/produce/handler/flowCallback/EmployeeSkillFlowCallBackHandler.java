package com.mongoso.mgs.module.produce.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.produce.controller.admin.employeeskill.vo.EmployeeSkillRespVO;
import org.springframework.stereotype.Component;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:40
 * @description: 员工技能等级回调处理类
 */

@Component
public class EmployeeSkillFlowCallBackHandler extends FlowCallbackHandler<EmployeeSkillRespVO> {

    protected EmployeeSkillFlowCallBackHandler(FlowApproveHandler<EmployeeSkillRespVO> approveHandler) {
        super(approveHandler);
    }

}
