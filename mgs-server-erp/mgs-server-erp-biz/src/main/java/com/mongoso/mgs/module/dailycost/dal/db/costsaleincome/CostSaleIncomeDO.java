package com.mongoso.mgs.module.dailycost.dal.db.costsaleincome;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售收入单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_cost_sale_income", autoResultMap = true)
//@KeySequence("u_cost_sale_income_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostSaleIncomeDO extends OperateDO {

    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long saleIncomeId;

    /** 摊销状态 */
    private Short amortiseStatus;

    /** 关联上游单号 */
    private String relatedUpFormCode;

    /** 关联上游单ID */
    private Long relatedUpFormId;

    /** 单据类型 */
    private String outboundTypeDictId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 数量 */
    private BigDecimal qty;

    /** 总金额 */
    private BigDecimal totalAmt;

    /** 订单类型 */
    private Short orderType;

    /** 承担对象ID */
    private String undertakeOrgId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 承担物料id */
    private Long undertakeMaterialId;

    /** 承担物料编码 */
    private String undertakeMaterialCode;

    /**物料BOMID路径*/
    private String materialBomIdPath;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;


}
