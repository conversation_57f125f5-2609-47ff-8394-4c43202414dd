package com.mongoso.mgs.module.finance.service.invoice.invoiceplandetail;

import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo.InvoicePlanDetailAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo.InvoicePlanDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo.InvoicePlanDetailQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo.InvoicePlanDetailRespVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplandetail.InvoicePlanDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceplandetail.InvoicePlanDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.invoice.enums.ErrorCodeConstants.*;


/**
 * 开票计划明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvoicePlanDetailServiceImpl implements InvoicePlanDetailService {

    @Resource
    private InvoicePlanDetailMapper planDetailMapper;

    @Override
    public Long invoicePlanDetailAdd(InvoicePlanDetailAditReqVO reqVO) {
        // 插入
        InvoicePlanDetailDO planDetail = BeanUtilX.copy(reqVO, InvoicePlanDetailDO::new);
        planDetailMapper.insert(planDetail);
        // 返回
        return planDetail.getInvoicePlanDetailId();
    }

    @Override
    public Long invoicePlanDetailEdit(InvoicePlanDetailAditReqVO reqVO) {
        // 校验存在
        this.invoicePlanDetailValidateExists(reqVO.getInvoicePlanDetailId());
        // 更新
        InvoicePlanDetailDO planDetail = BeanUtilX.copy(reqVO, InvoicePlanDetailDO::new);
        planDetailMapper.updateById(planDetail);
        // 返回
        return planDetail.getInvoicePlanDetailId();
    }

    @Override
    public void invoicePlanDetailDel(Long invoicePlanDetailId) {
        // 校验存在
        this.invoicePlanDetailValidateExists(invoicePlanDetailId);
        // 删除
        planDetailMapper.deleteById(invoicePlanDetailId);
    }

    private InvoicePlanDetailDO invoicePlanDetailValidateExists(Long invoicePlanDetailId) {
        InvoicePlanDetailDO planDetail = planDetailMapper.selectById(invoicePlanDetailId);
        if (planDetail == null) {
            // throw exception(PLAN_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "开票计划明细不存在");
        }
        return planDetail;
    }

    @Override
    public InvoicePlanDetailRespVO invoicePlanDetailDetail(Long invoicePlanDetailId) {
        InvoicePlanDetailDO data = planDetailMapper.selectById(invoicePlanDetailId);
        return BeanUtilX.copy(data, InvoicePlanDetailRespVO::new);
    }

    @Override
    public List<InvoicePlanDetailRespVO> invoicePlanDetailList(InvoicePlanDetailQueryReqVO reqVO) {
        List<InvoicePlanDetailDO> data = planDetailMapper.selectList(reqVO);
        return BeanUtilX.copy(data, InvoicePlanDetailRespVO::new);
    }

    @Override
    public PageResult<InvoicePlanDetailRespVO> invoicePlanDetailPage(InvoicePlanDetailPageReqVO reqVO) {
        PageResult<InvoicePlanDetailDO> data = planDetailMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, InvoicePlanDetailRespVO::new);
    }

}
