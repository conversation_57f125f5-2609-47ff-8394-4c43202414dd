package com.mongoso.mgs.module.base.controller.admin.customeraddress.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 客户地址 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class CustomerAddressBaseVO implements Serializable {

    /** 主键 */
    private Long customerAddressId;

    /** 联系人地址 */
    private String contactAddress;

    /** 是否默认地址 */
    private Integer isDefaultAddress;

    /** 备注 */
    private String remark;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /**
     * 关联单据号
     */
    private String relatedOrderCode;

    /** 业务类型["客户","供应商"] **/
    private Integer bizType;

    /** 省ID */
    private Integer provinceId;

    /** 省名称 */
    private String provinceName;

    /** 城市ID */
    private Integer cityId;

    /** 城市名称 */
    private String cityName;

    /** 地区ID */
    private Integer areaId;

    /** 地区名称 */
    private String areaName;

    /** 行号 */
    private Integer rowNo;

}
