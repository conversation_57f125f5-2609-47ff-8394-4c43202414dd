package com.mongoso.mgs.module.base.dal.mysql.saleordermaterial;

import com.mongoso.mgs.common.constants.SqlConstants;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.controller.admin.saleordermaterial.vo.SaleorderMaterialPageReqVO;
import com.mongoso.mgs.module.base.controller.admin.saleordermaterial.vo.SaleorderMaterialQueryReqVO;
import com.mongoso.mgs.module.base.dal.db.saleordermaterial.SaleorderMaterialDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 销售订单产品信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SaleorderMaterialMapper extends BaseMapperX<SaleorderMaterialDO> {

    default PageResult<SaleorderMaterialDO> selectPage(SaleorderMaterialPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<SaleorderMaterialDO>lambdaQueryX()
                .eqIfPresent(SaleorderMaterialDO::getOrderId, reqVO.getOrderId())
                .inIfPresent(SaleorderMaterialDO::getOrderId, reqVO.getOrderIdList())
                .eqIfPresent(SaleorderMaterialDO::getPo, reqVO.getPo())
                .likeIfPresent(SaleorderMaterialDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(SaleorderMaterialDO::getMaterialName, reqVO.getMaterialName())
                .eqIfPresent(SaleorderMaterialDO::getDictMaterialTypeId, reqVO.getDictMaterialTypeId())
                .eqIfPresent(SaleorderMaterialDO::getMainUnit, reqVO.getMainUnit())
                .eqIfPresent(SaleorderMaterialDO::getOrderQty, reqVO.getOrderQty())
                .eqIfPresent(SaleorderMaterialDO::getProdOrderCount, reqVO.getProdOrderCount())
                .eqIfPresent(SaleorderMaterialDO::getActProdCount, reqVO.getActProdCount())
                .eqIfPresent(SaleorderMaterialDO::getOutboundQty, reqVO.getOutboundQty())
                .eqIfPresent(SaleorderMaterialDO::getUnitPrice, reqVO.getUnitPrice())
                .eqIfPresent(SaleorderMaterialDO::getTotalAmt, reqVO.getTotalAmt())
                .betweenIfPresent(SaleorderMaterialDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(SaleorderMaterialDO::getId));
    }

    default List<SaleorderMaterialDO> selectList(SaleorderMaterialQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<SaleorderMaterialDO>lambdaQueryX()
                .eqIfPresent(SaleorderMaterialDO::getOrderId, reqVO.getOrderId())
                .inIfPresent(SaleorderMaterialDO::getOrderId, reqVO.getOrderIdList())
                .eqIfPresent(SaleorderMaterialDO::getMaterialCode, reqVO.getMaterialCode())
                .likeIfPresent(SaleorderMaterialDO::getMaterialName, reqVO.getMaterialName())
                .eqIfPresent(SaleorderMaterialDO::getDictMaterialTypeId, reqVO.getDictMaterialTypeId())
                .orderByDesc(SaleorderMaterialDO::getId));
    }

}
