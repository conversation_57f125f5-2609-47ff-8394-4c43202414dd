package com.mongoso.mgs.module.ai.controller.admin.finance.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

@Data
public class PaymentRespAI {


    private String accountsPayableNumber;// 应收账款单号
    private String customerName;// 客户名称
    private Map<String, String> contactList;// 邮箱
    private BigDecimal realAmt;// 金额
    private LocalDate actRecDate;// 应收日期
    private String overdueDay;// 逾期天数

}
