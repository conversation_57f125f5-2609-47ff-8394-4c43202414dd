package com.mongoso.mgs.module.finance.controller.admin.cashbank.interaccounttran.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import java.util.List;


/**
 * 账间转账 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InterAccountTranPageReqVO extends PageParam {

    /** 转账单号 */
    private String transferCode;

    /** 转账标题 */
    private String transferName;

    /** 转账日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate startTransferDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate endTransferDate;

    /** 备注 */
    private String remark;

    /** 转出账户ID */
    private Long outAccountId;

    private List<Long> outAccountIdList;

    /** 转出账户 */
    private String outAccountName;

    /** 转入账户ID */
    private Long inAccountId;

    private List<Long> inAccountIdList;

    /** 转入账户 */
    private String inAccountName;

    /** 转出账户余额 */
    private BigDecimal outAccountBalance;

    /** 转入账户余额 */
    private BigDecimal inAccountBalance;

    /** 转出账户币种 */
    private String outCurrencyDictName;

    /** 转入账户币种 */
    private String inCurrencyDictName;

    /** 转出金额 */
    private BigDecimal outAmt;

    /** 转入金额 */
    private BigDecimal inAmt;

    /** 转出汇率 */
    private BigDecimal outExchangeRate;

    /** 转入汇率 */
    private BigDecimal inExchangeRate;

    /** 转出手续费 */
    private BigDecimal outServiceFee;

    /** 转入手续费 */
    private BigDecimal inServiceFee;

    /** 汇兑差额 */
    private BigDecimal exchangeDiffAmt;

    /** 责任部门 */
    private String directorOrgId;

    /** 责任人 */
    private Long directorId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

    /** 审核人 */
    private String approvedBy;

}
