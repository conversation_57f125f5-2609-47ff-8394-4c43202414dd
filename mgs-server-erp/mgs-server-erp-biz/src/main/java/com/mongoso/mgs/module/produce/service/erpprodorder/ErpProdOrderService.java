package com.mongoso.mgs.module.produce.service.erpprodorder;

import java.util.*;

import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderPageReqVO;
import jakarta.validation.*;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.bo.ErpProdOrderBO;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.produce.service.materialanalysis.bo.IssuedBO;

/**
 * 生产订单 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpProdOrderService {

    /**
     * 创建生产订单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long erpProdOrderAdd(@Valid ErpProdOrderAditReqVO reqVO);

    /**
     * 更新生产订单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long erpProdOrderEdit(@Valid ErpProdOrderAditReqVO reqVO);

    /**
     * 更新生产订单
     *
     * @param orderBO
     * @return
     */
    void writeBackErpProdOrder(ErpProdOrderBO orderBO);

    /**
     * 删除生产订单
     *
     * @param prodOrderId 编号
     */
    void erpProdOrderDel(Long prodOrderId);

    /**
     * 获得生产订单信息
     *
     * @param prodOrderId 编号
     * @return 生产订单信息
     */
    ErpProdOrderRespVO erpProdOrderDetail(Long prodOrderId);

    /**
     * 获得生产订单列表
     *
     * @param reqVO 查询条件
     * @return 生产订单列表
     */
    List<ErpProdOrderRespVO> erpProdOrderList(@Valid ErpProdOrderQueryReqVO reqVO);

    /**
     * 获得生产订单分页
     *
     * @param reqVO 查询条件
     * @return 生产订单分页
     */
    PageResult<ErpProdOrderRespVO> erpProdOrderPage(@Valid ErpProdOrderPageReqVO reqVO);

    ErpProdOrderRespVO erpProdOrderChangQueryDetail(Long prodOrderChangeId);

    /**
     * 批量删除
     *
     * @param reqVO 编号
     */
    ResultX<BatchResult> erpProdOrderDelBatch(IdReq reqVO);

    BatchResult erpProdOrderApprove(FlowApprove reqVO);

    Object erpProdOrderFlowCallback(FlowCallback reqVO);

    /**
     * 生产订单状态变更
     *
     * @param reqVO
     * @return
     */
    Boolean erpProdOrderStatus(ErpProdOrderPrimaryReqVO reqVO);

    /**
     * 下发生产订单和生产工单
     *
     * @param reqVO
     * @return
     */
    Boolean erpProdOrderFastAdd(IssuedBO reqVO);

    List<ErpProdOrderDO> forewarnJob(Integer dataStatus, List<Integer> statusList);

    PageResult<ErpProdOrderResp> queryErpProdOrderDetailPage(@Valid ErpProdOrderPageReqVO reqVO);


    /**
     * 计算生产订单的可采购数量
     */
//    void computeProdPurchaseQty();
}
