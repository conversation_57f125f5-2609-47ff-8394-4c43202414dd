package com.mongoso.mgs.module.finance.controller.admin.advancepaymentrefund.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 退预收款 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class AdvancePaymentRefundBaseVO implements Serializable {

    /** 退预收款主键ID */
    private Long refundAdvanceId;

    /** 退预收款单号 */
    private String refundAdvanceCode;

    /** 单据类型 */
    @NotNull(message = "单据类型不能为空")
    @Min(value = 1, message = "单据类型值1：销售，2：采购")
    @Max(value = 2, message = "单据类型值1：销售，2：采购")
    private Short formType;

    /** 预收款主键ID */
    @NotNull(message = "预收款不能为空")
    private Long advanceId;

    /** 预收款单号 */
    @NotEmpty(message = "预收款单号不能为空")
    private String advanceCode;

    /** 客户id */
    @NotNull(message = "客户不能为空")
    private Long customerId;

    /** 币种id */
    @NotNull(message = "币种不能为空")
    private String currencyDictId;

    /** 币种名称 */
    private String currencyDictName;

    /** 退款金额 */
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.01", message = "退款金额不能小于0")
    private BigDecimal refundAmt;

    /** 出账账户ID */
    @NotNull(message = "出账账户不能为空")
    private Long outBillAccountId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @NotNull(message = "单据时间不能为空")
    private LocalDateTime formDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核人 */
    private String approvedBy;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;
    /** 备注 */
    private String remark;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币退款金额 */
    private BigDecimal localCurrencyRefundAmt;

}
