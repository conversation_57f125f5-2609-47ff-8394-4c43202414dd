package com.mongoso.mgs.module.purchase.service.processout.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.purchase.controller.admin.processout.bo.PurchaseProcessOutBO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.processout.PurchaseProcessOutMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * 工序委外采购订单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseProcessOutDetailServiceImpl implements PurchaseProcessOutDetailService {

    @Resource
    private PurchaseProcessOutDetailMapper processOutDetailMapper;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private PurchaseProcessOutMapper processOutMapper;

    @Resource
    private ProcessOutDemandMapper processOutDemandMapper;

    @Override
    public Long purchaseProcessOutDetailAdd(PurchaseProcessOutDetailAditReqVO reqVO) {
        // 插入
        PurchaseProcessOutDetailDO processOutDetail = BeanUtilX.copy(reqVO, PurchaseProcessOutDetailDO::new);
        processOutDetailMapper.insert(processOutDetail);
        // 返回
        return processOutDetail.getProcessOutDetailId();
    }

    @Override
    public Long purchaseProcessOutDetailEdit(PurchaseProcessOutDetailAditReqVO reqVO) {
        // 校验存在
        this.purchaseProcessOutDetailValidateExists(reqVO.getProcessOutDetailId());
        // 更新
        PurchaseProcessOutDetailDO processOutDetail = BeanUtilX.copy(reqVO, PurchaseProcessOutDetailDO::new);
        processOutDetailMapper.updateById(processOutDetail);
        // 返回
        return processOutDetail.getProcessOutDetailId();
    }

    @Override
    public void purchaseProcessOutDetailDel(Long processOutDetailId) {
        // 校验存在
        this.purchaseProcessOutDetailValidateExists(processOutDetailId);
        // 删除
        processOutDetailMapper.deleteById(processOutDetailId);
    }

    private PurchaseProcessOutDetailDO purchaseProcessOutDetailValidateExists(Long processOutDetailId) {
        PurchaseProcessOutDetailDO processOutDetail = processOutDetailMapper.selectById(processOutDetailId);
        if (processOutDetail == null) {
            // throw exception(PROCESS_OUT_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "工序委外采购订单明细不存在");
        }
        return processOutDetail;
    }

    @Override
    public PurchaseProcessOutDetailRespVO purchaseProcessOutDetailDetail(Long processOutDetailId) {
        PurchaseProcessOutDetailDO data = processOutDetailMapper.selectById(processOutDetailId);
        return BeanUtilX.copy(data, PurchaseProcessOutDetailRespVO::new);
    }

    @Override
    public List<PurchaseProcessOutDetailRespVO> purchaseProcessOutDetailList(PurchaseProcessOutDetailQueryReqVO reqVO) {
        List<PurchaseProcessOutDetailRespVO> detailRespVOList = processOutDetailMapper.queryList(reqVO);
        this.batchFillVoProperties(detailRespVOList);
        return detailRespVOList;
    }

    @Override
    public PageResult<PurchaseProcessOutDetailRespVO> purchaseProcessOutDetailPage(PurchaseProcessOutDetailPageReqVO reqVO) {
        IPage<PurchaseProcessOutDetailRespVO> respVOIPage = processOutDetailMapper.queryPage(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<PurchaseProcessOutDetailRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        this.batchFillVoProperties4page(pageResult.getList());
        return pageResult;
    }

    /**
     * 工序委外采购订单
     *
     * @param purchaseProcessOutBO
     */
    @Transactional
    @Override
    public void updateReceiptedByReceipt(PurchaseProcessOutBO purchaseProcessOutBO) {

        PurchaseProcessOutDetailDO processOutDetailDO = processOutDetailMapper.selectById(purchaseProcessOutBO.getRelatedOrderDetailId());
        if(processOutDetailDO == null){
            return;
        }
        //已收货数量
        processOutDetailDO.setReceiptedQty(processOutDetailDO.getReceiptedQty().add(purchaseProcessOutBO.getReceiptQty()));
        //可收货数量
        BigDecimal receiptableQty = processOutDetailDO.getPurchaseQty().subtract(processOutDetailDO.getReceiptedQty());
        //可操作数量
        BigDecimal operedableQty = processOutDetailDO.getReceiptedQty().subtract(processOutDetailDO.getOperedQty());
//        // 是否全部收货
        if(receiptableQty.compareTo(BigDecimal.ZERO) <= 0){
            processOutDetailDO.setIsMaterialFullReceipted(1);
        }else{
            processOutDetailDO.setIsMaterialFullReceipted(0);
        }

        // 是否操作完成
        if(operedableQty.compareTo(BigDecimal.ZERO) == 0){
            processOutDetailDO.setIsMaterialFullOpered(1);
        }else{
            processOutDetailDO.setIsMaterialFullOpered(0);
        }

        processOutDetailDO.setUpdatedBy(null);
        processOutDetailDO.setUpdatedDt(null);
        processOutDetailMapper.updateById(processOutDetailDO);

        // 更新工序委外采购订单
        Long purchaseProcessOutId = processOutDetailDO.getPurchaseProcessOutId();
        List<PurchaseProcessOutDetailDO> purchaseProcessOutDetailList = processOutDetailMapper.selectList(PurchaseProcessOutDetailDO::getPurchaseProcessOutId, purchaseProcessOutId);

        //是否全部收货
        Boolean isFullReceipted = true;
        //是否全部操作
        BigDecimal receiptedQty = BigDecimal.ZERO;
        BigDecimal operedQty = BigDecimal.ZERO;
        for (PurchaseProcessOutDetailDO item : purchaseProcessOutDetailList){
            if (item.getIsMaterialFullReceipted() == 0){
                isFullReceipted = false;
            }
            receiptedQty = receiptedQty.add(item.getReceiptedQty());
            operedQty = operedQty.add(item.getOperedQty());
        }

        PurchaseProcessOutDO processOutDO = new PurchaseProcessOutDO();

        //todo 允许超收
        if(isFullReceipted){
            processOutDO.setIsFullReceipted(1);
        }else{
            processOutDO.setIsFullReceipted(0);
        }


        if(receiptedQty.compareTo(BigDecimal.ZERO) == 0){
            processOutDO.setIsFullOpered(1);
        }else if (receiptedQty.compareTo(operedQty) > 0){
            processOutDO.setIsFullOpered(0);
        }else if (receiptedQty.compareTo(operedQty) <= 0){
            processOutDO.setIsFullOpered(1);
        }
        processOutDO.setPurchaseProcessOutId(purchaseProcessOutId);
        processOutDO.setUpdatedBy(null);
        processOutDO.setUpdatedDt(null);
        processOutMapper.updateById(processOutDO);

        //回写工序委外需求清单收货数量
        processOutDemandMapper.updateReceiptQtyById(processOutDetailDO.getProcessOutDemandId(),purchaseProcessOutBO.getReceiptQty());

    }

    /**
     * VO属性填充-批量处理(page)
     *
     * @param detailRespVOList
     */
    private void batchFillVoProperties4page(List<PurchaseProcessOutDetailRespVO> detailRespVOList) {

        if (CollUtilX.isEmpty(detailRespVOList)){
            return;
        }

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        List<Long> directorIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for (PurchaseProcessOutDetailRespVO detailResp: detailRespVOList){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
            directorIdList.add(detailResp.getDirectorId());
            supplierIdList.add(detailResp.getRelatedSupplierId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.CURRENCY.getDictCode(),
                CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.PURCHASE_PAYMENT_TERMS.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询责任人
        Map<Long, String> empNameMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameMap();

        //查询关联供应商
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        //查询物料信息
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        Map<Long, String> invTypeNameMap = erpBaseService.getInvTypeNameMap(invoiceTypeIdList);

        for (PurchaseProcessOutDetailRespVO detailResp: detailRespVOList){
            // 币种
            String currencyDictId = detailResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                detailResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = detailResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                detailResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = detailResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                detailResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 付款条件
            String paymentTermsDictId = detailResp.getPaymentTermsDictId();
            if(StrUtilX.isNotEmpty(paymentTermsDictId)){
                paymentTermsDictId = CustomerDictEnum.PURCHASE_PAYMENT_TERMS.getDictCode() + "-" + paymentTermsDictId;
                detailResp.setPaymentTermsDictName(dictMap.get(paymentTermsDictId));
            }

            // 审核状态
            if(detailResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + detailResp.getDataStatus();
                detailResp.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            detailResp.setDirectorName(empNameMap.get(detailResp.getDirectorId()));
            //责任部门属性填充
            detailResp.setDirectorOrgName(orgNameMap.get(detailResp.getDirectorOrgId()));
            //填充票据类型
            detailResp.setInvoiceTypeName(invTypeNameMap.get(detailResp.getInvoiceTypeId()));
            //供应商信息
            detailResp.setRelatedSupplierName(supplierNameMap.get(detailResp.getRelatedSupplierId()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());
            if (erpMaterialDO!=null){
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
            }
        }
    }

    /**
     * VO属性填充-批量处理(List)
     *
     * @param detailRespVOList
     */
    private void batchFillVoProperties(List<PurchaseProcessOutDetailRespVO> detailRespVOList) {

        if (CollUtilX.isEmpty(detailRespVOList)){
            return;
        }

        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();

        List<Long> materialIdList = new ArrayList<>();
        List<Long> invoiceTypeIdList = new ArrayList<>();
        for (PurchaseProcessOutDetailRespVO detailResp: detailRespVOList){
            materialIdList.add(detailResp.getMaterialId());
            invoiceTypeIdList.add(detailResp.getInvoiceTypeId());
        }

        //查询物料信息
        if (CollUtilX.isNotEmpty(materialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(materialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }
        Map<Long, String> invTypeMap = erpBaseService.getInvTypeNameMap(invoiceTypeIdList);

        for (PurchaseProcessOutDetailRespVO detailResp: detailRespVOList){
            //票据类型
            detailResp.setInvoiceTypeName(invTypeMap.get(detailResp.getInvoiceTypeId()));
            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(detailResp.getMaterialId());

            if (erpMaterialDO !=null){
                detailResp.setMaterialName(erpMaterialDO.getMaterialName());
                detailResp.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                detailResp.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                detailResp.setSpecModel(erpMaterialDO.getSpecModel());
                detailResp.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                detailResp.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                detailResp.setMainUnitDictId(erpMaterialDO.getMainUnitDictId());
            }

            BigDecimal purchaseQty = detailResp.getPurchaseQty();
            BigDecimal receiptedQty = detailResp.getReceiptedQty();
            BigDecimal operedQty = detailResp.getOperedQty();

            BigDecimal operableQty = receiptedQty.subtract(operedQty);
            BigDecimal receiptableQty = purchaseQty.subtract(receiptedQty);
            //可操作数量
            detailResp.setOperableQty(operableQty);
            //可采购数量
            detailResp.setReceiptableQty(receiptableQty);
        }

    }

}
