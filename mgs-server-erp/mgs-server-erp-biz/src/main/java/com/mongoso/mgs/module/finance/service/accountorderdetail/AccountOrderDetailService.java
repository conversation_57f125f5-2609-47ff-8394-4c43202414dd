package com.mongoso.mgs.module.finance.service.accountorderdetail;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.finance.controller.admin.accountorderdetail.vo.AccountOrderDetailAditReqVO;
import com.mongoso.mgs.module.finance.controller.admin.accountorderdetail.vo.AccountOrderDetailPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.accountorderdetail.vo.AccountOrderDetailQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.accountorderdetail.vo.AccountOrderDetailRespVO;

/**
 * 对账单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountOrderDetailService {

    /**
     * 创建对账单明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long accountOrderDetailAdd(@Valid AccountOrderDetailAditReqVO reqVO);

    /**
     * 更新对账单明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long accountOrderDetailEdit(@Valid AccountOrderDetailAditReqVO reqVO);

    /**
     * 删除对账单明细
     *
     * @param id 编号
     */
    void accountOrderDetailDel(Long id);

    /**
     * 获得对账单明细信息
     *
     * @param id 编号
     * @return 对账单明细信息
     */
    AccountOrderDetailRespVO accountOrderDetailDetail(Long id);

    /**
     * 获得对账单明细列表
     *
     * @param reqVO 查询条件
     * @return 对账单明细列表
     */
    List<AccountOrderDetailRespVO> accountOrderDetailList(@Valid AccountOrderDetailQueryReqVO reqVO);

    /**
     * 获得对账单明细分页
     *
     * @param reqVO 查询条件
     * @return 对账单明细分页
     */
    PageResult<AccountOrderDetailRespVO> accountOrderDetailPage(@Valid AccountOrderDetailPageReqVO reqVO);

}
