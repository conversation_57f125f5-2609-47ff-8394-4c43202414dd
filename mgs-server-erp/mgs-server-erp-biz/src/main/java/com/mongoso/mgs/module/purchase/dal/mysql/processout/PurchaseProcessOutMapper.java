package com.mongoso.mgs.module.purchase.dal.mysql.processout;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.PurchaseProcessOutPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.PurchaseProcessOutQueryReqVO;
import com.mongoso.mgs.module.purchase.dal.db.processout.PurchaseProcessOutDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工序委外采购订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseProcessOutMapper extends BaseMapperX<PurchaseProcessOutDO> {

    default PageResult<PurchaseProcessOutDO> selectPage(PurchaseProcessOutPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<PurchaseProcessOutDO>lambdaQueryX()
                .likeIfPresent(PurchaseProcessOutDO::getPurchaseProcessOutCode, reqVO.getPurchaseProcessOutCode())
                .eqIfPresent(PurchaseProcessOutDO::getRelatedSupplierId, reqVO.getRelatedSupplierId())
                .betweenIfPresent(PurchaseProcessOutDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .eqIfPresent(PurchaseProcessOutDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(PurchaseProcessOutDO::getIsFullReceipted, reqVO.getIsFullReceipted())
                .eqIfPresent(PurchaseProcessOutDO::getIsFullOpered, reqVO.getIsFullOpered())
                .inIfPresent(PurchaseProcessOutDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(PurchaseProcessOutDO::getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(PurchaseProcessOutDO::getSettlementMethodDictId, reqVO.getSettlementMethodDictId())
                .eqIfPresent(PurchaseProcessOutDO::getPaymentTermsDictId, reqVO.getPaymentTermsDictId())
                .eqIfPresent(PurchaseProcessOutDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(PurchaseProcessOutDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurchaseProcessOutDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(PurchaseProcessOutDO::getIsForceClose, reqVO.getIsForceClose())
                .betweenIfPresent(PurchaseProcessOutDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .orderByDesc(PurchaseProcessOutDO::getPurchaseProcessOutId));
    }

    default List<PurchaseProcessOutDO> selectList(PurchaseProcessOutQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<PurchaseProcessOutDO>lambdaQueryX()
                .likeIfPresent(PurchaseProcessOutDO::getPurchaseProcessOutCode, reqVO.getPurchaseProcessOutCode())
                .eqIfPresent(PurchaseProcessOutDO::getRelatedSupplierId, reqVO.getRelatedSupplierId())
                .likeIfPresent(PurchaseProcessOutDO::getContactName, reqVO.getContactName())
                .eqIfPresent(PurchaseProcessOutDO::getContactPhone, reqVO.getContactPhone())
                .eqIfPresent(PurchaseProcessOutDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .eqIfPresent(PurchaseProcessOutDO::getSettlementMethodDictId, reqVO.getSettlementMethodDictId())
                .betweenIfPresent(PurchaseProcessOutDO::getDeliveryDate, reqVO.getStartDeliveryDate(), reqVO.getEndDeliveryDate())
                .eqIfPresent(PurchaseProcessOutDO::getPaymentTermsDictId, reqVO.getPaymentTermsDictId())
                .eqIfPresent(PurchaseProcessOutDO::getInvoiceTypeId, reqVO.getInvoiceTypeId())
                .eqIfPresent(PurchaseProcessOutDO::getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(PurchaseProcessOutDO::getExclTaxTotalAmt, reqVO.getExclTaxTotalAmt())
                .eqIfPresent(PurchaseProcessOutDO::getInclTaxTotalAmt, reqVO.getInclTaxTotalAmt())
                .eqIfPresent(PurchaseProcessOutDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PurchaseProcessOutDO::getDataStatus, reqVO.getDataStatus())
                .inIfPresent(PurchaseProcessOutDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(PurchaseProcessOutDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurchaseProcessOutDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(PurchaseProcessOutDO::getIsForceClose, reqVO.getIsForceClose())
                .eqIfPresent(PurchaseProcessOutDO::getIsFullOpered, reqVO.getIsFullOpered())
                .eqIfPresent(PurchaseProcessOutDO::getIsFullReceipted, reqVO.getIsFullReceipted())
                .betweenIfPresent(PurchaseProcessOutDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .betweenIfPresent(PurchaseProcessOutDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(PurchaseProcessOutDO::getApprovedBy, reqVO.getApprovedBy())
                .inIfPresent(PurchaseProcessOutDO::getFormStatus, reqVO.getFormStatusList())
                .eqIfPresent(PurchaseProcessOutDO::getFormStatus, reqVO.getFormStatus())
                .betweenIfPresent(PurchaseProcessOutDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(PurchaseProcessOutDO::getCreatedDt));
    }

    default PurchaseProcessOutDO selectOne(PurchaseProcessOutQueryReqVO reqVO) {
        return selectOne(LambdaQueryWrapperX.<PurchaseProcessOutDO>lambdaQueryX()
                .eqIfPresent(PurchaseProcessOutDO::getPurchaseProcessOutId, reqVO.getPurchaseProcessOutId())
                .eqIfPresent(PurchaseProcessOutDO::getPurchaseProcessOutCode, reqVO.getPurchaseProcessOutCode()));
    }

    default List<PurchaseProcessOutDO> forewarnJob(Integer dataStatus, List<Integer> formStatusList, Integer isFullReceipted){
        return selectList(LambdaQueryWrapperX.<PurchaseProcessOutDO>lambdaQueryX()
                .eq(PurchaseProcessOutDO::getDataStatus, dataStatus)
                .in(PurchaseProcessOutDO::getFormStatus, formStatusList)
                .eq(PurchaseProcessOutDO::getIsFullReceipted, isFullReceipted));
    }
}