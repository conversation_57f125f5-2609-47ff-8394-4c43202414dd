package com.mongoso.mgs.module.finance.controller.admin.shouldpaymentdetail.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 应收付账款明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShouldPaymentDetailPageReqVO extends PageParam {

    /** 单据类型 */
    private Short formType;

    /** 应收款主键ID */
    private Long paymentId;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 来源单号 */
    private String sourceOrderNumber;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 来源单id */
    private Long sourceOrderId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 主单位ID */
    private String mainUnitDictId;

    /** 来源单据数量 */
    private BigDecimal sourceDocumentQty;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    private Long invoiceTypeDictId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 剩余应收数量 */
    private BigDecimal receiveableQty;

    /** 本次应收数量 */
    private BigDecimal currentReceiveQty;

    /** 本次应收金额(不含税) */
    private BigDecimal exclCurrentReceiveAmt;

    /** 本次应收金额(含税) */
    private BigDecimal inclCurrentReceiveAmt;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
