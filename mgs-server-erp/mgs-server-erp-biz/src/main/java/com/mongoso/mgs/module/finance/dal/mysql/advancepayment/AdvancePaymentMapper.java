package com.mongoso.mgs.module.finance.dal.mysql.advancepayment;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.advancepayment.vo.AdvancePaymentPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.advancepayment.vo.AdvancePaymentQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.advancepayment.AdvancePaymentDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预收款 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AdvancePaymentMapper extends BaseMapperX<AdvancePaymentDO> {

    default PageResult<AdvancePaymentDO> selectPageOld(AdvancePaymentPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<AdvancePaymentDO>lambdaQueryX()
                .likeIfPresent(AdvancePaymentDO::getAdvanceCode, reqVO.getAdvanceCode())
                .eqIfPresent(AdvancePaymentDO::getFormType, reqVO.getFormType())
                .eqIfPresent(AdvancePaymentDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AdvancePaymentDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(AdvancePaymentDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AdvancePaymentDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AdvancePaymentDO::getAdvanceAmt, reqVO.getAdvanceAmt())
                .eqIfPresent(AdvancePaymentDO::getRemainingAmt, reqVO.getRemainingAmt())
                .eqIfPresent(AdvancePaymentDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(AdvancePaymentDO::getTicketBillPackageNo, reqVO.getTicketBillPackageNo())
                .eqIfPresent(AdvancePaymentDO::getInBillType, reqVO.getInBillType())
                .betweenIfPresent(AdvancePaymentDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AdvancePaymentDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AdvancePaymentDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(AdvancePaymentDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(AdvancePaymentDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AdvancePaymentDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AdvancePaymentDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(AdvancePaymentDO::getCreatedDt));
    }



    default PageResult<AdvancePaymentDO> selectPage(AdvancePaymentPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<AdvancePaymentDO>lambdaQueryX()
                .likeIfPresent(AdvancePaymentDO::getAdvanceCode, reqVO.getAdvanceCode())
                .eqIfPresent(AdvancePaymentDO::getFormType, reqVO.getFormType())
                .likeIfPresent(AdvancePaymentDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AdvancePaymentDO::getCustomerId, reqVO.getCustomerId())
                .inIfPresent(AdvancePaymentDO::getCustomerId, reqVO.getCustomerIdList())
                .eqIfPresent(AdvancePaymentDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AdvancePaymentDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AdvancePaymentDO::getAdvanceAmt, reqVO.getAdvanceAmt())
                .eqIfPresent(AdvancePaymentDO::getRemainingAmt, reqVO.getRemainingAmt())
                .eqIfPresent(AdvancePaymentDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(AdvancePaymentDO::getAcceptBillId, reqVO.getAcceptBillId())
                .likeIfPresent(AdvancePaymentDO::getTicketBillPackageNo, reqVO.getTicketBillPackageNo())
                .eqIfPresent(AdvancePaymentDO::getInBillType, reqVO.getInBillType())
                .betweenIfPresent(AdvancePaymentDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AdvancePaymentDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AdvancePaymentDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(AdvancePaymentDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(AdvancePaymentDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AdvancePaymentDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AdvancePaymentDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(AdvancePaymentDO::getCreatedDt));
    }

    default List<AdvancePaymentDO> selectListOld(AdvancePaymentQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<AdvancePaymentDO>lambdaQueryX()
                .eqIfPresent(AdvancePaymentDO::getUseStatus, reqVO.getUseStatus())
                .inIfPresent(AdvancePaymentDO::getUseStatus, reqVO.getUseStatusList())
                .eqIfPresent(AdvancePaymentDO::getFormType, reqVO.getFormType())
                .likeIfPresent(AdvancePaymentDO::getAdvanceCode, reqVO.getAdvanceCode())
                .likeIfPresent(AdvancePaymentDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AdvancePaymentDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(AdvancePaymentDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AdvancePaymentDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AdvancePaymentDO::getAdvanceAmt, reqVO.getAdvanceAmt())
                .eqIfPresent(AdvancePaymentDO::getRemainingAmt, reqVO.getRemainingAmt())
                .eqIfPresent(AdvancePaymentDO::getInBillAccountId, reqVO.getInBillAccountId())
                .likeIfPresent(AdvancePaymentDO::getTicketBillPackageNo, reqVO.getTicketBillPackageNo())
                .eqIfPresent(AdvancePaymentDO::getInBillType, reqVO.getInBillType())
                .betweenIfPresent(AdvancePaymentDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AdvancePaymentDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AdvancePaymentDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(AdvancePaymentDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(AdvancePaymentDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AdvancePaymentDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AdvancePaymentDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                    .orderByDesc(AdvancePaymentDO::getCreatedDt));
    }

    default List<AdvancePaymentDO> selectList(AdvancePaymentQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<AdvancePaymentDO>lambdaQueryX()
                .eqIfPresent(AdvancePaymentDO::getUseStatus, reqVO.getUseStatus())
                .inIfPresent(AdvancePaymentDO::getUseStatus, reqVO.getUseStatusList())
                .eqIfPresent(AdvancePaymentDO::getFormType, reqVO.getFormType())
                .eqIfPresent(AdvancePaymentDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AdvancePaymentDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(AdvancePaymentDO::getCurrencyDictId, reqVO.getCurrencyDictId())
                .likeIfPresent(AdvancePaymentDO::getAdvanceCode, reqVO.getAdvanceCode())
                .likeIfPresent(AdvancePaymentDO::getCurrencyDictName, reqVO.getCurrencyDictName())
                .eqIfPresent(AdvancePaymentDO::getAdvanceAmt, reqVO.getAdvanceAmt())
                .eqIfPresent(AdvancePaymentDO::getRemainingAmt, reqVO.getRemainingAmt())
                .eqIfPresent(AdvancePaymentDO::getInBillAccountId, reqVO.getInBillAccountId())
                .eqIfPresent(AdvancePaymentDO::getAcceptBillId, reqVO.getAcceptBillId())
                .eqIfPresent(AdvancePaymentDO::getTicketBillPackageNo, reqVO.getTicketBillPackageNo())
                .eqIfPresent(AdvancePaymentDO::getInBillType, reqVO.getInBillType())
                .betweenIfPresent(AdvancePaymentDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AdvancePaymentDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AdvancePaymentDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .eqIfPresent(AdvancePaymentDO::getApprovedBy, reqVO.getApprovedBy())
                .eqIfPresent(AdvancePaymentDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AdvancePaymentDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AdvancePaymentDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(AdvancePaymentDO::getCreatedDt));
    }

}