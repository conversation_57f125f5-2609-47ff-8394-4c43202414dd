package com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceapply.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.LocalDate;

import static com.mongoso.mgs.framework.common.util.DateUtilX.*;

/**
 * 开票申请 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class InvoiceApplyBaseVO implements Serializable {

    /** 开票申请id */
    private Long invoiceApplyId;

    /** 开票计划明细id */
    private Long invoicePlanDetailId;

    /** 开票计划id */
    private Long invoicePlanId;

    /** 开票方向 */
    private Short billingDirection;

    /** 开票状态 */
    private Short invoiceStatus;

    /** 开票申请单号 */
    private String invoiceApplyNo;

    /** 客户id */
    private Long customerId;

    /** 开票方向 */
    private Short invoiceDirection;

    /** 币种id */
    private String currencyDictId;

    /** 审核人 */
    private String approvedBy;

    /** 申请开票总额（含税） */
    private BigDecimal applyInvoiceTotalInclTax;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 应开日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate expectedInvoiceDate;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 票据类型 */
    private Long invoiceTypeDictId;
    private String invoiceTypeDictName;

    /** 备注 */
    private String remark;

    /** 购方名称 */
    private String buyerCompany;

    /** 购方纳税人识别号 */
    private String buyerIdentifyNo;

    /** 购方地址 */
    private String buyerAddress;

    /** 购方电话 */
    private String buyerTel;

    /** 购方开户银行 */
    private String buyerBank;

    /** 购方银行账户 */
    private String buyerBankAccount;

    /** 消方名称 */
    private String cusCompany;

    /** 消方纳税人识别号 */
    private String cusIdentifyNo;

    /** 消方地址 */
    private String cusAddress;

    /** 消方电话 */
    private String cusTel;

    /** 消方开户银行 */
    private String cusBank;

    /** 消方银行账户 */
    private String cusBankAccount;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    private BigDecimal applyInvoiceTotalExclTax;

    private BigDecimal totalTax;

    private BigDecimal readyQty;

    private BigDecimal readyAmt;

    private BigDecimal canQty;

    private BigDecimal canAmt;

    private BigDecimal applyInvoiceQty;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币申请开票总额（含税） */
    private BigDecimal localCurrencyApplyInvoiceTotalInclTax;

    /** 物料编码 */
    private String materialCode;
}
