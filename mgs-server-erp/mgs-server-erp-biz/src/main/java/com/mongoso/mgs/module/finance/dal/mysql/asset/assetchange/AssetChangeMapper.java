package com.mongoso.mgs.module.finance.dal.mysql.asset.assetchange;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.AssetChangePageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.AssetChangeQueryReqVO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetchange.AssetChangeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产变动 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssetChangeMapper extends BaseMapperX<AssetChangeDO> {

    default PageResult<AssetChangeDO> selectPageOld(AssetChangePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<AssetChangeDO>lambdaQueryX()
                .likeIfPresent(AssetChangeDO::getChangeCode, reqVO.getChangeCode())
                .likeIfPresent(AssetChangeDO::getChangeName, reqVO.getChangeName())
                .eqIfPresent(AssetChangeDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetChangeDO::getAssetStatusId, reqVO.getAssetStatusId())
                .eqIfPresent(AssetChangeDO::getChangeStatusId, reqVO.getChangeStatusId())
                .eqIfPresent(AssetChangeDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetChangeDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetChangeDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetChangeDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetChangeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetChangeDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetChangeDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(AssetChangeDO::getCreatedDt));
    }



    default PageResult<AssetChangeDO> selectPage(AssetChangePageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<AssetChangeDO>lambdaQueryX()
                .likeIfPresent(AssetChangeDO::getChangeCode, reqVO.getChangeCode())
                .likeIfPresent(AssetChangeDO::getChangeName, reqVO.getChangeName())
                .eqIfPresent(AssetChangeDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetChangeDO::getAssetStatusId, reqVO.getAssetStatusId())
                .eqIfPresent(AssetChangeDO::getChangeStatusId, reqVO.getChangeStatusId())
                .eqIfPresent(AssetChangeDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetChangeDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetChangeDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetChangeDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetChangeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetChangeDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetChangeDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(AssetChangeDO::getCreatedDt));
    }

    default List<AssetChangeDO> selectListOld(AssetChangeQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<AssetChangeDO>lambdaQueryX()
                .likeIfPresent(AssetChangeDO::getChangeCode, reqVO.getChangeCode())
                .likeIfPresent(AssetChangeDO::getChangeName, reqVO.getChangeName())
                .eqIfPresent(AssetChangeDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetChangeDO::getAssetStatusId, reqVO.getAssetStatusId())
                .eqIfPresent(AssetChangeDO::getChangeStatusId, reqVO.getChangeStatusId())
                .eqIfPresent(AssetChangeDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetChangeDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetChangeDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetChangeDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetChangeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetChangeDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetChangeDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(AssetChangeDO::getCreatedDt));
    }

    default List<AssetChangeDO> selectList(AssetChangeQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<AssetChangeDO>lambdaQueryX()
                .likeIfPresent(AssetChangeDO::getChangeCode, reqVO.getChangeCode())
                .likeIfPresent(AssetChangeDO::getChangeName, reqVO.getChangeName())
                .eqIfPresent(AssetChangeDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetChangeDO::getAssetStatusId, reqVO.getAssetStatusId())
                .eqIfPresent(AssetChangeDO::getChangeStatusId, reqVO.getChangeStatusId())
                .eqIfPresent(AssetChangeDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetChangeDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetChangeDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetChangeDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetChangeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetChangeDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetChangeDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(AssetChangeDO::getCreatedDt));
    }

}