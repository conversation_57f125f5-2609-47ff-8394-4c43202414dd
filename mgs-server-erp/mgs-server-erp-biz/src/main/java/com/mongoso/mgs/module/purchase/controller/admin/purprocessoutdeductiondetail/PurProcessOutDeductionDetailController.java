package com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail.vo.*;
import com.mongoso.mgs.module.purchase.dal.db.purprocessoutdeductiondetail.PurProcessOutDeductionDetailDO;
import com.mongoso.mgs.module.purchase.service.purprocessoutdeductiondetail.PurProcessOutDeductionDetailService;

/**
 * 工序委外采购扣费明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchase")
@Validated
public class PurProcessOutDeductionDetailController {

    @Resource
    private PurProcessOutDeductionDetailService purProcessOutDeductionDetailService;

    @OperateLog("工序委外采购扣费明细添加或编辑")
    @PostMapping("/purProcessOutDeductionDetailAdit")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeductionDetail:adit')")
    public ResultX<Long> purProcessOutDeductionDetailAdit(@Valid @RequestBody PurProcessOutDeductionDetailAditReqVO reqVO) {
        return success(reqVO.getProcessOutDeductionDetailId() == null
                            ? purProcessOutDeductionDetailService.purProcessOutDeductionDetailAdd(reqVO)
                            : purProcessOutDeductionDetailService.purProcessOutDeductionDetailEdit(reqVO));
    }

    @OperateLog("工序委外采购扣费明细删除")
    @PostMapping("/purProcessOutDeductionDetailDel")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeductionDetail:delete')")
    public ResultX<Boolean> purProcessOutDeductionDetailDel(@Valid @RequestBody PurProcessOutDeductionDetailPrimaryReqVO reqVO) {
        purProcessOutDeductionDetailService.purProcessOutDeductionDetailDel(reqVO.getProcessOutDeductionDetailId());
        return success(true);
    }

    @OperateLog("工序委外采购扣费明细详情")
    @PostMapping("/purProcessOutDeductionDetailDetail")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeductionDetail:query')")
    public ResultX<PurProcessOutDeductionDetailRespVO> purProcessOutDeductionDetailDetail(@Valid @RequestBody PurProcessOutDeductionDetailPrimaryReqVO reqVO) {
        return success(purProcessOutDeductionDetailService.purProcessOutDeductionDetailDetail(reqVO.getProcessOutDeductionDetailId()));
    }

    @OperateLog("工序委外采购扣费明细列表")
    @PostMapping("/purProcessOutDeductionDetailList")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeductionDetail:query')")
    public ResultX<List<PurProcessOutDeductionDetailRespVO>> purProcessOutDeductionDetailList(@Valid @RequestBody PurProcessOutDeductionDetailQueryReqVO reqVO) {
        return success(purProcessOutDeductionDetailService.purProcessOutDeductionDetailList(reqVO));
    }

    @OperateLog("工序委外采购扣费明细分页")
    @PostMapping("/purProcessOutDeductionDetailPage")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeductionDetail:query')")
    public ResultX<PageResult<PurProcessOutDeductionDetailRespVO>> purProcessOutDeductionDetailPage(@Valid @RequestBody PurProcessOutDeductionDetailPageReqVO reqVO) {
        return success(purProcessOutDeductionDetailService.purProcessOutDeductionDetailPage(reqVO));
    }

}
