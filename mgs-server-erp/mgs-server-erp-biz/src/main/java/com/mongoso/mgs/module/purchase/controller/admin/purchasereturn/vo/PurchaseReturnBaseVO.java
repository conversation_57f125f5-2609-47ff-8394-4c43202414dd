package com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo;

import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.detail.PurchaseReturnDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 采购退货单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PurchaseReturnBaseVO implements Serializable {

    /** 采购退货单ID */
    private Long purchaseReturnId;

    /** 采购退货单号 */
    private String purchaseReturnCode;

    /** 采购退货单类型 */
    private String returnTypeDictId;

    /** 采购订单ID */
    private Long purchaseOrderId;

    /** 采购订单号 */
    private String purchaseOrderCode;

    /** 采购订单类型字典ID */
    private String purchaseTypeDictId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 联系人名称 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 交货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate deliveryDate;

    /** 退货日期 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate returnDate;

    /** 币种 */
    private String currencyDictId;

    /** 主体公司 */
    private String companyOrgId;

    /** 退款条件 */
    private String refundConditionDictId;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 退款总金额(不含税) */
    private BigDecimal exclTaxTotalAmt;

    /** 退款总金额(含税) */
    private BigDecimal inclTaxTotalAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 是否下发采购退货出库单 */
    private Integer isIssueReturnOut;

    /** 是否完成出库 */
    private Integer isFullOutbounded;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币订单总金额 */
    private BigDecimal inclTaxLocalCurrencyTotalAmt;

}
