package com.mongoso.mgs.module.finance.controller.admin.asset.assetchange;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetchange.vo.*;
import com.mongoso.mgs.module.finance.service.asset.assetchange.AssetChangeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 资产变动 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/finance")
@Validated
public class AssetChangeController {

    @Resource
    private AssetChangeService assetChangeService;

    @OperateLog("资产变动添加或编辑")
    @PostMapping("/assetChangeAdit")
    @PreAuthorize("@ss.hasPermission('assetChange:adit')")
    public ResultX<Long> assetChangeAdit(@Valid @RequestBody AssetChangeAditReqVO reqVO) {
        return success(reqVO.getAssetChangeId() == null
                            ? assetChangeService.assetChangeAdd(reqVO)
                            : assetChangeService.assetChangeEdit(reqVO));
    }

    @OperateLog("资产变动删除")
    @PostMapping("/assetChangeDel")
    @PreAuthorize("@ss.hasPermission('assetChange:delete')")
    public ResultX<Boolean> assetChangeDel(@Valid @RequestBody AssetChangePrimaryReqVO reqVO) {
        assetChangeService.assetChangeDel(reqVO.getAssetChangeId());
        return success(true);
    }

    @OperateLog("资产变动详情")
    @PostMapping("/assetChangeDetail")
    @PreAuthorize("@ss.hasPermission('assetChange:query')")
    public ResultX<AssetChangeRespVO> assetChangeDetail(@Valid @RequestBody AssetChangePrimaryReqVO reqVO) {
        return success(assetChangeService.assetChangeDetail(reqVO.getAssetChangeId()));
    }

    @OperateLog("资产变动列表")
    @PostMapping("/assetChangeList")
    @PreAuthorize("@ss.hasPermission('assetChange:query')")
    @DataPermission
    public ResultX<List<AssetChangeRespVO>> assetChangeList(@Valid @RequestBody AssetChangeQueryReqVO reqVO) {
        return success(assetChangeService.assetChangeList(reqVO));
    }

    @OperateLog("资产变动分页")
    @PostMapping("/assetChangePage")
    @PreAuthorize("@ss.hasPermission('assetChange:query')")
    @DataPermission
    public ResultX<PageResult<AssetChangeRespVO>> assetChangePage(@Valid @RequestBody AssetChangePageReqVO reqVO) {
        return success(assetChangeService.assetChangePage(reqVO));
    }

    @OperateLog("资产变动批量删除")
    @PostMapping("/assetChangeDelBatch")
    @PreAuthorize("@ss.hasPermission('assetChange:del')")
    public ResultX<BatchResult> assetChangeDelBatch(@Valid @RequestBody IdReq reqVO) {
        return assetChangeService.assetChangeDelBatch(reqVO);
    }

    @OperateLog("资产变动审核")
    @PostMapping("/assetChangeApprove")
    @PreAuthorize("@ss.hasPermission('assetChange:adit')")
    public ResultX<BatchResult> assetChangeApprove(@Valid @RequestBody FlowApprove reqVO) {
        return success(assetChangeService.assetChangeApprove(reqVO));
    }

    @OperateLog("资产变动回调接口")
    @PostMapping("/assetChangeFlowCallback")
    public ResultX<Object> assetChangeFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(assetChangeService.assetChangeFlowCallback(reqVO));
    }

}
