package com.mongoso.mgs.module.produce.controller.admin.materialalternative.vo.detail;

import lombok.*;

import java.io.Serializable;

  
import java.math.BigDecimal;

/**
 * 替代物料明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MaterialAlternativeDetailBaseVO implements Serializable {

    /** 主键 */
    private Long id;

    /** 替代物料id */
    private Long alternativeMaterialId;

    /** 替代物料编码 */
    private String alternativeMaterialCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 行号 */
    private Integer rowNo;

    /** 主数量 */
    private BigDecimal mainQty;

    /** 替代数量 */
    private BigDecimal alternativeQty;

    /** 优先级 */
    private Integer priority;

}
