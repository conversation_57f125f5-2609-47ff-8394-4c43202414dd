package com.mongoso.mgs.module.purchase.controller.admin.processout.vo;

import com.mongoso.mgs.module.purchase.controller.admin.processout.vo.detail.PurchaseProcessOutDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailAditReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 工序委外采购订单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseProcessOutAditReqVO extends PurchaseProcessOutBaseVO {

    /** 采购明细列表 */
    private List<PurchaseProcessOutDetailAditReqVO> detailList;

}
