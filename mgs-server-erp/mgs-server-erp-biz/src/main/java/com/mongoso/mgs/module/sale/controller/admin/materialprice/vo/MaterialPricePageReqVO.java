package com.mongoso.mgs.module.sale.controller.admin.materialprice.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  
import java.math.BigDecimal;
 

/**
 * 商品价格 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialPricePageReqVO extends PageParam {

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 创建人ID */
    private Long createdId;

    /** 备注 */
    private String remark;

    /** 客户订货价 */
    private BigDecimal customerOrderPrice;

    /** 物料ID */
    private Long materialId;

    /** 关联单据ID **/
    private Long relatedOrderId;

}
