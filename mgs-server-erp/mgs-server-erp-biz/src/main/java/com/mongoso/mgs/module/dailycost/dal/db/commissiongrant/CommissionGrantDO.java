package com.mongoso.mgs.module.dailycost.dal.db.commissiongrant;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提成发放 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_commission_grant", autoResultMap = true)
//@KeySequence("u_commission_grant_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommissionGrantDO extends OperateDO {

    /** 提成发放ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long commissionGrantId;

    /** 提成发放单号 */
    private String commissionGrantCode;

    /** 提成任务ID */
    private Long commissionTaskId;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单id */
    private Long sourceOrderId;

    /** 来源单行号 */
    private Integer sourceOrderRowNo;

    /** 单据类型 */
    private Integer formType;

    /** 数量 */
    private BigDecimal qty;

    /** 行金额 (不含税) */
    private BigDecimal exclTaxAmt;

    /** 提成比例 */
    private BigDecimal commissionRatio;

    /** 提成金额 */
    private BigDecimal commissionAmt;

    /** 单据时间 */
    private LocalDateTime formDt;


}
