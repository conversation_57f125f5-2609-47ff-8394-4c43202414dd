package com.mongoso.mgs.module.warehouse.handler.approve;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.finance.service.common.FinanceConnectService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderRespVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailBaseVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.warehouse.dal.db.erpdelivery.ErpDeliveryDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpdelivery.ErpDeliveryDetailDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDetailDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpdelivery.ErpDeliveryDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpdelivery.ErpDeliveryMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper;
import com.mongoso.mgs.module.warehouse.enums.DeliveryDetailEnum;
import com.mongoso.mgs.module.warehouse.enums.ErpDeliveryBizTypeEnum;
import com.mongoso.mgs.module.warehouse.service.erpoutbound.ErpOutboundDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Fashoin.Liu
 * @date: 2024/12/19 11:34
 * @description: 发货单审批流程处理类
 */

@Component
public class  ErpDeliveryApproveHandler extends FlowApproveHandler<ErpDeliveryDO> {

    @Resource
    private ErpDeliveryMapper erpDeliveryMapper;

    @Resource
    private ErpDeliveryDetailMapper erpDeliveryDetailMapper;

    @Resource
    private ErpOutboundMapper erpOutboundMapper;

    @Resource
    private ErpOutboundDetailMapper erpOutboundDetailMapper;

    @Resource
    private ErpOutboundDetailService erpOutboundDetailService;

    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private FinanceConnectService financeConnectService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(ErpDeliveryDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(ErpDeliveryDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(ErpDeliveryDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getDeliveryId())
                .objCode(item.getDeliveryCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(ErpDeliveryDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            Long deliveryId = item.getDeliveryId();

            if(item.getBizType() == ErpDeliveryBizTypeEnum.OUTBOUND_DELIVERY.getType()){

                ErpOutboundDO erpOutboundDO = erpOutboundMapper.selectById(item.getRelatedOrderId());
                if(erpOutboundDO == null){
                    failItem.setCode(item.getDeliveryCode());
                    failItem.setReason("关联的出库单已删除,不可操作审核");
                    return false;
                }
                if(erpOutboundDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()){
                    failItem.setCode(item.getDeliveryCode());
                    failItem.setReason("关联的出库单未审核,不可操作审核");
                    return false;
                }

                //查询发货单
                List<DocumentRespBO> deliveryRespList = erpDeliveryDetailMapper.deliveryQtyList(deliveryId);
                Map<Long, DocumentRespBO> deliveryQtyMap = deliveryRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, doc -> doc));

                //查询出库单
                List<DocumentRespBO> deliverableRespList = erpOutboundDetailMapper.deliverableQtyList(item.getRelatedOrderId());
                Map<Long, BigDecimal> deliverableQtyMap = deliverableRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断发货数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : deliveryQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal deliverableQty = deliverableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = deliveryQtyMap.get(relatedOrderDetailId);
                    if (deliverableQty == null || documentRespBO.getSumQty().compareTo(deliverableQty) > 0){
                        failItem.setCode(item.getDeliveryCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】发货数量不允许超过可发货数量!");
                        return false;
                    }
                }
            }
        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

            //财务反审校验
            financeConnectService.checkOrder(item.getDeliveryId());
        }

        return true;
    }

    @Override
    public Integer handleBusinessData(ErpDeliveryDO item, BaseApproveRequest request) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        Integer buttonType = request.getButtonType();
        Long id = item.getDeliveryId();
        Integer dataStatus = request.getDataStatus();

        ErpDeliveryDO erpDeliveryDO = erpDeliveryMapper.selectById(id);
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            //出库发货单
            if(erpDeliveryDO.getBizType() == ErpDeliveryBizTypeEnum.OUTBOUND_DELIVERY.getType()){
                //查询发货单
                List<DocumentRespBO> deliveryRespList = erpDeliveryDetailMapper.deliveryQtyList(id);
                for(DocumentRespBO docRespBO : deliveryRespList){
                    Long outboundDetailId = docRespBO.getFieldId();
                    BigDecimal deliveryQty = docRespBO.getSumQty();
                    // 增加出库单已发货数量
                    erpOutboundDetailService.updateDeliveredQty(outboundDetailId, deliveryQty);
                }
            }
        }

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //出库发货单
            if(erpDeliveryDO.getBizType() == ErpDeliveryBizTypeEnum.OUTBOUND_DELIVERY.getType()){
                //查询发货单
                List<DocumentRespBO> deliveryRespList = erpDeliveryDetailMapper.deliveryQtyList(id);
                for(DocumentRespBO docRespBO : deliveryRespList){
                    Long outboundDetailId = docRespBO.getFieldId();
                    BigDecimal deliveryQty = docRespBO.getSumQty().negate();
                    // 增加出库单已发货数量
                    erpOutboundDetailService.updateDeliveredQty(outboundDetailId, deliveryQty);
                }
            }
        }

        //更新业务数据
        erpDeliveryDO.setApprovedBy(loginUser.getFullUserName());
        erpDeliveryDO.setApprovedDt(LocalDateTime.now());
        erpDeliveryDO.setDataStatus(dataStatus);
        Integer updateCount = erpDeliveryMapper.updateById(erpDeliveryDO);

        //进入财务系统
        //查询销售订单
        if (erpDeliveryDO.getBizType() == 1 && erpDeliveryDO.getSaleOrderId() != null) {//出库发货单
            ErpSaleOrderRespVO erpSaleOrderRespVO = erpSaleOrderService.erpSaleOrderDetail(erpDeliveryDO.getSaleOrderId());
            if (Objects.nonNull(erpSaleOrderRespVO)){
                //重新处理数据
                List<ErpDeliveryDetailDO> erpDeliveryDetailDOList = erpDeliveryDetailMapper.selectList(new LambdaQueryWrapper<ErpDeliveryDetailDO>()
                        .eq(ErpDeliveryDetailDO::getDeliveryId, id)
                        .eq(ErpDeliveryDetailDO::getDetailTypeDictId, DeliveryDetailEnum.MATERIAL_DETAIL.type.toString())
                );
                Map<Long, ErpSaleOrderDetailRespVO> detailListMap = erpSaleOrderRespVO.getDetailList().stream().collect(Collectors.toMap(ErpSaleOrderDetailBaseVO::getMaterialId, Function.identity(), (s1, s2) -> s1));
                erpSaleOrderRespVO.setSaleOrderId(erpDeliveryDO.getDeliveryId());
                erpSaleOrderRespVO.setSaleOrderCode(erpDeliveryDO.getDeliveryCode());
                List<ErpSaleOrderDetailRespVO> listDetail = new ArrayList<>();
                for (ErpDeliveryDetailDO detail : erpDeliveryDetailDOList){
                    ErpSaleOrderDetailRespVO detailRespVO = detailListMap.get(detail.getMaterialId());
                    if (Objects.isNull(detailRespVO)){
                        continue;
                    }
                    detailRespVO.setQty(detail.getDeliveryQty());
                    detailRespVO.setInclTaxAmt(detailRespVO.getInclTaxPrice().multiply(detail.getDeliveryQty()));
                    detailRespVO.setExclTaxAmt(detailRespVO.getExclTaxPrice().multiply(detail.getDeliveryQty()));
                    detailRespVO.setSaleOrderDetailId(detail.getDeliveryDetailId());
                    listDetail.add(detailRespVO);
                }
                erpSaleOrderRespVO.setDetailList(listDetail);
                erpSaleOrderRespVO.setSourceFormType(OrderTypeEnum.SALE_INVOICES_ORDER.type.shortValue());

                //源头单据id
                erpSaleOrderRespVO.setOriginOrderId(erpDeliveryDO.getSaleOrderId());

                //进入财务系统
                financeConnectService.saleInsertFinance(erpSaleOrderRespVO, buttonType);
            }
        }

        return updateCount;
    }

}