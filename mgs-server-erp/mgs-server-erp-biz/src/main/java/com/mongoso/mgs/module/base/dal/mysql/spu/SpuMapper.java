package com.mongoso.mgs.module.base.dal.mysql.spu;


import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.controller.admin.spu.vo.SpuPageReqVO;
import com.mongoso.mgs.module.base.controller.admin.spu.vo.SpuQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.spu.vo.SpuRespVO;
import com.mongoso.mgs.module.base.dal.db.spu.SpuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SPU物料 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpuMapper extends BaseMapperX<SpuDO> {

    default PageResult<SpuDO> selectPageOld(SpuPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<SpuDO>lambdaQueryX()
                .likeIfPresent(SpuDO::getMaterialName, reqVO.getMaterialName())
                .likeIfPresent(SpuDO::getSpuCode, reqVO.getSpuCode())
                .eqIfPresent(SpuDO::getMaterialCategoryDictId, reqVO.getMaterialCategoryDictId())
                .eqIfPresent(SpuDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(SpuDO::getSpecModel, reqVO.getSpecModel())
                .eqIfPresent(SpuDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(SpuDO::getMaterialTypeDictId, reqVO.getMaterialTypeDictId())
                .eqIfPresent(SpuDO::getMaterialSourceDictId, reqVO.getMaterialSourceDictId())
                .likeIfPresent(SpuDO::getMaterialPictureNo, reqVO.getMaterialPictureNo())
                .eqIfPresent(SpuDO::getSortValue, reqVO.getSortValue())
                .eqIfPresent(SpuDO::getPurchaseStandardPrice, reqVO.getPurchaseStandardPrice())
                .eqIfPresent(SpuDO::getCustomerOrderPrice, reqVO.getCustomerOrderPrice())
                .eqIfPresent(SpuDO::getRetailPrice, reqVO.getRetailPrice())
//                .eqIfPresent(SpuDO::getUnitList, reqVO.getUnitList())
                .eqIfPresent(SpuDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SpuDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
//                .eqIfPresent(SpuDO::getSpecAttributeList, reqVO.getSpecAttributeList())
                .eqIfPresent(SpuDO::getIsAutoCheck, reqVO.getIsAutoCheck())
                .eqIfPresent(SpuDO::getInspectionMethod, reqVO.getInspectionMethod())
                .eqIfPresent(SpuDO::getInspectionDesc, reqVO.getInspectionDesc())
                .eqIfPresent(SpuDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(SpuDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(SpuDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(SpuDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(SpuDO::getSortValue)
                .orderByDesc(SpuDO::getSpuId));
    }



    default PageResult<SpuDO> selectPage(SpuPageReqVO reqVO) {
        LambdaQueryWrapperX<SpuDO> queryWrapperX = new LambdaQueryWrapperX<SpuDO>()
                .likeIfPresent(SpuDO::getMaterialName, reqVO.getMaterialName())
                .likeIfPresent(SpuDO::getSpuCode, reqVO.getSpuCode())
                .eqIfPresent(SpuDO::getMaterialCategoryDictId, reqVO.getMaterialCategoryDictId())
                .eqIfPresent(SpuDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .likeIfPresent(SpuDO::getSpecModel, reqVO.getSpecModel())
                .eqIfPresent(SpuDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(SpuDO::getMaterialTypeDictId, reqVO.getMaterialTypeDictId())
                .eqIfPresent(SpuDO::getMaterialSourceDictId, reqVO.getMaterialSourceDictId())
                .likeIfPresent(SpuDO::getMaterialPictureNo, reqVO.getMaterialPictureNo())
                .eqIfPresent(SpuDO::getPurchaseStandardPrice, reqVO.getPurchaseStandardPrice())
                .eqIfPresent(SpuDO::getCustomerOrderPrice, reqVO.getCustomerOrderPrice())
                .eqIfPresent(SpuDO::getRetailPrice, reqVO.getRetailPrice())
//                .eqIfPresent(SpuDO::getUnitList, reqVO.getUnitList())
                .eqIfPresent(SpuDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SpuDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
//                .eqIfPresent(SpuDO::getSpecAttributeList, reqVO.getSpecAttributeList())
                .eqIfPresent(SpuDO::getIsAutoCheck, reqVO.getIsAutoCheck())
                .eqIfPresent(SpuDO::getInspectionMethod, reqVO.getInspectionMethod())
                .eqIfPresent(SpuDO::getInspectionDesc, reqVO.getInspectionDesc())
                .eqIfPresent(SpuDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(SpuDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(SpuDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(SpuDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(SpuDO::getPublishStatus, reqVO.getPublishStatus())
                .betweenIfPresent(SpuDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                ;
        if (reqVO.getSortValue() == null){
            queryWrapperX.orderByDesc(SpuDO::getSortValue)
                    .orderByDesc(SpuDO::getSpuId);
        }else if (reqVO.getSortValue() == 0){
            queryWrapperX.orderByDesc(SpuDO::getSpuId);
        }

        return jsonbSelectPage(reqVO, queryWrapperX);
    }

    default List<SpuDO> selectListOld(SpuQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<SpuDO>lambdaQueryX()
                .likeIfPresent(SpuDO::getMaterialName, reqVO.getMaterialName())
                .likeIfPresent(SpuDO::getSpuCode, reqVO.getSpuCode())
                .eqIfPresent(SpuDO::getMaterialCategoryDictId, reqVO.getMaterialCategoryDictId())
                .eqIfPresent(SpuDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(SpuDO::getSpecModel, reqVO.getSpecModel())
                .eqIfPresent(SpuDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(SpuDO::getMaterialTypeDictId, reqVO.getMaterialType())
                .eqIfPresent(SpuDO::getMaterialSourceDictId, reqVO.getMaterialSourceDictId())
                .likeIfPresent(SpuDO::getMaterialPictureNo, reqVO.getMaterialPictureNo())
                .eqIfPresent(SpuDO::getSortValue, reqVO.getSortValue())
                .eqIfPresent(SpuDO::getPurchaseStandardPrice, reqVO.getPurchaseStandardPrice())
                .eqIfPresent(SpuDO::getCustomerOrderPrice, reqVO.getCustomerOrderPrice())
                .eqIfPresent(SpuDO::getRetailPrice, reqVO.getRetailPrice())
//                .eqIfPresent(SpuDO::getUnitList, reqVO.getUnitList())
                .eqIfPresent(SpuDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SpuDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
//                .eqIfPresent(SpuDO::getSpecAttributeList, reqVO.getSpecAttributeList())
                .eqIfPresent(SpuDO::getIsAutoCheck, reqVO.getIsAutoCheck())
                .eqIfPresent(SpuDO::getInspectionMethod, reqVO.getInspectionMethod())
                .eqIfPresent(SpuDO::getInspectionDesc, reqVO.getInspectionDesc())
                .eqIfPresent(SpuDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(SpuDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(SpuDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(SpuDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(SpuDO::getSortValue)
                .orderByDesc(SpuDO::getSpuId));
    }

    default List<SpuDO> selectList(SpuQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<SpuDO>lambdaQueryX()
                .likeIfPresent(SpuDO::getMaterialName, reqVO.getMaterialName())
                .likeIfPresent(SpuDO::getSpuCode, reqVO.getSpuCode())
                .inIfPresent(SpuDO::getSpuId, reqVO.getSpuIdList())
                .eqIfPresent(SpuDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(SpuDO::getMaterialCategoryDictId, reqVO.getMaterialCategoryDictId())
                .eqIfPresent(SpuDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(SpuDO::getSpecModel, reqVO.getSpecModel())
                .eqIfPresent(SpuDO::getBrandDictId, reqVO.getBrandDictId())
                .eqIfPresent(SpuDO::getMaterialTypeDictId, reqVO.getMaterialType())
                .eqIfPresent(SpuDO::getMaterialSourceDictId, reqVO.getMaterialSourceDictId())
                .likeIfPresent(SpuDO::getMaterialPictureNo, reqVO.getMaterialPictureNo())
                .eqIfPresent(SpuDO::getSortValue, reqVO.getSortValue())
                .eqIfPresent(SpuDO::getPurchaseStandardPrice, reqVO.getPurchaseStandardPrice())
                .eqIfPresent(SpuDO::getCustomerOrderPrice, reqVO.getCustomerOrderPrice())
                .eqIfPresent(SpuDO::getRetailPrice, reqVO.getRetailPrice())
//                .eqIfPresent(SpuDO::getUnitList, reqVO.getUnitList())
                .eqIfPresent(SpuDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SpuDO::getFormDt, reqVO.getStartDt(), reqVO.getEndDt())
//                .eqIfPresent(SpuDO::getSpecAttributeList, reqVO.getSpecAttributeList())
                .eqIfPresent(SpuDO::getIsAutoCheck, reqVO.getIsAutoCheck())
                .eqIfPresent(SpuDO::getInspectionMethod, reqVO.getInspectionMethod())
                .eqIfPresent(SpuDO::getInspectionDesc, reqVO.getInspectionDesc())
                .eqIfPresent(SpuDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(SpuDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(SpuDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(SpuDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(SpuDO::getSortValue)
                .orderByDesc(SpuDO::getSpuId));
    }

//    default void updateSpuPublishStatus(SpuDO reqVO){
//        LambdaUpdateWrapper<SpuDO> updateWrapper = new LambdaUpdateWrapper<SpuDO>()
//                .in(SpuDO::getSpuId, reqVO.getSpuIdList())
//                .set(SpuDO::getPublishStatus, reqVO.getPublishStatus())
//                .set(SpuDO::getUpdatedBy, reqVO.getUpdatedBy())
//                .set(SpuDO::getUpdatedDt, reqVO.getUpdatedDt());
//        update(updateWrapper);
//    }

    void updateSpuPublishStatus(@Param("reqVO") SpuQueryReqVO reqVO);

    List<SpuRespVO> getSpuPublishStatus(@Param("spuList") List<SpuRespVO> spuList);
}