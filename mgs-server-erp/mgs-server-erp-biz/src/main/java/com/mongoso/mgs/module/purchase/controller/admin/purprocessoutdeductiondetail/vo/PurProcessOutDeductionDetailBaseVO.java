package com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeductiondetail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 
/**
 * 工序委外采购扣费明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PurProcessOutDeductionDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long processOutDeductionDetailId;

    /** 行号 */
    private Integer rowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别 */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 物料类型 */
    private String materialTypeDictName;

    /** 规格型号 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 基本单位字典名称 */
    private String mainUnitDictName;

    /** 工序ID */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 票据类型 */
    private Long invoiceTypeId;
    private String invoiceTypeName;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 单价(含税) */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 扣费数量 */
    private BigDecimal deductionQty;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 工序委外采购扣费单主键ID */
    private Long processOutDeductionId;

    /** 工序委外采购扣费单号 */
    private String processOutDeductionCode;

    /** 工序委外采购订单明细ID */
    private Long processOutDetailId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 是否填报税价 **/
    private Integer includingTax;

    /** 币种字典ID */
    private String currencyDictId;
    private String currencyDictName;

    /** 本币币种字典ID */
    private String localCurrencyDictId;
    private String localCurrencyDictName;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;
}
