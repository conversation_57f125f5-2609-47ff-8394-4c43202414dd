package com.mongoso.mgs.module.produce.dal.db.workpicking;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 工单领料明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_work_picking_detail", autoResultMap = true)
//@KeySequence("erp.u_work_picking_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkPickingDetailDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long workPickingDetailId;

    /** 工单领料主键ID */
    private Long workPickingId;

    /** 领料单号 */
    private String workPickingCode;

    private String showId;// 前端多选和回显用的,后端没用到

    /** 行号 */
    private Integer rowNo;

    /** 物料id */
    private Long materialId;
    private String materialCode;
    private String materialCategoryDictId;
    private BigDecimal demandQty;// 需求数量
    private BigDecimal lossRate;// 损耗率
    private BigDecimal estimatedQty;// 预估用量

    /** 领料数量,前端输入 */
    private BigDecimal pickingQty;

    /** 领料仓库id */
    private String warehouseOrgId;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 已出库数量 */
    private BigDecimal outboundedQty;

    /** 可出库数量 */
    private BigDecimal outboundableQty;

    /** 是否出库完成 */
    private Integer isMaterialFullOutbounded;

    /** 替代物料id和编码 */
    private Long alternativeMaterialId;
    private String alternativeMaterialCode;

    private Integer level;// 物料层级，0是最高级
    private BigDecimal realLossRate;// 真实损耗率

}
