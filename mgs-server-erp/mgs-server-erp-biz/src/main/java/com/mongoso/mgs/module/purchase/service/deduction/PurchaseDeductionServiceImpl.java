package com.mongoso.mgs.module.purchase.service.deduction;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.dal.db.erpsupplier.ERPSupplierDO;
import com.mongoso.mgs.module.base.dal.mysql.erpsupplier.ERPSupplierMapper;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.finance.dal.db.accountorder.AccountOrderDO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.deduction.PurchaseDeductionDO;
import com.mongoso.mgs.module.purchase.dal.db.deduction.PurchaseDeductionDetailDO;
import com.mongoso.mgs.module.purchase.dal.mysql.deduction.PurchaseDeductionDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.deduction.PurchaseDeductionMapper;
import com.mongoso.mgs.module.purchase.handler.approve.PurchaseDeductionApproveHandler;
import com.mongoso.mgs.module.purchase.handler.flowcallback.PurchaseDeductionFlowCallBackHandler;
import com.mongoso.mgs.module.purchase.service.deduction.detail.PurchaseDeductionDetailService;
import com.mongoso.mgs.module.sale.dal.db.invtypemanage.InvTypeManageDO;
import com.mongoso.mgs.module.sale.dal.mysql.invtypemanage.InvTypeManageMapper;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.db.stockunlock.StockUnlockDO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_DELETE_NOT_APPROVED;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.ORDER_EDIT_NOT_APPROVED;
// import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;


/**
 * 采购扣费单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseDeductionServiceImpl implements PurchaseDeductionService {

    @Resource
    private PurchaseDeductionMapper deductionMapper;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private PurchaseDeductionDetailMapper deductionDetailMapper;
    @Resource
    private PurchaseDeductionDetailService deductionDetailService;
    @Resource
    private SeqService seqService;
    @Resource
    private InvTypeManageMapper invTypeManageMapper;
    @Resource
    private ERPSupplierMapper erpSupplierMapper;
    @Resource
    @Lazy
    private PurchaseDeductionApproveHandler deductionApproveHandler;

    @Resource
    private PurchaseDeductionFlowCallBackHandler purchaseDeductionFlowCallBackHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseDeductionAdd(PurchaseDeductionAditReqVO reqVO) {
        //编号
        String code = seqService.getGenerateCode(reqVO.getPurchaseDeductionCode(), MenuEnum.PURCHASE_DEDUCTION_ORDER.menuId);
       // 插入
        PurchaseDeductionDO deduction = BeanUtilX.copy(reqVO, PurchaseDeductionDO::new);
        deduction.setPurchaseDeductionId(IDUtilX.getId());
        deduction.setPurchaseDeductionCode(code);
        deduction.setApprovedBy(null);
        deduction.setApprovedDt(null);
        deduction.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
        List<PurchaseDeductionDetailDO> deductionDetailDOList = this.fillDetailList(reqVO, deduction);
        deductionMapper.insert(deduction);
        //新增明细
        deductionDetailMapper.insertBatch(deductionDetailDOList);
        // 返回
        return deduction.getPurchaseDeductionId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long purchaseDeductionEdit(PurchaseDeductionAditReqVO reqVO) {
        // 校验存在
        PurchaseDeductionDO purchaseDeductionDO = this.purchaseDeductionValidateExists(reqVO.getPurchaseDeductionId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(purchaseDeductionDO, reqVO);

        // 更新
        PurchaseDeductionDO deduction = BeanUtilX.copy(reqVO, PurchaseDeductionDO::new);
        List<PurchaseDeductionDetailDO> deductionDetailDOList = this.fillDetailList(reqVO, purchaseDeductionDO);
        deductionMapper.updateById(deduction);
        //明细更新，先删后增
        deductionDetailMapper.deleteByDeductionId(reqVO.getPurchaseDeductionId());
        deductionDetailMapper.insertBatch(deductionDetailDOList);
        // 返回
        return deduction.getPurchaseDeductionId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseDeductionDel(Long purchaseDeductionId) {
        // 校验存在
        PurchaseDeductionDO purchaseDeductionDO = this.purchaseDeductionValidateExists(purchaseDeductionId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(purchaseDeductionDO.getDataStatus())){
            throw exception(ORDER_DELETE_NOT_APPROVED);
        }
        // 删除
        deductionMapper.deleteById(purchaseDeductionId);
        deductionDetailMapper.deleteByDeductionId(purchaseDeductionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> purchaseDeductionDelBatch(IdReq reqVO) {
        //获取对象属性名
        String purchaseDeductionId = EntityUtilX.getPropertyName(PurchaseDeductionDO::getPurchaseDeductionId);
        String purchaseDeductionCode = EntityUtilX.getPropertyName(PurchaseDeductionDO::getPurchaseDeductionCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), PurchaseDeductionDO.class, PurchaseDeductionDetailDO.class,
                purchaseDeductionId, purchaseDeductionCode);
    }

    @Override
    public BatchResult purchaseDeductionApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<PurchaseDeductionDO> list = deductionMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (PurchaseDeductionDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = deductionApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getPurchaseDeductionCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }
        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (PurchaseDeductionDO item : list) {
                String reason = reasonMap.get(item.getPurchaseDeductionCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPurchaseDeductionId());
                    messageInfoBO.setObjCode(item.getPurchaseDeductionCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getPurchaseDeductionId());
                    messageInfoBO.setObjCode(item.getPurchaseDeductionCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object purchaseDeductionCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        PurchaseDeductionDO item = this.purchaseDeductionValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return purchaseDeductionFlowCallBackHandler.handleFlowCallback(item, flowCallbackBO);
    }

    private PurchaseDeductionDO purchaseDeductionValidateExists(Long purchaseDeductionId) {
        PurchaseDeductionDO deduction = deductionMapper.selectById(purchaseDeductionId);
        if (deduction == null) {
            // throw exception(DEDUCTION_NOT_EXISTS);
            throw new BizException("5001", "采购扣费单不存在");
        }
        return deduction;
    }

    @Override
    public PurchaseDeductionRespVO purchaseDeductionDetail(Long purchaseDeductionId) {
        PurchaseDeductionDO data = this.purchaseDeductionValidateExists(purchaseDeductionId);
        PurchaseDeductionRespVO deductionRespVO = BeanUtilX.copy(data, PurchaseDeductionRespVO::new);
        this.fillVoProperties(deductionRespVO);
        //查询明细列表
        PurchaseDeductionDetailQueryReqVO detailQueryReqVO = new PurchaseDeductionDetailQueryReqVO();
        detailQueryReqVO.setPurchaseDeductionId(purchaseDeductionId);
        List<PurchaseDeductionDetailRespVO> detailList = deductionDetailService.purchaseDeductionDetailList(detailQueryReqVO);
        deductionRespVO.setDetailList(detailList);
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(purchaseDeductionId.toString())).ifPresent(approveTask -> deductionRespVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return deductionRespVO;
    }

    @Override
    public List<PurchaseDeductionRespVO> purchaseDeductionList(PurchaseDeductionQueryReqVO reqVO) {
        List<PurchaseDeductionDO> data = deductionMapper.selectList(reqVO);
        return BeanUtilX.copy(data, PurchaseDeductionRespVO::new);
    }

    @Override
    public PageResult<PurchaseDeductionRespVO> purchaseDeductionPage(PurchaseDeductionPageReqVO reqVO) {
        PageResult<PurchaseDeductionDO> data = deductionMapper.selectPage(reqVO);
        PageResult<PurchaseDeductionRespVO> pageResult = BeanUtilX.copy(data, PurchaseDeductionRespVO::new);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    private List<PurchaseDeductionDetailDO> fillDetailList(PurchaseDeductionAditReqVO reqVO, PurchaseDeductionDO deductionDO) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        BigDecimal exclTotalAmt = BigDecimal.ZERO;
        List<PurchaseDeductionDetailDO> purchaseDeductionDetailList = new ArrayList<>();
        List<PurchaseDeductionDetailAditReqVO> detailList = reqVO.getDetailList();


        // 校验相同物料编码的可操作数量合并
        Map<String, BigDecimal> materialCodeReturnQtyMap = new HashMap<>();
        Map<String, BigDecimal> materialCodeOperableQtyMap = new HashMap<>();

        // 第一步：收集每个物料编码的退货数量和可操作数量
        for (PurchaseDeductionDetailAditReqVO detail : detailList) {
            String materialCode = detail.getMaterialCode();
            BigDecimal deductionQty = detail.getDeductionQty();
            BigDecimal operableQty = detail.getCompleteableQty() != null ? detail.getCompleteableQty() : deductionQty;

            // 累加相同物料编码的退货数量
            materialCodeReturnQtyMap.merge(materialCode, deductionQty, BigDecimal::add);

            // 记录相同物料编码的可操作数量（取第一次遇到的值）
            materialCodeOperableQtyMap.putIfAbsent(materialCode, operableQty);
        }

        // 第二步：校验每个物料编码的退货数量是否超过可操作数量
        for (Map.Entry<String, BigDecimal> entry : materialCodeReturnQtyMap.entrySet()) {
            String materialCode = entry.getKey();
            BigDecimal totalReturnQty = entry.getValue();
            BigDecimal operableQty = materialCodeOperableQtyMap.get(materialCode);

            if (operableQty != null && totalReturnQty.compareTo(operableQty) > 0) {
                throw new BizException("5002", "明细存在物料扣费数量超过可扣费数量，请重新编辑！");
            }
        }


        for (PurchaseDeductionDetailAditReqVO detail : detailList){
            BigDecimal unitPrice = BigDecimal.ZERO;
            BigDecimal amt = BigDecimal.ZERO;
            BigDecimal inclTaxPrice = BigDecimal.ZERO;
            BigDecimal inclTaxAmt = BigDecimal.ZERO;

            //以含税价价算未税单价,未税金额
            if (detail.getIncludingTax() != null && detail.getIncludingTax() == 1){
                //单价不含税
                unitPrice = MathUtilX.getUnitPrice(detail.getInclTaxUnitPrice(),detail.getTaxRate(),detail.getCalculatType());
                //不含税金额
                amt = MathUtilX.getAmt(unitPrice, detail.getDeductionQty());
                //含税单价
                inclTaxPrice = MathUtilX.stripTrailingZeros(detail.getInclTaxUnitPrice());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getDeductionQty());
            }else {
                //单价不含税
                unitPrice = MathUtilX.stripTrailingZeros(detail.getExclTaxUnitPrice());
                //不含税金额
                amt = MathUtilX.getAmt(detail.getExclTaxUnitPrice(), detail.getDeductionQty());
                //含税单价
                inclTaxPrice = MathUtilX.getInclTaxPrice(detail.getExclTaxUnitPrice(), detail.getTaxRate(),detail.getCalculatType());
                //含税金额
                inclTaxAmt = MathUtilX.getAmt(inclTaxPrice, detail.getDeductionQty());
            }

            detail.setExclTaxUnitPrice(unitPrice);
            detail.setExclTaxAmt(amt);
            detail.setInclTaxUnitPrice(inclTaxPrice);
            detail.setInclTaxAmt(inclTaxAmt);
            detail.setPurchaseDeductionId(deductionDO.getPurchaseDeductionId());
            detail.setPurchaseDeductionCode(deductionDO.getPurchaseDeductionCode());

            //订单含税总金额
            totalAmt = totalAmt.add(inclTaxAmt);
            exclTotalAmt = exclTotalAmt.add(amt);

            PurchaseDeductionDetailDO purchaseDeductionDetailDO = BeanUtilX.copy(detail, PurchaseDeductionDetailDO::new);
            purchaseDeductionDetailList.add(purchaseDeductionDetailDO);
        }

        //重算含税金额
        deductionDO.setInclTaxTotalAmt(totalAmt);
        deductionDO.setExclTaxTotalAmt(exclTotalAmt);
        return purchaseDeductionDetailList;
    }

    private void fillVoProperties(PurchaseDeductionRespVO respVO) {
        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.CURRENCY.getDictCode(),
                CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //票据类型
        InvTypeManageDO invTypeManageDO = invTypeManageMapper.selectById(respVO.getInvoiceTypeId());
        if (ObjUtilX.isNotEmpty(invTypeManageDO)){
            respVO.setInvoiceTypeName(invTypeManageDO.getInvoiceName());
        }
        //字典库属性填充
        respVO.setPurchaseTypeDictName(dictMap.get(respVO.getPurchaseTypeDictId()));
        respVO.setCurrencyDictName(dictMap.get(respVO.getCurrencyDictId()));
        respVO.setSettlementMethodDictName(dictMap.get(respVO.getSettlementMethodDictId()));
        respVO.setRefundConditionDictName(dictMap.get(respVO.getRefundConditionDictId()));

        // 采购单类型
        String purchaseTypeDictId = respVO.getPurchaseTypeDictId();
        if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
            purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
            respVO.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
        }

        // 币种
        String currencyDictId = respVO.getCurrencyDictId();
        if(StrUtilX.isNotEmpty(currencyDictId)){
            currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
            respVO.setCurrencyDictName(dictMap.get(currencyDictId));
        }

        // 本币币种
        String localCurrencyDictId = respVO.getLocalCurrencyDictId();
        if(StrUtilX.isNotEmpty(localCurrencyDictId)){
            localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
            respVO.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
        }

        // 结算方式
        String settlementMethodDictId = respVO.getSettlementMethodDictId();
        if(StrUtilX.isNotEmpty(settlementMethodDictId)){
            settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
            respVO.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
        }

        // 退款条件
        String refundConditionDictId = respVO.getRefundConditionDictId();
        if(StrUtilX.isNotEmpty(refundConditionDictId)){
            refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
            respVO.setRefundConditionDictName(dictMap.get(refundConditionDictId));
        }

        //查询负责人
        String directorName = erpBaseService.getEmpNameById(respVO.getDirectorId());
        respVO.setDirectorName(directorName);
        //查询责任部门
        String directorOrgName = erpBaseService.getOrgNameById(respVO.getDirectorOrgId().toString());
        respVO.setDirectorOrgName(directorOrgName);
        //查询供应商名称
        ERPSupplierDO erpSupplierDO = erpSupplierMapper.selectById(respVO.getRelatedSupplierId());
        if(erpSupplierDO != null){
            respVO.setRelatedSupplierName(erpSupplierDO.getSupplierName());
        }
    }

    private void batchFillVoProperties(List<PurchaseDeductionRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)){
            return;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        List<Long> supplierIdList = new ArrayList<>();
        for(PurchaseDeductionRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            orgIdList.add(item.getDirectorOrgId());
            orgIdList.add(item.getCompanyOrgId());
            supplierIdList.add(item.getRelatedSupplierId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.PURCHASE_TYPE.getDictCode(), SystemDictEnum.CURRENCY.getDictCode(),
                CustomerDictEnum.SETTLEMENT_METHOD.getDictCode(), CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        for (PurchaseDeductionRespVO demandResp: respVOList){
            //供应商
            demandResp.setRelatedSupplierName(supplierNameMap.get(demandResp.getRelatedSupplierId()));
            //公司主体
            demandResp.setCompanyOrgName(directorOrgMap.get(demandResp.getCompanyOrgId()));
            //责任部门
            demandResp.setDirectorOrgName(directorOrgMap.get(demandResp.getDirectorOrgId()));
            //责任人
            demandResp.setDirectorName(directorMap.get(demandResp.getDirectorId()));

            // 采购单类型
            String purchaseTypeDictId = demandResp.getPurchaseTypeDictId();
            if(StrUtilX.isNotEmpty(purchaseTypeDictId)){
                purchaseTypeDictId = CustomerDictEnum.PURCHASE_TYPE.getDictCode() + "-" + purchaseTypeDictId;
                demandResp.setPurchaseTypeDictName(dictMap.get(purchaseTypeDictId));
            }

            // 币种
            String currencyDictId = demandResp.getCurrencyDictId();
            if(StrUtilX.isNotEmpty(currencyDictId)){
                currencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + currencyDictId;
                demandResp.setCurrencyDictName(dictMap.get(currencyDictId));
            }

            // 本币币种
            String localCurrencyDictId = demandResp.getLocalCurrencyDictId();
            if(StrUtilX.isNotEmpty(localCurrencyDictId)){
                localCurrencyDictId = SystemDictEnum.CURRENCY.getDictCode() + "-" + localCurrencyDictId;
                demandResp.setLocalCurrencyDictName(dictMap.get(localCurrencyDictId));
            }

            // 结算方式
            String settlementMethodDictId = demandResp.getSettlementMethodDictId();
            if(StrUtilX.isNotEmpty(settlementMethodDictId)){
                settlementMethodDictId = CustomerDictEnum.SETTLEMENT_METHOD.getDictCode() + "-" + settlementMethodDictId;
                demandResp.setSettlementMethodDictName(dictMap.get(settlementMethodDictId));
            }

            // 退款条件
            String refundConditionDictId = demandResp.getRefundConditionDictId();
            if(StrUtilX.isNotEmpty(refundConditionDictId)){
                refundConditionDictId = CustomerDictEnum.RETURN_ORDER_CONDITION.getDictCode() + "-" + refundConditionDictId;
                demandResp.setRefundConditionDictName(dictMap.get(refundConditionDictId));
            }

            // 审核状态
            if(demandResp.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + demandResp.getDataStatus();
                demandResp.setDataStatusDictName(dictMap.get(dataStatus));
            }
        }
    }
}
