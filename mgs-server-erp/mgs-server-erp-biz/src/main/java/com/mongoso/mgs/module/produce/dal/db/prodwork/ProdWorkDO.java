package com.mongoso.mgs.module.produce.dal.db.prodwork;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 生产工单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_prod_work", autoResultMap = true)
//@KeySequence("erp.u_prod_work_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProdWorkDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long prodWorkId;

    /** 生产工单号 */
    private String prodWorkCode;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 生产订单号 */
    private String prodOrderCode;

    /** 生产订单id */
    private Long prodOrderId;

    /** 行号 */
    private Integer rowNo;

    /** 物料id */
    private Long materialId;

    /** 计划生产数量 */
    private BigDecimal workPlanTotalQty;

    /** 实际生产数量 */
    private BigDecimal workActTotalQty;

    /** 已入库数量 */
    private BigDecimal inboundedQty;

    /** 可入库数量 */
    private BigDecimal inboundableQty;

    /** 是否全部入库 */
    private Integer isFullInbounded;

    /** 是否全部出库 */
    private Integer isFullOutbounded;

    /** 优先级 */
    private Integer priority;

    /** 计划开始日期 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate planStartDate;

    /** 计划完成日期 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate planEndDate;

    /** 实际完成日期 */
    private LocalDate actEndDate;

    /** 是否入库 */
    private Integer isInbound;

    /** 是否自动入库 */
    private Integer isAutoInbound;

    /** 审核状态 */
    private Integer dataStatus;

    /** 单据状态 */
    private Integer formStatus;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    /** 物料编码 */
    private String materialCode;

    //原因
    private String reason;

    /** 生产流程配置['工单工序','工序'] */
    private Integer processConfig;

    /** 是否全部退料，退料单专用 */
    private Integer isFullPickingReturned;

    /** 版本号 */
//    private Integer version;

    /** 不良品数量 */
    private BigDecimal ngQty;

    /** 出库流程配置 */
    private Integer outProcessConfigDictId;

    /** 入库流程配置 */
    private Integer inProcessConfigDictId;

    /** 是否存在可入库数量 */
    private Integer isExistInboundableQty;

    /** 派工策略["手动派工","自动派工"] */
    private Integer dispatchStrategyConfig;

    /**
     * 下发工单领料单
     */
    private Short isIssueWorkPicking;

    /**
     * 下发工单直接领料出库单
     */
    private Short isIssueOutbound;
}
