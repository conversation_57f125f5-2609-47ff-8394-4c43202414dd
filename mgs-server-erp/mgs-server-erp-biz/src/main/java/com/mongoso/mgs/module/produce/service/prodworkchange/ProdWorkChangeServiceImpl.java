package com.mongoso.mgs.module.produce.service.prodworkchange;

import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.employee.service.personal.bo.UserBaseRespBO;
import com.mongoso.mgs.module.produce.controller.admin.flowprocessdetail.vo.FlowProcessDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodworkchangematerialbom.vo.ProdWorkChangeMaterialBomQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodworkchangematerialbom.vo.ProdWorkChangeMaterialBomRespVO;
import com.mongoso.mgs.module.produce.dal.db.flowprocessdetail.FlowProcessDetailDO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.prodworkchangematerialbom.ProdWorkChangeMaterialBomDO;
import com.mongoso.mgs.module.produce.dal.mysql.dispatchwork.DispatchWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.flowprocessdetail.FlowProcessDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.processoutdemand.ProcessOutDemandMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodworkchangematerialbom.ProdWorkChangeMaterialBomMapper;
import com.mongoso.mgs.module.produce.dal.mysql.reportedwork.ReportedWorkMapper;
import com.mongoso.mgs.module.produce.handler.approve.ProdWorkChangeApproveHandler;
import com.mongoso.mgs.module.produce.handler.flowCallback.ProdWorkChangeFlowCallBackHandler;
import com.mongoso.mgs.module.produce.service.prodwork.ProdWorkService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import com.mongoso.mgs.module.produce.controller.admin.prodworkchange.vo.*;
import com.mongoso.mgs.module.produce.dal.db.prodworkchange.ProdWorkChangeDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.prodworkchange.ProdWorkChangeMapper;
import com.mongoso.mgs.framework.common.exception.BizException;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;


/**
 * 生产工单变更单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProdWorkChangeServiceImpl implements ProdWorkChangeService {

    @Resource
    private ProdWorkChangeMapper prodWorkChangeMapper;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    @Lazy
    private ProdWorkChangeApproveHandler prodWorkChangeApproveHandler;

    @Resource
    private ProdWorkChangeFlowCallBackHandler prodWorkChangeFlowCallBackHandler;

    @Resource
    private SeqService seqService;

    @Resource
    private ProdWorkChangeMaterialBomMapper prodWorkChangeMaterialBomMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private DispatchWorkMapper dispatchWorkMapper;

    @Resource
    private ProcessOutDemandMapper processOutDemandMapper;

    @Resource
    private FlowProcessDetailMapper flowProcessDetailMapper;

    @Resource
    private ReportedWorkMapper reportedWorkMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long prodWorkChangeAdd(ProdWorkChangeAditReqVO reqVO) {

        if (reqVO.getWorkPlanTotalQty() == null){
            throw new BizException("5001", "计划生产数量不允许为空!");
        }

        // 生成单号
        String code = reqVO.getProdWorkChangeCode();
        if(reqVO.getIsOld() == null || reqVO.getIsOld() == 0){
            code = seqService.getGenerateCode(reqVO.getProdWorkChangeCode());
        }

        // 插入
        Long prodWorkChangeId = IDUtilX.getId();
        reqVO.setProdWorkChangeId(prodWorkChangeId);
        reqVO.setProdWorkChangeCode(code);
        ProdWorkChangeDO prodWorkChange = BeanUtilX.copy(reqVO, ProdWorkChangeDO::new);
        prodWorkChangeMapper.insert(prodWorkChange);

        //新增物料bom清单
        addWorkMaterialBomList(reqVO);

        // 返回
        return prodWorkChange.getProdWorkChangeId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long prodWorkChangeEdit(ProdWorkChangeAditReqVO reqVO) {
        if (reqVO.getWorkPlanTotalQty() == null){
            throw new BizException("5001", "计划生产数量不允许为空!");
        }

        // 校验存在
        ProdWorkChangeDO prodWorkChangeDO = this.prodWorkChangeValidateExists(reqVO.getProdWorkChangeId());
        if(!prodWorkChangeDO.getDataStatus().equals(DataStatusEnum.NOT_APPROVE.getKey()) ){
            throw exceptionMsg("单据状态不是未审核,不可进行编辑!");
        }

        // 更新
        ProdWorkChangeDO prodWorkChange = BeanUtilX.copy(reqVO, ProdWorkChangeDO::new);
        prodWorkChangeMapper.updateById(prodWorkChange);

        //新增物料bom清单
        prodWorkChangeMaterialBomMapper.batchDelete(reqVO.getProdWorkChangeId());
        addWorkMaterialBomList(reqVO);

        // 返回
        return prodWorkChange.getProdWorkChangeId();
    }

    /**
     * 新增工单bom
     *
     * @param reqVO
     */
    private void addWorkMaterialBomList(ProdWorkChangeAditReqVO reqVO) {
        List<ProdWorkChangeMaterialBomRespVO> reqVOBomDetailList = reqVO.getBomDetailList();

        List<ProdWorkChangeMaterialBomDO> workMaterialBomList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(reqVOBomDetailList)){
            for (ProdWorkChangeMaterialBomRespVO bomRespVO : reqVOBomDetailList){
                bomRespVO.setMaterialBomId(null);
                bomRespVO.setParentId(reqVO.getProdWorkChangeId());
                bomRespVO.setProdWorkChangeCode(reqVO.getProdWorkChangeCode());
                ProdWorkChangeMaterialBomDO workMaterialBomDO = BeanUtilX.copy(bomRespVO, ProdWorkChangeMaterialBomDO::new);
                workMaterialBomList.add(workMaterialBomDO);
            }
            prodWorkChangeMaterialBomMapper.insertBatch(workMaterialBomList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void prodWorkChangeDelete(Long prodWorkChangeId) {
        // 校验存在
        ProdWorkChangeDO prodWorkChangeDO = this.prodWorkChangeValidateExists(prodWorkChangeId);
        if(prodWorkChangeDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        prodWorkChangeMapper.deleteById(prodWorkChangeId);
        prodWorkChangeMaterialBomMapper.batchDelete(prodWorkChangeId);
    }

    private ProdWorkChangeDO prodWorkChangeValidateExists(Long prodWorkChangeId) {
        ProdWorkChangeDO prodWorkChange = prodWorkChangeMapper.selectById(prodWorkChangeId);
        if (prodWorkChange == null) {
            // throw exception(PROD_WORK_CHANGE_NOT_EXISTS);
            throw new BizException("5001", "生产工单变更单不存在");
        }
        return prodWorkChange;
    }

    @Override
    public ProdWorkChangeRespVO prodWorkChangeDetail(Long prodWorkChangeId) {
        ProdWorkChangeDO data = prodWorkChangeValidateExists(prodWorkChangeId);
        ProdWorkChangeRespVO respVO = BeanUtilX.copy(data, ProdWorkChangeRespVO::new);

        //VO属性填充
        fillVoProperties(Collections.singletonList(respVO));

        //查询物料bom清单
        ProdWorkChangeMaterialBomQueryReqVO prodWorkMaterialBomQuery = new ProdWorkChangeMaterialBomQueryReqVO();
        prodWorkMaterialBomQuery.setParentId(prodWorkChangeId);
        List<ProdWorkChangeMaterialBomDO> workMaterialBomRespList = prodWorkChangeMaterialBomMapper.selectList(prodWorkMaterialBomQuery);
        List<ProdWorkChangeMaterialBomRespVO> bomDetailList = BeanUtilX.copy(workMaterialBomRespList, ProdWorkChangeMaterialBomRespVO::new);
        if (CollUtilX.isNotEmpty(bomDetailList)){
            List<Long> materialIdList = new ArrayList<>();
            List<String> orgIdList = new ArrayList<>();

            for (ProdWorkChangeMaterialBomRespVO bomRespVO : bomDetailList){
                materialIdList.add(bomRespVO.getMaterialId());

                if (bomRespVO.getWarehouseOrgId()!=null){
                    orgIdList.add(bomRespVO.getWarehouseOrgId());
                }
            }

            if (CollUtilX.isNotEmpty(materialIdList)){
                ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
                erpMaterialQuery.setMaterialIdList(materialIdList);
                Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

                //查询仓库
                Map<String, String> orgMap = new HashMap<>();
                if (CollUtilX.isNotEmpty(orgIdList)){
                    orgMap = erpBaseService.getOrgNameByIds(orgIdList);
                }

                // 查询字典
                Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.PROD_PICKING_METHOD.getDictCode());

                //填充物料基本信息
                for (ProdWorkChangeMaterialBomRespVO item : bomDetailList){
                    ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(item.getMaterialId());
                    if (erpMaterialDO !=null){
                        item.setMaterialCode(erpMaterialDO.getMaterialCode());
                        item.setMaterialName(erpMaterialDO.getMaterialName());
                        item.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                        item.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                        item.setSpecModel(erpMaterialDO.getSpecModel());
                        item.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
                        item.setMainUnitDictId(erpMaterialDO.getMainUnitDictId());
                        item.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                        item.setMaterialSourceDictId(erpMaterialDO.getMaterialSourceDictId());
                        item.setMaterialSourceDictName(erpMaterialDO.getMaterialSourceDictName());
                    }

                    //领料方式
                    item.setPickingMethodDictId(item.getPickingMethodDictId());
                    item.setPickingMethodDictName(dictMap.get(item.getPickingMethodDictId().toString()));

                    //发料仓库
                    if (item.getWarehouseOrgId()!=null){
                        item.setWarehouseOrgId(item.getWarehouseOrgId());
                        item.setWarehouseOrgName(orgMap.get(item.getWarehouseOrgId()));
                    }
                }
            }
        }

        //填充物料bom清单
        respVO.setBomDetailList(bomDetailList);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(prodWorkChangeId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<ProdWorkChangeRespVO> prodWorkChangeList(ProdWorkChangeQueryReqVO reqVO) {
        List<ProdWorkChangeDO> data = prodWorkChangeMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProdWorkChangeRespVO::new);
    }

    @Override
    public PageResult<ProdWorkChangeRespVO> prodWorkChangePage(ProdWorkChangePageReqVO reqVO) {
        //物料编码名称查询
        if (StrUtilX.isNotEmpty(reqVO.getMaterialName())) {
            ERPMaterialQueryReqVO queryReq = new ERPMaterialQueryReqVO();
            queryReq.setMaterialName(reqVO.getMaterialName());
            List<Long> materialIdList = erpMaterialService.findMaterialIdList(queryReq);
            if (CollUtilX.isEmpty(materialIdList)){
                return PageResult.empty();
            }

            reqVO.setMaterialIdList(materialIdList);
        }

        PageResult<ProdWorkChangeDO> data = prodWorkChangeMapper.selectPage(reqVO);
        PageResult<ProdWorkChangeRespVO> pageResult = BeanUtilX.copy(data, ProdWorkChangeRespVO::new);
        if (CollUtilX.isNotEmpty(pageResult.getList())){
            //VO属性填充
            fillVoProperties(pageResult.getList());
        }

        return pageResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultX<BatchResult> prodWorkChangeDelBatch(IdReq reqVO) {
        // 删除
        String id = EntityUtilX.getPropertyName(ProdWorkChangeDO::getProdWorkChangeId);
        String code = EntityUtilX.getPropertyName(ProdWorkChangeDO::getProdWorkChangeCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), ProdWorkChangeDO.class, null, id, code);
    }

    @Override
    public BatchResult prodWorkChangeApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ProdWorkChangeDO> list = prodWorkChangeMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ProdWorkChangeDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = prodWorkChangeApproveHandler.process(item,flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getProdOrderCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ProdWorkChangeDO item : list) {
                String reason = reasonMap.get(item.getProdWorkChangeCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProdWorkChangeId());
                    messageInfoBO.setObjCode(item.getProdWorkChangeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getProdWorkChangeId());
                    messageInfoBO.setObjCode(item.getProdWorkChangeCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object prodWorkChangeFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        ProdWorkChangeDO item = this.prodWorkChangeValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return prodWorkChangeFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public BigDecimal checkProdWorkQty(ProdWorkChangeQueryReqVO reqVO) {
        //查询生产工单
        ProdWorkDO prodWorkDO = prodWorkMapper.selectById(reqVO.getProdWorkId());
        if (prodWorkDO == null){
            throw new BizException("5001", "生产工单不存在");
        }

        Integer dispatchStrategyConfig = prodWorkDO.getDispatchStrategyConfig();
        if (dispatchStrategyConfig == null){
            throw new BizException("5001", "生产工单派工策略不允许为空!");
        }

        Long prodWorkId = prodWorkDO.getProdWorkId();

        //查询生产工单工艺路线
        FlowProcessDetailQueryReqVO flowProcessDetail = new FlowProcessDetailQueryReqVO();
        flowProcessDetail.setFlowProcessId(prodWorkId);
        flowProcessDetail.setFlowProcessType(1);
        List<FlowProcessDetailDO> flowProcessDetailDOList = flowProcessDetailMapper.selectListOld(flowProcessDetail);
        if (CollUtilX.isEmpty(flowProcessDetailDOList)) {
            throw new BizException("500", "生产工单工艺路线不存在!");
        }

        //卡控数量集合
        List<BigDecimal> maxQtyList = new ArrayList<>();

        for (FlowProcessDetailDO item : flowProcessDetailDOList) {

            //手动派工 数量 = 手动派工数量
            if (dispatchStrategyConfig == 0){
                BigDecimal dispatchQty = dispatchWorkMapper.selectDispatchQty(prodWorkId,dispatchStrategyConfig,item.getProcessId());
                maxQtyList.add(dispatchQty);
            }

            //自动派工 数量 = 手动派工数量  + 报工数量
            if (dispatchStrategyConfig == 1){
                BigDecimal dispatchQty = dispatchWorkMapper.selectDispatchQty(prodWorkId,0,item.getProcessId());
                BigDecimal reportedQty = reportedWorkMapper.selectReportedQty(prodWorkId,dispatchStrategyConfig,item.getProcessId());

                maxQtyList.add(dispatchQty.add(reportedQty));
            }
        }

        //最大数量
        BigDecimal maxQty = maxQtyList.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

        return maxQty;
    }

    @Override
    public ProdWorkChangeAfterRespVO prodWorkChangeAfterDetail(Long prodWorkChangeId) {
        ProdWorkChangeAfterRespVO respVO = new ProdWorkChangeAfterRespVO();
        //生产工单变更后详情
        ProdWorkChangeRespVO prodWorkChangeAfter = prodWorkChangeDetail(prodWorkChangeId);
        respVO.setProdWorkChangeAfter(prodWorkChangeAfter);

        //生产工单变更前详情
        ProdWorkChangeQueryReqVO changeQuery = new ProdWorkChangeQueryReqVO();
        changeQuery.setProdWorkId(prodWorkChangeAfter.getProdWorkId());
        changeQuery.setIsOld(1);
        ProdWorkChangeDO changeBefore = prodWorkChangeMapper.selecOne(changeQuery);
        if (changeBefore != null){
            ProdWorkChangeRespVO prodWorkChangeBefore = prodWorkChangeDetail(changeBefore.getProdWorkChangeId());
            respVO.setProdWorkChangeBefore(prodWorkChangeBefore);
        }

        return respVO;
    }

    /**
     * VO属性填充
     *
     * @param itemList
     */
    private void fillVoProperties(List<ProdWorkChangeRespVO> itemList) {
        if (CollUtilX.isEmpty(itemList)){
            return;
        }

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();
        List<Long> materialIdList = new ArrayList<>();
        for (ProdWorkChangeRespVO item : itemList){
            empIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());
            materialIdList.add(item.getMaterialId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.PROD_WORK_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        //查询员工信息
        Map<Long, UserBaseRespBO> empNameMap = erpBaseService.getEmpByIdList(empIdList);

        //查询物料信息
        ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
        erpMaterialQuery.setMaterialIdList(materialIdList);
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);

        for (ProdWorkChangeRespVO item : itemList){

            //工单状态
            if(item.getFormStatus() != null){
                String formStatus = SystemDictEnum.PROD_WORK_STATUS.getDictCode() + "-" + item.getFormStatus();
                item.setFormStatusDictName(dictMap.get(formStatus));
            }

            //审核状态
            if(item.getDataStatus() != null){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //查询负责人
            UserBaseRespBO dirEmployee = empNameMap.get(item.getDirectorId());
            if (dirEmployee!=null){
                item.setDirectorName(dirEmployee.getEmployeeName());
            }

            //查询责任部门
            item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(item.getMaterialId());
            if (erpMaterialDO!=null){
                item.setMaterialName(erpMaterialDO.getMaterialName());
                item.setSpecModel(erpMaterialDO.getSpecModel());
                item.setMaterialCategoryDictId(erpMaterialDO.getMaterialCategoryDictId());
                item.setMaterialCategoryDictName(erpMaterialDO.getMaterialCategoryDictName());
                item.setMainUnitDictId(erpMaterialDO.getMainUnitDictId());
                item.setMainUnitDictName(erpMaterialDO.getMainUnitDictName());
                item.setSpecAttributeStr(erpMaterialDO.getSpecAttributeStr());
            }
        }

    }

}
