package com.mongoso.mgs.module.warehouse.controller.admin.materialassembly.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 组装拆卸单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class MaterialAssemblyQueryReqVO extends CommonParam{

    /** 组装拆卸单ID */
    private Long assemblyId;

    /** 组装拆卸单号 */
    private String assemblyCode;

    /** 组装拆卸单名称 */
    private String assemblyName;

    /** 组装拆卸单类型 */
    private String assemblyTypeDictId;

    /** 关联单ID */
    private Long relatedOrderId;

    /** 关联单号 */
    private String relatedOrderCode;

    /** 组装物料ID */
    private Long assemblyMaterialId;

    /** 组装物料编码 */
    private String assemblyMaterialCode;

    /** 组装物料基本单位ID */
    private String assemblyMainUnitDictId;

    /** 组装拆卸仓库ID */
    private String assemblyWarehouseOrgId;

    /** 业务类型 */
    private Short bizType;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

}
