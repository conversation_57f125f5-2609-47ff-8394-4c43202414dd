package com.mongoso.mgs.module.warehouse.service.erpinbound;

import com.mongoso.mgs.common.constants.RedisConstants;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.reidslock.RedisLockManager;
import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.common.vo.QuoteReqVO;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.WorkPickingReturnQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingMaterialTotalDO;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMaterialTotalMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd.WorkPickingReturnDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd.WorkPickingReturnMapper;
import com.mongoso.mgs.module.produce.service.prodwork.ProdWorkService;
import com.mongoso.mgs.module.produce.service.workpickingreturn.WorkPickingReturnService;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderQueryReqVO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.detail.PurchaseOrderDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchaseexchange.PurchaseExchangeDetailMapper;
import com.mongoso.mgs.module.purchase.service.purchase.PurchaseOrderService;
import com.mongoso.mgs.module.purchase.service.purchaseexchange.PurchaseExchangeService;
import com.mongoso.mgs.module.sale.controller.admin.saleexchange.vo.SaleExchangeQueryReqVO;
import com.mongoso.mgs.module.sale.controller.admin.salereturn.vo.SaleReturnQueryReqVO;
import com.mongoso.mgs.module.sale.dal.mysql.saleexchange.SaleExchangeMapper;
import com.mongoso.mgs.module.sale.dal.mysql.saleexchangedetail.SaleExchangeDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.salereturn.SaleReturnMapper;
import com.mongoso.mgs.module.sale.dal.mysql.salereturndetail.SaleReturnDetailMapper;
import com.mongoso.mgs.module.sale.service.saleexchange.SaleExchangeService;
import com.mongoso.mgs.module.sale.service.salereturn.SaleReturnService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.*;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.ErpReceiptQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDetailDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptMapper;
import com.mongoso.mgs.module.warehouse.enums.ErpInboundBizTypeEnum;
import com.mongoso.mgs.module.warehouse.handler.approve.ErpInboundApproveHandler;
import com.mongoso.mgs.module.warehouse.handler.flowCallback.ErpInboundFlowCallBackHandler;
import com.mongoso.mgs.module.warehouse.service.erpreceipt.ErpReceiptService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.warehouse.enums.ErrorCodeConstants.*;


/**
 * 入库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpInboundServiceImpl implements ErpInboundService {

    @Resource
    private ErpInboundMapper erpInboundMapper;

    @Resource
    private ErpInboundDetailMapper erpInboundDetailMapper;

    @Resource
    private ErpReceiptDetailMapper erpReceiptDetailMapper;

    @Resource
    private SaleReturnDetailMapper saleReturnDetailMapper;

    @Resource
    private ErpInboundDetailService erpInboundDetailService;

    @Resource
    private ErpReceiptService erpReceiptService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private SaleReturnMapper saleReturnMapper;

    @Lazy
    @Resource
    private SaleReturnService saleReturnService;

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private WorkPickingReturnDetailMapper pickingReturnDetailMapper;

    @Resource
    private ProdWorkService prodWorkService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    @Lazy
    private ErpInboundApproveHandler erpInboundApproveHandler;

    @Resource
    private ErpInboundFlowCallBackHandler erpInboundFlowCallBackHandler;

    @Resource
    private WorkPickingReturnMapper workPickingReturnMapper;

    @Resource
    private WorkPickingReturnService workPickingReturnService;

    @Resource
    private WorkPickingReturnDetailMapper workPickingReturnDetailMapper;

    @Resource
    private ErpReceiptMapper erpReceiptMapper;

    @Resource
    private SaleExchangeMapper saleExchangeMapper;

    @Resource
    private SaleExchangeDetailMapper saleExchangeDetailMapper;

    @Resource
    private PurchaseExchangeDetailMapper purchaseExchangeDetailMapper;

    @Resource
    private SaleExchangeService saleExchangeService;

    @Resource
    private PurchaseExchangeService purchaseExchangeService;

    @Resource
    private WorkPickingMaterialTotalMapper workPickingMaterialTotalMapper;

    @Resource
    private RedisLockManager redisLockManager;

    @Override
    public Long erpInboundAdd(ErpInboundAditReqVO reqVO) {

        //入库单信息验证
        this.erpInboundValidate(reqVO);

        // 生成入库单号
        if (reqVO.getBizType() == 0) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 1) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.RECEIVING_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 2) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.PURCHASE_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 3) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.SALES_RETURN_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 4) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.PRODUCTION_WORK_ORDER_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 5) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.WORK_ORDER_RETURN_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 6) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.OUTSOURCING_ORDER_RETURN_INBOUND.menuId));
        }else if (reqVO.getBizType() == 7) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.SALE_EXCHANGE_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 8) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.PURCHASE_EXCHANGE_INBOUND_ORDER.menuId));
        }else if (reqVO.getBizType() == 9) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.WORK_ORDER_DIRECT_RETURN_INBOUND.menuId));
        }else if (reqVO.getBizType() == 10) {
            reqVO.setInboundCode(seqService.getGenerateCode(reqVO.getInboundCode(), MenuEnum.OUTSOURCING_DIRECT_RETURN_INBOUND.menuId));

        }

        // 插入
        ErpInboundDO erpInbound = BeanUtilX.copy(reqVO, ErpInboundDO::new);

        if (reqVO.getBizType() == ErpInboundBizTypeEnum.RECEIPT_INBOUND.getType()){
            ErpReceiptDO erpReceiptDO = erpReceiptMapper.selectById(erpInbound.getRelatedOrderId());
            erpInbound.setPurchaseOrderId(erpReceiptDO.getPurchaseOrderId());
        }
        if (reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType()){
            erpInbound.setPurchaseOrderId(erpInbound.getRelatedOrderId());
        }

        erpInboundMapper.insert(erpInbound);

        List<ErpInboundDetailDO> detailDOList = new ArrayList<>();
        List<ErpInboundDetailAditReqVO> detailList = reqVO.getDetailList();
        for(ErpInboundDetailAditReqVO item : detailList){
            ErpInboundDetailDO detailDO = BeanUtilX.copy(item, ErpInboundDetailDO :: new);
            detailDO.setInboundId(erpInbound.getInboundId());
            detailDO.setInboundCode(erpInbound.getInboundCode());
            detailDOList.add(detailDO);
        }
        erpInboundDetailMapper.insertBatch(detailDOList);

        // 返回
        return erpInbound.getInboundId();
    }

    @Override
    public Long erpInboundEdit(ErpInboundAditReqVO reqVO) {
        // 校验存在
//        this.erpInboundValidateExists(reqVO.getInboundId());
        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.erpInboundValidateExists(reqVO.getInboundId()), reqVO);

        //入库单信息验证
        this.erpInboundValidate(reqVO);
        // 更新
        ErpInboundDO erpInbound = BeanUtilX.copy(reqVO, ErpInboundDO::new);
        if (reqVO.getBizType() == ErpInboundBizTypeEnum.RECEIPT_INBOUND.getType()){
            ErpReceiptDO erpReceiptDO = erpReceiptMapper.selectById(erpInbound.getRelatedOrderId());
            erpInbound.setPurchaseOrderId(erpReceiptDO.getPurchaseOrderId());
        }
        if (reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType()){
            erpInbound.setPurchaseOrderId(erpInbound.getRelatedOrderId());
        }
        erpInboundMapper.updateById(erpInbound);

        // 先删除后新增
        erpInboundDetailMapper.deleteByInboundId(reqVO.getInboundId());
        List<ErpInboundDetailDO> detailDOList = new ArrayList<>();
        List<ErpInboundDetailAditReqVO> detailList = reqVO.getDetailList();
        for(ErpInboundDetailAditReqVO item : detailList){
            ErpInboundDetailDO detailDO = BeanUtilX.copy(item, ErpInboundDetailDO :: new);
            detailDO.setInboundId(erpInbound.getInboundId());
            detailDO.setInboundCode(erpInbound.getInboundCode());
            detailDOList.add(detailDO);
        }
        erpInboundDetailMapper.insertBatch(detailDOList);

        // 返回
        return erpInbound.getInboundId();
    }

    @Override
    public void erpInboundDel(Long inboundId) {
        // 校验存在
        ErpInboundDO erpInboundDO = this.erpInboundValidateExists(inboundId);
        if(erpInboundDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException("5001", "单据状态不是未审核,不可删除！");
        }
        // 删除
        erpInboundMapper.deleteById(inboundId);
        erpInboundDetailMapper.deleteByInboundId(inboundId);
    }

    @Override
    public ResultX<BatchResult> erpInboundDelBatch(IdsReqVO reqVO){
        String id = EntityUtilX.getPropertyName(ErpInboundDO:: getInboundId);
        String code = EntityUtilX.getPropertyName(ErpInboundDO::getInboundCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), ErpInboundDO.class, ErpInboundDetailDO.class, id, code);
    }

    private ErpInboundDO erpInboundValidateExists(Long inboundId) {
        ErpInboundDO erpInbound = erpInboundMapper.selectById(inboundId);
        if (erpInbound == null) {
            // throw exception(ERP_INBOUND_NOT_EXISTS);
            throw new BizException("5001", "入库单不存在");
        }
        return erpInbound;
    }

    @Override
    public ErpInboundRespVO erpInboundDetail(Long inboundId) {
        // 校验存在
        ErpInboundDO erpInboundDO = this.erpInboundValidateExists(inboundId);
        // 对象转换
        ErpInboundRespVO respVO = BeanUtilX.copy(erpInboundDO, ErpInboundRespVO::new);
        //属性填充
        fillVoProperties(respVO);

        //查询明细列表
        ErpInboundDetailQueryReqVO detailQueryReqVO = new ErpInboundDetailQueryReqVO();
        detailQueryReqVO.setInboundId(inboundId);
        List<ErpInboundDetailRespVO> detailList = erpInboundDetailService.erpInboundDetailList(detailQueryReqVO);
        //工单直接退料入库单,委外直接退料入库单查询可入库数量
        Map<Long, WorkPickingMaterialTotalDO> workPickingMaterialTotalDOMap = new HashMap<>();
        if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.WORK_DIRECT_PICKING_RETURN_INBOUND.getType() || erpInboundDO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_RETURN_INBOUND.getType()) {
            List<WorkPickingMaterialTotalDO> list = workPickingMaterialTotalMapper.selectListByRelatedOrderId(erpInboundDO.getRelatedOrderId());
            workPickingMaterialTotalDOMap = list.stream().collect(Collectors.toMap(WorkPickingMaterialTotalDO::getMaterialId, Function.identity()));
        }
        for(ErpInboundDetailRespVO item : detailList) {
            if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.RECEIPT_INBOUND.getType()) {
                BigDecimal inboundableQty = erpReceiptDetailMapper.selectInboundableQty(item.getRelatedOrderDetailId());
                item.setInboundableQty(inboundableQty);
            } else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType()) {
                BigDecimal operableQty = purchaseOrderDetailMapper.queryOperableQty(item.getRelatedOrderDetailId());
                item.setOperableQty(operableQty);
            } else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.SALE_RETURN_INBOUND.getType()) {
                BigDecimal operableQty = saleReturnDetailMapper.queryOperableQty(item.getRelatedOrderDetailId());
                item.setOperableQty(operableQty);
            } else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.PROD_WORK_INBOUND.getType()) {
                BigDecimal inboundableQty = prodWorkMapper.selectInboundableQty(erpInboundDO.getRelatedOrderId());
                item.setInboundableQty(inboundableQty);
            } else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.WORK_PICKING_RETURN_INBOUND.getType()
                || erpInboundDO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_PICKING_RETURN_INBOUND.getType()) {
                BigDecimal inboundableQty = workPickingReturnDetailMapper.queryInboundableQty(item.getRelatedOrderDetailId());
                item.setInboundableQty(inboundableQty);
            } else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.SALE_EXCHANGE_INBOUND.getType()) {
                BigDecimal inboundableQty = saleExchangeDetailMapper.queryInboundableQty(item.getRelatedOrderDetailId());
                item.setInboundableQty(inboundableQty);
            } else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_EXCHANGE_INBOUND.getType()) {
                BigDecimal inboundableQty = purchaseExchangeDetailMapper.queryInboundableQty(item.getRelatedOrderDetailId());
                item.setInboundableQty(inboundableQty);
            }else if (erpInboundDO.getBizType() == ErpInboundBizTypeEnum.WORK_DIRECT_PICKING_RETURN_INBOUND.getType() || erpInboundDO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_RETURN_INBOUND.getType()){
                WorkPickingMaterialTotalDO workPickingMaterialTotalDO = workPickingMaterialTotalDOMap.get(item.getMaterialId());
                if (workPickingMaterialTotalDO != null){
                    BigDecimal outboundedQty = workPickingMaterialTotalDO.getOutboundedQty();
                    BigDecimal inboundedQty = workPickingMaterialTotalDO.getInboundedQty();
                    // 可入库数量 = 已出库数量 - 已入库数量
                    BigDecimal inboundableQty = outboundedQty.subtract(inboundedQty);
                    item.setInboundableQty(inboundableQty);
                }
            }
        }
        respVO.setDetailList(detailList);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(inboundId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<ErpInboundRespVO> erpInboundList(ErpInboundQueryReqVO reqVO) {
        List<ErpInboundRespVO> respVOList = BeanUtilX.copy(erpInboundMapper.selectList(reqVO), ErpInboundRespVO::new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<ErpInboundRespVO> erpInboundPage(ErpInboundPageReqVO reqVO) {
        PageResult<ErpInboundRespVO> pageResult = BeanUtilX.copy(erpInboundMapper.selectPage(reqVO), ErpInboundRespVO :: new);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }

        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private ErpInboundRespVO fillVoProperties(ErpInboundRespVO respVO) {
        List<ErpInboundRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        List<ErpInboundRespVO> erpRespVOList = batchFillVoProperties(respVOList);
        return erpRespVOList.get(0);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private List<ErpInboundRespVO> batchFillVoProperties(List<ErpInboundRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return respVOList;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> directorOrgIdList = new ArrayList<>();
        for(ErpInboundRespVO item : respVOList) {
            directorIdList.add(item.getDirectorId());
            directorOrgIdList.add(item.getDirectorOrgId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.INBOUND_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(directorOrgIdList);

        for(ErpInboundRespVO item : respVOList) {

            // 入库单类型
            if(StrUtilX.isNotEmpty(item.getInboundTypeDictId())){
                String inboundTypeDictId = CustomerDictEnum.INBOUND_TYPE.getDictCode() + "-" + item.getInboundTypeDictId();
                item.setInboundTypeDictName(dictMap.get(inboundTypeDictId));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if (item.getDirectorId() != null) {
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
        return respVOList;
    }

    @Override
    public BatchResult erpInboundApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ErpInboundDO> list = erpInboundMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //查询物料仓库数据
        List<Long> inboundIdList = list.stream().map(ErpInboundDO::getInboundId).collect(Collectors.toList());
        List<MaterialWarehouseBO> materialWarehouseList = erpInboundDetailMapper.inboundMaterialWarehouseList(inboundIdList);

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();

        for (ErpInboundDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //物料库存加锁
                for(MaterialWarehouseBO materialWarehouse : materialWarehouseList){
                    if(item.getInboundId().equals(materialWarehouse.getOrderId())){
                        String bizKey = RedisConstants.MATERIAL_STOCK + ":" + materialWarehouse.getMaterialId()
                                + "_"  + materialWarehouse.getWarehouseOrgId();
                        redisLockManager.acquireLock(bizKey);
                    }
                }

                //封装审批数据对象L
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = erpInboundApproveHandler.process(item, flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            } catch (Exception exception){
                //异常捕捉
                exception.printStackTrace();
                FailItem failItem = new FailItem();
                failItem.setCode(item.getInboundCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            } finally {
                //物料库存锁释放
                for(MaterialWarehouseBO materialWarehouse : materialWarehouseList){
                    if(item.getInboundId().equals(materialWarehouse.getOrderId())){
                        String bizKey = RedisConstants.MATERIAL_STOCK + ":" + materialWarehouse.getMaterialId()
                                + "_"  + materialWarehouse.getWarehouseOrgId();
                        redisLockManager.releaseLock(bizKey);
                    }
                }
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ErpInboundDO item : list) {
                String reason = reasonMap.get(item.getInboundCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInboundId());
                    messageInfoBO.setObjCode(item.getInboundCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getInboundId());
                    messageInfoBO.setObjCode(item.getInboundCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object erpInboundFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        ErpInboundDO currentDO = this.erpInboundValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();
        return erpInboundFlowCallBackHandler.handleFlowCallback(currentDO, flowCallbackBO);
    }

    @Override
    public ResultX erpInboundQuoteList(@Valid ErpInboundQuoteReqVO reqVO){

        // 收货入库单-引用收货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.RECEIPT_INBOUND.getType()){
            ErpReceiptQueryReqVO queryReqVO = new ErpReceiptQueryReqVO();
            queryReqVO.setReceiptCode(reqVO.getReceiptCode());
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setIsFullInbounded(0);
            queryReqVO.setExclBizTypeList(reqVO.getExclBizTypeList());
            return ResultX.success(erpReceiptService.erpReceiptList(queryReqVO));
        }

        // 采购入库单-引用采购订单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType()){
            PurchaseOrderQueryReqVO queryReqVO = new PurchaseOrderQueryReqVO();
            queryReqVO.setPurchaseOrderCode(reqVO.getPurchaseOrderCode());
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setFormStatusList(Arrays.asList(0, 1));
//            queryReqVO.setIsFullOpered(0);
//            queryReqVO.setIsForceClose(0);
            return ResultX.success(purchaseOrderMapper.selectList(queryReqVO));
        }

        // 销售退货入库单-引用销售退货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.SALE_RETURN_INBOUND.getType()){
            SaleReturnQueryReqVO queryReqVO = new SaleReturnQueryReqVO();
            queryReqVO.setSalesReturnCode(reqVO.getSalesReturnCode());
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setIsFullOpered(0);
            return ResultX.success(saleReturnMapper.selectList(queryReqVO));
        }

        // 产工单入库单-引用生产工单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PROD_WORK_INBOUND.getType()){
            ProdWorkQueryReqVO queryReqVO = new ProdWorkQueryReqVO();
            queryReqVO.setProdWorkCode(reqVO.getProdWorkCode());
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setIsInbound(1);
            queryReqVO.setIsFullInbounded(0);
            return ResultX.success(prodWorkMapper.selectList(queryReqVO));
        }

        // 工单退料入库单-引用工单退料单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.WORK_PICKING_RETURN_INBOUND.getType()){
            WorkPickingReturnQueryReqVO queryReqVO  = new WorkPickingReturnQueryReqVO();
            queryReqVO.setWorkPickingReturnCode(reqVO.getWorkPickingReturnCode());
            queryReqVO.setWorkPickingReturnBizType(0);
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setIsFullInbounded(0);
            return ResultX.success(workPickingReturnMapper.selectList(queryReqVO));
        }

        // 委外订单退料入库单-引用委外订单退料单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_PICKING_RETURN_INBOUND.getType()){
            WorkPickingReturnQueryReqVO queryReqVO  = new WorkPickingReturnQueryReqVO();
            queryReqVO.setWorkPickingReturnCode(reqVO.getWorkPickingReturnCode());
            queryReqVO.setWorkPickingReturnBizType(1);
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setIsFullInbounded(0);
            return ResultX.success(workPickingReturnMapper.selectList(queryReqVO));
        }

        // 销售换货入库单-引用销售换货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.SALE_EXCHANGE_INBOUND.getType()){
            SaleExchangeQueryReqVO exchangeReqVO = new SaleExchangeQueryReqVO();
            exchangeReqVO.setSaleExchangeCode(reqVO.getSaleExchangeCode());
            exchangeReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey().shortValue());
            exchangeReqVO.setIsFullInbounded((short) 0);
            return ResultX.success(saleExchangeMapper.selectList(exchangeReqVO));
        }

        // 生产工单直接退料入库单-引用生产工单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.WORK_DIRECT_PICKING_RETURN_INBOUND.getType()){
            ProdWorkQueryReqVO queryReqVO = new ProdWorkQueryReqVO();
            queryReqVO.setProdWorkCode(reqVO.getProdWorkCode());
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setFormStatusList(Arrays.asList(new Integer[]{0,1}));
            queryReqVO.setIsExistInboundableQty(1);
            queryReqVO.setInProcessConfigDictId(0);
            return ResultX.success(prodWorkMapper.selectList(queryReqVO));
        }
        // 委外订单直接退料入库单-引用委外采购订单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_RETURN_INBOUND.getType()){
            PurchaseOrderQueryReqVO queryReqVO = new PurchaseOrderQueryReqVO();
            queryReqVO.setPurchaseOrderCode(reqVO.getPurchaseOrderCode());
            queryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            queryReqVO.setFormStatusList(Arrays.asList(0, 1));
            queryReqVO.setIsExistInboundableQty(1);
            queryReqVO.setInProcessConfigDictId(0);
            return ResultX.success(purchaseOrderMapper.selectList(queryReqVO));
        }
        return ResultX.success();
    }


    @Override
    public ResultX erpInboundQuoteDetail(QuoteReqVO reqVO){
        // 收货入库单-引用收货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.RECEIPT_INBOUND.getType()){
            return ResultX.success(erpReceiptService.erpReceiptQuotedDetailByInbound(reqVO.getRelatedOrderId()));
        }

        // 采购入库单-引用采购订单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType()){
            return ResultX.success(purchaseOrderService.purchaseOrderQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 销售退货入库单-引用销售退货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.SALE_RETURN_INBOUND.getType()){
            return ResultX.success(saleReturnService.saleReturnQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 产工单入库单-引用生产工单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PROD_WORK_INBOUND.getType()){
            return ResultX.success(prodWorkService.prodWorkQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 工单退料入库单-引用工单退料单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.WORK_PICKING_RETURN_INBOUND.getType()){
            return ResultX.success(workPickingReturnService.workPickingReturnQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 委外订单退料入库单-引用委外订单退料单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_PICKING_RETURN_INBOUND.getType()){
             return ResultX.success(workPickingReturnService.workPickingReturnQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 采购换货入库单-引用采购换货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_EXCHANGE_INBOUND.getType()){
            return ResultX.success(purchaseExchangeService.purchaseExchangeQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 销售换货入库单-引用销售换货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.SALE_EXCHANGE_INBOUND.getType()){
            return ResultX.success(saleExchangeService.saleExchangeQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 生产工单直接退料入库单-引用生产工单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.WORK_DIRECT_PICKING_RETURN_INBOUND.getType()){
            return ResultX.success(prodWorkService.prodWorkDirectReturnQuotedDetail(reqVO.getRelatedOrderId()));
        }

        // 委外直接退料入库单-引用委外采购订单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_RETURN_INBOUND.getType()){
            return ResultX.success(purchaseOrderService.purchaseOrderDirectReturnQuotedDetail(reqVO.getRelatedOrderId()));
        }
        return ResultX.success();
    }

    @Override
    public List<ErpInboundRespVO> erpInboundProdQuoteList(ErpInboundQueryReqVO reqVO) {
        List<Long> prodWorkIdList = new ArrayList<>();

        if(reqVO.getProdOrderId()!=null){
            //查询生产工单
            ProdWorkQueryReqVO prodWorkQuery = new ProdWorkQueryReqVO();
            prodWorkQuery.setProdOrderId(reqVO.getProdOrderId());
            List<ProdWorkDO> prodWorkList = prodWorkMapper.selectList(prodWorkQuery);
            if(CollUtilX.isNotEmpty(prodWorkList)){
                prodWorkIdList = prodWorkList.stream().map(ProdWorkDO::getProdWorkId).collect(Collectors.toList());
            }
        }else {
            prodWorkIdList.add(reqVO.getProdWorkId());
        }

        if(CollUtilX.isEmpty(prodWorkIdList)){
            return List.of();
        }

        //查询生产工单入库单
        ErpInboundQueryReqVO erpInboundQuery = new ErpInboundQueryReqVO();
        erpInboundQuery.setRelatedOrderIdList(prodWorkIdList);
        erpInboundQuery.setDataStatus(reqVO.getDataStatus());
        erpInboundQuery.setBizType(ErpInboundBizTypeEnum.PROD_WORK_INBOUND.getType());
        return this.erpInboundList(erpInboundQuery);
    }


    /**
     *  入库单验证
     * @param reqVO 请求参数
     */
    private void erpInboundValidate(ErpInboundAditReqVO reqVO){
        Long relatedOrderId = reqVO.getRelatedOrderId();
        List<ErpInboundDetailAditReqVO> detailList = reqVO.getDetailList();
        if(CollUtilX.isEmpty(detailList)){
            return;
        }

        //合并关联行数量
        Map<Long, DocumentRespBO> inboundQtyMap = new HashMap<>();
        for(ErpInboundDetailAditReqVO detailAditReqVO : detailList){
            Long relatedOrderDetailId = detailAditReqVO.getRelatedOrderDetailId();
            DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
            if(documentRespBO == null ){
                documentRespBO = new DocumentRespBO();
                documentRespBO.setFieldId(relatedOrderDetailId);
                documentRespBO.setCode(detailAditReqVO.getMaterialCode());
                documentRespBO.setSumQty(detailAditReqVO.getInboundQty());
                inboundQtyMap.put(relatedOrderDetailId, documentRespBO);
            }else{
                documentRespBO.setSumQty(documentRespBO.getSumQty().add(detailAditReqVO.getInboundQty()));
                inboundQtyMap.put(relatedOrderDetailId, documentRespBO);
            }
        }

        // 收货入库单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.RECEIPT_INBOUND.getType()){
            //查询收货单(可入库数量)
            List<DocumentRespBO> inboundableQtyRespList = erpReceiptDetailMapper.inboundableQtyList(relatedOrderId);
            Map<Long, BigDecimal> inboundableQtyMap = inboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    DocumentRespBO::getSumQty));

            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
                Long relatedOrderDetailId = entry.getKey();
                BigDecimal inboundableQty = inboundableQtyMap.get(relatedOrderDetailId);
                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
                if (inboundableQty == null || documentRespBO.getSumQty().compareTo(inboundableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }
        }

        // 采购入库单 todo 允许超收
//        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType()){
//            //查询采购订单(可操作数量)
//            List<DocumentRespBO> operableQtyRespList = purchaseDetailMapper.operableQtyList(relatedOrderId);
//            Map<Long, BigDecimal> operableQtyMap = operableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
//                    DocumentRespBO::getSumQty));
//
//            //判断入库数量是否超出
//            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
//                Long relatedOrderDetailId = entry.getKey();
//                BigDecimal operableQty = operableQtyMap.get(relatedOrderDetailId);
//                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
//                if (operableQty == null || documentRespBO.getSumQty().compareTo(operableQty) > 0){
//                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
//                }
//            }
//        }

        // 销售退货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.SALE_RETURN_INBOUND.getType()){
            //查询销售退货单(可操作数量)
            List<DocumentRespBO> operableQtyRespList = saleReturnDetailMapper.operableQtyList(relatedOrderId);
            Map<Long, BigDecimal> operableQtyMap = operableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    DocumentRespBO::getSumQty));

            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
                Long relatedOrderDetailId = entry.getKey();
                BigDecimal operableQty = operableQtyMap.get(relatedOrderDetailId);
                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
                if (operableQty == null || documentRespBO.getSumQty().compareTo(operableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }
        }

        // 生产工单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PROD_WORK_INBOUND.getType()){
            ProdWorkDO prodWorkDO = prodWorkMapper.selectById(relatedOrderId);
            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
                Long relatedOrderDetailId = entry.getKey();
                BigDecimal inboundableQty = prodWorkDO.getInboundableQty();
                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
                if (inboundableQty == null || documentRespBO.getSumQty().compareTo(inboundableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }
        }

        //工单退料入库单和委外订单退料入库单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.WORK_PICKING_RETURN_INBOUND.getType()
                || reqVO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_PICKING_RETURN_INBOUND.getType()){
            //查询工单领料退料单(可入库数量)
            List<DocumentRespBO> inboundableQtyRespList = pickingReturnDetailMapper.inboundableQtyList(relatedOrderId);
            Map<Long, BigDecimal> inboundableQtyMap = inboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    DocumentRespBO::getSumQty));

            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
                Long relatedOrderDetailId = entry.getKey();
                BigDecimal inboundableQty = inboundableQtyMap.get(relatedOrderDetailId);
                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
                if (inboundableQty == null || documentRespBO.getSumQty().compareTo(inboundableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }

        }

        // 销售换货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.SALE_EXCHANGE_INBOUND.getType()){
            //查询销售退货单(可操作数量)
            List<DocumentRespBO> inboundableQtyRespList = saleExchangeDetailMapper.inboundableQtyList(relatedOrderId);
            Map<Long, BigDecimal> inboundableQtyMap = inboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    DocumentRespBO::getSumQty));

            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
                Long relatedOrderDetailId = entry.getKey();
                BigDecimal inboundableQty = inboundableQtyMap.get(relatedOrderDetailId);
                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
                if (inboundableQty == null || documentRespBO.getSumQty().compareTo(inboundableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }
        }

        // 采购换货单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.PURCHASE_EXCHANGE_INBOUND.getType()){
            //查询采购换货单(可操作数量)
            List<DocumentRespBO> inboundableQtyRespList = purchaseExchangeDetailMapper.inboundableQtyList(relatedOrderId);
            Map<Long, BigDecimal> inboundableQtyMap = inboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    DocumentRespBO::getSumQty));

            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : inboundQtyMap.entrySet()){
                Long relatedOrderDetailId = entry.getKey();
                BigDecimal inboundableQty = inboundableQtyMap.get(relatedOrderDetailId);
                DocumentRespBO documentRespBO = inboundQtyMap.get(relatedOrderDetailId);
                if (inboundableQty == null || documentRespBO.getSumQty().compareTo(inboundableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }
        }

        //工单直接退料入库单
        if(reqVO.getBizType() == ErpInboundBizTypeEnum.WORK_DIRECT_PICKING_RETURN_INBOUND.getType() || reqVO.getBizType() == ErpInboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_RETURN_INBOUND.getType()){
            //根据物料合并关联行数量
            Map<Long, DocumentRespBO> materialInboundQtyMap = new HashMap<>();
            for(ErpInboundDetailAditReqVO detailAditReqVO : detailList){
                Long materialId = detailAditReqVO.getMaterialId();
                DocumentRespBO documentRespBO = materialInboundQtyMap.get(materialId);
                if(documentRespBO == null ){
                    documentRespBO = new DocumentRespBO();
                    documentRespBO.setFieldId(materialId);
                    documentRespBO.setCode(detailAditReqVO.getMaterialCode());
                    documentRespBO.setSumQty(detailAditReqVO.getInboundQty());
                    materialInboundQtyMap.put(materialId, documentRespBO);
                }else{
                    documentRespBO.setSumQty(documentRespBO.getSumQty().add(detailAditReqVO.getInboundQty()));
                    materialInboundQtyMap.put(materialId, documentRespBO);
                }
            }
            //查询工单(可入库数量)
            List<DocumentRespBO> inboundableQtyRespList = workPickingMaterialTotalMapper.inboundedQtyList(relatedOrderId);
            Map<Long, BigDecimal> inboundableQtyMap = inboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                    DocumentRespBO::getSumQty));

            //判断入库数量是否超出
            for (Map.Entry<Long, DocumentRespBO> entry : materialInboundQtyMap.entrySet()){
                Long materialId = entry.getKey();
                BigDecimal inboundableQty = inboundableQtyMap.get(materialId);
                DocumentRespBO documentRespBO = materialInboundQtyMap.get(materialId);
                if (inboundableQty == null || documentRespBO.getSumQty().compareTo(inboundableQty) > 0){
                    throw new BizException("5001", "物料编码【"+documentRespBO.getCode()+"】入库数量不允许超过可入库数量!");
                }
            }
        }
    }
}
