package com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeduction;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.purchase.controller.admin.purprocessoutdeduction.vo.*;
import com.mongoso.mgs.module.purchase.service.purprocessoutdeduction.PurProcessOutDeductionService;

/**
 * 工序委外采购扣费单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchase")
@Validated
public class PurProcessOutDeductionController {

    @Resource
    private PurProcessOutDeductionService purProcessOutDeductionService;

    @OperateLog("工序委外采购扣费单添加或编辑")
    @PostMapping("/purProcessOutDeductionAdit")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:adit')")
    public ResultX<Long> purProcessOutDeductionAdit(@Valid @RequestBody PurProcessOutDeductionAditReqVO reqVO) {
        return success(reqVO.getProcessOutDeductionId() == null
                            ? purProcessOutDeductionService.purProcessOutDeductionAdd(reqVO)
                            : purProcessOutDeductionService.purProcessOutDeductionEdit(reqVO));
    }

    @OperateLog("工序委外采购扣费单删除")
    @PostMapping("/purProcessOutDeductionDel")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:delete')")
    public ResultX<Boolean> purProcessOutDeductionDel(@Valid @RequestBody PurProcessOutDeductionPrimaryReqVO reqVO) {
        purProcessOutDeductionService.purProcessOutDeductionDel(reqVO.getProcessOutDeductionId());
        return success(true);
    }

    @OperateLog("工序委外采购扣费单删除(批量)")
    @PostMapping("/purProcessOutDeductionDelBatch")
    @PreAuthorize("@ss.hasPermission('purProcessOutReturn:delete')")
    public ResultX<BatchResult> purProcessOutDeductionDelBatch(@Valid @RequestBody IdReq reqVO) {
        return purProcessOutDeductionService.purProcessOutDeductionDelBatch(reqVO);
    }

    @OperateLog("工序委外采购扣费单详情")
    @PostMapping("/purProcessOutDeductionDetail")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:query')")
    public ResultX<PurProcessOutDeductionRespVO> purProcessOutDeductionDetail(@Valid @RequestBody PurProcessOutDeductionPrimaryReqVO reqVO) {
        return success(purProcessOutDeductionService.purProcessOutDeductionDetail(reqVO.getProcessOutDeductionId()));
    }

    @OperateLog("工序委外采购扣费单列表")
    @PostMapping("/purProcessOutDeductionList")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:query')")
    @DataPermission
    public ResultX<List<PurProcessOutDeductionRespVO>> purProcessOutDeductionList(@Valid @RequestBody PurProcessOutDeductionQueryReqVO reqVO) {
        return success(purProcessOutDeductionService.purProcessOutDeductionList(reqVO));
    }

    @OperateLog("工序委外采购扣费单列表")
    @PostMapping("/purProcessOutDeductionQuoteList")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:query')")
    public ResultX<List<PurProcessOutDeductionRespVO>> purProcessOutDeductionQuoteList(@Valid @RequestBody PurProcessOutDeductionQueryReqVO reqVO) {
        return success(purProcessOutDeductionService.purProcessOutDeductionList(reqVO));
    }

    @OperateLog("工序委外采购扣费单分页")
    @PostMapping("/purProcessOutDeductionPage")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:query')")
    @DataPermission
    public ResultX<PageResult<PurProcessOutDeductionRespVO>> purProcessOutDeductionPage(@Valid @RequestBody PurProcessOutDeductionPageReqVO reqVO) {
        return success(purProcessOutDeductionService.purProcessOutDeductionPage(reqVO));
    }

    @OperateLog("工序委外采购扣费单分页")
    @PostMapping("/purProcessOutDeductionQuotePage")
    @PreAuthorize("@ss.hasPermission('purProcessOutDeduction:query')")
    public ResultX<PageResult<PurProcessOutDeductionRespVO>> purProcessOutDeductionQuotePage(@Valid @RequestBody PurProcessOutDeductionPageReqVO reqVO) {
        return success(purProcessOutDeductionService.purProcessOutDeductionPage(reqVO));
    }

    @OperateLog("工序委外采购扣费单审核")
    @PostMapping("/purProcessOutDeductionApprove")
    @PreAuthorize("@ss.hasPermission('device:adit')")
    public ResultX<BatchResult> purProcessOutDeductionApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = purProcessOutDeductionService.purProcessOutDeductionApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("工序委外采购扣费单回调接口")
    @PostMapping("/purProcessOutDeductionFlowCallback")
    public ResultX<Object> purProcessOutDeductionFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(purProcessOutDeductionService.purProcessOutDeductionFlowCallback(reqVO));
    }

    @OperateLog("主子表工序委外采购扣费单明细分页")
    @PostMapping("/queryProcessOutDeductionDetailPage")
    @PreAuthorize("@ss.hasPermission('purchaseReturn:query')")
    @DataPermission
    public ResultX<PageResult<PurProcessOutDeductionDetailResp>> queryProcessOutDeductionDetailPage(@Valid @RequestBody PurProcessOutDeductionPageReqVO reqVO) {
        PageResult<PurProcessOutDeductionDetailResp> pageResult = purProcessOutDeductionService.queryProcessOutDeductionDetailPage(reqVO);
        return success(pageResult);
    }

}
