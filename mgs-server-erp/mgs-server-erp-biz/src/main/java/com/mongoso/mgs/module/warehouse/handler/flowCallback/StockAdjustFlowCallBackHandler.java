package com.mongoso.mgs.module.warehouse.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.warehouse.dal.db.stockadjust.StockAdjustDO;
import org.springframework.stereotype.Component;

/**
 * @author: Fashon.Liu
 * @date: 2024/12/19 9:40
 * @description: 发货单回调处理类
 */

@Component
public class StockAdjustFlowCallBackHandler extends FlowCallbackHandler<StockAdjustDO> {

    protected StockAdjustFlowCallBackHandler(FlowApproveHandler<StockAdjustDO> approveHandler) {
        super(approveHandler);
    }
}
