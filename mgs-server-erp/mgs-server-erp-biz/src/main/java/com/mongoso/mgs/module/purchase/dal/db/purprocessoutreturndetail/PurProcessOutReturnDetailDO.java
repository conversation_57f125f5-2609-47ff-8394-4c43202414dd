package com.mongoso.mgs.module.purchase.dal.db.purprocessoutreturndetail;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 工序委外采购退货单明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_pur_process_out_return_detail", autoResultMap = true)
//@KeySequence("u_pur_process_out_return_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurProcessOutReturnDetailDO extends OperateDO {

    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long processOutReturnDetailId;

    /** 行号 */
    private Integer rowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 计算方式['乘法','除法'] */
    private Integer calculatType;

    /** 单价(含税) */
    private BigDecimal inclTaxUnitPrice;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 备注 */
    private String remark;

    /** 退货数量 */
    private BigDecimal returnQty;

    /** 工序ID */
    private Long processId;

    /** 工序编码 */
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 工序委外采购退货单主键ID */
    private Long processOutReturnId;

    /** 工序委外采购退货单号 */
    private String processOutReturnCode;

    /** 工序委外采购订单明细ID */
    private Long processOutDetailId;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 币种字典ID */
    private String currencyDictId;

    /** 本币币种字典ID */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币金额 */
    private BigDecimal exclTaxLocalCurrencyAmt;

    /** 含税本币金额 */
    private BigDecimal inclTaxLocalCurrencyAmt;

}
