package com.mongoso.mgs.module.produce.controller.admin.materialanalysisdetail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
 
/**
 * 物料分析生产物料 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MaterialAnalysisDetailBaseVO implements Serializable {

    /** 主键ID */
    private Long materialAnalysisDetailId;

    /** 物料分析ID */
    private Long materialAnalysisId;

    /** 物料分析编码 */
    private String materialAnalysisCode;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 行号 */
    private Integer rowNo;

    /** 需求数量 */
    private BigDecimal demandQty;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 可采购数量 */
    private BigDecimal purchaseableQty;

    /** 已采购数量 */
    private BigDecimal purchasedQty;

    /** 物料是否全部已采购 */
    private Integer isMaterialFullPurchased;

    /** 已规划需求数量 */
    private BigDecimal plannedDemandQty;

    /** 可规划需求数量 */
    private BigDecimal planableDemandQty;

    /** 物料是否全部已规划 */
    private Integer isMaterialFullPlaned;

}
