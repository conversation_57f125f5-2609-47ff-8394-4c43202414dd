package com.mongoso.mgs.module.base.controller.admin.customeraddress.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 客户地址 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class CustomerAddressQueryReqVO {

    /** 主键 */
    private Long customerAddressId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 联系人地址 */
    private String contactAddress;

    /** 是否默认地址 */
    private Integer isDefaultAddress;

    /** 备注 */
    private String remark;

    /** 关联单据ID **/
    private Long relatedOrderId;

    /**
     * 关联单据号
     */
    private String relatedOrderCode;

    /** 业务类型["客户","供应商"] **/
    private Integer bizType;

    /** 行号 */
    private Integer rowNo;

}
