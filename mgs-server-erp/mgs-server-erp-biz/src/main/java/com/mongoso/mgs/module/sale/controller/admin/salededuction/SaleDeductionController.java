package com.mongoso.mgs.module.sale.controller.admin.salededuction;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.sale.controller.admin.salededuction.vo.*;
import com.mongoso.mgs.module.sale.dal.db.salededuction.SaleDeductionDO;
import com.mongoso.mgs.module.sale.service.salededuction.SaleDeductionService;

/**
 * 销售扣费单 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sale")
@Validated
public class SaleDeductionController {

    @Resource
    private SaleDeductionService deductionService;

    @OperateLog("销售扣费单添加或编辑")
    @PostMapping("/saleDeductionAdit")
    @PreAuthorize("@ss.hasPermission('saleDeduction:adit')")
    public ResultX<Long> saleDeductionAdit(@Valid @RequestBody SaleDeductionAditReqVO reqVO) {
        return success(reqVO.getDeductionOrderId() == null
                            ? deductionService.saleDeductionAdd(reqVO)
                            : deductionService.saleDeductionEdit(reqVO));
    }

    @OperateLog("销售扣费单删除")
    @PostMapping("/saleDeductionDel")
    @PreAuthorize("@ss.hasPermission('saleDeduction:del')")
    public ResultX<Boolean> saleDeductionDel(@Valid @RequestBody SaleDeductionPrimaryReqVO reqVO) {
        deductionService.saleDeductionDel(reqVO.getDeductionOrderId());
        return success(true);
    }

    @OperateLog("销售扣费单删除")
    @PostMapping("/saleDeductionDelBatch")
    @PreAuthorize("@ss.hasPermission('saleDeduction:del')")
    public ResultX<BatchResult> saleDeductionDelBatch(@Valid @RequestBody IdReq reqVO) {
        return deductionService.saleDeductionDelBatch(reqVO);
    }

    @OperateLog("销售扣费单详情")
    @PostMapping("/saleDeductionDetail")
    @PreAuthorize("@ss.hasPermission('saleDeduction:query')")
    public ResultX<SaleDeductionRespVO> saleDeductionDetail(@Valid @RequestBody SaleDeductionPrimaryReqVO reqVO) {
        SaleDeductionRespVO saleDeductionRespVO = deductionService.saleDeductionDetail(reqVO.getDeductionOrderId());
        return success(saleDeductionRespVO);
    }

    @OperateLog("销售扣费单列表")
    @PostMapping("/saleDeductionList")
    @PreAuthorize("@ss.hasPermission('saleDeduction:query')")
    @DataPermission
    public ResultX<List<SaleDeductionRespVO>> saleDeductionList(@Valid @RequestBody SaleDeductionQueryReqVO reqVO) {
        List<SaleDeductionDO> list = deductionService.saleDeductionList(reqVO);
        return success(BeanUtilX.copyList(list, SaleDeductionRespVO::new));
    }

    @OperateLog("销售扣费单列表")
    @PostMapping("/saleDeductionQuoteList")
    @PreAuthorize("@ss.hasPermission('saleDeduction:query')")
    public ResultX<List<SaleDeductionRespVO>> saleDeductionQuoteList(@Valid @RequestBody SaleDeductionQueryReqVO reqVO) {
        List<SaleDeductionDO> list = deductionService.saleDeductionList(reqVO);
        return success(BeanUtilX.copyList(list, SaleDeductionRespVO::new));
    }

    @OperateLog("销售扣费单分页")
    @PostMapping("/saleDeductionPage")
    @PreAuthorize("@ss.hasPermission('saleDeduction:query')")
    @DataPermission
    public ResultX<PageResult<SaleDeductionDetailResp>> saleDeductionPage(@Valid @RequestBody SaleDeductionPageReqVO reqVO) {
        PageResult<SaleDeductionDetailResp> pageResult = deductionService.saleDeductionPage(reqVO);
        return success(pageResult);
    }

    @OperateLog("销售扣费单分页")
    @PostMapping("/saleDeductionQuotePage")
    @PreAuthorize("@ss.hasPermission('saleDeduction:query')")
    public ResultX<PageResult<SaleDeductionDetailResp>> saleDeductionQuotePage(@Valid @RequestBody SaleDeductionPageReqVO reqVO) {
        PageResult<SaleDeductionDetailResp> pageResult = deductionService.saleDeductionPage(reqVO);
        return success(pageResult);
    }

    @OperateLog("主子表销售扣费单细分页")
    @PostMapping("/querySaleDeductionDetailPage")
    @PreAuthorize("@ss.hasPermission('saleQuoteDetail:query')")
    @DataPermission
    public ResultX<PageResult<SaleDeductionDetailResp>> querySaleDeductionDetailPage(@Valid @RequestBody SaleDeductionPageReqVO reqVO) {
        PageResult<SaleDeductionDetailResp> pageResult = deductionService.querySaleDeductionDetailPage(reqVO);
        return success(pageResult);
    }

    @OperateLog("销售扣费单审核")
    @PostMapping("/saleDeductionApprove")
    @PreAuthorize("@ss.hasPermission('container:adit')")
    public ResultX<BatchResult> saleDeductionApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = deductionService.saleDeductionApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("销售扣费单回调接口")
    @PostMapping("/saleDeductionFlowCallback")
    public ResultX<Object> saleDeductionFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(deductionService.saleDeductionFlowCallback(reqVO));
    }


}
