package com.mongoso.mgs.module.warehouse.service.erptransfer;

import com.mongoso.mgs.common.constants.RedisConstants;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.reidslock.RedisLockManager;
import com.mongoso.mgs.common.vo.IdsReqVO;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.ErpTransferRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.detail.ErpTransferDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.detail.ErpTransferDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erptransfer.vo.detail.ErpTransferDetailRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.erptransfer.ErpTransferDO;
import com.mongoso.mgs.module.warehouse.dal.db.erptransfer.ErpTransferDetailDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erptransfer.ErpTransferDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erptransfer.ErpTransferMapper;
import com.mongoso.mgs.module.warehouse.handler.approve.ErpTransferApproveHandler;
import com.mongoso.mgs.module.warehouse.handler.flowCallback.ErpTransferFlowCallBackHandler;
import com.mongoso.mgs.module.warehouse.service.erpmaterialstock.ErpMaterialStockService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.warehouse.enums.ErrorCodeConstants.*;


/**
 * 调拨单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpTransferServiceImpl implements ErpTransferService {

    @Resource
    private ErpTransferMapper erpTransferMapper;

    @Resource
    private ErpTransferDetailMapper erpTransferDetailMapper;

    @Resource
    private ErpTransferDetailService erpTransferDetailService;

    @Resource
    private ErpMaterialStockService erpMaterialStockService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    private SeqService seqService;

    @Resource
    @Lazy
    private ErpTransferApproveHandler erpTransferApproveHandler;

    @Resource
    private ErpTransferFlowCallBackHandler erpTransferFlowCallBackHandler;

    @Resource
    private RedisLockManager redisLockManager;

    @Override
    public Long erpTransferAdd(ErpTransferAditReqVO reqVO) {

        // 生成调拨单号
        reqVO.setTransferCode(seqService.getGenerateCode(reqVO.getTransferCode(), MenuEnum.TRANSFER_ORDER.menuId));

        // 插入
        ErpTransferDO erpTransfer = BeanUtilX.copy(reqVO, ErpTransferDO::new);
        erpTransferMapper.insert(erpTransfer);

        List<ErpTransferDetailDO> detailDOList = new ArrayList<>();
        List<ErpTransferDetailAditReqVO> detailList = reqVO.getDetailList();
        for(ErpTransferDetailAditReqVO item : detailList){
            ErpTransferDetailDO detailDO = BeanUtilX.copy(item, ErpTransferDetailDO :: new);
            detailDO.setTransferId(erpTransfer.getTransferId());
            detailDO.setTransferCode(erpTransfer.getTransferCode());
            detailDOList.add(detailDO);
        }
        erpTransferDetailMapper.insertBatch(detailDOList);

        // 返回
        return erpTransfer.getTransferId();
    }

    @Override
    public Long erpTransferEdit(ErpTransferAditReqVO reqVO) {
        // 校验存在
//        this.erpTransferValidateExists(reqVO.getTransferId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.erpTransferValidateExists(reqVO.getTransferId()), reqVO);

        // 更新
        ErpTransferDO erpTransfer = BeanUtilX.copy(reqVO, ErpTransferDO::new);
        erpTransferMapper.updateById(erpTransfer);

        // 先删除后新增
        erpTransferDetailMapper.deleteByTransferId(reqVO.getTransferId());
        List<ErpTransferDetailDO> detailDOList = new ArrayList<>();
        List<ErpTransferDetailAditReqVO> detailList = reqVO.getDetailList();
        for(ErpTransferDetailAditReqVO item : detailList){
            ErpTransferDetailDO detailDO = BeanUtilX.copy(item, ErpTransferDetailDO :: new);
            detailDO.setTransferId(erpTransfer.getTransferId());
            detailDO.setTransferCode(erpTransfer.getTransferCode());
            detailDOList.add(detailDO);
        }
        erpTransferDetailMapper.insertBatch(detailDOList);

        // 返回
        return erpTransfer.getTransferId();
    }

    @Override
    public void erpTransferDel(Long transferId) {
        // 校验存在
        ErpTransferDO erpTransferDO = this.erpTransferValidateExists(transferId);
        if(erpTransferDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException("5001", "单据状态不是未审核,不可删除！");
        }
        // 删除
        erpTransferMapper.deleteById(transferId);
        erpTransferDetailMapper.deleteByTransferId(transferId);
    }

    @Override
    public ResultX<BatchResult> erpTransferDelBatch(IdsReqVO reqVO){
        String id = EntityUtilX.getPropertyName(ErpTransferDO:: getTransferId);
        String code = EntityUtilX.getPropertyName(ErpTransferDO :: getTransferCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), ErpTransferDO.class, ErpTransferDetailDO.class, id, code);
    }

    private ErpTransferDO erpTransferValidateExists(Long transferId) {
        ErpTransferDO erpTransfer = erpTransferMapper.selectById(transferId);
        if (erpTransfer == null) {
            // throw exception(ERP_TRANSFER_NOT_EXISTS);
            throw new BizException("5001", "调拨单不存在");
        }
        return erpTransfer;
    }

    @Override
    public ErpTransferRespVO erpTransferDetail(Long transferId) {
        //校验存在
        ErpTransferDO erpTransferDO = this.erpTransferValidateExists(transferId);
        //对象转换
        ErpTransferRespVO respVO = BeanUtilX.copy(erpTransferDO, ErpTransferRespVO::new);
        //查询明细列表
        ErpTransferDetailQueryReqVO reqVO = new ErpTransferDetailQueryReqVO();
        reqVO.setTransferId(transferId);
        List<ErpTransferDetailRespVO> detailList = erpTransferDetailService.erpTransferDetailList(reqVO);
        respVO.setDetailList(detailList);
        for(ErpTransferDetailRespVO detailRespVO : detailList){
            BigDecimal availableQty = erpMaterialStockService.queryStockAvailableQty(detailRespVO.getMaterialStockId());
            detailRespVO.setAvailableQty(availableQty);
        }

        //属性填充
        fillVoProperties(respVO);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(transferId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<ErpTransferRespVO> erpTransferList(ErpTransferQueryReqVO reqVO) {
        List<ErpTransferRespVO> respVOList = BeanUtilX.copy(erpTransferMapper.selectList(reqVO), ErpTransferRespVO :: new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<ErpTransferRespVO> erpTransferPage(ErpTransferPageReqVO reqVO) {
        PageResult<ErpTransferRespVO> pageResult =  BeanUtilX.copy(erpTransferMapper.selectPage(reqVO), ErpTransferRespVO :: new);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());

        return pageResult;
    }

    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(ErpTransferRespVO respVO) {
        List<ErpTransferRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<ErpTransferRespVO> respVOList) {

        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }

        List<Long> directorIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        for(ErpTransferRespVO respVO : respVOList){
            directorIdList.add(respVO.getDirectorId());
            orgIdList.add(respVO.getDirectorOrgId());
            orgIdList.add(respVO.getTransferOutOrgId());
            orgIdList.add(respVO.getTransferInOrgId());
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.TRANSFER_TYPE.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        //查询负责人
        Map<Long, String> directorMap = erpBaseService.getEmpNameByIdList(directorIdList);

        //查询责任部门信息
        Map<String, String> directorOrgMap = erpBaseService.getOrgNameByIds(orgIdList);

        // 属性填充
        for (ErpTransferRespVO item : respVOList) {
            // 调拨单类型
            if(StrUtilX.isNotEmpty(item.getTransferTypeDictId())){
                String transferTypeDictId = CustomerDictEnum.TRANSFER_TYPE.getDictCode() + "-" + item.getTransferTypeDictId();
                item.setTransferTypeDictName(dictMap.get(transferTypeDictId));
            }

            //调出仓库名称
            if(item.getTransferOutOrgId() != null){
                item.setTransferOutOrgName(directorOrgMap.get(item.getTransferOutOrgId()));
            }

            //调出仓库名称
            if(item.getTransferInOrgId() != null){
                item.setTransferInOrgName(directorOrgMap.get(item.getTransferInOrgId()));
            }

            // 审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //责任人
            if(item.getDirectorId() != null){
                item.setDirectorName(directorMap.get(item.getDirectorId()));
            }

            //责任部门
            if(item.getDirectorOrgId() != null){
                item.setDirectorOrgName(directorOrgMap.get(item.getDirectorOrgId()));
            }
        }
    }


    @Override
    public BatchResult erpTransferApprove(FlowApprove reqVO){
        //结果
        BatchResult batchResult = new BatchResult();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<ErpTransferDO> list = erpTransferMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //查询物料仓库数据
        List<Long> transferIdList = list.stream().map(ErpTransferDO::getTransferId).collect(Collectors.toList());
        List<MaterialWarehouseBO> materialWarehouseList = erpTransferDetailMapper.transferMaterialWarehouseList(transferIdList);

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (ErpTransferDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //物料库存加锁
                for(MaterialWarehouseBO materialWarehouse : materialWarehouseList){
                    if(item.getTransferId().equals(materialWarehouse.getOrderId())){
                        //调出仓库
                        String outBizKey = RedisConstants.MATERIAL_STOCK + ":" + materialWarehouse.getMaterialId()
                                + "_"  + item.getTransferOutOrgId();
                        redisLockManager.acquireLock(outBizKey);
                        //调入仓库
                        String inBizKey = RedisConstants.MATERIAL_STOCK + ":" + materialWarehouse.getMaterialId()
                                + "_"  + item.getTransferInOrgId();
                        redisLockManager.acquireLock(inBizKey);
                    }
                }

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = erpTransferApproveHandler.process(item,flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            } catch (Exception exception){
                //异常捕捉
                exception.printStackTrace();
                FailItem failItem = new FailItem();
                failItem.setCode(item.getTransferCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }  finally {
                //物料库存锁释放
                for(MaterialWarehouseBO materialWarehouse : materialWarehouseList){
                    if(item.getTransferId().equals(materialWarehouse.getOrderId())){
                        //调出仓库
                        String outBizKey = RedisConstants.MATERIAL_STOCK + ":" + materialWarehouse.getMaterialId()
                                + "_"  + item.getTransferOutOrgId();
                        redisLockManager.releaseLock(outBizKey);
                        //调入仓库
                        String inBizKey = RedisConstants.MATERIAL_STOCK + ":" + materialWarehouse.getMaterialId()
                                + "_"  + item.getTransferInOrgId();
                        redisLockManager.releaseLock(inBizKey);
                    }
                }
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (ErpTransferDO item : list) {
                String reason = reasonMap.get(item.getTransferCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getTransferId());
                    messageInfoBO.setObjCode(item.getTransferCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getTransferId());
                    messageInfoBO.setObjCode(item.getTransferCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }


    @Override
    public Object erpTransferFlowCallback(FlowCallback reqVO){
        String objId = reqVO.getObjId();
        ErpTransferDO currentDO = this.erpTransferValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();
        return erpTransferFlowCallBackHandler.handleFlowCallback(currentDO, flowCallbackBO);
    }
}
