package com.mongoso.mgs.module.finance.handler.approve.invoice;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.module.finance.dal.db.invoice.invoiceconfig.InvoiceConfigDO;
import com.mongoso.mgs.module.finance.dal.mysql.invoice.invoiceconfig.InvoiceConfigMapper;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class InvoiceConfigApproveHandler extends FlowApproveHandler<InvoiceConfigDO> {

    @Resource
    private InvoiceConfigMapper invoiceConfigMapper;
    @Override
    protected ApproveCommonAttrs approvalAttributes(InvoiceConfigDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(InvoiceConfigDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(InvoiceConfigDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getInvoiceConfigId())
                .objCode(item.getCompanyName() + " " + item.getTaxpayerIdentifyNo())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(InvoiceConfigDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        // 具体业务校验逻辑
        //发票主体不涉及到上下游问题，因为不需要做业务逻辑校验
        //审核校验

        // 没有编码
        String code = item.getCompanyName() + " " + item.getTaxpayerIdentifyNo();

        if (buttonType == DataButtonEnum.APPROVE.getKey().intValue()) {

        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey().intValue()) {

        }

        return true;
    }

    @Override
    public Integer handleBusinessData(InvoiceConfigDO currentDO, BaseApproveRequest request) {
        //当前对象
        if (currentDO == null){
            return 1;
        }

        Long id = currentDO.getInvoiceConfigId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        FailItem failItem = request.getFailItem();

        //审核通过之后的逻辑，这里包括已审核合反审核
        //这里是发票主体，审核通过合反审核之类的不需要做其他处理，直接修改数据的状态

        return invoiceConfigMapper.update(new LambdaUpdateWrapper<InvoiceConfigDO>()
                        .set(InvoiceConfigDO::getDataStatus, dataStatus)
                        .set(InvoiceConfigDO::getApprovedDt, LocalDateTime.now())
                        .set(InvoiceConfigDO::getApprovedBy, WebFrameworkUtilX.getLoginUser().getFullUserName())
                        .eq(InvoiceConfigDO::getInvoiceConfigId, id)
        );
    }
}
