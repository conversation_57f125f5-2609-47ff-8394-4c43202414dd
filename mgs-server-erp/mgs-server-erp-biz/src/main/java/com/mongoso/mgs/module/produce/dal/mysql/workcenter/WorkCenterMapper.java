package com.mongoso.mgs.module.produce.dal.mysql.workcenter;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.produce.dal.db.workcenter.WorkCenterDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.produce.controller.admin.workcenter.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 工作中心 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkCenterMapper extends BaseMapperX<WorkCenterDO> {

    default PageResult<WorkCenterDO> selectPageOld(WorkCenterPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<WorkCenterDO>lambdaQueryX()
                .inIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getWorkCenterIdList())
                .notInIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getExclWorkCenterIdList())
                .likeIfPresent(WorkCenterDO::getWorkCenterCode, reqVO.getWorkCenterCode())
                .likeIfPresent(WorkCenterDO::getWorkCenterName, reqVO.getWorkCenterName())
                .eqIfPresent(WorkCenterDO::getWorkCenterTypeDictId, reqVO.getWorkCenterTypeDictId())
                .eqIfPresent(WorkCenterDO::getResourcesTypeDictId, reqVO.getResourcesTypeDictId())
                .betweenIfPresent(WorkCenterDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkCenterDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkCenterDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkCenterDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkCenterDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkCenterDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkCenterDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkCenterDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(WorkCenterDO::getCreatedDt));
    }



    default PageResult<WorkCenterDO> selectPage(WorkCenterPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<WorkCenterDO>lambdaQueryX()
                .likeIfPresent(WorkCenterDO::getWorkCenterCode, reqVO.getWorkCenterCode())
                .inIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getWorkCenterIdList())
                .notInIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getExclWorkCenterIdList())
                .likeIfPresent(WorkCenterDO::getWorkCenterName, reqVO.getWorkCenterName())
                .eqIfPresent(WorkCenterDO::getWorkCenterTypeDictId, reqVO.getWorkCenterTypeDictId())
                .eqIfPresent(WorkCenterDO::getResourcesTypeDictId, reqVO.getResourcesTypeDictId())
                .betweenIfPresent(WorkCenterDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkCenterDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkCenterDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkCenterDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkCenterDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkCenterDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkCenterDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkCenterDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(WorkCenterDO::getCreatedDt));
    }

    default List<WorkCenterDO> selectListOld(WorkCenterQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<WorkCenterDO>lambdaQueryX()
                .inIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getWorkCenterIdList())
                .notInIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getExclWorkCenterIdList())
                .likeIfPresent(WorkCenterDO::getWorkCenterCode, reqVO.getWorkCenterCode())
                .likeIfPresent(WorkCenterDO::getWorkCenterName, reqVO.getWorkCenterName())
                .eqIfPresent(WorkCenterDO::getWorkCenterTypeDictId, reqVO.getWorkCenterTypeDictId())
                .eqIfPresent(WorkCenterDO::getResourcesTypeDictId, reqVO.getResourcesTypeDictId())
                .betweenIfPresent(WorkCenterDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkCenterDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkCenterDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkCenterDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkCenterDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .inIfPresent(WorkCenterDO::getDirectorOrgId, reqVO.getDirectorOrgIdList())
                .betweenIfPresent(WorkCenterDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkCenterDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkCenterDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByAsc(WorkCenterDO::getProcessOrder).orderByDesc(WorkCenterDO::getCreatedDt)
        );
    }

    default List<WorkCenterDO> selectList(WorkCenterQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<WorkCenterDO>lambdaQueryX()
                .inIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getWorkCenterIdList())
                .notInIfPresent(WorkCenterDO::getWorkCenterId, reqVO.getExclWorkCenterIdList())
                .likeIfPresent(WorkCenterDO::getWorkCenterCode, reqVO.getWorkCenterCode())
                .likeIfPresent(WorkCenterDO::getWorkCenterName, reqVO.getWorkCenterName())
                .eqIfPresent(WorkCenterDO::getWorkCenterTypeDictId, reqVO.getWorkCenterTypeDictId())
                .eqIfPresent(WorkCenterDO::getResourcesTypeDictId, reqVO.getResourcesTypeDictId())
                .betweenIfPresent(WorkCenterDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkCenterDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkCenterDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkCenterDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkCenterDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkCenterDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkCenterDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(WorkCenterDO::getCreatedDt));
    }

    List<WorkCenterDO> selectListByUserId(@Param("userId") Long userId );

}