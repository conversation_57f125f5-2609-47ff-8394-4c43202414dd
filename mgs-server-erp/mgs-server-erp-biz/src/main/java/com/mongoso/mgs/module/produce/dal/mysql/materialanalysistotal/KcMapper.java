package com.mongoso.mgs.module.produce.dal.mysql.materialanalysistotal;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.KcDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物料分析数量汇总 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KcMapper extends BaseMapperX<KcDO> {


    default List<KcDO> selectByMaterialId(Long materialId){
        return selectList(LambdaQueryWrapperX.<KcDO>lambdaQueryX()
                .eq(KcDO::getMaterialId, materialId));
    }

}