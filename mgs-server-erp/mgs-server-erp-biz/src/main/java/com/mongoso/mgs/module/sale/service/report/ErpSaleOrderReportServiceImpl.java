package com.mongoso.mgs.module.sale.service.report;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.purchase.PurchaseBizTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.bo.ErpProdOrderRespBO;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorder.vo.ErpProdOrderQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysis.vo.MaterialAnalysisQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.bo.ProdWorkRespBO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkRespVO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysis.MaterialAnalysisDO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysis.MaterialAnalysisMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMaterialTotalMapper;
import com.mongoso.mgs.module.purchase.controller.admin.demand.bo.PurchaseDemandRespBO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseRespBO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderQueryReqVO;
import com.mongoso.mgs.module.purchase.dal.mysql.demand.PurchaseDemandMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.sale.controller.admin.report.vo.ErpSaleOrderReportPageReqVO;
import com.mongoso.mgs.module.sale.controller.admin.report.vo.ErpSaleOrderReportRespVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.dal.mysql.report.ErpSaleOrderReportMapper;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.ErpInboundQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.bo.ErpOutboundRespBO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.ErpOutboundQueryReqVO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhiling
 * @date: 2025/2/24 16:11
 * @description:
 */

@Service
@Validated
public class ErpSaleOrderReportServiceImpl implements ErpSaleOrderReportService {

    @Resource
    private ErpSaleOrderReportMapper erpSaleOrderReportMapper;

    @Resource
    private PurchaseDemandMapper purchaseDemandMapper;

    @Resource
    private PurchaseOrderMapper purchaseMapper;

    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Resource
    private ErpOutboundMapper erpOutboundMapper;

    @Resource
    private ErpInboundMapper erpInboundMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Resource
    private WorkPickingMaterialTotalMapper workPickingMaterialTotalMapper;


    @Resource
    private MaterialAnalysisMapper materialAnalysisMapper;


    @Override
    public PageResult<ErpSaleOrderReportRespVO> querySaleTrackReport(ErpSaleOrderReportPageReqVO reqVO) {

        IPage<ErpSaleOrderReportRespVO> respVOIPage = erpSaleOrderReportMapper.findErpSaleOrderDetail(PageUtilX.buildParam(reqVO), reqVO);
        PageResult<ErpSaleOrderReportRespVO> pageResult = PageUtilX.buildResult(respVOIPage);
        if (CollUtilX.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.SALES_ORDER_TYPE.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);
        for (ErpSaleOrderReportRespVO erpSaleOrderReportRespVO : pageResult.getList()) {
            erpSaleOrderReportRespVO.setSalesOrderTypeDictName(dictMap.get(CustomerDictEnum.SALES_ORDER_TYPE.getDictCode() + "-" +erpSaleOrderReportRespVO.getSalesOrderTypeDictId()));
        }

        //条件集合
        List<Long> saleOrderIds = new ArrayList<>();
        List<Long> erpProdIds = new ArrayList<>();

        //处理数据map
        Map<Long, ErpSaleOrderReportRespVO> saleRespMap = new HashMap<>();
        Map<Long, PurchaseDemandRespBO> purchaseDemandBOMap = new HashMap<>();
        Map<Long, ErpProdOrderRespBO> erpProdBOMap = new HashMap<>();
        Map<Long, ErpOutboundRespBO> erpOutboundBOMap = new HashMap<>();

        for (ErpSaleOrderReportRespVO respVO : pageResult.getList()) {
            saleOrderIds.add(respVO.getSaleOrderId());
            saleRespMap.put(respVO.getSaleOrderId(), respVO);
        }

        //查询出库单数据-销售订单维度统计
        if (CollUtilX.isNotEmpty(saleOrderIds)) {
            List<ErpOutboundRespBO> erpOutboundRespBOList = erpOutboundMapper.findErpOutboundQty(saleOrderIds);
            for (ErpOutboundRespBO outboundRespBO : erpOutboundRespBOList) {
                erpOutboundBOMap.put(outboundRespBO.getRelatedOrderId(), outboundRespBO);
            }
        }

        //采购进度查询-销售订单维度统计
        queryPurchaseProgress(saleOrderIds, purchaseDemandBOMap);

        //生产进度查询-生产订单维度统计
        queryErpProdProgress(saleOrderIds, erpProdIds, erpProdBOMap);

        //委外进度查询-生产订单维度统计
        queryOutProgress(erpProdIds, erpProdBOMap);

        //处理响应数据
        processResponseData(saleRespMap, erpOutboundBOMap, purchaseDemandBOMap, erpProdBOMap);


        return pageResult;
    }

    @Override
    public List<String> querySaleTrackReportDetailParameter(ErpSaleOrderReportPageReqVO reqVO) {
        Long saleOrderId = reqVO.getSaleOrderId();
        String progressQtyType = reqVO.getProgressQtyType();

        // 如果没有销售订单ID，直接返回空列表
        if (saleOrderId == null) {
            return Collections.emptyList();
        }
        return switch (progressQtyType) {
            case "0" -> // 采购进度已采购数量
                    getPurchaseOrderIds(saleOrderId);
            case "1" -> // 采购进度已入库数量
                    getInboundIdList(saleOrderId);
            case "2" -> // 生产进度计划生产数量
                    getProdIdList(saleOrderId);
            case "3", "4" -> // 生产进度已领料数量 出库单, 入库单
                    getPickingIds(saleOrderId, "3".equals(progressQtyType));
            case "5" -> // 生产进度已入库数量（包括直接关联和物料分析关联）
                    erpSaleOrderReportMapper.queryAllInboundIdByProdList(saleOrderId);
            case "6" -> // 委外进度计划委外采购数量
                    getOutsourcePurchaseOrderIds(saleOrderId);
            case "7" -> // 委外进度计划委外已入库数量
                    getOutsourceInboundIdList(saleOrderId);
            default -> Collections.emptyList();
        };
    }

    /**
     * 获取采购订单IDs
     */
    private List<String> getPurchaseOrderIds(Long saleOrderId) {
        ErpSaleOrderDO saleOrder = erpSaleOrderMapper.selectById(saleOrderId);
        if (saleOrder == null) {
            return Collections.emptyList();
        }

        // 根据是否可以发起销售采购决定查询方式
        if (saleOrder.getIsCanIssueSalePur() == 1) {
            return erpSaleOrderReportMapper.queryPurchaseOrderIdList(saleOrderId);
        } else {
            PurchaseOrderQueryReqVO queryReqVO = new PurchaseOrderQueryReqVO();
            queryReqVO.setRelatedOrderId(saleOrderId);
            queryReqVO.setDataStatus(1);
            return purchaseMapper.selectList(queryReqVO).stream()
                    .map(order -> String.valueOf(order.getPurchaseOrderId()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 获取采购订单IDs
     */
    private List<String> getInboundIdList(Long saleOrderId) {
        ErpSaleOrderDO saleOrder = erpSaleOrderMapper.selectById(saleOrderId);
        if (saleOrder.getIsCanIssueSalePur() == 1) {
            return erpSaleOrderReportMapper.queryInboundIdByDemandList(saleOrderId);
        } else {
            return erpSaleOrderReportMapper.queryInboundIdByPurchaseList(saleOrderId);
        }
    }

    /**
     * 获取生产订单id
     */
    private List<String> getProdIdList(Long saleOrderId) {
        // 1. 直接关联的生产订单
        ErpProdOrderQueryReqVO prodOrderQueryReqVO = new ErpProdOrderQueryReqVO();
        prodOrderQueryReqVO.setRelatedOrderId(saleOrderId);
        List<String> prodOrderIds = erpProdOrderMapper.selectList(prodOrderQueryReqVO).stream()
                .map(prod -> String.valueOf(prod.getProdOrderId()))
                .collect(Collectors.toList());

        // 2. 通过物料分析关联的生产订单
        // 查询销售订单关联的物料分析
        MaterialAnalysisQueryReqVO materialAnalysisQueryReqVO =new MaterialAnalysisQueryReqVO();
        materialAnalysisQueryReqVO.setRelatedOrderId(saleOrderId);
        materialAnalysisQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
        List<MaterialAnalysisDO> materialAnalysisList = materialAnalysisMapper.selectListOld(materialAnalysisQueryReqVO);
        if (CollUtilX.isNotEmpty(materialAnalysisList)) {
            List<Long> materialAnalysisIds = materialAnalysisList.stream()
                    .map(MaterialAnalysisDO::getMaterialAnalysisId)
                    .collect(Collectors.toList());

            // 查询物料分析关联的生产订单
            ErpProdOrderQueryReqVO maQueryReqVO = new ErpProdOrderQueryReqVO();
            maQueryReqVO.setRelatedOrderIdList(materialAnalysisIds);
            List<String> materialAnalysisProdIds = erpProdOrderMapper.selectList(maQueryReqVO).stream()
                    .map(prod -> String.valueOf(prod.getProdOrderId()))
                    .collect(Collectors.toList());

            // 合并结果
            if (CollUtilX.isNotEmpty(materialAnalysisProdIds)) {
                prodOrderIds.addAll(materialAnalysisProdIds);
                // 去重
                prodOrderIds = prodOrderIds.stream().distinct().collect(Collectors.toList());
            }
        }

        return prodOrderIds;
    }

    /**
     * 获取领料相关的出入库单IDs
     *
     * @param isOutbound true表示查询出库单，false表示查询入库单
     */
    private List<String> getPickingIds(Long saleOrderId, boolean isOutbound) {
        // 1. 获取生产工单列表（包括直接关联和物料分析关联的）
        List<ProdWorkRespVO> prodWorkList = erpSaleOrderReportMapper.queryAllProdWorkIdList(saleOrderId);
        if (CollUtilX.isEmpty(prodWorkList)) {
            return Collections.emptyList();
        }

        List<String> resultIds = new ArrayList<>();

        if (isOutbound) {
            // 处理出库单 - 使用outProcessConfigDictId
            Map<Integer, List<Long>> prodWorkIdMap = prodWorkList.stream()
                    .collect(Collectors.groupingBy(
                            ProdWorkRespVO::getOutProcessConfigDictId,
                            Collectors.mapping(ProdWorkRespVO::getProdWorkId, Collectors.toList())
                    ));

            // 处理直接领料工单 (outProcessConfigDictId = 0)
            List<Long> directProdWorkIds = prodWorkIdMap.getOrDefault(0, Collections.emptyList());
            if (CollUtilX.isNotEmpty(directProdWorkIds)) {
                ErpOutboundQueryReqVO outReqVO = new ErpOutboundQueryReqVO();
                outReqVO.setRelatedOrderIdList(directProdWorkIds);
                outReqVO.setOutboundTypeDictId("10");
                resultIds.addAll(erpOutboundMapper.selectList(outReqVO).stream()
                        .map(out -> String.valueOf(out.getOutboundId()))
                        .toList());
            }

            // 处理标准领料工单 (outProcessConfigDictId = 1)
            List<Long> standardProdWorkIds = prodWorkIdMap.getOrDefault(1, Collections.emptyList());
            if (CollUtilX.isNotEmpty(standardProdWorkIds)) {
                resultIds.addAll(erpSaleOrderReportMapper.queryOutboundIdList(standardProdWorkIds));
            }
        } else {
            // 处理入库单 - 使用inProcessConfigDictId
            Map<Integer, List<Long>> prodWorkIdMap = prodWorkList.stream()
                    .collect(Collectors.groupingBy(
                            ProdWorkRespVO::getInProcessConfigDictId,
                            Collectors.mapping(ProdWorkRespVO::getProdWorkId, Collectors.toList())
                    ));

            // 处理直接领料工单 (inProcessConfigDictId = 0)
            List<Long> directProdWorkIds = prodWorkIdMap.getOrDefault(0, Collections.emptyList());
            if (CollUtilX.isNotEmpty(directProdWorkIds)) {
                ErpInboundQueryReqVO inReqVO = new ErpInboundQueryReqVO();
                inReqVO.setRelatedOrderIdList(directProdWorkIds);
                inReqVO.setInboundTypeDictId("8");
                resultIds.addAll(erpInboundMapper.selectList(inReqVO).stream()
                        .map(in -> String.valueOf(in.getInboundId()))
                        .toList());
            }

            // 处理标准领料工单 (inProcessConfigDictId = 1)
            List<Long> standardProdWorkIds = prodWorkIdMap.getOrDefault(1, Collections.emptyList());
            if (CollUtilX.isNotEmpty(standardProdWorkIds)) {
                resultIds.addAll(erpSaleOrderReportMapper.queryInboundIdList(standardProdWorkIds));
            }
        }

        return resultIds;
    }

    /**
     * 获取委外采购订单IDs（包括直接关联和物料分析关联）
     */
    private List<String> getOutsourcePurchaseOrderIds(Long saleOrderId) {
        return erpSaleOrderReportMapper.queryAllPurchaseIdByOutsourceList(saleOrderId);
    }

    /**
     * 获取生产委外采购订单IDs（包括直接关联和物料分析关联）
     */
    private List<String> getOutsourceInboundIdList(Long saleOrderId) {
        return erpSaleOrderReportMapper.queryAllInboundIdByOutsourceList(saleOrderId);
    }

    /**
     * 处理响应数据
     *
     * @param saleRespMap
     * @param erpOutboundBOMap
     * @param purchaseDemandBOMap
     * @param erpProdBOMap
     */
    private void processResponseData(Map<Long, ErpSaleOrderReportRespVO> saleRespMap, Map<Long, ErpOutboundRespBO> erpOutboundBOMap,
                                     Map<Long, PurchaseDemandRespBO> purchaseDemandBOMap, Map<Long, ErpProdOrderRespBO> erpProdBOMap) {

        BigDecimal planDemandQty = BigDecimal.ZERO;
        BigDecimal purchasedQty = BigDecimal.ZERO;
        BigDecimal purchaseInboundedQty = BigDecimal.ZERO;


        BigDecimal prodPlanTotalQty = BigDecimal.ZERO;
        BigDecimal prodActTotalQty = BigDecimal.ZERO;
        BigDecimal estimatedQty = BigDecimal.ZERO;
        BigDecimal receivedQty = BigDecimal.ZERO;
        BigDecimal receiveQty = BigDecimal.ZERO;
        BigDecimal estimateInboundQty = BigDecimal.ZERO;
        BigDecimal prodInboundedQty = BigDecimal.ZERO;
        BigDecimal prodInboundableQty = BigDecimal.ZERO;
        BigDecimal outPlanDemandQty = BigDecimal.ZERO;
        BigDecimal outInboundedQty = BigDecimal.ZERO;
        BigDecimal outInboundableQty = BigDecimal.ZERO;

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(SystemDictEnum.SALE_REPORT_OUTBOUND_STATUS.getDictCode(), SystemDictEnum.SALE_REPORT_INTBOUND_STATUS.getDictCode(),
                SystemDictEnum.SALE_REPORT_PROD_FORM_STATUS.getDictCode(), SystemDictEnum.SALE_REPORT_PICKING_FORM_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        for (Map.Entry<Long, ErpSaleOrderReportRespVO> entry : saleRespMap.entrySet()) {
            Long saleOrderId = entry.getKey();
            ErpSaleOrderReportRespVO respVO = entry.getValue();
            ErpOutboundRespBO outboundRespBO = erpOutboundBOMap.get(saleOrderId);

            if (outboundRespBO != null) {
                respVO.setDeliveredQty(outboundRespBO.getDeliveredQty());
            }

            planDemandQty = BigDecimal.ZERO;
            purchasedQty = BigDecimal.ZERO;
            purchaseInboundedQty = BigDecimal.ZERO;
            for (Map.Entry<Long, PurchaseDemandRespBO> purchaseDemandResp : purchaseDemandBOMap.entrySet()) {
                PurchaseDemandRespBO purchaseDemandBO = purchaseDemandResp.getValue();
                if (purchaseDemandBO.getSaleOrderId() != null && purchaseDemandBO.getSaleOrderId().equals(saleOrderId)) {
                    planDemandQty = planDemandQty.add(purchaseDemandBO.getPlanDemandQty());
                    purchasedQty = purchasedQty.add(purchaseDemandBO.getPurchasedQty());
                    purchaseInboundedQty = purchaseInboundedQty.add(purchaseDemandBO.getPurchaseInboundedQty());
                }
            }

            respVO.setPurchaseDemandQty(planDemandQty);
            respVO.setPurchasedQty(purchasedQty);
            respVO.setPurchaseInboundedQty(purchaseInboundedQty);

            prodPlanTotalQty = BigDecimal.ZERO;
            prodActTotalQty = BigDecimal.ZERO;
            estimatedQty = BigDecimal.ZERO;
            receivedQty = BigDecimal.ZERO;
            receiveQty = BigDecimal.ZERO;
            estimateInboundQty = BigDecimal.ZERO;
            prodInboundedQty = BigDecimal.ZERO;
            prodInboundableQty = BigDecimal.ZERO;
            outPlanDemandQty = BigDecimal.ZERO;
            outInboundedQty = BigDecimal.ZERO;
            outInboundableQty = BigDecimal.ZERO;

            StringBuilder builderFormStatus = new StringBuilder("");
            StringBuilder builderIsTakeMaterial = new StringBuilder("");

            for (Map.Entry<Long, ErpProdOrderRespBO> prodOrderResp : erpProdBOMap.entrySet()) {
                ErpProdOrderRespBO prodOrderRespBO = prodOrderResp.getValue();

                if (prodOrderRespBO.getSourceFormId() != null && prodOrderRespBO.getSourceFormId().equals(saleOrderId)) {
                    prodPlanTotalQty = prodPlanTotalQty.add(prodOrderRespBO.getProdPlanTotalQty());
                    prodActTotalQty = prodActTotalQty.add(prodOrderRespBO.getProdActTotalQty());
                    estimatedQty = estimatedQty.add(prodOrderRespBO.getEstimatedQty());
                    receivedQty = receivedQty.add(prodOrderRespBO.getReceivedQty());
                    receiveQty = receiveQty.add(prodOrderRespBO.getReceiveableQty());
                    estimateInboundQty = estimateInboundQty.add(prodOrderRespBO.getEstimateInboundQty());
                    prodInboundedQty = prodInboundedQty.add(prodOrderRespBO.getProdInboundedQty());
                    prodInboundableQty = prodInboundableQty.add(prodOrderRespBO.getProdInboundableQty());

                    outPlanDemandQty = outPlanDemandQty.add(prodOrderRespBO.getPurchasedQty());
                    outInboundedQty = outInboundedQty.add(prodOrderRespBO.getPurInboundedQty());
                    outInboundableQty = outInboundableQty.add(prodOrderRespBO.getOutInboundableQty());

                    builderFormStatus.append(prodOrderRespBO.getFormStatus());
                    builderIsTakeMaterial.append(prodOrderRespBO.getIsTakeMaterial());

                }
            }

            //生产
            respVO.setProdPlanTotalQty(prodPlanTotalQty);
            respVO.setProdActTotalQty(prodActTotalQty);
            respVO.setEstimatedQty(estimatedQty);
            respVO.setReceivedQty(receivedQty);
            respVO.setReceiveQty(receiveQty);
            respVO.setEstimateInboundQty(estimateInboundQty);
            respVO.setProdInboundedQty(prodInboundedQty);
            respVO.setProdInboundableQty(prodInboundableQty);

            //委外
            respVO.setOutPlanDemandQty(outPlanDemandQty);
            respVO.setOutInboundedQty(outInboundedQty);
            respVO.setOutInboundableQty(outInboundableQty);

            //完工状态
            Integer prodFormStatus = checkFormStatus(builderFormStatus.toString());
            respVO.setProdFormStatus(prodFormStatus);
            String prodFormStatusStr = SystemDictEnum.SALE_REPORT_PROD_FORM_STATUS.getDictCode() + "-" + prodFormStatus;
            respVO.setProdFormStatusDictName(dictMap.get(prodFormStatusStr));

            //是否带料
            Integer isTakeMaterial = checkIsTakeMaterial(builderIsTakeMaterial.toString());
            respVO.setIsTakeMaterial(isTakeMaterial);

            //销售出库状态
            String outboundStatusStr = SystemDictEnum.SALE_REPORT_OUTBOUND_STATUS.getDictCode() + "-" + respVO.getOutboundStatus();
            respVO.setOutboundStatusDictName(dictMap.get(outboundStatusStr));

            //采购入库状态
            Integer purchaseInboundStatus = MathUtilX.getStatus(respVO.getPurchaseInboundedQty(), respVO.getPurchasedQty());
            respVO.setPurchaseInboundStatus(purchaseInboundStatus);
            String purchaseInboundStatusStr = SystemDictEnum.SALE_REPORT_INTBOUND_STATUS.getDictCode() + "-" + purchaseInboundStatus;
            respVO.setPurchaseInboundStatusDictName(dictMap.get(purchaseInboundStatusStr));

            //生产领料状态
            Integer pickingFormStatus = MathUtilX.getStatus(respVO.getReceivedQty(), respVO.getEstimatedQty());
            if (estimatedQty.compareTo(BigDecimal.ZERO) == 0) {
                pickingFormStatus = 3;
            }
            respVO.setPickingFormStatus(pickingFormStatus);
            String pickingFormStatusStr = SystemDictEnum.SALE_REPORT_PICKING_FORM_STATUS.getDictCode() + "-" + pickingFormStatus;
            respVO.setPickingFormStatusDictName(dictMap.get(pickingFormStatusStr));

            //生产入库状态
            Integer prodInboundStatus = MathUtilX.getStatus(respVO.getProdInboundedQty(), respVO.getEstimateInboundQty());
            respVO.setProdInboundStatus(prodInboundStatus);
            String prodInboundStatusStr = SystemDictEnum.SALE_REPORT_INTBOUND_STATUS.getDictCode() + "-" + prodInboundStatus;
            respVO.setProdInboundStatusDictName(dictMap.get(prodInboundStatusStr));

            //委外入库状态
            Integer outInboundStatus = MathUtilX.getStatus(respVO.getOutInboundedQty(), respVO.getOutPlanDemandQty());
            respVO.setOutInboundStatus(outInboundStatus);
            String outInboundStatusStr = SystemDictEnum.SALE_REPORT_INTBOUND_STATUS.getDictCode() + "-" + outInboundStatus;
            respVO.setOutInboundStatusDictName(dictMap.get(outInboundStatusStr));
        }
    }

    /**
     * 采购进度查询-销售订单维度统计
     *
     * @param saleOrderIds
     * @param purchaseDemandBOMap
     */
    private void queryPurchaseProgress(List<Long> saleOrderIds, Map<Long, PurchaseDemandRespBO> purchaseDemandBOMap) {
        if (CollUtilX.isEmpty(saleOrderIds)) {
            return;
        }

        List<Long> purchaseDemandIds = new ArrayList<>();

        // 查询销售采购需求单
        Integer purchaseDemandBizType = 1;
        List<PurchaseDemandRespBO> purchaseDemandBOList = purchaseDemandMapper.findPurchaseDemandQty(saleOrderIds, purchaseDemandBizType);
        for (PurchaseDemandRespBO demandRespBO : purchaseDemandBOList) {
            purchaseDemandIds.add(demandRespBO.getPurchaseDemandId());
            purchaseDemandBOMap.put(demandRespBO.getPurchaseDemandId(), demandRespBO);
        }

        //查询需求采购订单
        Integer purchaseOrderBizType = 1;
        if (CollUtilX.isNotEmpty(purchaseDemandIds)) {
            List<PurchaseRespBO> purchaseRespBOList = purchaseMapper.findPurchaseQty(purchaseDemandIds, purchaseOrderBizType, null);
            for (PurchaseRespBO purchaseRespBO : purchaseRespBOList) {
                PurchaseDemandRespBO purchaseDemandRespBO = purchaseDemandBOMap.get(purchaseRespBO.getRelatedOrderId());
                if (purchaseDemandRespBO != null) {
                    purchaseDemandRespBO.setPurchaseInboundedQty(purchaseRespBO.getInboundedQty());
                }
            }
        }


        //查询销售采购订单
        purchaseOrderBizType = 5;
        if (CollUtilX.isNotEmpty(saleOrderIds)) {
            List<PurchaseRespBO> purchaseRespBOList = purchaseMapper.findPurchaseQty(saleOrderIds, purchaseOrderBizType, null);
            for (PurchaseRespBO purchaseRespBO : purchaseRespBOList) {
                PurchaseDemandRespBO purchaseDemandRespBO = new PurchaseDemandRespBO();
                purchaseDemandRespBO.setSaleOrderId(purchaseRespBO.getRelatedOrderId());
                purchaseDemandRespBO.setPurchasedQty(purchaseRespBO.getPurchasedQty());
                purchaseDemandRespBO.setPurchaseInboundedQty(purchaseRespBO.getInboundedQty());
                purchaseDemandBOMap.put(purchaseRespBO.getRelatedOrderId(), purchaseDemandRespBO);
            }
        }
    }


    /**
     * 生产进度查询-生产订单维度统计
     *
     * @param saleOrderIds
     * @param erpProdIds
     * @param erpProdBOMap
     */
    private void queryErpProdProgress(List<Long> saleOrderIds, List<Long> erpProdIds, Map<Long, ErpProdOrderRespBO> erpProdBOMap) {
        if (CollUtilX.isEmpty(saleOrderIds)) {
            return;
        }

        // 查询生产订单
        List<ErpProdOrderRespBO> erpProdOrderRespBOList = erpProdOrderMapper.findErpProdOrderQty(saleOrderIds);
        for (ErpProdOrderRespBO erpProdOrderRespBO : erpProdOrderRespBOList) {
            erpProdIds.add(erpProdOrderRespBO.getProdOrderId());
            erpProdBOMap.put(erpProdOrderRespBO.getProdOrderId(), erpProdOrderRespBO);
        }

        // 查询物料分析生产订单
        MaterialAnalysisQueryReqVO materialAnalysisQueryReqVO =new MaterialAnalysisQueryReqVO();
        materialAnalysisQueryReqVO.setRelatedOrderIdList(saleOrderIds);
        materialAnalysisQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
        List<MaterialAnalysisDO> materialAnalysisList = materialAnalysisMapper.selectListOld(materialAnalysisQueryReqVO);
        if (CollUtilX.isNotEmpty(materialAnalysisList)) {
            List<Long> materialAnalysisIds = materialAnalysisList.stream()
                    .map(MaterialAnalysisDO::getMaterialAnalysisId)
                    .collect(Collectors.toList());

            // 查询物料分析关联的生产订单
            List<ErpProdOrderRespBO> materialAnalysisProdOrders = erpProdOrderMapper.findErpProdOrderQty(materialAnalysisIds);
            for (ErpProdOrderRespBO prodOrderRespBO : materialAnalysisProdOrders) {
                if (!erpProdBOMap.containsKey(prodOrderRespBO.getProdOrderId())) {
                    erpProdIds.add(prodOrderRespBO.getProdOrderId());
                    erpProdBOMap.put(prodOrderRespBO.getProdOrderId(), prodOrderRespBO);
                }
            }
        }

        //查询生产工单
        if (CollUtilX.isEmpty(erpProdIds)) {
            return;
        }

        BigDecimal estimatedQty = BigDecimal.ZERO;
        BigDecimal receivedQty = BigDecimal.ZERO;
        BigDecimal receiveQty = BigDecimal.ZERO;
        BigDecimal estimateInboundQty = BigDecimal.ZERO;
        BigDecimal prodInboundedQty = BigDecimal.ZERO;
        BigDecimal prodInboundableQty = BigDecimal.ZERO;
        List<ProdWorkRespBO> prodWorkRespBOList = prodWorkMapper.findProdWorkQtyByProdId(erpProdIds);

        //查询领料单
        List<DocumentRespBO> outboundedList = workPickingMaterialTotalMapper.outboundedQtyByRelatedOrderIdList(erpProdIds);
        Map<Long, BigDecimal> workPickMap = outboundedList.stream()
            .collect(Collectors.groupingBy(
                DocumentRespBO::getRelatedOrderId,
                Collectors.reducing(
                    BigDecimal.ZERO,
                    doc -> Optional.ofNullable(doc.getOutboundedQty()).orElse(BigDecimal.ZERO),
                    BigDecimal::add
                )
            ));

        //查询领料退料单
        List<DocumentRespBO> inboundedList = workPickingMaterialTotalMapper.inboundedQtyByRelatedOrderIdList(erpProdIds);
        Map<Long, BigDecimal> workReturnMap = inboundedList.stream()
                .collect(Collectors.groupingBy(
                        DocumentRespBO::getRelatedOrderId,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                doc -> Optional.ofNullable(doc.getInboundedQty()).orElse(BigDecimal.ZERO),
                                BigDecimal::add
                        )
                ));

        for (ProdWorkRespBO workRespBO : prodWorkRespBOList) {
            ErpProdOrderRespBO prodOrderRespBO = erpProdBOMap.get(workRespBO.getProdOrderId());
            if (prodOrderRespBO != null) {
                //领料数量
                BigDecimal pickQty = workPickMap.get(workRespBO.getProdOrderId());
                pickQty = pickQty == null ? BigDecimal.ZERO : pickQty;

                //退料数量
                BigDecimal pickReturnQty = EntityUtilX.getBigDecDefault(workReturnMap.get(workRespBO.getProdOrderId()));
                //预估用量
                estimatedQty = EntityUtilX.getBigDecDefault(workRespBO.getEstimatedQty());
                //已领数量
                receivedQty = pickQty.subtract(pickReturnQty);
                //待领料数量
                receiveQty = estimatedQty.subtract(receivedQty);
                //预估入库数量
                estimateInboundQty = workRespBO.getEstimateInboundQty();
                //已入库数量
                prodInboundedQty = workRespBO.getInboundedQty();
                //待入库数量
                prodInboundableQty = estimateInboundQty.subtract(prodInboundedQty);

                prodOrderRespBO.setEstimatedQty(estimatedQty);
                prodOrderRespBO.setReceivedQty(receivedQty);
                prodOrderRespBO.setReceiveableQty(receiveQty);
                prodOrderRespBO.setEstimateInboundQty(estimateInboundQty);
                prodOrderRespBO.setProdInboundedQty(prodInboundedQty);
                prodOrderRespBO.setProdInboundableQty(prodInboundableQty);
            }
        }
    }

    /**
     * 委外进度查询-生产订单维度统计
     *
     * @param erpProdIds
     * @param erpProdBOMap
     */
    private void queryOutProgress(List<Long> erpProdIds, Map<Long, ErpProdOrderRespBO> erpProdBOMap) {
        // 查询生产委外采购订单
        List<Integer> purchaseOrderBizTypeList = new ArrayList<>();
        purchaseOrderBizTypeList.add(3);
        purchaseOrderBizTypeList.add(4);

        BigDecimal outInboundableQty = BigDecimal.ONE;
        if (CollUtilX.isNotEmpty(erpProdIds)) {
            List<PurchaseRespBO> purchaseRespBOList = purchaseMapper.findPurchaseQty(erpProdIds, null, purchaseOrderBizTypeList);
            for (PurchaseRespBO purchaseRespBO : purchaseRespBOList) {
                ErpProdOrderRespBO prodOrderRespBO = erpProdBOMap.get(purchaseRespBO.getRelatedOrderId());
                if (prodOrderRespBO != null) {
                    outInboundableQty = purchaseRespBO.getPurchasedQty().subtract(purchaseRespBO.getInboundedQty());
                    //已采购数量
                    prodOrderRespBO.setPurchasedQty(purchaseRespBO.getPurchasedQty());
                    //采购已入库数量
                    prodOrderRespBO.setPurInboundedQty(purchaseRespBO.getInboundedQty());
                    //是否带料
                    prodOrderRespBO.setIsTakeMaterial(purchaseRespBO.getIsTakeMaterial());
                    //待入库数量
                    prodOrderRespBO.setOutInboundableQty(outInboundableQty);
                    //源头单据id

//                    prodOrderRespBO.setSourceFormId(purchaseRespBO.getSourceFormId());//xs fx
                }
            }
        }
    }

    public Integer checkFormStatus(String str) {
        if (StrUtilX.isEmpty(str)) {
            return 0;
        }

        // 检查是否包含0或1
        if (str.matches(".*[01].*")) {
            return 0; // 包含0或1，未完工
        }

        // 检查是否全是2和3
        if (str.matches("[23]+")) {
            return 1; // 全是2和3，已完工
        }

        // 检查是否全是2、3或4，并且不包含0或1
        if (str.matches("[234]+")) {
            if (str.matches("4+")) {
                return 3; // 全是4，已关闭
            }
            return 2; // 部分完工
        }

        // 不满足任何条件
        return -1;
    }

    public Integer checkIsTakeMaterial(String str) {
        if (StrUtilX.isEmpty(str)) {
            return 0;
        }
        //带料
        boolean zero = str.matches("[0]+");
        //不带料
        boolean one = str.matches("[1]+");
        if (zero) {
            return 0;
        } else if (one) {
            return 1;
        } else {
            return 1;
        }
    }

}
