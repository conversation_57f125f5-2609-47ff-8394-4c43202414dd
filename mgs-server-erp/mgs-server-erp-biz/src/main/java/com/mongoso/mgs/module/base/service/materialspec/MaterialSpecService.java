package com.mongoso.mgs.module.base.service.materialspec;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.base.controller.admin.materialspec.vo.*;
import com.mongoso.mgs.module.base.dal.db.materialspec.MaterialSpecDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 物料规格属性 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialSpecService {

    /**
     * 创建物料规格属性
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long materialSpecAdd(@Valid MaterialSpecAditReqVO reqVO);

    /**
     * 更新物料规格属性
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long materialSpecEdit(@Valid MaterialSpecAditReqVO reqVO);

    /**
     * 删除物料规格属性
     *
     * @param materialSpecId 编号
     */
    void materialSpecDelete(Long materialSpecId);

    /**
     * 获得物料规格属性信息
     *
     * @param materialSpecId 编号
     * @return 物料规格属性信息
     */
    MaterialSpecRespVO materialSpecDetail(Long materialSpecId);

    /**
     * 获得物料规格属性列表
     *
     * @param reqVO 查询条件
     * @return 物料规格属性列表
     */
    List<MaterialSpecRespVO> materialSpecList(@Valid MaterialSpecQueryReqVO reqVO);

    /**
     * 获得物料规格属性分页
     *
     * @param reqVO 查询条件
     * @return 物料规格属性分页
     */
    PageResult<MaterialSpecRespVO> materialSpecPage(@Valid MaterialSpecPageReqVO reqVO);

}
