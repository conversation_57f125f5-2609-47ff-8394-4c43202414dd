package com.mongoso.mgs.module.purchase.dal.db.purprocessoutdeduction;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 工序委外采购扣费单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_pur_process_out_deduction", autoResultMap = true)
//@KeySequence("u_pur_process_out_deduction_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurProcessOutDeductionDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long processOutDeductionId;

    /** 工序委外采购扣费单号 */
    private String processOutDeductionCode;

    /** 工序委外采购订单ID */
    private Long purchaseProcessOutId;

    /** 工序委外采购订单code */
    private String purchaseProcessOutCode;

    /** 关联供应商 */
    private Long relatedSupplierId;

    /** 联系人名称 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;

    /** 交货日期 */
    private LocalDate deliveryDate;

    /** 退货日期 */
    private LocalDate returnDate;

    /** 币种 */
    private String currencyDictId;

    /** 主体公司 */
    private String companyOrgId;

    /** 退款条件 */
    private String refundConditionDictId;

    /** 结算方式 */
    private String settlementMethodDictId;

    /** 扣费总金额(不含税) */
    private BigDecimal exclTaxTotalAmt;

    /** 扣费总金额(含税) */
    private BigDecimal inclTaxTotalAmt;

    /** 票据类型 */
    private Long invoiceTypeId;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    private LocalDateTime approvedDt;

    /** 版本号 */
    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币订单总金额 */
    private BigDecimal inclTaxLocalCurrencyTotalAmt;
}
