package com.mongoso.mgs.module.comp.payroll.dal.mysql.payroll;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payroll.vo.PayrollPageReqVO;
import com.mongoso.mgs.module.comp.payroll.controller.admin.payroll.vo.PayrollQueryReqVO;
import com.mongoso.mgs.module.comp.payroll.dal.db.payroll.PayrollDO;
import com.mongoso.mgs.module.comp.payroll.dal.db.payrollpayment.PayrollPaymentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 工资单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PayrollMapper extends BaseMapperX<PayrollDO> {

    default PageResult<PayrollDO> selectPage(PayrollPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<PayrollDO>lambdaQueryX()
                .likeIfPresent(PayrollDO:: getPayrollCode, reqVO.getPayrollCode())
                .likeIfPresent(PayrollDO:: getPayrollName, reqVO.getPayrollName())
                .eqIfPresent(PayrollDO:: getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(PayrollDO:: getPayrollMonth, reqVO.getPayrollMonth())
                .eqIfPresent(PayrollDO:: getPayrollStatus, reqVO.getPayrollStatus())
                .eqIfPresent(PayrollDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PayrollDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(PayrollDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(PayrollDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .orderByDesc(PayrollDO::getCreatedDt));
    }

    default List<PayrollDO> selectList(PayrollQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<PayrollDO>lambdaQueryX()
                .likeIfPresent(PayrollDO:: getPayrollCode, reqVO.getPayrollCode())
                .likeIfPresent(PayrollDO:: getPayrollName, reqVO.getPayrollName())
                .eqIfPresent(PayrollDO:: getCompanyOrgId, reqVO.getCompanyOrgId())
                .eqIfPresent(PayrollDO:: getPayrollMonth, reqVO.getPayrollMonth())
                .eqIfPresent(PayrollDO:: getPayrollStatus, reqVO.getPayrollStatus())
                .eqIfPresent(PayrollDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PayrollDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .eqIfPresent(PayrollDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(PayrollDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .orderByDesc(PayrollDO::getCreatedDt));
    }

    default Long selectCount(String orgId, String payrollMonth) {
        return selectCount(LambdaQueryWrapperX.<PayrollDO>lambdaQueryX()
                .eqIfPresent(PayrollDO::getCompanyOrgId, orgId)
                .eqIfPresent(PayrollDO::getPayrollMonth, payrollMonth));
    }

    default PayrollDO selectLastPayroll(String companyOrgId) {
        return selectOne(LambdaQueryWrapperX.<PayrollDO>lambdaQueryX()
                .eq(PayrollDO::getCompanyOrgId, companyOrgId)
                .last("LIMIT 1"));
    }


    default PayrollDO selectLastPayroll(String companyOrgId, LocalDate calcMonth) {
        return selectOne(LambdaQueryWrapperX.<PayrollDO>lambdaQueryX()
                .eq(PayrollDO:: getCompanyOrgId, companyOrgId));
    }

    void updatePayrollStat(@Param("payrollId") Long payrollId);
}
