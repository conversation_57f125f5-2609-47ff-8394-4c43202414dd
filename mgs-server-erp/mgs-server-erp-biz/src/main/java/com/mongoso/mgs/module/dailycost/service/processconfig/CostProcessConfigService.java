package com.mongoso.mgs.module.dailycost.service.processconfig;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.dailycost.controller.admin.processconfig.vo.CostProcessConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.processconfig.vo.CostProcessConfigPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.processconfig.vo.CostProcessConfigQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.processconfig.vo.CostProcessConfigRespVO;
import com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 工序配置 Service 接口
 *
 * <AUTHOR>
 */
public interface CostProcessConfigService {

    /**
     * 创建工序配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costProcessConfigAdd(@Valid CostProcessConfigAditReqVO reqVO);

    /**
     * 更新工序配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
//    Long costProcessConfigEdit(@Valid CostProcessConfigAditReqVO reqVO);

    /**
     * 删除工序配置
     *
     * @param processConfigId 编号
     */
    void costProcessConfigDel(Long processConfigId);

    /**
     * 获得工序配置信息
     *
     * @param processConfigId 编号
     * @return 工序配置信息
     */
    CostProcessConfigRespVO costProcessConfigDetail(Long processConfigId);

    /**
     * 获得工序配置列表
     *
     * @param reqVO 查询条件
     * @return 工序配置列表
     */
    List<CostProcessConfigRespVO> costProcessConfigList(@Valid CostProcessConfigQueryReqVO reqVO);

    /**
     * 获得工序配置分页
     *
     * @param reqVO 查询条件
     * @return 工序配置分页
     */
    PageResult<CostProcessConfigRespVO> costProcessConfigPage(@Valid CostProcessConfigPageReqVO reqVO);

    /**
     * 批量删除采购订单
     * @param reqVO
     * @return
     */
    ResultX<BatchResult> costProcessConfigDelBatch(IdReq reqVO);

    PageResult<ProcessRespVO> processPage(ProcessPageReqVO reqVO);
}
