package com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailPageReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.bo.MaterialWarehouseBO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 入库单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpInboundDetailMapper extends BaseMapperX<ErpInboundDetailDO> {

    default PageResult<ErpInboundDetailDO> selectPage(ErpInboundDetailPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ErpInboundDetailDO>lambdaQueryX()
                .eqIfPresent(ErpInboundDetailDO::getInboundId, reqVO.getInboundId())
                .likeIfPresent(ErpInboundDetailDO::getInboundCode, reqVO.getInboundCode())
                .eqIfPresent(ErpInboundDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(ErpInboundDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpInboundDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(ErpInboundDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(ErpInboundDetailDO::getRemark, reqVO.getRemark())
                        .orderByDesc(ErpInboundDetailDO::getCreatedDt));
    }

    default List<ErpInboundDetailDO> selectList(ErpInboundDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ErpInboundDetailDO>lambdaQueryX()
                .eqIfPresent(ErpInboundDetailDO::getInboundId, reqVO.getInboundId())
                .likeIfPresent(ErpInboundDetailDO::getInboundCode, reqVO.getInboundCode())
                .eqIfPresent(ErpInboundDetailDO::getMaterialId, reqVO.getMaterialId())
                .likeIfPresent(ErpInboundDetailDO::getMaterialCode, reqVO.getMaterialCode())
                .eqIfPresent(ErpInboundDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(ErpInboundDetailDO::getWarehouseOrgId, reqVO.getWarehouseOrgId())
                .eqIfPresent(ErpInboundDetailDO::getRemark, reqVO.getRemark())
                        .orderByDesc(ErpInboundDetailDO::getCreatedDt));
    }

    default int deleteByInboundId(Long inboundId){
        return delete(LambdaQueryWrapperX.<ErpInboundDetailDO>lambdaQueryX()
                .eq(ErpInboundDetailDO:: getInboundId, inboundId)
        );
    }

    IPage<ErpInboundDetailRespVO> queryInboundDetailPage(Page<ErpInboundDetailPageReqVO> page,
                                                         @Param("reqVO") ErpInboundDetailPageReqVO reqVO);

    List<ErpInboundDetailRespVO> queryInboundDetailList(@Param("reqVO") ErpInboundDetailQueryReqVO reqVO);

    List<DocumentRespBO> inboundQtyList(@Param("inboundId") Long inboundId);

    List<DocumentRespBO> materialInboundQtyList(@Param("inboundId") Long inboundId);

    List<DocumentRespBO> inboundQtyDetailList(@Param("inboundId") Long inboundId);

    List<MaterialWarehouseBO> inboundMaterialWarehouseList(@Param("inboundIdList") List<Long> inboundIdList);

    List<ErpInboundDetailDO> selectListBySourceOrderCode(@Param("sourceOrderCode") String sourceOrderCode);
}