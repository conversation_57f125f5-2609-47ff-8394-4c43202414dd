package com.mongoso.mgs.module.produce.controller.admin.workpicking.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
 import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 工单领料单 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class WorkPickingQueryReqVO extends CommonParam{

    /** 主键ID */
    private Long workPickingId;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startFormDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endFormDt;

    /** 领料单号 */
    private String workPickingCode;

    /** 领料单类型 */
    private String workPickingTypeDictId;

    /** 工单领料业务类型 */
    private Integer workPickingBizType;

    /** 关联单据id */
    private Long relatedOrderId;

    /** 关联单据号 */
    private String relatedOrderCode;

    /** 物料ID */
    private Long materialId;

    /** 行号 */
    private Integer rowNo;

    /** 是否完全出库 */
    private Integer isFullOutbounded;

    /** 责任人 */
    private Long directorId;

    /** 审核状态 */
    private Integer dataStatus;

    /** 备注 */
    private String remark;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 审批人 */
    private String approvedBy;

    /** 审批时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startApprovedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endApprovedDt;

}
