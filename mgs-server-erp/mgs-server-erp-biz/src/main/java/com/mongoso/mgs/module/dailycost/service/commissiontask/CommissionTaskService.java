package com.mongoso.mgs.module.dailycost.service.commissiontask;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.CommissionTaskAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.CommissionTaskPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.CommissionTaskQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiontask.vo.CommissionTaskRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 提成任务 Service 接口
 *
 * <AUTHOR>
 */
public interface CommissionTaskService {

    /**
     * 创建提成任务
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long commissionTaskAdd(@Valid CommissionTaskAditReqVO reqVO);

    /**
     * 更新提成任务
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long commissionTaskEdit(@Valid CommissionTaskAditReqVO reqVO);

    /**
     * 更新提成任务
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long saleChangOrderEdit(@Valid CommissionTaskAditReqVO reqVO);

    /**
     * 删除提成任务
     *
     * @param commissionTaskId 编号
     */
    void commissionTaskDel(Long commissionTaskId);

    /**
     * 删除提成任务
     *
     * @param saleOrderId 编号
     */
    void saleOrderIdDel(Long saleOrderId);

    /**
     * 获得提成任务信息
     *
     * @param commissionTaskId 编号
     * @return 提成任务信息
     */
    CommissionTaskRespVO commissionTaskDetail(Long commissionTaskId);

    /**
     * 获得提成任务列表
     *
     * @param reqVO 查询条件
     * @return 提成任务列表
     */
    List<CommissionTaskRespVO> commissionTaskList(@Valid CommissionTaskQueryReqVO reqVO);

    /**
     * 获得提成任务分页
     *
     * @param reqVO 查询条件
     * @return 提成任务分页
     */
    PageResult<CommissionTaskRespVO> commissionTaskPage(@Valid CommissionTaskPageReqVO reqVO);

    /**
     * 状态变更
     *
     * @param FlowApprove 编号
     */
    BatchResult commissionTaskApprove(FlowApprove reqVO);


    /**
     * 批量删除提成规则
     *
     * @param id 编号
     */
    ResultX<BatchResult> commissionTaskDelBatch(IdReq reqVO);

    Object commissionTaskFlowCallback(FlowCallback reqVO);

}
