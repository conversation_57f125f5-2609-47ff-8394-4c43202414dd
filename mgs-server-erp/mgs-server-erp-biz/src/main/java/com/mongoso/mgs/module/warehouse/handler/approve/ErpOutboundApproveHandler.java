package com.mongoso.mgs.module.warehouse.handler.approve;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.common.util.MathUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.dal.db.customeraddress.CustomerAddressDO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.base.service.orderrelation.OrderRelationService;
import com.mongoso.mgs.module.dailycost.controller.admin.commissiongrant.vo.SaleOrderAddGrantReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costsaleincome.vo.CostSaleIncomeAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.enums.SpuConfigSourceOrderEnum;
import com.mongoso.mgs.module.dailycost.service.commissiongrant.CommissionGrantService;
import com.mongoso.mgs.module.dailycost.service.costsaleincome.CostSaleIncomeService;
import com.mongoso.mgs.module.dailycost.service.spuconfig.CostSpuConfigService;
import com.mongoso.mgs.module.finance.service.common.FinanceConnectService;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingDetailDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingMaterialTotalDO;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodworkmaterialbom.ProdWorkMaterialBomMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMaterialTotalMapper;
import com.mongoso.mgs.module.produce.service.prodworkmaterialbom.ProdWorkMaterialBomService;
import com.mongoso.mgs.module.produce.service.workpicking.WorkPickingService;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.purchase.dal.db.purchaseexchange.PurchaseExchangeDO;
import com.mongoso.mgs.module.purchase.dal.db.purchasereturn.PurchaseReturnDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchaseexchange.PurchaseExchangeDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchaseexchange.PurchaseExchangeMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchasereturn.PurchaseReturnDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchasereturn.PurchaseReturnMapper;
import com.mongoso.mgs.module.purchase.service.purchase.PurchaseOrderService;
import com.mongoso.mgs.module.purchase.service.purchaseexchange.PurchaseExchangeDetailService;
import com.mongoso.mgs.module.purchase.service.purchasereturn.detail.PurchaseReturnDetailService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.vo.ErpSaleOrderRespVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailBaseVO;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorderdetail.vo.ErpSaleOrderDetailRespVO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorderdetail.ErpSaleOrderDetailDO;
import com.mongoso.mgs.module.sale.dal.db.saleexchange.SaleExchangeDO;
import com.mongoso.mgs.module.sale.dal.db.shipnotice.SaleShipNoticeDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorderdetail.ErpSaleOrderDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.saleexchange.SaleExchangeMapper;
import com.mongoso.mgs.module.sale.dal.mysql.saleexchangedetail.SaleExchangeDetailMapper;
import com.mongoso.mgs.module.sale.dal.mysql.shipnotice.SaleShipNoticeMapper;
import com.mongoso.mgs.module.sale.dal.mysql.shipnoticedetail.SaleShipNoticeDetailMapper;
import com.mongoso.mgs.module.sale.enums.FormStatusEnum;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.ErpSaleOrderDetailService;
import com.mongoso.mgs.module.sale.service.saleexchangedetail.SaleExchangeDetailService;
import com.mongoso.mgs.module.sale.service.shipnoticedetail.SaleShipNoticeDetailService;
import com.mongoso.mgs.module.warehouse.controller.admin.erpdelivery.vo.ErpDeliveryQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpmaterialstock.vo.ErpMaterialStockQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.detail.ErpOutboundDetailRespVO;
import com.mongoso.mgs.module.warehouse.controller.admin.stockbook.vo.detail.StockBookDetailRespVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDetailDO;
import com.mongoso.mgs.module.warehouse.dal.db.materialstock.ErpMaterialStockDO;
import com.mongoso.mgs.module.warehouse.dal.db.stockbook.StockBookDO;
import com.mongoso.mgs.module.warehouse.dal.db.stockbook.StockBookDetailDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpdelivery.ErpDeliveryMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stockbook.StockBookDetailMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.stockbook.StockBookMapper;
import com.mongoso.mgs.module.warehouse.enums.DeliveryDetailEnum;
import com.mongoso.mgs.module.warehouse.enums.ErpOutboundBizTypeEnum;
import com.mongoso.mgs.module.warehouse.enums.ErpStcokSFTEnum;
import com.mongoso.mgs.module.warehouse.service.erpmaterialstock.ErpMaterialStockService;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;
import static com.mongoso.mgs.module.sale.enums.ErrorCodeConstants.RELATED_SALE_FORCE_CLOSE;

/**
 * @author: Fashoin.Liu
 * @date: 2024/12/11 18:34
 * @description: 出库单审批流程处理类
 */

@Component
public class ErpOutboundApproveHandler extends FlowApproveHandler<ErpOutboundDO> {

    @Resource
    private ErpOutboundMapper erpOutboundMapper;

    @Resource
    private ErpOutboundDetailMapper erpOutboundDetailMapper;

    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Resource
    private ErpSaleOrderDetailMapper erpSaleOrderDetailMapper;

    @Resource
    private ErpSaleOrderDetailService erpSaleOrderDetailService;

    @Resource
    private SaleShipNoticeMapper saleShipNoticeMapper;

    @Resource
    private SaleShipNoticeDetailMapper saleShipNoticeDetailMapper;

    @Resource
    private SaleShipNoticeDetailService saleShipNoticeDetailService;

    @Resource
    private PurchaseReturnMapper purchaseReturnMapper;

    @Resource
    private PurchaseReturnDetailMapper purchaseReturnDetailMapper;

    @Resource
    private PurchaseReturnDetailService purchaseReturnDetailService;

    @Resource
    private WorkPickingDetailMapper workPickingDetailMapper;

    @Resource
    private WorkPickingMapper workPickingMapper;

    @Resource
    private WorkPickingService workPickingService;

    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;

    @Resource
    private ErpMaterialStockService erpMaterialStockService;

    @Resource
    private PurchaseExchangeDetailService purchaseExchangeDetailService;

    @Resource
    private ProdWorkMaterialBomMapper prodWorkMaterialBomMapper;

    @Resource
    private ProdWorkMaterialBomService prodWorkMaterialBomService;

    @Resource
    private WorkPickingMaterialTotalMapper workPickingMaterialTotalMapper;

    @Resource
    private ErpDeliveryMapper erpDeliveryMapper;

    @Resource
    ErpBaseService erpBaseService;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private FinanceConnectService financeConnectService;
    @Resource
    private CostSpuConfigService costSpuConfigService;
    @Resource
    private CostSaleIncomeService costSaleIncomeService;

    @Resource
    private OrderRelationService orderRelationService;

    @Resource
    private StockBookDetailMapper stockBookDetailMapper;

    @Resource
    private StockBookMapper stockBookMapper;

    @Resource
    private StockBookService stockBookService;

    @Resource
    private CommissionGrantService commissionGrantService;

    @Resource
    private SaleExchangeMapper saleExchangeMapper;

    @Resource
    private SaleExchangeDetailMapper saleExchangeDetailMapper;

    @Resource
    private PurchaseExchangeMapper purchaseExchangeMapper;

    @Resource
    private PurchaseExchangeDetailMapper purchaseExchangeDetailMapper;

    @Resource
    private SaleExchangeDetailService saleExchangeDetailService;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private ProdWorkMapper prodWorkMapper;

    @Override
    protected ApproveCommonAttrs approvalAttributes(ErpOutboundDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(ErpOutboundDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(ErpOutboundDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getOutboundId())
                .objCode(item.getOutboundCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }


    @Override
    protected Boolean businessVerify(ErpOutboundDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();

        //审核校验
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            Long outboundId = item.getOutboundId();
            Long relatedOrderId = item.getRelatedOrderId();

            //查询出库单
            ErpOutboundDetailQueryReqVO outboundDetailQueryReqVO = new ErpOutboundDetailQueryReqVO();
            outboundDetailQueryReqVO.setOutboundId(outboundId);
            outboundDetailQueryReqVO.setDetailTypeDictId(DeliveryDetailEnum.MATERIAL_DETAIL.type.toString());
            List<ErpOutboundDetailDO> outboundRespList = erpOutboundDetailMapper.selectList(outboundDetailQueryReqVO);
            for (ErpOutboundDetailDO detailDO : outboundRespList) {
                if(StrUtilX.isEmpty(detailDO.getWarehouseOrgId())) {
                    failItem.setCode(item.getOutboundCode());
                    failItem.setReason("物料编码【" + detailDO.getMaterialCode() + "】出库仓库不能为空!");
                    return false;
                }
            }

            //验证上游单据
            if(!perDocVerify(item, failItem)) {
                return false;
            }

            //关联行合并
            Map<Long, DocumentRespBO> outboundQtyMap = mergeConvertMap(outboundRespList);

            //销售出库单
            if(item.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType()
                    || item.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()){

                //查询销售订单
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(relatedOrderId);
//                if (erpSaleOrder.getIsForceClose().equals(1)) {
//                    failItem.setCode(item.getOutboundCode());
//                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
//                    return false;
//                }

                //查询销售订单(可出库数量)
                List<DocumentRespBO> outboundableQtyRespList = erpSaleOrderDetailMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出 todo 取消限制条件
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】在上游单据已被删除,审核失败!");
                        return false;
                    }
                }

                if (erpSaleOrder!=null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type){
                    failItem.setCode(item.getOutboundCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }

            //销售通知出库单
            if(item.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_OUTBOUND.getType() || item.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){
                //查询销售发货通知单(可出库数量)
                List<DocumentRespBO> outboundableQtyRespList = saleShipNoticeDetailMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null || documentRespBO.getSumQty().compareTo(outboundableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量不允许超过可出库数量,审核失败!");
                        return false;
                    }
                }
            }

            //采购退货出库单
            if(item.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_RETURN_OUTBOUND.getType()){
                //查询采购退货单(可出库数量)
                List<DocumentRespBO> outboundableQtyRespList = purchaseReturnDetailMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null || documentRespBO.getSumQty().compareTo(outboundableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量不允许超过可出库数量,审核失败!");
                        return false;
                    }
                }
            }

            //工单领料出库单和委外订单领料出库单
            if (item.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType() ||
                    item.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_PICKING_OUTBOUND.getType()) {
                //查询采工单领料单(可出库数量)
                List<DocumentRespBO> outboundableQtyRespList = workPickingDetailMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null || documentRespBO.getSumQty().compareTo(outboundableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量不允许超过可出库数量,审核失败!");
                        return false;
                    }
                }
            }

            //生产倒冲领料出库单
            if (item.getBizType() == ErpOutboundBizTypeEnum.PROD_BACKFLUSH_PICKING_OUTBOUND.getType()) {
                //查询生产工单BOM清单倒冲领料(可出库数量)
                List<DocumentRespBO> outboundableQtyRespList = prodWorkMaterialBomMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null || documentRespBO.getSumQty().compareTo(outboundableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量不允许超过可出库数量!");
                        return false;
                    }
                }
            }

            //销售换货出库单
            if (item.getBizType() == ErpOutboundBizTypeEnum.SALE_EXCHANGE_OUTBOUND.getType()) {
                //查询销售退货单(可操作数量)
                List<DocumentRespBO> outboundableQtyRespList = saleExchangeDetailMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null || documentRespBO.getSumQty().compareTo(outboundableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量不允许超过可出库数量!");
                        return false;
                    }
                }
            }

            //采购换货出库单
            if (item.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_EXCHANGE_OUTBOUND.getType()) {
                //查询销售退货单(可操作数量)
                List<DocumentRespBO> outboundableQtyRespList = purchaseExchangeDetailMapper.outboundableQtyList(relatedOrderId);
                Map<Long, BigDecimal> outboundableQtyMap = outboundableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal outboundableQty = outboundableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (outboundableQty == null || documentRespBO.getSumQty().compareTo(outboundableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量不允许超过可出库数量!");
                        return false;
                    }
                }
            }


            //查询物料库存请求参数封装
            List<ErpMaterialStockQueryReqVO> reqVOList = new ArrayList<>();
            for(ErpOutboundDetailDO detailDO : outboundRespList){
                ErpMaterialStockQueryReqVO stockQueryReqVO = new ErpMaterialStockQueryReqVO();
                stockQueryReqVO.setMaterialId(detailDO.getMaterialId());
                stockQueryReqVO.setWarehouseOrgId(detailDO.getWarehouseOrgId());
                reqVOList.add(stockQueryReqVO);
            }

            //查询物料库存
            List<DocumentRespBO> stockRespList = erpMaterialStockMapper.queryStockListByMaterialAndOrgId(reqVOList);
            Map<String, DocumentRespBO> materialStockQtyMap = stockRespList.stream().collect(Collectors.toMap(docResp ->
                            docResp.getMaterialId()+"_"+docResp.getWarehouseOrgId(), obj -> obj));

            //判断归还数量是否超出库存可用数量
            for(ErpOutboundDetailDO detailDO : outboundRespList){
                String key = detailDO.getMaterialId() + "_" + detailDO.getWarehouseOrgId();
                DocumentRespBO documentRespBO = materialStockQtyMap.get(key);
                BigDecimal stockableQty = documentRespBO.getStockQty().subtract(documentRespBO.getLockedQty());

                //生产倒冲领料出库单 不算预定数量
                if (item.getBizType() == ErpOutboundBizTypeEnum.PROD_BACKFLUSH_PICKING_OUTBOUND.getType()) {

                    if (stockableQty == null || detailDO.getOutboundQty().compareTo(stockableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        String warehouseOrgName = erpBaseService.getOrgNameById(detailDO.getWarehouseOrgId());
                        String reason = "物料编码【"+detailDO.getMaterialCode()+"】仓库【"+ warehouseOrgName +"】出库数量超过物料库存可用数量,审核失败!";
                        failItem.setReason(reason);
                        return false;
                    }
                }else {
                    //计算可用数量（减去预订数量）
                    ErpOutboundDetailRespVO outboundDetailRespVO = new ErpOutboundDetailRespVO();
                    outboundDetailRespVO.setWarehouseOrgId(detailDO.getWarehouseOrgId());
                    outboundDetailRespVO.setMaterialId(detailDO.getMaterialId());
                    stockableQty = stockBookService.getAvailableQty(item.getRelatedOrderId(), outboundDetailRespVO, stockableQty);

                    if (stockableQty == null || detailDO.getOutboundQty().compareTo(stockableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        String warehouseOrgName = erpBaseService.getOrgNameById(detailDO.getWarehouseOrgId());
                        String reason = "物料编码【"+detailDO.getMaterialCode()+"】仓库【"+ warehouseOrgName +"】出库数量超过物料库存可用数量,审核失败!";
                        failItem.setReason(reason);
                        return false;
                    }
                }
            }

        }

        //反审核校验
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            Long outboundId = item.getOutboundId();
            Long relatedOrderId = item.getRelatedOrderId();

            //查询出库发货单
            ErpDeliveryQueryReqVO deliveryQueryReqVO = new ErpDeliveryQueryReqVO();
            deliveryQueryReqVO.setRelatedOrderId(outboundId);
            deliveryQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            Long deliveryApprovedCount = erpDeliveryMapper.selectCount(deliveryQueryReqVO);
            if(deliveryApprovedCount > 0){
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("出库单存在已审核的出库发货单,不可操作反审核");
                return false;
            }

            //销售出库单
            if(item.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType() || item.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()){
                //查询销售订单
                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(relatedOrderId);
//                if (erpSaleOrder.getIsForceClose().equals(1)) {
//                    failItem.setCode(item.getOutboundCode());
//                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
//                    return false;
//                }

                //查询出库单
                List<DocumentRespBO> outboundQtyRespList = erpOutboundDetailMapper.materialOutboundQtyList(outboundId);
                Map<Long, DocumentRespBO> outboundQtyMap = outboundQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, doc -> doc));

                //查询销售订单(可操作数量=出库数量-已扣费数量-已退货数量-已换货数量+已换货入库数量)
                List<DocumentRespBO> completeableQtyRespList = erpSaleOrderDetailMapper.completeableQtyList(relatedOrderId);
                Map<Long, BigDecimal> completeableQtyMap = completeableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getMaterialId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal completeableQty = completeableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (completeableQty == null || documentRespBO.getSumQty().compareTo(completeableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量大于关联销售订单物料已出库总数量-已扣费数量-已退货数量-已换货数量+已换货入库数量，不可反审核!");
                        return false;
                    }
                }

                if (erpSaleOrder!=null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type){
                    failItem.setCode(item.getOutboundCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }

            //销售通知出库单
            if(item.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_OUTBOUND.getType() || item.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){

                //查询出库单
                List<DocumentRespBO> outboundQtyRespList = erpOutboundDetailMapper.materialOutboundQtyList(outboundId);
                Map<Long, DocumentRespBO> outboundQtyMap = outboundQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, doc -> doc));

                SaleShipNoticeDO saleShipNoticeDO = saleShipNoticeMapper.selectById(relatedOrderId);
                //查询销售订单(可操作数量=出库数量-已扣费数量-已退货数量-已换货数量+已换货入库数量)
                List<DocumentRespBO> completeableQtyRespList = erpSaleOrderDetailMapper.completeableQtyList(saleShipNoticeDO.getSaleOrderId());
                Map<Long, BigDecimal> completeableQtyMap = completeableQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getMaterialId,
                        DocumentRespBO::getSumQty));

                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal completeableQty = completeableQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (completeableQty == null || documentRespBO.getSumQty().compareTo(completeableQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量大于关联销售订单物料已出库总数量-已扣费数量-已退货数量-已换货数量+已换货入库数量，不可反审核!");
                        return false;
                    }
                }
            }

            //工单领料出库单和委外订单领料出库单
            if (item.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType() ||
                    item.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_PICKING_OUTBOUND.getType()) {

                //查询出库单
                List<DocumentRespBO> outboundQtyRespList = erpOutboundDetailMapper.materialOutboundQtyList(outboundId);
                Map<Long, DocumentRespBO> outboundQtyMap = outboundQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, doc -> doc));

                WorkPickingDO workPickingDO = workPickingMapper.selectById(relatedOrderId);
                //查询领料统计表
                if(workPickingDO !=null){
                    //判断入库是直接入库还是委外退退料
                    PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
                    ProdWorkDO prodWorkDO = new ProdWorkDO();
                    if (item.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_PICKING_OUTBOUND.getType()){
                        purchaseOrderDO = purchaseOrderMapper.selectById(workPickingDO.getRelatedOrderId());
                    }else {
                        prodWorkDO = prodWorkMapper.selectById(workPickingDO.getRelatedOrderId());
                    }
                    //查询领料单BOM汇总
                    List<DocumentRespBO> outboundedQtyList = new ArrayList<>();
                    //查询领料单BOM汇总
                    if ((purchaseOrderDO.getInProcessConfigDictId() != null && purchaseOrderDO.getInProcessConfigDictId() == 1) || (prodWorkDO.getInProcessConfigDictId() != null && prodWorkDO.getInProcessConfigDictId() == 1)){
                        outboundedQtyList = workPickingMaterialTotalMapper.outboundedQtyList(workPickingDO.getRelatedOrderId());
                    }else {
                        outboundedQtyList = workPickingMaterialTotalMapper.inboundedQtyList(workPickingDO.getRelatedOrderId());
                    }

                    Map<Long, BigDecimal> outboundedQtyMap = outboundedQtyList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, DocumentRespBO::getSumQty));
                    //判断出库数量是否超出
                    for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                        Long relatedOrderDetailId = entry.getKey();
                        BigDecimal outboundedQty = outboundedQtyMap.get(relatedOrderDetailId);
                        DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                        if (outboundedQty == null || documentRespBO.getSumQty().compareTo(outboundedQty) > 0){
                            failItem.setCode(item.getOutboundCode());
                            failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库数量大于关联单据物料已出库总数量-已退料数量，不可反审核!");
                            return false;
                        }
                    }
                }
            }

            //工单直接领料出库单，委外订单直接领料出库单
            if (item.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_DIRECT_PICKING_OUTBOUND.getType() ||
                    item.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_OUTBOUND.getType()) {

                //查询出库单
                List<DocumentRespBO> outboundQtyRespList = erpOutboundDetailMapper.materialOutboundQtyList(outboundId);
                Map<Long, DocumentRespBO> outboundQtyMap = outboundQtyRespList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, doc -> doc));

                //判断入库是直接入库还是委外退退料
                PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
                ProdWorkDO prodWorkDO = new ProdWorkDO();
                if (item.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_OUTBOUND.getType()){
                    purchaseOrderDO = purchaseOrderMapper.selectById(relatedOrderId);
                }else {
                    prodWorkDO = prodWorkMapper.selectById(relatedOrderId);
                }

                List<DocumentRespBO> outboundedQtyList = new ArrayList<>();
                //查询领料单BOM汇总
                if ((purchaseOrderDO.getInProcessConfigDictId() != null && purchaseOrderDO.getInProcessConfigDictId() == 1) || (prodWorkDO.getInProcessConfigDictId() != null && prodWorkDO.getInProcessConfigDictId() == 1)){
                    outboundedQtyList = workPickingMaterialTotalMapper.returnedQtyList(relatedOrderId);
                }else {
                    outboundedQtyList = workPickingMaterialTotalMapper.inboundedQtyList(relatedOrderId);
                }
                Map<Long, BigDecimal> outboundedQtyMap = outboundedQtyList.stream().collect(Collectors.toMap(DocumentRespBO::getFieldId, DocumentRespBO::getSumQty));
                //判断出库数量是否超出
                for (Map.Entry<Long, DocumentRespBO> entry : outboundQtyMap.entrySet()){
                    Long relatedOrderDetailId = entry.getKey();
                    BigDecimal inboundedQty = outboundedQtyMap.get(relatedOrderDetailId);
                    DocumentRespBO documentRespBO = outboundQtyMap.get(relatedOrderDetailId);
                    if (inboundedQty == null || documentRespBO.getSumQty().compareTo(inboundedQty) > 0){
                        failItem.setCode(item.getOutboundCode());
                        failItem.setReason("物料编码【"+documentRespBO.getCode()+"】出库单内的物料出库数量大于对应物料的可入库数量，不可反审核!");
                        return false;
                    }
                }


            }

            //财务反审校验
            financeConnectService.checkOrder(item.getOutboundId());
        }

        return true;
    }

    @Override
    public Integer handleBusinessData(ErpOutboundDO erpOutboundDO, BaseApproveRequest request) {
        int buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        Long id = erpOutboundDO.getOutboundId();

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        List<ErpOutboundDetailDO> outboundRespList = new ArrayList<>();
//        ErpOutboundDO erpOutboundDO = erpOutboundMapper.selectById(id);

        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            //查询出库单
            ErpOutboundDetailQueryReqVO outboundDetailQueryReqVO = new ErpOutboundDetailQueryReqVO();
            outboundDetailQueryReqVO.setOutboundId(id);
//            outboundDetailQueryReqVO.setDetailTypeDictId(DeliveryDetailEnum.MATERIAL_DETAIL.type.toString());
            outboundRespList = erpOutboundDetailMapper.selectList(outboundDetailQueryReqVO);

            //出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.OUTBOUND.getType()) {
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //销售出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType()
                    || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()) {
                if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()){
                    for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                        if (Integer.valueOf(outboundDetailDO.getDetailTypeDictId()) == DeliveryDetailEnum.CHECK_MATERIAL_DETAIL.type){
                            continue;
                        }
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        erpSaleOrderDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());

                        outboundDetailDO.setDeliveredQty(outboundQty);
                        outboundDetailDO.setIsMaterialFullDelivered(1);
                    }
                    erpOutboundDO.setIsFullDelivered(1);
                }else {
                    for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        erpSaleOrderDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());

                        //新增日成本销售收入单
                        costSaleIncomeAdd(outboundDetailDO,erpOutboundDO);
                    }
                }

                //判断是否已出库
                erpSaleOrderDetailService.updateShippedStatus(erpOutboundDO.getSaleOrderId());



                //新增日成本提成发放
                saleOrderAddGrant(erpOutboundDO,outboundRespList);
            }

            //销售通知出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_OUTBOUND.getType()
                    || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()) {
                if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){
                    for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                        if (Integer.valueOf(outboundDetailDO.getDetailTypeDictId()) == DeliveryDetailEnum.CHECK_MATERIAL_DETAIL.type){
                            continue;
                        }
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        saleShipNoticeDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());

                        outboundDetailDO.setDeliveredQty(outboundQty);
                        outboundDetailDO.setIsMaterialFullDelivered(1);
                        //新增日成本销售收入单
                        costSaleIncomeAdd(outboundDetailDO,erpOutboundDO);
                    }
                    erpOutboundDO.setIsFullDelivered(1);

                }else {
                    for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        saleShipNoticeDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                        //新增日成本销售收入单
                        costSaleIncomeAdd(outboundDetailDO,erpOutboundDO);
                    }
                }
                //判断是否已出库
                erpSaleOrderDetailService.updateShippedStatus(erpOutboundDO.getSaleOrderId());



                //新增日成本提成发放
                saleOrderAddGrant(erpOutboundDO,outboundRespList);
            }

            //采购退货出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_RETURN_OUTBOUND.getType()) {
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新采购退货单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    purchaseReturnDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //工单领料出库单和委外订单领料出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType()
                || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_PICKING_OUTBOUND.getType()) {

                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新工单领料出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    workPickingService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
                WorkPickingDO workPickingDO = workPickingMapper.selectById(erpOutboundDO.getRelatedOrderId());
                if (workPickingDO != null) {
                    if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType()){
                        ProdWorkDO prodWorkDO = new ProdWorkDO();
                        prodWorkDO.setProdWorkId(workPickingDO.getRelatedOrderId());
                        prodWorkDO.setIsExistInboundableQty(1);
                        prodWorkMapper.updateById(prodWorkDO);
                    }else {
                        PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
                        purchaseOrderDO.setPurchaseOrderId(workPickingDO.getRelatedOrderId());
                        purchaseOrderDO.setIsExistInboundableQty(1);
                        purchaseOrderMapper.updateById(purchaseOrderDO);
                    }
                }
            }

            //生产倒冲领料出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_BACKFLUSH_PICKING_OUTBOUND.getType()) {
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新生产工单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    prodWorkMaterialBomService.updateOutboundedQty(relatedOrderDetailId, outboundQty);
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //销售换货出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_EXCHANGE_OUTBOUND.getType()){
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新销售换货单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    saleExchangeDetailService.updateOutboundedQtyByInbound(relatedOrderDetailId, outboundQty);
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //采购换货出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_EXCHANGE_OUTBOUND.getType()){
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新销售换货单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    purchaseExchangeDetailService.updateOutboundedQtyByInbound(relatedOrderDetailId, outboundQty);
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }

                purchaseExchangeDetailService.handleOutboundExchange(outboundRespList, erpOutboundDO.getRelatedOrderId(), buttonType);
            }

            //工单直接领料出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_DIRECT_PICKING_OUTBOUND.getType() || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_OUTBOUND.getType()){
                //以物料的维度进行合并
                Map<Long, ErpOutboundDetailDO> outboundQtyMap = new HashMap<>();
                for(ErpOutboundDetailDO detailAditReqVO :  outboundRespList){
                    Long materialId = detailAditReqVO.getMaterialId();
                    ErpOutboundDetailDO documentRespBO = outboundQtyMap.get(materialId);
                    if(documentRespBO == null ){
                        documentRespBO = BeanUtilX.copy(detailAditReqVO, ErpOutboundDetailDO::new);
                        outboundQtyMap.put(materialId, documentRespBO);
                    }else{
                        documentRespBO.setOutboundQty(documentRespBO.getOutboundQty().add(detailAditReqVO.getOutboundQty()));
                        outboundQtyMap.put(materialId, documentRespBO);
                    }
                    BigDecimal outboundQty = detailAditReqVO.getOutboundQty();
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(detailAditReqVO.getMaterialId(),
                            detailAditReqVO.getWarehouseOrgId(), outboundQty.negate(),
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }

                //判断新增还是修改生产工单领料统计表
                List<WorkPickingMaterialTotalDO> prodWorkPickingDetailDO = workPickingMaterialTotalMapper.selectListByRelatedOrderId(erpOutboundDO.getRelatedOrderId());
                Map<Long, WorkPickingMaterialTotalDO> prodWorkPickingDetailDOMap = prodWorkPickingDetailDO.stream().collect(Collectors.toMap(WorkPickingMaterialTotalDO::getMaterialId, Function.identity()));

                List<WorkPickingMaterialTotalDO> addList = new ArrayList<>();
                List<WorkPickingMaterialTotalDO> updateList = new ArrayList<>();
                if (CollUtilX.isEmpty(prodWorkPickingDetailDO)) {
                    // 全部新增
                    for (Map.Entry<Long, ErpOutboundDetailDO> entry : outboundQtyMap.entrySet()){
                        WorkPickingMaterialTotalDO copy = BeanUtilX.copy(entry.getValue(), WorkPickingMaterialTotalDO::new);
                        copy.setRelatedOrderId(erpOutboundDO.getRelatedOrderId());
                        copy.setRelatedOrderCode(erpOutboundDO.getRelatedOrderCode());
                        copy.setWarehouseOrgId(null);
                        copy.setReceivedQty(BigDecimal.ZERO);
                        copy.setReturnedQty(BigDecimal.ZERO);
                        copy.setInboundedQty(BigDecimal.ZERO);
                        copy.setOutboundedQty(entry.getValue().getOutboundQty());
                        addList.add(copy);
                    }
                } else {
                    // 有增有改
                    for (Map.Entry<Long, ErpOutboundDetailDO> entry : outboundQtyMap.entrySet()){
                        WorkPickingMaterialTotalDO copy = BeanUtilX.copy(entry.getValue(), WorkPickingMaterialTotalDO::new);
                        copy.setRelatedOrderId(erpOutboundDO.getRelatedOrderId());
                        copy.setRelatedOrderCode(erpOutboundDO.getRelatedOrderCode());
                        WorkPickingMaterialTotalDO prodWorkPickingDetailDO1 = prodWorkPickingDetailDOMap.get(entry.getValue().getMaterialId());
                        if (prodWorkPickingDetailDO1 == null) {
                            // 新增
                            copy.setReceivedQty(BigDecimal.ZERO);
                            copy.setReturnedQty(BigDecimal.ZERO);
                            copy.setInboundedQty(BigDecimal.ZERO);
                            copy.setOutboundedQty(entry.getValue().getOutboundQty());
                            addList.add(copy);

                        } else {
                            // 修改
                            prodWorkPickingDetailDO1.setOutboundedQty(prodWorkPickingDetailDO1.getOutboundedQty().add(entry.getValue().getOutboundQty()));
                            updateList.add(prodWorkPickingDetailDO1);
                        }
                    }
                }
                if (CollUtilX.isNotEmpty(addList)) {
                    workPickingMaterialTotalMapper.insertBatch(addList);
                }

                if (CollUtilX.isNotEmpty(updateList)) {
                    workPickingMaterialTotalMapper.updateBatch(updateList);
                }
                // 刷新采购订单或者生产工单的字段，是否存在可入库数量，isFullPickingReturn
                purchaseOrderService.refreshIsFullPickingReturn(erpOutboundDO.getRelatedOrderId());

                if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_DIRECT_PICKING_OUTBOUND.getType()){
                    ProdWorkDO prodWorkDO = new ProdWorkDO();
                    prodWorkDO.setProdWorkId(erpOutboundDO.getRelatedOrderId());
                    prodWorkDO.setIsExistInboundableQty(1);
                    prodWorkMapper.updateById(prodWorkDO);
                }else {
                    PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
                    purchaseOrderDO.setPurchaseOrderId(erpOutboundDO.getRelatedOrderId());
                    purchaseOrderDO.setIsExistInboundableQty(1);
                    purchaseOrderMapper.updateById(purchaseOrderDO);
                }


            }

            //更新已出库数量
            for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                if (Integer.valueOf(outboundDetailDO.getDetailTypeDictId()) == DeliveryDetailEnum.MATERIAL_DETAIL.type){
                    outboundDetailDO.setOutboundedQty(outboundDetailDO.getOutboundQty());
                }else {
                    outboundDetailDO.setDeliveredQty(outboundDetailDO.getDeliverableQty());
                }
                outboundDetailDO.setUpdatedBy(loginUser.getFullUserName());
                outboundDetailDO.setUpdatedDt(LocalDateTime.now());
            }
            erpOutboundDetailMapper.updateBatch(outboundRespList);
        }

        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            //查询出库单
            ErpOutboundDetailQueryReqVO outboundDetailQueryReqVO = new ErpOutboundDetailQueryReqVO();
            outboundDetailQueryReqVO.setOutboundId(id);
            outboundRespList = erpOutboundDetailMapper.selectList(outboundDetailQueryReqVO);

            //出库单
            if(erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.OUTBOUND.getType()){
                for(ErpOutboundDetailDO outboundDetailDO : outboundRespList){
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //销售出库单
            if(erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType()
                    || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()){
                if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()){
                    for(ErpOutboundDetailDO outboundDetailDO : outboundRespList){
                        if (Integer.valueOf(outboundDetailDO.getDetailTypeDictId()) == DeliveryDetailEnum.CHECK_MATERIAL_DETAIL.type){
                            continue;
                        }
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        erpSaleOrderDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty,
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                        outboundDetailDO.setDeliveredQty(BigDecimal.ZERO);
                        outboundDetailDO.setIsMaterialFullDelivered(0);
                    }
                    erpOutboundDO.setIsFullDelivered(0);
                }else {
                    for(ErpOutboundDetailDO outboundDetailDO : outboundRespList){
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        erpSaleOrderDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty,
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                        outboundDetailDO.setDeliveredQty(outboundQty);
                    }
                }

                //判断是否已出库
                erpSaleOrderDetailService.updateShippedStatus(erpOutboundDO.getSaleOrderId());

                //删除日成本销售收入单
                costSaleIncomeService.relatedUpFormIdDel(erpOutboundDO.getOutboundId());

                //删除日成本提成发放
                commissionGrantService.sourceOrderIdDel(erpOutboundDO.getOutboundId(),0);

            }

            //销售通知出库单
            if(erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_OUTBOUND.getType()
                    || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){
                if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){
                    for(ErpOutboundDetailDO outboundDetailDO : outboundRespList){
                        if (Integer.valueOf(outboundDetailDO.getDetailTypeDictId()) == DeliveryDetailEnum.CHECK_MATERIAL_DETAIL.type){
                            continue;
                        }
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        saleShipNoticeDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty,
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                        outboundDetailDO.setDeliveredQty(BigDecimal.ZERO);
                        outboundDetailDO.setIsMaterialFullDelivered(0);
                    }
                    erpOutboundDO.setIsFullDelivered(0);
                }else{
                    for(ErpOutboundDetailDO outboundDetailDO : outboundRespList){
                        Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                        //更新销售订单出库数量
                        BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                        saleShipNoticeDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                        //更新库存数量
                        erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                                outboundDetailDO.getWarehouseOrgId(), outboundQty,
                                ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                                erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                    }
                }

                //判断是否已出库
                erpSaleOrderDetailService.updateShippedStatus(erpOutboundDO.getSaleOrderId());

                //删除日成本销售收入单
                costSaleIncomeService.relatedUpFormIdDel(erpOutboundDO.getOutboundId());

                //删除日成本提成发放
                commissionGrantService.sourceOrderIdDel(erpOutboundDO.getOutboundId(),1);
            }

            //采购退货出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_RETURN_OUTBOUND.getType()) {
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新采购退货单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    purchaseReturnDetailService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //工单领料出库单和委外订单领料出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType()
                || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_PICKING_OUTBOUND.getType()) {
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新工单领料出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    workPickingService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
                WorkPickingDO workPickingDO = workPickingMapper.selectById(erpOutboundDO.getRelatedOrderId());
                if (workPickingDO != null) {
                    List<WorkPickingMaterialTotalDO> totalDOList = workPickingMaterialTotalMapper.selectListByRelatedOrderId(workPickingDO.getRelatedOrderId());
                    boolean allZero = totalDOList.stream().allMatch(totalDo -> totalDo.getOutboundedQty().compareTo(BigDecimal.ZERO) == 0 || totalDo.getInboundedQty().compareTo(totalDo.getOutboundedQty()) == 0);
                    if (allZero){
                        if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType()){
                            ProdWorkDO prodWorkDO = new ProdWorkDO();
                            prodWorkDO.setProdWorkId(workPickingDO.getRelatedOrderId());
                            prodWorkDO.setIsExistInboundableQty(0);
                            prodWorkMapper.updateById(prodWorkDO);
                        }else {
                            PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
                            purchaseOrderDO.setPurchaseOrderId(workPickingDO.getRelatedOrderId());
                            purchaseOrderDO.setIsExistInboundableQty(0);
                            purchaseOrderMapper.updateById(purchaseOrderDO);
                        }
                    }
                }
            }

            //生产倒冲领料出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_BACKFLUSH_PICKING_OUTBOUND.getType()) {
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新生产工单库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    prodWorkMaterialBomService.updateOutboundedQty(relatedOrderDetailId, outboundQty.negate());
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //销售换货单出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_EXCHANGE_OUTBOUND.getType()){
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新销售换货单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    saleExchangeDetailService.updateOutboundedQtyByInbound(relatedOrderDetailId, outboundQty.negate());
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }
            }

            //采购换货出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_EXCHANGE_OUTBOUND.getType()){
                for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                    Long relatedOrderDetailId = outboundDetailDO.getRelatedOrderDetailId();
                    //更新销售换货单出库数量
                    BigDecimal outboundQty = outboundDetailDO.getOutboundQty();
                    purchaseExchangeDetailService.updateOutboundedQtyByInbound(relatedOrderDetailId, outboundQty.negate());
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(outboundDetailDO.getMaterialId(),
                            outboundDetailDO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }

                purchaseExchangeDetailService.handleOutboundExchange(outboundRespList, erpOutboundDO.getRelatedOrderId(), buttonType);
            }

            //工单直接领料出库单
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_DIRECT_PICKING_OUTBOUND.getType() || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_DIRECT_PICKING_OUTBOUND.getType()) {

                //以物料的维度进行合并
                Map<Long, ErpOutboundDetailDO> outboundQtyMap = new HashMap<>();
                for(ErpOutboundDetailDO detailAditReqVO :  outboundRespList){
                    Long materialId = detailAditReqVO.getMaterialId();
                    ErpOutboundDetailDO documentRespBO = outboundQtyMap.get(materialId);
                    if(documentRespBO == null ){
                        documentRespBO = BeanUtilX.copy(detailAditReqVO, ErpOutboundDetailDO::new);
                        outboundQtyMap.put(materialId, documentRespBO);
                    }else{
                        documentRespBO.setOutboundQty(documentRespBO.getOutboundQty().add(detailAditReqVO.getOutboundQty()));
                        outboundQtyMap.put(materialId, documentRespBO);
                    }
                    BigDecimal outboundQty = detailAditReqVO.getOutboundQty();
                    //更新库存数量
                    erpMaterialStockService.updateMaterialStockQty(detailAditReqVO.getMaterialId(),
                            detailAditReqVO.getWarehouseOrgId(), outboundQty,
                            ErpStcokSFTEnum.MO_OUTBOUND.getType(), id,
                            erpOutboundDO.getCreatedBy(), erpOutboundDO.getFormDt());
                }

                List<WorkPickingMaterialTotalDO> prodWorkPickingDetailDO = workPickingMaterialTotalMapper.selectListByRelatedOrderId(erpOutboundDO.getRelatedOrderId());
                if (CollUtilX.isEmpty(prodWorkPickingDetailDO)) {
                    throw exceptionMsg("数据问题，生产工单领料单统计表没有物料数据！");
                }
                Map<Long, WorkPickingMaterialTotalDO> prodWorkPickingDetailDOMap = prodWorkPickingDetailDO.stream().collect(Collectors.toMap(WorkPickingMaterialTotalDO::getMaterialId, Function.identity()));

                List<WorkPickingMaterialTotalDO> updateList = new ArrayList<>();
                // 有增有改
                for (Map.Entry<Long, ErpOutboundDetailDO> entry : outboundQtyMap.entrySet()){
                    WorkPickingMaterialTotalDO prodWorkPickingDetailDO1 = prodWorkPickingDetailDOMap.get(entry.getValue().getMaterialId());
                    if (prodWorkPickingDetailDO1 != null) {
                        // 修改
                        prodWorkPickingDetailDO1.setOutboundedQty(prodWorkPickingDetailDO1.getOutboundedQty().subtract(entry.getValue().getOutboundQty()));
                        updateList.add(prodWorkPickingDetailDO1);
                    }
                }
                if (CollUtilX.isNotEmpty(updateList)) {
                    workPickingMaterialTotalMapper.updateBatch(updateList);
                }

                // 刷新采购订单或者生产工单的字段，是否全部退料，isFullPickingReturn
                purchaseOrderService.refreshIsFullPickingReturn(erpOutboundDO.getRelatedOrderId());

                List<WorkPickingMaterialTotalDO> totalDOList = workPickingMaterialTotalMapper.selectListByRelatedOrderId(erpOutboundDO.getRelatedOrderId());
                boolean allZero = totalDOList.stream().allMatch(totalDo -> totalDo.getOutboundedQty().compareTo(BigDecimal.ZERO) == 0 || totalDo.getInboundedQty().compareTo(totalDo.getOutboundedQty()) == 0);
                if (allZero){
                    if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_DIRECT_PICKING_OUTBOUND.getType()){
                        ProdWorkDO prodWorkDO = new ProdWorkDO();
                        prodWorkDO.setProdWorkId(erpOutboundDO.getRelatedOrderId());
                        prodWorkDO.setIsExistInboundableQty(0);
                        prodWorkMapper.updateById(prodWorkDO);
                    }else {
                        PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
                        purchaseOrderDO.setPurchaseOrderId(erpOutboundDO.getRelatedOrderId());
                        purchaseOrderDO.setIsExistInboundableQty(0);
                        purchaseOrderMapper.updateById(purchaseOrderDO);
                    }
                }
            }

            //更新已入库数量
            for (ErpOutboundDetailDO outboundDetailDO : outboundRespList) {
                if (Integer.valueOf(outboundDetailDO.getDetailTypeDictId()) == DeliveryDetailEnum.MATERIAL_DETAIL.type){
                    outboundDetailDO.setOutboundedQty(BigDecimal.ZERO);
                }else {
                    outboundDetailDO.setDeliveredQty(BigDecimal.ZERO);
                }
                outboundDetailDO.setUpdatedBy(loginUser.getFullUserName());
                outboundDetailDO.setUpdatedDt(LocalDateTime.now());
            }
            erpOutboundDetailMapper.updateBatch(outboundRespList);
        }

        //日成本初始化
        this.dailyCostInit(buttonType, erpOutboundDO, outboundRespList);

        //更新业务数据
        erpOutboundDO.setApprovedBy(loginUser.getFullUserName());
        erpOutboundDO.setApprovedDt(LocalDateTime.now());
        erpOutboundDO.setDataStatus(dataStatus);
        Integer updateCount = erpOutboundMapper.updateById(erpOutboundDO);

        //进入财务系统
        //查询销售订单
        if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType()
                || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_OUTBOUND.getType()
                || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()
                || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()
        ) {
            ErpSaleOrderRespVO erpSaleOrderRespVO = erpSaleOrderService.erpSaleOrderDetail(erpOutboundDO.getSaleOrderId());

            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()
                    || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){
                erpSaleOrderRespVO.setIsSaleDeliveryOutbound(true);
                erpSaleOrderRespVO.set
            }
            if (erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType() || erpOutboundDO.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()) {
                //销售销售订单状态
                erpSaleOrderService.editChildrenOrderCount(erpOutboundDO.getSaleOrderId(), buttonType);
            }

            if (Objects.nonNull(erpSaleOrderRespVO)){
                //重新处理数据
                List<ErpOutboundDetailDO> erpOutboundDetailList = erpOutboundDetailMapper.selectList(new LambdaQueryWrapper<ErpOutboundDetailDO>()
                        .eq(ErpOutboundDetailDO::getOutboundId, id)
                        .eq(ErpOutboundDetailDO::getDetailTypeDictId, DeliveryDetailEnum.MATERIAL_DETAIL.type.toString())
                );

                Map<Long, ErpSaleOrderDetailRespVO> saleOrderDetailMap = erpSaleOrderRespVO.getDetailList().stream().collect(Collectors.toMap(ErpSaleOrderDetailBaseVO::getMaterialId, Function.identity(), (s1, s2) -> s1));


                erpSaleOrderRespVO.setSaleOrderCode(erpOutboundDO.getOutboundCode());
                erpSaleOrderRespVO.setSaleOrderId(erpOutboundDO.getOutboundId());
                List<ErpSaleOrderDetailRespVO> listDetail = new ArrayList<>();
                for (ErpOutboundDetailDO detail : erpOutboundDetailList){
                    ErpSaleOrderDetailRespVO saleOrderDetail = saleOrderDetailMap.get(detail.getMaterialId());
                    if (Objects.isNull(saleOrderDetail)){
                        continue;
                    }
                    ErpSaleOrderDetailRespVO detailRespVO = BeanUtilX.copy(saleOrderDetail, ErpSaleOrderDetailRespVO::new);
                    detailRespVO.setQty(detail.getOutboundedQty());
                    detailRespVO.setInclTaxAmt(detailRespVO.getInclTaxPrice().multiply(detail.getOutboundedQty()));
                    detailRespVO.setExclTaxAmt(detailRespVO.getExclTaxPrice().multiply(detail.getOutboundedQty()));
                    detailRespVO.setSaleOrderDetailId(detail.getOutboundDetailId());
                    detailRespVO.setRowNo(detail.getRowNo().shortValue());
                    listDetail.add(detailRespVO);
                }
                erpSaleOrderRespVO.setDetailList(listDetail);
                erpSaleOrderRespVO.setSourceFormType(OrderTypeEnum.SALE_OUT_BOUND_ORDER.type.shortValue());

                //源头单据id
                erpSaleOrderRespVO.setOriginOrderId(erpOutboundDO.getSaleOrderId());

                //对接财务数据
                financeConnectService.saleInsertFinance(erpSaleOrderRespVO, buttonType);
            }
        }

        //关联信息处理
        orderRelationService.orderRelationAditOrDel(buttonType, erpOutboundDO.getOutboundId(), erpOutboundDO.getOutboundCode(), erpOutboundDO.getRelatedOrderId());

        //处理预订任务（扣减预订明细的可预订数量，并处理对应预订任务的预定状态）
        this.handleBookTask(buttonType, erpOutboundDO.getOutboundId(), outboundRespList);

        return updateCount;
    }

    private void handleBookTask(Integer buttonType, Long relatedOrderId, List<ErpOutboundDetailDO> outboundRespList) {
        //更新预订明细的可用数量
        if (buttonType == DataButtonEnum.APPROVE.getKey()){
            for(ErpOutboundDetailDO detailDO : outboundRespList){
                //计算可用数量（减去预订数量）
                ErpOutboundDetailRespVO outboundDetailRespVO = BeanUtilX.copy(detailDO, ErpOutboundDetailRespVO::new);
                handleBookQty(relatedOrderId, outboundDetailRespVO);
            }
        }
    }

    /**
     * 处理预定任务的预订数量
     * @param orderId
     * @param detailRespVO
     * @return
     */
    private void handleBookQty(Long orderId, ErpOutboundDetailRespVO detailRespVO) {

        BigDecimal outboundQty = detailRespVO.getOutboundQty();

        //预订单对应的明细信息
        List<StockBookDetailRespVO> bookDetailList = stockBookDetailMapper.queryBookIdList(detailRespVO);

        //获取关联的预订队列 使用orderId查询
        List<Long> relationIdList = orderRelationService.orderRelationIdList(orderId);
        Map<Long, Long> relationIdMap = relationIdList.stream().collect(Collectors.toMap(Long::valueOf, obj -> obj));

        for (StockBookDetailRespVO bookDetailRespVO : bookDetailList) {
            Long relatedOrderId = bookDetailRespVO.getOrderId();
            Long stockBookId = bookDetailRespVO.getStockBookId();
            if (relationIdMap.containsKey(relatedOrderId)) {
                outboundQty = outboundQty.subtract(bookDetailRespVO.getBookableQty());
                StockBookDetailDO bookDetailDO = BeanUtilX.copy(bookDetailRespVO, StockBookDetailDO::new);
                if (outboundQty.compareTo(BigDecimal.ZERO) >= 0) {
                    //处理预定任务的预订数量
                    //可预订数量-，已使用数量+
                    stockBookDetailMapper.updateBookAbleQty(bookDetailDO.getBookableQty(), bookDetailDO.getStockBookDetailId());

                    //更新库存单的已预订数量
                    erpMaterialStockService.updateStockBookedQty(bookDetailDO, -1);

                    //判断当前预订任务所有明细的可预订数量是否都为0，如果都为0，将预定任务的状态改为已完成
                    int result = stockBookMapper.updateBookStatus(stockBookId);
                    if (result > 0){
                        //修改该预定任务下的预订队列
                        StockBookDO stockBookDO = stockBookMapper.selectById(stockBookId);
                        stockBookMapper.intervalUpdateBookSeq(stockBookDO.getBookSeq(), null, -1);
                    }
                } else {
                    //更新库存单的已预订数量
                    bookDetailDO.setBookableQty(outboundQty.add(bookDetailRespVO.getBookableQty()));
                    erpMaterialStockService.updateStockBookedQty(bookDetailDO, -1);

                    stockBookDetailMapper.updateBookAbleQty(bookDetailDO.getBookableQty(), bookDetailDO.getStockBookDetailId());
                    break;
                }
            }
        }
    }

    private void dailyCostInit(Integer buttonType, ErpOutboundDO erpOutboundDO, List<ErpOutboundDetailDO> outboundDetailDOList) {
        ArrayList<CostSpuConfigAditReqVO> costSpuConfigList = new ArrayList<>();
        for (ErpOutboundDetailDO erpOutboundDetailDO : outboundDetailDOList) {
            CostSpuConfigAditReqVO costSpuConfigAditReqVO = new CostSpuConfigAditReqVO();
            costSpuConfigAditReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey());
            costSpuConfigAditReqVO.setRelatedUpOrderId(erpOutboundDetailDO.getOutboundId());
            costSpuConfigAditReqVO.setRelatedUpOrderCode(erpOutboundDetailDO.getOutboundCode());
            costSpuConfigAditReqVO.setRelatedOrderDetailId(erpOutboundDetailDO.getOutboundDetailId());
            costSpuConfigAditReqVO.setMaterialId(erpOutboundDetailDO.getMaterialId());
            costSpuConfigAditReqVO.setMaterialCode(erpOutboundDetailDO.getMaterialCode());
            costSpuConfigAditReqVO.setFormDt(erpOutboundDO.getFormDt());
            costSpuConfigAditReqVO.setOrderType(SpuConfigSourceOrderEnum.OUTBOUND.code);
            costSpuConfigList.add(costSpuConfigAditReqVO);
        }
        costSpuConfigService.costSpuConfigOperate(buttonType, costSpuConfigList);
    }

    /**
     * 验收上游单据状态
     * @param item 出库单信息
     * @param failItem 失败信息
     * @return
     */
    private boolean perDocVerify(ErpOutboundDO item, FailItem failItem) {
        Long relatedOrderId = item.getRelatedOrderId();
        //销售出库单
        if(item.getBizType() == ErpOutboundBizTypeEnum.SALE_OUTBOUND.getType() || item.getBizType() == ErpOutboundBizTypeEnum.SALE_DELIVERY_OUTBOUND.getType()) {
            ErpSaleOrderDO erpSaleOrderDO = erpSaleOrderMapper.selectById(relatedOrderId);
            if (erpSaleOrderDO == null) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售订单已删除,不可操作审核");
                return false;
            }
            if (erpSaleOrderDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售订单未审核,不可操作审核");
                return false;
            }

            if (erpSaleOrderDO.getFormStatus().equals(FormStatusEnum.COMPLETED.type) || erpSaleOrderDO.getFormStatus().equals(FormStatusEnum.CLOSED.type)){
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售订单不是未开始/进行中状态,不可操作审核");
                return false;
            }
        }

        //销售通知出库单
        if(item.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_OUTBOUND.getType() || item.getBizType() == ErpOutboundBizTypeEnum.SALE_NOTICE_DELIVERY_OUTBOUND.getType()){
            SaleShipNoticeDO saleShipNoticeDO = saleShipNoticeMapper.selectById(relatedOrderId);
            if (saleShipNoticeDO == null) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售发货通知单已删除,不可操作审核");
                return false;
            }
            if (saleShipNoticeDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售发货通知单未审核,不可操作审核");
                return false;
            }
        }

        //采购退货出库单
        if(item.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_RETURN_OUTBOUND.getType()){
            PurchaseReturnDO purchaseReturnDO = purchaseReturnMapper.selectById(relatedOrderId);
            if (purchaseReturnDO == null) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的采购退货单已删除,不可操作审核");
                return false;
            }
            if (purchaseReturnDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的采购退货单未审核,不可操作审核");
                return false;
            }
        }

        //工单领料出库单和委外订单领料出库单
        if (item.getBizType() == ErpOutboundBizTypeEnum.PROD_WORK_PICKING_OUTBOUND.getType()
                || item.getBizType() == ErpOutboundBizTypeEnum.OUTSOURCE_PICKING_OUTBOUND.getType()) {
            WorkPickingDO workPickingDO = workPickingMapper.selectById(relatedOrderId);
            if (workPickingDO == null) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的领料单已删除,不可操作审核");
                return false;
            }
            if (workPickingDO.getDataStatus() != DataButtonEnum.APPROVE.getKey()) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的领料单未审核,不可操作审核");
                return false;
            }
        }

        //销售换货出库单
        if (item.getBizType() == ErpOutboundBizTypeEnum.SALE_EXCHANGE_OUTBOUND.getType()){
            SaleExchangeDO saleExchangeDO = saleExchangeMapper.selectById(relatedOrderId);
            if (saleExchangeDO == null) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售换货单已删除,不可操作审核");
                return false;
            }
            if(saleExchangeDO.getDataStatus() != DataButtonEnum.APPROVE.getKey().shortValue()){
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的销售换货单未审核,不可操作审核");
                return false;
            }
        }

        //采购换货出库单
        if (item.getBizType() == ErpOutboundBizTypeEnum.PURCHASE_EXCHANGE_OUTBOUND.getType()){
            PurchaseExchangeDO purchaseExchangeDO = purchaseExchangeMapper.selectById(relatedOrderId);
            if (purchaseExchangeDO == null) {
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的采购换货单已删除,不可操作审核");
                return false;
            }
            if(purchaseExchangeDO.getDataStatus() != DataButtonEnum.APPROVE.getKey().shortValue()){
                failItem.setCode(item.getOutboundCode());
                failItem.setReason("关联的采购换货单未审核,不可操作审核");
                return false;
            }
        }

        return true;
    }


    /**
     * 合并转换Map
     * @param outboundRespList
     */
    private Map<Long, DocumentRespBO> mergeConvertMap(List<ErpOutboundDetailDO> outboundRespList) {
        Map<Long, DocumentRespBO> outboundQtyMap = new HashMap<>();
        //合并数量
        for(ErpOutboundDetailDO detailDO : outboundRespList){
            Long relatedOrderDetailId = detailDO.getRelatedOrderDetailId();
            DocumentRespBO documentRespBO =  outboundQtyMap.get(relatedOrderDetailId);
            if(documentRespBO == null ){
                documentRespBO = new DocumentRespBO();
                documentRespBO.setFieldId(relatedOrderDetailId);
                documentRespBO.setCode(detailDO.getMaterialCode());
                documentRespBO.setSumQty(detailDO.getOutboundQty());
                outboundQtyMap.put(relatedOrderDetailId, documentRespBO);
            }else{
                documentRespBO.setSumQty(documentRespBO.getSumQty().add(detailDO.getOutboundQty()));
                outboundQtyMap.put(relatedOrderDetailId, documentRespBO);
            }
        }
        return outboundQtyMap;
    }


    //新增日成本销售收入单
    private void costSaleIncomeAdd(ErpOutboundDetailDO outboundDetailDO,ErpOutboundDO erpOutboundDO) {
        CostSaleIncomeAditReqVO reqVO = new CostSaleIncomeAditReqVO();
        reqVO.setRelatedUpFormCode(erpOutboundDO.getOutboundCode());
        reqVO.setRelatedUpFormId(erpOutboundDO.getOutboundId());
        reqVO.setOutboundTypeDictId(erpOutboundDO.getOutboundTypeDictId());
        reqVO.setFormDt(erpOutboundDO.getFormDt());
        reqVO.setOrderType((short) 0);
        ErpSaleOrderRespVO saleOrderRespVO = erpSaleOrderService.erpSaleOrderDetail(erpOutboundDO.getSaleOrderId());
        reqVO.setRelatedOrderCode(saleOrderRespVO.getSaleOrderCode());
        if (saleOrderRespVO != null) {
            reqVO.setUndertakeOrgId(saleOrderRespVO.getCompanyOrgId()+"");
        }
        reqVO.setRelatedOrderId(erpOutboundDO.getSaleOrderId());
        reqVO.setRelatedRowNo(outboundDetailDO.getRowNo());
        reqVO.setMaterialId(outboundDetailDO.getMaterialId());
        reqVO.setMaterialCode(outboundDetailDO.getMaterialCode());
        ErpSaleOrderDetailDO saleOrderDetailDO = erpSaleOrderDetailService.erpSaleOrderDetailDetail(outboundDetailDO.getRelatedOrderDetailId());
        reqVO.setQty(outboundDetailDO.getOutboundQty());
        if (saleOrderDetailDO != null) {
            reqVO.setExclTaxUnitPrice(saleOrderDetailDO.getExclTaxPrice());
            reqVO.setTotalAmt(MathUtilX.getAmt(saleOrderDetailDO.getExclTaxPrice(), outboundDetailDO.getOutboundQty()));
        }
        costSaleIncomeService.costSaleIncomeAdd(reqVO);
    }

    private void saleOrderAddGrant(ErpOutboundDO erpOutboundDO,List<ErpOutboundDetailDO> outboundRespList) {
        SaleOrderAddGrantReqVO reqVO = new SaleOrderAddGrantReqVO();
        reqVO.setSourceOrderId(erpOutboundDO.getOutboundId());
        reqVO.setSourceOrderCode(erpOutboundDO.getOutboundCode());
        reqVO.setSaleOrderId(erpOutboundDO.getSaleOrderId());
        reqVO.setFormType(erpOutboundDO.getBizType() == 1 ? 0 : 1);
        reqVO.setFormDt(erpOutboundDO.getFormDt());
        reqVO.setOutboundDetailList(outboundRespList);
        commissionGrantService.saleOrderAddGrant(reqVO);
    }


}