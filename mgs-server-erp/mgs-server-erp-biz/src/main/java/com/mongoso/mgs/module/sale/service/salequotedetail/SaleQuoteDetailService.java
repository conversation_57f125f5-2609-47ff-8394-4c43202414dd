package com.mongoso.mgs.module.sale.service.salequotedetail;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.sale.controller.admin.salequotedetail.vo.*;
import com.mongoso.mgs.module.sale.dal.db.salequotedetail.SaleQuoteDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 销售报价明细 Service 接口
 *
 * <AUTHOR>
 */
public interface SaleQuoteDetailService {

    /**
     * 创建销售报价明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long saleQuoteDetailAdd(@Valid SaleQuoteDetailAditReqVO reqVO);

    /**
     * 更新销售报价明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long saleQuoteDetailEdit(@Valid SaleQuoteDetailAditReqVO reqVO);

    /**
     * 删除销售报价明细
     *
     * @param saleQuoteDetailId 编号
     */
    void saleQuoteDetailDel(Long saleQuoteDetailId);

    /**
     * 获得销售报价明细信息
     *
     * @param saleQuoteDetailId 编号
     * @return 销售报价明细信息
     */
    SaleQuoteDetailDO saleQuoteDetailDetail(Long saleQuoteDetailId);

    /**
     * 获得销售报价明细列表
     *
     * @param reqVO 查询条件
     * @return 销售报价明细列表
     */
    List<SaleQuoteDetailRespVO> saleQuoteDetailList(@Valid SaleQuoteDetailQueryReqVO reqVO);

    /**
     * 获得销售报价明细分页
     *
     * @param reqVO 查询条件
     * @return 销售报价明细分页
     */
    PageResult<SaleQuoteDetailDO> saleQuoteDetailPage(@Valid SaleQuoteDetailPageReqVO reqVO);

}
