package com.mongoso.mgs.module.dailycost.service.costprodlabor;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodlabor.vo.CostProdLaborAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodlabor.vo.CostProdLaborPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodlabor.vo.CostProdLaborQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodlabor.vo.CostProdLaborRespVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.AmortiseStatusReqVO;

import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseAditReqVO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产人工成本单 Service 接口
 *
 * <AUTHOR>
 */
public interface CostProdLaborService {

    /**
     * 创建生产人工成本单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costProdLaborAdd(@Valid CostProdLaborAditReqVO reqVO);

    /**
     * 更新生产人工成本单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long costProdLaborEdit(@Valid CostProdLaborAditReqVO reqVO);

    /**
     * 生产采购成本单修改承担物料信息
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long undertakeMaterialAdit(@Valid CostProdLaborAditReqVO reqVO);

    /**
     * 删除生产人工成本单
     *
     * @param costProdLaborId 编号
     */
    void costProdLaborDel(Long costProdLaborId);

    /**
     *
     * 报工记录ID删除生产物料成本单
     *
     * @param reportedWorkId 编号
     */
    void reportedWorkIdDel(Long reportedWorkId);

    /**
     * 获得生产人工成本单信息
     *
     * @param costProdLaborId 编号
     * @return 生产人工成本单信息
     */
    CostProdLaborRespVO costProdLaborDetail(Long costProdLaborId);

    /**
     * 获得生产人工成本单列表
     *
     * @param reqVO 查询条件
     * @return 生产人工成本单列表
     */
    List<CostProdLaborRespVO> costProdLaborList(@Valid CostProdLaborQueryReqVO reqVO);

    /**
     * 获得生产人工成本单分页
     *
     * @param reqVO 查询条件
     * @return 生产人工成本单分页
     */
    PageResult<CostProdLaborRespVO> costProdLaborPage(@Valid CostProdLaborPageReqVO reqVO);

    /**
     * 摊销状态变更
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    BatchResult costProdLaborAmortise(@Valid AmortiseStatusReqVO reqVO);

}
