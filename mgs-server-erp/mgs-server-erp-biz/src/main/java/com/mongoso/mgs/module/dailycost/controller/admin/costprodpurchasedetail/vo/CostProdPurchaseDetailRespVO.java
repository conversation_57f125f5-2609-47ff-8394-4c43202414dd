package com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchasedetail.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 生产采购成本明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CostProdPurchaseDetailRespVO extends CostProdPurchaseDetailBaseVO {

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    private String relatedOrderCode;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料SPU编码 */
    private String spuCode;

    /** 物料SPUId */
    private Long spuId;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料来源 */
    private Integer materialSourceDictId;
    private String materialSourceDictName;

    /** 规格属性 */
    private String specAttributeStr;

    /** 工序id */
    private Long processId;

    /** 工序名称 */
    private String processName;

    /** 数量 */
    private BigDecimal qty;

    /** 单价(不含税) */
    private BigDecimal exclTaxUnitPrice;

    /** 承担物料编码 */
    private String undertakeMaterialCode;
    private String undertakeMaterialName;

}
