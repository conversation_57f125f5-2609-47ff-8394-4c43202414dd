package com.mongoso.mgs.module.finance.controller.admin.shouldpayment;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.shouldpayment.vo.*;
import com.mongoso.mgs.module.finance.service.shouldpayment.ShouldPaymentService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 应收付账款 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/should")
@Validated
public class ShouldPaymentController {

    @Resource
    private ShouldPaymentService paymentService;

    @OperateLog("应收付账款添加或编辑")
    @PostMapping("/shouldPaymentAdit")
    @PreAuthorize("@ss.hasPermission('shouldPayment:adit')")
    public ResultX<Long> shouldPaymentAdit(@Valid @RequestBody ShouldPaymentAditReqVO reqVO) {
        return success(reqVO.getPaymentId() == null
                            ? paymentService.shouldPaymentAdd(reqVO)
                            : paymentService.shouldPaymentEdit(reqVO));
    }

    @OperateLog("应收账款删除")
    @PostMapping("/shouldPaymentDel")
    @PreAuthorize("@ss.hasPermission('shouldPayment:delete')")
    public ResultX<Boolean> shouldPaymentDel(@Valid @RequestBody ShouldPaymentPrimaryReqVO reqVO) {
        paymentService.shouldPaymentDel(reqVO.getPaymentId());
        return success(true);
    }

    @OperateLog("应收账款批量删除")
    @PostMapping("/shouldPaymentDelBatch")
    @PreAuthorize("@ss.hasPermission('shouldPayment:delete')")
    public ResultX<BatchResult> shouldPaymentDelBatch(@Valid @RequestBody IdReq reqVO) {
        return paymentService.shouldPaymentDelBatch(reqVO);
    }

    @OperateLog("应收付账款详情")
    @PostMapping("/shouldPaymentDetail")
    @PreAuthorize("@ss.hasPermission('shouldPayment:query')")
    public ResultX<ShouldPaymentRespVO> shouldPaymentDetail(@Valid @RequestBody ShouldPaymentPrimaryReqVO reqVO) {
        return success(paymentService.shouldPaymentDetail(reqVO.getPaymentId()));
    }

    @OperateLog("应收付账款列表")
    @PostMapping("/shouldPaymentList")
    @PreAuthorize("@ss.hasPermission('shouldPayment:query')")
    @DataPermission
    public ResultX<List<ShouldPaymentRespVO>> shouldPaymentList(@Valid @RequestBody ShouldPaymentQueryReqVO reqVO) {
        return success(paymentService.shouldPaymentList(reqVO));
    }

    @OperateLog("应收付账款分页")
    @PostMapping("/shouldPaymentPage")
    @PreAuthorize("@ss.hasPermission('shouldPayment:query')")
    @DataPermission
    public ResultX<PageResult<ShouldPaymentRespVO>> shouldPaymentPage(@Valid @RequestBody ShouldPaymentPageReqVO reqVO) {
        return success(paymentService.shouldPaymentPage(reqVO));
    }

    @OperateLog("应收付账款列表")
    @PostMapping("/shouldPaymentQuoteList")
    @PreAuthorize("@ss.hasPermission('shouldPayment:query')")
    public ResultX<List<ShouldPaymentRespVO>> shouldPaymentQuoteList(@Valid @RequestBody ShouldPaymentQueryReqVO reqVO) {
        return success(paymentService.shouldPaymentList(reqVO));
    }

    @OperateLog("应收付账款分页")
    @PostMapping("/shouldPaymentQuotePage")
    @PreAuthorize("@ss.hasPermission('shouldPayment:query')")
    public ResultX<PageResult<ShouldPaymentRespVO>> shouldPaymentQuotePage(@Valid @RequestBody ShouldPaymentPageReqVO reqVO) {
        return success(paymentService.shouldPaymentPage(reqVO));
    }

    @OperateLog("应收付账款审核")
    @PostMapping("/shouldPaymentApprove")
    @PreAuthorize("@ss.hasPermission('shouldPayment:adit')")
    public ResultX<BatchResult> containerApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = paymentService.shouldPaymentApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("应收付账款回调接口")
    @PostMapping("/shouldPaymentFlowCallback")
    public ResultX<Object> containerFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(paymentService.shouldPaymentFlowCallback(reqVO));
    }
}
