package com.mongoso.mgs.module.produce.controller.admin.flowprocess;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.flowprocess.vo.*;
import com.mongoso.mgs.module.produce.service.flowprocess.FlowProcessService;

/**
 * 工艺路线 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class FlowProcessController {

    @Resource
    private FlowProcessService flowProcessService;

    @OperateLog("工艺路线添加或编辑")
    @PostMapping("/flowProcessAdit")
    @PreAuthorize("@ss.hasPermission('flowProcess:adit')")
    public ResultX<Long> flowProcessAdit(@Valid @RequestBody FlowProcessAditReqVO reqVO) {
        return success(reqVO.getFlowProcessId() == null
                            ? flowProcessService.flowProcessAdd(reqVO)
                            : flowProcessService.flowProcessEdit(reqVO));
    }

    @OperateLog("工艺路线删除")
    @PostMapping("/flowProcessDel")
    @PreAuthorize("@ss.hasPermission('flowProcess:delete')")
    public ResultX<Boolean> flowProcessDel(@Valid @RequestBody FlowProcessPrimaryReqVO reqVO) {
        flowProcessService.flowProcessDel(reqVO.getFlowProcessId());
        return success(true);
    }

    @OperateLog("工艺路线详情")
    @PostMapping("/flowProcessDetail")
    @PreAuthorize("@ss.hasPermission('flowProcess:query')")
    public ResultX<FlowProcessRespVO> flowProcessDetail(@Valid @RequestBody FlowProcessPrimaryReqVO reqVO) {
        return success(flowProcessService.flowProcessDetail(reqVO.getFlowProcessId()));
    }

    @DataPermission
    @OperateLog("工艺路线列表")
    @PostMapping("/flowProcessList")
    @PreAuthorize("@ss.hasPermission('flowProcess:query')")
    public ResultX<List<FlowProcessRespVO>> flowProcessList(@Valid @RequestBody FlowProcessQueryReqVO reqVO) {
        return success(flowProcessService.flowProcessList(reqVO));
    }

    @DataPermission
    @OperateLog("工艺路线分页")
    @PostMapping("/flowProcessPage")
    @PreAuthorize("@ss.hasPermission('flowProcess:query')")
    public ResultX<PageResult<FlowProcessRespVO>> flowProcessPage(@Valid @RequestBody FlowProcessPageReqVO reqVO) {
        return success(flowProcessService.flowProcessPage(reqVO));
    }

    @OperateLog("工艺路线删除")
    @PostMapping("/flowProcessDelBatch")
    @PreAuthorize("@ss.hasPermission('device:del')")
    public ResultX<BatchResult> flowProcessDelBatch(@Valid @RequestBody IdReq reqVO) {
        return flowProcessService.flowProcessDelBatch(reqVO);
    }

    @OperateLog("工艺路线审核")
    @PostMapping("/flowProcessApprove")
    @PreAuthorize("@ss.hasPermission('device:adit')")
    public ResultX<BatchResult> flowProcessApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = flowProcessService.flowProcessApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("工艺路线回调接口")
    @PostMapping("/flowProcessFlowCallback")
    public ResultX<Object> flowProcessFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(flowProcessService.flowProcessFlowCallback(reqVO));
    }

    @OperateLog("查询工艺路线下的物料")
    @PostMapping("/flowProcessMaterialList")
    @PreAuthorize("@ss.hasPermission('flowProcess:query')")
    public ResultX<Set<Long>> flowProcessMaterialList(@Valid @RequestBody FlowProcessQueryReqVO reqVO) {
        return success(flowProcessService.flowProcessMaterialList(reqVO));
    }

    @OperateLog("查询物料的工艺路线")
    @PostMapping("/flowProcessByMaterial")
    @PreAuthorize("@ss.hasPermission('flowProcess:query')")
    public ResultX<FlowProcessRespVO> flowProcessByMaterial(@RequestBody FlowProcessQueryReqVO reqVO) {
        return success(flowProcessService.flowProcessByMaterial(reqVO.getMaterialId()));
    }

}
