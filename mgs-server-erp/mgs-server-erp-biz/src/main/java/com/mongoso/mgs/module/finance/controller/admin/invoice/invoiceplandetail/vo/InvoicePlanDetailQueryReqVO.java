package com.mongoso.mgs.module.finance.controller.admin.invoice.invoiceplandetail.vo;

import lombok.*;
import com.mongoso.mgs.framework.common.domain.CommonParam;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
 import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 开票计划明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class InvoicePlanDetailQueryReqVO extends CommonParam{

    /** 开票计划明细id */
    private Long invoicePlanDetailId;

    /** 开票方向 */
    private Short billingDirection;

    /** 开票计划id */
    private Long invoicePlanId;

    /** 待开票计划明细id */
    private Long invoicePendingPlanDetailId;

    /** 行号 */
    private Long rowNo;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 来源单行号 */
    private Short sourceLineNumber;

    /** 物料id */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private Integer mainUnitDictId;

    /** 计划开票数量 */
    private BigDecimal planInvoiceQty;

    /** 计划开票金额 */
    private BigDecimal planInvoiceAmt;

    /** 行金额(含税) */
    private BigDecimal inclTaxAmt;

    /** 行金额(不含税) */
    private BigDecimal exclTaxAmt;

    /** 数量 */
    private BigDecimal qty;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

    /** 单价(含税） */
    private BigDecimal inclTaxUnitPrice;

    /** 单价(不含税） */
    private BigDecimal exclTaxUnitPrice;

    /** 票据类型 */
    private Long invoiceTypeDictId;
    private String invoiceTypeDictName;

    /** 税率 */
    private BigDecimal taxRate;

    private BigDecimal taxAmt;
}
