package com.mongoso.mgs.module.sale.dal.db.materialprice;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 商品价格 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_material_price", autoResultMap = true)
//@KeySequence("u_material_price_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPriceDO extends OperateDO {

    /** 主键 */
        @TableId(type = IdType.ASSIGN_ID)
    private Long materialPriceId;

    /** 创建人ID */
    private Long createdId;

    /** 备注 */
    private String remark;

    /** 客户订货价 */
    private BigDecimal customerOrderPrice;

    /** 物料ID */
    private Long materialId;

    /** 关联单据ID **/
    private Long relatedOrderId;


}
