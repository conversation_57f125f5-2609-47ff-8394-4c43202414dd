package com.mongoso.mgs.module.finance.dal.db.cashbank.endorsetransfer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 背书转让信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_endorse_transfer", autoResultMap = true)
//@KeySequence("erp.u_endorse_transfer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EndorseTransferDO extends OperateDO {

    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long endorseTransferId;

    /** 承兑汇票ID */
    private Long acceptBillId;

    /** 子票区间上限 */
    private Long subTicketRangeUpper;

    /** 子票区间下限 */
    private Long subTicketRangeLower;

    /** 被背书人ID */
    private Long endorseeId;

    /** 背书人 */
    private String endorserName;

    /** 背书金额 */
    private BigDecimal endorseAmt;

    /** 背书日期 */
    private LocalDate endorseDate;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;


}
