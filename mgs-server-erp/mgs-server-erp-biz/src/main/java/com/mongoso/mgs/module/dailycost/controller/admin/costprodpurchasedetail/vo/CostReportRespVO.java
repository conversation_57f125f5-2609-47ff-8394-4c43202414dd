package com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchasedetail.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 生产采购成本报表
 *
 * <AUTHOR>
 */
@Data
public class CostReportRespVO implements Serializable {

    /** 时间 */
    private String dateStr;
    private String dateRange;
    private String weekStartDate;
    private String weekEndDate;

    private Long materialId;

    private String materialCode;
    private String materialName;

    private String undertakeOrgId;
    private String undertakeOrgName;

    /** 数量 */
    private BigDecimal purchaseQty;

    /** 总金额 */
    private BigDecimal purchaseAmt;

    private BigDecimal diffAmt;

    /** 数量 */
    private BigDecimal incomeQty;

    /** 总金额 */
    private BigDecimal incomeAmt;

    private Long undertakeMaterialId;

    private String undertakeMaterialCode;
    private String undertakeMaterialName;

}
