package com.mongoso.mgs.module.comp.company.controller.admin.compensationsetting;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.comp.company.controller.admin.compensationsetting.vo.*;
import com.mongoso.mgs.module.comp.company.dal.db.compensationsetting.CompanyCompensationSettingDO;
import com.mongoso.mgs.module.comp.company.service.compensationsetting.CompanyCompensationSettingService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 公司薪酬配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/compensation")
@Validated
public class CompanyCompensationSettingController {

    @Resource
    private CompanyCompensationSettingService compensationSettingService;

    @OperateLog("公司薪酬配置添加或编辑")
    @PostMapping("/compensationSettingAdit")
    @PreAuthorize("@ss.hasPermission('compensationSetting:adit')")
    public ResultX<Long> compensationSettingAdit(@Valid @RequestBody CompanyCompensationSettingAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? compensationSettingService.compensationSettingAdd(reqVO)
                            : compensationSettingService.compensationSettingEdit(reqVO));
    }

    @OperateLog("公司薪酬配置删除")
    @PostMapping("/compensationSettingDelete")
    @PreAuthorize("@ss.hasPermission('compensationSetting:del')")
    public ResultX<Boolean> compensationSettingDel(@Valid @RequestBody CompanyCompensationSettingPrimaryReqVO reqVO) {
        compensationSettingService.compensationSettingDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("公司薪酬配置详情")
    @PostMapping("/compensationSettingDetail")
    @PreAuthorize("@ss.hasPermission('compensationSetting:query')")
    public ResultX<CompanyCompensationSettingRespVO> compensationSettingDetail(@Valid @RequestBody CompanyCompensationSettingPrimaryReqVO reqVO) {
        CompanyCompensationSettingDO oldDO = compensationSettingService.compensationSettingDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, CompanyCompensationSettingRespVO::new));
    }

    @OperateLog("公司薪酬配置列表")
    @PostMapping("/compensationSettingList")
    @PreAuthorize("@ss.hasPermission('compensationSetting:query')")
    public ResultX<List<CompanyCompensationSettingRespVO>> compensationSettingList(@Valid @RequestBody CompanyCompensationSettingQueryReqVO reqVO) {
        List<CompanyCompensationSettingDO> list = compensationSettingService.compensationSettingList(reqVO);
        return success(BeanUtilX.copyList(list, CompanyCompensationSettingRespVO::new));
    }

    @OperateLog("公司薪酬配置分页")
    @PostMapping("/compensationSettingPage")
    @PreAuthorize("@ss.hasPermission('compensationSetting:query')")
    public ResultX<PageResult<CompanyCompensationSettingRespVO>> compensationSettingPage(@Valid @RequestBody CompanyCompensationSettingPageReqVO reqVO) {
        return success(compensationSettingService.compensationSettingPage(reqVO));
    }

}
