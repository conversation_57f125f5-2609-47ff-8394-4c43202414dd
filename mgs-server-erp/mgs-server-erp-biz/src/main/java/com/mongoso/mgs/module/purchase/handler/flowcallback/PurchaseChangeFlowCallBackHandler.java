package com.mongoso.mgs.module.purchase.handler.flowcallback;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.purchasechange.PurchaseChangeDO;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： ZhouYangqing
 * @date： 2024/12/11
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurchaseChangeFlowCallBackHandler extends FlowCallbackHandler<PurchaseChangeDO> {


    protected PurchaseChangeFlowCallBackHandler(FlowApproveHandler<PurchaseChangeDO> approveHandler) {
        super(approveHandler);
    }
}
