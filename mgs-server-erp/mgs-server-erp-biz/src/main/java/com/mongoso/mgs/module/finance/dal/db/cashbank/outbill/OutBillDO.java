package com.mongoso.mgs.module.finance.dal.db.cashbank.outbill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 直接出账 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_out_bill", autoResultMap = true)
//@KeySequence("erp.u_out_bill_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutBillDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long outBillId;

    /** 出账编码 */
    private String outBillCode;

    /** 出账标题 */
    private String outBillName;

    /** 出账账户ID */
    private Long outBillAccountId;

    /** 可用余额 */
    private BigDecimal availableBalance;

    /** 出账金额 */
    private BigDecimal outBillAmount;

    /** 关联客户ID */
    private Long relatedCustomerId;

    /** 币种 */
    private String currencyDictName;

    /** 备注 */
    private String remark;

    /** 出账分类ID */
    private String outBillCategoryId;

    /** 出账日期 */
    private LocalDate outBillDate;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Integer dataStatus;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 审核人 */
    private String approvedBy;

    /** 版本号 */
//    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币出账金额 */
    private BigDecimal localCurrencyOutBillAmount;

}
