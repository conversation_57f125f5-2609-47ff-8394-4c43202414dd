package com.mongoso.mgs.module.finance.handler.flowCallback.asset;

import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.finance.dal.db.asset.assetchange.AssetChangeDO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetregister.AssetRegisterDO;
import org.springframework.stereotype.Component;



@Component
public class AssetChangeFlowCallBackHandler extends FlowCallbackHandler<AssetChangeDO> {
    protected AssetChangeFlowCallBackHandler(FlowApproveHandler<AssetChangeDO> approveHandler) {
        super(approveHandler);
    }
}