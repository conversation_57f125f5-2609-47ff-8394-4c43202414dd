package com.mongoso.mgs.module.warehouse.controller.admin.materialcheckngdetail.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
import java.math.BigDecimal;
  import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 检验单不良品明细 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialCheckNgDetailPageReqVO extends PageParam {

    /** 关联单ID */
    private Long relatedOrderId;

    /** 行号 */
    private Integer rowNo;

    /** 不良品总数 */
    private BigDecimal ngQty;

    /** 不良原因 */
    private String ngReasonDictId;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 创建人ID */
    private Long createdId;

    /** 版本号 */
    private Integer version;

}
