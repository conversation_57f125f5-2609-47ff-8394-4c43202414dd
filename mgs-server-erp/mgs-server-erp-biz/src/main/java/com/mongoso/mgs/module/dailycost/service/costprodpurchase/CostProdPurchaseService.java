package com.mongoso.mgs.module.dailycost.service.costprodpurchase;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产采购成本单 Service 接口
 *
 * <AUTHOR>
 */
public interface CostProdPurchaseService {

    /**
     * 创建生产采购成本单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costProdPurchaseAdd(@Valid CostProdPurchaseAditReqVO reqVO);

    /**
     * 更新生产采购成本单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long costProdPurchaseEdit(@Valid CostProdPurchaseAditReqVO reqVO);

    /**
     * 生产采购成本单修改承担物料信息
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long undertakeMaterialAdit(@Valid CostProdPurchaseAditReqVO reqVO);


    /**
     * 更新生产采购成本单
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    void relatedOrderIdEdit(@Valid List<CostProdPurchaseAditReqVO> aditReqVOList);

    /**
     * 删除生产采购成本单
     *
     * @param costProdPurchaseId 编号
     */
    void costProdPurchaseDel(Long costProdPurchaseId);

    /**
     * 删除生产采购成本单
     *
     * @param relatedOrderId 编号
     */
    void costRelatedOrderIdDel(Long relatedOrderId);

    /**
     * 获得生产采购成本单信息
     *
     * @param costProdPurchaseId 编号
     * @return 生产采购成本单信息
     */
    CostProdPurchaseRespVO costProdPurchaseDetail(Long costProdPurchaseId);

    /**
     * 获得生产采购成本单列表
     *
     * @param reqVO 查询条件
     * @return 生产采购成本单列表
     */
    List<CostProdPurchaseRespVO> costProdPurchaseList(@Valid CostProdPurchaseQueryReqVO reqVO);

    /**
     * 获得生产采购成本单分页
     *
     * @param reqVO 查询条件
     * @return 生产采购成本单分页
     */
    PageResult<CostProdPurchaseRespVO> costProdPurchasePage(@Valid CostProdPurchasePageReqVO reqVO);

    /**
     * 摊销状态变更
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    BatchResult amortiseStatusChange(@Valid AmortiseStatusReqVO reqVO);

    /**
     * 查询订单统计数据
     * @param
     * @return 统计数据
     *
     */
    CostProdPurchaseOrderRespVO queryOrderStatData();

}
