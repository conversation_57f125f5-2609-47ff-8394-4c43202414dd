package com.mongoso.mgs.module.dailycost.service.costprodpurchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.enums.dailycost.AmortiseEnum;
import com.mongoso.mgs.common.enums.material.MaterialEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.*;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchasedetail.vo.CostProdPurchaseDetailAditReqVO;
import com.mongoso.mgs.module.dailycost.dal.db.costaggreoriginalorder.CostAggreOriginalOrderDO;
import com.mongoso.mgs.module.dailycost.dal.db.costprodpurchase.CostProdPurchaseDO;
import com.mongoso.mgs.module.dailycost.dal.mysql.costaggreorder.CostAggreOriginalOrderMapper;
import com.mongoso.mgs.module.dailycost.dal.mysql.costprodpurchase.CostProdPurchaseMapper;
import com.mongoso.mgs.module.dailycost.enums.AmortiseOrderTypeEnum;
import com.mongoso.mgs.module.dailycost.service.costprodlabor.CostProdLaborService;
import com.mongoso.mgs.module.dailycost.service.costprodmaterial.CostProdMaterialService;
import com.mongoso.mgs.module.dailycost.service.costprodpurchasedetail.CostProdPurchaseDetailService;
import com.mongoso.mgs.module.dailycost.service.costsaleincome.CostSaleIncomeService;
import com.mongoso.mgs.module.dailycost.service.spuconfig.CostSpuConfigService;
import com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.process.vo.ProcessRespVO;
import com.mongoso.mgs.module.produce.service.process.ProcessService;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictQueryReqVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
// import static com.mongoso.mgs.module.dailycost.enums.ErrorCodeConstants.*;


/**
 * 生产采购成本单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CostProdPurchaseServiceImpl implements CostProdPurchaseService {

    @Resource
    private CostProdPurchaseMapper costProdPurchaseMapper;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private ProcessService processService;

    @Resource
    @Lazy
    private CostProdPurchaseDetailService costProdPurchaseDetailService;

    @Resource
    private CostProdMaterialService costProdMaterialService;

    @Resource
    private CostProdLaborService costProdLaborService;

    @Resource
    private CostSpuConfigService costSpuConfigService;

    @Resource
    private CostSaleIncomeService costSaleIncomeService;

    @Resource
    private CostAggreOriginalOrderMapper aggreOriginalOrderMapper;

    @Resource
    private ERPMaterialService erpMaterialService;

    @Override
    public Long costProdPurchaseAdd(CostProdPurchaseAditReqVO reqVO) {
        // 插入
        CostProdPurchaseDO costProdPurchase = BeanUtilX.copy(reqVO, CostProdPurchaseDO::new);
        costProdPurchaseMapper.insert(costProdPurchase);
        // 返回
        return costProdPurchase.getCostProdPurchaseId();
    }

    @Override
    public Long costProdPurchaseEdit(CostProdPurchaseAditReqVO reqVO) {
        // 校验存在
        this.costProdPurchaseValidateExists(reqVO.getCostProdPurchaseId());
        // 更新
        CostProdPurchaseDO costProdPurchase = BeanUtilX.copy(reqVO, CostProdPurchaseDO::new);
        costProdPurchaseMapper.updateById(costProdPurchase);
        // 返回
        return costProdPurchase.getCostProdPurchaseId();
    }

    @Override
    public Long undertakeMaterialAdit(CostProdPurchaseAditReqVO reqVO) {
        // 校验存在
        this.costProdPurchaseValidateExists(reqVO.getCostProdPurchaseId());
        // 更新
        CostProdPurchaseDO costProdPurchase = BeanUtilX.copy(reqVO, CostProdPurchaseDO::new);
        costProdPurchaseMapper.updateByundertakeMaterial(costProdPurchase);
        // 返回
        return costProdPurchase.getCostProdPurchaseId();
    }

    @Override
    @Transactional
    public void relatedOrderIdEdit(List<CostProdPurchaseAditReqVO> aditReqVOList) {
        CostProdPurchaseQueryReqVO queryReqVO = new CostProdPurchaseQueryReqVO();
        queryReqVO.setRelatedOrderId(aditReqVOList.get(0).getRelatedOrderId());
        List<CostProdPurchaseRespVO> purchaseRespVOList = costProdPurchaseMapper.selectReqList(queryReqVO);

        //相等修改
        for (CostProdPurchaseAditReqVO item : aditReqVOList){
            for (CostProdPurchaseRespVO sitem : purchaseRespVOList){
                if (item.getRelatedOrderDetailId().longValue() == sitem.getRelatedOrderDetailId().longValue()){
                    costProdPurchaseMapper.updateByRelatedOrderDetailId(item);
                    CostProdPurchaseDetailAditReqVO detailAditReqVO = new CostProdPurchaseDetailAditReqVO();
                    detailAditReqVO.setCostProdPurchaseId(sitem.getCostProdPurchaseId());
                    detailAditReqVO.setDetailType(sitem.getMaterialSourceDictId() == MaterialEnum.OUTSOURCE.key ? (short) 1 : (short) 0);
                    detailAditReqVO.setTotalAmt(item.getQty().multiply(item.getExclTaxUnitPrice()));
                    costProdPurchaseDetailService.costProdPurchaseIdEdit(detailAditReqVO);
                }
            }
        }

        //关联表不存在于待摊销生产成本就新增
        List<CostProdPurchaseAditReqVO> filteredList = aditReqVOList.stream()
                .filter(aditReq ->
                        purchaseRespVOList.stream()
                                .noneMatch(purchaseResp ->
                                        purchaseResp.getRelatedOrderDetailId().equals(aditReq.getRelatedOrderDetailId())
                                )
                ).collect(Collectors.toList());
        for (CostProdPurchaseAditReqVO item : filteredList){
            this.costProdPurchaseAdd(item);
        }

        //待摊销生产成本不存在于关联表就删除
        List<CostProdPurchaseRespVO> purchaseList = purchaseRespVOList.stream()
                .filter(aditReq ->
                        aditReqVOList.stream()
                                .noneMatch(purchaseResp ->
                                        purchaseResp.getRelatedOrderDetailId().equals(aditReq.getRelatedOrderDetailId())
                                )
                ).collect(Collectors.toList());
        for (CostProdPurchaseRespVO item : purchaseList) {
            costProdPurchaseMapper.deleteById(item.getCostProdPurchaseId());
            costProdPurchaseDetailService.costProdPurchaseIdDel(item.getCostProdPurchaseId());
        }

    }

    @Override
    public void costProdPurchaseDel(Long costProdPurchaseId) {
        // 校验存在
        this.costProdPurchaseValidateExists(costProdPurchaseId);
        // 删除
        costProdPurchaseMapper.deleteById(costProdPurchaseId);
    }

    @Override
    @Transactional
    public void costRelatedOrderIdDel(Long relatedOrderId) {
        //删除明细表
        CostProdPurchaseQueryReqVO queryReqVO = new CostProdPurchaseQueryReqVO();
        queryReqVO.setRelatedOrderId(relatedOrderId);
        List<CostProdPurchaseRespVO> purchaseRespVOList = costProdPurchaseMapper.selectReqList(queryReqVO);
        for (CostProdPurchaseRespVO item : purchaseRespVOList){
            costProdPurchaseDetailService.costProdPurchaseIdDel(item.getCostProdPurchaseId());
        }
        // 删除
        costProdPurchaseMapper.deleteByRelatedOrderId(relatedOrderId);

    }

    private CostProdPurchaseDO costProdPurchaseValidateExists(Long costProdPurchaseId) {
        CostProdPurchaseDO costProdPurchase = costProdPurchaseMapper.selectById(costProdPurchaseId);
        if (costProdPurchase == null) {
            // throw exception(COST_PROD_PURCHASE_NOT_EXISTS);
            throw new BizException("5001", "生产采购成本单不存在");
        }
        return costProdPurchase;
    }

    @Override
    public CostProdPurchaseRespVO costProdPurchaseDetail(Long costProdPurchaseId) {
        CostProdPurchaseRespVO respVO = costProdPurchaseMapper.selectDetail(costProdPurchaseId,null);
        //VO属性填充
        fillVoProperties(respVO);

        return respVO;
    }

    @Override
    public List<CostProdPurchaseRespVO> costProdPurchaseList(CostProdPurchaseQueryReqVO reqVO) {
        List<CostProdPurchaseRespVO> respVOList = BeanUtilX.copy(costProdPurchaseMapper.selectReqList(reqVO), CostProdPurchaseRespVO :: new);
        //属性填充
        batchFillVoProperties(respVOList);

        return respVOList;
    }

    @Override
    public PageResult<CostProdPurchaseRespVO> costProdPurchasePage(CostProdPurchasePageReqVO reqVO) {
        IPage<CostProdPurchaseRespVO> paymentIPage = costProdPurchaseMapper.selectPage(PageUtilX.buildParam(reqVO),reqVO);
        System.out.println(paymentIPage);
        PageResult<CostProdPurchaseRespVO> pageResult = PageUtilX.buildResult(paymentIPage);
        if (CollUtilX.isEmpty(pageResult.getList())){
            return pageResult;
        }
        //属性填充
        batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    @Override
    public CostProdPurchaseOrderRespVO queryOrderStatData(){
        return costProdPurchaseMapper.queryOrderStatData();
    }

    @Override
    @Transactional
    public BatchResult amortiseStatusChange(AmortiseStatusReqVO reqVO) {
        //判断单个摊销和批量摊销
        BatchResult batchResult = new BatchResult();

        if (reqVO.getFormType() == AmortiseOrderTypeEnum.PROD_PURCHASE.code){
            batchResult = this.costProdPurchaseAmortise(reqVO);
        }else if (reqVO.getFormType() == AmortiseOrderTypeEnum.PROD_MATERIAL.code){
            batchResult = costProdMaterialService.costProdMaterialAmortise(reqVO);
        }else if (reqVO.getFormType() == AmortiseOrderTypeEnum.PROD_LABOR.code){
            batchResult = costProdLaborService.costProdLaborAmortise(reqVO);
        }else if (reqVO.getFormType() == AmortiseOrderTypeEnum.SALE_INCOME.code){
            batchResult = costSaleIncomeService.costSaleIncomeAmortise(reqVO);
        }
        return batchResult;
    }
    private BatchResult costProdPurchaseAmortise(AmortiseStatusReqVO reqVO) {
        BatchResult batchResult = new BatchResult();
        //判断单个摊销和批量摊销
        if (reqVO.getId() != null) {
            batchResult = handleSingleAmortise(reqVO);
        } else {
            batchResult = handleBatchAmortise(reqVO);
        }
        return batchResult;
    }



    private BatchResult handleSingleAmortise(AmortiseStatusReqVO reqVO) {
        //判断摊销和无效摊销
        BatchResult batchResult = new BatchResult();
        CostProdPurchaseRespVO costProdPurchaseRespVO = this.costProdPurchaseDetail(reqVO.getId());
        if (reqVO.getAmortiseStatus().intValue() == AmortiseEnum.AMORTISE.key){
            this.costProdPurchaseOneCheck(reqVO, costProdPurchaseRespVO);
        }else if (reqVO.getAmortiseStatus().intValue() == AmortiseEnum.NOT_AMORTISE.key){
            costProdPurchaseDetailService.costProdPurchaseIdDel(reqVO.getId());
        }
        if (reqVO.getAmortiseStatus() == AmortiseEnum.NO_AMORTISE.key && costProdPurchaseRespVO.getAmortiseStatus() != AmortiseEnum.NOT_AMORTISE.key){
            throw new BizException("5001", "摊销状态是未摊销才可以无需摊销");
        }
        CostProdPurchaseDO costProdPurchase = new CostProdPurchaseDO();
        costProdPurchase.setCostProdPurchaseId(reqVO.getId());
        costProdPurchase.setAmortiseStatus(reqVO.getAmortiseStatus());
        costProdPurchaseMapper.updateById(costProdPurchase);
        return batchResult;
    }

    private BatchResult handleBatchAmortise(AmortiseStatusReqVO reqVO) {
        BatchResult batchResult = new BatchResult();
        List<FailItem> failItemList = new ArrayList<>();
        //批量摊销校验
        if (reqVO.getAmortiseStatus().intValue() == AmortiseEnum.AMORTISE.key){
            CostProdPurchaseQueryReqVO queryReqVO = new CostProdPurchaseQueryReqVO();
            queryReqVO.setCostProdPurchaseIdList(reqVO.getIdList());
            List<CostProdPurchaseRespVO> list = this.costProdPurchaseList(queryReqVO);
            List<Long> isSupCode = new ArrayList<>();
            for (CostProdPurchaseRespVO item : list) {
                //实时查当前摊销状态是不是未摊销
                if (item.getAmortiseStatus() != AmortiseEnum.NOT_AMORTISE.key) {
                    FailItem failItem = new FailItem();
                    failItem.setCode(item.getRelatedOrderCode());
                    failItem.setReason("【行号" + item.getRelatedRowNo() + "】，摊销状态是未摊销才可以摊销");
                    failItemList.add(failItem);
                    continue;
                }
                if (item.getUndertakeMaterialId() == null){
                    FailItem failItem = new FailItem();
                    failItem.setCode(item.getRelatedOrderCode());
                    failItem.setReason("【行号" + item.getRelatedRowNo() + "】，未选择承担物料的不能进行摊销");
                    failItemList.add(failItem);
                    continue;
                }
//                if (StringUtils.isEmpty(item.getSpuCode())) {
//                    FailItem failItem = new FailItem();
//                    failItem.setCode(item.getRelatedOrderCode());
//                    failItem.setReason("【行号" + item.getRelatedRowNo() + "】，该单据没有spu编码");
//                    failItemList.add(failItem);
//                    continue;
//                }
                if (item.getExclTaxUnitPrice() == null) {
                    FailItem failItem = new FailItem();
                    failItem.setCode(item.getRelatedOrderCode());
                    failItem.setReason("【行号" + item.getRelatedRowNo() + "】，该单据没有单价，不能摊销");
                    failItemList.add(failItem);
                    continue;
                }
                if (item.getOrderType() == 0 || item.getOrderType() == 1) {
                    CostAggreOriginalOrderQueryReqVO costAggreReqVO = new CostAggreOriginalOrderQueryReqVO();
                    costAggreReqVO.setOriginalOrderId(item.getRelatedOrderId());
                    costAggreReqVO.setAggreStatusList(new ArrayList<>(Arrays.asList(1, 2)));
                    List<CostAggreOriginalOrderDO> costAggreList = aggreOriginalOrderMapper.selectList(costAggreReqVO);
                    if (CollUtilX.isNotEmpty(costAggreList)) {
                        FailItem failItem = new FailItem();
                        failItem.setCode(item.getRelatedOrderCode());
                        failItem.setReason("【行号" + item.getRelatedRowNo() + "】，该单据已被归集，不能摊销");
                        failItemList.add(failItem);
                        continue;
                    }
                }
                isSupCode.add(item.getCostProdPurchaseId());
            }
            batchResult.setFailItem(failItemList);
            batchResult.setTotalCount(list.size());
            batchResult.setFailCount(failItemList.size());
            batchResult.setSuccessCount(isSupCode.size());
            reqVO.setIdList(isSupCode);
        }else if (reqVO.getAmortiseStatus().intValue() == AmortiseEnum.NO_AMORTISE.key) {
            CostProdPurchaseQueryReqVO queryReqVO = new CostProdPurchaseQueryReqVO();
            queryReqVO.setCostProdPurchaseIdList(reqVO.getIdList());
            List<CostProdPurchaseRespVO> list = this.costProdPurchaseList(queryReqVO);
            List<Long> isSupCode = new ArrayList<>();
            for (CostProdPurchaseRespVO item : list) {
                //实时查当前摊销状态是不是未摊销
                if (item.getAmortiseStatus() != AmortiseEnum.NOT_AMORTISE.key) {
                    FailItem failItem = new FailItem();
                    failItem.setCode(item.getRelatedOrderCode());
                    failItem.setReason("摊销状态是未摊销才可以无需摊销");
                    failItemList.add(failItem);
                    continue;
                }
                isSupCode.add(item.getCostProdPurchaseId());
            }
            batchResult.setFailItem(failItemList);
            batchResult.setTotalCount(list.size());
            batchResult.setFailCount(list.size()-isSupCode.size());
            batchResult.setSuccessCount(isSupCode.size());
            reqVO.setIdList(isSupCode);
        }
        if (CollUtilX.isNotEmpty(reqVO.getIdList())){
            CostProdPurchaseQueryReqVO queryReqVO = new CostProdPurchaseQueryReqVO();
            queryReqVO.setCostProdPurchaseIdList(reqVO.getIdList());
            List<CostProdPurchaseRespVO> respVOList = costProdPurchaseMapper.selectReqList(queryReqVO);
            for (CostProdPurchaseRespVO item : respVOList){
                if (reqVO.getAmortiseStatus().intValue() == AmortiseEnum.AMORTISE.key){
                    //生成详情明细
                    this.costProdPurchaseDetailAdd(reqVO,item);
                }else if (reqVO.getAmortiseStatus().intValue() == AmortiseEnum.NOT_AMORTISE.key){
                    //删除明细
                    costProdPurchaseDetailService.costProdPurchaseIdDel(item.getCostProdPurchaseId());
                }
                item.setAmortiseStatus(reqVO.getAmortiseStatus());
            }
            List<CostProdPurchaseDO> doList =  BeanUtilX.copy(respVOList, CostProdPurchaseDO::new);
            //修改摊销状态
            costProdPurchaseMapper.updateBatch(doList);
        }
        return batchResult;
    }


    private void costProdPurchaseOneCheck(AmortiseStatusReqVO reqVO,CostProdPurchaseRespVO costProdPurchaseRespVO) {
        if (costProdPurchaseRespVO.getAmortiseStatus() != AmortiseEnum.NOT_AMORTISE.key){
            throw new BizException("5001", "摊销状态是未摊销才可以摊销");
        }
//        if (StringUtils.isEmpty(costProdPurchaseRespVO.getSpuCode())) {
//            throw new BizException("5001", "该单据spu编码不存在");
//        }
        if (reqVO.getAmortiseStatus() == AmortiseEnum.AMORTISE.key && costProdPurchaseRespVO.getUndertakeMaterialId() == null){
            throw new BizException("5001", "未选择承担物料的不能进行摊销");
        }
        if (costProdPurchaseRespVO.getExclTaxUnitPrice() == null) {
            throw new BizException("5001", "该单据单价不存在");
        }
        if (costProdPurchaseRespVO.getOrderType() == 0 || costProdPurchaseRespVO.getOrderType() == 1){
            CostAggreOriginalOrderQueryReqVO queryReqVO = new CostAggreOriginalOrderQueryReqVO();
            queryReqVO.setOriginalOrderId(costProdPurchaseRespVO.getRelatedOrderId());
            queryReqVO.setAggreStatusList(new ArrayList<>(Arrays.asList(1, 2)));
            List<CostAggreOriginalOrderDO> list = aggreOriginalOrderMapper.selectList(queryReqVO);
            if (CollUtilX.isNotEmpty(list)){
                throw new BizException("5001", "该单据已被归集，不能摊销");
            }
        }
        this.costProdPurchaseDetailAdd(reqVO,costProdPurchaseRespVO);
    }

    private void costProdPurchaseDetailAdd(AmortiseStatusReqVO reqVO,CostProdPurchaseRespVO costProdPurchaseRespVO) {
        CostProdPurchaseDetailAditReqVO aditReqVO = new CostProdPurchaseDetailAditReqVO();
        aditReqVO.setCostProdPurchaseId(costProdPurchaseRespVO.getCostProdPurchaseId());
        aditReqVO.setCostSubjectId(reqVO.getCostSubjectId());
        aditReqVO.setUndertakeOrgId(reqVO.getUndertakeOrgId());
        aditReqVO.setDirectorId(reqVO.getDirectorId());
        aditReqVO.setDirectorOrgId(reqVO.getDirectorOrgId());
        aditReqVO.setTotalAmt(costProdPurchaseRespVO.getQty().multiply(costProdPurchaseRespVO.getExclTaxUnitPrice()));
        aditReqVO.setFormDt(reqVO.getFormDt());
        aditReqVO.setDetailType(costProdPurchaseRespVO.getMaterialSourceDictId() == MaterialEnum.OUTSOURCE.key ? (short) 1 : (short) 0);
        costProdPurchaseDetailService.costProdPurchaseDetailAdd(aditReqVO);
    }


    /**
     * VO属性填充-单个处理
     *
     * @param respVO
     */
    private void fillVoProperties(CostProdPurchaseRespVO respVO) {
        List<CostProdPurchaseRespVO> respVOList = new ArrayList<>();
        respVOList.add(respVO);
        // 批量处理
        batchFillVoProperties(respVOList);
    }

    /**
     * VO属性填充-批量处理
     *
     * @param respVOList
     */
    private void batchFillVoProperties(List<CostProdPurchaseRespVO> respVOList) {
        if (CollUtilX.isEmpty(respVOList)) {
            return;
        }
        List<Long> supplierIdList = new ArrayList<>();
        List<Long> processIds = new ArrayList<>();
//        List<Long> relatedOrderIdList = new ArrayList<>();
        List<Long> undertakeMaterialIdList = new ArrayList<>();
        for(CostProdPurchaseRespVO respVO : respVOList){
            supplierIdList.add(respVO.getRelatedSupplierId());
            processIds.add(respVO.getProcessId());
//            relatedOrderIdList.add(respVO.getRelatedOrderId());
            undertakeMaterialIdList.add(respVO.getUndertakeMaterialId());
        }

        //查询物料信息
        Map<Long, ERPMaterialRespVO> erpMaterialDOMap = new HashMap<>();
        if (CollUtilX.isNotEmpty(undertakeMaterialIdList)){
            ERPMaterialQueryReqVO erpMaterialQuery = new ERPMaterialQueryReqVO();
            erpMaterialQuery.setMaterialIdList(undertakeMaterialIdList);
            erpMaterialDOMap = erpMaterialService.selectERPMaterialMap(erpMaterialQuery);
        }

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(SystemDictEnum.MATERIAL_SOURCE.getDictCode());

        //查询供应商名称
        Map<Long, String> supplierNameMap = erpBaseService.getERPSupplierNameByIdList(supplierIdList);

        //查询工序
        ProcessQueryReqVO processQuery = new ProcessQueryReqVO();
        processQuery.setProcessIdList(processIds);
        List<ProcessRespVO> processList = processService.processList(processQuery);
        Map<Long, ProcessRespVO> processMap = processList.stream().collect(Collectors.toMap(ProcessRespVO::getProcessId, detail -> detail));

        //查询spu编码
//        Map<Long, Map<Long, String>> supCodeMap = costSpuConfigService.spuCodeMap(relatedOrderIdList);

        // 属性填充
        for (CostProdPurchaseRespVO item : respVOList) {
            //供应商
            if(item.getRelatedSupplierId() != null){
                item.setRelatedSupplierName(supplierNameMap.get(item.getRelatedSupplierId()));
            }

            // 字典库属性填充
            if(item.getMaterialSourceDictId() != null){
                item.setMaterialSourceDictName(dictMap.get(item.getMaterialSourceDictId().toString()));
            }

            //工序
            ProcessRespVO processRespVO = processMap.get(item.getProcessId());
            if (processRespVO!=null){
                item.setProcessName(processRespVO.getProcessName());
            }

            //填充物料基本信息
            ERPMaterialRespVO erpMaterialDO = erpMaterialDOMap.get(item.getUndertakeMaterialId());
            if (erpMaterialDO!=null){
                item.setUndertakeMaterialName(erpMaterialDO.getMaterialName());
            }

//            // spu编码
//            if(item.getRelatedOrderId() != null && item.getMaterialId() != null){
//                Map<Long, String> supcode = supCodeMap.get(item.getRelatedOrderId());
//                if(supcode != null) {
//                    item.setSpuCode(supcode.get(item.getMaterialId()));
//                }
//            }
        }
    }


}
