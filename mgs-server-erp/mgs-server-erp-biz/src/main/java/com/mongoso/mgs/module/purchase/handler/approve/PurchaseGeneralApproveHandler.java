package com.mongoso.mgs.module.purchase.handler.approve;

import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.enums.material.MaterialEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.enums.purchase.PurchaseBizTypeEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseApproveHandler;
import com.mongoso.mgs.module.base.service.orderrelation.OrderRelationService;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggreoriginalorder.vo.CostAggreOriginalOrderAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costprodpurchase.vo.CostProdPurchaseAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costunitpricedetail.vo.CostUnitPriceDetailAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costunitpricedetail.vo.CostUnitPriceDetailRespVO;
import com.mongoso.mgs.module.dailycost.controller.admin.spuconfig.vo.CostSpuConfigAditReqVO;
import com.mongoso.mgs.module.dailycost.enums.CostAggreOriginalTypeEnum;
import com.mongoso.mgs.module.dailycost.enums.SpuConfigSourceOrderEnum;
import com.mongoso.mgs.module.dailycost.service.costaggreoriginalorder.CostAggreOriginalOrderService;
import com.mongoso.mgs.module.dailycost.service.costprodpurchase.CostProdPurchaseService;
import com.mongoso.mgs.module.dailycost.service.costunitpricedetail.CostUnitPriceDetailService;
import com.mongoso.mgs.module.dailycost.service.spuconfig.CostSpuConfigService;
import com.mongoso.mgs.module.finance.service.common.FinanceConnectService;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderdetail.vo.ErpProdOrderDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.materialanalysisdetail.vo.MaterialAnalysisDetailQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.prodwork.vo.ProdWorkQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpicking.vo.WorkPickingQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.WorkPickingReturnQueryReqVO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorder.ErpProdOrderDO;
import com.mongoso.mgs.module.produce.dal.db.erpprodorderdetail.ErpProdOrderDetailDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysis.MaterialAnalysisDO;
import com.mongoso.mgs.module.produce.dal.db.materialanalysistotal.MaterialAnalysisTotalDO;
import com.mongoso.mgs.module.produce.dal.db.prodwork.ProdWorkDO;
import com.mongoso.mgs.module.produce.dal.db.workpicking.WorkPickingDO;
import com.mongoso.mgs.module.produce.dal.db.workpickingreturn.WorkPickingReturnDO;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorder.ErpProdOrderMapper;
import com.mongoso.mgs.module.produce.dal.mysql.erpprodorderdetail.ErpProdOrderDetailMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysis.MaterialAnalysisMapper;
import com.mongoso.mgs.module.produce.dal.mysql.materialanalysistotal.MaterialAnalysisTotalMapper;
import com.mongoso.mgs.module.produce.dal.mysql.prodwork.ProdWorkMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpicking.WorkPickingMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd.WorkPickingReturnMapper;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.PurchaseDeductionQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.PurchaseOrderRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.purchasereturn.vo.PurchaseReturnQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.deduction.PurchaseDeductionDO;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDetailDO;
import com.mongoso.mgs.module.purchase.dal.db.purchasereturn.PurchaseReturnDO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDO;
import com.mongoso.mgs.module.purchase.dal.mysql.deduction.PurchaseDeductionMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.demand.PurchaseDemandMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.detail.PurchaseOrderDetailMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.purchasereturn.PurchaseReturnMapper;
import com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.PurchaseReceiptNoticeMapper;
import com.mongoso.mgs.module.purchase.service.purchase.PurchaseOrderService;
import com.mongoso.mgs.module.purchase.service.receiptnotice.PurchaseReceiptNoticeService;
import com.mongoso.mgs.module.sale.dal.db.erpsaleorder.ErpSaleOrderDO;
import com.mongoso.mgs.module.sale.dal.mysql.erpsaleorder.ErpSaleOrderMapper;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictNewQueryReqVO;
import com.mongoso.mgs.module.system.controller.admin.dict.vo.DictNewRespVO;
import com.mongoso.mgs.module.sale.enums.FormStatusEnum;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.ErpInboundAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.ErpInboundQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail.ErpInboundDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpoutbound.vo.ErpOutboundQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.ErpReceiptAditReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.ErpReceiptQueryReqVO;
import com.mongoso.mgs.module.warehouse.controller.admin.erpreceipt.vo.detail.ErpReceiptDetailAditReqVO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptMapper;
import com.mongoso.mgs.module.warehouse.enums.ErpInboundBizTypeEnum;
import com.mongoso.mgs.module.warehouse.enums.ErpReceiptBizTypeEnum;
import com.mongoso.mgs.module.warehouse.service.erpinbound.ErpInboundService;
import com.mongoso.mgs.module.warehouse.service.erpreceipt.ErpReceiptService;
import com.mongoso.mgs.module.warehouse.service.stockbook.StockBookService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import static com.mongoso.mgs.module.purchase.enums.ErrorCodeConstants.*;

import static com.mongoso.mgs.module.sale.enums.ErrorCodeConstants.RELATED_SALE_FORCE_CLOSE;
/**
 * Created with IntelliJ IDEA.
 *
 * @author： ZhouYangqing
 * @date： 2024/12/4
 * @description：普通采购订单审批
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class PurchaseGeneralApproveHandler extends FlowApproveHandler<PurchaseOrderDO> {

    @Resource
    private PurchaseOrderMapper purchaseMapper;
    @Resource
    private ProdWorkMapper prodWorkMapper;
    @Resource
    @Lazy
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private PurchaseDemandMapper purchaseDemandMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseDetailMapper;
    @Resource
    private PurchaseReceiptNoticeMapper receiptNoticeMapper;
    @Resource
    private ErpReceiptMapper erpReceiptMapper;
    @Lazy
    @Resource
    private ErpReceiptService erpReceiptService;
    @Resource
    private PurchaseReceiptNoticeService receiptNoticeService;
    @Resource
    private ErpProdOrderMapper erpProdOrderMapper;
    @Resource
    private ErpProdOrderDetailMapper erpProdOrderDetailMapper;
    @Resource
    private MaterialAnalysisMapper materialAnalysisMapper;
    @Resource
    private MaterialAnalysisTotalMapper materialAnalysisTotalMapper;
    @Resource
    private PurchaseReturnMapper purchaseReturnMapper;
    @Resource
    private PurchaseDeductionMapper purchaseDeductionMapper;
    @Lazy
    @Resource
    private ErpInboundService erpInboundService;

    @Resource
    private FinanceConnectService financeConnectService;
    @Resource
    private WorkPickingMapper workPickingMapper;
    @Resource
    private WorkPickingReturnMapper workPickingReturnMapper;
    @Resource
    private ErpInboundMapper erpInboundMapper;
    @Resource
    private CostProdPurchaseService costProdPurchaseService;
    @Resource
    private CostSpuConfigService costSpuConfigService;
    @Resource
    private CostUnitPriceDetailService costUnitPriceDetailService;
    @Resource
    private CostAggreOriginalOrderService costAggreOriginalOrderService;
    @Resource
    private OrderRelationService orderRelationService;

    @Resource
    private StockBookService stockBookService;

    @Resource
    private ErpBaseService erpBaseService;

    @Lazy
    @Resource
    private ErpSaleOrderMapper erpSaleOrderMapper;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private ErpOutboundMapper erpOutboundMapper;


    @Override
    protected ApproveCommonAttrs approvalAttributes(PurchaseOrderDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(PurchaseOrderDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(PurchaseOrderDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getPurchaseOrderId())
                .objCode(item.getPurchaseOrderCode())
                .tableName(tableName)
                .pkFieldName(pkFieldName)

                .build();

        return attrs;
    }
    @Override
    protected Boolean businessVerify(PurchaseOrderDO item, BaseApproveRequest request) {
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        if(buttonType == DataButtonEnum.APPROVE.getKey()) {

//            if (!DataStatusEnum.NOT_APPROVE.getKey().equals(item.getDataStatus())){
//                failItem.setCode(item.getPurchaseOrderCode());
//                failItem.setReason(ORDER_NOT_OPERATION_NOT_APPROVED.getMsg());
//                return false;
//            }

            Short bizType = item.getPurchaseOrderBizType();

            if (bizType == PurchaseBizTypeEnum.DEMAND_PURCHASE.type){
                return demandPurchaseVerify(item, failItem);
            }

            if (bizType == PurchaseBizTypeEnum.PROD_OUT_PURCHASE.type){
                return prodOutPurchaseVerify(item, failItem);
            }

            if (bizType == PurchaseBizTypeEnum.MATERIAL_OUT_PURCHASE.type){
                return materialOutPurchaseVerify(item, failItem);
            }

            if (bizType == PurchaseBizTypeEnum.SALE_PURCHASE.type){
                return salePurchaseVerify(item, failItem);
            }
        }

        if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {

            Short bizType = item.getPurchaseOrderBizType();

            Boolean bizTypeFlag = bizType == PurchaseBizTypeEnum.GENERAL_PURCHASE.type ||
                    bizType == PurchaseBizTypeEnum.DEMAND_PURCHASE.type ||
                    bizType == PurchaseBizTypeEnum.OUTSOURCING_PURCHASE.type ||
                    bizType == PurchaseBizTypeEnum.PROD_OUT_PURCHASE.type ||
                    bizType == PurchaseBizTypeEnum.SALE_PURCHASE.type ||
                    bizType == PurchaseBizTypeEnum.MATERIAL_OUT_PURCHASE.type;
//            if ( bizTypeFlag && item.getIsForceClose() == 1){
//                failItem.setCode(item.getPurchaseOrderCode());
//                failItem.setReason("订单已强制关闭,不允许进行反审核操作!");
//                return false;
//            }

            if (bizTypeFlag && (item.getFormStatus() == FormStatusEnum.COMPLETED.type || item.getFormStatus() == FormStatusEnum.CLOSED.type)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason("采购订单单据状态是【已完成】或者【已关闭】,不允许进行反审核操作！");
                return false;
            }

            if (bizType == PurchaseBizTypeEnum.SALE_PURCHASE.type){
                ErpSaleOrderDO erpSaleOrderDO = erpSaleOrderMapper.selectById(item.getRelatedOrderId());
                if (erpSaleOrderDO == null) {
                    failItem.setCode(item.getPurchaseOrderCode());
                    failItem.setReason(RELATED_ORDER_NOT_EXIST.getMsg());
                    return false;
                }

                if (erpSaleOrderDO.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrderDO.getFormStatus() == FormStatusEnum.CLOSED.type) {
                    failItem.setCode(item.getPurchaseOrderCode());
                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
                    return false;
                }
            }

            //采购收货通知单
            PurchaseReceiptNoticeQueryReqVO noticeQueryReqVO = new PurchaseReceiptNoticeQueryReqVO();
            noticeQueryReqVO.setPurchaseOrderId(item.getPurchaseOrderId());
            noticeQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<PurchaseReceiptNoticeDO> noticeDOList = receiptNoticeMapper.selectList(noticeQueryReqVO);

            if (CollUtilX.isNotEmpty(noticeDOList)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //采购入库单
            ErpInboundQueryReqVO erpInboundQueryReqVO = new ErpInboundQueryReqVO();
            erpInboundQueryReqVO.setRelatedOrderId(item.getPurchaseOrderId());
            erpInboundQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<ErpInboundDO> inboundDOList = erpInboundMapper.selectList(erpInboundQueryReqVO);
            if (CollUtilX.isNotEmpty(inboundDOList)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //采购收货单
            ErpReceiptQueryReqVO erpReceiptQueryReqVO = new ErpReceiptQueryReqVO();
            erpReceiptQueryReqVO.setRelatedOrderId(item.getPurchaseOrderId());
            erpReceiptQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<ErpReceiptDO> receiptDOList = erpReceiptMapper.selectList(erpReceiptQueryReqVO);
            if (CollUtilX.isNotEmpty(receiptDOList)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //采购退货单
            PurchaseReturnQueryReqVO returnQueryReqVO = new PurchaseReturnQueryReqVO();
            returnQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            returnQueryReqVO.setPurchaseOrderId(item.getPurchaseOrderId());
            List<PurchaseReturnDO> returnDOList = purchaseReturnMapper.selectList(returnQueryReqVO);
            if (CollUtilX.isNotEmpty(returnDOList)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //采购扣费单
            PurchaseDeductionQueryReqVO deductionQueryReqVO = new PurchaseDeductionQueryReqVO();
            deductionQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            deductionQueryReqVO.setPurchaseOrderId(item.getPurchaseOrderId());
            List<PurchaseDeductionDO> deductionDOList = purchaseDeductionMapper.selectList(deductionQueryReqVO);
            if (CollUtilX.isNotEmpty(deductionDOList)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //领料单
            WorkPickingQueryReqVO workPickingQueryReqVO = new WorkPickingQueryReqVO();
            workPickingQueryReqVO.setRelatedOrderId(item.getPurchaseOrderId());
            workPickingQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<WorkPickingDO> workPickingDOS = workPickingMapper.selectList(workPickingQueryReqVO);
            if (CollUtilX.isNotEmpty(workPickingDOS)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //直接领料出库单
            ErpOutboundQueryReqVO erpOutboundQueryReqVO = new ErpOutboundQueryReqVO();
            erpOutboundQueryReqVO.setRelatedOrderId(item.getPurchaseOrderId());
            erpOutboundQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            List<ErpOutboundDO> outboundDOList = erpOutboundMapper.selectList(erpOutboundQueryReqVO);
            if (CollUtilX.isNotEmpty(outboundDOList)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }

            //退料单
            WorkPickingReturnQueryReqVO pickingReturnQueryReqVO = new WorkPickingReturnQueryReqVO();
            pickingReturnQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.key);
            pickingReturnQueryReqVO.setRelatedOrderId(item.getPurchaseOrderId());
            List<WorkPickingReturnDO> workPickingReturnDOS = workPickingReturnMapper.selectList(pickingReturnQueryReqVO);
            if (CollUtilX.isNotEmpty(workPickingReturnDOS)){
                failItem.setCode(item.getPurchaseOrderCode());
                failItem.setReason(DOWNSTREAM_HAVE_APPROVED.getMsg());
                return false;
            }



            //财务反审校验
            financeConnectService.checkOrder(item.getPurchaseOrderId());

        }
        return true;
    }

    private Boolean salePurchaseVerify(PurchaseOrderDO item, FailItem failItem) {
        ErpSaleOrderDO erpSaleOrderDO = erpSaleOrderMapper.selectById(item.getRelatedOrderId());
        if (erpSaleOrderDO == null) {
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_EXIST.getMsg());
            return false;
        }

        if (erpSaleOrderDO.getDataStatus() != DataStatusEnum.APPROVED.key) {
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason("销售订单状态不是【已审核】,审核失败!");
            return false;
        }

        List<Integer> formStatusList = new ArrayList<>();
        formStatusList.add(FormStatusEnum.NOT_STARTED.getType());
        formStatusList.add(FormStatusEnum.IN_PROGERSS.getType());

        if (!formStatusList.contains(erpSaleOrderDO.getFormStatus())) {
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
            return false;
        }

        return true;
    }

    @Override
    public Integer handleBusinessData(PurchaseOrderDO item,  BaseApproveRequest request) {
        Long id = item.getPurchaseOrderId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        PurchaseOrderDO purchaseDO = purchaseMapper.selectById(id);
        PurchaseOrderDetailQueryReqVO purchaseDetailQueryReqVO = new PurchaseOrderDetailQueryReqVO();
        purchaseDetailQueryReqVO.setPurchaseOrderId(id);
        List<PurchaseOrderDetailDO> purchaseDetailDOList = purchaseDetailMapper.selectList(purchaseDetailQueryReqVO);

        Map<Long, BigDecimal> purchaseQtyMap = purchaseDetailDOList.stream().collect(Collectors
                .toMap(PurchaseOrderDetailDO::getMaterialId, PurchaseOrderDetailDO::getPurchaseQty));

        Short bizType = purchaseDO.getPurchaseOrderBizType();

        //需求采购订单
        if (bizType == PurchaseBizTypeEnum.DEMAND_PURCHASE.type){
            this.handleDemandPurchaseQty(purchaseDO, buttonType, purchaseQtyMap);
        }

        //生产委外采购订单
        if (bizType == PurchaseBizTypeEnum.PROD_OUT_PURCHASE.type){
            this.handleProdOutPurchaseQty(purchaseDO, buttonType, purchaseQtyMap);
        }

        //物料分析委外采购订单
        if (bizType == PurchaseBizTypeEnum.MATERIAL_OUT_PURCHASE.type){
            this.handleMaterialOutPurchaseQty(purchaseDO, buttonType, purchaseQtyMap);
        }

        if(bizType == PurchaseBizTypeEnum.SALE_PURCHASE.type) {
            //修改销售订单状态
            erpSaleOrderService.editChildrenOrderCount(purchaseDO.getRelatedOrderId(), buttonType);
        }

        if(buttonType == DataButtonEnum.APPROVE.getKey()) {
            this.autoIssueDownstreamForm(purchaseDO);
        }

        //日成本初始化
        this.dailyCostInit(buttonType,purchaseDO, purchaseDetailDOList);

        //处理单据关联的预订任务
        stockBookService.bookReleaseByOrderApprove(buttonType, id);

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        purchaseDO.setApprovedBy(loginUser.getFullUserName());
        purchaseDO.setApprovedDt(LocalDateTime.now());
        purchaseDO.setDataStatus(dataStatus);
        purchaseMapper.updateById(purchaseDO);

        //数据进入财务
        PurchaseOrderRespVO purchaseOrderRespVO = purchaseOrderService.purchaseOrderDetail(id);
        purchaseOrderRespVO.setSourceFormType(OrderTypeEnum.PURCHASE_ORDER.type.shortValue());
        purchaseOrderRespVO.setRelatedOrderId(purchaseOrderRespVO.getPurchaseOrderId());
        purchaseOrderRespVO.setRelatedOrderCode(purchaseOrderRespVO.getPurchaseOrderCode());

        //源头单据id
        purchaseOrderRespVO.setOriginOrderId(id);

        financeConnectService.purchaseInsertFinance(purchaseOrderRespVO, buttonType);

        //数据进入日成本
        if(bizType == PurchaseBizTypeEnum.GENERAL_PURCHASE.type || bizType == PurchaseBizTypeEnum.DEMAND_PURCHASE.type || bizType == PurchaseBizTypeEnum.OUTSOURCING_PURCHASE.type) {
            if (buttonType == DataButtonEnum.APPROVE.getKey()){
                for (PurchaseOrderDetailRespVO purchaseOrderDetailRespVO : purchaseOrderRespVO.getDetailList()){
                    CostProdPurchaseAditReqVO aditReqVO = new CostProdPurchaseAditReqVO();
                    aditReqVO.setOrderType(bizType == PurchaseBizTypeEnum.OUTSOURCING_PURCHASE.type ? (short) 1 : (short)0);
                    aditReqVO.setSourceFormType(bizType.intValue());
                    aditReqVO.setRelatedOrderCode(purchaseOrderRespVO.getPurchaseOrderCode());
                    aditReqVO.setRelatedOrderId(purchaseOrderRespVO.getPurchaseOrderId());
                    aditReqVO.setRelatedOrderDetailId(purchaseOrderDetailRespVO.getPurchaseOrderDetailId());
                    aditReqVO.setRelatedRowNo(purchaseOrderDetailRespVO.getRowNo());
                    aditReqVO.setFormDt(purchaseOrderRespVO.getFormDt());
                    aditReqVO.setRelatedSupplierId(purchaseOrderRespVO.getRelatedSupplierId());
                    aditReqVO.setMaterialId(purchaseOrderDetailRespVO.getMaterialId());
                    aditReqVO.setMaterialCode(purchaseOrderDetailRespVO.getMaterialCode());
                    aditReqVO.setQty(purchaseOrderDetailRespVO.getPurchaseQty());
                    aditReqVO.setExclTaxUnitPrice(purchaseOrderDetailRespVO.getExclTaxUnitPrice());
                    aditReqVO.setExclTaxAmt(purchaseOrderDetailRespVO.getExclTaxAmt());
                    aditReqVO.setCompanyOrgId(purchaseOrderRespVO.getCompanyOrgId());
                    costProdPurchaseService.costProdPurchaseAdd(aditReqVO);
                }
                //记录物料单价到单价明细
                if (bizType == PurchaseBizTypeEnum.GENERAL_PURCHASE.type){
                    for (PurchaseOrderDetailRespVO purchaseOrderDetailRespVO : purchaseOrderRespVO.getDetailList()){
                        CostUnitPriceDetailRespVO costUnitPriceDetailRespVO = costUnitPriceDetailService.purchaseOrderIdDetail(purchaseOrderDetailRespVO.getMaterialId());
                        CostUnitPriceDetailAditReqVO reqVO = new CostUnitPriceDetailAditReqVO();
                        reqVO.setPurchaseOrderId(purchaseOrderRespVO.getPurchaseOrderId());
                        reqVO.setPurchaseOrderCode(purchaseOrderRespVO.getPurchaseOrderCode());
                        reqVO.setFormDt(purchaseOrderRespVO.getFormDt());
                        reqVO.setMaterialId(purchaseOrderDetailRespVO.getMaterialId());
                        reqVO.setMaterialCode(purchaseOrderDetailRespVO.getMaterialCode());
                        reqVO.setPurchaseUnitPrice(purchaseOrderDetailRespVO.getExclTaxUnitPrice());
                        if (costUnitPriceDetailRespVO != null) {
                            reqVO.setCostUnitPriceDetailId(costUnitPriceDetailRespVO.getCostUnitPriceDetailId());
                            costUnitPriceDetailService.costUnitPriceDetailEdit(reqVO);
                        }else {
                            costUnitPriceDetailService.costUnitPriceDetailAdd(reqVO);
                        }
                    }
                }
            }else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
                costProdPurchaseService.costRelatedOrderIdDel(purchaseOrderRespVO.getPurchaseOrderId());
            }
        }

        //关联信息处理
        if (purchaseDO.getPurchaseOrderBizType() == PurchaseBizTypeEnum.OUTSOURCING_PURCHASE.type
                || purchaseDO.getPurchaseOrderBizType() == PurchaseBizTypeEnum.PROD_OUT_PURCHASE.type
                || purchaseDO.getPurchaseOrderBizType() == PurchaseBizTypeEnum.MATERIAL_OUT_PURCHASE.type){
            orderRelationService.orderRelationAditOrDel(buttonType, id, purchaseDO.getPurchaseOrderCode(), purchaseDO.getRelatedOrderId());
        }

        return 1;
    }

//    private void handleBookTask(Integer buttonType, Long orderId) {
//        if (buttonType == DataButtonEnum.NOT_APPROVE.key){
//            StockBookQueryReqVO stockBookQueryReqVO = new StockBookQueryReqVO();
//            stockBookQueryReqVO.setOrderId(orderId);
//            stockBookQueryReqVO.setTaskStatus(BookTaskStatusEnum.ONGOING.status);
//            StockBookDO stockBookDO = stockBookMapper.selectOne(stockBookQueryReqVO);
//
//            //释放预订任务
//            if (ObjUtilX.isNotEmpty(stockBookDO)){
//                stockBookService.stockBookRelease(stockBookDO.getStockBookId());
//            }
//        }
//    }

    private void dailyCostInit(Integer buttonType, PurchaseOrderDO purchaseDO, List<PurchaseOrderDetailDO> purchaseDetailDOList) {
        //初始化SPU配置
        ArrayList<CostSpuConfigAditReqVO> costSpuConfigList = new ArrayList<>();
        for (PurchaseOrderDetailDO purchaseOrderDetailDO : purchaseDetailDOList) {
            CostSpuConfigAditReqVO costSpuConfigAditReqVO = new CostSpuConfigAditReqVO();
            costSpuConfigAditReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.getKey());
            costSpuConfigAditReqVO.setRelatedUpOrderId(purchaseOrderDetailDO.getPurchaseOrderId());
            costSpuConfigAditReqVO.setRelatedUpOrderCode(purchaseOrderDetailDO.getPurchaseOrderCode());
            costSpuConfigAditReqVO.setRelatedOrderDetailId(purchaseOrderDetailDO.getPurchaseOrderDetailId());
            costSpuConfigAditReqVO.setMaterialId(purchaseOrderDetailDO.getMaterialId());
            costSpuConfigAditReqVO.setMaterialCode(purchaseOrderDetailDO.getMaterialCode());
            costSpuConfigAditReqVO.setFormDt(purchaseDO.getFormDt());
            if (PurchaseBizTypeEnum.GENERAL_PURCHASE.getType().longValue() == purchaseDO.getPurchaseOrderBizType() ||
                    PurchaseBizTypeEnum.DEMAND_PURCHASE.getType().longValue() == purchaseDO.getPurchaseOrderBizType()){
                costSpuConfigAditReqVO.setOrderType(SpuConfigSourceOrderEnum.PURCHASE.code);
            }else {
                costSpuConfigAditReqVO.setOrderType(SpuConfigSourceOrderEnum.OUTSOURCING_PURCHASE.code);
            }
            costSpuConfigList.add(costSpuConfigAditReqVO);
        }
        costSpuConfigService.costSpuConfigOperate(buttonType, costSpuConfigList);
        //初始化归集原始单
        CostAggreOriginalOrderAditReqVO aggreOriginalAditReqVO = new CostAggreOriginalOrderAditReqVO();
        aggreOriginalAditReqVO.setOriginalOrderId(purchaseDO.getPurchaseOrderId());
        aggreOriginalAditReqVO.setOriginalOrderCode(purchaseDO.getPurchaseOrderCode());
        aggreOriginalAditReqVO.setOriginalType(CostAggreOriginalTypeEnum.PURCHASE.code);
        aggreOriginalAditReqVO.setCompanyOrgId(purchaseDO.getCompanyOrgId());
        aggreOriginalAditReqVO.setFormDt(purchaseDO.getFormDt());
        costAggreOriginalOrderService.costAggreOriginalOperate(buttonType, aggreOriginalAditReqVO);
    }

    private boolean demandPurchaseVerify(PurchaseOrderDO item, FailItem failItem) {
        //关联的**采购需求单**必须是**已审核**状态
        PurchaseDemandDO demandDO = purchaseDemandMapper.selectById(item.getRelatedOrderId());
        if(ObjUtilX.isEmpty(demandDO)){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_EXIST.getMsg());
            return false;
        }
        if (!Objects.equals(demandDO.getDataStatus(), DataStatusEnum.APPROVED.getKey())){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_APPROVED.getMsg());
            return false;
        }
        //且采购单的每个物料采购数量**不可大于**采购需求单中的可采购数量
        Integer result = purchaseDetailMapper.getPurchaseIsApproveAble(item);
        if (result == 0){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_NUM_GREATER_RELATED_ORDER.getMsg());
            return false;
        }
        return true;
    }
    private boolean prodOutPurchaseVerify(PurchaseOrderDO item, FailItem failItem) {
        //关联的**生产订单**必须是**已审核**状态
        ErpProdOrderDO erpProdOrderDO = erpProdOrderMapper.selectById(item.getRelatedOrderId());
        if(ObjUtilX.isEmpty(erpProdOrderDO)){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_EXIST.getMsg());
            return false;
        }
        if (!Objects.equals(erpProdOrderDO.getDataStatus(), DataStatusEnum.APPROVED.getKey())){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_APPROVED.getMsg());
            return false;
        }
        //生产订单的单据状态需要为待开始、进行中
        if (OrderStatusEnum.TOBEGIN.getCode() != erpProdOrderDO.getFormStatus()
                && OrderStatusEnum.PROGRESS.getCode() != erpProdOrderDO.getFormStatus()){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(PROD_NOT_FINISHED.getMsg());
            return false;
        }
        //采购单的每个物料采购数量**不可大于**采购需求单中的可采购数量
//        Integer result = purchaseDetailMapper.getPurchaseIsApproveAbleByProd(item);
//        if (result == 0){
//            failItem.setCode(item.getPurchaseOrderCode());
//            failItem.setReason(RELATED_NUM_GREATER_RELATED_ORDER.getMsg());
//            return false;
//        }
        return true;
    }

    private boolean materialOutPurchaseVerify(PurchaseOrderDO item, FailItem failItem) {
        //关联的物料分析单必须是已审核状态
        MaterialAnalysisDO materialAnalysisDO = materialAnalysisMapper.selectById(item.getRelatedOrderId());
        if (ObjUtilX.isEmpty(materialAnalysisDO)){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_EXIST.getMsg());
            return false;
        }
        if (!Objects.equals(materialAnalysisDO.getDataStatus(), DataStatusEnum.APPROVED.getKey())){
            failItem.setCode(item.getPurchaseOrderCode());
            failItem.setReason(RELATED_ORDER_NOT_APPROVED.getMsg());
            return false;
        }
        //采购单的每个物料采购数量不可大于物料分析单中的可采购数量
//        Integer result = purchaseDetailMapper.getPurchaseIsApproveAbleByAnalysis(item);
//        if (result == 0){
//            failItem.setCode(item.getPurchaseOrderCode());
//            failItem.setReason(RELATED_NUM_GREATER_RELATED_ORDER.getMsg());
//            return false;
//        }
        return true;
    }

    private void handleDemandPurchaseQty(PurchaseOrderDO purchaseDO, Integer buttonType, Map<Long, BigDecimal> purchaseQtyMap){
        //处理需求订单明细的的purchased_qty（已采购数量）和purchase_able_qty（可采购数量）
        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
            purchaseDetailMapper.updateDemandDetailPurchaseQty(purchaseDO, 1);
        }else if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            purchaseDetailMapper.updateDemandDetailPurchaseQty(purchaseDO, null);
        }
        //查询采购单关联的需求单类型
        PurchaseDemandDO purchaseDemandDO = purchaseDemandMapper.selectById(purchaseDO.getRelatedOrderId());
        if (purchaseDemandDO.getPurchaseDemandBizType() == PurchaseBizTypeEnum.MATERIAL_DEMAND.type){
            //更新物料分析的已外购数量
            MaterialAnalysisDO materialAnalysisDO = materialAnalysisMapper.selectById(purchaseDemandDO.getRelatedOrderId());
            MaterialAnalysisDetailQueryReqVO analysisDetailQueryReqVO = new MaterialAnalysisDetailQueryReqVO();
            analysisDetailQueryReqVO.setMaterialAnalysisId(materialAnalysisDO.getMaterialAnalysisId());
            List<MaterialAnalysisTotalDO> analysisDetailDOList = materialAnalysisTotalMapper.selectByMaterialAnalysisIdAndType(materialAnalysisDO.getBizType()
                    , materialAnalysisDO.getMaterialAnalysisId(),2L);
            if (buttonType == DataButtonEnum.APPROVE.getKey() || buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
                if (buttonType == DataButtonEnum.APPROVE.getKey()) {
                    for (MaterialAnalysisTotalDO detailRespVO : analysisDetailDOList) {
                        if (purchaseQtyMap.get(detailRespVO.getMaterialId()) != null){
                            detailRespVO.setOutsourcedQty(detailRespVO.getOutsourcedQty().add(purchaseQtyMap.get(detailRespVO.getMaterialId())));
                        }
                    }
                }else if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                    for (MaterialAnalysisTotalDO detailRespVO : analysisDetailDOList) {
                        if (purchaseQtyMap.get(detailRespVO.getMaterialId()) != null){
                            detailRespVO.setOutsourcedQty(detailRespVO.getOutsourcedQty().subtract(purchaseQtyMap.get(detailRespVO.getMaterialId())));
                        }
                    }
                }
                materialAnalysisMapper.updateById(materialAnalysisDO);
                materialAnalysisTotalMapper.updateBatch(analysisDetailDOList);
            }
        }
    }

    private void handleProdOutPurchaseQty(PurchaseOrderDO purchaseDO, Integer buttonType, Map<Long, BigDecimal> purchaseQtyMap){
        //处理生产订单明细的的purchaseable_qty（可采购数量）和purchased_qty（已采购数量）
        //处理生产订单的物料是否全部已采购状态
        ErpProdOrderDO erpProdOrderDO = erpProdOrderMapper.selectById(purchaseDO.getRelatedOrderId());

        //查询生产订单明细
        ErpProdOrderDetailQueryReqVO prodOrderDetailQueryReqVO = new ErpProdOrderDetailQueryReqVO();
        prodOrderDetailQueryReqVO.setProdOrderId(erpProdOrderDO.getProdOrderId());
//        prodOrderDetailQueryReqVO.setMaterialSourceDictId(MaterialEnum.OUT_MADE.key);
        List<Integer> materialSourceDictIds = new ArrayList<>();
        materialSourceDictIds.add(MaterialEnum.OUT_MADE.key);
        materialSourceDictIds.add(MaterialEnum.SELF_MADE.key);
        prodOrderDetailQueryReqVO.setMaterialSourceDictIdList(materialSourceDictIds);
        List<ErpProdOrderDetailDO> prodOrderDetailDOList = erpProdOrderDetailMapper.selectList(prodOrderDetailQueryReqVO);

        if (buttonType == DataButtonEnum.APPROVE.getKey()){
            if (erpProdOrderDO.getFormStatus() == OrderStatusEnum.PENDING.getCode()){
                erpProdOrderDO.setFormStatus(OrderStatusEnum.IN_PRODUCTION.getCode());
            }
        }
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
            //查询该生产订单管理的已审核的采购订单或生产工单
            PurchaseOrderQueryReqVO purchaseOrderQueryReqVO = new PurchaseOrderQueryReqVO();
            purchaseOrderQueryReqVO.setRelatedOrderId(purchaseDO.getRelatedOrderId());
            purchaseOrderQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            purchaseOrderQueryReqVO.setExclPurchaseIdList(Arrays.asList(purchaseDO.getPurchaseOrderId()));
            List<PurchaseOrderDO> purchaseOrderList = purchaseMapper.selectList(purchaseOrderQueryReqVO);
            ProdWorkQueryReqVO prodWorkQueryReqVO = new ProdWorkQueryReqVO();
            prodWorkQueryReqVO.setProdOrderId(purchaseDO.getRelatedOrderId());
            prodWorkQueryReqVO.setDataStatus(DataStatusEnum.APPROVED.getKey());
            List<ProdWorkDO> prodWorkList = prodWorkMapper.selectList(prodWorkQueryReqVO);
            if (CollUtilX.isEmpty(purchaseOrderList) && CollUtilX.isEmpty(prodWorkList)){
                erpProdOrderDO.setFormStatus(OrderStatusEnum.PENDING.getCode());
            }
        }

        //是否全部已采购
        boolean isFullPurchased = true;
        for (ErpProdOrderDetailDO detailRespVO : prodOrderDetailDOList) {
            //采购数量
            BigDecimal purchasedQty = purchaseQtyMap.get(detailRespVO.getMaterialId());

            if (purchasedQty != null){
                if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                    purchasedQty = purchasedQty.negate();
                }
                detailRespVO.setPurchaseableQty(detailRespVO.getPurchaseableQty().subtract(purchasedQty));
                detailRespVO.setPurchasedQty(detailRespVO.getPurchasedQty().add(purchasedQty));
                erpProdOrderDO.setWorkPlanTotalQty(erpProdOrderDO.getWorkPlanTotalQty().add(purchasedQty));
            }

            if (detailRespVO.getPurchasedQty().compareTo(detailRespVO.getProdPlanQty()) == 0){
                detailRespVO.setIsMaterialFullPurchased(1);
            }else {
                detailRespVO.setIsMaterialFullPurchased(0);
                isFullPurchased = false;
            }
        }

        if (isFullPurchased){
            erpProdOrderDO.setIsFullPurchased(1);
        }else {
            erpProdOrderDO.setIsFullPurchased(0);
        }

        erpProdOrderMapper.updateById(erpProdOrderDO);
        erpProdOrderDetailMapper.updateBatch(prodOrderDetailDOList);


    }

    private void handleMaterialOutPurchaseQty(PurchaseOrderDO purchaseDO, Integer buttonType, Map<Long, BigDecimal> purchaseQtyMap){
        //处理生产订单明细的的purchaseable_qty（可扣费数量）和purchased_qty（已扣费数量）
        //处理生产订单的物料是否全部已采购状态
        MaterialAnalysisDO materialAnalysisDO = materialAnalysisMapper.selectById(purchaseDO.getRelatedOrderId());
        List<MaterialAnalysisTotalDO> analysisDetailDOList = materialAnalysisTotalMapper.selectByMaterialAnalysisIdAndType(materialAnalysisDO.getBizType()
                , materialAnalysisDO.getMaterialAnalysisId(),1L);
        int purchasedDetailCount = 0;
        if (buttonType == DataButtonEnum.APPROVE.getKey() || buttonType == DataButtonEnum.NOT_APPROVE.getKey()){
            if (buttonType == DataButtonEnum.APPROVE.getKey()) {
                for (MaterialAnalysisTotalDO detailRespVO : analysisDetailDOList) {
                    if (purchaseQtyMap.get(detailRespVO.getMaterialId()) != null){
                        detailRespVO.setPurchaseableQty(detailRespVO.getPurchaseableQty().subtract(purchaseQtyMap.get(detailRespVO.getMaterialId())));
                        detailRespVO.setPurchasedQty(detailRespVO.getPurchasedQty().add(purchaseQtyMap.get(detailRespVO.getMaterialId())));
                    }
                    if (detailRespVO.getPurchasedQty().compareTo(detailRespVO.getNetDemandQty()) == 0){
                        detailRespVO.setIsMaterialFullPurchased(1);
                        purchasedDetailCount++;
                    }else {
                        detailRespVO.setIsMaterialFullPurchased(0);
                    }
                }
            }else if(buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                for (MaterialAnalysisTotalDO detailRespVO : analysisDetailDOList) {
                    if (purchaseQtyMap.get(detailRespVO.getMaterialId()) != null){
                        detailRespVO.setPurchaseableQty(detailRespVO.getPurchasedQty().add(purchaseQtyMap.get(detailRespVO.getMaterialId())));
                        detailRespVO.setPurchasedQty(detailRespVO.getPurchasedQty().subtract(purchaseQtyMap.get(detailRespVO.getMaterialId())));
                    }
                    if (detailRespVO.getPurchasedQty().compareTo(detailRespVO.getNetDemandQty()) == 0){
                        detailRespVO.setIsMaterialFullPurchased(1);
                        purchasedDetailCount++;
                    }else {
                        detailRespVO.setIsMaterialFullPurchased(0);
                    }
                }
            }
            if (purchasedDetailCount == analysisDetailDOList.size()){
                materialAnalysisDO.setIsFullPurchased(1);
            }else {
                materialAnalysisDO.setIsFullPurchased(0);
            }
            materialAnalysisMapper.updateById(materialAnalysisDO);
            materialAnalysisTotalMapper.updateBatch(analysisDetailDOList);
        }
    }

    /**
     * 自动下发单据
     * @param purchaseDO
     */
    public void autoIssueDownstreamForm(PurchaseOrderDO purchaseDO){
        PurchaseOrderDetailQueryReqVO detailQueryReqVO = new PurchaseOrderDetailQueryReqVO();
        detailQueryReqVO.setPurchaseOrderId(purchaseDO.getPurchaseOrderId());
        List<PurchaseOrderDetailDO> purchaseDetailDOList = purchaseDetailMapper.selectList(detailQueryReqVO);
        //下发采购收货通知单
        if (purchaseDO.getIsIssueReceiptNotice() != null && 1 == purchaseDO.getIsIssueReceiptNotice()){
            issuePurchaseReceiptNotice(purchaseDO, purchaseDetailDOList);
        }
        //下发采购收货单
        if (purchaseDO.getIsIssueReceipt() != null && 1 == purchaseDO.getIsIssueReceipt()){
            issueReceipt(purchaseDO, purchaseDetailDOList);
        }
        //下发采购入库单
        if (purchaseDO.getIsIssueInbound() != null && 1 == purchaseDO.getIsIssueInbound()){
            issueInbound(purchaseDO, purchaseDetailDOList);
        }
        //委外领料单
        if (purchaseDO.getIsIssueOutGetMaterial() != null && 1 == purchaseDO.getIsIssueOutGetMaterial()){

        }
    }


    /**
     * 下发采购收货通知单
     * @param purchaseDO 采购订单信息
     * @param detailDOList 采购顶下
     */
    private void issuePurchaseReceiptNotice(PurchaseOrderDO purchaseDO, List<PurchaseOrderDetailDO> detailDOList) {
        if (CollUtilX.isNotEmpty(detailDOList)){
            PurchaseReceiptNoticeAditReqVO receiptNoticeAditReqVO = new PurchaseReceiptNoticeAditReqVO();
            receiptNoticeAditReqVO.setPurchaseTypeDictId(purchaseDO.getPurchaseTypeDictId());
            receiptNoticeAditReqVO.setPurchaseOrderId(purchaseDO.getPurchaseOrderId());
            receiptNoticeAditReqVO.setPurchaseOrderCode(purchaseDO.getPurchaseOrderCode());
            receiptNoticeAditReqVO.setRelatedSupplierId(purchaseDO.getRelatedSupplierId());
            receiptNoticeAditReqVO.setRelatedOrderCode(purchaseDO.getRelatedOrderCode());
            receiptNoticeAditReqVO.setContactName(purchaseDO.getContactName());
            receiptNoticeAditReqVO.setContactPhone(purchaseDO.getContactPhone());
            receiptNoticeAditReqVO.setRemark(null);
            receiptNoticeAditReqVO.setFormDt(purchaseDO.getFormDt());
            receiptNoticeAditReqVO.setDirectorId(purchaseDO.getDirectorId());
            receiptNoticeAditReqVO.setDirectorOrgId(purchaseDO.getDirectorOrgId());
            receiptNoticeAditReqVO.setDeliveryDate(purchaseDO.getDeliveryDate());
            List<PurchaseReceiptNoticeDetailRespVO> detailList = new ArrayList<>();
            for (PurchaseOrderDetailDO purchaseOrderDetailDO : detailDOList) {
                PurchaseReceiptNoticeDetailRespVO detailRespVO = new PurchaseReceiptNoticeDetailRespVO();
                detailRespVO.setRowNo(purchaseOrderDetailDO.getRowNo());
                detailRespVO.setRelatedRowNo(purchaseOrderDetailDO.getRowNo());
                detailRespVO.setPurchaseOrderDetailId(purchaseOrderDetailDO.getPurchaseOrderDetailId());
                detailRespVO.setMaterialCode(purchaseOrderDetailDO.getMaterialCode());
                detailRespVO.setMaterialId(purchaseOrderDetailDO.getMaterialId());
                detailRespVO.setMainUnitDictId(purchaseOrderDetailDO.getMainUnitDictId());
                detailRespVO.setNoticeQty(purchaseOrderDetailDO.getOperableQty());
                detailList.add(detailRespVO);
            }
            receiptNoticeAditReqVO.setDetailList(detailList);
            receiptNoticeService.purchaseReceiptNoticeAdd(receiptNoticeAditReqVO);
        }
    }


    /**
     * 下发销售退货收货单
     * @param purchaseDO 采购订单信息
     * @param detailDOList 采购订单明细信息
     */
    private void issueReceipt(PurchaseOrderDO purchaseDO, List<PurchaseOrderDetailDO> detailDOList) {
        if (CollUtilX.isNotEmpty(detailDOList)) {
            //收货单信息
            ErpReceiptAditReqVO receiptAditReqVO = new ErpReceiptAditReqVO();
            //查询收货单类型
            DictNewQueryReqVO dictQueryReqVO = new DictNewQueryReqVO();
            dictQueryReqVO.setParentDictCode(CustomerDictEnum.RECEIPT_TYPE.getDictCode());
            dictQueryReqVO.setDictName("采购收货");
            List<DictNewRespVO> dictList = erpBaseService.getDictList(dictQueryReqVO);
            if(CollUtilX.isNotEmpty(dictList)){
                receiptAditReqVO.setReceiptTypeDictId(dictList.get(0).getDictCode());
            }

            receiptAditReqVO.setRelatedOrderId(purchaseDO.getPurchaseOrderId());
            receiptAditReqVO.setRelatedOrderCode(purchaseDO.getPurchaseOrderCode());
            receiptAditReqVO.setBizType(ErpReceiptBizTypeEnum.PURCHASE_RECEIPT.getType());
            receiptAditReqVO.setDirectorId(purchaseDO.getDirectorId());
            receiptAditReqVO.setDirectorOrgId(purchaseDO.getDirectorOrgId());
            receiptAditReqVO.setFormDt(purchaseDO.getFormDt());

            //出库单明细信息
            List<ErpReceiptDetailAditReqVO> receiptDetailList = new ArrayList<>();
            for (PurchaseOrderDetailDO detailDO : detailDOList) {
                ErpReceiptDetailAditReqVO receiptDetailAditReqVO = new ErpReceiptDetailAditReqVO();
                receiptDetailAditReqVO.setRowNo((int) detailDO.getRowNo());
                receiptDetailAditReqVO.setRelatedOrderDetailId(detailDO.getPurchaseOrderDetailId());
                receiptDetailAditReqVO.setRelatedRowNo((int) detailDO.getRowNo());
                receiptDetailAditReqVO.setMaterialId(detailDO.getMaterialId());
                receiptDetailAditReqVO.setMaterialCode(detailDO.getMaterialCode());
                receiptDetailAditReqVO.setMainUnitDictId(detailDO.getMainUnitDictId());
                receiptDetailAditReqVO.setReceiptQty(detailDO.getPurchaseQty());
                receiptDetailList.add(receiptDetailAditReqVO);
            }
            receiptAditReqVO.setDetailList(receiptDetailList);
            erpReceiptService.erpReceiptAdd(receiptAditReqVO);
        }
    }

    /**
     *  下发入库单
     * @param purchaseDO 采购订单信息
     * @param detailDOList 采购订单明细信息
     */
    private void issueInbound(PurchaseOrderDO purchaseDO, List<PurchaseOrderDetailDO> detailDOList) {
        if (CollUtilX.isNotEmpty(detailDOList)){
            //入库单信息
            ErpInboundAditReqVO inboundAditReqVO = new ErpInboundAditReqVO();
            //查询入库单类型
            DictNewQueryReqVO dictQueryReqVO = new DictNewQueryReqVO();
            dictQueryReqVO.setParentDictCode(CustomerDictEnum.INBOUND_TYPE.getDictCode());
            dictQueryReqVO.setDictName("采购入库");
            List<DictNewRespVO> dictList = erpBaseService.getDictList(dictQueryReqVO);
            if(CollUtilX.isNotEmpty(dictList)){
                inboundAditReqVO.setInboundTypeDictId(dictList.get(0).getDictCode());
            }

            inboundAditReqVO.setRelatedOrderId(purchaseDO.getPurchaseOrderId());
            inboundAditReqVO.setRelatedOrderCode(purchaseDO.getPurchaseOrderCode());
            inboundAditReqVO.setBizType(ErpInboundBizTypeEnum.PURCHASE_INBOUND.getType());
            inboundAditReqVO.setDirectorId(purchaseDO.getDirectorId());
            inboundAditReqVO.setDirectorOrgId(purchaseDO.getDirectorOrgId());
            inboundAditReqVO.setFormDt(purchaseDO.getFormDt());

            //入库单明细信息
            List<ErpInboundDetailAditReqVO> inboundDetailList = new ArrayList<>();
            for (PurchaseOrderDetailDO detailDO : detailDOList) {
                ErpInboundDetailAditReqVO inboundDetailAditReqVO = new ErpInboundDetailAditReqVO();
                inboundDetailAditReqVO.setRowNo((long)detailDO.getRowNo());
                inboundDetailAditReqVO.setRelatedOrderDetailId(detailDO.getPurchaseOrderDetailId());
                inboundDetailAditReqVO.setRelatedRowNo((int)detailDO.getRowNo());
                inboundDetailAditReqVO.setMaterialId(detailDO.getMaterialId());
                inboundDetailAditReqVO.setMaterialCode(detailDO.getMaterialCode());
                inboundDetailAditReqVO.setMainUnitDictId(detailDO.getMainUnitDictId());
                inboundDetailAditReqVO.setInboundQty(detailDO.getPurchaseQty());
                inboundDetailList.add(inboundDetailAditReqVO);
            }
            inboundAditReqVO.setDetailList(inboundDetailList);
            erpInboundService.erpInboundAdd(inboundAditReqVO);
        }
    }
}
