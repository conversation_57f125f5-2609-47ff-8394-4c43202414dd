package com.mongoso.mgs.module.finance.controller.admin.paymentapply;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.datapermission.core.annotation.DataPermission;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.finance.controller.admin.paymentapply.vo.*;
import com.mongoso.mgs.module.finance.service.paymentapply.PaymentApplyService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 收款申请 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment")
@Validated
public class PaymentApplyController {

    @Resource
    private PaymentApplyService applyService;

    @OperateLog("收款申请添加或编辑")
    @PostMapping("/paymentApplyAdit")
    @PreAuthorize("@ss.hasPermission('paymentApply:adit')")
    public ResultX<Long> paymentApplyAdit(@Valid @RequestBody PaymentApplyAditReqVO reqVO) {
        return success(reqVO.getReciveApplyId() == null
                            ? applyService.paymentApplyAdd(reqVO)
                            : applyService.paymentApplyEdit(reqVO));
    }

    @OperateLog("收款申请删除")
    @PostMapping("/paymentApplyDel")
    @PreAuthorize("@ss.hasPermission('paymentApply:delete')")
    public ResultX<Boolean> paymentApplyDel(@Valid @RequestBody PaymentApplyPrimaryReqVO reqVO) {
        applyService.paymentApplyDel(reqVO.getReciveApplyId());
        return success(true);
    }

    @OperateLog("收款申请批量删除")
    @PostMapping("/paymentApplyDelBatch")
    @PreAuthorize("@ss.hasPermission('paymentApply:delete')")
    public ResultX<BatchResult> paymentApplyDelBatch(@Valid @RequestBody IdReq reqVO) {
        return applyService.paymentApplyDelBatch(reqVO);
    }

    @OperateLog("收款申请详情")
    @PostMapping("/paymentApplyDetail")
    @PreAuthorize("@ss.hasPermission('paymentApply:query')")
    public ResultX<PaymentApplyRespVO> paymentApplyDetail(@Valid @RequestBody PaymentApplyPrimaryReqVO reqVO) {
        return success(applyService.paymentApplyDetail(reqVO.getReciveApplyId()));
    }

    @OperateLog("收款申请列表")
    @PostMapping("/paymentApplyList")
    @PreAuthorize("@ss.hasPermission('paymentApply:query')")
    @DataPermission
    public ResultX<List<PaymentApplyRespVO>> paymentApplyList(@Valid @RequestBody PaymentApplyQueryReqVO reqVO) {
        return success(applyService.paymentApplyList(reqVO));
    }

    @OperateLog("收款申请分页")
    @PostMapping("/paymentApplyPage")
    @PreAuthorize("@ss.hasPermission('paymentApply:query')")
    @DataPermission
    public ResultX<PageResult<PaymentApplyRespVO>> paymentApplyPage(@Valid @RequestBody PaymentApplyPageReqVO reqVO) {
        return success(applyService.paymentApplyPage(reqVO));
    }

    @OperateLog("收款申请列表")
    @PostMapping("/paymentApplyQuoteList")
    @PreAuthorize("@ss.hasPermission('paymentApply:query')")
    public ResultX<List<PaymentApplyRespVO>> paymentApplyQuoteList(@Valid @RequestBody PaymentApplyQueryReqVO reqVO) {
        return success(applyService.paymentApplyList(reqVO));
    }

    @OperateLog("收款申请分页")
    @PostMapping("/paymentApplyQuotePage")
    @PreAuthorize("@ss.hasPermission('paymentApply:query')")
    public ResultX<PageResult<PaymentApplyRespVO>> paymentApplyQuotePage(@Valid @RequestBody PaymentApplyPageReqVO reqVO) {
        return success(applyService.paymentApplyPage(reqVO));
    }

    @OperateLog("收款申请审核")
    @PostMapping("/paymentApplyApprove")
    @PreAuthorize("@ss.hasPermission('paymentApply:adit')")
    public ResultX<BatchResult> paymentApplyApprove(@Valid @RequestBody FlowApprove reqVO) {
        BatchResult resultList = applyService.paymentApplyApprove(reqVO);
        return success(resultList);
    }

    @OperateLog("收款申请回调接口")
    @PostMapping("/paymentApplyFlowCallback")
    public ResultX<Object> paymentApplyFlowCallback(@Valid @RequestBody FlowCallback reqVO) {
        return success(applyService.paymentApplyFlowCallback(reqVO));
    }
}
