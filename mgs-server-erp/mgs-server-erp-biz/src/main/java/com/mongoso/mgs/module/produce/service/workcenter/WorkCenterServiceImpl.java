package com.mongoso.mgs.module.produce.service.workcenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.enums.CustomerDictEnum;
import com.mongoso.mgs.common.enums.MenuEnum;
import com.mongoso.mgs.common.enums.SeqEnum;
import com.mongoso.mgs.common.enums.SystemDictEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.domain.batch.IdReq;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.employee.dal.db.position.PositionDO;
import com.mongoso.mgs.module.employee.service.personal.bo.UserBaseRespBO;
import com.mongoso.mgs.module.employee.service.position.PositionService;
import com.mongoso.mgs.module.produce.controller.admin.device.vo.DeviceRespVO;
import com.mongoso.mgs.module.produce.controller.admin.devicerel.vo.DeviceRelQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.devicerel.vo.DeviceRelRespVO;
import com.mongoso.mgs.module.produce.controller.admin.employeeskillratio.vo.EmployeeSkillRatioQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.employeeskillratio.vo.EmployeeSkillRatioRespVO;
import com.mongoso.mgs.module.produce.controller.admin.workcenterperson.vo.WorkCenterPersonQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workcenterperson.vo.WorkCenterPersonRespVO;
import com.mongoso.mgs.module.produce.dal.db.devicerel.DeviceRelDO;
import com.mongoso.mgs.module.produce.dal.db.employeeskillratio.EmployeeSkillRatioDO;
import com.mongoso.mgs.module.produce.dal.db.workcenterperson.WorkCenterPersonDO;
import com.mongoso.mgs.module.produce.dal.mysql.devicerel.DeviceRelMapper;
import com.mongoso.mgs.module.produce.dal.mysql.employeeskillratio.EmployeeSkillRatioMapper;
import com.mongoso.mgs.module.produce.dal.mysql.workcenterperson.WorkCenterPersonMapper;
import com.mongoso.mgs.module.produce.handler.approve.WorkCenterApproveHandler;
import com.mongoso.mgs.module.produce.handler.flowCallback.WorkCenterFlowCallBackHandler;
import com.mongoso.mgs.module.produce.service.device.DeviceService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import com.mongoso.mgs.module.produce.controller.admin.workcenter.vo.*;
import com.mongoso.mgs.module.produce.dal.db.workcenter.WorkCenterDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.produce.dal.mysql.workcenter.WorkCenterMapper;
import com.mongoso.mgs.framework.common.exception.BizException;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.NOT_DELETE_NO_APPROVAL;


/**
 * 工作中心 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WorkCenterServiceImpl implements WorkCenterService {

    @Resource
    private WorkCenterMapper workCenterMapper;

    @Resource
    private SeqService seqService;

    @Resource
    private ErpBaseService erpBaseService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private EmployeeSkillRatioMapper employeeSkillRatioMapper;

    @Resource
    private ApproveService approveService;

    @Resource
    private MessageTemplateService messageTemplateService;

    @Resource
    @Lazy
    private WorkCenterApproveHandler workCenterApproveHandler;

    @Resource
    private WorkCenterFlowCallBackHandler workCenterFlowCallBackHandler;

    @Resource
    private PositionService positionService;

    @Resource
    private DeviceRelMapper deviceRelMapper;

    @Resource
    private WorkCenterPersonMapper workCenterPersonMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long workCenterAdd(WorkCenterAditReqVO reqVO) {

        // 生成单号
        String code = seqService.getGenerateCode(reqVO.getWorkCenterCode(), MenuEnum.WORK_CENTER_MANAGEMENT.menuId);

        // 插入
        WorkCenterDO workCenter = BeanUtilX.copy(reqVO, WorkCenterDO::new);
        workCenter.setWorkCenterCode(code);
        workCenterMapper.insert(workCenter);

        //技能分配比例处理
        reqVO.setWorkCenterId(workCenter.getWorkCenterId());
        List<EmployeeSkillRatioDO> employeeSkillRatioDOS = getDetailEditList(reqVO);
        employeeSkillRatioMapper.insertBatch(employeeSkillRatioDOS);

        //关联设备
        if (CollUtilX.isNotEmpty(reqVO.getDeviceInfo())){
            List<DeviceRelDO> list = new ArrayList<>();
            for (DeviceRelRespVO baseVO : reqVO.getDeviceInfo()){
                baseVO.setBizType(0);
                baseVO.setRelatedOrderId(workCenter.getWorkCenterId());
                baseVO.setRelatedOrderCode(workCenter.getWorkCenterCode());
                list.add(BeanUtilX.copy(baseVO, DeviceRelDO::new));
            }
            deviceRelMapper.insertBatch(list);
        }

        //关联人员
        if (CollUtilX.isNotEmpty(reqVO.getPersonInfo())){
            List<WorkCenterPersonDO> list = new ArrayList<>();
            for (WorkCenterPersonRespVO personResp : reqVO.getPersonInfo()){
                WorkCenterPersonDO workCenterPersonDO = new WorkCenterPersonDO();
                workCenterPersonDO.setWorkCenterId(workCenter.getWorkCenterId());
                workCenterPersonDO.setWorkCenterCode(workCenter.getWorkCenterCode());
                workCenterPersonDO.setRowNo(personResp.getRowNo());
                workCenterPersonDO.setUserId(personResp.getUserId());
                list.add(workCenterPersonDO);
            }
            workCenterPersonMapper.insertBatch(list);
        }

        // 返回
        return workCenter.getWorkCenterId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long workCenterEdit(WorkCenterAditReqVO reqVO) {
        // 校验存在
//        this.workCenterValidateExists(reqVO.getWorkCenterId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.workCenterValidateExists(reqVO.getWorkCenterId()), reqVO);

        // 更新
        WorkCenterDO workCenter = BeanUtilX.copy(reqVO, WorkCenterDO::new);

        List<EmployeeSkillRatioDO> employeeSkillRatioDOS = getDetailEditList(reqVO);

        //先删后增
        employeeSkillRatioMapper.batchDelete(reqVO.getWorkCenterId());
        workCenterMapper.updateById(workCenter);
        employeeSkillRatioMapper.insertBatch(employeeSkillRatioDOS);

        //关联设备
        deviceRelMapper.batchDeleteByRelatedOrderId(reqVO.getWorkCenterId());
        if (CollUtilX.isNotEmpty(reqVO.getDeviceInfo())){
            List<DeviceRelDO> list = new ArrayList<>();
            for (DeviceRelRespVO baseVO : reqVO.getDeviceInfo()){
                baseVO.setBizType(0);
                baseVO.setRelatedOrderId(workCenter.getWorkCenterId());
                baseVO.setRelatedOrderCode(workCenter.getWorkCenterCode());
                list.add(BeanUtilX.copy(baseVO, DeviceRelDO::new));
            }
            deviceRelMapper.insertBatch(list);
        }

        //关联人员
        workCenterPersonMapper.batchDeleteByRelatedOrderId(reqVO.getWorkCenterId());
        if (CollUtilX.isNotEmpty(reqVO.getPersonInfo())){
            List<WorkCenterPersonDO> list = new ArrayList<>();
            for (WorkCenterPersonRespVO personResp : reqVO.getPersonInfo()){
                WorkCenterPersonDO workCenterPersonDO = new WorkCenterPersonDO();
                workCenterPersonDO.setWorkCenterId(workCenter.getWorkCenterId());
                workCenterPersonDO.setWorkCenterCode(workCenter.getWorkCenterCode());
                workCenterPersonDO.setRowNo(personResp.getRowNo());
                workCenterPersonDO.setUserId(personResp.getUserId());
                list.add(workCenterPersonDO);
            }
            workCenterPersonMapper.insertBatch(list);
        }

        // 返回
        return workCenter.getWorkCenterId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void workCenterDel(Long workCenterId) {
        // 校验存在
        WorkCenterDO currentDO = this.workCenterValidateExists(workCenterId);

        if(currentDO.getDataStatus() != DataStatusEnum.NOT_APPROVE.getKey()){
            throw new BizException(NOT_DELETE_NO_APPROVAL.getCode(), NOT_DELETE_NO_APPROVAL.getMsg());
        }
        // 删除
        workCenterMapper.deleteById(workCenterId);
        employeeSkillRatioMapper.batchDelete(workCenterId);
    }

    private WorkCenterDO workCenterValidateExists(Long workCenterId) {
        WorkCenterDO workCenter = workCenterMapper.selectById(workCenterId);
        if (workCenter == null) {
            // throw exception(WORK_CENTER_NOT_EXISTS);
            throw new BizException("5001", "工作中心不存在");
        }
        return workCenter;
    }

    @Override
    public WorkCenterRespVO workCenterDetail(Long workCenterId) {
        WorkCenterDO data = workCenterValidateExists(workCenterId);
        if (data == null){
            return null;
        }
        WorkCenterRespVO respVO = BeanUtilX.copy(data, WorkCenterRespVO::new);

        //VO属性填充
        fillVoProperties(Collections.singletonList(respVO),true);

        //明细详情处理
        EmployeeSkillRatioQueryReqVO detailQuery = new EmployeeSkillRatioQueryReqVO();
        detailQuery.setWorkCenterId(respVO.getWorkCenterId());
        List<EmployeeSkillRatioDO> detailDOList = employeeSkillRatioMapper.selectListOld(detailQuery);

        List<EmployeeSkillRatioRespVO> detailRespVOS = BeanUtilX.copy(detailDOList,EmployeeSkillRatioRespVO::new);

        //查询字典库信息
        Map<String, String> dictMap = erpBaseService.dictMap(CustomerDictEnum.SKILL_LEVEL.getDictCode());

        for (EmployeeSkillRatioRespVO employeeSkillRatio : detailRespVOS){
            String skillLevelDictName = dictMap.get(employeeSkillRatio.getSkillLevelDictId());
            employeeSkillRatio.setSkillLevelDictName(skillLevelDictName);
        }
        respVO.setDetailList(detailRespVOS);

        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(workCenterId.toString())).ifPresent(approveTask -> respVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return respVO;
    }

    @Override
    public List<WorkCenterRespVO> workCenterList(WorkCenterQueryReqVO reqVO) {
        List<WorkCenterDO> data = workCenterMapper.selectList(reqVO);
        List<WorkCenterRespVO> respVOList = BeanUtilX.copy(data, WorkCenterRespVO::new);

        //VO属性填充
        if (CollUtilX.isNotEmpty(respVOList)){
            fillVoProperties(respVOList,false);
        }
        return respVOList;
    }

    @Override
    public PageResult<WorkCenterRespVO> workCenterPage(WorkCenterPageReqVO reqVO) {
        PageResult<WorkCenterDO> data = workCenterMapper.selectPage(reqVO);
        PageResult<WorkCenterRespVO> pageResult = BeanUtilX.copy(data, WorkCenterRespVO::new);

        //VO属性填充
        if (CollUtilX.isNotEmpty(pageResult.getList())){
            fillVoProperties(pageResult.getList(),false);
        }
        return pageResult;
    }

    @Override
    public ResultX<BatchResult> workCenterDelBatch(IdReq reqVO) {
        // 删除
        String id = EntityUtilX.getPropertyName(WorkCenterDO::getWorkCenterId);
        String code = EntityUtilX.getPropertyName(WorkCenterDO::getWorkCenterCode);
        return erpBaseService.batchDelete(reqVO.getIdList(), WorkCenterDO.class, null, id, code);

    }

    @Override
    public BatchResult workCenterApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<WorkCenterDO> list = workCenterMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (WorkCenterDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());

                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();

                //流程处理
                FailItem failItem = workCenterApproveHandler.process(item,flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())){
                    failItemList.add(failItem);
                }
            }catch (Exception exception){
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getWorkCenterCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }

        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount()-batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (WorkCenterDO item : list) {
                String reason = reasonMap.get(item.getWorkCenterCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getWorkCenterId());
                    messageInfoBO.setObjCode(item.getWorkCenterCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getWorkCenterId());
                    messageInfoBO.setObjCode(item.getWorkCenterCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

    @Override
    public Object workCenterFlowCallback(FlowCallback reqVO) {

        String objId = reqVO.getObjId();
        WorkCenterDO item = this.workCenterValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return workCenterFlowCallBackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public List<WorkCenterRespVO> selectListByUserId(Long userId) {
        List<WorkCenterDO> workCenterDOList = workCenterMapper.selectListByUserId(userId);
        List<WorkCenterRespVO> respVOS = BeanUtilX.copy(workCenterDOList, WorkCenterRespVO::new);

        if (CollUtilX.isNotEmpty(respVOS)){
            //查询字典库信息
            List<String> dictCodeList = Arrays.asList(CustomerDictEnum.WORK_CENTER_TYPE.getDictCode(), CustomerDictEnum.RESOURCES_TYPE.getDictCode());
            Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

            List<Long> userIdList = new ArrayList<>();
            List<String> deptOrgIds = new ArrayList<>();
            for (WorkCenterRespVO item : respVOS){
                userIdList.add(item.getDirectorId());
                deptOrgIds.add(item.getDirectorOrgId());
            }

            //查询部门
            Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

            //查询员工信息
            Map<Long, UserBaseRespBO> userNameMap = erpBaseService.getEmpByIdList(userIdList);

            for (WorkCenterRespVO item : respVOS){

                // 工作中心类型
                String workCenterTypeDictId = item.getWorkCenterTypeDictId();
                if(StrUtilX.isNotEmpty(workCenterTypeDictId)){
                    workCenterTypeDictId = CustomerDictEnum.WORK_CENTER_TYPE.getDictCode() + "-" + workCenterTypeDictId;
                    item.setWorkCenterTypeDictName(dictMap.get(workCenterTypeDictId));
                }

                // 资源类型(
                String resourcesTypeDictId = item.getResourcesTypeDictId();
                if(StrUtilX.isNotEmpty(resourcesTypeDictId)){
                    resourcesTypeDictId = CustomerDictEnum.RESOURCES_TYPE.getDictCode() + "-" + resourcesTypeDictId;
                    item.setResourcesTypeDictName(dictMap.get(resourcesTypeDictId));
                }

                //查询负责人
                UserBaseRespBO dirEmployee = userNameMap.get(item.getDirectorId());
                if (dirEmployee!=null){
                    item.setDirectorName(dirEmployee.getEmployeeName());
                }

                //查询责任部门
                item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));
            }
        }
        return respVOS;
    }

    /**
     * 明细新增/编辑处理
     *
     * @param reqVO
     * @return
     */
    private List<EmployeeSkillRatioDO> getDetailEditList(WorkCenterAditReqVO reqVO) {
        List<EmployeeSkillRatioDO> employeeSkillRatioList = new ArrayList<>();
        List<EmployeeSkillRatioRespVO> detailList = reqVO.getDetailList();
        for (EmployeeSkillRatioRespVO detail : detailList){
            detail.setWorkCenterId(reqVO.getWorkCenterId());
            detail.setWorkCenterCode(reqVO.getWorkCenterCode());
            EmployeeSkillRatioDO employeeSkillRatioDO = BeanUtilX.copy(detail, EmployeeSkillRatioDO::new);
            employeeSkillRatioList.add(employeeSkillRatioDO);
        }

        return employeeSkillRatioList;
    }

    /**
     * VO属性填充
     *
     * @param itemList
     */
    private void fillVoProperties(List<WorkCenterRespVO> itemList,Boolean isDetailQuery) {
        if (CollUtilX.isEmpty(itemList)){
            return;
        }

        //查询字典库信息
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.WORK_CENTER_TYPE.getDictCode(), CustomerDictEnum.RESOURCES_TYPE.getDictCode(),
                SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);

        List<Long> userIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();
        List<Long> deviceIds = new ArrayList<>();
        for (WorkCenterRespVO item : itemList){
            userIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());

            DeviceRelQueryReqVO deviceRelQueryReqVO = new DeviceRelQueryReqVO();
            deviceRelQueryReqVO.setRelatedOrderId(item.getWorkCenterId());
            List<DeviceRelDO> deviceRelList = deviceRelMapper.selectList(deviceRelQueryReqVO);
            if (CollUtilX.isNotEmpty(deviceRelList)){
                deviceIds = deviceRelList.stream().map(DeviceRelDO::getDeviceId).collect(Collectors.toList());
            }

            WorkCenterPersonQueryReqVO workCenterPersonQuery = new WorkCenterPersonQueryReqVO();
            workCenterPersonQuery.setWorkCenterId(item.getWorkCenterId());
            List<WorkCenterPersonDO> workCenterPersonList = workCenterPersonMapper.selectList(workCenterPersonQuery);
            if (CollUtilX.isNotEmpty(workCenterPersonList)){
                List<Long> uids = workCenterPersonList.stream().map(WorkCenterPersonDO::getUserId).collect(Collectors.toList());
                userIdList.addAll(uids);
            }
        }

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        //查询员工信息
        Map<Long, UserBaseRespBO> userNameMap = erpBaseService.getEmpByIdList(userIdList);

        //详情查询
        Map<Long, DeviceRespVO> deviceInfoMap = new HashMap<>();
        if (isDetailQuery){
            deviceInfoMap = deviceService.getDeviceInfo(deviceIds);
        }

        for (WorkCenterRespVO item : itemList){

            // 工作中心类型
            String workCenterTypeDictId = item.getWorkCenterTypeDictId();
            if(StrUtilX.isNotEmpty(workCenterTypeDictId)){
                workCenterTypeDictId = CustomerDictEnum.WORK_CENTER_TYPE.getDictCode() + "-" + workCenterTypeDictId;
                item.setWorkCenterTypeDictName(dictMap.get(workCenterTypeDictId));
            }

            // 资源类型(
            String resourcesTypeDictId = item.getResourcesTypeDictId();
            if(StrUtilX.isNotEmpty(resourcesTypeDictId)){
                resourcesTypeDictId = CustomerDictEnum.RESOURCES_TYPE.getDictCode() + "-" + resourcesTypeDictId;
                item.setResourcesTypeDictName(dictMap.get(resourcesTypeDictId));
            }

            //审核状态
            if(item.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + item.getDataStatus();
                item.setDataStatusDictName(dictMap.get(dataStatus));
            }

            //查询负责人
            UserBaseRespBO dirEmployee = userNameMap.get(item.getDirectorId());
            if (dirEmployee!=null){
                item.setDirectorName(dirEmployee.getEmployeeName());
            }

            //查询责任部门
            item.setDirectorOrgName(orgNameMap.get(item.getDirectorOrgId()));

            //详情查询
            List<DeviceRelRespVO> deviceJson = new ArrayList<>();
            if (isDetailQuery){

                DeviceRelQueryReqVO deviceRelQueryReqVO = new DeviceRelQueryReqVO();
                deviceRelQueryReqVO.setRelatedOrderId(item.getWorkCenterId());
                List<DeviceRelDO> deviceRelList = deviceRelMapper.selectList(deviceRelQueryReqVO);

                for (DeviceRelDO device : deviceRelList){

                    DeviceRespVO deviceRespVO = deviceInfoMap.get(device.getDeviceId());
                    if (deviceRespVO!=null){
                        DeviceRelRespVO deviceRelRespVO = BeanUtilX.copy(deviceRespVO, DeviceRelRespVO::new);
                        deviceRelRespVO.setRemark(device.getRemark());
                        deviceRelRespVO.setRowNo(device.getRowNo());
                        deviceRelRespVO.setRelatedOrderId(device.getRelatedOrderId());
                        deviceRelRespVO.setRelatedOrderCode(device.getRelatedOrderCode());
                        deviceJson.add(deviceRelRespVO);
                    }
                }
                item.setDeviceInfo(deviceJson);

                //员工信息
                WorkCenterPersonQueryReqVO workCenterPersonQuery = new WorkCenterPersonQueryReqVO();
                workCenterPersonQuery.setWorkCenterId(item.getWorkCenterId());
                List<WorkCenterPersonDO> workCenterPersonList = workCenterPersonMapper.selectList(workCenterPersonQuery);

                List<WorkCenterPersonRespVO> personJson = new ArrayList<>();
                if (CollUtilX.isNotEmpty(workCenterPersonList)){
                    for (WorkCenterPersonDO personResp : workCenterPersonList){
                        UserBaseRespBO employee = userNameMap.get(personResp.getUserId());
                        if (employee!=null){
                            WorkCenterPersonRespVO workCenterPersonResp = BeanUtilX.copy(employee, WorkCenterPersonRespVO::new);
                            //查询员工职位信息
                            PositionDO positionDO = positionService.positionDetail(employee.getPositionId());
                            if (positionDO!=null){
                                workCenterPersonResp.setPositionName(positionDO.getPositionName());
                            }

                            workCenterPersonResp.setRowNo(personResp.getRowNo());
                            workCenterPersonResp.setWorkCenterId(personResp.getWorkCenterId());
                            workCenterPersonResp.setWorkCenterCode(personResp.getWorkCenterCode());
                            personJson.add(workCenterPersonResp);
                        }
                    }

                    item.setPersonInfo(personJson);
                }else {
                    item.setPersonInfo(new ArrayList<>());
                }

//                List<JSONObject> personJson = new ArrayList<>();
//                if (CollUtilX.isNotEmpty(item.getPersonIds())){
//                    for (Long personId : item.getPersonIds()){
//                        UserBaseRespBO employee = userNameMap.get(personId);
//                        if (employee!=null){
//                            JSONObject jsonObject = (JSONObject) JSON.toJSON(employee);
//                            jsonObject.put("userId",employee.getUserId());
//                            //查询员工职位信息
//                            PositionDO positionDO = positionService.positionDetail(employee.getPositionId());
//                            if (positionDO!=null){
//                                jsonObject.put("positionName",positionDO.getPositionName());
//                            }
//                            personJson.add(jsonObject);
//                        }
//                    }
//
//                    item.setPersonInfo(personJson);
//                }else {
//                    item.setPersonInfo(new ArrayList<>());
//                }
            }

        }
    }

}
