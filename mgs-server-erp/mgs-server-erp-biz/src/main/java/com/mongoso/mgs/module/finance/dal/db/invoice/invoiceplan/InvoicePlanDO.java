package com.mongoso.mgs.module.finance.dal.db.invoice.invoiceplan;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 开票计划 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp.u_invoice_plan", autoResultMap = true)
//@KeySequence("erp.u_invoice_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePlanDO extends OperateDO {

    /** 开票计划id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long invoicePlanId;

    /** 开票方向 */
    private Short billingDirection;

    /** 开票申请状态 */
    private Short invoiceApplyStatus;

    /** 待开票计划id */
    private Long invoicePendingPlanId;

    /** 待开票计划明细id */
    private Long invoicePendingPlanDetailId;

    /** 票据类型 */
    private Long invoiceTypeDictId;
    private String invoiceTypeDictName;

    /** 开票计划单号 */
    private String invoicePlanNo;

    /** 来源单号 */
    private String sourceOrderCode;

    /** 币种id */
    private String currencyDictId;

    /** 客户id */
    private Long customerId;

    /** 计划开票总额（含税） */
    private BigDecimal planInvoiceTotalInclTax;

    /** 剩余计划开票总额（含税） */
    private BigDecimal remainPlanTotalInclTax;

    /** 计划开票日期 */
    private LocalDate planInvoiceDate;

    /** 来源单据类型 */
    private Short sourceFormType;

    /** 单据时间 */
    private LocalDateTime formDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    private LocalDateTime approvedDt;

    /** 备注 */
    private String remark;

    /** 责任人 */
    private Long directorId;

    /** 责任部门 */
    private String directorOrgId;

    private BigDecimal planInvoiceTotalExclTax;

    private BigDecimal totalTax;

    private BigDecimal readyQty;

    private BigDecimal readyAmt;

    private BigDecimal canQty;

    private BigDecimal canAmt;// 可申请金额

    //开票策略
    private String invoiceStrategy;

    /**
     * 收款计划策略--取子集
     */
    private String collectionPlanStrategy;

    /** 跳过开票申请 */
    private Integer skipInvoiceApply;

    /** 已开票数量 */
    private BigDecimal readyInvoiceQty;

    /** 已开票金额 */
    private BigDecimal readyInvoiceAmt;

    /** 可开票数量 */
    private BigDecimal canInvoiceQty;

    /** 可开票金额 */
    private BigDecimal canInvoiceAmt;

    /** 源头单据id */
    private Long originOrderId;

    /** 版本号 */
//    private Integer version;

    /** 本币币种 */
    private String localCurrencyDictId;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 本币计划开票总额（含税） */
    private BigDecimal localCurrencyPlanInvoiceTotalInclTax;

    /** 本币已开票金额 */
    private BigDecimal localCurrencyReadyInvoiceAmt;

    /** 本币可开票金额 */
    private BigDecimal localCurrencyCanInvoiceAmt;
}
