package com.mongoso.mgs.module.finance.controller.admin.cashbank.usedrecord.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 承兑汇票关联核销 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class UsedRecordBaseVO implements Serializable {

    /** 主键ID */
    private Long usedRecordId;

    /** 承兑汇票ID */
    @NotNull(message = "承兑汇票ID不能为空")
    private Long acceptBillId;
    /** 票据包号 */
    private String ticketBillPackageNo;
    /** 子票区间下限 */
    private Long subTicketRangeLower;

    /** 子票区间上限 */
    private Long subTicketRangeUpper;
    /** 来源单据类型 */
    @NotNull(message = "来源单据类型不能为空")
    private Short sourceFormType;

    /** 账目明细类型 */
    @NotNull(message = "账目明细类型不能为空")
    private Integer accountDetailType;

    /** 来源单号 */
    @NotEmpty(message = "来源单号不能为空")
    private String sourceOrderCode;

    /** 来源单据ID */
    @NotNull(message = "来源单据ID不能为空")
    private Long sourceFormId;

    /** 责任人 */
    @NotNull(message = "责任人不能为空")
    private Long directorId;

    private String directorName;

    /** 责任部门 */
    private String directorOrgId;

    /** 单据时间 */
    @NotNull(message = "单据时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime formDt;

    /** 审核状态 */
    private Short dataStatus;

    /** 审核人 */
    private String approvedBy;

    /** 审核时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime approvedDt;

}
