package com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticePageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.PurchaseReceiptNoticeRespVO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购收货通知单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseReceiptNoticeMapper extends BaseMapperX<PurchaseReceiptNoticeDO> {

    IPage<PurchaseReceiptNoticeRespVO> queryPage(Page<PurchaseReceiptNoticeRespVO> page, @Param("reqVO") PurchaseReceiptNoticePageReqVO reqVO);

    default List<PurchaseReceiptNoticeDO> selectList(PurchaseReceiptNoticeQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<PurchaseReceiptNoticeDO>lambdaQueryX()
                .likeIfPresent(PurchaseReceiptNoticeDO::getReceiptNoticeCode, reqVO.getReceiptNoticeCode())
                .eqIfPresent(PurchaseReceiptNoticeDO::getPurchaseOrderId, reqVO.getPurchaseOrderId())
                .likeIfPresent(PurchaseReceiptNoticeDO::getPurchaseOrderCode, reqVO.getPurchaseOrderCode())
                .eqIfPresent(PurchaseReceiptNoticeDO::getPurchaseTypeDictId, reqVO.getPurchaseTypeDictId())
                .eqIfPresent(PurchaseReceiptNoticeDO::getRelatedSupplierId, reqVO.getRelatedSupplierId())
                .likeIfPresent(PurchaseReceiptNoticeDO::getContactName, reqVO.getContactName())
                .eqIfPresent(PurchaseReceiptNoticeDO::getContactPhone, reqVO.getContactPhone())
                .eqIfPresent(PurchaseReceiptNoticeDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PurchaseReceiptNoticeDO::getIsIssueNoticeReceipt, reqVO.getIsIssueNoticeReceipt())
                .eqIfPresent(PurchaseReceiptNoticeDO::getIsFullReceipted, reqVO.getIsFullReceipted())
                .betweenIfPresent(PurchaseReceiptNoticeDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(PurchaseReceiptNoticeDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(PurchaseReceiptNoticeDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(PurchaseReceiptNoticeDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(PurchaseReceiptNoticeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(PurchaseReceiptNoticeDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(PurchaseReceiptNoticeDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(PurchaseReceiptNoticeDO::getCreatedDt));
    }

}