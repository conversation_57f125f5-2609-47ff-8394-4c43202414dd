package com.mongoso.mgs.module.purchase.service.deduction.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailAditReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.deduction.vo.detail.PurchaseDeductionDetailRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 采购扣费单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseDeductionDetailService {

    /**
     * 获得采购扣费单明细列表
     *
     * @param reqVO 查询条件
     * @return 采购扣费单明细列表
     */
    List<PurchaseDeductionDetailRespVO> purchaseDeductionDetailList(@Valid PurchaseDeductionDetailQueryReqVO reqVO);

    /**
     * 获得采购扣费单明细分页
     *
     * @param reqVO 查询条件
     * @return 采购扣费单明细分页
     */
    PageResult<PurchaseDeductionDetailRespVO> purchaseDeductionDetailPage(@Valid PurchaseDeductionDetailPageReqVO reqVO);

}
