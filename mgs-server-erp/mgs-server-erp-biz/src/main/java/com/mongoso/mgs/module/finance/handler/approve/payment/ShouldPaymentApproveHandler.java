package com.mongoso.mgs.module.finance.handler.approve.payment;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.mongoso.mgs.common.enums.OrderTypeEnum;
import com.mongoso.mgs.common.enums.order.OrderStatusEnum;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.DataButtonEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.request.ApproveCommonAttrs;
import com.mongoso.mgs.component.flow.service.handler.request.BaseApproveRequest;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.finance.dal.db.accountorder.AccountOrderDO;
import com.mongoso.mgs.module.finance.dal.db.accountorderdetail.AccountOrderDetailDO;
import com.mongoso.mgs.module.finance.dal.db.payment.PaymentOrderDO;
import com.mongoso.mgs.module.finance.dal.db.paymentapply.PaymentApplyDO;
import com.mongoso.mgs.module.finance.dal.db.pendingpaymentplan.PendingPaymentPlanDO;
import com.mongoso.mgs.module.finance.dal.db.pendingpaymentplandetail.PendingPaymentPlanDetailDO;
import com.mongoso.mgs.module.finance.dal.db.refund.RefundDO;
import com.mongoso.mgs.module.finance.dal.db.shouldpayment.ShouldPaymentDO;
import com.mongoso.mgs.module.finance.dal.db.shouldpaymentdetail.ShouldPaymentDetailDO;
import com.mongoso.mgs.module.finance.dal.mysql.accountorder.AccountOrderMapper;
import com.mongoso.mgs.module.finance.dal.mysql.accountorderdetail.AccountOrderDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.payment.PaymentOrderMapper;
import com.mongoso.mgs.module.finance.dal.mysql.paymentapply.PaymentApplyMapper;
import com.mongoso.mgs.module.finance.dal.mysql.pendingpaymentplan.PendingPaymentPlanMapper;
import com.mongoso.mgs.module.finance.dal.mysql.pendingpaymentplandetail.PendingPaymentPlanDetailMapper;
import com.mongoso.mgs.module.finance.dal.mysql.refund.RefundMapper;
import com.mongoso.mgs.module.finance.dal.mysql.shouldpayment.ShouldPaymentMapper;
import com.mongoso.mgs.module.finance.dal.mysql.shouldpaymentdetail.ShouldPaymentDetailMapper;
import com.mongoso.mgs.module.payment.dal.db.paymentrelation.PaymentRelationDO;
import com.mongoso.mgs.module.payment.dal.mysql.paymentrelation.PaymentRelationMapper;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.bo.PurchaseOrderBO;
import com.mongoso.mgs.module.purchase.dal.db.purchase.PurchaseOrderDO;
import com.mongoso.mgs.module.purchase.dal.mysql.purchase.PurchaseOrderMapper;
import com.mongoso.mgs.module.purchase.service.processout.PurchaseProcessOutService;
import com.mongoso.mgs.module.purchase.service.purchase.PurchaseOrderService;
import com.mongoso.mgs.module.purchase.service.purchase.detail.PurchaseOrderDetailService;
import com.mongoso.mgs.module.sale.controller.admin.erpsaleorder.bo.ErpSaleOrderBO;
import com.mongoso.mgs.module.sale.service.erpsaleorder.ErpSaleOrderService;
import com.mongoso.mgs.module.sale.service.erpsaleorderdetail.ErpSaleOrderDetailService;
import com.mongoso.mgs.module.warehouse.dal.db.erpdelivery.ErpDeliveryDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpinbound.ErpInboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpoutbound.ErpOutboundDO;
import com.mongoso.mgs.module.warehouse.dal.db.erpreceipt.ErpReceiptDO;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpdelivery.ErpDeliveryMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpinbound.ErpInboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpoutbound.ErpOutboundMapper;
import com.mongoso.mgs.module.warehouse.dal.mysql.erpreceipt.ErpReceiptMapper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @author: daijinbiao
 * @date: 2024-12-4 10:54:03
 * @description: 应收账款单审批流程处理类
 */
//todo 待处理
@Component
public class ShouldPaymentApproveHandler extends FlowApproveHandler<ShouldPaymentDO> {

    @Resource
    private ShouldPaymentMapper paymentMapper;
    @Resource
    private ShouldPaymentDetailMapper paymentDetailMapper;
    @Resource
    private PendingPaymentPlanMapper paymentPlanMapper;
    @Resource
    private PendingPaymentPlanDetailMapper paymentPlanDetailMapper;
    @Resource
    private AccountOrderMapper orderMapper;
    @Resource
    private AccountOrderDetailMapper orderDetailMapper;
    @Resource
    private PaymentApplyMapper applyMapper;
    @Resource
    private PaymentRelationMapper relationMapper;
    @Resource
    private PaymentOrderMapper paymentOrderMapper;
    @Resource
    private RefundMapper refundMapper;

    @Resource
    private ErpSaleOrderDetailService erpSaleOrderDetailService;

    @Resource
    private ErpDeliveryMapper erpDeliveryMapper;

    @Resource
    private ErpOutboundMapper erpOutboundMapper;

    @Resource
    private ErpReceiptMapper erpReceiptMapper;

    @Resource
    private ErpInboundMapper erpInboundMapper;

    @Resource
    private PurchaseOrderDetailService purchaseDetailService;

    @Lazy
    @Resource
    private ErpSaleOrderService erpSaleOrderService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    @Lazy
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private PurchaseProcessOutService purchaseProcessOutService;

    @Override
    protected ApproveCommonAttrs approvalAttributes(ShouldPaymentDO item) {
        String tableName = EntityUtilX.getTableNameByAnnot(ShouldPaymentDO.class);
        String pkFieldName = EntityUtilX.getPkFieldName(ShouldPaymentDO.class);

        ApproveCommonAttrs attrs = ApproveCommonAttrs.builder()
                .objId(item.getPaymentId())
                .objCode(item.getAccountsPayableNumber())
                .tableName(tableName)
                .pkFieldName(pkFieldName)
                .build();

        return attrs;
    }

    @Override
    protected Boolean businessVerify(ShouldPaymentDO item, BaseApproveRequest request) {
        // 具体业务校验逻辑
        Integer buttonType = request.getButtonType();
        FailItem failItem = request.getFailItem();
        //审核校验 上游 若是 对账单 未审核，就报错
        if (buttonType == DataButtonEnum.APPROVE.getKey()
            && item.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.type.shortValue()
                || item.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.type.shortValue()) {
            AccountOrderDO orderDO = orderMapper.selectById(item.getSourceOrderId());
            if(null == orderDO || orderDO.getDataStatus() == DataStatusEnum.NOT_APPROVE.key.shortValue()){
                failItem.setCode(item.getAccountsPayableNumber());
                failItem.setReason("上游对账单不存在或未审核，不能审核");
                return false;
            }
            // 现有的明细
            List<ShouldPaymentDetailDO> detailDOS = paymentDetailMapper.selectList(LambdaQueryWrapperX.<ShouldPaymentDetailDO>lambdaQueryX()
                    .eq(ShouldPaymentDetailDO::getPaymentId, item.getPaymentId())
            );
            // 通过账款去找 待收款计划，根据来源单号找计划
            PendingPaymentPlanDO planDO = paymentPlanMapper.selectOne(LambdaQueryWrapperX.<PendingPaymentPlanDO>lambdaQueryX()
                    .eq(PendingPaymentPlanDO::getSourceOrderId, item.getSourceOrderId())
            );
            if(ObjUtilX.isEmpty(planDO)){
                failItem.setCode(item.getAccountsPayableNumber());
                failItem.setReason("找不到关联的待计划信息!");
                //throw new BizException("5001", "找不到关联的待计划信息!");
                return false;
            }

            for (ShouldPaymentDetailDO detailDO : detailDOS) {

                // 修改上游的待收款计划明细  这里循环处理先
                PendingPaymentPlanDetailDO pendingPaymentDetailDO = paymentPlanDetailMapper.selectOne(LambdaQueryWrapperX.<PendingPaymentPlanDetailDO>lambdaQueryX()
                        .eq(PendingPaymentPlanDetailDO::getPlanId, planDO.getPlanId())
                        .eq(PendingPaymentPlanDetailDO::getSourceOrderCode, detailDO.getSourceOrderCode())
                        .eq(PendingPaymentPlanDetailDO::getSourceLineNumber, detailDO.getSourceLineNumber())
                        .eq(PendingPaymentPlanDetailDO::getMaterialId, detailDO.getMaterialId())
                );

                if (null == pendingPaymentDetailDO) {
                    failItem.setCode(item.getAccountsPayableNumber());
                    failItem.setReason("找不到关联的待计划明细信息!");
                    //throw new BizException("5001", "找不到关联的待计划明细信息!");
                    return false;
                }

                BigDecimal ableQty = BigDecimal.ZERO;//操作后的可对账数量
                BigDecimal ableAmt = BigDecimal.ZERO;//操作后的剩余计划金额

                // 如果最终对账单总额为 0,则为已收款
                if (item.getTotalAmt().compareTo(BigDecimal.ZERO) == 0) {
                    item.setAccountsPayableStatus(OrderStatusEnum.APPLYED.getCode().shortValue());
                }

                // 已收数量 += 本次应收数量
                pendingPaymentDetailDO.setReceivedQty(pendingPaymentDetailDO.getReceivedQty().add(detailDO.getCurrentReceiveQty()));
                // 已收金额 += 本次应收金额
                pendingPaymentDetailDO.setReceivedAmt(pendingPaymentDetailDO.getReceivedAmt().add(detailDO.getInclCurrentReceiveAmt()));

                // 剩余计划数量 -= 本次应收数量
                ableQty = pendingPaymentDetailDO.getReceiveableQty().subtract(detailDO.getCurrentReceiveQty());
                // 剩余计划金额 -= 本次应收金额
                ableAmt = pendingPaymentDetailDO.getReceiveableAmt().subtract(detailDO.getInclCurrentReceiveAmt());

                // 超额处理
                String title = "本次应付金额不能超过可计划金额！";
                String title2 = "本次应付数量不能超过可计划数量！";
                if (item.getFormType() == 1) {
                    title = "本次应收金额不能超过可计划金额！";
                    title2 = "本次应收数量不能超过可计划数量！";
                }
                if (item.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE1.type.shortValue() || item.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE2.type.shortValue()) {
                    title = "本次应付金额不能大于发票金额（含税!）";
                    if (item.getFormType() == 1) {
                        title = "本次应收金额不能大于发票金额（含税!）";
                    }
                }
                if (pendingPaymentDetailDO.getTotalAmt().compareTo(BigDecimal.ZERO) > 0) {
                    if (detailDO.getInclCurrentReceiveAmt().compareTo(BigDecimal.ZERO) < 0) {
                        failItem.setCode(item.getAccountsPayableNumber());
                        failItem.setReason(title);
                        //throw new BizException("5001", title);
                        return false;
                    }
                    if (ableAmt.compareTo(BigDecimal.ZERO) < 0) {// 负数的时候判断大于0
                        failItem.setCode(item.getAccountsPayableNumber());
                        failItem.setReason(title);
                        //throw new BizException("5001", title);
                        return false;
                    }
                    if (ableQty.compareTo(BigDecimal.ZERO) < 0) {// 负数的时候判断大于0
                        failItem.setCode(item.getAccountsPayableNumber());
                        failItem.setReason(title2);
                        //throw new BizException("5001", title2);
                        return false;
                    }
                } else {
                    if (detailDO.getInclCurrentReceiveAmt().compareTo(BigDecimal.ZERO) > 0) {
                        failItem.setCode(item.getAccountsPayableNumber());
                        failItem.setReason(title);
                        //throw new BizException("5001", title);
                        return false;
                    }
                    if (ableAmt.compareTo(BigDecimal.ZERO) > 0) {// 负数的时候判断大于0
                        failItem.setCode(item.getAccountsPayableNumber());
                        failItem.setReason(title);
                        //throw new BizException("5001", title);
                        return false;
                    }
                    if (ableQty.compareTo(BigDecimal.ZERO) > 0) {// 负数的时候判断大于0
                        failItem.setCode(item.getAccountsPayableNumber());
                        failItem.setReason(title2);
                        //throw new BizException("5001", title2);
                        return false;
                    }
                }
            }
            //销售订单
//            if (item.getSourceFormType() == OrderTypeEnum.SALE_ORDER.getType().shortValue()) {
//                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getSourceOrderId());
//                if (erpSaleOrder != null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type) {
//                    failItem.setCode(item.getAccountsPayableNumber());
//                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
//                    return false;
//                }
//            }
//
//            if (item.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()) {
//                //采购订单
//                PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(item.getSourceOrderId());
//                if (purchaseOrder != null){
//                    if (purchaseOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || purchaseOrder.getFormStatus() == FormStatusEnum.CLOSED.type) {
//                        failItem.setCode(item.getAccountsPayableNumber());
//                        failItem.setReason(RELATED_PURCHASE_FORM_STATUS.getMsg());
//                        return false;
//                    }
//                }else {
//                    //工序委外采购订单
//                    PurchaseProcessOutDO purchaseProcessOutDO = processOutMapper.selectById(item.getSourceOrderId());
//                    if (purchaseProcessOutDO != null && purchaseProcessOutDO.getFormStatus() == FormStatusEnum.COMPLETED.type || purchaseProcessOutDO.getFormStatus() == FormStatusEnum.CLOSED.type) {
//                        failItem.setCode(item.getAccountsPayableNumber());
//                        failItem.setReason(PURCHASE_PROCESS_OUT_FORM_STATUS.getMsg());
//                        return false;
//                    }
//                }
//
//            }

        }

        //反审核校验 需下游（收款申请（正），销售退款（负）） 已审核，就报错
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {


            if(item.getTotalAmt().compareTo(BigDecimal.ZERO) > 0) { //正数 收款申请
                if (item.getSkipPaymentApply() == 0 && item.getFormType() == 2) {
                    Long lastCount = applyMapper.selectCount(LambdaQueryWrapperX.<PaymentApplyDO>lambdaQueryX()
                            .eq(PaymentApplyDO::getPaymentId, item.getPaymentId())
                            .eq(PaymentApplyDO::getDataStatus, DataStatusEnum.APPROVED.getKey())
                    );
                    if(lastCount > 0){
                        failItem.setReason("下游存在已审核的付款申请，不能反审核");
                        failItem.setCode(item.getAccountsPayableNumber());
                        return false;
                    }
                }else {
                    List<PaymentRelationDO> relationDOS = relationMapper.selectListByRelatedOrderId(item.getPaymentId());
                    List<Long> payIdList = Lists.transform(relationDOS, PaymentRelationDO::getOrderId);
                    Long paymentLastCount = 0L;
                    if(ObjUtilX.isNotEmpty(payIdList)){
                        paymentLastCount = paymentOrderMapper.selectCount(LambdaQueryWrapperX.<PaymentOrderDO>lambdaQueryX()
                                .in(PaymentOrderDO::getPayId, payIdList)
                                .eq(PaymentOrderDO::getDataStatus, DataStatusEnum.APPROVED.getKey())
                        );
                    }
                    if(paymentLastCount > 0){
                        if (item.getFormType() == 1){
                            failItem.setReason("下游存在已审核的销售收款，不能反审核");
                            failItem.setCode(item.getAccountsPayableNumber());
                            return false;
                        }else{
                            failItem.setReason("下游存在已审核的采购付款，不能反审核");
                            failItem.setCode(item.getAccountsPayableNumber());
                            return false;
                        }
                    }
                }

            }else{//负数 销售退款
                List<PaymentRelationDO> relationDOS = relationMapper.selectListByRelatedOrderId(item.getPaymentId());
                List<Long> payIdList = Lists.transform(relationDOS, PaymentRelationDO::getOrderId);
                Long lastCount = 0L;
                if(ObjUtilX.isNotEmpty(payIdList)){
                    lastCount = refundMapper.selectCount(LambdaQueryWrapperX.<RefundDO>lambdaQueryX()
                            .in(RefundDO::getRefundId, payIdList)
                            .eq(RefundDO::getDataStatus, DataStatusEnum.APPROVED.getKey())
                    );
                }

                if(lastCount > 0){
                    if(item.getFormType() == 1) {
                        failItem.setReason("下游存在已审核的销售退款，不能反审核");
                    }else{
                        failItem.setReason("下游存在已审核的采购退款，不能反审核");
                    }
                    failItem.setCode(item.getAccountsPayableNumber());
                    return false;
                }
            }

            //销售订单
//            if (item.getSourceFormType() == OrderTypeEnum.SALE_ORDER.getType().shortValue()) {
//                ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(item.getSourceOrderId());
//                if (erpSaleOrder != null && erpSaleOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || erpSaleOrder.getFormStatus() == FormStatusEnum.CLOSED.type) {
//                    failItem.setCode(item.getAccountsPayableNumber());
//                    failItem.setReason(RELATED_SALE_FORCE_CLOSE.getMsg());
//                    return false;
//                }
//            }
//
//
//            if (item.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()) {
//                //采购订单
//                PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(item.getSourceOrderId());
//                if (purchaseOrder != null){
//                    if (purchaseOrder.getFormStatus() == FormStatusEnum.COMPLETED.type || purchaseOrder.getFormStatus() == FormStatusEnum.CLOSED.type) {
//                        failItem.setCode(item.getAccountsPayableNumber());
//                        failItem.setReason(RELATED_PURCHASE_FORM_STATUS.getMsg());
//                        return false;
//                    }
//                }else {
//                    //工序委外采购订单
//                    PurchaseProcessOutDO purchaseProcessOutDO = processOutMapper.selectById(item.getSourceOrderId());
//                    if (purchaseProcessOutDO != null && purchaseProcessOutDO.getFormStatus() == FormStatusEnum.COMPLETED.type || purchaseProcessOutDO.getFormStatus() == FormStatusEnum.CLOSED.type) {
//                        failItem.setCode(item.getAccountsPayableNumber());
//                        failItem.setReason(PURCHASE_PROCESS_OUT_FORM_STATUS.getMsg());
//                        return false;
//                    }
//                }
//
//            }
        }

        return true;
    }

    /**
     * 账款上游数据来源: 对账单或者待计划
     *
     * @param currentDO
     * @param request
     * @return
     */
    @Override
    public Integer handleBusinessData(ShouldPaymentDO currentDO, BaseApproveRequest request) {
        //当前对象
        if (currentDO == null){
            return 1;
        }
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        Long id = currentDO.getPaymentId();
        Integer buttonType = request.getButtonType();
        Integer dataStatus = request.getDataStatus();
        Short oldStatus = currentDO.getDataStatus();
        FailItem failItem = request.getFailItem();

        ShouldPaymentDO exist  = currentDO;
        exist.setApprovedBy(loginUser.getFullUserName());
        exist.setApprovedDt(LocalDateTime.now());
        exist.setDataStatus(dataStatus.shortValue());

        // 审核通过，反审核通过
        if (buttonType == DataButtonEnum.APPROVE.getKey() || buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            // 现有的明细
            List<ShouldPaymentDetailDO> detailDOS = paymentDetailMapper.selectList(LambdaQueryWrapperX.<ShouldPaymentDetailDO>lambdaQueryX()
                    .eq(ShouldPaymentDetailDO::getPaymentId, exist.getPaymentId())
            );
            // 通过账款去找 待收款计划，根据来源单号找计划
            PendingPaymentPlanDO planDO = paymentPlanMapper.selectOne(LambdaQueryWrapperX.<PendingPaymentPlanDO>lambdaQueryX()
                    .eq(PendingPaymentPlanDO::getSourceOrderId, exist.getSourceOrderId())
            );
            if(ObjUtilX.isEmpty(planDO)){
                failItem.setCode(exist.getAccountsPayableNumber());
                failItem.setReason("找不到关联的待计划信息!");
                throw new BizException("5001", "找不到关联的待计划信息!");
            }

            // 已计划金额
            BigDecimal totalPlanAmt = planDO.getPlanAmt();
            //剩余可计划金额
            BigDecimal totalRemAmt = planDO.getRemainingPlanAmt();

            for (ShouldPaymentDetailDO detailDO : detailDOS) {

                // 修改上游的待收款计划明细  这里循环处理先
                PendingPaymentPlanDetailDO pendingPaymentDetailDO = paymentPlanDetailMapper.selectOne(LambdaQueryWrapperX.<PendingPaymentPlanDetailDO>lambdaQueryX()
                        .eq(PendingPaymentPlanDetailDO::getPlanId, planDO.getPlanId())
                        .eq(PendingPaymentPlanDetailDO::getSourceOrderCode, detailDO.getSourceOrderCode())
                        .eq(PendingPaymentPlanDetailDO::getSourceLineNumber, detailDO.getSourceLineNumber())
                        .eq(PendingPaymentPlanDetailDO::getMaterialId, detailDO.getMaterialId())
                );

                if(null == pendingPaymentDetailDO){
                    failItem.setCode(exist.getAccountsPayableNumber());
                    failItem.setReason("找不到关联的待计划明细信息!");
                    throw new BizException("5001", "找不到关联的待计划明细信息!");
                }

                BigDecimal ableQty = BigDecimal.ZERO;//操作后的可对账数量
                BigDecimal ableAmt = BigDecimal.ZERO;//操作后的剩余计划金额

                if(buttonType == DataButtonEnum.APPROVE.getKey()) {
                    // 如果最终对账单总额为 0,则为已收款
                    if(exist.getTotalAmt().compareTo(BigDecimal.ZERO) == 0){
                        exist.setAccountsPayableStatus(OrderStatusEnum.APPLYED.getCode().shortValue());
                    }

                    // 已收数量 += 本次应收数量
                    pendingPaymentDetailDO.setReceivedQty(pendingPaymentDetailDO.getReceivedQty().add(detailDO.getCurrentReceiveQty()));
                    // 已收金额 += 本次应收金额
                    pendingPaymentDetailDO.setReceivedAmt(pendingPaymentDetailDO.getReceivedAmt().add(detailDO.getInclCurrentReceiveAmt()));

                    // 剩余计划数量 -= 本次应收数量
                    ableQty = pendingPaymentDetailDO.getReceiveableQty().subtract(detailDO.getCurrentReceiveQty());
                    // 剩余计划金额 -= 本次应收金额
                    ableAmt = pendingPaymentDetailDO.getReceiveableAmt().subtract(detailDO.getInclCurrentReceiveAmt());

                    // 超额处理
                    String title = "本次应付金额不能超过可计划金额！";
                    String title2 = "本次应付数量不能超过可计划数量！";
                    if (exist.getFormType() == 1){
                        title = "本次应收金额不能超过可计划金额！";
                        title2 = "本次应收数量不能超过可计划数量！";
                    }
                    if (exist.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE1.type.shortValue() || exist.getSourceFormType() == OrderTypeEnum.INVOICE_ISSUE2.type.shortValue()){
                        title = "本次应付金额不能大于发票金额（含税!）";
                        if (exist.getFormType() == 1){
                            title = "本次应收金额不能大于发票金额（含税!）";
                        }
                    }
                    if (pendingPaymentDetailDO.getTotalAmt().compareTo(BigDecimal.ZERO) > 0){
                        if (detailDO.getInclCurrentReceiveAmt().compareTo(BigDecimal.ZERO) < 0) {
                            failItem.setCode(exist.getAccountsPayableNumber());
                            failItem.setReason(title);
                            throw new BizException("5001", title);
                        }
                        if (ableAmt.compareTo(BigDecimal.ZERO) < 0) {// 负数的时候判断大于0
                            failItem.setCode(exist.getAccountsPayableNumber());
                            failItem.setReason(title);
                            throw new BizException("5001", title);
                        }
                        if (ableQty.compareTo(BigDecimal.ZERO) < 0) {// 负数的时候判断大于0
                            failItem.setCode(exist.getAccountsPayableNumber());
                            failItem.setReason(title2);
                            throw new BizException("5001", title2);
                        }
                    }else{
                        if (detailDO.getInclCurrentReceiveAmt().compareTo(BigDecimal.ZERO) > 0) {
                            failItem.setCode(exist.getAccountsPayableNumber());
                            failItem.setReason(title);
                            throw new BizException("5001", title);
                        }
                        if (ableAmt.compareTo(BigDecimal.ZERO) > 0) {// 负数的时候判断大于0
                            failItem.setCode(exist.getAccountsPayableNumber());
                            failItem.setReason(title);
                            throw new BizException("5001", title);
                        }
                        if (ableQty.compareTo(BigDecimal.ZERO) > 0) {// 负数的时候判断大于0
                            failItem.setCode(exist.getAccountsPayableNumber());
                            failItem.setReason(title2);
                            throw new BizException("5001", title2);
                        }
                    }

                    // 累加已计划金额
                    totalPlanAmt = totalPlanAmt.add(detailDO.getInclCurrentReceiveAmt());
                    // 累减剩余可计划金额
                    totalRemAmt = totalRemAmt.subtract(detailDO.getInclCurrentReceiveAmt());

                }else{//反审核 相反的操作
                    // 如果最终对账单总额为 0
                    if(exist.getTotalAmt().compareTo(BigDecimal.ZERO) == 0){
                        exist.setAccountsPayableStatus(OrderStatusEnum.NOT_APPLY.getCode().shortValue());
                    }

                    // 已收数量 -= 本次应收数量
                    pendingPaymentDetailDO.setReceivedQty(pendingPaymentDetailDO.getReceivedQty().subtract(detailDO.getCurrentReceiveQty()));
                    // 已收金额 -= 本次应收金额
                    pendingPaymentDetailDO.setReceivedAmt(pendingPaymentDetailDO.getReceivedAmt().subtract(detailDO.getInclCurrentReceiveAmt()));

                    // 剩余计划数量 += 本次应收数量
                    ableQty = pendingPaymentDetailDO.getReceiveableQty().add(detailDO.getCurrentReceiveQty());
                    // 剩余计划金额 += 本次应收金额
                    ableAmt = pendingPaymentDetailDO.getReceiveableAmt().add(detailDO.getInclCurrentReceiveAmt());

                    // 累减已计划金额
                    totalPlanAmt = totalPlanAmt.subtract(detailDO.getInclCurrentReceiveAmt());
                    // 累加剩余可计划金额
                    totalRemAmt = totalRemAmt.add(detailDO.getInclCurrentReceiveAmt());
                }

                pendingPaymentDetailDO.setReceiveableQty(ableQty);
                pendingPaymentDetailDO.setReceiveableAmt(ableAmt);

                //回写待计划数据
                paymentPlanDetailMapper.updateById(pendingPaymentDetailDO);

                // 如果是对账单生成账款，还需要回写金额到对账单详细
                if(exist.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.type.shortValue()
                        || exist.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.type.shortValue()) {

                    //AccountOrderApproveHandler 存储对账单明细ID，审核时定位回写
                    AccountOrderDetailDO accountDetail = orderDetailMapper.selectOne(LambdaQueryWrapperX.<AccountOrderDetailDO>lambdaQueryX()
                            .eq(AccountOrderDetailDO::getAccountDetailId, detailDO.getAccountDetailId())
                    );

                    // 回写对账单明细的已对账数量
                    if(ObjUtilX.isNotEmpty(accountDetail)) {
                        // 审核
                        if (buttonType == DataButtonEnum.APPROVE.getKey()) {
                            //已计划数量|金额
                            accountDetail.setPlanAmt(accountDetail.getPlanAmt().add(detailDO.getInclCurrentReceiveAmt()));
                            accountDetail.setPlanQty(accountDetail.getPlanQty().add(detailDO.getCurrentReceiveQty()));

                            //可计划数量|金额
                            accountDetail.setPlannableAmt(accountDetail.getPlannableAmt().subtract(detailDO.getInclCurrentReceiveAmt()));
                            accountDetail.setPlannableQty(accountDetail.getPlannableQty().subtract(detailDO.getCurrentReceiveQty()));

                        } else if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
                            //已计划数量|金额
                            accountDetail.setPlanAmt(accountDetail.getPlanAmt().subtract(detailDO.getInclCurrentReceiveAmt()));
                            accountDetail.setPlanQty(accountDetail.getPlanQty().subtract(detailDO.getCurrentReceiveQty()));

                            //可计划数量|金额
                            accountDetail.setPlannableAmt(accountDetail.getPlannableAmt().add(detailDO.getInclCurrentReceiveAmt()));
                            accountDetail.setPlannableQty(accountDetail.getPlannableQty().add(detailDO.getCurrentReceiveQty()));
                        }
                        orderDetailMapper.updateById(accountDetail);
                    }
                }

                //回写已计划数量
                writeBackSaleOrPurchaseOrder(buttonType,exist,detailDO);
            }

            //更新计划金额
            planDO.setPlanAmt(totalPlanAmt);
            planDO.setRemainingPlanAmt(totalRemAmt);

            if(buttonType == DataButtonEnum.APPROVE.getKey()) {
                // 更新状态
                if (planDO.getRemainingPlanAmt().compareTo(BigDecimal.ZERO) == 0) {
                    planDO.setPaymentPlanStatus(OrderStatusEnum.PLANED.getCode().shortValue());
                } else {
                    planDO.setPaymentPlanStatus(OrderStatusEnum.PLANING.getCode().shortValue());
                }
            }else{
                // 更新状态
                if (planDO.getPlanAmt().compareTo(BigDecimal.ZERO) == 0) {
                    planDO.setPaymentPlanStatus(OrderStatusEnum.NOT_PLAN.getCode().shortValue());
                } else {
                    planDO.setPaymentPlanStatus(OrderStatusEnum.PLANING.getCode().shortValue());
                }
            }
            paymentPlanMapper.updateById(planDO);

            exist.setPlanId(planDO.getPlanId());

            // 修改销售对账单汇总
            if (exist.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.type.shortValue()
                    || exist.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.type.shortValue()) {
                AccountOrderDO orderDO = orderMapper.selectById(exist.getSourceOrderId());
                // 校验上面做了，这里不做了
                if(buttonType == DataButtonEnum.APPROVE.getKey()) {
                    orderDO.setPlanAmt(orderDO.getPlanAmt().add(exist.getTotalAmt()));
                    orderDO.setPlannableAmt(orderDO.getPlannableAmt().subtract(exist.getTotalAmt()));
                }else{
                    orderDO.setPlanAmt(orderDO.getPlanAmt().subtract(exist.getTotalAmt()));
                    orderDO.setPlannableAmt(orderDO.getPlannableAmt().add(exist.getTotalAmt()));
                }
                orderMapper.updateById(orderDO);
            }
        }


        if (exist.getSourceFormType() == OrderTypeEnum.SALE_ORDER.getType().shortValue()) {
            //销售销售订单状态
//            ErpSaleOrderDO erpSaleOrder = erpSaleOrderMapper.selectById(exist.getSourceOrderId());
            erpSaleOrderService.editChildrenOrderCount(exist.getSourceOrderId(), buttonType);
        }
        if (exist.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()) {
            PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(exist.getSourceOrderId());
            //销售订单状态修改
            if (purchaseOrder != null) {
                purchaseOrderService.editChildrenOrderCount(purchaseOrder.getPurchaseOrderId(), buttonType);
            }else {
                //工序委外采购订单状态修改
                purchaseProcessOutService.editChildrenOrderCount(exist.getSourceOrderId(), buttonType);
            }
        }

        //更新业务数据
        Integer updateCount = paymentMapper.updateById(exist);

        return updateCount;
    }


    /**
     * 回写已计划数量
     *
     * @param buttonType
     * @param shouldPaymentDO
     * @param detailDO
     */
    private void writeBackSaleOrPurchaseOrder(Integer buttonType, ShouldPaymentDO shouldPaymentDO,ShouldPaymentDetailDO detailDO) {
        BigDecimal decimal = new BigDecimal("1");
        if (buttonType == DataButtonEnum.NOT_APPROVE.getKey()) {
            decimal = new BigDecimal("-1");
        }
        BigDecimal currentReceiveQty = detailDO.getCurrentReceiveQty();
        currentReceiveQty = currentReceiveQty.multiply(decimal);

        //销售
        if (detailDO.getFormType() == 1) {
            ErpSaleOrderBO erpSaleOrderBO = new ErpSaleOrderBO();
            erpSaleOrderBO.setMaterialId(detailDO.getMaterialId());
            erpSaleOrderBO.setPlanedQty(currentReceiveQty);

            //销售订单
            if (detailDO.getSourceFormType() == OrderTypeEnum.SALE_ORDER.getType().shortValue()) {
                erpSaleOrderBO.setSaleOrderId(shouldPaymentDO.getSourceOrderId());
                erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
            }

            //销售发货单
            if (detailDO.getSourceFormType() == OrderTypeEnum.SALE_INVOICES_ORDER.getType().shortValue()) {
                ErpDeliveryDO erpDeliveryDO = erpDeliveryMapper.selectOne(ErpDeliveryDO::getDeliveryId, shouldPaymentDO.getSourceOrderId());
                if (!Objects.isNull(erpDeliveryDO)){
                    erpSaleOrderBO.setSaleOrderId(erpDeliveryDO.getSaleOrderId());
                    erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
                }
            }

            //销售出库单
            if (detailDO.getSourceFormType() == OrderTypeEnum.SALE_OUT_BOUND_ORDER.getType().shortValue()) {
                ErpOutboundDO erpOutboundDO = erpOutboundMapper.selectOne(ErpOutboundDO::getOutboundId, shouldPaymentDO.getSourceOrderId());
                if (!Objects.isNull(erpOutboundDO)){
                    erpSaleOrderBO.setSaleOrderId(erpOutboundDO.getSaleOrderId());
                    erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
                }
            }

            //销售对账单,判断对账单据来源是不是销售订单,如果是的话就回写
            if (detailDO.getSourceFormType() == OrderTypeEnum.SALE_STATEMENTS_ORDER.getType().shortValue()) {
                AccountOrderDetailDO accountOrderDetailDO = orderDetailMapper.selectById(detailDO.getSourceOrderId());
                if (!Objects.isNull(accountOrderDetailDO) && accountOrderDetailDO.getSourceFormType() == OrderTypeEnum.SALE_ORDER.getType().shortValue()){
                    erpSaleOrderBO.setSaleOrderId(accountOrderDetailDO.getSourceOrderId());
                    erpSaleOrderDetailService.writeBackSaleOrder(erpSaleOrderBO);
                }
            }

        }

        //采购
        if (detailDO.getFormType() == 2){
            PurchaseOrderBO purchaseOrderBO = new PurchaseOrderBO();
            purchaseOrderBO.setMaterialId(detailDO.getMaterialId());
            purchaseOrderBO.setPlanedQty(currentReceiveQty);

            //采购订单
            if (detailDO.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()){
                purchaseOrderBO.setPurchaseOrderId(shouldPaymentDO.getSourceOrderId());
                purchaseOrderBO.setRowNo(Integer.valueOf(detailDO.getSourceLineNumber()));
                purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
            }

            //采购收货单
            if (detailDO.getSourceFormType() == OrderTypeEnum.PURCHASE_RECEIPT_ORDER.getType().shortValue()){
                ErpReceiptDO erpReceiptDO = erpReceiptMapper.selectOne(ErpReceiptDO::getReceiptId, shouldPaymentDO.getSourceOrderId());
                if (!Objects.isNull(erpReceiptDO)){
                    purchaseOrderBO.setPurchaseOrderId(erpReceiptDO.getPurchaseOrderId());
                    purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
                }
            }

            //采购入库单
            if (detailDO.getSourceFormType() == OrderTypeEnum.PURCHASE_IN_BOUND_ORDER.getType().shortValue()){
                ErpInboundDO erpInboundDO = erpInboundMapper.selectOne(ErpInboundDO::getInboundId, shouldPaymentDO.getSourceOrderId());
                if (!Objects.isNull(erpInboundDO)){
                    purchaseOrderBO.setPurchaseOrderId(erpInboundDO.getPurchaseOrderId());
                    purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
                }
            }

            //采购对账单,判断对账单据来源是不是采购订单,如果是的话就回写
            if (detailDO.getSourceFormType() == OrderTypeEnum.PURCHASE_STATEMENTS_ORDER.getType().shortValue()) {
                AccountOrderDetailDO accountOrderDetailDO = orderDetailMapper.selectById(detailDO.getSourceOrderId());
                if (!Objects.isNull(accountOrderDetailDO) && accountOrderDetailDO.getSourceFormType() == OrderTypeEnum.PURCHASE_ORDER.getType().shortValue()){
                    purchaseOrderBO.setPurchaseOrderId(accountOrderDetailDO.getSourceOrderId());
                    purchaseDetailService.writeBackPurchaseOrder(purchaseOrderBO);
                }
            }
        }
    }

}