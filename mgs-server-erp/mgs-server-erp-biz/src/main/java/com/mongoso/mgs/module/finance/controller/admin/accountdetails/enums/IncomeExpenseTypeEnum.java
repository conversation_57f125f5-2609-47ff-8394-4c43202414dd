package com.mongoso.mgs.module.finance.controller.admin.accountdetails.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON>
 * @date： 2024/9/11
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum IncomeExpenseTypeEnum {


    INCOME(0, "收入"),
    EXPENSE(1,"支出")

    ;
    // 类型
    public final Integer type;
    // 类型名
    public final String typeName;


}
