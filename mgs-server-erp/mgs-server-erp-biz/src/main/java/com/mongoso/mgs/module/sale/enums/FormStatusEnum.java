package com.mongoso.mgs.module.sale.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON>
 * @date： 2025/3/21
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum FormStatusEnum {

    NOT_STARTED(0,"未开始"),
    IN_PROGERSS(1,"进行中"),
    COMPLETED(2,"已完成"),
    CLOSED(3,"已关闭");
    ;

    public final int type;// 类型
    public final String desc;// 描述

}