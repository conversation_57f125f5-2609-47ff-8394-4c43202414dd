package com.mongoso.mgs.module.finance.dal.mysql.asset.assetdepreciate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciatePageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetdepreciate.vo.AssetDepreciateRespVO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetdepreciate.AssetDepreciateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资产折旧 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssetDepreciateMapper extends BaseMapperX<AssetDepreciateDO> {

    default PageResult<AssetDepreciateDO> selectPageOld(AssetDepreciatePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<AssetDepreciateDO>lambdaQueryX()
                .eqIfPresent(AssetDepreciateDO::getAccrualMonth, reqVO.getAccrualMonth())
                .eqIfPresent(AssetDepreciateDO::getAccruedAmt, reqVO.getAccruedAmt())
                .eqIfPresent(AssetDepreciateDO::getCurrentAccrualAmt, reqVO.getCurrentAccrualAmt())
                .eqIfPresent(AssetDepreciateDO::getCurrentPeriod, reqVO.getCurrentPeriod())
                .eqIfPresent(AssetDepreciateDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetDepreciateDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetDepreciateDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetDepreciateDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetDepreciateDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetDepreciateDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetDepreciateDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetDepreciateDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(AssetDepreciateDO::getCreatedDt));
    }



//    default PageResult<AssetDepreciateDO> selectPage(AssetDepreciatePageReqVO reqVO) {
//        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<AssetDepreciateDO>lambdaQueryX()
//                .eqIfPresent(AssetDepreciateDO::getAccrualMonth, reqVO.getAccrualMonth())
//                .eqIfPresent(AssetDepreciateDO::getAccruedAmt, reqVO.getAccruedAmt())
//                .eqIfPresent(AssetDepreciateDO::getCurrentAccrualAmt, reqVO.getCurrentAccrualAmt())
//                .eqIfPresent(AssetDepreciateDO::getCurrentPeriod, reqVO.getCurrentPeriod())
//                .eqIfPresent(AssetDepreciateDO::getAssetId, reqVO.getAssetId())
//                .eqIfPresent(AssetDepreciateDO::getDirectorId, reqVO.getDirectorId())
//                .eqIfPresent(AssetDepreciateDO::getDirectorOrgId, reqVO.getDirectorOrgId())
//                .betweenIfPresent(AssetDepreciateDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
//                .eqIfPresent(AssetDepreciateDO::getDataStatus, reqVO.getDataStatus())
//                .betweenIfPresent(AssetDepreciateDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
//                .eqIfPresent(AssetDepreciateDO::getApprovedBy, reqVO.getApprovedBy())
//                .betweenIfPresent(AssetDepreciateDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
//                        .orderByAsc(AssetDepreciateDO::getCurrentPeriod));
//    }

    IPage<AssetDepreciateRespVO> selectPage(Page<AssetDepreciateRespVO> page, @Param("reqVO") AssetDepreciatePageReqVO reqVO);

    List<AssetDepreciateDO> selectBatchIdList(@Param("idList") List<Long> idList,@Param("buttonType") Integer buttonType);

    AssetDepreciateRespVO selectByIdDetail(@Param("assetDepreciateId") Long assetDepreciateId);

    void assetDepreciateDel(@Param("assetId") Long assetId);

    AssetDepreciateRespVO selectIdAndCurrentPeriod(@Param("assetId") Long assetId,@Param("currentPeriod") BigDecimal currentPeriod);

    default List<AssetDepreciateDO> selectListOld(AssetDepreciateQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<AssetDepreciateDO>lambdaQueryX()
                .eqIfPresent(AssetDepreciateDO::getAccrualMonth, reqVO.getAccrualMonth())
                .eqIfPresent(AssetDepreciateDO::getAccruedAmt, reqVO.getAccruedAmt())
                .eqIfPresent(AssetDepreciateDO::getCurrentAccrualAmt, reqVO.getCurrentAccrualAmt())
                .eqIfPresent(AssetDepreciateDO::getCurrentPeriod, reqVO.getCurrentPeriod())
                .eqIfPresent(AssetDepreciateDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetDepreciateDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetDepreciateDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetDepreciateDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetDepreciateDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetDepreciateDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetDepreciateDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetDepreciateDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(AssetDepreciateDO::getCreatedDt));
    }

    default List<AssetDepreciateDO> selectList(AssetDepreciateQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<AssetDepreciateDO>lambdaQueryX()
                .eqIfPresent(AssetDepreciateDO::getAccrualMonth, reqVO.getAccrualMonth())
                .eqIfPresent(AssetDepreciateDO::getAccruedAmt, reqVO.getAccruedAmt())
                .eqIfPresent(AssetDepreciateDO::getCurrentAccrualAmt, reqVO.getCurrentAccrualAmt())
                .eqIfPresent(AssetDepreciateDO::getCurrentPeriod, reqVO.getCurrentPeriod())
                .eqIfPresent(AssetDepreciateDO::getAssetId, reqVO.getAssetId())
                .eqIfPresent(AssetDepreciateDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetDepreciateDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetDepreciateDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetDepreciateDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetDepreciateDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetDepreciateDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetDepreciateDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(AssetDepreciateDO::getCurrentPeriod));
    }

}