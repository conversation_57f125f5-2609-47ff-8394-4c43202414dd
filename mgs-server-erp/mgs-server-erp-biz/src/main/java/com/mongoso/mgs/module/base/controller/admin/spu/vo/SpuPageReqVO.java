package com.mongoso.mgs.module.base.controller.admin.spu.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.PageParam;
import com.mongoso.mgs.module.base.controller.admin.materialsamplingstrategy.vo.MaterialSamplingStrategyBaseVO;
import com.mongoso.mgs.module.base.controller.admin.materialspec.vo.MaterialSpecRespVO;
import com.mongoso.mgs.module.base.controller.admin.materialunit.vo.MaterialUnitRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * SPU物料 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpuPageReqVO extends PageParam {

    /** SPUId */
    private Long spuId;

    /** 物料名称 */
    private String materialName;

    /** 物料SPU编码 */
    private String spuCode;

    /** 物料类别 */
    private String materialCategoryDictId;

    /** 基本单位 */
    private String mainUnitDictId;

    /** 规格型号 */
    private String specModel;

    /** 品牌 */
    private String brandDictId;

    /** 物料类型 */
    private String materialTypeDictId;

    /** 物料来源 */
    private Integer materialSourceDictId;

    /** 物料图号 */
    private String materialPictureNo;

    /** 排列排序值 */
    private Integer sortValue;

    /** 采购标准价 */
    private BigDecimal purchaseStandardPrice;

    /** 客户订货价 */
    private BigDecimal customerOrderPrice;

    /** 零售价 */
    private BigDecimal retailPrice;

    /** 多单位管理 */
    private List<MaterialUnitRespVO> unitList;

    /** 备注 */
    private String remark;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDt;

    /** 物料图片 */
    private List<JSONObject> materialPictureList;

    /** 规格属性 */
    private List<MaterialSpecRespVO> specAttributeList;

    /** 是否自动来料检验 */
    private Integer isAutoCheck;

    /** 检验方式 */
    private Integer inspectionMethod;

    /** 抽检策略 */
    private List<MaterialSamplingStrategyBaseVO> samplingStrategy;

    /** 检验说明 */
    private String inspectionDesc;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 数据状态 */
    private Integer dataStatus;

    /** 上架状态 */
    private Integer publishStatus;
}
