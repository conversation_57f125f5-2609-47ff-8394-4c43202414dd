package com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.PageParam;
import com.mongoso.mgs.module.base.controller.admin.materialunit.vo.MaterialUnitRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 物料 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ERPMaterialPageReqVO extends PageParam {

    /** 排除物料IDList */
    private List<Long> exclMaterialIdList;

    /** 物料IDList */
    private List<Long> materialIdList;

    /** 物料编码 */
    private String materialCode;

    /** 物料SPU编码 */
    private String spuCode;

    /** 物料SPUId */
    private Long spuId;

    /** 物料名称 */
    private String materialName;

    /** 物料类别 */
    private String materialCategoryDictId;

    /** 规格型号 */
    private String specModel;

    /** 主单位(基本单位) */
    private String mainUnitDictId;

    /** 规格属性 */
    private String specAttributeStr;

    /** 品牌 */
    private String brandDictId;

    /** 物料来源 */
    private Integer materialSourceDictId;
    private List<Integer> materialSourceDictIdList;

    /** 物料类型 */
    private String materialTypeDictId;

    /** 排列排序值 */
    private Integer sortValue;

    /** 物料图号 */
    private String materialPictureNo;

    /** 客户订货价 */
    private BigDecimal customerOrderPrice;

    /** 采购标准价 */
    private BigDecimal purchaseStandardPrice;

    /** 多单位管理 */
    private List<MaterialUnitRespVO> unitList;

    /** 零售价 */
    private BigDecimal retailPrice;

    /** 备注 */
    private String remark;

    /** 物料图片 */
    private List<JSONObject> materialPictureList;

    /** 单据时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDt;

    /** 是否自动来料检验 */
    private Integer isAutoCheck;

    /** 审核状态 */
    private Integer dataStatus;

    /** 责任人 */
    private Long directorId;
    private String directorName;

    /** 责任部门 */
    private String directorOrgId;
    private String directorOrgName;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 检验方式 */
    private BigDecimal inspectionMethod;

    /** 检验说明 */
    private String inspectionDesc;

    /** 是否为商品["否", "是"] */
    private Integer isProduct;
    private List<Integer> isProductList;

    /** 上架状态 */
    private Integer publishStatus;

    /** 操作模块 */
    private String module;

    /** 关联订单ID */
    private Long relatedOrderId;

    /** 业务类型 */
    private Integer bizType;

    /** 销售订单id */
    private Long saleOrderId;

    /** 供应商id */
    private Long supplierId;
}
