package com.mongoso.mgs.module.purchase.dal.mysql.receiptnotice.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.tenant.core.aop.TenantIgnore;
import com.mongoso.mgs.module.base.service.erpbase.bo.DocumentRespBO;
import com.mongoso.mgs.module.purchase.controller.admin.purchase.vo.detail.PurchaseOrderDetailRespVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailPageReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailQueryReqVO;
import com.mongoso.mgs.module.purchase.controller.admin.receiptnotice.vo.detail.PurchaseReceiptNoticeDetailRespVO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDO;
import com.mongoso.mgs.module.purchase.dal.db.receiptnotice.PurchaseReceiptNoticeDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购收货通知单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseReceiptNoticeDetailMapper extends BaseMapperX<PurchaseReceiptNoticeDetailDO> {

    IPage<PurchaseReceiptNoticeDetailRespVO> queryPage(Page<PurchaseOrderDetailRespVO> page, @Param("reqVO") PurchaseReceiptNoticeDetailPageReqVO reqVO);

    default List<PurchaseReceiptNoticeDetailDO> selectList(PurchaseReceiptNoticeDetailQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<PurchaseReceiptNoticeDetailDO>lambdaQueryX()
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getReceiptNoticeId, reqVO.getReceiptNoticeId())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getRelatedRowNo, reqVO.getRelatedRowNo())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getMainUnitDictId, reqVO.getMainUnitDictId())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getNoticeQty, reqVO.getNoticeQty())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getIsMaterialFullReceipted, reqVO.getIsMaterialFullReceipted())
                .eqIfPresent(PurchaseReceiptNoticeDetailDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(PurchaseReceiptNoticeDetailDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                        .orderByDesc(PurchaseReceiptNoticeDetailDO::getCreatedDt));
    }

    default BigDecimal queryReceiptableQty(Long receiptNoticeDetailId) {
        PurchaseReceiptNoticeDetailDO receiptNoticeDetailDO =  selectOne(LambdaQueryWrapperX.<PurchaseReceiptNoticeDetailDO>lambdaQueryX()
                .select(PurchaseReceiptNoticeDetailDO:: getReceiptableQty)
                .eq(PurchaseReceiptNoticeDetailDO:: getReceiptNoticeDetailId, receiptNoticeDetailId));
        if(receiptNoticeDetailDO == null){
            return BigDecimal.ZERO;
        }
        return receiptNoticeDetailDO.getReceiptableQty();
    }

    default Long selectUnFullReceiptedCount(Long receiptNoticeId){
        return selectCount(LambdaQueryWrapperX.<PurchaseReceiptNoticeDetailDO>lambdaQueryX()
                .eq(PurchaseReceiptNoticeDetailDO:: getReceiptNoticeId, receiptNoticeId)
                .eq(PurchaseReceiptNoticeDetailDO:: getIsMaterialFullReceipted, 0)
        );
    }

    List<PurchaseReceiptNoticeDetailRespVO> queryList(@Param("reqVO") PurchaseReceiptNoticeDetailQueryReqVO reqVO);

    List<PurchaseReceiptNoticeDetailDO> selectNoticeQtyListByPurchaseId(Long purchaseOrderId);

    default int deleteByPurchaseId(Long receiptNoticeId){
        return delete(LambdaQueryWrapperX.<PurchaseReceiptNoticeDetailDO>lambdaQueryX()
                .eq(PurchaseReceiptNoticeDetailDO::getReceiptNoticeId, receiptNoticeId));
    }
    @TenantIgnore
    Integer getReceiptNoticeIsApproveAble(@Param("item") PurchaseReceiptNoticeDO item);

    @TenantIgnore
    Integer updatePurchaseDetailPurchaseQty(@Param("item") PurchaseReceiptNoticeDO receiptNoticeDO, @Param("operate") Integer operate);

    List<DocumentRespBO> receiptableQtyList(@Param("receiptNoticeId") Long receiptNoticeId);

}