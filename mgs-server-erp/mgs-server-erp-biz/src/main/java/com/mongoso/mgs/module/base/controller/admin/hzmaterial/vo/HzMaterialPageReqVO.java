package com.mongoso.mgs.module.base.controller.admin.hzmaterial.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 物料 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HzMaterialPageReqVO extends PageParam {

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** sap物料编码 */
    private String sapMaterialCode;

    /** 物料类型 */
    private Long dictMaterialTypeId;

    /** 字典单位 */
    private Long dictUnit;

    /** 物料描述 */
    private String materialDesc;

    /** 机种id */
    private Long machineType;

    /** 流程编码 */
    private String flowCode;

    /** 产品id */
    private Long productId;

    /** 产品编码 */
    private String productCode;

    /** 产品名称 */
    private String productName;

    /** 是否启用 */
    private Short isEnable;
    /** 是否bom */
    private Short isBom;

    /** 机种代号 */
    private String machineName;

    /** 社内项目料号 */
    private String intraMaterialCode;

    /** BOM版本 */
    private String dictBomVersionId;

    /** 版本说明 */
    private String versionDesc;

    /** 备注 */
    private String remark;

    /** 物料属性，0：基本物料，1：订单物料 */
    private Short mattr;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    private Boolean isPurchaseQuery = false;

    private List<Long> ids;

}
