package com.mongoso.mgs.module.sale.handler.flowCallback;

import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.handler.FlowApproveHandler;
import com.mongoso.mgs.component.flow.service.handler.FlowCallbackHandler;
import com.mongoso.mgs.module.base.service.erpbase.handler.BaseFlowCallbackHandler;
import com.mongoso.mgs.module.purchase.dal.db.demand.PurchaseDemandDO;
import com.mongoso.mgs.module.sale.dal.db.salereturn.SaleReturnDO;
import org.springframework.stereotype.Component;

/**
 * @author: zhiling
 * @date: 2024/11/29 9:40
 * @description: 销售退货单回调处理类
 */

@Component
public class SaleReturnFlowCallBackHandler extends FlowCallbackHandler<SaleReturnDO> {


    protected SaleReturnFlowCallBackHandler(FlowApproveHandler<SaleReturnDO> approveHandler) {
        super(approveHandler);
    }
}
