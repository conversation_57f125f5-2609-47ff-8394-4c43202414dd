package com.mongoso.mgs.module.base.service.spu;

import com.mongoso.mgs.common.enums.*;
import com.mongoso.mgs.common.enums.material.PublishEnum;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.common.util.EntityUtilX;
import com.mongoso.mgs.component.flow.enums.ApproveResultEnum;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.component.flow.service.approve.ApproveService;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowApprove;
import com.mongoso.mgs.component.flow.service.approve.bo.FlowCallback;
import com.mongoso.mgs.component.flow.service.flowbase.bo.FlowConfigBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowApproveBO;
import com.mongoso.mgs.component.flow.service.handler.bo.FlowCallbackBO;
import com.mongoso.mgs.component.flow.util.ApproveUtilX;
import com.mongoso.mgs.component.message.service.messagetemplate.MessageTemplateService;
import com.mongoso.mgs.component.message.service.messagetemplate.bo.MessageInfoBO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.domain.batch.BatchResult;
import com.mongoso.mgs.framework.common.domain.batch.FailItem;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.base.controller.admin.customercontact.vo.CustomerContactBaseVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialAditReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.erpmaterial.vo.ERPMaterialRespVO;
import com.mongoso.mgs.module.base.controller.admin.materialsamplingstrategy.vo.MaterialSamplingStrategyBaseVO;
import com.mongoso.mgs.module.base.controller.admin.materialsamplingstrategy.vo.MaterialSamplingStrategyQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.materialspec.vo.MaterialSpecQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.materialspec.vo.MaterialSpecRespVO;
import com.mongoso.mgs.module.base.controller.admin.materialunit.vo.MaterialUnitQueryReqVO;
import com.mongoso.mgs.module.base.controller.admin.materialunit.vo.MaterialUnitRespVO;
import com.mongoso.mgs.module.base.controller.admin.spu.vo.*;
import com.mongoso.mgs.module.base.dal.db.erpmaterial.ERPMaterialDO;
import com.mongoso.mgs.module.base.dal.db.hzmaterial.HzMaterialDO;
import com.mongoso.mgs.module.base.dal.db.materialsamplingstrategy.MaterialSamplingStrategyDO;
import com.mongoso.mgs.module.base.dal.db.materialspec.MaterialSpecDO;
import com.mongoso.mgs.module.base.dal.db.materialunit.MaterialUnitDO;
import com.mongoso.mgs.module.base.dal.db.spu.SpuDO;
import com.mongoso.mgs.module.base.dal.mysql.erpmaterial.ERPMaterialMapper;
import com.mongoso.mgs.module.base.dal.mysql.hzmaterial.HzMaterialMapper;
import com.mongoso.mgs.module.base.dal.mysql.materialsamplingstrategy.MaterialSamplingStrategyMapper;
import com.mongoso.mgs.module.base.dal.mysql.materialspec.MaterialSpecMapper;
import com.mongoso.mgs.module.base.dal.mysql.materialunit.MaterialUnitMapper;
import com.mongoso.mgs.module.base.dal.mysql.spu.SpuMapper;
import com.mongoso.mgs.module.base.handler.approve.SpuApproveHandler;
import com.mongoso.mgs.module.base.handler.flowCallback.SpuFlowCallbackHandler;
import com.mongoso.mgs.module.base.service.erpbase.ErpBaseService;
import com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialService;
import com.mongoso.mgs.module.infra.controller.admin.file.vo.FileLogRespVO;
import com.mongoso.mgs.module.infra.service.file.FileService;
import com.mongoso.mgs.module.system.service.seq.SeqService;
import com.mongoso.mgs.module.warehouse.dal.mysql.materialstock.ErpMaterialStockMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.base.enums.ErrorCodeConstants.*;
import static com.mongoso.mgs.module.base.service.erpmaterial.ERPMaterialServiceImpl.jsonObjectListToStr;


/**
 * SPU物料 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SpuServiceImpl implements SpuService {

    @Resource
    private SpuMapper spuMapper;
    @Resource
    private SeqService seqService;
    @Resource
    private ERPMaterialMapper erpMaterialMapper;
    @Resource
    private ERPMaterialService erpMaterialService;
    @Resource
    private ErpMaterialStockMapper erpMaterialStockMapper;
    @Resource
    private HzMaterialMapper hzMaterialMapper;
    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private FileService fileService;
    @Resource
    private ApproveService approveService;
    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private SpuApproveHandler spuApproveHandler;

    @Resource
    private MaterialSamplingStrategyMapper materialSamplingStrategyMapper;

    @Resource
    private SpuFlowCallbackHandler spuFlowCallbackHandler;

    @Resource
    private MaterialSpecMapper materialSpecMapper;

    @Resource
    private MaterialUnitMapper materialUnitMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long spuAdd(SpuAditReqVO reqVO) {
        // 插入
        SpuDO spu = BeanUtilX.copy(reqVO, SpuDO::new);
        // 生成SPU编码
        String spuCode = seqService.getGenerateCode(reqVO.getSpuCode(), MenuEnum.SPU_MANAGEMENT.menuId);
        spu.setSpuId(IDUtilX.getId());
        spu.setSpuCode(spuCode);
        spu.setDataStatus(0);
        spu.setPublishStatus(0);
        spuMapper.insert(spu);


        //保存物料规格和单位
        spuSpecAndUnitSave(reqVO,spu.getSpuId(),spuCode,true);

        // 保存物料抽样策略
        samplingStrategySave(reqVO,spu.getSpuId(),true);

        //新增物料
        List<ERPMaterialAditReqVO> materialDetailList = reqVO.getMaterialDetailList();
        ArrayList<ERPMaterialDO> erpMaterialList = new ArrayList<>();
        ArrayList<HzMaterialDO> hzMaterialList = new ArrayList<>();

        //批量新增序列号

        for (ERPMaterialAditReqVO materialAditReqVO : materialDetailList) {
            // 生成物料编码
            materialAditReqVO.setMaterialId(IDUtilX.getId());
            String materialCode = seqService.getGenerateCode(null, MenuEnum.PRODUCT_MATERIAL_MANAGEMENT_11063.menuId);

            materialAditReqVO.setMaterialCode(materialCode);
            materialAditReqVO.setSpuCode(spu.getSpuCode());
            materialAditReqVO.setSpuId(spu.getSpuId());
            materialAditReqVO.setSpuIdOld(spu.getSpuId());
            materialAditReqVO.setMaterialCategoryDictId(reqVO.getMaterialCategoryDictId());
            materialAditReqVO.setMaterialCategoryDictName(reqVO.getMaterialCategoryDictName());
            materialAditReqVO.setMainUnitDictId(reqVO.getMainUnitDictId());
            materialAditReqVO.setMainUnitDictName(reqVO.getMainUnitDictName());
            materialAditReqVO.setBrandDictId(reqVO.getBrandDictId());
            materialAditReqVO.setMaterialSourceDictId(reqVO.getMaterialSourceDictId());
            materialAditReqVO.setMaterialTypeDictId(reqVO.getMaterialTypeDictId());
            materialAditReqVO.setMaterialPictureNo(reqVO.getMaterialPictureNo());
            materialAditReqVO.setUnitList(reqVO.getUnitList());
            materialAditReqVO.setFormDt(reqVO.getFormDt());
            materialAditReqVO.setDirectorId(reqVO.getDirectorId());
            materialAditReqVO.setDirectorOrgId(reqVO.getDirectorOrgId());
            materialAditReqVO.setIsAutoCheck(reqVO.getIsAutoCheck());
            materialAditReqVO.setInspectionMethod(reqVO.getInspectionMethod());
            materialAditReqVO.setSamplingStrategy(reqVO.getSamplingStrategy());
            materialAditReqVO.setInspectionDesc(reqVO.getInspectionDesc());
            materialAditReqVO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
            materialAditReqVO.setIsProduct(1);
            materialAditReqVO.setPublishStatus(PublishEnum.NOT_PUBLISH.key);

//            materialAditReqVO.setStandardWeight(reqVO.getStandardWeight());
//            materialAditReqVO.setBillingWeight(reqVO.getBillingWeight());
//            materialAditReqVO.setLightestWeight(reqVO.getLightestWeight());
//            materialAditReqVO.setHeaviestWeight(reqVO.getHeaviestWeight());
//            materialAditReqVO.setRoundingFee(reqVO.getRoundingFee());
//            materialAditReqVO.setAluminumLoss(reqVO.getAluminumLoss());
            materialAditReqVO.setProcessingFee(reqVO.getProcessingFee());

            ERPMaterialDO erpMaterialDO = BeanUtilX.copy(materialAditReqVO, ERPMaterialDO::new);
            erpMaterialList.add(erpMaterialDO);

            //保存物料规格和单位
            materialSpecAndUnitSave(materialAditReqVO,erpMaterialDO.getMaterialId(),materialCode,true);

            // 保存物料抽样策略
            samplingStrategySave(reqVO,erpMaterialDO.getMaterialId(),true);

            HzMaterialDO hzMaterialDO = BeanUtilX.copy(erpMaterialDO, HzMaterialDO::new);
            hzMaterialDO.setId(erpMaterialDO.getMaterialId());
            hzMaterialDO.setSpecSetting(erpMaterialDO.getSpecModel());
            hzMaterialDO.setDictMaterialTypeId(1L); //暂时默认为1, 待生产重构
            hzMaterialDO.setDictMaterialTypeName(reqVO.getMaterialCategoryDictName());
            hzMaterialDO.setMainUnit(reqVO.getMainUnitDictName());
            hzMaterialDO.setMattr((short)0);
            hzMaterialDO.setIsBom((short)0);
            hzMaterialDO.setIsEnable((short)1);
            hzMaterialList.add(hzMaterialDO);
        }

        if (!erpMaterialList.isEmpty()){
            erpMaterialMapper.insertBatch(erpMaterialList);
        }

        if (!hzMaterialList.isEmpty()){
            hzMaterialMapper.insertBatch(hzMaterialList);
        }

        //保存附件
        if (CollUtilX.isNotEmpty(reqVO.getMaterialPictureList())){
            List<String> fileIdList = reqVO.getMaterialPictureList().stream().map(FileLogRespVO::getFileId).collect(Collectors.toList());
            String objId = spu.getSpuId().toString();
            fileService.bind(fileIdList,objId, FileTableEnum.SPU_MATERIAL_PICTURE.getTableName(), FileTableEnum.SPU_MATERIAL_PICTURE.getFieldName());
        }

        // 返回
        return spu.getSpuId();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long spuEdit(SpuAditReqVO reqVO) {
        // 校验存在
//        this.spuValidateExists(reqVO.getSpuId());

        //校验是否存在和版本号
        EntityUtilX.checkVersion(this.spuValidateExists(reqVO.getSpuId()), reqVO);


        // 更新
        SpuDO spu = BeanUtilX.copy(reqVO, SpuDO::new);
        spuMapper.updateById(spu);

        //保存物料规格和单位
        spuSpecAndUnitSave(reqVO,spu.getSpuId(),spu.getSpuCode(),false);

        // 保存物料抽样策略
        samplingStrategySave(reqVO,spu.getSpuId(),false);

        List<ERPMaterialAditReqVO> materialDetailList = reqVO.getMaterialDetailList();
        ArrayList<ERPMaterialDO> erpMaterialInsertList = new ArrayList<>();
        ArrayList<ERPMaterialDO> erpMaterialUpdateList = new ArrayList<>();
        ArrayList<HzMaterialDO> hzMaterialInsertList = new ArrayList<>();
        ArrayList<HzMaterialDO> hzMaterialUpdateList = new ArrayList<>();
        ArrayList<Long> erpMaterialIdList = new ArrayList<>();
        for (ERPMaterialAditReqVO materialAditReqVO : materialDetailList) {
            materialAditReqVO.setSpuCode(spu.getSpuCode());
            materialAditReqVO.setSpuId(spu.getSpuId());
            materialAditReqVO.setSpuIdOld(spu.getSpuId());
            materialAditReqVO.setSpuCode(spu.getSpuCode());
            materialAditReqVO.setMaterialCategoryDictId(reqVO.getMaterialCategoryDictId());
            materialAditReqVO.setMaterialCategoryDictName(reqVO.getMaterialCategoryDictName());
            materialAditReqVO.setMainUnitDictId(reqVO.getMainUnitDictId());
            materialAditReqVO.setMainUnitDictName(reqVO.getMainUnitDictName());
            materialAditReqVO.setBrandDictId(reqVO.getBrandDictId());
            materialAditReqVO.setMaterialSourceDictId(reqVO.getMaterialSourceDictId());
            materialAditReqVO.setMaterialTypeDictId(reqVO.getMaterialTypeDictId());
            materialAditReqVO.setMaterialPictureNo(reqVO.getMaterialPictureNo());
            materialAditReqVO.setUnitList(reqVO.getUnitList());
            materialAditReqVO.setFormDt(reqVO.getFormDt());
            materialAditReqVO.setDirectorId(reqVO.getDirectorId());
            materialAditReqVO.setDirectorOrgId(reqVO.getDirectorOrgId());
            materialAditReqVO.setIsAutoCheck(reqVO.getIsAutoCheck());
            materialAditReqVO.setInspectionMethod(reqVO.getInspectionMethod());

            // 保存物料抽样策略
            if (CollUtilX.isNotEmpty(reqVO.getSamplingStrategy())){
                materialSamplingStrategyMapper.batchDelete(spu.getSpuId());
                List<MaterialSamplingStrategyDO> samplingStrategyList = new ArrayList<>();
                for (MaterialSamplingStrategyBaseVO samplingStrategy : reqVO.getSamplingStrategy()) {
                    samplingStrategy.setMaterialFkId(spu.getSpuId());
                    samplingStrategyList.add(BeanUtilX.copy(samplingStrategy, MaterialSamplingStrategyDO::new));
                }
                materialSamplingStrategyMapper.insertBatch(samplingStrategyList);
            }

            materialAditReqVO.setInspectionDesc(reqVO.getInspectionDesc());
//            if (CollUtilX.isEmpty(materialAditReqVO.getSpecAttributeList())){
//                materialAditReqVO.setSpecAttributeList(reqVO.getSpecAttributeList());
//                materialAditReqVO.setSpecAttributeStr(jsonObjectListToStr(materialAditReqVO.getSpecAttributeList()));
//            }
            materialAditReqVO.setDataStatus(PublishEnum.NOT_PUBLISH.key);
            materialAditReqVO.setIsProduct(1);
            materialAditReqVO.setPublishStatus(DataStatusEnum.NOT_APPROVE.key);

            ERPMaterialDO erpMaterialDO = BeanUtilX.copy(materialAditReqVO, ERPMaterialDO::new);
            erpMaterialDO.setProcessingFee(reqVO.getProcessingFee());


            HzMaterialDO hzMaterialDO = BeanUtilX.copy(erpMaterialDO, HzMaterialDO::new);
            hzMaterialDO.setId(erpMaterialDO.getMaterialId());
            hzMaterialDO.setSpecSetting(erpMaterialDO.getSpecModel());
            hzMaterialDO.setDictMaterialTypeId(1L); //暂时默认为1, 待生产重构
            hzMaterialDO.setDictMaterialTypeName(reqVO.getMaterialCategoryDictName());
            hzMaterialDO.setMainUnit(reqVO.getMainUnitDictName());
            if (materialAditReqVO.getMaterialId() != null){//更新
                erpMaterialIdList.add(materialAditReqVO.getMaterialId());
                hzMaterialUpdateList.add(hzMaterialDO);
                erpMaterialUpdateList.add(erpMaterialDO);

            }else{//新增
                // 生成物料编码
                String materialCode = seqService.getGenerateCode(null, MenuEnum.PRODUCT_MATERIAL_MANAGEMENT_11063.menuId);

                erpMaterialDO.setMaterialId(IDUtilX.getId());
                erpMaterialDO.setMaterialCode(materialCode);
                erpMaterialDO.setProcessingFee(reqVO.getProcessingFee());

                //保存物料规格和单位
                materialSpecAndUnitSave(materialAditReqVO,erpMaterialDO.getMaterialId(),erpMaterialDO.getMaterialCode(),false);

                // 保存物料抽样策略
                samplingStrategySave(reqVO,erpMaterialDO.getMaterialId(),false);

                hzMaterialDO.setId(erpMaterialDO.getMaterialId());
                hzMaterialDO.setMaterialCode(materialCode);
                hzMaterialDO.setMattr((short)0);
                hzMaterialDO.setIsBom((short)0);
                hzMaterialDO.setIsEnable((short)1);
                erpMaterialInsertList.add(erpMaterialDO);
                hzMaterialInsertList.add(hzMaterialDO);
            }
        }

        //解绑不需要的物料
        erpMaterialMapper.unbindMaterials(erpMaterialIdList, Collections.singletonList(reqVO.getSpuId()));

        //更新和新增
        if (!erpMaterialInsertList.isEmpty()){
            erpMaterialMapper.insertBatch(erpMaterialInsertList);
        }

        if (!erpMaterialUpdateList.isEmpty()){
            erpMaterialMapper.updateBatch(erpMaterialUpdateList);
        }

        if (!hzMaterialInsertList.isEmpty()){
            hzMaterialMapper.insertBatch(hzMaterialInsertList);
        }

        if (!hzMaterialUpdateList.isEmpty()){
            hzMaterialMapper.updateBatch(hzMaterialUpdateList);
        }

        //保存附件
        if (CollUtilX.isNotEmpty(reqVO.getMaterialPictureList())){
            List<String> fileIdList = reqVO.getMaterialPictureList().stream().map(FileLogRespVO::getFileId).collect(Collectors.toList());
            String objId = spu.getSpuId().toString();
            fileService.bind(fileIdList,objId, FileTableEnum.SPU_MATERIAL_PICTURE.getTableName(), FileTableEnum.SPU_MATERIAL_PICTURE.getFieldName());
        }

        // 返回
        return spu.getSpuId();
    }

    private void spuSpecAndUnitSave(SpuAditReqVO reqVO, Long id,String code, Boolean isAdd) {
        if (CollUtilX.isNotEmpty(reqVO.getSpecAttributeList())){

            if (!isAdd){
                materialSpecMapper.batchDelete(id);
            }

            List<MaterialSpecDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getSpecAttributeList().size(); i++) {
                MaterialSpecRespVO item = reqVO.getSpecAttributeList().get(i);
                item.setRowNo(i + 1);
                item.setRelatedOrderId(id);
                item.setRelatedOrderCode(code);
                item.setMaterialSpecId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialSpecDO::new));
            }
            materialSpecMapper.insertBatch(materialSpec);
        }

        if (CollUtilX.isNotEmpty(reqVO.getUnitList())){

            if (!isAdd){
                materialUnitMapper.batchDelete(id);
            }

            List<MaterialUnitDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getUnitList().size(); i++) {
                MaterialUnitRespVO item = reqVO.getUnitList().get(i);
                item.setRowNo(i + 1);
                item.setRelatedOrderId(id);
                item.setRelatedOrderCode(code);
                item.setMaterialUnitId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialUnitDO::new));
            }
            materialUnitMapper.insertBatch(materialSpec);
        }
    }

    private void materialSpecAndUnitSave(ERPMaterialAditReqVO reqVO, Long id,String code, Boolean isAdd) {
        if (CollUtilX.isNotEmpty(reqVO.getSpecAttributeList())){

            if (!isAdd){
                materialSpecMapper.batchDelete(id);
            }

            List<MaterialSpecDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getSpecAttributeList().size(); i++) {
                MaterialSpecRespVO item = reqVO.getSpecAttributeList().get(i);
                item.setRowNo(i + 1);
                item.setRelatedOrderId(id);
                item.setRelatedOrderCode(code);
                item.setMaterialSpecId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialSpecDO::new));
            }
            materialSpecMapper.insertBatch(materialSpec);
        }

        if (CollUtilX.isNotEmpty(reqVO.getUnitList())){

            if (!isAdd){
                materialUnitMapper.batchDelete(id);
            }

            List<MaterialUnitDO> materialSpec = new ArrayList<>();
            for (int i = 0; i < reqVO.getUnitList().size(); i++) {
                MaterialUnitRespVO item = reqVO.getUnitList().get(i);
                item.setRowNo(i + 1);
                item.setRelatedOrderId(id);
                item.setRelatedOrderCode(code);
                item.setMaterialUnitId(null);
                materialSpec.add(BeanUtilX.copy(item, MaterialUnitDO::new));
            }
            materialUnitMapper.insertBatch(materialSpec);
        }
    }

    private void samplingStrategySave(SpuAditReqVO reqVO, Long id,Boolean isAdd) {
        if (CollUtilX.isNotEmpty(reqVO.getSamplingStrategy())){

            if (!isAdd){
                materialSamplingStrategyMapper.batchDelete(id);
            }

            List<MaterialSamplingStrategyDO> samplingStrategyList = new ArrayList<>();
            for (MaterialSamplingStrategyBaseVO samplingStrategy : reqVO.getSamplingStrategy()) {
                samplingStrategy.setMaterialFkId(id);
                samplingStrategy.setStrategyId(null);
                samplingStrategyList.add(BeanUtilX.copy(samplingStrategy, MaterialSamplingStrategyDO::new));
            }
            materialSamplingStrategyMapper.insertBatch(samplingStrategyList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void spuDel(Long spuId) {
        // 校验存在
        SpuDO spuDO = this.spuValidateExists(spuId);
        if (!DataStatusEnum.NOT_APPROVE.getKey().equals(spuDO.getDataStatus())){
            throw exception(NOT_DELETE_NO_APPROVAL);
        }
        spuMapper.deleteById(spuDO.getSpuId());
        //解绑该SPU下的物料
        erpMaterialMapper.unbindMaterials(null, Collections.singletonList(spuDO.getSpuId()));
//        if (spuDO.getDataStatus() != DataStatusEnum.NO_REVIEW.key){
//            throw exception(SPU_NOT_NO_APPROVAL);
//        }
//        SpuQueryReqVO queryReqVO = BeanUtilX.copy(reqVO, SpuQueryReqVO::new);
//        queryReqVO.setDataStatus(0);
//        if (queryReqVO.getSpuIdList() == null){
//            return;
//        }
//        //筛选出符合删除前提的SPU
//        List<Long> spuIdList = spuMapper.selectList(queryReqVO).stream().map(SpuDO::getSpuId).collect(Collectors.toList());
//        //解绑该SPU下的物料
//        erpMaterialMapper.unbindMaterials(null, spuIdList);
//        // 删除
//        if (!spuIdList.isEmpty()){
//            spuMapper.deleteBatchIds(spuIdList);
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultX<BatchResult> spuDelBatch(SpuPrimaryReqVO reqVO) {
        //获取对象属性名
        String spuId = EntityUtilX.getPropertyName(SpuDO::getSpuId);
        String spuCode = EntityUtilX.getPropertyName(SpuDO::getSpuCode);
        ResultX<BatchResult> resultResultX = erpBaseService.batchDelete(reqVO.getIdList(), SpuDO.class, null, spuId, spuCode);
        //解绑该SPU下的物料
        erpMaterialMapper.unbindMaterials(null, reqVO.getIdList());
        return resultResultX;
    }

    private SpuDO spuValidateExists(Long spuId) {
        SpuDO spu = spuMapper.selectById(spuId);
        if (spu == null) {
            // throw exception(SPU_NOT_EXISTS);
            throw new BizException("5001", "SPU物料不存在");
        }
        return spu;
    }

    @Override
    public SpuRespVO spuDetail(Long spuId) {
        SpuRespVO spuRespVO = BeanUtilX.copy(spuMapper.selectById(spuId), SpuRespVO::new);
        if (spuRespVO == null){
             return spuRespVO;
        }
        BigDecimal materialStockQty = erpMaterialStockMapper.queryMaterialStockQtyForSpu(spuId);
        if (materialStockQty != null && materialStockQty.compareTo(BigDecimal.ZERO) > 0){
            spuRespVO.setIsChangeMainUnit(0);
        }else {
            spuRespVO.setIsChangeMainUnit(1);
        }
        this.batchFillVoProperties(Collections.singletonList(spuRespVO));
        //查询物料明细
        ERPMaterialQueryReqVO queryReqVO = new ERPMaterialQueryReqVO();
        queryReqVO.setSpuId(spuId);

        List<ERPMaterialRespVO> materialRespVOS = erpMaterialService.materialList(queryReqVO);
        // 根据行号排序
        materialRespVOS.sort(Comparator.comparing(ERPMaterialRespVO::getRowNo, Comparator.nullsLast(Comparator.naturalOrder())));
        spuRespVO.setMaterialDetailList(materialRespVOS);

        //查询抽检策略
        MaterialSamplingStrategyQueryReqVO samplingStrategyQueryReqVO = new MaterialSamplingStrategyQueryReqVO();
        samplingStrategyQueryReqVO.setMaterialFkId(spuId);
        List<MaterialSamplingStrategyDO> strategyList = materialSamplingStrategyMapper.selectList(samplingStrategyQueryReqVO);
        // 根据行号排序
        strategyList.sort(Comparator.comparing(MaterialSamplingStrategyDO::getRowNo, Comparator.nullsLast(Comparator.naturalOrder())));
        spuRespVO.setSamplingStrategy(BeanUtilX.copy(strategyList, MaterialSamplingStrategyBaseVO::new));

        //查询物料规格
        MaterialSpecQueryReqVO materialSpecQuery = new MaterialSpecQueryReqVO();
        materialSpecQuery.setRelatedOrderId(spuId);
        List<MaterialSpecDO> materialSpecList = materialSpecMapper.selectList(materialSpecQuery);
        spuRespVO.setSpecAttributeList(BeanUtilX.copy(materialSpecList, MaterialSpecRespVO::new));

        //查询物料单位
        MaterialUnitQueryReqVO materialUnitQuery = new MaterialUnitQueryReqVO();
        materialUnitQuery.setRelatedOrderId(spuId);
        List<MaterialUnitDO> materialUnitList = materialUnitMapper.selectList(materialUnitQuery);
        spuRespVO.setUnitList(BeanUtilX.copy(materialUnitList, MaterialUnitRespVO::new));

        //获取附件
        spuRespVO.setMaterialPictureList(fileService.listByObjId(spuId.toString(),FileTableEnum.SPU_MATERIAL_PICTURE.getTableName(), FileTableEnum.SPU_MATERIAL_PICTURE.getFieldName()));
        //设置审批任务id
        Optional.ofNullable(approveService.detailByObjId(spuId.toString())).ifPresent(approveTask -> spuRespVO.setApproveTaskId(approveTask.getApproveTaskId()));

        return spuRespVO;
    }

    @Override
    public List<SpuRespVO> spuList(SpuQueryReqVO reqVO) {
        List<SpuDO> spuDOList = spuMapper.selectList(reqVO);
        List<SpuRespVO> respVOList = BeanUtilX.copy(spuDOList, SpuRespVO::new);
        this.batchFillVoProperties(respVOList);
        return respVOList;
    }

    @Override
    public PageResult<SpuRespVO> spuPage(SpuPageReqVO reqVO) {
        PageResult<SpuDO> spuDOPageResult = spuMapper.selectPage(reqVO);
        PageResult<SpuRespVO> pageResult = BeanUtilX.copy(spuDOPageResult, SpuRespVO::new);
        this.batchFillVoProperties(pageResult.getList());
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishStatusChange(SpuQueryReqVO reqVO) {
        Short operationType = reqVO.getOpType();
        switch (operationType) {
            case 1://全部上架（前提：已审核）-》已上架
                reqVO.setPreStatus(1);
                reqVO.setPublishStatus(PublishEnum.PUBLISH.getKey());
                break;
            case 0://全部下架（前提：已审核）-》未上架
                reqVO.setPreStatus(1);
                reqVO.setPublishStatus(PublishEnum.NOT_PUBLISH.getKey());
                break;
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        reqVO.setUpdatedBy(loginUser.getFullUserName());

        erpMaterialMapper.spuPublishStatusChange(reqVO);
        spuMapper.updateSpuPublishStatus(reqVO);

        return null;
    }

    @Override
    public BatchResult spuApprove(FlowApprove reqVO) {
        //结果
        BatchResult batchResult = new BatchResult();

        //改为ids查询出列表然后遍历
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        String flowFunctionCode = reqVO.getFlowFunctionCode();

        //1.查询业务数据
        List<SpuDO> list = spuMapper.selectBatchIds(reqVO.getIdList());
        if (CollUtilX.isEmpty(list)) {
            return batchResult;
        }

        //2.查询流程模版
        FlowConfigBO flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);

        //3.执行批量操作
        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
        List<FailItem> failItemList = new ArrayList<>();
        for (SpuDO item : list) {
            try {
                //操作状态校验
                ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
                //封装审批数据对象
                FlowApproveBO flowApproveBO = FlowApproveBO.builder().conditionMap(null).flowFunctionCode(flowFunctionCode).flowConfigBO(flowConfigBO).buttonType(buttonType).build();


                //流程处理
                FailItem failItem = spuApproveHandler.process(item,flowApproveBO);
                if (StrUtilX.isNotEmpty(failItem.getCode())) {
                    failItemList.add(failItem);
                }
            } catch (Exception exception) {
                //异常捕捉
                FailItem failItem = new FailItem();
                failItem.setCode(item.getSpuCode());
                failItem.setReason(exception.getMessage());
                failItemList.add(failItem);
            }
        }
        //封装结果对象
        batchResult.setFailItem(failItemList);
        batchResult.setTotalCount(reqVO.getIdList().size());
        batchResult.setFailCount(failItemList.size());
        batchResult.setSuccessCount(batchResult.getTotalCount() - batchResult.getFailCount());

        // 不走流程，发消息
        String curMenuId = WebFrameworkUtilX.getLoginUser().getCurMenuId();
        if (!flowConfigBO.getToFlow()){
            List<MessageInfoBO> messageInfoBOS = new ArrayList<>();
            Map<String, String> reasonMap = failItemList.stream().collect(Collectors.toMap(FailItem::getCode, FailItem::getReason));
            for (SpuDO item : list) {
                String reason = reasonMap.get(item.getSpuCode());
                if (StrUtilX.isEmpty(reason)){
                    // 正常
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getSpuId());
                    messageInfoBO.setObjCode(item.getSpuCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBOS.add(messageInfoBO);
                }else {
                    // 错误
                    MessageInfoBO messageInfoBO = new MessageInfoBO();
                    messageInfoBO.setMenuId(curMenuId);
                    messageInfoBO.setObjId(item.getSpuId());
                    messageInfoBO.setObjCode(item.getSpuCode());
                    messageInfoBO.setApproveResult(ApproveResultEnum.UN_PASS.getKey());
                    messageInfoBO.setUserIdList(Arrays.asList(item.getDirectorId(),item.getCreatedId()));
                    messageInfoBO.setError(reason);
                    messageInfoBOS.add(messageInfoBO);
                }
            }
            messageTemplateService.sendMessage(messageInfoBOS);
        }
        return batchResult;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public List<ApproveResult> spuApprove(FlowApprove reqVO) {
//        //改为ids查询出列表然后遍历
//        LocalDateTime now = LocalDateTime.now();
//        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//        String flowFunctionCode = reqVO.getFlowFunctionCode();
//
//        // 第一步，业务前缀处理
//        // 1.1 查询业务数据
//        List<SpuDO> list = spuMapper.selectBatchIds(reqVO.getIdList());
//        if (CollUtilX.isEmpty(list)) {
//            return Collections.emptyList();
//        }
//
//        Integer buttonType = ApproveUtilX.getButtonType(flowFunctionCode);
//        for (SpuDO item : list) {
//
//            // 1.2 通用校验
//            ApproveUtilX.checkDataStatus(flowFunctionCode, item.getDataStatus());
//
//            // 1.3 业务校验
//        }
//
//        // 第二步，流程查询
//        // 2.1 查询流程模版
//        FlowConfigBO flowConfigBO = null;
//        FlowTemplateDO flowTemplateDO = approveService.getFlowTemplate(flowFunctionCode);
//        if (flowTemplateDO != null) {
//            // 2.2 查询流程配置
//            flowConfigBO = approveService.getFlowConfigBO(loginUser.getUserId(), flowFunctionCode);
//        }
//
//        // 第三步，业务处理
//        // 3.1 处理主表
//        // 3.2 处理子表
//        List<SpuDO> updateList = new ArrayList<>();
//        ArrayList<ERPMaterialDO> materialUpdateList = new ArrayList<>();
//        for (SpuDO item : list) {
//            item.setSpuId(item.getSpuId());
//            item.setReviewer(loginUser.getFullUserName());
//            item.setReviewDt(now);
//            if (flowTemplateDO == null) {
//                // 不走流程，直接结束状态
//                Integer dataStatus = ApproveUtilX.getDataStatusByApproveSuccess(flowFunctionCode);
//                item.setDataStatus(dataStatus);
//                //修改SPU下的SKU状态
//                ERPMaterialDO materialDO = new ERPMaterialDO();
//                if (Objects.equals(buttonType, DataButtonEnum.APPROVE.key)){
//                    item.setPublishStatus(PublishEnum.PUBLISH.getKey());
//                    materialDO.setSpuId(item.getSpuId());
//                    materialDO.setDataStatus(DataStatusEnum.APPROVED.key);
//                    materialDO.setPublishStatus(PublishEnum.PUBLISH.getKey());
//                }
//                if (Objects.equals(buttonType, DataButtonEnum.NOT_APPROVE.key)){
//                    item.setPublishStatus(PublishEnum.NOT_PUBLISH.getKey());
//                    materialDO.setSpuId(item.getSpuId());
//                    materialDO.setDataStatus(DataStatusEnum.NOT_APPROVE.key);
//                    materialDO.setPublishStatus(PublishEnum.NOT_PUBLISH.getKey());
//
//                }
//                materialDO.setReviewer(loginUser.getFullUserName());
//                materialDO.setReviewDt(now);
//                materialUpdateList.add(materialDO);
//            }else {
//                // 走流程，中间状态
//                Integer dataStatus = ApproveUtilX.getDataStatusByApproveStart(flowFunctionCode);
//                item.setDataStatus(dataStatus);
//            }
//            updateList.add(item);
//        }
//        if (!updateList.isEmpty()){
//            spuMapper.updateBatch(updateList);
//        }
//        if (!materialUpdateList.isEmpty()){
//            erpMaterialMapper.updateBatchBySpuId(materialUpdateList);
//        }
//
//        List<ApproveResult> approveResults = new ArrayList<>();
//        // 第四步，发起审批任务
//        if (flowTemplateDO != null) {
//            // 4.1 封装条件对象
//            List<FlowBizBO> flowBizBOS = new ArrayList<>();
//            for (SpuDO item : list) {
//                Map<String, Object> conditionMap = ReflectUtilX.toMapByExcluded(item, null);
//                FlowBizBO flowBizBO = new FlowBizBO();
//                flowBizBO.setObjId(item.getSpuId() + "");
//                flowBizBO.setConditionMap(conditionMap);
//                flowBizBOS.add(flowBizBO);
//            }
//            // 4.2 调用预申请方法
//            approveResults = approveService.preApproveTaskAdd(flowConfigBO, flowBizBOS);
//
//            // 4.3 批量发起审批
//            int i = approveService.approveTaskAdd(flowConfigBO, approveResults);
//        }
//
//        return ApproveUtilX.result(approveResults);
//    }

    @Override
    public Object spuFlowCallback(FlowCallback reqVO) {
        String objId = reqVO.getObjId();
        SpuDO item = this.spuValidateExists(Long.valueOf(objId));
        FlowCallbackBO flowCallbackBO = FlowCallbackBO.builder().flowCallback(reqVO).build();

        return spuFlowCallbackHandler.handleFlowCallback(item,flowCallbackBO);
    }

    @Override
    public SpuRespVO spuTest(SpuQueryReqVO reqVO) {

        List<SpuDO> spuDOList = spuMapper.selectList(reqVO);
        List<SpuRespVO> respVOList = BeanUtilX.copy(spuDOList, SpuRespVO::new);

        Map<Long, Integer> publishStatusMap = spuMapper.getSpuPublishStatus(respVOList).stream()
                .collect(Collectors.toMap(SpuRespVO::getSpuId, SpuRespVO::getPublishStatus));
        return null;
    }

    private void batchFillVoProperties(List<SpuRespVO> spuList){
        //查询字典数据
        List<String> dictCodeList = Arrays.asList(CustomerDictEnum.MATERIAL_TYPE.getDictCode(),
                CustomerDictEnum.MATERIAL_CATEGORY.getDictCode(), SystemDictEnum.MATERIAL_SOURCE.getDictCode(),
                CustomerDictEnum.MAIN_UNIT.getDictCode(), CustomerDictEnum.BRAND.getDictCode(),
                SystemDictEnum.SPU_PUBLISH_STATUS.getDictCode(), SystemDictEnum.APPROVED_STATUS.getDictCode());
        Map<String, String> dictMap = erpBaseService.dictMap(dictCodeList);


        if (CollUtilX.isEmpty(spuList)){
            return;
        }

        List<Long> empIdList = new ArrayList<>();
        List<String> deptOrgIds = new ArrayList<>();
        for (SpuRespVO item : spuList){
            empIdList.add(item.getDirectorId());
            deptOrgIds.add(item.getDirectorOrgId());
        }

        //查询负责人
        Map<Long, String> empNameMap = erpBaseService.getEmpNameByIdList(empIdList);

        //查询部门
        Map<String, String> orgNameMap = erpBaseService.getOrgNameByIds(deptOrgIds);

        for (SpuRespVO spuRespVO :spuList) {
            // 物料类型
            String materialTypeDictId = spuRespVO.getMaterialTypeDictId();
            if(StrUtilX.isNotEmpty(materialTypeDictId)){
                materialTypeDictId =  CustomerDictEnum.MATERIAL_TYPE.getDictCode() + "-" + materialTypeDictId;
                spuRespVO.setMaterialTypeDictName(dictMap.get(materialTypeDictId));
            }

            // 物料类别
            String materialCategoryDictId = spuRespVO.getMaterialCategoryDictId();
            if(StrUtilX.isNotEmpty(materialCategoryDictId)){
                materialCategoryDictId =  CustomerDictEnum.MATERIAL_CATEGORY.getDictCode() + "-" + materialCategoryDictId;
                spuRespVO.setMaterialCategoryDictName(dictMap.get(materialCategoryDictId));
            }

            // 物料来源
            Integer materialSourceDictId = spuRespVO.getMaterialSourceDictId();
            if(materialSourceDictId != null){
                String materialSourceDictIdStr =  SystemDictEnum.MATERIAL_SOURCE.getDictCode() + "-" + materialSourceDictId;
                spuRespVO.setMaterialSourceDictName(dictMap.get(materialSourceDictIdStr));
            }

            // 基本单位
            String mainUnitDictId = spuRespVO.getMainUnitDictId();
            if(StrUtilX.isNotEmpty(mainUnitDictId)){
                mainUnitDictId =  CustomerDictEnum.MAIN_UNIT.getDictCode() + "-" + mainUnitDictId;
                spuRespVO.setMainUnitDictName(dictMap.get(mainUnitDictId));
            }

            // 品牌
            String brandDictId = spuRespVO.getBrandDictId();
            if(StrUtilX.isNotEmpty(brandDictId)){
                brandDictId =  CustomerDictEnum.BRAND.getDictCode() + "-" + brandDictId;
                spuRespVO.setBrandDictName(dictMap.get(brandDictId));
            }

            // 上架状态
            Integer publishStatus = spuRespVO.getPublishStatus();
            if(publishStatus != null){
                String publishStatusStr = SystemDictEnum.SPU_PUBLISH_STATUS.getDictCode() + "-" + publishStatus;
                spuRespVO.setPublishStatusDictName(dictMap.get(publishStatusStr));
            }

            if(spuRespVO.getDataStatus() != null ){
                String dataStatus = SystemDictEnum.APPROVED_STATUS.getDictCode() + "-" + spuRespVO.getDataStatus();
                spuRespVO.setDataStatusDictName(dictMap.get(dataStatus));
            }

            spuRespVO.setDirectorName(empNameMap.get(spuRespVO.getDirectorId()));
            spuRespVO.setDirectorOrgName(orgNameMap.get(spuRespVO.getDirectorOrgId()));
        }

    }

}
