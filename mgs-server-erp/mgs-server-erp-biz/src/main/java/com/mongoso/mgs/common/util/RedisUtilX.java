package com.mongoso.mgs.common.util;

import com.mongoso.mgs.common.enums.order.OrderCodeEnum;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.redis.core.RedisTemplateX;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;


@Component
public class RedisUtilX {

    @Resource
    private RedisTemplateX redisTemplateX;


    /**
     * 工单通用规则 前缀 + 时间格式化 + num 位序列号
     *  格式化日期 yyMMdd
     * @return WO24041000001
     */
    public String genOrderCode(OrderCodeEnum oem) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(oem.getTimeStr());//"yyMMdd"
        String formattedDate = currentDate.format(formatter);
        String format = String.format("%0"+oem.getNum()+"d", 1);//5
        // 生成单据编码
        String documentCode = formattedDate + format;
        Long codeIncr=0L;
        if (StringUtils.isBlank(redisTemplateX.get(oem.getRedisKey()))){//mes:prodcode
            redisTemplateX.set(oem.getRedisKey(), documentCode);
        }else {
            String code = redisTemplateX.get(oem.getRedisKey());
            if (code.startsWith(formattedDate)){
                codeIncr = redisTemplateX.incrBy(oem.getRedisKey(), 1);
                documentCode = ""+ codeIncr;
            }else {
                redisTemplateX.set(oem.getRedisKey(), documentCode);
            }
        }
        documentCode = oem.getPrefix() + documentCode;//PRO
        return documentCode;
    }

    public String genCodeNoDate(OrderCodeEnum oem) {

        // 生成单据编码
        Long codeIncr=1L;

        if (StrUtilX.isEmpty(redisTemplateX.get(oem.getRedisKey()))){
            redisTemplateX.set(oem.getRedisKey(), codeIncr.toString());
        }else {
            codeIncr = redisTemplateX.incrBy(oem.getRedisKey(),1);
        }
        String documentCode = String.format("%0"+oem.getNum()+"d", codeIncr);
        documentCode = oem.getPrefix() +documentCode;//PRO
        return documentCode;
    }

    public String genOrderCode(OrderCodeEnum oem, Integer dayOut) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(oem.getTimeStr());//"yyMMdd"
        String formattedDate = currentDate.format(formatter);
        String format = String.format("%0"+oem.getNum()+"d", 1);//5
        // 生成单据编码
        String documentCode = formattedDate + format;
        Long codeIncr=0L;
        if (StringUtils.isBlank(redisTemplateX.get(oem.getRedisKey()))){//mes:prodcode
            redisTemplateX.set(oem.getRedisKey(), documentCode, dayOut, TimeUnit.DAYS);
        }else {
            String code = redisTemplateX.get(oem.getRedisKey());
            if (code.startsWith(formattedDate)){
                codeIncr = redisTemplateX.incrBy(oem.getRedisKey(), 1);
                documentCode = ""+ codeIncr;
            }else {
                redisTemplateX.set(oem.getRedisKey(), documentCode, dayOut, TimeUnit.DAYS);
            }
        }
        documentCode = oem.getPrefix() + documentCode;//PRO
        return documentCode;
    }

}
