package com.mongoso.mgs.module.dailycost.service.costaggre.detail;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailAditReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailPageReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailQueryReqVO;
import com.mongoso.mgs.module.dailycost.controller.admin.costaggre.vo.detail.CostAggreDetailRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 成本归集明细 Service 接口
 *
 * <AUTHOR>
 */
public interface CostAggreDetailService {

    /**
     * 创建成本归集明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long costAggreDetailAdd(@Valid CostAggreDetailAditReqVO reqVO);

    /**
     * 更新成本归集明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long costAggreDetailEdit(@Valid CostAggreDetailAditReqVO reqVO);

    /**
     * 删除成本归集明细
     *
     * @param aggreDetailId 编号
     */
    void costAggreDetailDel(Long aggreDetailId);

    /**
     * 获得成本归集明细信息
     *
     * @param aggreDetailId 编号
     * @return 成本归集明细信息
     */
    CostAggreDetailRespVO costAggreDetailDetail(Long aggreDetailId);

    /**
     * 获得成本归集明细列表
     *
     * @param reqVO 查询条件
     * @return 成本归集明细列表
     */
    List<CostAggreDetailRespVO> costAggreDetailList(@Valid CostAggreDetailQueryReqVO reqVO);

    /**
     * 获得成本归集明细分页
     *
     * @param reqVO 查询条件
     * @return 成本归集明细分页
     */
    PageResult<CostAggreDetailRespVO> costAggreDetailPage(@Valid CostAggreDetailPageReqVO reqVO);

}
