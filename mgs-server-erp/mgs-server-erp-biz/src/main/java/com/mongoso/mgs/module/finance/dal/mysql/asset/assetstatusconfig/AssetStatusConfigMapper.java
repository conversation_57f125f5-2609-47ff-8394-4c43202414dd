package com.mongoso.mgs.module.finance.dal.mysql.asset.assetstatusconfig;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetstatusconfig.vo.AssetStatusConfigPageReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetstatusconfig.vo.AssetStatusConfigQueryReqVO;
import com.mongoso.mgs.module.finance.controller.admin.asset.assetstatusconfig.vo.AssetStatusConfigRespVO;
import com.mongoso.mgs.module.finance.dal.db.asset.assetstatusconfig.AssetStatusConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产状态配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssetStatusConfigMapper extends BaseMapperX<AssetStatusConfigDO> {

    default PageResult<AssetStatusConfigDO> selectPageOld(AssetStatusConfigPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<AssetStatusConfigDO>lambdaQueryX()
                .likeIfPresent(AssetStatusConfigDO::getStatusName, reqVO.getStatusName())
                .eqIfPresent(AssetStatusConfigDO::getIsAccrualDepreciated, reqVO.getIsAccrualDepreciated())
                .eqIfPresent(AssetStatusConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(AssetStatusConfigDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetStatusConfigDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetStatusConfigDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetStatusConfigDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetStatusConfigDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetStatusConfigDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetStatusConfigDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(AssetStatusConfigDO::getCreatedDt));
    }



    default PageResult<AssetStatusConfigDO> selectPage(AssetStatusConfigPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<AssetStatusConfigDO>lambdaQueryX()
                .likeIfPresent(AssetStatusConfigDO::getStatusName, reqVO.getStatusName())
                .eqIfPresent(AssetStatusConfigDO::getIsAccrualDepreciated, reqVO.getIsAccrualDepreciated())
                .eqIfPresent(AssetStatusConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(AssetStatusConfigDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetStatusConfigDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetStatusConfigDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetStatusConfigDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetStatusConfigDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetStatusConfigDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetStatusConfigDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(AssetStatusConfigDO::getCreatedDt));
    }

    default List<AssetStatusConfigDO> selectListOld(AssetStatusConfigQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<AssetStatusConfigDO>lambdaQueryX()
                .likeIfPresent(AssetStatusConfigDO::getStatusName, reqVO.getStatusName())
                .eqIfPresent(AssetStatusConfigDO::getIsAccrualDepreciated, reqVO.getIsAccrualDepreciated())
                .eqIfPresent(AssetStatusConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(AssetStatusConfigDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetStatusConfigDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetStatusConfigDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetStatusConfigDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetStatusConfigDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetStatusConfigDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetStatusConfigDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(AssetStatusConfigDO::getCreatedDt));
    }

    default List<AssetStatusConfigDO> selectList(AssetStatusConfigQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<AssetStatusConfigDO>lambdaQueryX()
                .likeIfPresent(AssetStatusConfigDO::getStatusName, reqVO.getStatusName())
                .eqIfPresent(AssetStatusConfigDO::getIsAccrualDepreciated, reqVO.getIsAccrualDepreciated())
                .eqIfPresent(AssetStatusConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(AssetStatusConfigDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(AssetStatusConfigDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(AssetStatusConfigDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(AssetStatusConfigDO::getDataStatus, reqVO.getDataStatus())
                .betweenIfPresent(AssetStatusConfigDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(AssetStatusConfigDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(AssetStatusConfigDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(AssetStatusConfigDO::getCreatedDt));
    }

    default AssetStatusConfigDO selectOne(Long assetStatusConfigId) {
        return selectOne(LambdaQueryWrapperX.<AssetStatusConfigDO>lambdaQueryX()
                .eq(AssetStatusConfigDO::getAssetStatusConfigId, assetStatusConfigId)
                .eq(AssetStatusConfigDO::getDataStatus, 1)
                .eq(AssetStatusConfigDO::getIsAccrualDepreciated, 1));
    }

    default Long selectCount(AssetStatusConfigQueryReqVO reqVO) {
        return selectCount(LambdaQueryWrapperX.<AssetStatusConfigDO>lambdaQueryX()
                .eqIfPresent(AssetStatusConfigDO::getStatusName, reqVO.getStatusName()));
    }

    List<AssetStatusConfigRespVO> selectUnableDelete(@Param("list") List<Long> idList);
}