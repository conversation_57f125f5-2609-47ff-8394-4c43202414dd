package com.mongoso.mgs.module.produce.dal.mysql.workpickingreturnd;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.component.flow.enums.DataStatusEnum;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.WorkPickingReturnPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.WorkPickingReturnQueryReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.item.WorkPickingReturnItemPageReqVO;
import com.mongoso.mgs.module.produce.controller.admin.workpickingreturn.vo.item.WorkPickingReturnItemRespVO;
import com.mongoso.mgs.module.produce.dal.db.workpickingreturn.WorkPickingReturnDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 工单退料单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkPickingReturnMapper extends BaseMapperX<WorkPickingReturnDO> {

    default PageResult<WorkPickingReturnDO> selectPageOld(WorkPickingReturnPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<WorkPickingReturnDO>lambdaQueryX()
                .likeIfPresent(WorkPickingReturnDO::getWorkPickingReturnCode, reqVO.getWorkPickingReturnCode())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnTypeDictId, reqVO.getWorkPickingReturnTypeDictId())
                .eqIfPresent(WorkPickingReturnDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(WorkPickingReturnDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(WorkPickingReturnDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkPickingReturnDO::getIsFullInbounded, reqVO.getIsFullInbounded())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnBizType, reqVO.getWorkPickingReturnBizType())
                .eqIfPresent(WorkPickingReturnDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkPickingReturnDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkPickingReturnDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkPickingReturnDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkPickingReturnDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkPickingReturnDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkPickingReturnDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                .orderByDesc(WorkPickingReturnDO::getCreatedDt));
    }



    default PageResult<WorkPickingReturnDO> selectPage(WorkPickingReturnPageReqVO reqVO) {
        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<WorkPickingReturnDO>lambdaQueryX()
                .likeIfPresent(WorkPickingReturnDO::getWorkPickingReturnCode, reqVO.getWorkPickingReturnCode())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnTypeDictId, reqVO.getWorkPickingReturnTypeDictId())
                .eqIfPresent(WorkPickingReturnDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(WorkPickingReturnDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(WorkPickingReturnDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkPickingReturnDO::getIsFullInbounded, reqVO.getIsFullInbounded())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnBizType, reqVO.getWorkPickingReturnBizType())
                .eqIfPresent(WorkPickingReturnDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkPickingReturnDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkPickingReturnDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkPickingReturnDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkPickingReturnDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkPickingReturnDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkPickingReturnDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(WorkPickingReturnDO::getCreatedDt));
    }

    default List<WorkPickingReturnDO> selectListOld(WorkPickingReturnQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<WorkPickingReturnDO>lambdaQueryX()
                .likeIfPresent(WorkPickingReturnDO::getWorkPickingReturnCode, reqVO.getWorkPickingReturnCode())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnTypeDictId, reqVO.getWorkPickingReturnTypeDictId())
                .eqIfPresent(WorkPickingReturnDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(WorkPickingReturnDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(WorkPickingReturnDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkPickingReturnDO::getIsFullInbounded, reqVO.getIsFullInbounded())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnBizType, reqVO.getWorkPickingReturnBizType())
                .eqIfPresent(WorkPickingReturnDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkPickingReturnDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkPickingReturnDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkPickingReturnDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkPickingReturnDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkPickingReturnDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkPickingReturnDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                    .orderByDesc(WorkPickingReturnDO::getCreatedDt));
    }

    default List<WorkPickingReturnDO> selectList(WorkPickingReturnQueryReqVO reqVO) {
        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<WorkPickingReturnDO>lambdaQueryX()
                .likeIfPresent(WorkPickingReturnDO::getWorkPickingReturnCode, reqVO.getWorkPickingReturnCode())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnTypeDictId, reqVO.getWorkPickingReturnTypeDictId())
                .eqIfPresent(WorkPickingReturnDO::getRelatedOrderId, reqVO.getRelatedOrderId())
                .likeIfPresent(WorkPickingReturnDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .betweenIfPresent(WorkPickingReturnDO::getFormDt, reqVO.getStartFormDt(), reqVO.getEndFormDt())
                .eqIfPresent(WorkPickingReturnDO::getIsFullInbounded, reqVO.getIsFullInbounded())
                .eqIfPresent(WorkPickingReturnDO::getWorkPickingReturnBizType, reqVO.getWorkPickingReturnBizType())
                .eqIfPresent(WorkPickingReturnDO::getDataStatus, reqVO.getDataStatus())
                .eqIfPresent(WorkPickingReturnDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WorkPickingReturnDO::getDirectorId, reqVO.getDirectorId())
                .eqIfPresent(WorkPickingReturnDO::getDirectorOrgId, reqVO.getDirectorOrgId())
                .betweenIfPresent(WorkPickingReturnDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(WorkPickingReturnDO::getApprovedBy, reqVO.getApprovedBy())
                .betweenIfPresent(WorkPickingReturnDO::getApprovedDt, reqVO.getStartApprovedDt(), reqVO.getEndApprovedDt())
                        .orderByDesc(WorkPickingReturnDO::getCreatedDt));
    }

    IPage<WorkPickingReturnItemRespVO> selectPage2(Page<WorkPickingReturnItemPageReqVO> page, @Param("reqVO") WorkPickingReturnItemPageReqVO reqVO);

    default List<WorkPickingReturnDO> selectListByRelatedOrderId(Long relatedOrderId, Integer dataStatus, Integer bizType){
        return selectList(LambdaQueryWrapperX.<WorkPickingReturnDO>lambdaQueryX()
                .eq(WorkPickingReturnDO::getWorkPickingReturnBizType, bizType)
                .eq(WorkPickingReturnDO::getDataStatus, dataStatus)
                .eq(WorkPickingReturnDO::getRelatedOrderId, relatedOrderId));
    }

    default List<WorkPickingReturnDO> selectListByBizType(Integer bizType){
        return selectList(LambdaQueryWrapperX.<WorkPickingReturnDO>lambdaQueryX()
                .eq(WorkPickingReturnDO::getWorkPickingReturnBizType, bizType)
                .eq(WorkPickingReturnDO::getDataStatus, DataStatusEnum.APPROVED.getKey()));
    }


}