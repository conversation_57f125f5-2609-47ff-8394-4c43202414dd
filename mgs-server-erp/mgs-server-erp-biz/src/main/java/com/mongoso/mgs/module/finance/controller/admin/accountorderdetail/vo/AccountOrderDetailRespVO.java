package com.mongoso.mgs.module.finance.controller.admin.accountorderdetail.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 对账单明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountOrderDetailRespVO extends AccountOrderDetailBaseVO {

    private String directorName;
    /** 责任部门 */
    private String directorOrgName;

    /** 数量 */
    private BigDecimal noticeQty;

    /** 物料编码 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 物料类别id */
    private String materialCategoryDictId;
    private String materialCategoryDictName;

    /** 规格设置 */
    private String specModel;

    /** 规格属性 */
    private String specAttributeStr;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 可对账金额 */
    private BigDecimal reconcilableAmt;

    /** 开票计划策略 */
    private String invoiceStrategyDictName;

    private String invoicingSettlementStrategyDictName;

    /** 收款计划策略--取父集 */
    private String relatedCollectionPlanStrategyDictName;

    /** 收款计划策略--取子集 */
    private String collectionPlanStrategyDictName;

    /** 退款策略 */
    private String saleRefundPlanStrategyDictName;
}
