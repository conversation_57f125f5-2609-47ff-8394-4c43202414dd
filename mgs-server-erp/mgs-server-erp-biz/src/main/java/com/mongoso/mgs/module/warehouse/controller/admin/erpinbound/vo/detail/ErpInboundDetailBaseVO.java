package com.mongoso.mgs.module.warehouse.controller.admin.erpinbound.vo.detail;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  
import java.math.BigDecimal;
 import java.math.BigDecimal;
 
/**
 * 入库单明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ErpInboundDetailBaseVO implements Serializable {

    /** 入库单明细ID */
    private Long inboundDetailId;

    /** 入库单ID */
    private Long inboundId;

    /** 入库单号 */
    private String inboundCode;

    /** 行号 */
    private Long rowNo;

    /** 关联单据明细ID */
    private Long relatedOrderDetailId;

    /** 关联行号 */
    private Integer relatedRowNo;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    private String materialCode;

    /** 基本单位ID */
    private String mainUnitDictId;

    /** 入库数量 */
    private BigDecimal inboundQty;

    /** 已入库数量 */
    private BigDecimal inboundedQty;

    /** 入库组织ID */
    private String warehouseOrgId;

    /** 备注 */
    private String remark;

}
