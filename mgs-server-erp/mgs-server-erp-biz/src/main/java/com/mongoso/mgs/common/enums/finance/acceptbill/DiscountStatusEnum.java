package com.mongoso.mgs.common.enums.finance.acceptbill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 贴现状态
 */
@AllArgsConstructor
@Getter
public enum DiscountStatusEnum implements Serializable {
    NOT_DISCOUNTED(0,"未贴现"),
    PARTIALLY_DISCOUNTED(1,"部分贴现"),
    DISCOUNTED(2,"已贴现"),
    NO_DISCOUNT_NEEDED(3,"无需贴现");

    public final int key;// 类型
    public final String value;// 描述
}
