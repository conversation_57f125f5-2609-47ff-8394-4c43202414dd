package com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.produce.controller.admin.erpprodorderchangedetail.vo.*;
import com.mongoso.mgs.module.produce.service.erpprodorderchangedetail.ErpProdOrderChangeDetailService;

/**
 * 生产物料 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/produce")
@Validated
public class ErpProdOrderChangeDetailController {

    @Resource
    private ErpProdOrderChangeDetailService erpProdOrderChangeDetailService;

    @OperateLog("生产物料添加或编辑")
    @PostMapping("/erpProdOrderChangeDetailAdit")
    @PreAuthorize("@ss.hasPermission('erpProdOrderChangeDetail:adit')")
    public ResultX<Long> erpProdOrderChangeDetailAdit(@Valid @RequestBody ErpProdOrderChangeDetailAditReqVO reqVO) {
        return success(reqVO.getProdOrderChangeDetailId() == null
                            ? erpProdOrderChangeDetailService.erpProdOrderChangeDetailAdd(reqVO)
                            : erpProdOrderChangeDetailService.erpProdOrderChangeDetailEdit(reqVO));
    }

    @OperateLog("生产物料删除")
    @PostMapping("/erpProdOrderChangeDetailDel")
    @PreAuthorize("@ss.hasPermission('erpProdOrderChangeDetail:delete')")
    public ResultX<Boolean> erpProdOrderChangeDetailDel(@Valid @RequestBody ErpProdOrderChangeDetailPrimaryReqVO reqVO) {
        erpProdOrderChangeDetailService.erpProdOrderChangeDetailDel(reqVO.getProdOrderChangeDetailId());
        return success(true);
    }

    @OperateLog("生产物料详情")
    @PostMapping("/erpProdOrderChangeDetailDetail")
    @PreAuthorize("@ss.hasPermission('erpProdOrderChangeDetail:query')")
    public ResultX<ErpProdOrderChangeDetailRespVO> erpProdOrderChangeDetailDetail(@Valid @RequestBody ErpProdOrderChangeDetailPrimaryReqVO reqVO) {
        return success(erpProdOrderChangeDetailService.erpProdOrderChangeDetailDetail(reqVO.getProdOrderChangeDetailId()));
    }

    @OperateLog("生产物料列表")
    @PostMapping("/erpProdOrderChangeDetailList")
    @PreAuthorize("@ss.hasPermission('erpProdOrderChangeDetail:query')")
    public ResultX<List<ErpProdOrderChangeDetailRespVO>> erpProdOrderChangeDetailList(@Valid @RequestBody ErpProdOrderChangeDetailQueryReqVO reqVO) {
        return success(erpProdOrderChangeDetailService.erpProdOrderChangeDetailList(reqVO));
    }

    @OperateLog("生产物料分页")
    @PostMapping("/erpProdOrderChangeDetailPage")
    @PreAuthorize("@ss.hasPermission('erpProdOrderChangeDetail:query')")
    public ResultX<PageResult<ErpProdOrderChangeDetailRespVO>> erpProdOrderChangeDetailPage(@Valid @RequestBody ErpProdOrderChangeDetailPageReqVO reqVO) {
        return success(erpProdOrderChangeDetailService.erpProdOrderChangeDetailPage(reqVO));
    }

    @OperateLog("查询不可移除物料")
    @PostMapping("/findNonRemovableMaterial")
    @PreAuthorize("@ss.hasPermission('erpProdOrderChangeDetail:delete')")
    public ResultX<Boolean> findNonRemovableMaterial(@Valid @RequestBody ErpProdOrderChangeDetailQueryReqVO reqVO) {
        erpProdOrderChangeDetailService.findNonRemovableMaterial(reqVO);
        return success(true);
    }

}
