package com.mongoso.mgs.module.produce.controller.admin.materialbom.vo;

import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * BOM链条响应VO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class MaterialBomChainRespVO implements Serializable {

    /** 链条ID（用于标识不同的链条） */
    private String chainId;

    /** 链条路径描述 */
    private String chainPath;

    /** 链条中的节点列表（从根节点到目标物料） */
    private List<MaterialBomChainNodeVO> nodes = new ArrayList<>();

    /** 链条深度 */
    private Integer depth;

    /** 是否包含目标物料 */
    private Boolean containsTargetMaterial;

    @Data
    @ToString
    public static class MaterialBomChainNodeVO implements Serializable {

        /** 主键ID */
        private Long materialBomId;

        /** 物料id */
        private Long materialId;

        /** 物料编码 */
        private String materialCode;

        /** 物料名称 */
        private String materialName;

        //计算公式
        private String calcFormula;

        //下级总和
        private BigDecimal subTotalCost;

        //单价
        private BigDecimal costValue;

        //成本类型
        private Integer costType;

//        /** 物料类别id */
//        private String materialCategoryDictId;
//        private String materialCategoryDictName;
//
//        /** 规格设置 */
//        private String specModel;
//
//        /** 规格属性 */
//        private String specAttributeStr;
//
//        /** 基本单位 */
//        private String mainUnitDictId;
//        private String mainUnitDictName;
//
//        /** 物料来源 */
//        private Integer materialSourceDictId;
//        private String materialSourceDictName;
//
//        /** 单据时间 */
//        @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//        private LocalDateTime formDt;
//
//        /** 是否父级节点['否','是'] */
//        private Integer isParent;
//
//        /** 版本号 */
//        private Integer version;
//
//        /** 备注 */
//        private String remark;
//
//        /** 审核状态 */
//        private Integer dataStatus;
//
//        /** 责任人 */
//        private Long directorId;
//        private String directorName;
//
//        /** 责任部门 */
//        private String directorOrgId;
//        private String directorOrgName;
//
//        /** 审批人 */
//        private String approvedBy;
//
//        /** 审批时间 */
//        @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//        private LocalDateTime approvedDt;
//
//        /** 父级id */
//        private Long parentId;
//
//        /** 父级ids */
//        private String pids;
//
//        /** 行号 */
//        private Integer rowNo;
//
        /** 节点在链条中的层级 */
        private Integer level;

        /** 需求数量 */
        private BigDecimal demandQty;

        /** 损耗率 */
        private BigDecimal lossRate;

        /** 预估用量 */
        private BigDecimal estimatedQty;
//
//        /** 领料方式 */
//        private Integer pickingMethodDictId;
//        private String pickingMethodDictName;
//
//        /** 默认发料仓库 */
//        private String warehouseOrgId;
//        private String warehouseOrgName;
//
//        /** 子物料外键 */
//        private Long fkMaterialBomId;
//
        /** 是否为目标物料 */
        private Boolean isTargetMaterial;
//
        /** 节点类型：root-根节点，intermediate-中间节点，target-目标节点 */
        private String nodeType;
    }

}
