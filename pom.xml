<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <groupId>com.mongoso.cloud</groupId>
    <artifactId>platform-erp</artifactId>
    <version>2.0.3</version>

    <description>
        软件平台-子系统端
    </description>

    <modules>
        <module>mgs-server-erp</module>
    </modules>

    <properties>
        <!-- mgs-微服务 -->
        <mongoso-cloud-starter.version>2.0.19-jgzy</mongoso-cloud-starter.version>
        <mongoso-common.version>3.0.48</mongoso-common.version>
        <mgs-flow.version>2.2.2</mgs-flow.version>
        <mgs-message.version>2.0.15</mgs-message.version>

        <!-- mgs-系统调用 -->
        <platform-api.version>2.0.3</platform-api.version>
        <mes-api.version>2.0.3</mes-api.version>


        <xxl-job.version>2.4.1</xxl-job.version>

        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- mgs-微服务统一版本管理 -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-dependencies</artifactId>
                <version>${mongoso-cloud-starter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 微服务starter -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-cloud-starter</artifactId>
                <version>${mongoso-cloud-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 公共模块 -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-platform-common</artifactId>
                <version>${mongoso-common.version}</version>
            </dependency>

            <!-- 平台模块 api -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-server-platform-api</artifactId>
                <version>${platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-server-erp-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-server-mes-api</artifactId>
                <version>${mes-api.version}</version>
            </dependency>

            <!-- 审批流程 -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-flow</artifactId>
                <version>${mgs-flow.version}</version>
            </dependency>

            <!-- 消息 -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-message</artifactId>
                <version>${mgs-message.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!-- 上传maven私服-->
    <distributionManagement>
        <repository>
            <id>mongoso-releases</id>
            <url>http://maven.mongoso.com:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>mongoso-snapshots</id>
            <url>http://maven.mongoso.com:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>