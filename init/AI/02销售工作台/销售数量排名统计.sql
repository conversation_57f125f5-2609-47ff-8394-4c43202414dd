--销售数量排行统计sql
--销售订单表和销售订单明细表和物料表，条件是审核状态为已审核、销售订单变更id为空、单据时间大于30天（近1个月内）
--按物料编码、物料名称、规格属性分组
--按销售订单明细表的总量之和进行倒序
--只查询10条数据
--查询结果物料编码、物料名称、规格属性，总量
SELECT m.material_code, m.material_name, m.spec_attribute_str, SUM(b.qty) value
FROM u_erp_sale_order a
    JOIN u_erp_sale_order_detail b ON a.sale_order_id = b.sale_order_id
    LEFT JOIN u_material m ON b.material_id = m.material_id
WHERE a.data_status = 1
  AND a.sale_chang_id IS NULL
  AND a.form_dt > CURRENT_DATE - INTERVAL '30 days'
GROUP BY m.material_code, m.material_name, m.spec_attribute_str
ORDER BY value DESC
    LIMIT 10