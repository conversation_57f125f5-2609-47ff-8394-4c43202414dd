--销售订单的待交付订单数量统计sql
--查询销售订单表，条件是审核状态为已审核、强制关闭状态为非关闭、出库状态为未出库或部分出库、变更销售单id为空

SELECT COUNT(1)
FROM u_erp_sale_order a
WHERE a.data_status = 1
  AND a.is_force_close = 0
  AND a.outbound_status IN (0, 1)
  AND sale_chang_id IS NULL

--销售订单的待出库商品数量统计sql
--查询销售订单表和销售订单明细表，条件是审核状态为已审核、强制关闭状态为非关闭、出库状态为未出库或部分出库、变更销售单id为空，
--待出库数量=明细表的总量-明细表的已出库数量
SELECT COALESCE(SUM(b.qty - b.out_qty), 0)
FROM u_erp_sale_order a
         JOIN u_erp_sale_order_detail b ON a.sale_order_id = b.sale_order_id
WHERE a.data_status = 1
  AND a.is_force_close = 0
  AND a.outbound_status IN (0, 1)
  AND a.sale_chang_id IS NULL

--待收款金额统计sql
--查询收付款表，条件是审核状态为已审核、单据类型是收款类型、应收总金额-实收金额大于0，
--待收款金额=应收总金额-实收金额
SELECT COALESCE(SUM(a.total_amt - a.real_amt), 0)
FROM u_should_payment a
WHERE a.data_status = 1
  AND a.form_type = 1
  AND (a.total_amt - a.real_amt) > 0

--待退款金额统计sql
--查询收付款表，条件是审核状态为已审核、单据类型是收款类型、应收总金额-实收金额小于0，
--待收款金额=应收总金额-实收金额
SELECT ABS(COALESCE(SUM(a.total_amt - a.real_amt), 0))
FROM u_should_payment a
WHERE a.data_status = 1
  AND a.form_type = 1
  AND (a.total_amt - a.real_amt) < 0

--待退货商品数量统计sql
--查询销售退货单表和销售退货单明细表，条件是审核状态为已审核
--待退货商品数量=明细表的退货数量-明细表的已入库数量
SELECT COALESCE(SUM(b.return_qty - b.inbounded_qty), 0)
FROM u_sale_return a
    JOIN u_sale_return_detail b ON a.sale_return_id = b.sale_return_id
WHERE a.data_status = 1