-- 仓库物料库存排名统计sql
--库存表和物料表，条件是库存数量不等于0、单据时间大于30天（近1个月内）
--按物料编码、物料名称、规格属性分组
--按库存数量之和进行倒序
--只查询10条数据
--查询结果物料编码、物料名称、规格属性，库存数量
SELECT m.material_code, m.material_name, m.spec_attribute_str, SUM(a.stock_qty) value
FROM u_material_stock a
    LEFT JOIN u_material m ON a.material_id = m.material_id
WHERE a.stock_qty != 0
GROUP BY m.material_code, m.material_name, m.spec_attribute_str
ORDER BY value DESC
    LIMIT 10