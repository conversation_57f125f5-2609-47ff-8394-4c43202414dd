-- 待入库物料数量统计sql
--子查询1，采购订单表和采购订单明细表，条件是审核状态为已审核、强制关闭状态为非关闭、入库状态为未入库或部分入库
--待入库物数量=（采购数量-入库数量）之和
--子查询2，生产工单表，条件是审核状态为已审核、是否入库为是、单据状态为已完成或强制完工或已关闭、是否全部入库为否
--可入库数量之和
--子查询3，销售退货单表和销售退货单明细表，条件是审核状态为已审核、是否全部操作为否
--待入库物数量=（退货数量-入库数量）之和
--子查询4，工单退料单表和工单退料单明细表，条件是审核状态为已审核、是否完成入库为否
--入库数量之和

-- 在上面4个子查询得出的结果后求和
SELECT SUM(inboundQty)
FROM (
            SELECT COALESCE(SUM(b.purchase_qty-inbounded_qty), 0) inboundQty
            FROM u_purchase_order a
            JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
            WHERE a.data_status = 1
            AND a.is_force_close = 0
            AND a.inbound_status IN (0, 1)
            UNION ALL
            SELECT COALESCE(SUM(inboundable_qty), 0) inboundQty
            FROM u_prod_work
            WHERE data_status = 1
            AND is_inbound = 1
            AND form_status IN (2, 3, 4)
            AND is_full_inbounded = 0
            UNION ALL
            SELECT COALESCE(SUM(b.return_qty - b.inbounded_qty), 0) inboundQty
            FROM u_sale_return a
            JOIN u_sale_return_detail b ON a.sale_return_id = b.sale_return_id
            WHERE a.data_status = 1
            AND a.is_full_opered = 0
            UNION ALL
            SELECT COALESCE(SUM(inboundable_qty), 0) inboundQty
            FROM u_work_picking_return a
            JOIN u_work_picking_return_detail b ON a.work_picking_return_id = b.work_picking_return_id
            WHERE a.data_status = 1
            AND b.is_material_full_inbounded = 0
         ) t


--待出库物料数量统计sql
--子查询1，销售订单表和销售订单明细表，条件是审核状态为已审核、强制关闭状态为非关闭、出库状态为未处库或部分处库、销售订单变更id为空
--待入库物数量=（销售数量-已出库数量）之和
--子查询2，采购退货单表和采购退货单明细表，条件是审核状态为已审核
--可退货出货数量之和
--子查询3，工单退料单表和工单退料单明细表，条件是审核状态为已审核、是否全部操作为否
--可出库数量之和
-- 在上面3个子查询得出的结果后求和
SELECT SUM(outboundQty)
FROM (
            SELECT COALESCE(SUM(qty-out_qty), 0) outboundQty
            FROM u_erp_sale_order a
            JOIN u_erp_sale_order_detail b ON a.sale_order_id = b.sale_order_id
            WHERE a.data_status = 1
            AND a.is_force_close = 0
            AND a.outbound_status IN (0, 1)
            AND a.sale_chang_id IS NULL
            UNION ALL
            <!-- 采购退货 -->
            SELECT COALESCE(SUM(outboundable_qty), 0) outboundQty
            FROM u_purchase_return a
            JOIN u_purchase_return_detail b ON a.purchase_return_id = b.purchase_return_id
            WHERE a.data_status = 1
            <!-- 工单退料单/委外订单退料单 -->
            UNION ALL
            SELECT COALESCE(SUM(outboundable_qty), 0) outboundQty
            FROM u_work_picking a
            JOIN u_work_picking_detail b ON a.work_picking_id = b.work_picking_id
            WHERE a.data_status = 1
            AND b.is_material_full_outbounded = 0
        ) t

-- 待归还物料数量统计sql
--外借单表和外借单明细表，条件是审核状态为已审核、是否全部归还为否
--待归还数量之和
SELECT COALESCE(SUM(pending_return_qty), 0)
FROM u_material_loan a
    JOIN u_material_loan_detail b ON a.loan_id = b.loan_id
WHERE a.data_status = 1
  AND b.is_material_returned = 0

-- 待完成盘点单统计sql
--盘点单表，条件是审核状态为已审核、盘点状态为未盘点或盘点中
SELECT COUNT(1)
FROM u_inventory a
WHERE a.data_status = 1
  AND inventory_status IN (0, 1)