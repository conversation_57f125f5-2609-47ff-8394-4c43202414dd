--在制订单数统计sql
--生产订单表，条件是审核状态为已审核、单据状态为待生产或生产中
--统计总数量
SELECT COUNT(1)
FROM u_erp_prod_order a
WHERE a.data_status = 1
  AND a.form_status IN (0, 1)

--在制工单数统计sql
--生产工单表，条件是审核状态为已审核、单据状态为待生产或生产中
--统计总数量
SELECT COUNT(1)
FROM u_prod_work a
WHERE a.data_status = 1
  AND a.form_status IN (0, 1)

--待生产订单数统计sql
--生产订单表，条件是审核状态为已审核、单据状态为待生产或生产中
--生产计划总数之和
SELECT SUM(prod_plan_total_qty)
FROM u_erp_prod_order a
WHERE a.data_status = 1 AND a.form_status IN (0, 1)

--待生产工单数统计sql
--生产工单表，条件是审核状态为已审核、单据状态为待生产或生产中
--计划生产数量之和
SELECT SUM(work_plan_total_qty)
FROM u_prod_work a
WHERE a.data_status = 1 AND a.form_status IN (0, 1)

--工单完工数统计sql
--生产工单表，条件是审核状态为已审核、单据时间等于当天
--实际生产数量之和
SELECT SUM(work_act_total_qty) as workActTotalQty
FROM u_prod_work a
WHERE a.data_status = 1 and DATE(form_dt) = CURRENT_DATE

--当天报工良品数统计sql
--报工记录表，条件是报工时间等于当天、报工类型是工单报工或工序报工或委外
--良品数量之和
SELECT COALESCE(SUM(ok_qty), 0)
FROM u_reported_work
WHERE DATE(reported_dt) = CURRENT_DATE
  AND reported_work_type IN(0, 1, 2)

--当天报工不良数统计sql
--报工记录表，条件是报工时间等于当天、报工类型是工单报工或工序报工或委外
--不良品数量之和
SELECT COALESCE(SUM(ng_qty), 0)
FROM u_reported_work
WHERE DATE(reported_dt) = CURRENT_DATE
  AND reported_work_type IN(0, 1, 2)


