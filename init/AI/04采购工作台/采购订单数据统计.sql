--采购需求订单的待交付订单数量统计sql
--查询采购需求订单表和采购需求订单明细表，条件是审核状态为已审核、可采购数量大于0
--去除重复的采购需求订单
SELECT COUNT(DISTINCT a.purchase_demand_id)
FROM u_purchase_demand a
JOIN u_purchase_demand_detail b ON a.purchase_demand_id = b.purchase_demand_id
WHERE a.data_status = 1
  AND b.purchase_able_qty > 0

--采购订单的待完成订单数量统计sql
--查询采购订单表，条件是审核状态为已审核、强制关闭状态为非关闭、入库状态为未入库或部分入库
SELECT COUNT(1)
FROM u_purchase_order a
WHERE a.data_status = 1
  AND a.is_force_close = 0
  AND a.inbound_status IN (0, 1)

--采购单的待入库物料数量统计sql
--查询采购订单表和采购订单明细表，条件是审核状态为已审核、强制关闭状态为非关闭、入库状态为未入库或部分入库
--待入库物料数量=采购数量-入库数量
--如果采购数量-入库数量为空，则返回0，否则返回采购数量-入库数量之和
SELECT COALESCE(SUM(b.purchase_qty - b.inbounded_qty), 0)
FROM u_purchase_order a
JOIN u_purchase_order_detail b ON a.purchase_order_id = b.purchase_order_id
WHERE a.data_status = 1
  AND a.is_force_close = 0
  AND a.inbound_status IN (0, 1)

--工序委外待采购数量统计sql
--查询工序委外需求清单
--可采购数量之和
SELECT COALESCE(SUM(purchaseable_qty), 0)
FROM u_process_out_demand

--待退货物料数量统计sql
--查询销售退货单表和销售退货单明细表，条件是审核状态为已审核
--待退货商品数量=明细表的退货数量-明细表的已入库数量
SELECT COALESCE(SUM(b.return_qty - b.inbounded_qty), 0)
FROM u_sale_return a
JOIN u_sale_return_detail b ON a.sale_return_id = b.sale_return_id
WHERE a.data_status = 1