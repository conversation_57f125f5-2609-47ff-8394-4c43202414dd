--erp 新增工作中心关联人员表 --卢致灵
CREATE TABLE "erp"."u_work_center_person" (
                                              "center_person_id" int8 NOT NULL,
                                              "created_id" int8,
                                              "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                              "created_dt" timestamp(6),
                                              "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                              "updated_dt" timestamp(6),
                                              "tenant_id" int8,
                                              "work_center_id" int8,
                                              "work_center_code" varchar(50) COLLATE "pg_catalog"."default",
                                              "row_no" int4,
                                              "user_id" int8
)
;
COMMENT ON COLUMN "erp"."u_work_center_person"."center_person_id" IS '主键';
COMMENT ON COLUMN "erp"."u_work_center_person"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_work_center_person"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_work_center_person"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_work_center_person"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_work_center_person"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_work_center_person"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_work_center_person"."work_center_id" IS '工作中心ID';
COMMENT ON COLUMN "erp"."u_work_center_person"."work_center_code" IS '工作中心编码';
COMMENT ON COLUMN "erp"."u_work_center_person"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_work_center_person"."user_id" IS '用户ID';
COMMENT ON TABLE "erp"."u_work_center_person" IS '工作中心关联人员表';

ALTER TABLE "erp"."u_work_center_person" ADD CONSTRAINT "u_work_center_person_pkey" PRIMARY KEY ("center_person_id");

--platform 新增采购状态系统枚举 --卢致灵
INSERT INTO "platform"."t_dict" ("dict_id", "dict_code", "parent_dict_id", "parent_dict_code", "dir_type", "dict_type", "dict_name", "is_sys_value", "params", "remark", "seq", "is_enable", "deleted", "created_id", "created_by", "created_dt", "updated_by", "updated_dt", "tenant_id", "dict_structure", "dict_obj_code") VALUES (1909887736811294721, '0', 1909884113108799489, 'SERP411', 2, 1, '未采购', 1, '', '', 1, 1, 0, 1821080084202868738, 'admin/管理员', '2025-04-09 16:34:55.112567', 'admin/管理员', '2025-04-09 16:34:55.112567', 0, NULL, 'SERP411');

--erp 定额清单表新增"计价单编码/行号" 字段 --卢致灵
ALTER TABLE "erp"."u_calculate_price_order_rule"  ADD COLUMN "calculate_price_order_code" VARCHAR(50) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."calculate_price_order_code" IS '计价单编码';

ALTER TABLE "erp"."u_calculate_price_order_rule"  ADD COLUMN "row_no" INT4;
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."row_no" IS '行号';

ALTER TABLE "erp"."u_calculate_price_order_rule"  ADD COLUMN "parent_name" VARCHAR(50) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."parent_name" IS '计价维度父级名称';

ALTER TABLE "erp"."u_calculate_price_order_rule"  ADD COLUMN "calculate_price_dimension_name" VARCHAR(50) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."calculate_price_dimension_name" IS '计价维度名称';

--platform 更新计件方式 --卢致灵
UPDATE "platform"."t_dict" SET "dict_name" = '不计价' WHERE "dict_id" = 1909888250668060673;
UPDATE "platform"."t_dict" SET "dict_name" = '计价方式' WHERE "dict_id" = 1909884145631432706;

--erp 设备台账表新增"机台吨位" 字段 --卢致灵
ALTER TABLE "erp"."u_device"  ADD COLUMN "machine_tonnage" VARCHAR(250) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_device"."machine_tonnage" IS '机台吨位';

--erp 工作中心表新增"无技能分配比例" 字段 --卢致灵
ALTER TABLE "erp"."u_work_center"  ADD COLUMN "defalut_ratio" NUMERIC(18,2);
COMMENT ON COLUMN "erp"."u_work_center"."defalut_ratio" IS '无技能分配比例';

--erp 修改生产订单明细表名称 --卢致灵
COMMENT ON TABLE "erp"."u_erp_prod_order_detail" IS '生产订单明细表';