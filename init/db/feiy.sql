
ALTER TABLE "erp"."u_settle_pool"  ADD COLUMN "collection_plan_strategy" VARCHAR(50) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN erp.u_settle_pool.collection_plan_strategy IS '收款计划策略';

ALTER TABLE "erp"."u_settle_pool"  ADD COLUMN "invoicing_plan_strategy" VARCHAR(50) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN erp.u_settle_pool.invoicing_plan_strategy IS '开票计划策略';

ALTER TABLE "erp"."u_settle_pool"  ADD COLUMN "sale_refund_plan_strategy" VARCHAR(50) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN erp.u_settle_pool.sale_refund_plan_strategy IS '销售退款计划策略';


ALTER TABLE "erp"."u_account_order_detail"  ADD COLUMN "sale_refund_plan_strategy" VARCHAR(50) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN erp.u_account_order_detail.sale_refund_plan_strategy IS '收款计划策略';

ALTER TABLE "erp"."u_account_order_detail"  ADD COLUMN "invoicing_plan_strategy" VARCHAR(50) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN erp.u_account_order_detail.invoicing_plan_strategy IS '开票计划策略';

ALTER TABLE "erp"."u_account_order_detail"  ADD COLUMN "collection_plan_strategy" VARCHAR(50) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN erp.u_account_order_detail.collection_plan_strategy IS '销售退款计划策略';

ALTER TABLE "erp"."u_invoice_issue"  ADD COLUMN "related_order_code" VARCHAR(255) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_invoice_issue"."related_order_code" IS '关联单号';


-- 发票
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "ready_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "ready_amt" TYPE NUMERIC(18,8);
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "can_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "can_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "pending_total_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "remaining_plan_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_plan" ALTER COLUMN "plan_amt" TYPE NUMERIC(18,6);


ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "remaining_plan_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "remaining_plan_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "ready_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "ready_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "can_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "can_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "incl_tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "excl_tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "incl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "excl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_pending_detail" ALTER COLUMN "tax_rate" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "plan_invoice_total_excl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "total_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "ready_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "ready_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "can_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "can_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "plan_invoice_total_incl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "ready_invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "ready_invoice_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "can_invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "can_invoice_amt" TYPE NUMERIC(18,2);
ALTER TABLE "erp"."u_invoice_plan" ALTER COLUMN "remain_plan_total_incl_tax" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "plan_invoice_amt_incl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "ready_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "ready_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "can_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "can_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "remaining_plan_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "plan_invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "remaining_plan_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "plan_invoice_amt_excl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "incl_tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "excl_tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "ready_invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "can_invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "ready_invoice_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "can_invoice_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "incl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "excl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_plan_detail" ALTER COLUMN "tax_rate" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_apply" ALTER COLUMN "apply_invoice_total_incl_tax" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN apply_invoice_qty TYPE numeric(14,6) USING apply_invoice_qty::numeric;
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "ready_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "ready_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "can_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "can_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "remaining_apply_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "remaining_apply_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "apply_invoice_amt_excl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "plan_invoice_amt_incl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "plan_invoice_amt_excl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "incl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "excl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_apply_detail" ALTER COLUMN "tax_rate" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_issue" ALTER COLUMN "invoice_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_issue" ALTER COLUMN "invoice_tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_issue" ALTER COLUMN "invoice_total_tax" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "remaining_invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "remaining_invoice_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "apply_invoice_amt_incl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "apply_invoice_amt_excl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "incl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "excl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "tax_rate" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "invoice_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "invoice_amt_incl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail" ALTER COLUMN "invoice_amt_excl_tax" TYPE NUMERIC(18,6);

ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "tax_rate" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "incl_tax_unit_price" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "invoice_amt_excl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "invoice_amt_incl_tax" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_invoice_detail_list" ALTER COLUMN "excl_tax_unit_price" TYPE NUMERIC(18,6);
