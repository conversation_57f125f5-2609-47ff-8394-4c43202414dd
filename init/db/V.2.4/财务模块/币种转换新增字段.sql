--ERP-直接出账表-新增字段-刘尚新
ALTER TABLE "erp"."u_out_bill"  ADD COLUMN "local_currency_dict_id" VARCHAR(20) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_out_bill"."local_currency_dict_id" IS '本币币种字典ID';
ALTER TABLE "erp"."u_out_bill"  ADD COLUMN "exchange_rate" NUMERIC(13,4);
COMMENT ON COLUMN "erp"."u_out_bill"."exchange_rate" IS '汇率';
ALTER TABLE "erp"."u_out_bill"  ADD COLUMN "local_currency_out_bill_amount" NUMERIC(18,6);
COMMENT ON COLUMN "erp"."u_out_bill"."local_currency_out_bill_amount" IS '本币出账金额';

--ERP-直接入账表-新增字段-刘尚新
ALTER TABLE "erp"."u_in_bill"  ADD COLUMN "local_currency_dict_id" VARCHAR(20) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_in_bill"."local_currency_dict_id" IS '本币币种字典ID';
ALTER TABLE "erp"."u_in_bill"  ADD COLUMN "exchange_rate" NUMERIC(13,4);
COMMENT ON COLUMN "erp"."u_in_bill"."exchange_rate" IS '汇率';
ALTER TABLE "erp"."u_in_bill"  ADD COLUMN "local_currency_in_bill_amount" VARCHAR(50) COLLATE "pg_catalog"."default" ;
COMMENT ON COLUMN "erp"."u_in_bill"."local_currency_in_bill_amount" IS '本币入账金额';