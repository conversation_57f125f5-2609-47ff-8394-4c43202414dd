--ERP- 客户表 新增字段 -卢致灵
ALTER TABLE "erp"."u_customer"  ADD COLUMN "customer_code" VARCHAR(50) COLLATE "pg_catalog"."default" ;;COMMENT ON COLUMN "erp"."u_customer"."customer_code" IS '客户编码';
ALTER TABLE "erp"."u_customer_address" ALTER COLUMN "is_default_address" TYPE int4 USING "is_default_address"::int4;

--erp 新增客户地址表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_customer_address";
CREATE TABLE "erp"."u_customer_address" (
                                            "customer_address_id" int8 NOT NULL,
                                            "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                            "created_dt" timestamp(6),
                                            "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                            "updated_dt" timestamp(6),
                                            "tenant_id" int8,
                                            "contact_address" varchar(50) COLLATE "pg_catalog"."default",
                                            "is_default_address" int4,
                                            "remark" varchar(50) COLLATE "pg_catalog"."default",
                                            "customer_id" int8,
                                            "created_id" int8
)
;
COMMENT ON COLUMN "erp"."u_customer_address"."customer_address_id" IS '主键';
COMMENT ON COLUMN "erp"."u_customer_address"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_customer_address"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_customer_address"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_customer_address"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_customer_address"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_customer_address"."contact_address" IS '联系人地址';
COMMENT ON COLUMN "erp"."u_customer_address"."is_default_address" IS '是否默认地址';
COMMENT ON COLUMN "erp"."u_customer_address"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_customer_address"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_customer_address"."created_id" IS '创建人ID';
COMMENT ON TABLE "erp"."u_customer_address" IS '客户地址表';

-- ----------------------------
-- Primary Key structure for table u_customer_address
-- ----------------------------
ALTER TABLE "erp"."u_customer_address" ADD CONSTRAINT "u_customer_address_pkey" PRIMARY KEY ("customer_address_id");

--erp 新增客户联系人表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_customer_contact";
CREATE TABLE "erp"."u_customer_contact" (
                                            "customer_contact_id" int8 NOT NULL,
                                            "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                            "created_dt" timestamp(6),
                                            "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                            "updated_dt" timestamp(6),
                                            "tenant_id" int8,
                                            "contact_id" varchar(50) COLLATE "pg_catalog"."default",
                                            "contact_name" varchar(50) COLLATE "pg_catalog"."default",
                                            "contact_phone" varchar(50) COLLATE "pg_catalog"."default",
                                            "contact_email" varchar(50) COLLATE "pg_catalog"."default",
                                            "contact_position_dict_id" varchar(50) COLLATE "pg_catalog"."default",
                                            "is_default_contact" varchar(50) COLLATE "pg_catalog"."default",
                                            "customer_id" int8,
                                            "created_id" int8
)
;
COMMENT ON COLUMN "erp"."u_customer_contact"."customer_contact_id" IS '主键';
COMMENT ON COLUMN "erp"."u_customer_contact"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_customer_contact"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_customer_contact"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_customer_contact"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_customer_contact"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_customer_contact"."contact_id" IS '外借联系人ID';
COMMENT ON COLUMN "erp"."u_customer_contact"."contact_name" IS '联系人名称';
COMMENT ON COLUMN "erp"."u_customer_contact"."contact_phone" IS '联系人电话';
COMMENT ON COLUMN "erp"."u_customer_contact"."contact_email" IS '联系人邮箱';
COMMENT ON COLUMN "erp"."u_customer_contact"."contact_position_dict_id" IS '联系人职务字典Id';
COMMENT ON COLUMN "erp"."u_customer_contact"."is_default_contact" IS '是否默认联系人';
COMMENT ON COLUMN "erp"."u_customer_contact"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_customer_contact"."created_id" IS '创建人ID';
COMMENT ON TABLE "erp"."u_customer_contact" IS '客户联系人表';

-- ----------------------------
-- Primary Key structure for table u_customer_contact
-- ----------------------------
ALTER TABLE "erp"."u_customer_contact" ADD CONSTRAINT "u_customer_contact_pkey" PRIMARY KEY ("customer_contact_id");

--erp 新增抽检策略表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_material_sampling_strategy";
CREATE TABLE "erp"."u_material_sampling_strategy" (
                                                      "strategy_id" int8 NOT NULL,
                                                      "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                      "created_dt" timestamp(6),
                                                      "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                      "updated_dt" timestamp(6),
                                                      "tenant_id" int8,
                                                      "created_id" int8,
                                                      "sort" numeric,
                                                      "check_interval" numeric(14,6),
                                                      "check_qty" numeric(14,6),
                                                      "allow_defect_qty" numeric(14,6),
                                                      "check_ratio" numeric(14,6),
                                                      "allow_defect_ratio" numeric(14,6),
                                                      "material_fk_id" int8
)
;
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."strategy_id" IS '主键';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."sort" IS '排序';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."check_interval" IS '抽检区间';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."check_qty" IS '抽检数量';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."allow_defect_qty" IS '允许缺陷数';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."check_ratio" IS '抽检比例';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."allow_defect_ratio" IS '允许缺陷数比例';
COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."material_fk_id" IS '物料外键';
COMMENT ON TABLE "erp"."u_material_sampling_strategy" IS '抽检策略';

-- ----------------------------
-- Primary Key structure for table u_material_sampling_strategy
-- ----------------------------
ALTER TABLE "erp"."u_material_sampling_strategy" ADD CONSTRAINT "u_material_sampling_strategy_pkey" PRIMARY KEY ("strategy_id");

--erp 新增客户中间表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_cutomer_rel";
CREATE TABLE "erp"."u_cutomer_rel" (
                                       "customer_rel_id" int8 NOT NULL,
                                       "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                       "created_dt" timestamp(6),
                                       "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                       "updated_dt" timestamp(6),
                                       "tenant_id" int8,
                                       "created_id" int8,
                                       "customer_id" int8,
                                       "related_order_id" int8
)
;
COMMENT ON COLUMN "erp"."u_cutomer_rel"."customer_rel_id" IS '主键';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_cutomer_rel"."related_order_id" IS '关联单ID';
COMMENT ON TABLE "erp"."u_cutomer_rel" IS '客户中间表';
COMMENT ON TABLE "erp"."u_cutomer_rel" IS '客户引用表';

-- ----------------------------
-- Primary Key structure for table u_cutomer_rel
-- ----------------------------
ALTER TABLE "erp"."u_cutomer_rel" ADD CONSTRAINT "u_cutomer_rel_pkey" PRIMARY KEY ("customer_rel_id");

--erp 商品价格表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_material_price";
CREATE TABLE "erp"."u_material_price" (
                                          "material_price_id" int8 NOT NULL,
                                          "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                          "created_dt" timestamp(6),
                                          "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                          "updated_dt" timestamp(6),
                                          "tenant_id" int8,
                                          "created_id" int8,
                                          "remark" varchar(255) COLLATE "pg_catalog"."default",
                                          "customer_order_price" numeric(14,6),
                                          "material_id" int8,
                                          "related_order_id" int8
)
;
COMMENT ON COLUMN "erp"."u_material_price"."material_price_id" IS '主键';
COMMENT ON COLUMN "erp"."u_material_price"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_price"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_price"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_price"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_price"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_price"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_material_price"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_material_price"."customer_order_price" IS '客户订货价';
COMMENT ON COLUMN "erp"."u_material_price"."material_id" IS '物料ID';
COMMENT ON COLUMN "erp"."u_material_price"."related_order_id" IS '关联单ID';
COMMENT ON TABLE "erp"."u_material_price" IS '商品价格表';

-- ----------------------------
-- Primary Key structure for table u_material_price
-- ----------------------------
ALTER TABLE "erp"."u_material_price" ADD CONSTRAINT "u_material_price_pkey" PRIMARY KEY ("material_price_id");

--erp 新增工作中心引用表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_work_center_rel";
CREATE TABLE "erp"."u_work_center_rel" (
                                           "center_rel_id" int8 NOT NULL,
                                           "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                           "created_dt" timestamp(6),
                                           "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                           "updated_dt" timestamp(6),
                                           "tenant_id" int8,
                                           "row_no" numeric,
                                           "work_center_id" int8,
                                           "remark" varchar(255) COLLATE "pg_catalog"."default",
                                           "created_id" int8,
                                           "related_order_id" int8
)
;
COMMENT ON COLUMN "erp"."u_work_center_rel"."center_rel_id" IS '主键';
COMMENT ON COLUMN "erp"."u_work_center_rel"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_work_center_rel"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_work_center_rel"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_work_center_rel"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_work_center_rel"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_work_center_rel"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_work_center_rel"."work_center_id" IS '工作中心ID';
COMMENT ON COLUMN "erp"."u_work_center_rel"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_work_center_rel"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_work_center_rel"."related_order_id" IS '关联单ID';
COMMENT ON TABLE "erp"."u_work_center_rel" IS '工作中心引用表';

-- ----------------------------
-- Primary Key structure for table u_work_center_rel
-- ----------------------------
ALTER TABLE "erp"."u_work_center_rel" ADD CONSTRAINT "u_work_center_rel_pkey" PRIMARY KEY ("center_rel_id");

--erp 更新客户编码/名称 -卢致灵
UPDATE "erp".u_customer SET customer_code = (base_info ->> 'customerCode'),customer_name = (base_info ->> 'customerName')
WHERE base_info IS NOT NULL;

--erp 更新客户联系人表字段名称 -卢致灵
ALTER TABLE "erp"."u_customer_contact" RENAME COLUMN "customer_id" TO "related_order_id";
COMMENT ON COLUMN "erp"."u_customer_contact"."related_order_id" IS '关联单据id';

--erp 更新客户联系人表 新增"业务类型字段" -卢致灵
ALTER TABLE "erp"."u_customer_contact" ADD COLUMN "biz_type" int4;
COMMENT ON COLUMN "erp"."u_customer_contact"."biz_type" IS '[''客户'',''供应商'']';

--erp 更新客户地址表 -卢致灵
ALTER TABLE "erp"."u_customer_address" RENAME COLUMN "customer_id" TO "related_order_id";
COMMENT ON COLUMN "erp"."u_customer_address"."related_order_id" IS '关联单据id';

--erp 更新客户地址表 新增"业务类型字段" -卢致灵
ALTER TABLE "erp"."u_customer_address" ADD COLUMN "biz_type" int4;
COMMENT ON COLUMN "erp"."u_customer_address"."biz_type" IS '[''客户'',''供应商'']';

--erp 工作中心表 新增"关联人员"字段 -卢致灵
ALTER TABLE "erp"."u_work_center"ADD COLUMN "person_ids" int8[];
COMMENT ON COLUMN "erp"."u_work_center"."person_ids" IS '关联人员';

--erp 物料引用表 新增"行号"字段 -卢致灵
ALTER TABLE "erp"."u_material_price" RENAME TO "u_material_rel";
ALTER TABLE "erp"."u_material_rel" ADD COLUMN "row_no" int4;
COMMENT ON COLUMN "erp"."u_material_rel"."row_no" IS '行号';
COMMENT ON TABLE "erp"."u_material_rel" IS '物料引用表';

--erp 物料引用表 新增"物料编码"字段 -卢致灵
ALTER TABLE "erp"."u_material_rel" ADD COLUMN "material_code" varchar(60);
COMMENT ON COLUMN "erp"."u_material_rel"."material_code" IS '物料编码';

--erp 成本归集单 新增"计提周期单位"字段 -卢致灵
ALTER TABLE "erp"."u_cost_aggre"
ADD COLUMN "period_unit" int4,
ADD COLUMN "period_count" int4;

--erp 成本归集单 修改"计提周期单位"字段类型 -卢致灵
ALTER TABLE "erp"."u_cost_aggre" ALTER COLUMN "period_unit" TYPE int4 USING "period_unit"::int4;
ALTER TABLE "erp"."u_cost_aggre" ALTER COLUMN "period_count" TYPE int4 USING "period_count"::int4;


--erp 新增薪酬项目归集表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_salary_item";
CREATE TABLE "erp"."u_salary_item" (
                                       "salary_item_id" int8 NOT NULL,
                                       "created_id" int8,
                                       "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                       "created_dt" timestamp(6),
                                       "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                       "updated_dt" timestamp(6),
                                       "tenant_id" int8,
                                       "related_order_id" int8,
                                       "biz_type" int4,
                                       "item_id" int8,
                                       "type" int4
)
;
COMMENT ON COLUMN "erp"."u_salary_item"."salary_item_id" IS '主键';
COMMENT ON COLUMN "erp"."u_salary_item"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_salary_item"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_salary_item"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_salary_item"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_salary_item"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_salary_item"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_salary_item"."related_order_id" IS '关联单ID';
COMMENT ON COLUMN "erp"."u_salary_item"."biz_type" IS '业务类型';
COMMENT ON COLUMN "erp"."u_salary_item"."item_id" IS '项目';
COMMENT ON COLUMN "erp"."u_salary_item"."type" IS '类型';
COMMENT ON TABLE "erp"."u_salary_item" IS '薪酬项目归集';

-- ----------------------------
-- Primary Key structure for table u_salary_item
-- ----------------------------
ALTER TABLE "erp"."u_salary_item" ADD CONSTRAINT "u_salary_item_pkey" PRIMARY KEY ("salary_item_id");

--erp 新增工资单归集承担对象表 -卢致灵
DROP TABLE IF EXISTS "erp"."u_salary_aggre_obj";
CREATE TABLE "erp"."u_salary_aggre_obj" (
                                            "aggre_obj_id" int8 NOT NULL,
                                            "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                            "created_dt" timestamp(6),
                                            "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                            "updated_dt" timestamp(6),
                                            "tenant_id" int8,
                                            "biz_type" numeric,
                                            "item_id" varchar(50) COLLATE "pg_catalog"."default",
                                            "item_name" varchar(50) COLLATE "pg_catalog"."default",
                                            "undertake_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                            "undertake_ratio" numeric(14,6),
                                            "related_order_id" int8,
                                            "created_id" int8
)
;
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."aggre_obj_id" IS '主键';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."biz_type" IS '业务类型';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."item_id" IS '项目';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."item_name" IS '项目名称';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."undertake_org_id" IS '承担对象ID';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."undertake_ratio" IS '承担比例';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."related_order_id" IS '关联单据id';
COMMENT ON COLUMN "erp"."u_salary_aggre_obj"."created_id" IS '创建人ID';
COMMENT ON TABLE "erp"."u_salary_aggre_obj" IS '工资单归集承担对象';

-- ----------------------------
-- Primary Key structure for table u_salary_aggre_obj
-- ----------------------------
ALTER TABLE "erp"."u_salary_aggre_obj" ADD CONSTRAINT "u_salary_aggre_obj_pkey" PRIMARY KEY ("aggre_obj_id");

-- ERP 修改spu表字段精度 -卢致灵
ALTER TABLE "erp"."u_spu"
ALTER COLUMN "standard_weight" TYPE numeric(18,6),
ALTER COLUMN "billing_weight" TYPE numeric(18,6),
ALTER COLUMN "lightest_weight" TYPE numeric(18,6),
ALTER COLUMN "heaviest_weight" TYPE numeric(18,6);

-- ERP 修改物料表字段精度 -卢致灵
ALTER TABLE "erp"."u_material"
ALTER COLUMN "standard_weight" TYPE numeric(18,6),
ALTER COLUMN "billing_weight" TYPE numeric(18,6),
ALTER COLUMN "lightest_weight" TYPE numeric(18,6),
ALTER COLUMN "heaviest_weight" TYPE numeric(18,6);

-- ERP 修改客户价格方案 字段类型 -卢致灵
ALTER TABLE "erp"."u_customer_price_plan"
ALTER COLUMN "start_date" TYPE date USING "start_date"::date,
ALTER COLUMN "end_date" TYPE date USING "end_date"::date;

-- ERP 新增"设备台账引用表" -卢致灵
DROP TABLE IF EXISTS "erp"."u_device_rel";
CREATE TABLE "erp"."u_device_rel" (
                                      "device_rel_id" int8 NOT NULL,
                                      "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                      "created_dt" timestamp(6),
                                      "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                      "updated_dt" timestamp(6),
                                      "tenant_id" int8,
                                      "created_id" int8,
                                      "device_id" int8,
                                      "device_code" varchar(50) COLLATE "pg_catalog"."default",
                                      "related_order_id" int8,
                                      "remark" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_device_rel"."device_rel_id" IS '主键';
COMMENT ON COLUMN "erp"."u_device_rel"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_device_rel"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_device_rel"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_device_rel"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_device_rel"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_device_rel"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_device_rel"."device_id" IS '设备id';
COMMENT ON COLUMN "erp"."u_device_rel"."device_code" IS '设备编码';
COMMENT ON COLUMN "erp"."u_device_rel"."related_order_id" IS '关联单ID';
COMMENT ON COLUMN "erp"."u_device_rel"."remark" IS '备注';
COMMENT ON TABLE "erp"."u_device_rel" IS '设备台账引用表';

-- ----------------------------
-- Primary Key structure for table u_device_rel
-- ----------------------------
ALTER TABLE "erp"."u_device_rel" ADD CONSTRAINT "u_device_rel_pkey" PRIMARY KEY ("device_rel_id");

--erp 工资单归集单修改承担对象非必填 -卢致灵
ALTER TABLE "erp"."u_salary_aggre_ploy" ALTER COLUMN "undertake_org" DROP NOT NULL;
ALTER TABLE "erp"."u_salary_aggre_ploy" ALTER COLUMN "salary_item_collection" DROP NOT NULL;

--erp 客户表 新增"审批人/审批时间"-卢致灵
ALTER TABLE "erp"."u_customer"
ADD COLUMN "approved_by" varchar(50) COLLATE "pg_catalog"."default",
ADD COLUMN "approved_dt" timestamp(6);
COMMENT ON COLUMN "erp"."u_customer"."approved_by" IS '审批人';
COMMENT ON COLUMN "erp"."u_customer"."approved_dt" IS '审批时间';


--ERP- 客户表 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_customer" DROP COLUMN "base_info";
ALTER TABLE "erp"."u_customer" DROP COLUMN "finance_info";
ALTER TABLE "erp"."u_customer" DROP COLUMN "contact_list";
ALTER TABLE "erp"."u_customer" DROP COLUMN "address_list";
--ERP- 物料表 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_material" DROP COLUMN "sampling_strategy";
--ERP- 物料表 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_spu" DROP COLUMN "sampling_strategy";

--ERP- 客户价格方案表 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_customer_price_plan" DROP COLUMN "customer_info";
ALTER TABLE "erp"."u_customer_price_plan" DROP COLUMN "material_info";
ALTER TABLE "erp"."u_customer_price_plan" DROP COLUMN "base_info";

--ERP- 供应商表 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_supplier" DROP COLUMN "alipay_payment_qr_code";
ALTER TABLE "erp"."u_supplier" DROP COLUMN "wechat_payment_qr_code";
ALTER TABLE "erp"."u_supplier" DROP COLUMN "contact_list";
ALTER TABLE "erp"."u_supplier" DROP COLUMN "address_list";

--ERP- 模具台账 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_mold" DROP COLUMN "mold_param";

--ERP- 工序管理 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_process" DROP COLUMN "work_center_info";

--ERP- 工作中心 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_work_center" DROP COLUMN "device_info";
ALTER TABLE "erp"."u_work_center" DROP COLUMN "person_info";

--ERP- 工艺路线 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_flow_process" DROP COLUMN "material_info";

--ERP- 成本归集单 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_cost_aggre" DROP COLUMN "provision_period";
ALTER TABLE "erp"."u_cost_aggre" DROP COLUMN "undertake_obj_list";

--ERP- 工资归集单 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_salary_aggre" DROP COLUMN "undertake_org";
ALTER TABLE "erp"."u_salary_aggre" DROP COLUMN "salary_item_collection";

--ERP- 工资归集策略 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_salary_aggre_ploy" DROP COLUMN "undertake_org";
ALTER TABLE "erp"."u_salary_aggre_ploy" DROP COLUMN "salary_item_collection";

--ERP- spu 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_spu" DROP COLUMN "unit_list";
ALTER TABLE "erp"."u_spu" DROP COLUMN "spec_attribute_list";

--ERP- spu 删除jsonb字段 -卢致灵
ALTER TABLE "erp"."u_material" DROP COLUMN "unit_list";
ALTER TABLE "erp"."u_material" DROP COLUMN "spec_attribute_list";

--erp 客户表 新增"物料单位表"-卢致灵
DROP TABLE IF EXISTS "erp"."u_material_unit";
CREATE TABLE "erp"."u_material_unit" (
                                         "material_unit_id" int8 NOT NULL,
                                         "created_id" int8,
                                         "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "created_dt" timestamp(6),
                                         "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "updated_dt" timestamp(6),
                                         "tenant_id" int8,
                                         "related_order_id" int8,
                                         "left_unit" varchar(50) COLLATE "pg_catalog"."default",
                                         "left_unit_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                         "right_val" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_material_unit"."material_unit_id" IS '主键';
COMMENT ON COLUMN "erp"."u_material_unit"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_material_unit"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_unit"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_unit"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_unit"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_unit"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_unit"."related_order_id" IS '关联单ID';
COMMENT ON COLUMN "erp"."u_material_unit"."left_unit" IS '左单位编码';
COMMENT ON COLUMN "erp"."u_material_unit"."left_unit_dict_name" IS '左单位名称';
COMMENT ON COLUMN "erp"."u_material_unit"."right_val" IS '右单位值';
COMMENT ON TABLE "erp"."u_material_unit" IS '物料单位表';

-- ----------------------------
-- Primary Key structure for table u_material_unit
-- ----------------------------
ALTER TABLE "erp"."u_material_unit" ADD CONSTRAINT "u_material_unit_pkey" PRIMARY KEY ("material_unit_id");

--erp 客户表 新增"物料规格属性表"-卢致灵
DROP TABLE IF EXISTS "erp"."u_material_spec";
CREATE TABLE "erp"."u_material_spec" (
                                         "material_spec_id" int8 NOT NULL,
                                         "created_id" int8,
                                         "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "created_dt" timestamp(6),
                                         "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "updated_dt" timestamp(6),
                                         "tenant_id" int8,
                                         "related_order_id" int8,
                                         "dict_category_id" int8,
                                         "spec_name" varchar(50) COLLATE "pg_catalog"."default",
                                         "spec_value" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_material_spec"."material_spec_id" IS '主键';
COMMENT ON COLUMN "erp"."u_material_spec"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_material_spec"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_spec"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_spec"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_spec"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_spec"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_spec"."related_order_id" IS '关联单ID';
COMMENT ON COLUMN "erp"."u_material_spec"."dict_category_id" IS '字典分类';
COMMENT ON COLUMN "erp"."u_material_spec"."spec_name" IS '规格名';
COMMENT ON COLUMN "erp"."u_material_spec"."spec_value" IS '规格值';
COMMENT ON TABLE "erp"."u_material_spec" IS '物料规格属性表';

-- ----------------------------
-- Primary Key structure for table u_material_spec
-- ----------------------------
ALTER TABLE "erp"."u_material_spec" ADD CONSTRAINT "u_material_spec_pkey" PRIMARY KEY ("material_spec_id");

-- erp 删除客户表 字段 -卢致灵
ALTER TABLE "erp"."u_customer"
DROP COLUMN "bank_name",
DROP COLUMN "bank_account";
COMMENT ON COLUMN "erp"."u_customer_price_plan"."import_index" IS '重要指数';

--erp 客户表 新增审批人/审批时间 -卢致灵
ALTER TABLE "erp"."u_customer"
ADD COLUMN "approved_by" varchar(50) COLLATE "pg_catalog"."default",
ADD COLUMN "approved_dt" timestamp(6);

--erp spu表 新增审批人/审批时间 -卢致灵
ALTER TABLE "erp"."u_spu"
ADD COLUMN "approved_by" varchar(50) COLLATE "pg_catalog"."default",
ADD COLUMN "approved_dt" timestamp(6);

--erp 物料表 新增审批人/审批时间 -卢致灵
ALTER TABLE "erp"."u_material"
ADD COLUMN "approved_by" varchar(50) COLLATE "pg_catalog"."default",
ADD COLUMN "approved_dt" timestamp(6);
COMMENT ON COLUMN "erp"."u_material"."approved_by" IS '审批人';
COMMENT ON COLUMN "erp"."u_material"."approved_dt" IS '审批时间';

--erp 客户表 修改"结算日期"字段类型 -卢致灵
ALTER TABLE "erp"."u_customer"
DROP COLUMN "settlement_date",
ADD COLUMN "settlement_date" int4;
COMMENT ON COLUMN "erp"."u_customer"."settlement_date" IS '结算日期';