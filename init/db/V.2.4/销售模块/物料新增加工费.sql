--ERP- 物料表 新增"加工费"字段 -卢致灵
ALTER TABLE "erp"."u_spu" ADD COLUMN "processing_fee" numeric(18,6) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_spu"."processing_fee" IS '加工费';

--ERP- spu表 新增"加工费"字段 -卢致灵
ALTER TABLE "erp"."u_material" ADD COLUMN "processing_fee" numeric(18,6) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_material"."processing_fee" IS '加工费';

--ERP-销售报价明细表 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_sale_quote_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);

--ERP-销售换货明细 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_sale_exchange_detail" ALTER COLUMN "outboundable_qty" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_sale_exchange_detail" ALTER COLUMN "can_exchange_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_exchange_detail" ALTER COLUMN "inboundable_qty" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_sale_exchange_detail" ALTER COLUMN "outbounded_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_exchange_detail" ALTER COLUMN "inbounded_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_exchange_detail" ALTER COLUMN "exchange_qty" TYPE NUMERIC(18,6);

--ERP-销售订单明细表 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "exchange_inbound_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "exchange_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "invoiced_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "planed_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "reconciled_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "feed_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "purchased_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "returned_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "notified_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "plan_able_demand_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "planned_demand_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "out_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_erp_sale_order_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);

--ERP-销售扣费明细单 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_sale_deduction_detail" ALTER COLUMN "deduction_qty" TYPE NUMERIC(14,6);

--ERP-销售退货单明细 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_sale_return_detail" ALTER COLUMN "opered_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_return_detail" ALTER COLUMN "operable_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_return_detail" ALTER COLUMN "return_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_return_detail" ALTER COLUMN "receipted_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_return_detail" ALTER COLUMN "inbounded_qty" TYPE NUMERIC(14,6);

--ERP-销售发货通知明细单 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_sale_ship_notice_detail" ALTER COLUMN "outbounded_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_ship_notice_detail" ALTER COLUMN "outboundable_qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_ship_notice_detail" ALTER COLUMN "notice_qty" TYPE NUMERIC(14,6);

--ERP-销售订单变更明细表 修改字段精度 -卢致灵
ALTER TABLE "erp"."u_sale_chang_detail" ALTER COLUMN "excl_tax_amt" TYPE NUMERIC(18,6);
ALTER TABLE "erp"."u_sale_quote_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);
ALTER TABLE "erp"."u_sale_chang_detail" ALTER COLUMN "qty" TYPE NUMERIC(14,6);