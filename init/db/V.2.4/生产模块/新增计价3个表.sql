-- 新增维度表，王忠
DROP TABLE IF EXISTS "erp"."u_calculate_price_dimension";
CREATE TABLE "erp"."u_calculate_price_dimension" (
                                                     "calculate_price_dimension_id" int8 NOT NULL,
                                                     "calculate_price_dimension_name" varchar(50) COLLATE "pg_catalog"."default",
                                                     "calculate_price_dimension_code" varchar(50) COLLATE "pg_catalog"."default",
                                                     "parent_id" int8,
                                                     "is_enable" int2,
                                                     "created_id" int8,
                                                     "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                     "created_dt" timestamp(6),
                                                     "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                     "updated_dt" timestamp(6),
                                                     "tenant_id" int8,
                                                     "biz_type" int2,
                                                     "type" int2
)
;

COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."calculate_price_dimension_id" IS '计价维度id';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."calculate_price_dimension_name" IS '维度名称';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."calculate_price_dimension_code" IS '维度编码';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."parent_id" IS '父级id';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."is_enable" IS '是否启用';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."biz_type" IS '业务类型，0：计价，1：计时';
COMMENT ON COLUMN "erp"."u_calculate_price_dimension"."type" IS '0：计价，1：计时';
COMMENT ON TABLE "erp"."u_calculate_price_dimension" IS '计价维度表';

ALTER TABLE "erp"."u_calculate_price_dimension" ADD CONSTRAINT "u_calculate_price_dimension_pkey" PRIMARY KEY ("calculate_price_dimension_id");

-- 新增计价单表，王忠
DROP TABLE IF EXISTS "erp"."u_calculate_price_order";
CREATE TABLE "erp"."u_calculate_price_order" (
                                                 "calculate_price_order_id" int8 NOT NULL,
                                                 "calculate_price_order_code" varchar(50) COLLATE "pg_catalog"."default",
                                                 "material_id" int8,
                                                 "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                 "process_id" int8,
                                                 "process_code" varchar(60) COLLATE "pg_catalog"."default",
                                                 "calculate_time_amt" numeric(18,3),
                                                 "ok_calculate_unit_amt" numeric(18,3),
                                                 "ng_calculate_unit_amt" numeric(18,3),
                                                 "standard_working_hours" numeric(18,6),
                                                 "remark" varchar(50) COLLATE "pg_catalog"."default",
                                                 "data_status" int4 DEFAULT 0,
                                                 "director_id" int8,
                                                 "director_org_id" varchar(64) COLLATE "pg_catalog"."default",
                                                 "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                 "created_dt" timestamp(6),
                                                 "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                 "updated_dt" timestamp(6),
                                                 "approved_by" varchar(50) COLLATE "pg_catalog"."default",
                                                 "approved_dt" timestamp(6),
                                                 "tenant_id" int8,
                                                 "created_id" int8,
                                                 "version" int4 DEFAULT 0,
                                                 "form_dt" timestamp(6),
                                                 "biz_type" int2,
                                                 "calculate_price_dimension_ids" varchar(300) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_calculate_price_order"."calculate_price_order_id" IS '计价单id';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."calculate_price_order_code" IS '计价单编码';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."process_id" IS '工序id';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."process_code" IS '工序编码';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."calculate_time_amt" IS '计时单价';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."ok_calculate_unit_amt" IS '良品计件单价';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."ng_calculate_unit_amt" IS '不良品计件单价';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."standard_working_hours" IS '标准工时';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."approved_by" IS '审批人';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."approved_dt" IS '审批时间';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."version" IS '版本号';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."biz_type" IS '业务类型';
COMMENT ON COLUMN "erp"."u_calculate_price_order"."calculate_price_dimension_ids" IS '维度id，验重用的';
COMMENT ON TABLE "erp"."u_calculate_price_order" IS '计价单';

ALTER TABLE "erp"."u_calculate_price_order" ADD CONSTRAINT "u_calculate_price_order_pkey" PRIMARY KEY ("calculate_price_order_id");


-- 新增计价单规则表，王忠
DROP TABLE IF EXISTS "erp"."u_calculate_price_order_rule";
CREATE TABLE "erp"."u_calculate_price_order_rule" (
                                                      "id" int8 NOT NULL,
                                                      "calculate_price_order_id" int8,
                                                      "calculate_price_dimension_id" int8,
                                                      "created_id" int8,
                                                      "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                      "created_dt" timestamp(6),
                                                      "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                      "updated_dt" timestamp(6),
                                                      "tenant_id" int8,
                                                      "parent_id" int8
)
;
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."id" IS '主键';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."calculate_price_order_id" IS '计价单id';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."calculate_price_dimension_id" IS '计价维度id';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."created_id" IS '创建人id';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_calculate_price_order_rule"."parent_id" IS '计价维度父级id';
COMMENT ON TABLE "erp"."u_calculate_price_order_rule" IS '计价单规则表';

ALTER TABLE "erp"."u_calculate_price_order_rule" ADD CONSTRAINT "u_calculate_price_order_rule_pkey" PRIMARY KEY ("id");

