--ERP-报工记录表 -王忠
ALTER TABLE "erp"."u_reported_work" ADD COLUMN "work_time_qty" NUMERIC(14,2) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_reported_work"."work_time_qty" IS '工时';

ALTER TABLE "erp"."u_reported_work" ADD COLUMN "ok_amt" NUMERIC(14,3) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_reported_work"."ok_amt" IS '良品价';

ALTER TABLE "erp"."u_reported_work" ADD COLUMN "ng_amt" NUMERIC(14,3) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_reported_work"."ng_amt" IS '不良品价';

ALTER TABLE "erp"."u_reported_work" ADD COLUMN "unit_amt" NUMERIC(14,3) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_reported_work"."unit_amt" IS '单价';

ALTER TABLE "erp"."u_reported_work" ADD COLUMN "total_amt" NUMERIC(14,3) DEFAULT 0;
COMMENT ON COLUMN "erp"."u_reported_work"."total_amt" IS '总价';
