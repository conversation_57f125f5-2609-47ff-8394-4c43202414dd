--erp-新增是否使用可替代物料字段和替换物料信息 -邱雪云
ALTER TABLE "erp"."u_material_analysis_result"  ADD COLUMN "use_substitute_material" INT2;COMMENT ON COLUMN "erp"."u_material_analysis_result"."use_substitute_material" IS '是否使用可替代物料';
ALTER TABLE "erp"."u_material_analysis_result"  ADD COLUMN "substitute_material_info" JSONB;COMMENT ON COLUMN "erp"."u_material_analysis_result"."substitute_material_info" IS '可替代物料的信息';

                                                                                                    -- ----------------------------
-- 表结构： u_material_analysis_statistics_report
-- ----------------------------
CREATE TABLE IF NOT EXISTS "erp"."u_material_analysis_statistics_report"  (
                                                                              "id" INT8 NOT NULL,
                                                                              "created_id" INT8,
                                                                              "created_by" VARCHAR(50) COLLATE "pg_catalog"."default" ,
    "created_dt" TIMESTAMP,
    "updated_by" VARCHAR(50) COLLATE "pg_catalog"."default" ,
    "updated_dt" TIMESTAMP,
    "tenant_id" INT8,
    "source_order_id" INT8,
    "biz_type" INT2,
    "form_type" INT2,
    "form_code" VARCHAR(50) COLLATE "pg_catalog"."default" ,
    "issued_qty" NUMERIC(18,6),
    CONSTRAINT "u_material_analysis_statistics_report_pkey" PRIMARY KEY ("id")
    );
-- ----------------------------
-- 字段注释： u_material_analysis_statistics_report
-- ----------------------------
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."id" IS '主键';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."source_order_id" IS '来源单id';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."biz_type" IS '业务类型';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."form_type" IS '单据类型';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."form_code" IS '单据编码';
COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."issued_qty" IS '下发数量';
COMMENT ON TABLE "erp"."u_material_analysis_statistics_report" IS '物料分析下发统计表';



ALTER TABLE "erp"."u_material_analysis_statistics_report"  ADD COLUMN "source_order_code" VARCHAR(50) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."source_order_code" IS '来源单号';
ALTER TABLE "erp"."u_material_analysis_statistics_report"  ADD COLUMN "is_current_generated" INT2;COMMENT ON COLUMN "erp"."u_material_analysis_statistics_report"."is_current_generated" IS '是否当前生成';