
-- 替代物料表，王忠
DROP TABLE IF EXISTS "erp"."u_material_alternative";
CREATE TABLE "erp"."u_material_alternative" (
                                                "alternative_material_id" int8 NOT NULL,
                                                "alternative_material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                "material_id" int8,
                                                "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                "alternative_strategy" varchar(255) COLLATE "pg_catalog"."default",
                                                "remark" varchar(255) COLLATE "pg_catalog"."default",
                                                "data_status" int4,
                                                "approved_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "approved_dt" timestamp(6),
                                                "form_dt" timestamp(6),
                                                "director_id" int8,
                                                "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                                "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "created_dt" timestamp(6),
                                                "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "updated_dt" timestamp(6),
                                                "tenant_id" int8,
                                                "created_id" int8
)
;
COMMENT ON COLUMN "erp"."u_material_alternative"."alternative_material_id" IS '替代物料id';
COMMENT ON COLUMN "erp"."u_material_alternative"."alternative_material_code" IS '替代物料编码';
COMMENT ON COLUMN "erp"."u_material_alternative"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_material_alternative"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_material_alternative"."alternative_strategy" IS '替代策略';
COMMENT ON COLUMN "erp"."u_material_alternative"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_material_alternative"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_material_alternative"."approved_by" IS '审核人';
COMMENT ON COLUMN "erp"."u_material_alternative"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "erp"."u_material_alternative"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_material_alternative"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_material_alternative"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_material_alternative"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_alternative"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_alternative"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_alternative"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_alternative"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_alternative"."created_id" IS '创建人id';
COMMENT ON TABLE "erp"."u_material_alternative" IS '替代物料表';

ALTER TABLE "erp"."u_material_alternative" ADD CONSTRAINT "u_material_alternative_pkey" PRIMARY KEY ("alternative_material_id");

-- 替代物料明细表，王忠
DROP TABLE IF EXISTS "erp"."u_material_alternative_detail";
CREATE TABLE "erp"."u_material_alternative_detail" (
                                                       "id" int8 NOT NULL,
                                                       "alternative_material_id" int8,
                                                       "alternative_material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                       "material_id" int8,
                                                       "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                       "row_no" int4,
                                                       "main_qty" numeric(18,6),
                                                       "alternative_qty" numeric(18,6),
                                                       "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                       "created_dt" timestamp(6),
                                                       "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                       "updated_dt" timestamp(6),
                                                       "tenant_id" int8,
                                                       "created_id" int8
)
;
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."id" IS '主键';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."alternative_material_id" IS '替代物料id';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."alternative_material_code" IS '替代物料编码';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."main_qty" IS '主数量';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."alternative_qty" IS '替代数量';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_material_alternative_detail"."created_id" IS '创建人id';
COMMENT ON TABLE "erp"."u_material_alternative_detail" IS '替代物料明细表';

ALTER TABLE "erp"."u_material_alternative_detail" ADD CONSTRAINT "u_material_alternative_detail_pkey" PRIMARY KEY ("id");