ALTER TABLE "erp"."u_erp_sale_order_detail"  ADD COLUMN "material_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_erp_sale_order_detail"."material_code" IS '物料编码';
ALTER TABLE "erp"."u_sale_quote_detail"  ADD COLUMN "material_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_sale_quote_detail"."material_code" IS '物料编码';
ALTER TABLE "erp"."u_sale_chang_detail"  ADD COLUMN "material_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_sale_chang_detail"."material_code" IS '物料编码';
ALTER TABLE "erp"."u_customer_contact"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_customer_contact"."row_no" IS '行号';
ALTER TABLE "erp"."u_customer_address"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_customer_address"."row_no" IS '行号';
ALTER TABLE "erp"."u_bank_account_info"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_bank_account_info"."row_no" IS '行号';
ALTER TABLE "erp"."u_material_spec"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_material_spec"."row_no" IS '行号';
ALTER TABLE "erp"."u_material"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_material"."row_no" IS '行号';
ALTER TABLE "erp"."u_material_unit"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_material_unit"."row_no" IS '行号';
ALTER TABLE "erp"."u_material_sampling_strategy"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_material_sampling_strategy"."row_no" IS '行号';
ALTER TABLE "erp"."u_cutomer_rel"  ADD COLUMN "row_no" INT4;COMMENT ON COLUMN "erp"."u_cutomer_rel"."row_no" IS '行号';
ALTER TABLE "erp"."u_purchase_receipt_notice_detail"  ADD COLUMN "receipt_notice_code" VARCHAR(50) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_purchase_receipt_notice_detail"."receipt_notice_code" IS '收货通知单号';
ALTER TABLE "erp"."u_purchase_change_detail"  ADD COLUMN "purchase_change_code" VARCHAR(50) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_purchase_change_detail"."purchase_change_code" IS '采购订单变更单号';
ALTER TABLE "erp"."u_cutomer_rel"  ADD COLUMN "related_order_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_cutomer_rel"."related_order_code" IS '关联单据号';
ALTER TABLE "erp"."u_customer_contact"  ADD COLUMN "related_order_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_customer_contact"."related_order_code" IS '关联单据号';
ALTER TABLE "erp"."u_customer_address"  ADD COLUMN "related_order_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_customer_address"."related_order_code" IS '关联单据号';
ALTER TABLE "erp"."u_bank_account_info"  ADD COLUMN "related_order_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_bank_account_info"."related_order_code" IS '关联单据号';
ALTER TABLE "erp"."u_material_unit"  ADD COLUMN "related_order_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_material_unit"."related_order_code" IS '关联单据号';
ALTER TABLE "erp"."u_material_spec"  ADD COLUMN "related_order_code" VARCHAR(20) COLLATE "pg_catalog"."default" ;COMMENT ON COLUMN "erp"."u_material_spec"."related_order_code" IS '关联单据号';