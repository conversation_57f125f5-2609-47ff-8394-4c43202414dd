--字典库
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136422, 'RDL104', '销售报价单类型', 1, 104, 1, 0, 'luzhiling', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136425, 'RDL105', '销售订单类型', 1, 105, 1, 0, 'luzhiling', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1857251300132212739, 'RDL1050001', '报价销售', 1, 105, 1, 0, 'lzl123/卢某人1', '2024-11-15 10:36:30.67', 'lzl123/卢某人1', '2024-11-15 10:36:30.67', 1850016287628136425, 1, 50, 1, '', 1, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136423, 'RDL106', '销售退货单类型', 1, 106, 1, 0, 'luzhiling', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136424, 'RDL107', '退款条件', 1, 107, 1, 0, 'luzhiling', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136525, 'RDL108', '客户类型', 1, 108, 1, 0, 'luzhiling', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136526, 'RDL109', '客户行业', 1, 109, 1, 0, 'luzhiling', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1850016287628136419, 'RDL103', '方案类型', 1, 103, 1, 0, 'test', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

ALTER TABLE u_erp_sale_order_detail
ADD COLUMN planned_demand_qty NUMERIC(16, 2),
ADD COLUMN plan_able_demand_qty NUMERIC(16, 2);
COMMENT ON COLUMN u_erp_sale_order_detail.planned_demand_qty IS '已规划需求数量';
COMMENT ON COLUMN u_erp_sale_order_detail.plan_able_demand_qty IS '可规划需求数量';