/*
 Navicat Premium Data Transfer

 Source Server         : erp-test
 Source Server Type    : PostgreSQL
 Source Server Version : 140010 (140010)
 Source Host           : **************:54321
 Source Catalog        : mgs-jgzy
 Source Schema         : erp

 Target Server Type    : PostgreSQL
 Target Server Version : 140010 (140010)
 File Encoding         : 65001

 Date: 14/01/2025 14:21:33
*/


-- ----------------------------
-- Table structure for u_invoice_apply
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_apply";
CREATE TABLE "erp"."u_invoice_apply" (
                                         "invoice_apply_id" int8 NOT NULL,
                                         "invoice_plan_detail_id" int8,
                                         "invoice_plan_id" int8,
                                         "billing_direction" int2,
                                         "invoice_status" int2,
                                         "invoice_apply_no" varchar(50) COLLATE "pg_catalog"."default",
                                         "customer_id" int8,
                                         "invoice_direction" int2,
                                         "currency_dict_id" int8,
                                         "approved_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "apply_invoice_total_incl_tax" numeric(12,2),
                                         "approved_dt" timestamp(6),
                                         "data_status" int2,
                                         "expected_invoice_date" date,
                                         "form_dt" timestamp(0),
                                         "source_form_type" int2,
                                         "invoice_type_dict_id" int8,
                                         "remark" text COLLATE "pg_catalog"."default",
                                         "buyer_company" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_identify_no" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_address" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_tel" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_bank" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_bank_account" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_company" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_identify_no" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_address" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_tel" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_bank" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_bank_account" varchar(50) COLLATE "pg_catalog"."default",
                                         "director_id" int8,
                                         "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                         "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "created_dt" timestamp(6),
                                         "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "updated_dt" timestamp(6),
                                         "tenant_id" int8,
                                         "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                         "ready_qty" numeric(12,2),
                                         "ready_amt" numeric(12,2),
                                         "can_qty" numeric(12,2),
                                         "can_amt" numeric(12,2),
                                         "apply_invoice_total_excl_tax" numeric(12,2),
                                         "total_tax" numeric(12,2),
                                         "apply_invoice_qty" numeric(12,2),
                                         "invoice_strategy" varchar(20) COLLATE "pg_catalog"."default",
                                         "collection_plan_strategy" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_apply_id" IS '开票申请id';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_plan_detail_id" IS '开票计划明细id';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_plan_id" IS '开票计划id';
COMMENT ON COLUMN "erp"."u_invoice_apply"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_status" IS '开票状态';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_apply_no" IS '开票申请单号';
COMMENT ON COLUMN "erp"."u_invoice_apply"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_direction" IS '发票方向';
COMMENT ON COLUMN "erp"."u_invoice_apply"."currency_dict_id" IS '币种id';
COMMENT ON COLUMN "erp"."u_invoice_apply"."approved_by" IS '审核人';
COMMENT ON COLUMN "erp"."u_invoice_apply"."apply_invoice_total_incl_tax" IS '申请开票总额（含税）';
COMMENT ON COLUMN "erp"."u_invoice_apply"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "erp"."u_invoice_apply"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_invoice_apply"."expected_invoice_date" IS '应开日期';
COMMENT ON COLUMN "erp"."u_invoice_apply"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_invoice_apply"."source_form_type" IS '来源单据类型';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_apply"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_invoice_apply"."buyer_company" IS '购方名称';
COMMENT ON COLUMN "erp"."u_invoice_apply"."buyer_identify_no" IS '购方纳税人识别号';
COMMENT ON COLUMN "erp"."u_invoice_apply"."buyer_address" IS '购方地址';
COMMENT ON COLUMN "erp"."u_invoice_apply"."buyer_tel" IS '购方电话';
COMMENT ON COLUMN "erp"."u_invoice_apply"."buyer_bank" IS '购方开户银行';
COMMENT ON COLUMN "erp"."u_invoice_apply"."buyer_bank_account" IS '购方银行账户';
COMMENT ON COLUMN "erp"."u_invoice_apply"."cus_company" IS '消方名称';
COMMENT ON COLUMN "erp"."u_invoice_apply"."cus_identify_no" IS '消方纳税人识别号';
COMMENT ON COLUMN "erp"."u_invoice_apply"."cus_address" IS '消方地址';
COMMENT ON COLUMN "erp"."u_invoice_apply"."cus_tel" IS '消方电话';
COMMENT ON COLUMN "erp"."u_invoice_apply"."cus_bank" IS '消方开户银行';
COMMENT ON COLUMN "erp"."u_invoice_apply"."cus_bank_account" IS '消方银行账户';
COMMENT ON COLUMN "erp"."u_invoice_apply"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_apply"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_apply"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_apply"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_apply"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_apply"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_apply"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_apply"."ready_qty" IS '已申请数量';
COMMENT ON COLUMN "erp"."u_invoice_apply"."ready_amt" IS '已申请金额';
COMMENT ON COLUMN "erp"."u_invoice_apply"."can_qty" IS '可申请数量';
COMMENT ON COLUMN "erp"."u_invoice_apply"."can_amt" IS '可申请金额';
COMMENT ON COLUMN "erp"."u_invoice_apply"."apply_invoice_total_excl_tax" IS '申请开票总额（不含税）';
COMMENT ON COLUMN "erp"."u_invoice_apply"."total_tax" IS '税额';
COMMENT ON COLUMN "erp"."u_invoice_apply"."apply_invoice_qty" IS '申请开票数量';
COMMENT ON COLUMN "erp"."u_invoice_apply"."invoice_strategy" IS '开票计划策略';
COMMENT ON COLUMN "erp"."u_invoice_apply"."collection_plan_strategy" IS '收款计划策略--取子集';
COMMENT ON TABLE "erp"."u_invoice_apply" IS '开票申请';

-- ----------------------------
-- Table structure for u_invoice_apply_detail
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_apply_detail";
CREATE TABLE "erp"."u_invoice_apply_detail" (
                                                "invoice_apply_detail_id" int8 NOT NULL,
                                                "billing_direction" int2,
                                                "invoice_apply_id" int8,
                                                "invoice_plan_detail_id" int8,
                                                "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                "source_order_code" varchar(30) COLLATE "pg_catalog"."default",
                                                "main_unit_dict_id" int8,
                                                "remaining_apply_qty" numeric(12,2),
                                                "remaining_apply_amt" numeric(12,2),
                                                "source_line_number" int8,
                                                "row_no" int8,
                                                "plan_invoice_amt_incl_tax" numeric(12,2),
                                                "plan_invoice_amt_excl_tax" numeric(12,2),
                                                "qty" numeric(12,2),
                                                "director_id" int8,
                                                "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                                "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "created_dt" timestamp(6),
                                                "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "updated_dt" timestamp(6),
                                                "tenant_id" int8,
                                                "incl_tax_unit_price" numeric(12,2),
                                                "excl_tax_unit_price" numeric(12,2),
                                                "invoice_type_dict_id" int8,
                                                "tax_rate" numeric(12,2),
                                                "apply_invoice_qty" varchar(12) COLLATE "pg_catalog"."default",
                                                "apply_invoice_amt_incl_tax" varchar(12) COLLATE "pg_catalog"."default",
                                                "material_id" int8,
                                                "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                                "ready_qty" numeric(12,2),
                                                "ready_amt" numeric(12,2),
                                                "can_qty" numeric(12,2),
                                                "can_amt" numeric(12,2),
                                                "tax_amt" numeric(12,2),
                                                "apply_invoice_amt_excl_tax" numeric(12,2)
)
;
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."invoice_apply_detail_id" IS '开票申请明细id';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."invoice_apply_id" IS '开票申请id';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."invoice_plan_detail_id" IS '开票计划明细id';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."source_order_code" IS '来源单号';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."main_unit_dict_id" IS '基本单位ID';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."remaining_apply_qty" IS '剩余申请数量';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."remaining_apply_amt" IS '剩余申请金额';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."source_line_number" IS '来源单行号';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."plan_invoice_amt_incl_tax" IS '计划开票金额（含税）';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."plan_invoice_amt_excl_tax" IS '计划开票金额（不含税）';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."qty" IS '数量';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."incl_tax_unit_price" IS '单价（含税）';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."excl_tax_unit_price" IS '单价(不含税）';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."tax_rate" IS '税率';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."apply_invoice_qty" IS '申请开票数量';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."apply_invoice_amt_incl_tax" IS '申请开票金额(含税)';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."ready_qty" IS '已申请数量';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."ready_amt" IS '已申请金额';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."can_qty" IS '可申请数量';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."can_amt" IS '可申请金额';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."tax_amt" IS '税额';
COMMENT ON COLUMN "erp"."u_invoice_apply_detail"."apply_invoice_amt_excl_tax" IS '申请开票金额（不含税）';
COMMENT ON TABLE "erp"."u_invoice_apply_detail" IS '开票申请明细';

-- ----------------------------
-- Table structure for u_invoice_config
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_config";
CREATE TABLE "erp"."u_invoice_config" (
                                          "invoice_config_id" int8 NOT NULL,
                                          "company_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                          "address" varchar(255) COLLATE "pg_catalog"."default",
                                          "taxpayer_identify_no" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                          "phone_number" varchar(255) COLLATE "pg_catalog"."default",
                                          "bank_name" varchar(255) COLLATE "pg_catalog"."default",
                                          "bank_account" varchar(255) COLLATE "pg_catalog"."default",
                                          "data_status" int2,
                                          "approved_by" varchar(255) COLLATE "pg_catalog"."default",
                                          "approved_dt" timestamp(6),
                                          "form_dt" timestamp(0),
                                          "is_void" int2,
                                          "director_id" int8,
                                          "director_org_id" varchar(255) COLLATE "pg_catalog"."default",
                                          "created_by" varchar(255) COLLATE "pg_catalog"."default",
                                          "created_dt" timestamp(6),
                                          "updated_by" varchar(255) COLLATE "pg_catalog"."default",
                                          "updated_dt" timestamp(6),
                                          "tenant_id" int8
)
;
COMMENT ON COLUMN "erp"."u_invoice_config"."invoice_config_id" IS '开票主体配置id';
COMMENT ON COLUMN "erp"."u_invoice_config"."company_name" IS '企业名称';
COMMENT ON COLUMN "erp"."u_invoice_config"."address" IS '地址';
COMMENT ON COLUMN "erp"."u_invoice_config"."taxpayer_identify_no" IS '纳税人识别号';
COMMENT ON COLUMN "erp"."u_invoice_config"."phone_number" IS '电话';
COMMENT ON COLUMN "erp"."u_invoice_config"."bank_name" IS '开户银行';
COMMENT ON COLUMN "erp"."u_invoice_config"."bank_account" IS '银行账户';
COMMENT ON COLUMN "erp"."u_invoice_config"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_invoice_config"."approved_by" IS '审核人';
COMMENT ON COLUMN "erp"."u_invoice_config"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "erp"."u_invoice_config"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_invoice_config"."is_void" IS '是否作废';
COMMENT ON COLUMN "erp"."u_invoice_config"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_config"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_config"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_config"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_config"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_config"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_config"."tenant_id" IS '租户ID';
COMMENT ON TABLE "erp"."u_invoice_config" IS '开票主体配置';

-- ----------------------------
-- Table structure for u_invoice_detail
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_detail";
CREATE TABLE "erp"."u_invoice_detail" (
                                          "invoice_detail_id" int8 NOT NULL,
                                          "billing_direction" int2,
                                          "invoice_id" int8,
                                          "invoice_apply_detail_id" int8,
                                          "source_order_code" varchar(50) COLLATE "pg_catalog"."default",
                                          "source_line_number" int2,
                                          "row_no" int2,
                                          "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                          "main_unit_dict_id" int8,
                                          "remaining_invoice_qty" numeric(12,2),
                                          "remaining_invoice_amt" numeric(12,2),
                                          "apply_invoice_amt_incl_tax" numeric(12,2),
                                          "apply_invoice_amt_excl_tax" numeric(12,2),
                                          "qty" numeric(12,2),
                                          "director_id" int8,
                                          "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                          "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                          "created_dt" timestamp(6),
                                          "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                          "updated_dt" timestamp(6),
                                          "tenant_id" int8,
                                          "incl_tax_unit_price" numeric(12,2),
                                          "excl_tax_unit_price" numeric(12,2),
                                          "invoice_type_dict_id" int8,
                                          "tax_rate" numeric(12,2),
                                          "invoice_qty" numeric(12,2),
                                          "invoice_amt_incl_tax" numeric(12,2),
                                          "invoice_amt_excl_tax" numeric(12,2),
                                          "material_id" int8,
                                          "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                          "tax_amt" numeric(12,2)
)
;
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_detail_id" IS '实开发票明细id';
COMMENT ON COLUMN "erp"."u_invoice_detail"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_id" IS '实开发票id';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_apply_detail_id" IS '开票申请明细id';
COMMENT ON COLUMN "erp"."u_invoice_detail"."source_order_code" IS '来源单号';
COMMENT ON COLUMN "erp"."u_invoice_detail"."source_line_number" IS '来源单行号';
COMMENT ON COLUMN "erp"."u_invoice_detail"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_invoice_detail"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_invoice_detail"."main_unit_dict_id" IS '基本单位ID';
COMMENT ON COLUMN "erp"."u_invoice_detail"."remaining_invoice_qty" IS '剩余开票数量';
COMMENT ON COLUMN "erp"."u_invoice_detail"."remaining_invoice_amt" IS '剩余开票金额';
COMMENT ON COLUMN "erp"."u_invoice_detail"."apply_invoice_amt_incl_tax" IS '申请开票金额（含税）';
COMMENT ON COLUMN "erp"."u_invoice_detail"."apply_invoice_amt_excl_tax" IS '申请开票金额（不含税）';
COMMENT ON COLUMN "erp"."u_invoice_detail"."qty" IS '数量';
COMMENT ON COLUMN "erp"."u_invoice_detail"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_detail"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_detail"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_detail"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_detail"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_detail"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_detail"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_detail"."incl_tax_unit_price" IS '单价（含税）';
COMMENT ON COLUMN "erp"."u_invoice_detail"."excl_tax_unit_price" IS '单价(不含税）';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_detail"."tax_rate" IS '税率';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_qty" IS '本次开票数量';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_amt_incl_tax" IS '本次开票金额（含税）';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_amt_excl_tax" IS '本次开票金额（不含税）';
COMMENT ON COLUMN "erp"."u_invoice_detail"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_invoice_detail"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_detail"."tax_amt" IS '税额';
COMMENT ON TABLE "erp"."u_invoice_detail" IS '实开发票明细';

-- ----------------------------
-- Table structure for u_invoice_issue
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_issue";
CREATE TABLE "erp"."u_invoice_issue" (
                                         "invoice_id" int8 NOT NULL,
                                         "billing_direction" int2,
                                         "invoice_apply_id" int8,
                                         "invoice_apply_detail_id" int8,
                                         "invoice_issue_status" int2,
                                         "invoice_code" varchar(50) COLLATE "pg_catalog"."default",
                                         "invoice_direction" int2,
                                         "currency_dict_id" int8,
                                         "invoice_type_dict_id" int8,
                                         "invoice_amt" numeric(12,2),
                                         "invoice_tax_amt" numeric(12,2),
                                         "invoice_total_tax" numeric(12,2),
                                         "actual_invoice_date" date,
                                         "form_dt" timestamp(6),
                                         "approved_dt" timestamp(6),
                                         "source_form_type" int4,
                                         "approved_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "data_status" int2,
                                         "payee" varchar(50) COLLATE "pg_catalog"."default",
                                         "payee_phone" varchar(50) COLLATE "pg_catalog"."default",
                                         "payee_address" varchar(50) COLLATE "pg_catalog"."default",
                                         "tel_phone" varchar(50) COLLATE "pg_catalog"."default",
                                         "postal_code" varchar(50) COLLATE "pg_catalog"."default",
                                         "remark" varchar(250) COLLATE "pg_catalog"."default",
                                         "buyer_company" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_identify_no" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_address" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_tel" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_bank" varchar(50) COLLATE "pg_catalog"."default",
                                         "buyer_bank_account" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_company" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_identify_no" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_address" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_tel" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_bank" varchar(50) COLLATE "pg_catalog"."default",
                                         "cus_bank_account" varchar(50) COLLATE "pg_catalog"."default",
                                         "director_id" int8,
                                         "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                         "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "created_dt" timestamp(6),
                                         "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                         "updated_dt" timestamp(6),
                                         "tenant_id" int8,
                                         "red_invoice_id" int8,
                                         "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                         "customer_id" int8,
                                         "invoice_strategy" varchar(20) COLLATE "pg_catalog"."default",
                                         "collection_plan_strategy" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_id" IS '实开发票id';
COMMENT ON COLUMN "erp"."u_invoice_issue"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_apply_id" IS '开票申请id';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_apply_detail_id" IS '开票申请明细id';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_issue_status" IS '发票状态';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_code" IS '发票代码';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_direction" IS '发票方向';
COMMENT ON COLUMN "erp"."u_invoice_issue"."currency_dict_id" IS '币种id';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_amt" IS '开票金额';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_tax_amt" IS '开票税额';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_total_tax" IS '开票价税合计';
COMMENT ON COLUMN "erp"."u_invoice_issue"."actual_invoice_date" IS '实际开票日期';
COMMENT ON COLUMN "erp"."u_invoice_issue"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_invoice_issue"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "erp"."u_invoice_issue"."source_form_type" IS '来源单据类型';
COMMENT ON COLUMN "erp"."u_invoice_issue"."approved_by" IS '审核人';
COMMENT ON COLUMN "erp"."u_invoice_issue"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_invoice_issue"."payee" IS '收票人';
COMMENT ON COLUMN "erp"."u_invoice_issue"."payee_phone" IS '收票人手机号';
COMMENT ON COLUMN "erp"."u_invoice_issue"."payee_address" IS '收票地址';
COMMENT ON COLUMN "erp"."u_invoice_issue"."tel_phone" IS '固定电话';
COMMENT ON COLUMN "erp"."u_invoice_issue"."postal_code" IS '邮政编码';
COMMENT ON COLUMN "erp"."u_invoice_issue"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_invoice_issue"."buyer_company" IS '购方名称';
COMMENT ON COLUMN "erp"."u_invoice_issue"."buyer_identify_no" IS '购方纳税人识别号';
COMMENT ON COLUMN "erp"."u_invoice_issue"."buyer_address" IS '购方地址';
COMMENT ON COLUMN "erp"."u_invoice_issue"."buyer_tel" IS '购方电话';
COMMENT ON COLUMN "erp"."u_invoice_issue"."buyer_bank" IS '购方开户银行';
COMMENT ON COLUMN "erp"."u_invoice_issue"."buyer_bank_account" IS '购方银行账户';
COMMENT ON COLUMN "erp"."u_invoice_issue"."cus_company" IS '消方名称';
COMMENT ON COLUMN "erp"."u_invoice_issue"."cus_identify_no" IS '消方纳税人识别号';
COMMENT ON COLUMN "erp"."u_invoice_issue"."cus_address" IS '消方地址';
COMMENT ON COLUMN "erp"."u_invoice_issue"."cus_tel" IS '消方电话';
COMMENT ON COLUMN "erp"."u_invoice_issue"."cus_bank" IS '消方开户银行';
COMMENT ON COLUMN "erp"."u_invoice_issue"."cus_bank_account" IS '消方银行账户';
COMMENT ON COLUMN "erp"."u_invoice_issue"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_issue"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_issue"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_issue"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_issue"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_issue"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_issue"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_issue"."red_invoice_id" IS '红冲发票上级id';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_issue"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_invoice_issue"."invoice_strategy" IS '开票计划策略';
COMMENT ON COLUMN "erp"."u_invoice_issue"."collection_plan_strategy" IS '收款计划策略--取子集';
COMMENT ON TABLE "erp"."u_invoice_issue" IS '实开发票';

-- ----------------------------
-- Table structure for u_invoice_pending_detail
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_pending_detail";
CREATE TABLE "erp"."u_invoice_pending_detail" (
                                                  "invoice_pending_plan_detail_id" int8 NOT NULL,
                                                  "billing_direction" int2,
                                                  "invoice_pending_plan_id" int8,
                                                  "row_no" int8,
                                                  "source_order_code" varchar(50) COLLATE "pg_catalog"."default",
                                                  "source_line_number" int4,
                                                  "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                                  "main_unit_dict_id" int8,
                                                  "remaining_plan_qty" numeric(12,2),
                                                  "remaining_plan_amt" numeric(12,2),
                                                  "incl_tax_amt" numeric(12,2),
                                                  "excl_tax_amt" numeric(12,2),
                                                  "qty" numeric(12,2),
                                                  "director_id" varchar(50) COLLATE "pg_catalog"."default",
                                                  "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                                  "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                  "created_dt" timestamp(6),
                                                  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                  "updated_dt" timestamp(6),
                                                  "tenant_id" int8,
                                                  "incl_tax_unit_price" numeric(12,2),
                                                  "excl_tax_unit_price" numeric(12,2),
                                                  "invoice_type_dict_id" int8,
                                                  "tax_rate" numeric(12,2),
                                                  "material_id" int8,
                                                  "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                                  "ready_qty" numeric(12,2),
                                                  "ready_amt" numeric(12,2),
                                                  "can_qty" numeric(12,2),
                                                  "can_amt" numeric(12,2),
                                                  "tax_amt" numeric(12,2),
                                                  "up_id" int8
)
;
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."invoice_pending_plan_detail_id" IS '待开票计划明细id';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."invoice_pending_plan_id" IS '待开票计划id';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."source_order_code" IS '来源单号';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."source_line_number" IS '来源单行号';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."main_unit_dict_id" IS '基本单位id';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."remaining_plan_qty" IS '剩余可计划数量';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."remaining_plan_amt" IS '剩余可计划金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."incl_tax_amt" IS '行金额(含税)';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."excl_tax_amt" IS '行金额(不含税)';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."qty" IS '数量';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."incl_tax_unit_price" IS '单价（含税）';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."excl_tax_unit_price" IS '单价(不含税）';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."tax_rate" IS '税率';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."ready_qty" IS '已申请数量';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."ready_amt" IS '已申请金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."can_qty" IS '可申请数量';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."can_amt" IS '可申请金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."tax_amt" IS '税额';
COMMENT ON COLUMN "erp"."u_invoice_pending_detail"."up_id" IS '上游明细id';
COMMENT ON TABLE "erp"."u_invoice_pending_detail" IS '待开票计划明细';

-- ----------------------------
-- Table structure for u_invoice_pending_plan
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_pending_plan";
CREATE TABLE "erp"."u_invoice_pending_plan" (
                                                "invoice_pending_plan_id" int8 NOT NULL,
                                                "billing_direction" int2,
                                                "invoice_plan_status" int2,
                                                "form_dt" timestamp(6),
                                                "customer_id" int8,
                                                "currency_dict_id" int8,
                                                "source_order_code" varchar(50) COLLATE "pg_catalog"."default",
                                                "source_form_type" int2,
                                                "pending_total_amt" numeric(12,2),
                                                "remaining_plan_amt" numeric(12,2),
                                                "plan_amt" numeric(12,2),
                                                "source_line_number" int8,
                                                "plan_invoice_date" date,
                                                "director_id" int8,
                                                "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                                "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "created_dt" timestamp(6),
                                                "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                "updated_dt" timestamp(6),
                                                "tenant_id" int8,
                                                "remark" varchar(255) COLLATE "pg_catalog"."default",
                                                "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                                "ready_qty" numeric(12,2),
                                                "ready_amt" numeric(12,2),
                                                "can_qty" numeric(12,2),
                                                "can_amt" numeric(12,2),
                                                "up_id" int8,
                                                "invoice_strategy" varchar(20) COLLATE "pg_catalog"."default",
                                                "collection_plan_strategy" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."invoice_pending_plan_id" IS '待开票计划id';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."invoice_plan_status" IS '开票计划状态';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."currency_dict_id" IS '币种id';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."source_order_code" IS '来源单号';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."source_form_type" IS '来源单据类型';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."pending_total_amt" IS '待开票总金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."remaining_plan_amt" IS '剩余可计划金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."plan_amt" IS '已计划金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."source_line_number" IS '来源单行号';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."plan_invoice_date" IS '计划开票日期';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."ready_qty" IS '已申请数量';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."ready_amt" IS '已申请金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."can_qty" IS '可申请数量';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."can_amt" IS '可申请金额';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."up_id" IS '上游主表id';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."invoice_strategy" IS '开票计划策略';
COMMENT ON COLUMN "erp"."u_invoice_pending_plan"."collection_plan_strategy" IS '收款计划策略--取子集';
COMMENT ON TABLE "erp"."u_invoice_pending_plan" IS '待开票计划';

-- ----------------------------
-- Table structure for u_invoice_plan
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_plan";
CREATE TABLE "erp"."u_invoice_plan" (
                                        "invoice_plan_id" int8 NOT NULL,
                                        "billing_direction" int2,
                                        "invoice_apply_status" int2,
                                        "invoice_pending_plan_id" int8,
                                        "invoice_pending_plan_detail_id" int8,
                                        "invoice_type_dict_id" int8,
                                        "invoice_plan_no" varchar(50) COLLATE "pg_catalog"."default",
                                        "source_order_code" varchar(50) COLLATE "pg_catalog"."default",
                                        "currency_dict_id" int8,
                                        "customer_id" int8,
                                        "plan_invoice_total_incl_tax" numeric(12,2),
                                        "plan_invoice_date" date,
                                        "source_form_type" int2,
                                        "form_dt" timestamp(6),
                                        "data_status" int2,
                                        "approved_by" varchar(50) COLLATE "pg_catalog"."default",
                                        "approved_dt" timestamp(6),
                                        "remark" text COLLATE "pg_catalog"."default",
                                        "director_id" int8,
                                        "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                        "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                        "created_dt" timestamp(6),
                                        "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                        "updated_dt" timestamp(6),
                                        "tenant_id" int8,
                                        "remain_plan_total_incl_tax" numeric(12,2),
                                        "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                        "plan_invoice_total_excl_tax" numeric(12,2),
                                        "total_tax" numeric(12,2),
                                        "ready_qty" numeric(12,2),
                                        "ready_amt" numeric(12,2),
                                        "can_qty" numeric(12,2),
                                        "can_amt" numeric(12,2),
                                        "invoice_strategy" varchar(20) COLLATE "pg_catalog"."default",
                                        "collection_plan_strategy" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_plan_id" IS '开票计划id';
COMMENT ON COLUMN "erp"."u_invoice_plan"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_apply_status" IS '开票申请状态';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_pending_plan_id" IS '待开票计划id';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_pending_plan_detail_id" IS '待开票计划明细id';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_plan_no" IS '开票计划单号';
COMMENT ON COLUMN "erp"."u_invoice_plan"."source_order_code" IS '来源单号';
COMMENT ON COLUMN "erp"."u_invoice_plan"."currency_dict_id" IS '币种id';
COMMENT ON COLUMN "erp"."u_invoice_plan"."customer_id" IS '客户id';
COMMENT ON COLUMN "erp"."u_invoice_plan"."plan_invoice_total_incl_tax" IS '计划开票总额（含税）';
COMMENT ON COLUMN "erp"."u_invoice_plan"."plan_invoice_date" IS '计划开票日期';
COMMENT ON COLUMN "erp"."u_invoice_plan"."source_form_type" IS '来源单据类型';
COMMENT ON COLUMN "erp"."u_invoice_plan"."form_dt" IS '单据时间';
COMMENT ON COLUMN "erp"."u_invoice_plan"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_invoice_plan"."approved_by" IS '审核人';
COMMENT ON COLUMN "erp"."u_invoice_plan"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "erp"."u_invoice_plan"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_invoice_plan"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_plan"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_plan"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_plan"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_plan"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_plan"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_plan"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_plan"."remain_plan_total_incl_tax" IS '剩余计划总额';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_plan"."plan_invoice_total_excl_tax" IS '计划开票总额（不含税）';
COMMENT ON COLUMN "erp"."u_invoice_plan"."total_tax" IS '总税额';
COMMENT ON COLUMN "erp"."u_invoice_plan"."ready_qty" IS '已申请数量';
COMMENT ON COLUMN "erp"."u_invoice_plan"."ready_amt" IS '已申请金额';
COMMENT ON COLUMN "erp"."u_invoice_plan"."can_qty" IS '可申请数量';
COMMENT ON COLUMN "erp"."u_invoice_plan"."can_amt" IS '可申请金额';
COMMENT ON COLUMN "erp"."u_invoice_plan"."invoice_strategy" IS '开票计划策略';
COMMENT ON COLUMN "erp"."u_invoice_plan"."collection_plan_strategy" IS '收款计划策略--取子集';
COMMENT ON TABLE "erp"."u_invoice_plan" IS '开票计划';

-- ----------------------------
-- Table structure for u_invoice_plan_detail
-- ----------------------------
DROP TABLE IF EXISTS "erp"."u_invoice_plan_detail";
CREATE TABLE "erp"."u_invoice_plan_detail" (
                                               "invoice_plan_detail_id" int8 NOT NULL,
                                               "billing_direction" int2,
                                               "invoice_plan_id" int8,
                                               "invoice_pending_plan_detail_id" int8,
                                               "row_no" int8,
                                               "source_order_code" varchar(50) COLLATE "pg_catalog"."default",
                                               "source_line_number" int4,
                                               "material_code" varchar(50) COLLATE "pg_catalog"."default",
                                               "main_unit_dict_id" int8,
                                               "plan_invoice_qty" numeric(12,2),
                                               "plan_invoice_amt_incl_tax" numeric(12,2),
                                               "incl_tax_amt" numeric(12,2),
                                               "excl_tax_amt" numeric(12,2),
                                               "qty" numeric(12,2),
                                               "director_id" int8,
                                               "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                               "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                               "created_dt" timestamp(6),
                                               "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                               "updated_dt" timestamp(6),
                                               "tenant_id" int8,
                                               "incl_tax_unit_price" numeric(12,2),
                                               "excl_tax_unit_price" numeric(12,2),
                                               "invoice_type_dict_id" int8,
                                               "tax_rate" numeric(12,2),
                                               "material_id" int8,
                                               "invoice_type_dict_name" varchar(50) COLLATE "pg_catalog"."default",
                                               "ready_qty" numeric(12,2),
                                               "ready_amt" numeric(12,2),
                                               "can_qty" numeric(12,2),
                                               "can_amt" numeric(12,2),
                                               "remaining_plan_qty" numeric(12,2),
                                               "remaining_plan_amt" numeric(12,2),
                                               "tax_amt" numeric(12,2),
                                               "plan_invoice_amt_excl_tax" numeric(12,2)
)
;
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."invoice_plan_detail_id" IS '开票计划明细id';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."billing_direction" IS '开票方向';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."invoice_plan_id" IS '开票计划id';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."invoice_pending_plan_detail_id" IS '待开票计划明细id';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."row_no" IS '行号';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."source_order_code" IS '来源单号';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."source_line_number" IS '来源单行号';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."material_code" IS '物料编码';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."main_unit_dict_id" IS '基本单位ID';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."plan_invoice_qty" IS '计划开票数量';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."plan_invoice_amt_incl_tax" IS '计划开票金额（含税）';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."incl_tax_amt" IS '行金额(含税)';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."excl_tax_amt" IS '行金额(不含税)';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."qty" IS '数量';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."incl_tax_unit_price" IS '单价（含税）';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."excl_tax_unit_price" IS '单价(不含税）';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."invoice_type_dict_id" IS '票据类型';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."tax_rate" IS '税率';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."material_id" IS '物料id';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."invoice_type_dict_name" IS '票据类型名称';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."ready_qty" IS '已申请数量';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."ready_amt" IS '已申请金额';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."can_qty" IS '可申请数量';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."can_amt" IS '可申请金额';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."remaining_plan_qty" IS '剩余开票数量';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."remaining_plan_amt" IS '剩余开票金额';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."tax_amt" IS '税额';
COMMENT ON COLUMN "erp"."u_invoice_plan_detail"."plan_invoice_amt_excl_tax" IS '计划开票金额(不含税)';
COMMENT ON TABLE "erp"."u_invoice_plan_detail" IS '开票计划明细';

-- ----------------------------
-- Primary Key structure for table u_invoice_apply
-- ----------------------------
ALTER TABLE "erp"."u_invoice_apply" ADD CONSTRAINT "u_invoice_apply_pkey" PRIMARY KEY ("invoice_apply_id");

-- ----------------------------
-- Primary Key structure for table u_invoice_apply_detail
-- ----------------------------
ALTER TABLE "erp"."u_invoice_apply_detail" ADD CONSTRAINT "u_invoice_apply_detail_pkey" PRIMARY KEY ("invoice_apply_detail_id");

-- ----------------------------
-- Primary Key structure for table u_invoice_config
-- ----------------------------
ALTER TABLE "erp"."u_invoice_config" ADD CONSTRAINT "u_invoice_config_pkey" PRIMARY KEY ("invoice_config_id");

-- ----------------------------
-- Primary Key structure for table u_invoice_detail
-- ----------------------------
ALTER TABLE "erp"."u_invoice_detail" ADD CONSTRAINT "u_invoice_detail_pkey" PRIMARY KEY ("invoice_detail_id");

-- ----------------------------
-- Primary Key structure for table u_invoice_issue
-- ----------------------------
ALTER TABLE "erp"."u_invoice_issue" ADD CONSTRAINT "u_invoice_issue_pkey" PRIMARY KEY ("invoice_id");

-- ----------------------------
-- Indexes structure for table u_invoice_pending_detail
-- ----------------------------
CREATE INDEX "idx_invoice_detail_dirct" ON "erp"."u_invoice_pending_detail" USING btree (
    "billing_direction" "pg_catalog"."int2_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_invoice_detail_plan_id" ON "erp"."u_invoice_pending_detail" USING btree (
    "invoice_pending_plan_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "u_pending_invoicing_detail_pkey" ON "erp"."u_invoice_pending_detail" USING btree (
    "invoice_pending_plan_detail_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table u_invoice_pending_detail
-- ----------------------------
ALTER TABLE "erp"."u_invoice_pending_detail" ADD CONSTRAINT "u_invoice_pending_detail_pkey" PRIMARY KEY ("invoice_pending_plan_detail_id");

-- ----------------------------
-- Indexes structure for table u_invoice_pending_plan
-- ----------------------------
CREATE INDEX "idx_bill_dirct" ON "erp"."u_invoice_pending_plan" USING btree (
    "billing_direction" "pg_catalog"."int2_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table u_invoice_pending_plan
-- ----------------------------
ALTER TABLE "erp"."u_invoice_pending_plan" ADD CONSTRAINT "u_invoice_pending_plan_pkey" PRIMARY KEY ("invoice_pending_plan_id");

-- ----------------------------
-- Indexes structure for table u_invoice_plan
-- ----------------------------
CREATE INDEX "idx_invoice_bill_dirct" ON "erp"."u_invoice_plan" USING btree (
    "billing_direction" "pg_catalog"."int2_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_plan_detai_id" ON "erp"."u_invoice_plan" USING btree (
    "invoice_pending_plan_detail_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_plan_id" ON "erp"."u_invoice_plan" USING btree (
    "invoice_pending_plan_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table u_invoice_plan
-- ----------------------------
ALTER TABLE "erp"."u_invoice_plan" ADD CONSTRAINT "u_invoice_plan_pkey" PRIMARY KEY ("invoice_plan_id");

-- ----------------------------
-- Indexes structure for table u_invoice_plan_detail
-- ----------------------------
CREATE INDEX "idx_invoice_plan_dirct" ON "erp"."u_invoice_plan_detail" USING btree (
    "billing_direction" "pg_catalog"."int2_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_invoice_plan_id" ON "erp"."u_invoice_plan_detail" USING btree (
    "invoice_plan_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_plan_detail_id" ON "erp"."u_invoice_plan_detail" USING btree (
    "invoice_pending_plan_detail_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "u_invoicing_plan_detail_pkey" ON "erp"."u_invoice_plan_detail" USING btree (
    "invoice_plan_detail_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table u_invoice_plan_detail
-- ----------------------------
ALTER TABLE "erp"."u_invoice_plan_detail" ADD CONSTRAINT "u_invoice_plan_detail_pkey" PRIMARY KEY ("invoice_plan_detail_id");
