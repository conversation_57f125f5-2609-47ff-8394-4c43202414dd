INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666680, 'RDL201', '设备状态', 1, 201, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666681, 'RDL202', '模具状态', 1, 202, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666682, 'RDL203', '使用状态', 1, 203, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666683, 'RDL204', '库存状态', 1, 204, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666684, 'RDL205', '设备来源', 1, 205, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666685, 'RDL206', '模具来源', 1, 206, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666686, 'RDL204', '设备等级', 1, 207, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666687, 'RDL205', '工作中心类型', 1, 208, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666688, 'RDL209', '资源类型', 1, 209, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666689, 'RDL210', '领料方式', 1, 210, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666610, 'RDL211', '生产状态', 1, 211, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666611, 'RDL212', '委外订单类型', 1, 212, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666612, 'RDL213', '领料单类型', 1, 213, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666613, 'RDL214', '退料单类型', 1, 214, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666614, 'RDL215', '不良原因分类', 1, 215, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666615, 'RDL216', '严重程度', 1, 216, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666616, 'RDL217', '技能等级', 1, 217, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666617, 'RDL218', '设备类型', 1, 218, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666618, 'RDL219', '模具类型', 1, 219, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666619, 'RDL220', '工序类型', 1, 220, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666620, 'RDL221', '生产订单类型', 1, 221, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666621, 'RDL222', '物料分析类型', 1, 222, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed")
VALUES (1866734402860666622, 'RDL223', '计件方式', 1, 223, 1, 0, 'lzl', '2024-04-09 14:06:33', NULL, NULL, 0, 0, 50, 0, NULL, NULL, NULL);

--规格表
ALTER TABLE "erp"."t_specs" ALTER COLUMN "is_enable" SET DEFAULT 1;
COMMENT ON COLUMN "erp"."t_specs"."category" IS '模块 ["资源","生产"]';
COMMENT ON COLUMN "erp"."t_specs"."dict_category" IS '规格名';

ALTER TABLE "erp"."t_specs" ADD COLUMN "specs_type" int4 DEFAULT 1;
COMMENT ON COLUMN "erp"."t_specs"."specs_type" IS '规格类型';



---------字典值------------------
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743020809072641, 'RDL2010002', '带病运行', 1, 201, 1, 0, 'lzl123/卢某人', '2024-12-11 15:13:13.226', 'lzl123/卢某人', '2024-12-11 15:13:13.226', 1866734402860666680, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866742829594947585, 'RDL2010001', '正常', 1, 201, 1, 0, 'lzl123/卢某人', '2024-12-11 15:12:27.638', 'lzl123/卢某人', '2024-12-11 15:12:27.638', 1866734402860666680, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743050689294338, 'RDL2010003', '故障', 1, 201, 1, 0, 'lzl123/卢某人', '2024-12-11 15:13:20.35', 'lzl123/卢某人', '2024-12-11 15:13:20.35', 1866734402860666680, 1, 0, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743404202012674, 'RDL2030004', '禁用', 1, 203, 1, 0, 'lzl123/卢某人', '2024-12-11 15:14:44.634', 'lzl123/卢某人', '2024-12-11 15:14:44.634', 1866734402860666682, 1, 0, 4, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743379153629185, 'RDL2030003', '出租', 1, 203, 1, 0, 'lzl123/卢某人', '2024-12-11 15:14:38.662', 'lzl123/卢某人', '2024-12-11 15:14:38.662', 1866734402860666682, 1, 0, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743314720731138, 'RDL2030001', '在用', 1, 203, 1, 0, 'lzl123/卢某人', '2024-12-11 15:14:23.3', 'lzl123/卢某人', '2024-12-11 15:14:23.3', 1866734402860666682, 1, 1, 0, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743349873192961, 'RDL2030002', '闲置', 1, 203, 1, 0, 'lzl123/卢某人', '2024-12-11 15:14:31.681', 'lzl123/卢某人', '2024-12-11 15:14:31.681', 1866734402860666682, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743655327576066, 'RDL2060001', '自制', 1, 206, 1, 0, 'lzl123/卢某人', '2024-12-11 15:15:44.508', 'lzl123/卢某人', '2024-12-11 15:15:44.508', 1866734402860666685, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743676852744194, 'RDL2060002', '外购', 1, 206, 1, 0, 'lzl123/卢某人', '2024-12-11 15:15:49.639', 'lzl123/卢某人', '2024-12-11 15:15:49.639', 1866734402860666685, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743613455839234, 'RDL2050003', '借用', 1, 205, 1, 0, 'lzl123/卢某人', '2024-12-11 15:15:34.524', 'lzl123/卢某人', '2024-12-11 15:15:34.524', 1866734402860666684, 1, 0, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743588407455746, 'RDL2050002', '租赁', 1, 205, 1, 0, 'lzl123/卢某人', '2024-12-11 15:15:28.552', 'lzl123/卢某人', '2024-12-11 15:15:28.552', 1866734402860666684, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743547206807553, 'RDL2050001', '自购', 1, 205, 1, 0, 'lzl123/卢某人', '2024-12-11 15:15:18.729', 'lzl123/卢某人', '2024-12-11 15:15:18.729', 1866734402860666684, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866758797100490754, 'RDL2150001', '外观问题', 1, 215, 1, 0, 'lzl123/卢某人', '2024-12-11 16:15:54.588', 'lzl123/卢某人', '2024-12-11 16:15:54.588', 1866734402860666614, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866758844609372162, 'RDL2150002', '性能问题', 1, 215, 1, 0, 'lzl123/卢某人', '2024-12-11 16:16:05.914', 'lzl123/卢某人', '2024-12-11 16:16:05.914', 1866734402860666614, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866758869821333506, 'RDL2150003', '材料问题', 1, 215, 1, 0, 'lzl123/卢某人', '2024-12-11 16:16:11.926', 'lzl123/卢某人', '2024-12-11 16:16:11.926', 1866734402860666614, 1, 0, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866758896685850625, 'RDL2150004', '工艺问题', 1, 215, 1, 0, 'lzl123/卢某人', '2024-12-11 16:16:18.331', 'lzl123/卢某人', '2024-12-11 16:16:18.331', 1866734402860666614, 1, 0, 4, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744304014438402, 'RDL2090001', '设备', 1, 209, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:19.167', 'lzl123/卢某人', '2024-12-11 15:18:19.167', 1866734402860666688, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744319977959425, 'RDL2090002', '员工', 1, 209, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:22.973', 'lzl123/卢某人', '2024-12-11 15:18:22.973', 1866734402860666688, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744245252239361, 'RDL2050003', '返工', 1, 208, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:05.156', 'lzl123/卢某人', '2024-12-11 15:18:05.156', 1866734402860666687, 1, 0, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744182308319234, 'RDL2050001', '生产', 1, 208, 1, 0, 'lzl123/卢某人', '2024-12-11 15:17:50.149', 'lzl123/卢某人', '2024-12-11 15:17:50.149', 1866734402860666687, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744207042129922, 'RDL2050002', '质检', 1, 208, 1, 0, 'lzl123/卢某人', '2024-12-11 15:17:56.046', 'lzl123/卢某人', '2024-12-11 15:17:56.046', 1866734402860666687, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743838849347585, 'RDL2040002', 'B(重要)', 1, 207, 1, 0, 'lzl123/卢某人', '2024-12-11 15:16:28.262', 'lzl123/卢某人', '2024-12-11 15:16:28.262', 1866734402860666686, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743897766735874, 'RDL2040003', 'C(一般)', 1, 207, 1, 0, 'lzl123/卢某人', '2024-12-11 15:16:42.31', 'lzl123/卢某人', '2024-12-11 15:16:42.31', 1866734402860666686, 1, 0, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743788765163522, 'RDL2040001', 'A(关键)', 1, 207, 1, 0, 'lzl123/卢某人', '2024-12-11 15:16:16.322', 'lzl123/卢某人', '2024-12-11 15:16:16.322', 1866734402860666686, 1, 0, 1, '', 1, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743244852015105, 'RDL2020005', '已报废', 1, 202, 1, 0, 'lzl123/卢某人', '2024-12-11 15:14:06.643', 'lzl123/卢某人', '2024-12-11 15:14:06.643', 1866734402860666681, 1, 50, 5, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743114065227777, 'RDL2020001', '正常', 1, 202, 1, 0, 'lzl123/卢某人', '2024-12-11 15:13:35.461', 'lzl123/卢某人', '2024-12-11 15:13:35.461', 1866734402860666681, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743146847907841, 'RDL2020002', '带病运行', 1, 202, 1, 0, 'lzl123/卢某人', '2024-12-11 15:13:43.276', 'lzl123/卢某人', '2024-12-11 15:13:43.276', 1866734402860666681, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743180616249345, 'RDL2020003', '保养中', 1, 202, 1, 0, 'lzl123/卢某人', '2024-12-11 15:13:51.327', 'lzl123/卢某人', '2024-12-11 15:13:51.327', 1866734402860666681, 1, 50, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743203315822594, 'RDL2020004', '维修中', 1, 202, 1, 0, 'lzl123/卢某人', '2024-12-11 15:13:56.74', 'lzl123/卢某人', '2024-12-11 15:13:56.74', 1866734402860666681, 1, 50, 4, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743449710211073, 'RDL2040001', '在库', 1, 204, 1, 0, 'lzl123/卢某人', '2024-12-11 15:14:55.485', 'lzl123/卢某人', '2024-12-11 15:14:55.485', 1866734402860666683, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743494471823362, 'RDL2040002', '领用中', 1, 204, 1, 0, 'lzl123/卢某人', '2024-12-11 15:15:06.156', 'lzl123/卢某人', '2024-12-11 15:15:06.156', 1866734402860666683, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743838849347585, 'RDL2040002', 'B(重要)', 1, 207, 1, 0, 'lzl123/卢某人', '2024-12-11 15:16:28.262', 'lzl123/卢某人', '2024-12-11 15:16:28.262', 1866734402860666686, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743897766735874, 'RDL2040003', 'C(一般)', 1, 207, 1, 0, 'lzl123/卢某人', '2024-12-11 15:16:42.31', 'lzl123/卢某人', '2024-12-11 15:16:42.31', 1866734402860666686, 1, 50, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866743788765163522, 'RDL2040001', 'A(关键)', 1, 207, 1, 0, 'lzl123/卢某人', '2024-12-11 15:16:16.322', 'lzl123/卢某人', '2024-12-11 15:16:16.322', 1866734402860666686, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744245252239361, 'RDL2050003', '返工', 1, 208, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:05.156', 'lzl123/卢某人', '2024-12-11 15:18:05.156', 1866734402860666687, 1, 50, 3, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744182308319234, 'RDL2050001', '生产', 1, 208, 1, 0, 'lzl123/卢某人', '2024-12-11 15:17:50.149', 'lzl123/卢某人', '2024-12-11 15:17:50.149', 1866734402860666687, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744207042129922, 'RDL2050002', '质检', 1, 208, 1, 0, 'lzl123/卢某人', '2024-12-11 15:17:56.046', 'lzl123/卢某人', '2024-12-11 15:17:56.046', 1866734402860666687, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744304014438402, 'RDL2090001', '设备', 1, 209, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:19.167', 'lzl123/卢某人', '2024-12-11 15:18:19.167', 1866734402860666688, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744319977959425, 'RDL2090002', '员工', 1, 209, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:22.973', 'lzl123/卢某人', '2024-12-11 15:18:22.973', 1866734402860666688, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744394749816834, 'RDL2100001', '正向领料', 1, 210, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:40.799', 'lzl123/卢某人', '2024-12-11 15:18:40.799', 1866734402860666689, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744436030156802, 'RDL2100002', '倒冲领料', 1, 210, 1, 0, 'lzl123/卢某人', '2024-12-11 15:18:50.642', 'lzl123/卢某人', '2024-12-11 15:18:50.642', 1866734402860666689, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866745538955948033, 'RDL2170002', '老手', 1, 217, 1, 0, 'lzl123/卢某人', '2024-12-11 15:23:13.599', 'lzl123/卢某人', '2024-12-11 15:23:20.082', 1866734402860666616, 0, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866745520253546498, 'RDL2170001', '新手', 1, 217, 1, 0, 'lzl123/卢某人', '2024-12-11 15:23:09.141', 'lzl123/卢某人', '2024-12-11 15:23:09.141', 1866734402860666616, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866744988013146113, 'RDL2210001', '销售生产订单', 1, 221, 1, 0, 'lzl123/卢某人', '2024-12-11 15:21:02.245', 'lzl123/卢某人', '2024-12-11 15:21:02.245', 1866734402860666620, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866757304037318658, 'RDL2120001', '生产委外订单', 1, 212, 1, 0, 'lzl123/卢某人', '2024-12-11 16:09:58.614', 'lzl123/卢某人', '2024-12-11 16:09:58.614', 1866734402860666611, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866757586087485441, 'RDL2130001', '工单领料', 1, 213, 1, 0, 'lzl123/卢某人', '2024-12-11 16:11:05.861', 'lzl123/卢某人', '2024-12-11 16:11:05.861', 1866734402860666612, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866757618308128770, 'RDL2130002', '委外订单领料', 1, 213, 1, 0, 'lzl123/卢某人', '2024-12-11 16:11:13.542', 'lzl123/卢某人', '2024-12-11 16:11:13.542', 1866734402860666612, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866758425736814593, 'RDL2140001', '工单退料', 1, 214, 1, 0, 'lzl123/卢某人', '2024-12-11 16:14:26.048', 'lzl123/卢某人', '2024-12-11 16:14:26.048', 1866734402860666613, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866758462290173954, 'RDL2140002', '委外订单退料', 1, 214, 1, 0, 'lzl123/卢某人', '2024-12-11 16:14:34.763', 'lzl123/卢某人', '2024-12-11 16:14:34.763', 1866734402860666613, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866759183718850562, 'RDL2160001', 'A', 1, 216, 1, 0, 'lzl123/卢某人', '2024-12-11 16:17:26.764', 'lzl123/卢某人', '2024-12-11 16:17:26.764', 1866734402860666615, 1, 50, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866759194376577025, 'RDL2160002', 'B', 1, 216, 1, 0, 'lzl123/卢某人', '2024-12-11 16:17:29.306', 'lzl123/卢某人', '2024-12-11 16:17:29.306', 1866734402860666615, 1, 50, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1866759206292594689, 'RDL2160003', 'C', 1, 216, 1, 0, 'lzl123/卢某人', '2024-12-11 16:17:32.147', 'lzl123/卢某人', '2024-12-11 16:17:32.147', 1866734402860666615, 1, 50, 3, '', 1, NULL);

INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1880161066653782018, 'RDL010001', '实物类', 1, 101, 1, 0, 'admin/周大大', '2024-11-13 16:40:36.189', 'admin/周大大', '2024-11-13 16:40:36.189', 1, 1, 0, 1, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1880161256756453378, 'RDL010002', '服务类', 1, 101, 1, 0, 'admin/周大大', '2024-11-13 16:40:36.189', 'admin/周大大', '2024-11-13 16:40:36.189', 1, 1, 0, 2, '', 1, NULL);
INSERT INTO "platform"."t_all_dict" ("id", "dict_code", "fieldvalue", "category", "dict_category", "is_enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "parent_item_id", "sort", "tenant_id", "seq", "field_suffix", "is_default", "fixed") VALUES (1880161413019439106, 'RDL010003', '虚拟类', 1, 101, 1, 0, 'admin/周大大', '2024-11-13 16:40:36.189', 'admin/周大大', '2024-11-13 16:40:36.189', 1, 1, 0, 3, '', 1, NULL);
--工艺路线新增维度属性字段
ALTER TABLE "erp"."u_flow_process_detail"
ADD COLUMN "good_product_rule_info" jsonb,
ADD COLUMN "check_rule_info" jsonb,
ADD COLUMN "rework_rule_info" jsonb;

COMMENT ON COLUMN "erp"."u_flow_process_detail"."good_product_rule_info" IS '良品维度属性信息';
COMMENT ON COLUMN "erp"."u_flow_process_detail"."check_rule_info" IS '质检维度属性信息';
COMMENT ON COLUMN "erp"."u_flow_process_detail"."rework_rule_info" IS '返工维度属性信息';


ALTER TABLE "erp"."u_erp_prod_order_change_detail"
 ADD COLUMN "purchased_qty" numeric(18,2),
ADD COLUMN "main_unit_dict_id" int8,
ADD COLUMN "material_source_dict_id" int8;


ALTER TABLE "erp"."u_erp_prod_order_detail"
ALTER COLUMN "purchaseable_qty" SET DEFAULT 0,
ALTER COLUMN "purchased_qty" SET DEFAULT 0,
ALTER COLUMN "is_material_full_purchased" SET DEFAULT 1;

ALTER TABLE "erp"."u_erp_prod_order_change_detail"  ALTER COLUMN "purchased_qty" SET DEFAULT 0;

COMMENT ON COLUMN "erp"."u_erp_prod_order_change_detail"."main_unit_dict_id" IS '基本单位';
COMMENT ON COLUMN "erp"."u_erp_prod_order_change_detail"."material_source_dict_id" IS '物料来源';
COMMENT ON COLUMN "erp"."u_erp_prod_order_change_detail"."purchased_qty" IS '已采购数量';



