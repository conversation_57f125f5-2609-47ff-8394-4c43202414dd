
--产品关联BOM清单
ALTER TABLE "public"."t_hz_product_bom_list" ADD COLUMN "remark" varchar(200);
COMMENT ON COLUMN "public"."t_hz_product_bom_list"."remark" IS '备注';

--物料关联BOM清单
ALTER TABLE "public"."t_hz_material_bom_list"
ALTER COLUMN "usage_qty" DROP NOT NULL,
ALTER COLUMN "loss_rate" DROP NOT NULL;

--销售订单
ALTER TABLE "public"."t_sale_order" ADD COLUMN "is_tolerance" int4;
COMMENT ON COLUMN "public"."t_sale_order"."is_tolerance" IS '容差类型限制[是,否]';

ALTER TABLE "public"."t_sale_order"
ALTER COLUMN "employee_name" TYPE varchar(200) COLLATE "pg_catalog"."default",
ALTER COLUMN "employee_id" TYPE varchar(200) USING "employee_id"::varchar(200);