--ERP-物料分析总表 新增“订单已下发数量”字段 -卢致灵
ALTER TABLE "erp"."u_material_analysis_total"  ADD COLUMN "prod_issued_qty" numeric DEFAULT 0;

--ERP-生产工单bom表 新增“订单已下发数量”字段 -卢致灵
ALTER TABLE "erp"."u_prod_work_material_bom"  ADD COLUMN "estimated_total_qty" numeric(18,2) DEFAULT 0;

--ERP-生产工单表 新增“订单已下发数量”字段 -卢致灵
ALTER TABLE "erp"."u_process_config" ADD COLUMN "process_type" int4 DEFAULT 0;

--ERP-生产工单表 新增“订单已下发数量”字段 -卢致灵
ALTER TABLE "erp"."u_prod_work" ADD COLUMN "is_auto_inbound" int4 DEFAULT 0;

--ERP-生产订单明细表 新增“订单已下发数量”字段 -卢致灵
ALTER TABLE "erp"."u_erp_prod_order_detail" ADD COLUMN "priority" int4;


COMMENT ON COLUMN "erp"."u_material_analysis_total"."prod_issued_qty" IS '订单已下发数量';
COMMENT ON COLUMN "erp"."u_material_analysis_total"."issued_qty" IS '工单已下发数量';
COMMENT ON COLUMN "erp"."u_prod_work_material_bom"."estimated_total_qty" IS '预估总量';
COMMENT ON COLUMN "erp"."u_prod_work"."is_auto_inbound" IS '是否自动入库';
COMMENT ON COLUMN "erp"."u_process_config"."process_type" IS '配置类型';
COMMENT ON COLUMN "erp"."u_erp_prod_order_detail"."priority" IS '优先级';

--ERP-技能分配比例表 修改“技能等级”字段类型 -卢致灵
ALTER TABLE "erp"."u_employee_skill_ratio"
ALTER COLUMN "skill_level_dict_id" TYPE varchar(64) USING "skill_level_dict_id"::varchar(64);

--ERP-客户价格方案表 新增表描述 -卢致灵
COMMENT ON TABLE "erp"."u_customer_price_plan" IS '客户价格方案';

--ERP-物料bom表 修改"领料方式"字段类型 -卢致灵
ALTER TABLE "erp"."u_material_bom"
ALTER COLUMN "picking_method_dict_id" TYPE int4 USING "picking_method_dict_id"::int4;

--ERP-生产订单变更单表 新增字段类型描述 -卢致灵
COMMENT ON COLUMN "erp"."u_erp_prod_order_change_detail"."main_unit_dict_id" IS '基本单位';
COMMENT ON COLUMN "erp"."u_erp_prod_order_change_detail"."material_source_dict_id" IS '物料来源';

--ERP-物料bom表 修改用量字段长度 -卢致灵
ALTER TABLE "erp"."u_material_bom"
ALTER COLUMN "demand_qty" TYPE numeric(18,6),
ALTER COLUMN "estimated_qty" TYPE numeric(18,6);

--ERP-生产工单物料bom表 修改用量字段长度 -卢致灵
ALTER TABLE "erp"."u_prod_work_material_bom"
ALTER COLUMN "demand_qty" TYPE numeric(18,6),
ALTER COLUMN "estimated_qty" TYPE numeric(18,6);

--ERP-生产工单物料bom表 修改用量字段长度 -卢致灵
ALTER TABLE "erp"."u_prod_work_material_bom"
ALTER COLUMN "outbounded_qty" TYPE numeric(18,6),
ALTER COLUMN "outboundable_qty" TYPE numeric(18,6),
ALTER COLUMN "act_used_qty" TYPE numeric(18,6),
ALTER COLUMN "estimated_total_qty" TYPE numeric(18,6);