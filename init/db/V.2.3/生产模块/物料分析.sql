--ERP- 物料分析表 新增“需求日期/分析参数”字段 -卢致灵
ALTER TABLE "erp"."u_material_analysis"
ADD COLUMN "demand_date" date,
ADD COLUMN "analyze_param" varchar(60);

--ERP- 物料分析结果表 新增“可用库存数量/采购在制/工单在制/预估可用数量”字段 -卢致灵
ALTER TABLE "erp"."u_material_analysis_result"
ADD COLUMN "stockable_qty" numeric(18,2),
ADD COLUMN "purchase_in_qty" numeric(18,2),
ADD COLUMN "work_order_in_qty" numeric(18,2),
ADD COLUMN "estimate_able_qty" numeric(18,2);

COMMENT ON COLUMN "erp"."u_material_analysis"."demand_date" IS '需求日期';
COMMENT ON COLUMN "erp"."u_material_analysis"."demand_date" IS '分析参数';
COMMENT ON COLUMN "erp"."u_material_analysis_result"."stockable_qty" IS '可用库存数量';
COMMENT ON COLUMN "erp"."u_material_analysis_result"."purchase_in_qty" IS '采购在制';
COMMENT ON COLUMN "erp"."u_material_analysis_result"."work_order_in_qty" IS '工单在制';
COMMENT ON COLUMN "erp"."u_material_analysis_result"."estimate_able_qty" IS '预估可用数量';

--ERP- 物料分析结果表 删除“预估可用数量”字段 -卢致灵
ALTER TABLE "erp"."u_material_analysis_result" DROP COLUMN "estimate_able_qty";

--ERP- 物料分析结果表 新增“工单数量明细/采购数量明细”字段 -卢致灵
ALTER TABLE "erp"."u_material_analysis_result" ADD COLUMN "pur_detail_list" jsonb;
ALTER TABLE "erp"."u_material_analysis_result" ADD COLUMN "work_detail_list" jsonb;

COMMENT ON COLUMN "erp"."u_material_analysis_result"."work_detail_list" IS '工单数量明细';
COMMENT ON COLUMN "erp"."u_material_analysis_result"."pur_detail_list" IS '采购数量明细';

--ERP- 物料分析生产物料表 新增“优先级”字段 -卢致灵
ALTER TABLE "erp"."u_material_analysis_detail" ADD COLUMN "priority" int4;
COMMENT ON COLUMN "erp"."u_material_analysis_detail"."priority" IS '优先级';
