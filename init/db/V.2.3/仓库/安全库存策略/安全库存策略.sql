CREATE TABLE "erp"."u_inventory_security_strategy" (
                                                       "inventory_security_strategy_id" int8 NOT NULL,
                                                       "inventory_security_strategy_code" varchar(50) COLLATE "pg_catalog"."default",
                                                       "inventory_security_strategy_name" varchar(50) COLLATE "pg_catalog"."default",
                                                       "warehouse_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                                       "remark" varchar(255) COLLATE "pg_catalog"."default",
                                                       "director_id" int8 NOT NULL,
                                                       "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
                                                       "created_id" int8,
                                                       "created_by" varchar(50) COLLATE "pg_catalog"."default",
                                                       "created_dt" timestamp(6),
                                                       "updated_by" varchar(50) COLLATE "pg_catalog"."default",
                                                       "updated_dt" timestamp(6),
                                                       "approved_by" varchar(50) COLLATE "pg_catalog"."default",
                                                       "approved_dt" timestamp(6),
                                                       "data_status" int2 NOT NULL DEFAULT 0,
                                                       "tenant_id" int8 NOT NULL DEFAULT 0,
                                                       "form_dt" timestamp(6)
)
;
ALTER TABLE "erp"."u_inventory_security_strategy" OWNER TO "mgs_platform";
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."inventory_security_strategy_id" IS '主键ID';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."inventory_security_strategy_code" IS '安全库存策略编码';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."inventory_security_strategy_name" IS '安全库存策略名称';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."warehouse_org_id" IS '仓库id';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."remark" IS '备注';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."director_id" IS '责任人';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."created_id" IS '创建人ID';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."created_by" IS '创建人';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."created_dt" IS '创建时间';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."updated_by" IS '更新人';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."approved_by" IS '审核人';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."data_status" IS '审核状态';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "erp"."u_inventory_security_strategy"."form_dt" IS '单据时间';
COMMENT ON TABLE "erp"."u_inventory_security_strategy" IS '安全库存策略';

-- ----------------------------
-- Primary Key structure for table u_inventory_security_strategy
-- ----------------------------
ALTER TABLE "erp"."u_inventory_security_strategy" ADD CONSTRAINT "u_Inventory_security_strategy_pkey" PRIMARY KEY ("inventory_security_strategy_id");
